site:
  fullname: "Table Convert"
  name: "TableConvert"
  subtitle: "เครื่องมือแปลงและสร้างตารางออนไลน์ฟรี"
  intro: "TableConvert เป็นเครื่องมือแปลงตารางออนไลน์ฟรีและเครื่องมือสร้างข้อมูล รองรับการแปลงระหว่างรูปแบบมากกว่า 30 รูปแบบ รวมถึง Excel, CSV, JSON, Markdown, LaTeX, SQL และอื่นๆ"
  followTwitter: "ติดตามเราบน X"
title:
  converter: "%s เป็น %s"
  generator: "เครื่องมือสร้าง %s"
post:
  tags:
    converter: "เครื่องมือแปลง"
    editor: "เครื่องมือแก้ไข"
    generator: "เครื่องมือสร้าง"
    maker: "เครื่องมือสร้าง"
  converter:
    title: "แปลง %s เป็น %s ออนไลน์"
    short: "เครื่องมือออนไลน์ %s เป็น %s ที่ฟรีและทรงพลัง"
    intro: "เครื่องมือแปลง %s เป็น %s ออนไลน์ที่ใช้งานง่าย แปลงข้อมูลตารางได้อย่างง่ายดายด้วยเครื่องมือแปลงที่ใช้งานง่าย รวดเร็ว เชื่อถือได้ และเป็นมิตรกับผู้ใช้"
  generator:
    title: "เครื่องมือแก้ไขและสร้าง %s ออนไลน์"
    short: "เครื่องมือสร้าง %s ออนไลน์แบบมืออาชีพพร้อมฟีเจอร์ครบครัน"
    intro: "เครื่องมือสร้าง %s ออนไลน์และเครื่องมือแก้ไขตารางที่ใช้งานง่าย สร้างตารางข้อมูลแบบมืออาชีพได้อย่างง่ายดายด้วยเครื่องมือที่ใช้งานง่ายและการแสดงผลแบบเรียลไทม์"
navbar:
  search:
    placeholder: "ค้นหาเครื่องมือแปลง ..."
  sponsor: "ซื้อกาแฟให้ฉัน"
  extension: "ส่วนขยาย"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "แหล่งข้อมูล"
    placeholder: "วางข้อมูล %s ของคุณหรือลากไฟล์ %s มาที่นี่"
    example: "ตัวอย่าง"
    upload: "อัปโหลดไฟล์"
    extract:
      enter: "แยกจากหน้าเว็บ"
      intro: "ป้อน URL ของหน้าเว็บที่มีข้อมูลตารางเพื่อแยกข้อมูลที่มีโครงสร้างโดยอัตโนมัติ"
      btn: "แยก %s"
    excel:
      sheet: "แผ่นงาน"
      none: "ไม่มี"
  tableEditor:
    title: "เครื่องมือแก้ไขตารางออนไลน์"
    undo: "เลิกทำ"
    redo: "ทำซ้ำ"
    transpose: "สลับแถวคอลัมน์"
    clear: "ล้าง"
    deleteBlank: "ลบช่องว่าง"
    deleteDuplicate: "ลบข้อมูลซ้ำ"
    uppercase: "ตัวพิมพ์ใหญ่"
    lowercase: "ตัวพิมพ์เล็ก"
    capitalize: "ตัวพิมพ์ใหญ่ต้นคำ"
    replace:
      replace: "ค้นหาและแทนที่ (รองรับ Regex)"
      subst: "แทนที่ด้วย..."
      btn: "แทนที่ทั้งหมด"
  tableGenerator:
    title: "เครื่องมือสร้างตาราง"
    sponsor: "ซื้อกาแฟให้ฉัน"
    copy: "คัดลอกไปยังคลิปบอร์ด"
    download: "ดาวน์โหลดไฟล์"
    tooltip:
      html:
        escape: "Escape อักขระพิเศษ HTML (&, <, >, \", ') เพื่อป้องกันข้อผิดพลาดในการแสดงผล"
        div: "ใช้เลย์เอาต์ DIV+CSS แทนแท็ก TABLE แบบดั้งเดิม เหมาะสำหรับการออกแบบแบบตอบสนอง"
        minify: "ลบช่องว่างและการขึ้นบรรทัดใหม่เพื่อสร้างโค้ด HTML ที่บีบอัด"
        thead: "สร้างโครงสร้างหัวตารางมาตรฐาน (&lt;thead&gt;) และเนื้อหา (&lt;tbody&gt;)"
        tableCaption: "เพิ่มชื่อเรื่องอธิบายเหนือตาราง (องค์ประกอบ &lt;caption&gt;)"
        tableClass: "เพิ่มชื่อคลาส CSS ให้กับตารางเพื่อการปรับแต่งสไตล์ที่ง่าย"
        tableId: "ตั้งค่าตัวระบุ ID เฉพาะสำหรับตารางสำหรับการจัดการ JavaScript"
      jira:
        escape: "Escape อักขระไปป์ (|) เพื่อหลีกเลี่ยงความขัดแย้งกับไวยากรณ์ตาราง Jira"
      json:
        parsingJSON: "แยกวิเคราะห์สตริง JSON ในเซลล์เป็นออบเจ็กต์อย่างชาญฉลาด"
        minify: "สร้างรูปแบบ JSON บรรทัดเดียวที่กะทัดรัดเพื่อลดขนาดไฟล์"
        format: "เลือกโครงสร้างข้อมูล JSON เอาต์พุต: อาร์เรย์ออบเจ็กต์, อาร์เรย์ 2D, ฯลฯ"
      latex:
        escape: "Escape อักขระพิเศษ LaTeX (%, &, _, #, $, ฯลฯ) เพื่อให้แน่ใจว่าการคอมไพล์ถูกต้อง"
        ht: "เพิ่มพารามิเตอร์ตำแหน่งลอย [!ht] เพื่อควบคุมตำแหน่งตารางบนหน้า"
        mwe: "สร้างเอกสาร LaTeX ที่สมบูรณ์"
        tableAlign: "ตั้งค่าการจัดแนวแนวนอนของตารางบนหน้า"
        tableBorder: "กำหนดค่าสไตล์เส้นขอบตาราง: ไม่มีเส้นขอบ, เส้นขอบบางส่วน, เส้นขอบเต็ม"
        label: "ตั้งค่าป้ายกำกับตารางสำหรับการอ้างอิงข้าม \\ref{}"
        caption: "ตั้งค่าคำบรรยายตารางเพื่อแสดงเหนือหรือใต้ตาราง"
        location: "เลือกตำแหน่งการแสดงคำบรรยายตาราง: เหนือหรือใต้"
        tableType: "เลือกประเภทสภาพแวดล้อมตาราง: tabular, longtable, array, ฯลฯ"
      markdown:
        escape: "Escape อักขระพิเศษ Markdown (*, _, |, \\, ฯลฯ) เพื่อหลีกเลี่ยงความขัดแย้งของรูปแบบ"
        pretty: "จัดแนวความกว้างคอลัมน์อัตโนมัติเพื่อสร้างรูปแบบตารางที่สวยงามยิ่งขึ้น"
        simple: "ใช้ไวยากรณ์ที่เรียบง่าย ละเว้นเส้นแนวตั้งเส้นขอบด้านนอก"
        boldFirstRow: "ทำให้ข้อความแถวแรกเป็นตัวหนา"
        boldFirstColumn: "ทำให้ข้อความคอลัมน์แรกเป็นตัวหนา"
        firstHeader: "ถือว่าแถวแรกเป็นหัวเรื่องและเพิ่มเส้นแบ่ง"
        textAlign: "ตั้งค่าการจัดแนวข้อความคอลัมน์: ซ้าย, กลาง, ขวา"
        multilineHandling: "การจัดการข้อความหลายบรรทัด: รักษาการขึ้นบรรทัดใหม่, escape เป็น \\n, ใช้แท็ก &lt;br&gt;"

        includeLineNumbers: "เพิ่มคอลัมน์หมายเลขบรรทัดทางด้านซ้ายของตาราง"
      magic:
        builtin: "เลือกรูปแบบเทมเพลตทั่วไปที่กำหนดไว้ล่วงหน้า"
        rowsTpl: "<table> <tr> <th>ไวยากรณ์ Magic</th> <th>คำอธิบาย</th> <th>รองรับเมธอด JS</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>ฟิลด์ที่ 1, 2 ... ของ <b>h</b>eading, หรือ {hA} {hB} ...</td> <td>เมธอดสตริง</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>ฟิลด์ที่ 1, 2 ... ของแถวปัจจุบัน, หรือ {$A} {$B} ...</td> <td>เมธอดสตริง</td> </tr> <tr> <td>{F,} {F;}</td> <td>แยกแถวปัจจุบันด้วยสตริงหลัง <b>F</b></td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td><b>N</b>หมายเลขบรรทัดของ <b>R</b>ow ปัจจุบันจาก 1 หรือ 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> หมายเลขบรรทัด<b>สุดท้าย</b>ของ <b>R</b>ows </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>E<b>x</b>ecute โค้ด JavaScript, เช่น: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> ใช้ backslash <b>\\</b> เพื่อแสดงวงเล็บปีกกา {...} </td> <td></td> </tr></table>"
        headerTpl: "เทมเพลตเอาต์พุตแบบกำหนดเองสำหรับส่วนหัว"
        footerTpl: "เทมเพลตเอาต์พุตแบบกำหนดเองสำหรับส่วนท้าย"
      textile:
        escape: "Escape อักขระไวยากรณ์ Textile (|, ., -, ^) เพื่อหลีกเลี่ยงความขัดแย้งของรูปแบบ"
        rowHeader: "ตั้งค่าแถวแรกเป็นแถวหัวเรื่อง"
        thead: "เพิ่มเครื่องหมายไวยากรณ์ Textile สำหรับหัวตารางและเนื้อหา"
      xml:
        escape: "Escape อักขระพิเศษ XML (&lt;, &gt;, &amp;, \", ') เพื่อให้แน่ใจว่า XML ถูกต้อง"
        minify: "สร้างเอาต์พุต XML ที่บีบอัด ลบช่องว่างพิเศษ"
        rootElement: "ตั้งค่าชื่อแท็กองค์ประกอบรูท XML"
        rowElement: "ตั้งค่าชื่อแท็กองค์ประกอบ XML สำหรับแต่ละแถวของข้อมูล"
        declaration: "เพิ่มหัวเรื่องการประกาศ XML (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "เอาต์พุตข้อมูลเป็นแอตทริบิวต์ XML แทนองค์ประกอบลูก"
        cdata: "ห่อเนื้อหาข้อความด้วย CDATA เพื่อปกป้องอักขระพิเศษ"
        encoding: "ตั้งค่ารูปแบบการเข้ารหัสอักขระสำหรับเอกสาร XML"
        indentation: "เลือกอักขระการเยื้อง XML: ช่องว่างหรือแท็บ"
      yaml:
        indentSize: "ตั้งค่าจำนวนช่องว่างสำหรับการเยื้องลำดับชั้น YAML (โดยปกติ 2 หรือ 4)"
        arrayStyle: "รูปแบบอาร์เรย์: บล็อก (หนึ่งรายการต่อบรรทัด) หรือโฟลว์ (รูปแบบอินไลน์)"
        quotationStyle: "สไตล์เครื่องหมายคำพูดสตริง: ไม่มีเครื่องหมายคำพูด, เครื่องหมายคำพูดเดี่ยว, เครื่องหมายคำพูดคู่"
      csv:
        bom: "เพิ่มเครื่องหมายลำดับไบต์ UTF-8 เพื่อช่วย Excel และซอฟต์แวร์อื่นๆ ในการจดจำการเข้ารหัส"
      excel:
        autoWidth: "ปรับความกว้างคอลัมน์โดยอัตโนมัติตามเนื้อหา"
        protectSheet: "เปิดใช้งานการป้องกันแผ่นงานด้วยรหัสผ่าน: tableconvert.com"
      sql:
        primaryKey: "ระบุชื่อฟิลด์คีย์หลักสำหรับคำสั่ง CREATE TABLE"
        dialect: "เลือกประเภทฐานข้อมูล ส่งผลต่อไวยากรณ์เครื่องหมายคำพูดและประเภทข้อมูล"
      ascii:
        forceSep: "บังคับเส้นแบ่งระหว่างแต่ละแถวของข้อมูล"
        style: "เลือกสไตล์การวาดเส้นขอบตาราง ASCII"
        comment: "เพิ่มเครื่องหมายความคิดเห็นเพื่อห่อทั้งตาราง"
      mediawiki:
        minify: "บีบอัดโค้ดเอาต์พุต ลบช่องว่างพิเศษ"
        header: "ทำเครื่องหมายแถวแรกเป็นสไตล์หัวเรื่อง"
        sort: "เปิดใช้งานฟังก์ชันการเรียงลำดับคลิกตาราง"
      asciidoc:
        minify: "บีบอัดเอาต์พุตรูปแบบ AsciiDoc"
        firstHeader: "ตั้งค่าแถวแรกเป็นแถวหัวเรื่อง"
        lastFooter: "ตั้งค่าแถวสุดท้ายเป็นแถวท้าย"
        title: "เพิ่มข้อความชื่อเรื่องให้กับตาราง"
      tracwiki:
        rowHeader: "ตั้งค่าแถวแรกเป็นหัวเรื่อง"
        colHeader: "ตั้งค่าคอลัมน์แรกเป็นหัวเรื่อง"
      bbcode:
        minify: "บีบอัดรูปแบบเอาต์พุต BBCode"
      restructuredtext:
        style: "เลือกสไตล์เส้นขอบตาราง reStructuredText"
        forceSep: "บังคับเส้นแบ่ง"
      pdf:
        theme: "เลือกสไตล์ภาพตาราง PDF สำหรับเอกสารมืออาชีพ"
        headerColor: "เลือกสีพื้นหลังหัวเรื่องสำหรับตาราง PDF"
        showHead: "ควบคุมการแสดงหัวเรื่องในหน้า PDF"
        docTitle: "ชื่อเรื่องเสริมสำหรับเอกสาร PDF"
        docDescription: "ข้อความคำอธิบายเสริมสำหรับเอกสาร PDF"
    label:
      ascii:
        forceSep: "ตัวแบ่งแถว"
        style: "สไตล์เส้นขอบ"
        comment: "ตัวห่อความคิดเห็น"
      restructuredtext:
        style: "สไตล์เส้นขอบ"
        forceSep: "บังคับตัวแบ่ง"
      bbcode:
        minify: "บีบอัดเอาต์พุต"
      csv:
        doubleQuote: "ห่อด้วยเครื่องหมายคำพูดคู่"
        delimiter: "ตัวแบ่งฟิลด์"
        bom: "UTF-8 BOM"
        valueDelimiter: "ตัวแบ่งค่า"
        rowDelimiter: "ตัวแบ่งแถว"
        prefix: "คำนำหน้าแถว"
        suffix: "คำต่อท้ายแถว"
      excel:
        autoWidth: "ความกว้างอัตโนมัติ"
        textFormat: "รูปแบบข้อความ"
        protectSheet: "ป้องกันแผ่นงาน"
        boldFirstRow: "ทำแถวแรกเป็นตัวหนา"
        boldFirstColumn: "ทำคอลัมน์แรกเป็นตัวหนา"
        sheetName: "ชื่อแผ่นงาน"
      html:
        escape: "Escape อักขระ HTML"
        div: "ตาราง DIV"
        minify: "บีบอัดโค้ด"
        thead: "โครงสร้างหัวตาราง"
        tableCaption: "คำบรรยายตาราง"
        tableClass: "คลาสตาราง"
        tableId: "ID ตาราง"
        rowHeader: "หัวแถว"
        colHeader: "หัวคอลัมน์"
      jira:
        escape: "Escape อักขระ"
        rowHeader: "หัวแถว"
        colHeader: "หัวคอลัมน์"
      json:
        parsingJSON: "แยกวิเคราะห์ JSON"
        minify: "บีบอัดเอาต์พุต"
        format: "รูปแบบข้อมูล"
        rootName: "ชื่อออบเจ็กต์รูท"
        indentSize: "ขนาดการเยื้อง"
      jsonlines:
        parsingJSON: "แยกวิเคราะห์ JSON"
        format: "รูปแบบข้อมูล"
      latex:
        escape: "Escape อักขระตาราง LaTeX"
        ht: "ตำแหน่งลอย"
        mwe: "เอกสารสมบูรณ์"
        tableAlign: "การจัดแนวตาราง"
        tableBorder: "สไตล์เส้นขอบ"
        label: "ป้ายกำกับอ้างอิง"
        caption: "คำบรรยายตาราง"
        location: "ตำแหน่งคำบรรยาย"
        tableType: "ประเภทตาราง"
        boldFirstRow: "ทำแถวแรกเป็นตัวหนา"
        boldFirstColumn: "ทำคอลัมน์แรกเป็นตัวหนา"
        textAlign: "การจัดแนวข้อความ"
        borders: "การตั้งค่าเส้นขอบ"
      markdown:
        escape: "Escape อักขระ"
        pretty: "ตาราง Markdown สวยงาม"
        simple: "รูปแบบ Markdown แบบง่าย"
        boldFirstRow: "ทำแถวแรกเป็นตัวหนา"
        boldFirstColumn: "ทำคอลัมน์แรกเป็นตัวหนา"
        firstHeader: "หัวเรื่องแรก"
        textAlign: "การจัดแนวข้อความ"
        multilineHandling: "การจัดการหลายบรรทัด"

        includeLineNumbers: "เพิ่มหมายเลขบรรทัด"
        align: "การจัดแนว"
      mediawiki:
        minify: "บีบอัดโค้ด"
        header: "มาร์กอัปหัวเรื่อง"
        sort: "สามารถเรียงลำดับได้"
      asciidoc:
        minify: "บีบอัดรูปแบบ"
        firstHeader: "หัวเรื่องแรก"
        lastFooter: "ท้ายเรื่องสุดท้าย"
        title: "ชื่อตาราง"
      tracwiki:
        rowHeader: "หัวแถว"
        colHeader: "หัวคอลัมน์"
      sql:
        drop: "ลบตาราง (หากมีอยู่)"
        create: "สร้างตาราง"
        oneInsert: "แทรกแบบแบตช์"
        table: "ชื่อตาราง"
        dialect: "ประเภทฐานข้อมูล"
        primaryKey: "คีย์หลัก"
      magic:
        builtin: "เทมเพลตในตัว"
        rowsTpl: "เทมเพลตแถว, ไวยากรณ์ ->"
        headerTpl: "เทมเพลตหัวเรื่อง"
        footerTpl: "เทมเพลตท้ายเรื่อง"
      textile:
        escape: "Escape อักขระ"
        rowHeader: "หัวแถว"
        thead: "ไวยากรณ์หัวตาราง"
      xml:
        escape: "Escape อักขระ XML"
        minify: "บีบอัดเอาต์พุต"
        rootElement: "องค์ประกอบรูท"
        rowElement: "องค์ประกอบแถว"
        declaration: "การประกาศ XML"
        attributes: "โหมดแอตทริบิวต์"
        cdata: "ตัวห่อ CDATA"
        encoding: "การเข้ารหัส"
        indentSize: "ขนาดการเยื้อง"
      yaml:
        indentSize: "ขนาดการเยื้อง"
        arrayStyle: "สไตล์อาร์เรย์"
        quotationStyle: "สไตล์เครื่องหมายคำพูด"
      pdf:
        theme: "ธีมตาราง PDF"
        headerColor: "สีหัวเรื่อง PDF"
        showHead: "การแสดงหัวเรื่อง PDF"
        docTitle: "ชื่อเอกสาร PDF"
        docDescription: "คำอธิบายเอกสาร PDF"

sidebar:
  all: "เครื่องมือแปลงทั้งหมด"
  dataSource:
    title: "แหล่งข้อมูล"
    description:
      converter: "นำเข้า %s เพื่อแปลงเป็น %s รองรับการอัปโหลดไฟล์ การแก้ไขออนไลน์ และการแยกข้อมูลเว็บ"
      generator: "สร้างข้อมูลตารางด้วยการรองรับวิธีการป้อนข้อมูลหลายแบบ รวมถึงการป้อนข้อมูลด้วยตนเอง การนำเข้าไฟล์ และการสร้างเทมเพลต"
  tableEditor:
    title: "เครื่องมือแก้ไขตารางออนไลน์"
    description:
      converter: "ประมวลผล %s ออนไลน์โดยใช้เครื่องมือแก้ไขตารางของเรา ประสบการณ์การใช้งานแบบ Excel พร้อมการรองรับการลบแถวว่าง การลบข้อมูลซ้ำ การเรียงลำดับ และการค้นหาและแทนที่"
      generator: "เครื่องมือแก้ไขตารางออนไลน์ที่ทรงพลัง ให้ประสบการณ์การใช้งานแบบ Excel รองรับการลบแถวว่าง การลบข้อมูลซ้ำ การเรียงลำดับ และการค้นหาและแทนที่"
  tableGenerator:
    title: "เครื่องมือสร้างตาราง"
    description:
      converter: "สร้าง %s อย่างรวดเร็วด้วยการแสดงตัวอย่างแบบเรียลไทม์ของเครื่องมือสร้างตาราง ตัวเลือกการส่งออกที่หลากหลาย คัดลอกและดาวน์โหลดด้วยคลิกเดียว"
      generator: "ส่งออกข้อมูล %s ในหลายรูปแบบเพื่อตอบสนองสถานการณ์การใช้งานที่แตกต่างกัน รองรับตัวเลือกแบบกำหนดเองและการแสดงตัวอย่างแบบเรียลไทม์"
footer:
  changelog: "บันทึกการเปลี่ยนแปลง"
  sponsor: "ผู้สนับสนุน"
  contact: "ติดต่อเรา"
  privacyPolicy: "นโยบายความเป็นส่วนตัว"
  about: "เกี่ยวกับ"
  resources: "ทรัพยากร"
  popularConverters: "เครื่องมือแปลงยอดนิยม"
  popularGenerators: "เครื่องมือสร้างยอดนิยม"
  dataSecurity: "ข้อมูลของคุณปลอดภัย - การแปลงทั้งหมดทำงานในเบราว์เซอร์ของคุณ"
converters:
  Markdown:
    alias: "ตาราง Markdown"
    what: "Markdown เป็นภาษามาร์กอัปที่มีน้ำหนักเบาซึ่งใช้กันอย่างแพร่หลายสำหรับเอกสารทางเทคนิค การสร้างเนื้อหาบล็อก และการพัฒนาเว็บ ไวยากรณ์ตารางของมันกระชับและใช้งานง่าย รองรับการจัดแนวข้อความ การฝังลิงก์ และการจัดรูปแบบ เป็นเครื่องมือที่โปรแกรมเมอร์และนักเขียนเทคนิคชื่นชอบ เข้ากันได้อย่างสมบูรณ์กับ GitHub, GitLab และแพลตฟอร์มโฮสติ้งโค้ดอื่นๆ"
    step1: "วางข้อมูลตาราง Markdown ลงในพื้นที่แหล่งข้อมูล หรือลากและวางไฟล์ .md โดยตรงเพื่ออัปโหลด เครื่องมือจะแยกวิเคราะห์โครงสร้างตารางและการจัดรูปแบบโดยอัตโนมัติ รองรับเนื้อหาที่ซ้อนกันที่ซับซ้อนและการจัดการอักขระพิเศษ"
    step3: "สร้างโค้ดตาราง Markdown มาตรฐานแบบเรียลไทม์ รองรับวิธีการจัดแนวหลายแบบ การทำให้ข้อความเป็นตัวหนา การเพิ่มหมายเลขบรรทัด และการตั้งค่ารูปแบบขั้นสูงอื่นๆ โค้ดที่สร้างขึ้นเข้ากันได้อย่างสมบูรณ์กับ GitHub และเครื่องมือแก้ไข Markdown หลัก พร้อมใช้งานด้วยการคัดลอกคลิกเดียว"
    from_alias: "ไฟล์ตาราง Markdown"
    to_alias: "รูปแบบตาราง Markdown"
  Magic:
    alias: "เทมเพลตแบบกำหนดเอง"
    what: "เทมเพลต Magic เป็นเครื่องมือสร้างข้อมูลขั้นสูงที่เป็นเอกลักษณ์ของเครื่องมือนี้ ช่วยให้ผู้ใช้สามารถสร้างเอาต์พุตข้อมูลรูปแบบใดก็ได้ผ่านไวยากรณ์เทมเพลตแบบกำหนดเอง รองรับการแทนที่ตัวแปร การตัดสินใจตามเงื่อนไข และการประมวลผลแบบลูป เป็นโซลูชันสูงสุดสำหรับการจัดการความต้องการการแปลงข้อมูลที่ซับซ้อนและรูปแบบเอาต์พุตที่เป็นส่วนตัว เหมาะสำหรับนักพัฒนาและวิศวกรข้อมูลเป็นพิเศษ"
    step1: "เลือกเทมเพลตทั่วไปที่มีอยู่แล้วหรือสร้างไวยากรณ์เทมเพลตแบบกำหนดเอง รองรับตัวแปรและฟังก์ชันที่หลากหลายที่สามารถจัดการโครงสร้างข้อมูลที่ซับซ้อนและตรรกะทางธุรกิจ"
    step3: "สร้างเอาต์พุตข้อมูลที่ตอบสนองความต้องการรูปแบบแบบกำหนดเองอย่างสมบูรณ์ รองรับตรรกะการแปลงข้อมูลที่ซับซ้อนและการประมวลผลตามเงื่อนไข ช่วยเพิ่มประสิทธิภาพการประมวลผลข้อมูลและคุณภาพเอาต์พุตอย่างมาก เครื่องมือที่ทรงพลังสำหรับการประมวลผลข้อมูลแบบแบตช์"
    from_alias: "ข้อมูลตาราง"
    to_alias: "เอาต์พุตรูปแบบแบบกำหนดเอง"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) เป็นรูปแบบการแลกเปลี่ยนข้อมูลที่ใช้กันอย่างแพร่หลายที่สุด ได้รับการสนับสนุนอย่างสมบูรณ์โดย Excel, Google Sheets, ระบบฐานข้อมูล และเครื่องมือวิเคราะห์ข้อมูลต่างๆ โครงสร้างที่เรียบง่ายและความเข้ากันได้ที่แข็งแกร่งทำให้เป็นรูปแบบมาตรฐานสำหรับการย้ายข้อมูล การนำเข้า/ส่งออกแบบแบตช์ และการแลกเปลี่ยนข้อมูลข้ามแพลตฟอร์ม ใช้กันอย่างแพร่หลายในการวิเคราะห์ทางธุรกิจ วิทยาศาสตร์ข้อมูล และการรวมระบบ"
    step1: "อัปโหลดไฟล์ CSV หรือวางข้อมูล CSV โดยตรง เครื่องมือจะจดจำตัวแบ่งต่างๆ (จุลภาค แท็บ เซมิโคลอน ไปป์ ฯลฯ) อย่างชาญฉลาด ตรวจจับประเภทข้อมูลและรูปแบบการเข้ารหัสโดยอัตโนมัติ รองรับการแยกวิเคราะห์ไฟล์ขนาดใหญ่และโครงสร้างข้อมูลที่ซับซ้อนอย่างรวดเร็ว"
    step3: "สร้างไฟล์รูปแบบ CSV มาตรฐานพร้อมการรองรับตัวแบ่งแบบกำหนดเอง สไตล์เครื่องหมายคำพูด รูปแบบการเข้ารหัส และการตั้งค่าเครื่องหมาย BOM ทำให้มั่นใจได้ถึงความเข้ากันได้ที่สมบูรณ์แบบกับระบบเป้าหมาย ให้ตัวเลือกการดาวน์โหลดและการบีบอัดเพื่อตอบสนองความต้องการการประมวลผลข้อมูลระดับองค์กร"
    from_alias: "ไฟล์ข้อมูล CSV"
    to_alias: "รูปแบบมาตรฐาน CSV"
  JSON:
    alias: "อาร์เรย์ JSON"
    what: "JSON (JavaScript Object Notation) เป็นรูปแบบข้อมูลตารางมาตรฐานสำหรับแอปพลิเคชันเว็บสมัยใหม่ REST API และสถาปัตยกรรมไมโครเซอร์วิส โครงสร้างที่ชัดเจนและการแยกวิเคราะห์ที่มีประสิทธิภาพทำให้ใช้กันอย่างแพร่หลายในการโต้ตอบข้อมูลส่วนหน้าและส่วนหลัง การจัดเก็บไฟล์กำหนดค่า และฐานข้อมูล NoSQL รองรับออบเจ็กต์ที่ซ้อนกัน โครงสร้างอาร์เรย์ และประเภทข้อมูลหลายแบบ ทำให้เป็นข้อมูลตารางที่ขาดไม่ได้สำหรับการพัฒนาซอฟต์แวร์สมัยใหม่"
    step1: "อัปโหลดไฟล์ JSON หรือวางอาร์เรย์ JSON รองรับการจดจำและแยกวิเคราะห์อาร์เรย์ออบเจ็กต์ โครงสร้างที่ซ้อนกัน และประเภทข้อมูลที่ซับซ้อนโดยอัตโนมัติ เครื่องมือจะตรวจสอบไวยากรณ์ JSON อย่างชาญฉลาดและให้คำแนะนำข้อผิดพลาด"
    step3: "สร้างเอาต์พุตรูปแบบ JSON หลายแบบ: อาร์เรย์ออบเจ็กต์มาตรฐาน อาร์เรย์ 2D อาร์เรย์คอลัมน์ และรูปแบบคู่คีย์-ค่า รองรับเอาต์พุตที่สวยงาม โหมดการบีบอัด ชื่อออบเจ็กต์รูทแบบกำหนดเอง และการตั้งค่าการเยื้อง ปรับให้เข้ากับอินเทอร์เฟซ API ต่างๆ และความต้องการการจัดเก็บข้อมูลได้อย่างสมบูรณ์แบบ"
    from_alias: "ไฟล์อาร์เรย์ JSON"
    to_alias: "รูปแบบมาตรฐาน JSON"
  JSONLines:
    alias: "รูปแบบ JSONLines"
    what: "JSON Lines (หรือที่เรียกว่า NDJSON) เป็นรูปแบบที่สำคัญสำหรับการประมวลผลข้อมูลขนาดใหญ่และการส่งข้อมูลแบบสตรีม โดยแต่ละบรรทัดจะมีออบเจ็กต์ JSON อิสระ ใช้กันอย่างแพร่หลายในการวิเคราะห์ล็อก การประมวลผลสตรีมข้อมูล การเรียนรู้ของเครื่อง และระบบแบบกระจาย รองรับการประมวลผลแบบเพิ่มหน่วยและการคำนวณแบบขนาน ทำให้เป็นตัวเลือกที่เหมาะสำหรับการจัดการข้อมูลที่มีโครงสร้างขนาดใหญ่"
    step1: "อัปโหลดไฟล์ JSONLines หรือวางข้อมูล เครื่องมือจะแยกวิเคราะห์ออบเจ็กต์ JSON ทีละบรรทัด รองรับการประมวลผลสตรีมไฟล์ขนาดใหญ่และฟังก์ชันการข้ามบรรทัดที่ผิดพลาด"
    step3: "สร้างรูปแบบ JSONLines มาตรฐานโดยแต่ละบรรทัดจะส่งออกออบเจ็กต์ JSON ที่สมบูรณ์ เหมาะสำหรับการประมวลผลสตรีม การนำเข้าแบบแบตช์ และสถานการณ์การวิเคราะห์ข้อมูลขนาดใหญ่ รองรับการตรวจสอบข้อมูลและการปรับปรุงรูปแบบ"
    from_alias: "ข้อมูล JSONLines"
    to_alias: "รูปแบบสตรีม JSONLines"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) เป็นรูปแบบมาตรฐานสำหรับการแลกเปลี่ยนข้อมูลระดับองค์กรและการจัดการกำหนดค่า มีข้อกำหนดไวยากรณ์ที่เข้มงวดและกลไกการตรวจสอบที่ทรงพลัง ใช้กันอย่างแพร่หลายในเว็บเซอร์วิส ไฟล์กำหนดค่า การจัดเก็บเอกสาร และการรวมระบบ รองรับเนมสเปซ การตรวจสอบสคีมา และการแปลง XSLT ทำให้เป็นข้อมูลตารางที่สำคัญสำหรับแอปพลิเคชันองค์กร"
    step1: "อัปโหลดไฟล์ XML หรือวางข้อมูล XML เครื่องมือจะแยกวิเคราะห์โครงสร้าง XML โดยอัตโนมัติและแปลงเป็นรูปแบบตาราง รองรับเนมสเปซ การจัดการแอตทริบิวต์ และโครงสร้างที่ซ้อนกันที่ซับซ้อน"
    step3: "สร้างเอาต์พุต XML ที่สอดคล้องกับมาตรฐาน XML รองรับองค์ประกอบรูทแบบกำหนดเอง ชื่อองค์ประกอบแถว โหมดแอตทริบิวต์ การห่อ CDATA และการตั้งค่าการเข้ารหัสอักขระ ทำให้มั่นใจได้ถึงความสมบูรณ์และความเข้ากันได้ของข้อมูล ตอบสนองความต้องการของแอปพลิเคชันระดับองค์กร"
    from_alias: "ไฟล์ข้อมูล XML"
    to_alias: "รูปแบบมาตรฐาน XML"
  YAML:
    alias: "การกำหนดค่า YAML"
    what: "YAML เป็นมาตรฐานการซีเรียลไลซ์ข้อมูลที่เป็นมิตรกับมนุษย์ มีชื่อเสียงในด้านโครงสร้างลำดับชั้นที่ชัดเจนและไวยากรณ์ที่กระชับ ใช้กันอย่างแพร่หลายในไฟล์กำหนดค่า ห่วงโซ่เครื่องมือ DevOps, Docker Compose และการปรับใช้ Kubernetes ความสามารถในการอ่านที่แข็งแกร่งและไวยากรณ์ที่กระชับทำให้เป็นรูปแบบการกำหนดค่าที่สำคัญสำหรับแอปพลิเคชันคลาวด์เนทีฟสมัยใหม่และการดำเนินงานอัตโนมัติ"
    step1: "อัปโหลดไฟล์ YAML หรือวางข้อมูล YAML เครื่องมือจะแยกวิเคราะห์โครงสร้าง YAML อย่างชาญฉลาดและตรวจสอบความถูกต้องของไวยากรณ์ รองรับรูปแบบเอกสารหลายฉบับและประเภทข้อมูลที่ซับซ้อน"
    step3: "สร้างเอาต์พุตรูปแบบ YAML มาตรฐานพร้อมการรองรับสไตล์อาร์เรย์แบบบล็อกและโฟลว์ การตั้งค่าเครื่องหมายคำพูดหลายแบบ การเยื้องแบบกำหนดเอง และการรักษาความคิดเห็น ทำให้มั่นใจว่าไฟล์ YAML เอาต์พุตเข้ากันได้อย่างสมบูรณ์กับพาร์เซอร์และระบบกำหนดค่าต่างๆ"
    from_alias: "ไฟล์การกำหนดค่า YAML"
    to_alias: "รูปแบบมาตรฐาน YAML"
  MySQL:
      alias: "ผลลัพธ์ Query MySQL"
      what: "MySQL เป็นระบบจัดการฐานข้อมูลเชิงสัมพันธ์แบบโอเพ่นซอร์สที่ได้รับความนิยมมากที่สุดในโลก มีชื่อเสียงในด้านประสิทธิภาพสูง ความน่าเชื่อถือ และความง่ายในการใช้งาน ใช้กันอย่างแพร่หลายในแอปพลิเคชันเว็บ ระบบองค์กร และแพลตฟอร์มการวิเคราะห์ข้อมูล ผลลัพธ์ query MySQL มักจะมีข้อมูลตารางที่มีโครงสร้าง ทำหน้าที่เป็นแหล่งข้อมูลสำคัญในการจัดการฐานข้อมูลและงานวิเคราะห์ข้อมูล"
      step1: "วางผลลัพธ์เอาต์พุต query MySQL ลงในพื้นที่แหล่งข้อมูล เครื่องมือจะจดจำและแยกวิเคราะห์รูปแบบเอาต์พุต command-line ของ MySQL โดยอัตโนมัติ รองรับสไตล์ผลลัพธ์ query ต่างๆ และการเข้ารหัสอักขระ จัดการหัวเรื่องและแถวข้อมูลอย่างชาญฉลาด"
      step3: "แปลงผลลัพธ์ query MySQL เป็นรูปแบบข้อมูลตารางหลายแบบอย่างรวดเร็ว อำนวยความสะดวกในการวิเคราะห์ข้อมูล การสร้างรายงาน การย้ายข้อมูลข้ามระบบ และการตรวจสอบข้อมูล เครื่องมือที่ใช้งานได้จริงสำหรับผู้ดูแลฐานข้อมูลและนักวิเคราะห์ข้อมูล"
      from_alias: "เอาต์พุต Query MySQL"
      to_alias: "ข้อมูลตาราง MySQL"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) เป็นภาษาการดำเนินงานมาตรฐานสำหรับฐานข้อมูลเชิงสัมพันธ์ ใช้สำหรับการ query ข้อมูล การแทรก การอัปเดต และการลบข้อมูล ในฐานะเทคโนโลยีหลักของการจัดการฐานข้อมูล SQL ใช้กันอย่างแพร่หลายในการวิเคราะห์ข้อมูล business intelligence การประมวลผล ETL และการสร้างคลังข้อมูล เป็นเครื่องมือทักษะที่จำเป็นสำหรับผู้เชี่ยวชาญด้านข้อมูล"
    step1: "วางคำสั่ง INSERT SQL หรืออัปโหลดไฟล์ .sql เครื่องมือจะแยกวิเคราะห์ไวยากรณ์ SQL อย่างชาญฉลาดและแยกข้อมูลตาราง รองรับภาษาถิ่น SQL หลายแบบและการประมวลผลคำสั่ง query ที่ซับซ้อน"
    step3: "สร้างคำสั่ง INSERT SQL มาตรฐานและคำสั่งสร้างตาราง รองรับภาษาถิ่นฐานข้อมูลหลายแบบ (MySQL, PostgreSQL, SQLite, SQL Server, Oracle) จัดการการแมปประเภทข้อมูล การ escape อักขระ และข้อจำกัดคีย์หลักโดยอัตโนมัติ ทำให้มั่นใจว่าโค้ด SQL ที่สร้างขึ้นสามารถดำเนินการได้โดยตรง"
    from_alias: "Insert SQL"
    to_alias: "คำสั่ง SQL"
  Qlik:
      alias: "ตาราง Qlik"
      what: "Qlik เป็นผู้จำหน่ายซอฟต์แวร์ที่เชี่ยวชาญด้านการแสดงข้อมูลเป็นภาพ แดชบอร์ดผู้บริหาร และผลิตภัณฑ์ business intelligence แบบบริการตนเอง พร้อมกับ Tableau และ Microsoft"
      step1: ""
      step3: "สุดท้าย [เครื่องมือสร้างตาราง](#TableGenerator) จะแสดงผลลัพธ์การแปลง ใช้ใน Qlik Sense, Qlik AutoML, QlikView หรือซอฟต์แวร์ที่รองรับ Qlik อื่นๆ ของคุณ"
      from_alias: "ตาราง Qlik"
      to_alias: "ตาราง Qlik"
  DAX:
      alias: "ตาราง DAX"
      what: "DAX (Data Analysis Expressions) เป็นภาษาโปรแกรมที่ใช้ใน Microsoft Power BI สำหรับการสร้างคอลัมน์ที่คำนวณ การวัด และตารางแบบกำหนดเอง"
      step1: ""
      step3: "สุดท้าย [เครื่องมือสร้างตาราง](#TableGenerator) จะแสดงผลลัพธ์การแปลง ตามที่คาดไว้ จะใช้ในผลิตภัณฑ์ Microsoft หลายตัว รวมถึง Microsoft Power BI, Microsoft Analysis Services และ Microsoft Power Pivot สำหรับ Excel"
      from_alias: "ตาราง DAX"
      to_alias: "ตาราง DAX"
  Firebase:
    alias: "รายการ Firebase"
    what: "Firebase เป็นแพลตฟอร์มการพัฒนาแอปพลิเคชัน BaaS ที่ให้บริการแบ็กเอนด์ที่โฮสต์ เช่น ฐานข้อมูลแบบเรียลไทม์ การจัดเก็บคลาวด์ การยืนยันตัวตน การรายงานข้อขัดข้อง ฯลฯ"
    step1: ""
    step3: "สุดท้าย [เครื่องมือสร้างตาราง](#TableGenerator) จะแสดงผลลัพธ์การแปลง จากนั้นคุณสามารถใช้เมธอด push ใน Firebase API เพื่อเพิ่มไปยังรายการข้อมูลในฐานข้อมูล Firebase"
    from_alias: "รายการ Firebase"
    to_alias: "รายการ Firebase"
  HTML:
    alias: "ตาราง HTML"
    what: "ตาราง HTML เป็นวิธีมาตรฐานในการแสดงข้อมูลที่มีโครงสร้างในหน้าเว็บ สร้างด้วยแท็ก table, tr, td และอื่นๆ รองรับการปรับแต่งสไตล์ที่หลากหลาย เลย์เอาต์ที่ตอบสนอง และฟังก์ชันการโต้ตอบ ใช้กันอย่างแพร่หลายในการพัฒนาเว็บไซต์ การแสดงข้อมูล และการสร้างรายงาน ทำหน้าที่เป็นองค์ประกอบสำคัญของการพัฒนาส่วนหน้าและการออกแบบเว็บ"
    step1: "วางโค้ด HTML ที่มีตารางหรืออัปโหลดไฟล์ HTML เครื่องมือจะจดจำและแยกข้อมูลตารางจากหน้าเว็บโดยอัตโนมัติ รองรับโครงสร้าง HTML ที่ซับซ้อน สไตล์ CSS และการประมวลผลตารางที่ซ้อนกัน"
    step3: "สร้างโค้ดตาราง HTML เชิงความหมายพร้อมการรองรับโครงสร้าง thead/tbody การตั้งค่าคลาส CSS คำบรรยายตาราง หัวแถว/คอลัมน์ และการกำหนดค่าแอตทริบิวต์ที่ตอบสนอง ทำให้มั่นใจว่าโค้ดตารางที่สร้างขึ้นตรงตามมาตรฐานเว็บพร้อมการเข้าถึงที่ดีและเป็นมิตรกับ SEO"
    from_alias: "ตาราง HTML"
    to_alias: "ตาราง HTML"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel เป็นซอฟต์แวร์สเปรดชีตที่ได้รับความนิยมมากที่สุดในโลก ใช้กันอย่างแพร่หลายในการวิเคราะห์ทางธุรกิจ การจัดการทางการเงิน การประมวลผลข้อมูล และการสร้างรายงาน ความสามารถในการประมวลผลข้อมูลที่ทรงพลัง ไลบรารีฟังก์ชันที่หลากหลาย และฟีเจอร์การแสดงข้อมูลเป็นภาพที่ยืดหยุ่น ทำให้เป็นเครื่องมือมาตรฐานสำหรับการทำงานอัตโนมัติในสำนักงานและการวิเคราะห์ข้อมูล มีการใช้งานอย่างกว้างขวางในเกือบทุกอุตสาหกรรมและสาขา"
    step1: "อัปโหลดไฟล์ Excel (รองรับรูปแบบ .xlsx, .xls) หรือคัดลอกข้อมูลตารางโดยตรงจาก Excel และวาง เครื่องมือรองรับการประมวลผลหลายเวิร์กชีต การจดจำรูปแบบที่ซับซ้อน และการแยกวิเคราะห์ไฟล์ขนาดใหญ่อย่างรวดเร็ว จัดการเซลล์ที่ผสานและประเภทข้อมูลโดยอัตโนมัติ"
    step3: "สร้างข้อมูลตารางที่เข้ากันได้กับ Excel ที่สามารถวางลงใน Excel โดยตรงหรือดาวน์โหลดเป็นไฟล์ .xlsx มาตรฐาน รองรับการตั้งชื่อเวิร์กชีต การจัดรูปแบบเซลล์ ความกว้างคอลัมน์อัตโนมัติ การจัดรูปแบบหัวเรื่อง และการตั้งค่าการตรวจสอบข้อมูล ทำให้มั่นใจว่าไฟล์ Excel เอาต์พุตมีรูปลักษณ์ที่เป็นมืออาชีพและฟังก์ชันการทำงานที่สมบูรณ์"
    from_alias: "สเปรดชีต Excel"
    to_alias: "Excel"
  LaTeX:
    alias: "ตาราง LaTeX"
    what: "LaTeX เป็นระบบการจัดพิมพ์เอกสารแบบมืออาชีพ เหมาะสำหรับการสร้างเอกสารทางวิชาการ เอกสารทางเทคนิค และสิ่งพิมพ์ทางวิทยาศาสตร์เป็นพิเศษ ฟังก์ชันตารางของมันทรงพลัง รองรับสูตรทางคณิตศาสตร์ที่ซับซ้อน การควบคุมเลย์เอาต์ที่แม่นยำ และเอาต์พุต PDF คุณภาพสูง เป็นเครื่องมือมาตรฐานในวงการวิชาการและการเผยแพร่ทางวิทยาศาสตร์ ใช้กันอย่างแพร่หลายในเอกสารวารสาร วิทยานิพนธ์ และการจัดพิมพ์คู่มือทางเทคนิค"
    step1: "วางโค้ดตาราง LaTeX หรืออัปโหลดไฟล์ .tex เครื่องมือจะแยกวิเคราะห์ไวยากรณ์ตาราง LaTeX และแยกเนื้อหาข้อมูล รองรับสภาพแวดล้อมตารางหลายแบบ (tabular, longtable, array ฯลฯ) และคำสั่งรูปแบบที่ซับซ้อน"
    step3: "สร้างโค้ดตาราง LaTeX แบบมืออาชีพพร้อมการรองรับการเลือกสภาพแวดล้อมตารางหลายแบบ การกำหนดค่าสไตล์เส้นขอบ การตั้งค่าตำแหน่งคำบรรยาย การระบุคลาสเอกสาร และการจัดการแพ็กเกจ สามารถสร้างเอกสาร LaTeX ที่คอมไพล์ได้สมบูรณ์ ทำให้มั่นใจว่าตารางเอาต์พุตตรงตามมาตรฐานการเผยแพร่ทางวิชาการ"
    from_alias: "ตาราง LaTeX"
    to_alias: "ตาราง LaTeX"
  ASCII:
    alias: "ตารางข้อความ ASCII"
    what: "ตาราง ASCII ใช้อักขระข้อความธรรมดาในการวาดเส้นขอบและโครงสร้างตาราง ให้ความเข้ากันได้และความสามารถในการพกพาที่ดีที่สุด เข้ากันได้กับเครื่องมือแก้ไขข้อความทั้งหมด สภาพแวดล้อมเทอร์มินัล และระบบปฏิบัติการ ใช้กันอย่างแพร่หลายในเอกสารโค้ด คู่มือทางเทคนิค ไฟล์ README และเอาต์พุตเครื่องมือ command-line รูปแบบการแสดงข้อมูลที่โปรแกรมเมอร์และผู้ดูแลระบบชื่นชอบ"
    step1: "อัปโหลดไฟล์ข้อความที่มีตาราง ASCII หรือวางข้อมูลตารางโดยตรง เครื่องมือจะจดจำและแยกวิเคราะห์โครงสร้างตาราง ASCII อย่างชาญฉลาด รองรับสไตล์เส้นขอบหลายแบบและรูปแบบการจัดแนว"
    step3: "สร้างตาราง ASCII ข้อความธรรมดาที่สวยงามพร้อมการรองรับสไตล์เส้นขอบหลายแบบ (เส้นเดี่ยว เส้นคู่ มุมโค้ง ฯลฯ) วิธีการจัดแนวข้อความ และความกว้างคอลัมน์อัตโนมัติ ตารางที่สร้างขึ้นแสดงได้อย่างสมบูรณ์แบบในเครื่องมือแก้ไขโค้ด เอกสาร และ command line"
    from_alias: "ตารางข้อความ ASCII"
    to_alias: "ตารางข้อความ ASCII"
  MediaWiki:
    alias: "ตาราง MediaWiki"
    what: "MediaWiki เป็นแพลตฟอร์มซอฟต์แวร์โอเพ่นซอร์สที่ใช้โดยเว็บไซต์วิกิที่มีชื่อเสียงเช่น Wikipedia ไวยากรณ์ตารางของมันกระชับแต่ทรงพลัง รองรับการปรับแต่งสไตล์ตาราง ฟังก์ชันการเรียงลำดับ และการฝังลิงก์ ใช้กันอย่างแพร่หลายในการจัดการความรู้ การแก้ไขแบบร่วมมือ และระบบจัดการเนื้อหา ทำหน้าที่เป็นเทคโนโลยีหลักสำหรับการสร้างสารานุกรมวิกิและฐานความรู้"
    step1: "วางโค้ดตาราง MediaWiki หรืออัปโหลดไฟล์ซอร์สวิกิ เครื่องมือจะแยกวิเคราะห์ไวยากรณ์มาร์กอัปวิกิและแยกข้อมูลตาราง รองรับไวยากรณ์วิกิที่ซับซ้อนและการประมวลผลเทมเพลต"
    step3: "สร้างโค้ดตาราง MediaWiki มาตรฐานพร้อมการรองรับการตั้งค่าสไตล์หัวเรื่อง การจัดแนวเซลล์ การเปิดใช้งานฟังก์ชันการเรียงลำดับ และตัวเลือกการบีบอัดโค้ด โค้ดที่สร้างขึ้นสามารถใช้สำหรับการแก้ไขหน้าวิกิได้โดยตรง ทำให้มั่นใจได้ถึงการแสดงผลที่สมบูรณ์แบบบนแพลตฟอร์ม MediaWiki"
    from_alias: "ตาราง MediaWiki"
    to_alias: "ตาราง MediaWiki"
  TracWiki:
    alias: "ตาราง TracWiki"
    what: "Trac เป็นระบบการจัดการโปรเจ็กต์และการติดตามข้อบกพร่องบนเว็บที่ใช้ไวยากรณ์วิกิแบบง่ายในการสร้างเนื้อหาตาราง"
    step1: "อัปโหลดไฟล์ TracWiki หรือวางข้อมูลตาราง"
    step3: "สร้างโค้ดตารางที่เข้ากันได้กับ TracWiki พร้อมการรองรับการตั้งค่าหัวแถว/คอลัมน์ อำนวยความสะดวกในการจัดการเอกสารโปรเจ็กต์"
    from_alias: "ตาราง TracWiki"
    to_alias: "ตาราง TracWiki"
  AsciiDoc:
    alias: "ตาราง AsciiDoc"
    what: "AsciiDoc เป็นภาษามาร์กอัปน้ำหนักเบาที่สามารถแปลงเป็น HTML, PDF, หน้าคู่มือ และรูปแบบอื่นๆ ใช้กันอย่างแพร่หลายสำหรับการเขียนเอกสารทางเทคนิค"
    step1: "อัปโหลดไฟล์ AsciiDoc หรือวางข้อมูล"
    step3: "สร้างไวยากรณ์ตาราง AsciiDoc พร้อมการรองรับการตั้งค่าหัวเรื่อง ท้ายเรื่อง และชื่อเรื่อง ใช้ได้โดยตรงในเครื่องมือแก้ไข AsciiDoc"
    from_alias: "ตาราง AsciiDoc"
    to_alias: "ตาราง AsciiDoc"
  reStructuredText:
    alias: "ตาราง reStructuredText"
    what: "reStructuredText เป็นรูปแบบเอกสารมาตรฐานสำหรับชุมชน Python รองรับไวยากรณ์ตารางที่หลากหลาย ใช้กันทั่วไปสำหรับการสร้างเอกสาร Sphinx"
    step1: "อัปโหลดไฟล์ .rst หรือวางข้อมูล reStructuredText"
    step3: "สร้างตาราง reStructuredText มาตรฐานพร้อมการรองรับสไตล์เส้นขอบหลายแบบ ใช้ได้โดยตรงในโปรเจ็กต์เอกสาร Sphinx"
    from_alias: "ตาราง reStructuredText"
    to_alias: "ตาราง reStructuredText"
  PHP:
    alias: "อาร์เรย์ PHP"
    what: "PHP เป็นภาษาสคริปต์ฝั่งเซิร์ฟเวอร์ที่ได้รับความนิยม โดยอาร์เรย์เป็นโครงสร้างข้อมูลหลักของมัน ใช้กันอย่างแพร่หลายในการพัฒนาเว็บและการประมวลผลข้อมูล"
    step1: "อัปโหลดไฟล์ที่มีอาร์เรย์ PHP หรือวางข้อมูลโดยตรง"
    step3: "สร้างโค้ดอาร์เรย์ PHP มาตรฐานที่สามารถใช้ได้โดยตรงในโปรเจ็กต์ PHP รองรับรูปแบบอาร์เรย์แบบเชื่อมโยงและดัชนี"
    from_alias: "อาร์เรย์ PHP"
    to_alias: "โค้ด PHP"
  Ruby:
    alias: "อาร์เรย์ Ruby"
    what: "Ruby เป็นภาษาโปรแกรมเชิงวัตถุแบบไดนามิกที่มีไวยากรณ์กระชับและสง่างาม โดยอาร์เรย์เป็นโครงสร้างข้อมูลที่สำคัญ"
    step1: "อัปโหลดไฟล์ Ruby หรือวางข้อมูลอาร์เรย์"
    step3: "สร้างโค้ดอาร์เรย์ Ruby ที่สอดคล้องกับข้อกำหนดไวยากรณ์ Ruby ใช้ได้โดยตรงในโปรเจ็กต์ Ruby"
    from_alias: "อาร์เรย์ Ruby"
    to_alias: "โค้ด Ruby"
  ASP:
    alias: "อาร์เรย์ ASP"
    what: "ASP (Active Server Pages) เป็นสภาพแวดล้อมสคริปต์ฝั่งเซิร์ฟเวอร์ของ Microsoft รองรับภาษาโปรแกรมหลายภาษาสำหรับการพัฒนาหน้าเว็บแบบไดนามิก"
    step1: "อัปโหลดไฟล์ ASP หรือวางข้อมูลอาร์เรย์"
    step3: "สร้างโค้ดอาร์เรย์ที่เข้ากันได้กับ ASP พร้อมการรองรับไวยากรณ์ VBScript และ JScript ใช้ได้ในโปรเจ็กต์ ASP.NET"
    from_alias: "อาร์เรย์ ASP"
    to_alias: "โค้ด ASP"
  ActionScript:
    alias: "อาร์เรย์ ActionScript"
    what: "ActionScript เป็นภาษาโปรแกรมเชิงวัตถุที่ใช้หลักสำหรับการพัฒนาแอปพลิเคชัน Adobe Flash และ AIR"
    step1: "อัปโหลดไฟล์ .as หรือวางข้อมูล ActionScript"
    step3: "สร้างโค้ดอาร์เรย์ ActionScript ที่สอดคล้องกับมาตรฐานไวยากรณ์ AS3 ใช้ได้สำหรับการพัฒนาโปรเจ็กต์ Flash และ Flex"
    from_alias: "อาร์เรย์ ActionScript"
    to_alias: "โค้ด ActionScript"
  BBCode:
    alias: "ตาราง BBCode"
    what: "BBCode เป็นภาษามาร์กอัปน้ำหนักเบาที่ใช้กันทั่วไปในฟอรัมและชุมชนออนไลน์ ให้ฟังก์ชันการจัดรูปแบบแบบง่ายรวมถึงการรองรับตาราง"
    step1: "อัปโหลดไฟล์ที่มี BBCode หรือวางข้อมูล"
    step3: "สร้างโค้ดตาราง BBCode ที่เหมาะสำหรับการโพสต์ฟอรัมและการสร้างเนื้อหาชุมชน พร้อมการรองรับรูปแบบเอาต์พุตที่บีบอัด"
    from_alias: "ตาราง BBCode"
    to_alias: "ตาราง BBCode"
  PDF:
    alias: "ตาราง PDF"
    what: "PDF (Portable Document Format) เป็นมาตรฐานเอกสารข้ามแพลตฟอร์มที่มีเลย์เอาต์คงที่ การแสดงผลที่สม่ำเสมอ และลักษณะการพิมพ์คุณภาพสูง ใช้กันอย่างแพร่หลายในเอกสารทางการ รายงาน ใบแจ้งหนี้ สัญญา และเอกสารทางวิชาการ รูปแบบที่ต้องการสำหรับการสื่อสารทางธุรกิจและการเก็บถาวรเอกสาร ทำให้มั่นใจได้ถึงเอฟเฟกต์ภาพที่สอดคล้องกันอย่างสมบูรณ์ในอุปกรณ์และระบบปฏิบัติการที่แตกต่างกัน"
    step1: "นำเข้าข้อมูลตารางในรูปแบบใดก็ได้ เครื่องมือจะวิเคราะห์โครงสร้างข้อมูลโดยอัตโนมัติและทำการออกแบบเลย์เอาต์อย่างชาญฉลาด รองรับการแบ่งหน้าอัตโนมัติของตารางขนาดใหญ่และการประมวลผลประเภทข้อมูลที่ซับซ้อน"
    step3: "สร้างไฟล์ตาราง PDF คุณภาพสูงพร้อมการรองรับสไตล์ธีมแบบมืออาชีพหลายแบบ (ธุรกิจ วิชาการ มินิมอล ฯลฯ) ฟอนต์หลายภาษา การแบ่งหน้าอัตโนมัติ การเพิ่มลายน้ำ และการปรับปรุงการพิมพ์ ทำให้มั่นใจว่าเอกสาร PDF เอาต์พุตมีรูปลักษณ์ที่เป็นมืออาชีพ ใช้ได้โดยตรงสำหรับการนำเสนอทางธุรกิจและการเผยแพร่อย่างเป็นทางการ"
    from_alias: "ข้อมูลตาราง"
    to_alias: "ตาราง PDF"
  JPEG:
    alias: "รูปภาพ JPEG"
    what: "JPEG เป็นรูปแบบภาพดิจิทัลที่ใช้กันอย่างแพร่หลายที่สุดด้วยเอฟเฟกต์การบีบอัดที่ยอดเยี่ยมและความเข้ากันได้ที่กว้างขวาง ขนาดไฟล์เล็กและความเร็วในการโหลดที่รวดเร็วทำให้เหมาะสำหรับการแสดงเว็บ การแชร์โซเชียลมีเดีย ภาพประกอบเอกสาร และการนำเสนอออนไลน์ รูปแบบภาพมาตรฐานสำหรับสื่อดิจิทัลและการสื่อสารเครือข่าย ได้รับการสนับสนุนอย่างสมบูรณ์แบบจากอุปกรณ์และซอฟต์แวร์เกือบทั้งหมด"
    step1: "นำเข้าข้อมูลตารางในรูปแบบใดก็ได้ เครื่องมือจะทำการออกแบบเลย์เอาต์อย่างชาญฉลาดและการปรับปรุงภาพ คำนวณขนาดและความละเอียดที่เหมาะสมโดยอัตโนมัติ"
    step3: "สร้างภาพตาราง JPEG ความละเอียดสูงพร้อมการรองรับโทนสีธีมหลายแบบ (สว่าง มืด เป็นมิตรกับสายตา ฯลฯ) เลย์เอาต์ที่ปรับตัวได้ การปรับปรุงความชัดเจนของข้อความ และการปรับแต่งขนาด เหมาะสำหรับการแชร์ออนไลน์ การแทรกเอกสาร และการใช้งานการนำเสนอ ทำให้มั่นใจได้ถึงเอฟเฟกต์ภาพที่ยอดเยี่ยมบนอุปกรณ์แสดงผลต่างๆ"
    from_alias: "ข้อมูลตาราง"
    to_alias: "รูปภาพ JPEG"
  Jira:
    alias: "ตาราง Jira"
    what: "JIRA เป็นซอฟต์แวร์การจัดการโปรเจ็กต์และการติดตามข้อบกพร่องแบบมืออาชีพที่พัฒนาโดย Atlassian ใช้กันอย่างแพร่หลายในการพัฒนาแบบ agile การทดสอบซอฟต์แวร์ และการทำงานร่วมกันในโปรเจ็กต์ ฟังก์ชันตารางของมันรองรับตัวเลือกการจัดรูปแบบที่หลากหลายและการแสดงข้อมูล ทำหน้าที่เป็นเครื่องมือสำคัญสำหรับทีมพัฒนาซอฟต์แวร์ ผู้จัดการโปรเจ็กต์ และบุคลากรประกันคุณภาพในการจัดการความต้องการ การติดตามข้อบกพร่อง และการรายงานความคืบหน้า"
    step1: "อัปโหลดไฟล์ที่มีข้อมูลตารางหรือวางเนื้อหาข้อมูลโดยตรง เครื่องมือจะประมวลผลข้อมูลตารางและการ escape อักขระพิเศษโดยอัตโนมัติ"
    step3: "สร้างโค้ดตารางที่เข้ากันได้กับแพลตฟอร์ม JIRA พร้อมการรองรับการตั้งค่าสไตล์หัวเรื่อง การจัดแนวเซลล์ การประมวลผล escape อักขระ และการปรับปรุงรูปแบบ โค้ดที่สร้างขึ้นสามารถวางลงในคำอธิบายปัญหา JIRA ความคิดเห็น หรือหน้าวิกิได้โดยตรง ทำให้มั่นใจได้ถึงการแสดงผลและการเรนเดอร์ที่ถูกต้องในระบบ JIRA"
    from_alias: "ตาราง Jira"
    to_alias: "ตาราง Jira"
  Textile:
    alias: "ตาราง Textile"
    what: "Textile เป็นภาษามาร์กอัปน้ำหนักเบาที่กระชับด้วยไวยากรณ์ที่เรียบง่ายและเรียนรู้ง่าย ใช้กันอย่างแพร่หลายในระบบจัดการเนื้อหา แพลตฟอร์มบล็อก และระบบฟอรัม ไวยากรณ์ตารางของมันชัดเจนและใช้งานง่าย รองรับการจัดรูปแบบอย่างรวดเร็วและการตั้งค่าสไตล์ เครื่องมือที่เหมาะสำหรับผู้สร้างเนื้อหาและผู้ดูแลเว็บไซต์สำหรับการเขียนเอกสารอย่างรวดเร็วและการเผยแพร่เนื้อหา"
    step1: "อัปโหลดไฟล์รูปแบบ Textile หรือวางข้อมูลตาราง เครื่องมือจะแยกวิเคราะห์ไวยากรณ์มาร์กอัป Textile และแยกเนื้อหาตาราง"
    step3: "สร้างไวยากรณ์ตาราง Textile มาตรฐานพร้อมการรองรับมาร์กอัปหัวเรื่อง การจัดแนวเซลล์ การ escape อักขระพิเศษ และการปรับปรุงรูปแบบ โค้ดที่สร้างขึ้นสามารถใช้ได้โดยตรงในระบบ CMS แพลตฟอร์มบล็อก และระบบเอกสารที่รองรับ Textile ทำให้มั่นใจได้ถึงการเรนเดอร์และการแสดงเนื้อหาที่ถูกต้อง"
    from_alias: "ตาราง Textile"
    to_alias: "ตาราง Textile"
  PNG:
    alias: "รูปภาพ PNG"
    what: "PNG (Portable Network Graphics) เป็นรูปแบบภาพแบบไม่สูญเสียข้อมูลที่มีการบีบอัดที่ยอดเยี่ยมและการรองรับความโปร่งใส ใช้กันอย่างแพร่หลายในการออกแบบเว็บ กราฟิกดิจิทัล และการถ่ายภาพแบบมืออาชีพ คุณภาพสูงและความเข้ากันได้ที่กว้างขวางทำให้เหมาะสำหรับภาพหน้าจอ โลโก้ ไดอะแกรม และภาพใดๆ ที่ต้องการรายละเอียดที่คมชัดและพื้นหลังโปร่งใส"
    step1: "นำเข้าข้อมูลตารางในรูปแบบใดก็ได้ เครื่องมือจะทำการออกแบบเลย์เอาต์อย่างชาญฉลาดและการปรับปรุงภาพ คำนวณขนาดและความละเอียดที่เหมาะสมสำหรับเอาต์พุต PNG โดยอัตโนมัติ"
    step3: "สร้างภาพตาราง PNG คุณภาพสูงพร้อมการรองรับโทนสีธีมหลายแบบ พื้นหลังโปร่งใส เลย์เอาต์ที่ปรับตัวได้ และการปรับปรุงความชัดเจนของข้อความ เหมาะสำหรับการใช้งานเว็บ การแทรกเอกสาร และการนำเสนอแบบมืออาชีพด้วยคุณภาพภาพที่ยอดเยี่ยม"
    from_alias: ""
    to_alias: "รูปภาพ PNG"
  TOML:
    alias: "TOML"
    what: "TOML (Tom's Obvious, Minimal Language) เป็นรูปแบบไฟล์กำหนดค่าที่อ่านและเขียนง่าย ออกแบบมาให้ไม่คลุมเครือและเรียบง่าย ใช้กันอย่างแพร่หลายในโปรเจ็กต์ซอฟต์แวร์สมัยใหม่สำหรับการจัดการกำหนดค่า ไวยากรณ์ที่ชัดเจนและการพิมพ์ที่แข็งแกร่งทำให้เป็นตัวเลือกที่ยอดเยี่ยมสำหรับการตั้งค่าแอปพลิเคชันและไฟล์กำหนดค่าโปรเจ็กต์"
    step1: "อัปโหลดไฟล์ TOML หรือวางข้อมูลกำหนดค่า เครื่องมือจะแยกวิเคราะห์ไวยากรณ์ TOML และแยกข้อมูลกำหนดค่าที่มีโครงสร้าง"
    step3: "สร้างรูปแบบ TOML มาตรฐานพร้อมการรองรับโครงสร้างที่ซ้อนกัน ประเภทข้อมูล และความคิดเห็น ไฟล์ TOML ที่สร้างขึ้นเหมาะสำหรับการกำหนดค่าแอปพลิเคชัน เครื่องมือสร้าง และการตั้งค่าโปรเจ็กต์"
    from_alias: "TOML"
    to_alias: "รูปแบบ TOML"
  INI:
    alias: "INI"
    what: "ไฟล์ INI เป็นไฟล์กำหนดค่าแบบง่ายที่ใช้โดยแอปพลิเคชันและระบบปฏิบัติการจำนวนมาก โครงสร้างคู่คีย์-ค่าที่ตรงไปตรงมาทำให้อ่านและแก้ไขด้วยมือได้ง่าย ใช้กันอย่างแพร่หลายในแอปพลิเคชัน Windows ระบบเก่า และสถานการณ์การกำหนดค่าแบบง่ายที่ความสามารถในการอ่านของมนุษย์มีความสำคัญ"
    step1: "อัปโหลดไฟล์ INI หรือวางข้อมูลกำหนดค่า เครื่องมือจะแยกวิเคราะห์ไวยากรณ์ INI และแยกข้อมูลกำหนดค่าตามส่วน"
    step3: "สร้างรูปแบบ INI มาตรฐานพร้อมการรองรับส่วน ความคิดเห็น และประเภทข้อมูลต่างๆ ไฟล์ INI ที่สร้างขึ้นเข้ากันได้กับแอปพลิเคชันและระบบกำหนดค่าส่วนใหญ่"
    from_alias: "INI"
    to_alias: "รูปแบบ INI"
  Avro:
    alias: "สคีมา Avro"
    what: "Apache Avro เป็นระบบการซีเรียลไลซ์ข้อมูลที่ให้โครงสร้างข้อมูลที่หลากหลาย รูปแบบไบนารีที่กะทัดรัด และความสามารถในการพัฒนาสคีมา ใช้กันอย่างแพร่หลายในการประมวลผลข้อมูลขนาดใหญ่ คิวข้อความ และระบบแบบกระจาย การกำหนดสคีมาของมันรองรับประเภทข้อมูลที่ซับซ้อนและความเข้ากันได้ของเวอร์ชัน ทำให้เป็นเครื่องมือสำคัญสำหรับวิศวกรข้อมูลและสถาปนิกระบบ"
    step1: "อัปโหลดไฟล์สคีมา Avro หรือวางข้อมูล เครื่องมือจะแยกวิเคราะห์คำจำกัดความสคีมา Avro และแยกข้อมูลโครงสร้างตาราง"
    step3: "สร้างคำจำกัดความสคีมา Avro มาตรฐานพร้อมการรองรับการแมปประเภทข้อมูล ข้อจำกัดฟิลด์ และการตรวจสอบสคีมา สคีมาที่สร้างขึ้นสามารถใช้ได้โดยตรงในระบบนิเวศ Hadoop ระบบข้อความ Kafka และแพลตฟอร์มข้อมูลขนาดใหญ่อื่นๆ"
    from_alias: "สคีมา Avro"
    to_alias: "สคีมา Avro"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) เป็นกลไกของ Google ที่เป็นกลางต่อภาษาและแพลตฟอร์ม ขยายได้สำหรับการซีเรียลไลซ์ข้อมูลที่มีโครงสร้าง ใช้กันอย่างแพร่หลายในไมโครเซอร์วิส การพัฒนา API และการจัดเก็บข้อมูล รูปแบบไบนารีที่มีประสิทธิภาพและการพิมพ์ที่แข็งแกร่งทำให้เหมาะสำหรับแอปพลิเคชันประสิทธิภาพสูงและการสื่อสารข้ามภาษา"
    step1: "อัปโหลดไฟล์ .proto หรือวางคำจำกัดความ Protocol Buffer เครื่องมือจะแยกวิเคราะห์ไวยากรณ์ protobuf และแยกข้อมูลโครงสร้างข้อความ"
    step3: "สร้างคำจำกัดความ Protocol Buffer มาตรฐานพร้อมการรองรับประเภทข้อความ ตัวเลือกฟิลด์ และคำจำกัดความบริการ ไฟล์ .proto ที่สร้างขึ้นสามารถคอมไพล์สำหรับภาษาโปรแกรมหลายภาษา"
    from_alias: "Protocol Buffer"
    to_alias: "สคีมา Protobuf"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas เป็นไลบรารีการวิเคราะห์ข้อมูลที่ได้รับความนิยมมากที่สุดใน Python โดย DataFrame เป็นโครงสร้างข้อมูลหลักของมัน ให้ความสามารถในการจัดการข้อมูล การทำความสะอาด และการวิเคราะห์ที่ทรงพลัง ใช้กันอย่างแพร่หลายในวิทยาศาสตร์ข้อมูล การเรียนรู้ของเครื่อง และ business intelligence เครื่องมือที่ขาดไม่ได้สำหรับนักพัฒนา Python และนักวิเคราะห์ข้อมูล"
    step1: "อัปโหลดไฟล์ Python ที่มีโค้ด DataFrame หรือวางข้อมูล เครื่องมือจะแยกวิเคราะห์ไวยากรณ์ Pandas และแยกข้อมูลโครงสร้าง DataFrame"
    step3: "สร้างโค้ด Pandas DataFrame มาตรฐานพร้อมการรองรับข้อกำหนดประเภทข้อมูล การตั้งค่าดัชนี และการดำเนินการข้อมูล โค้ดที่สร้างขึ้นสามารถดำเนินการได้โดยตรงในสภาพแวดล้อม Python สำหรับการวิเคราะห์และประมวลผลข้อมูล"
    from_alias: "Pandas DataFrame"
    to_alias: "Pandas DataFrame"
  RDF:
    alias: "RDF Triple"
    what: "RDF (Resource Description Framework) เป็นแบบจำลองมาตรฐานสำหรับการแลกเปลี่ยนข้อมูลบนเว็บ ออกแบบมาเพื่อแสดงข้อมูลเกี่ยวกับทรัพยากรในรูปแบบกราฟ ใช้กันอย่างแพร่หลายในเว็บเชิงความหมาย กราฟความรู้ และแอปพลิเคชันข้อมูลที่เชื่อมโยง โครงสร้างสามส่วนของมันช่วยให้สามารถแสดงเมตาดาต้าที่หลากหลายและความสัมพันธ์เชิงความหมาย"
    step1: "อัปโหลดไฟล์ RDF หรือวางข้อมูลสามส่วน เครื่องมือจะแยกวิเคราะห์ไวยากรณ์ RDF และแยกความสัมพันธ์เชิงความหมายและข้อมูลทรัพยากร"
    step3: "สร้างรูปแบบ RDF มาตรฐานพร้อมการรองรับการซีเรียลไลซ์ต่างๆ (RDF/XML, Turtle, N-Triples) RDF ที่สร้างขึ้นสามารถใช้ในแอปพลิเคชันเว็บเชิงความหมาย ฐานความรู้ และระบบข้อมูลที่เชื่อมโยง"
    from_alias: "RDF"
    to_alias: "RDF Triple"
  MATLAB:
    alias: "อาร์เรย์ MATLAB"
    what: "MATLAB เป็นซอฟต์แวร์การคำนวณเชิงตัวเลขและการแสดงข้อมูลเป็นภาพประสิทธิภาพสูงที่ใช้กันอย่างแพร่หลายในการคำนวณทางวิศวกรรม การวิเคราะห์ข้อมูล และการพัฒนาอัลกอริทึม การดำเนินการอาร์เรย์และเมทริกซ์ของมันทรงพลัง รองรับการคำนวณทางคณิตศาสตร์ที่ซับซ้อนและการประมวลผลข้อมูล เครื่องมือที่จำเป็นสำหรับวิศวกร นักวิจัย และนักวิทยาศาสตร์ข้อมูล"
    step1: "อัปโหลดไฟล์ MATLAB .m หรือวางข้อมูลอาร์เรย์ เครื่องมือจะแยกวิเคราะห์ไวยากรณ์ MATLAB และแยกข้อมูลโครงสร้างอาร์เรย์"
    step3: "สร้างโค้ดอาร์เรย์ MATLAB มาตรฐานพร้อมการรองรับอาร์เรย์หลายมิติ ข้อกำหนดประเภทข้อมูล และการตั้งชื่อตัวแปร โค้ดที่สร้างขึ้นสามารถดำเนินการได้โดยตรงในสภาพแวดล้อม MATLAB สำหรับการวิเคราะห์ข้อมูลและการคำนวณทางวิทยาศาสตร์"
    from_alias: "อาร์เรย์ MATLAB"
    to_alias: "อาร์เรย์ MATLAB"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame เป็นโครงสร้างข้อมูลหลักในภาษาโปรแกรม R ใช้กันอย่างแพร่หลายในการวิเคราะห์ทางสถิติ การขุดข้อมูล และการเรียนรู้ของเครื่อง R เป็นเครื่องมือชั้นนำสำหรับการคำนวณทางสถิติและกราฟิก โดย DataFrame ให้ความสามารถในการจัดการข้อมูล การวิเคราะห์ทางสถิติ และการแสดงข้อมูลเป็นภาพที่ทรงพลัง จำเป็นสำหรับนักวิทยาศาสตร์ข้อมูล นักสถิติ และนักวิจัยที่ทำงานกับการวิเคราะห์ข้อมูลที่มีโครงสร้าง"
    step1: "อัปโหลดไฟล์ข้อมูล R หรือวางโค้ด DataFrame เครื่องมือจะแยกวิเคราะห์ไวยากรณ์ R และแยกข้อมูลโครงสร้าง DataFrame รวมถึงประเภทคอลัมน์ ชื่อแถว และเนื้อหาข้อมูล"
    step3: "สร้างโค้ด R DataFrame มาตรฐานพร้อมการรองรับข้อกำหนดประเภทข้อมูล ระดับปัจจัย ชื่อแถว/คอลัมน์ และโครงสร้างข้อมูลเฉพาะ R โค้ดที่สร้างขึ้นสามารถดำเนินการได้โดยตรงในสภาพแวดล้อม R สำหรับการวิเคราะห์ทางสถิติและการประมวลผลข้อมูล"
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "เริ่มแปลง"
  start_generating: "เริ่มสร้าง"
  api_docs: "เอกสาร API"
related:
  section_title: 'เครื่องมือแปลง {{ if and .from (ne .from "generator") }}{{ .from }} และ {{ end }}{{ .to }} เพิ่มเติม'
  section_description: 'สำรวจเครื่องมือแปลงเพิ่มเติมสำหรับรูปแบบ {{ if and .from (ne .from "generator") }}{{ .from }} และ {{ end }}{{ .to }} แปลงข้อมูลของคุณระหว่างหลายรูปแบบด้วยเครื่องมือแปลงออนไลน์แบบมืออาชีพของเรา'
  title: "{{ .from }} เป็น {{ .to }}"
howto:
  step2: "แก้ไขข้อมูลโดยใช้เครื่องมือแก้ไขตารางออนไลน์ขั้นสูงของเราพร้อมฟีเจอร์แบบมืออาชีพ รองรับการลบแถวว่าง การลบข้อมูลซ้ำ การสลับแถวคอลัมน์ การเรียงลำดับ การค้นหาและแทนที่ด้วย regex และการแสดงตัวอย่างแบบเรียลไทม์ การเปลี่ยนแปลงทั้งหมดจะแปลงเป็นรูปแบบ %s โดยอัตโนมัติด้วยผลลัพธ์ที่แม่นยำและเชื่อถือได้"
  section_title: "วิธีใช้ {{ . }}"
  converter_description: "เรียนรู้การแปลง {{ .from }} เป็น {{ .to }} ด้วยคู่มือทีละขั้นตอนของเรา เครื่องมือแปลงออนไลน์แบบมืออาชีพพร้อมฟีเจอร์ขั้นสูงและการแสดงตัวอย่างแบบเรียลไทม์"
  generator_description: "เรียนรู้การสร้างตาราง {{ .to }} แบบมืออาชีพด้วยเครื่องมือสร้างออนไลน์ของเรา การแก้ไขแบบ Excel การแสดงตัวอย่างแบบเรียลไทม์ และความสามารถในการส่งออกทันที"
extension:
  section_title: "ส่วนขยายการตรวจจับและแยกตาราง"
  section_description: "แยกตารางจากเว็บไซต์ใดก็ได้ด้วยการคลิกเพียงครั้งเดียว แปลงเป็น 30+ รูปแบบรวมถึง Excel, CSV, JSON ทันที - ไม่ต้องคัดลอกและวาง"
  features:
    extraction_title: "การแยกตารางด้วยคลิกเดียว"
    extraction_description: "แยกตารางจากหน้าเว็บใดก็ได้ทันทีโดยไม่ต้องคัดลอกและวาง - การแยกข้อมูลแบบมืออาชีพทำได้ง่าย"
    formats_title: "รองรับเครื่องมือแปลง 30+ รูปแบบ"
    formats_description: "แปลงตารางที่แยกออกมาเป็น Excel, CSV, JSON, Markdown, SQL และอื่นๆ ด้วยเครื่องมือแปลงตารางขั้นสูงของเรา"
    detection_title: "การตรวจจับตารางอัจฉริยะ"
    detection_description: "ตรวจจับและเน้นตารางบนหน้าเว็บใดก็ได้โดยอัตโนมัติเพื่อการแยกและแปลงข้อมูลที่รวดเร็ว"
  hover_tip: "✨ เลื่อนเมาส์ไปที่ตารางใดก็ได้เพื่อดูไอคอนการแยก"
recommendations:
  section_title: "แนะนำโดยมหาวิทยาลัยและผู้เชี่ยวชาญ"
  section_description: "TableConvert ได้รับความไว้วางใจจากผู้เชี่ยวชาญในมหาวิทยาลัย สถาบันวิจัย และทีมพัฒนาสำหรับการแปลงตารางและการประมวลผลข้อมูลที่เชื่อถือได้"
  cards:
    university_title: "University of Wisconsin-Madison"
    university_description: "TableConvert.com - เครื่องมือแปลงตารางออนไลน์ฟรีและเครื่องมือรูปแบบข้อมูลแบบมืออาชีพ"
    university_link: "อ่านบทความ"
    facebook_title: "ชุมชนผู้เชี่ยวชาญด้านข้อมูล"
    facebook_description: "แชร์และแนะนำโดยนักวิเคราะห์ข้อมูลและผู้เชี่ยวชาญในกลุ่มนักพัฒนา Facebook"
    facebook_link: "ดูโพสต์"
    twitter_title: "ชุมชนนักพัฒนา"
    twitter_description: "แนะนำโดย @xiaoying_eth และนักพัฒนาอื่นๆ บน X (Twitter) สำหรับการแปลงตาราง"
    twitter_link: "ดูทวีต"
faq:
  section_title: "คำถามที่พบบ่อย"
  section_description: "คำถามทั่วไปเกี่ยวกับเครื่องมือแปลงตารางออนไลน์ฟรีของเรา รูปแบบข้อมูล และกระบวนการแปลง"
  what: "รูปแบบ %s คืออะไร?"
  howto_convert:
    question: "วิธีใช้ {{ . }} ฟรี?"
    answer: "อัปโหลดไฟล์ {{ .from }} ของคุณ วางข้อมูล หรือแยกจากหน้าเว็บโดยใช้เครื่องมือแปลงตารางออนไลน์ฟรีของเรา เครื่องมือแปลงแบบมืออาชีพของเราจะแปลงข้อมูลของคุณเป็นรูปแบบ {{ .to }} ทันทีพร้อมการแสดงตัวอย่างแบบเรียลไทม์และฟีเจอร์การแก้ไขขั้นสูง ดาวน์โหลดหรือคัดลอกผลลัพธ์ที่แปลงแล้วทันที"
  security:
    question: "ข้อมูลของฉันปลอดภัยหรือไม่เมื่อใช้เครื่องมือแปลงออนไลน์นี้?"
    answer: "แน่นอน! การแปลงตารางทั้งหมดเกิดขึ้นในเบราว์เซอร์ของคุณในเครื่อง - ข้อมูลของคุณไม่เคยออกจากอุปกรณ์ของคุณ เครื่องมือแปลงออนไลน์ของเราประมวลผลทุกอย่างฝั่งไคลเอ็นต์ ทำให้มั่นใจได้ถึงความเป็นส่วนตัวและความปลอดภัยของข้อมูลอย่างสมบูรณ์ ไม่มีไฟล์ใดถูกเก็บไว้ในเซิร์ฟเวอร์ของเรา"
  free:
    question: "TableConvert ใช้ฟรีจริงหรือไม่?"
    answer: "ใช่ TableConvert ฟรีอย่างสมบูรณ์! ฟีเจอร์เครื่องมือแปลงทั้งหมด เครื่องมือแก้ไขตาราง เครื่องมือสร้างข้อมูล และตัวเลือกการส่งออกใช้ได้โดยไม่มีค่าใช้จ่าย การลงทะเบียน หรือค่าธรรมเนียมที่ซ่อนอยู่ แปลงไฟล์ไม่จำกัดออนไลน์ฟรี"
  filesize:
    question: "เครื่องมือแปลงออนไลน์มีข้อจำกัดขนาดไฟล์อย่างไร?"
    answer: "เครื่องมือแปลงตารางออนไลน์ฟรีของเรารองรับไฟล์ขนาดสูงสุด 10MB สำหรับไฟล์ขนาดใหญ่กว่า การประมวลผลแบบแบตช์ หรือความต้องการระดับองค์กร ใช้ส่วนขยายเบราว์เซอร์หรือบริการ API แบบมืออาชีพของเราที่มีขีดจำกัดสูงกว่า"
stats:
  conversions: "ตารางที่แปลงแล้ว"
  tables: "ตารางที่สร้างแล้ว"
  formats: "รูปแบบไฟล์ข้อมูล"
  rating: "คะแนนผู้ใช้"
