site:
  fullname: "ஆன்லைன் அட்டவணை மாற்றி"
  name: "TableConvert"
  subtitle: "இலவச ஆன்லைன் அட்டவணை மாற்றி மற்றும் ஜெனரேட்டர்"
  intro: "TableConvert என்பது Excel, CSV, JSON, Markdown, LaTeX, SQL மற்றும் பலவற்றை உள்ளடக்கிய 30+ வடிவங்களுக்கு இடையே மாற்றத்தை ஆதரிக்கும் இலவச ஆன்லைன் அட்டவணை மாற்றி மற்றும் தரவு ஜெனரேட்டர் கருவியாகும்."
  followTwitter: "X இல் எங்களைப் பின்தொடரவும்"
title:
  converter: "%s இலிருந்து %s க்கு"
  generator: "%s ஜெனரேட்டர்"
post:
  tags:
    converter: "மாற்றி"
    editor: "எடிட்டர்"
    generator: "ஜெனரேட்டர்"
    maker: "பில்டர்"
  converter:
    title: "%s ஐ %s ஆக ஆன்லைனில் மாற்றவும்"
    short: "இலவச மற்றும் சக்திவாய்ந்த %s இலிருந்து %s ஆன்லைன் கருவி"
    intro: "பயன்படுத்த எளிதான ஆன்லைன் %s இலிருந்து %s மாற்றி. எங்கள் உள்ளுணர்வு மாற்ற கருவியுடன் அட்டவணை தரவை எளிதாக மாற்றவும். வேகமான, நம்பகமான மற்றும் பயனர் நட்பு."
  generator:
    title: "ஆன்லைன் %s எடிட்டர் மற்றும் ஜெனரேட்டர்"
    short: "விரிவான அம்சங்களுடன் தொழில்முறை %s ஆன்லைன் உருவாக்க கருவி"
    intro: "பயன்படுத்த எளிதான ஆன்லைன் %s ஜெனரேட்டர் மற்றும் அட்டவணை எடிட்டர். எங்கள் உள்ளுணர்வு கருவி மற்றும் நேரடி முன்னோட்டத்துடன் தொழில்முறை தரவு அட்டவணைகளை எளிதாக உருவாக்கவும்."
navbar:
  search:
    placeholder: "மாற்றியைத் தேடவும்..."
  sponsor: "எங்களுக்கு காபி வாங்குங்கள்"
  extension: "நீட்டிப்பு"
  api: "API ஆவணங்கள்"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "தரவு மூலம்"
    placeholder: "உங்கள் %s தரவை ஒட்டவும் அல்லது %s கோப்புகளை இங்கே இழுக்கவும்"
    example: "உதாரணம்"
    upload: "கோப்பை பதிவேற்றவும்"
    extract:
      enter: "வலைப்பக்கத்திலிருந்து பிரித்தெடுக்கவும்"
      intro: "கட்டமைக்கப்பட்ட தரவை தானாக பிரித்தெடுக்க அட்டவணை தரவு கொண்ட வலைப்பக்க URL ஐ உள்ளிடவும்"
      btn: "%s ஐ பிரித்தெடுக்கவும்"
    excel:
      sheet: "பணித்தாள்"
      none: "எதுவுமில்லை"
  tableEditor:
    title: "ஆன்லைன் அட்டவணை எடிட்டர்"
    undo: "செயல்தவிர்"
    redo: "மீண்டும் செய்"
    transpose: "இடமாற்று"
    clear: "அழிக்கவும்"
    deleteBlank: "வெற்றுகளை நீக்கவும்"
    deleteDuplicate: "நகல்களை நீக்கவும்"
    uppercase: "பெரிய எழுத்துகள்"
    lowercase: "சிறிய எழுத்துகள்"
    capitalize: "முதல் எழுத்து பெரியது"
    replace:
      replace: "கண்டுபிடித்து மாற்றவும் (Regex ஆதரவு)"
      subst: "இதனால் மாற்றவும்..."
      btn: "அனைத்தையும் மாற்றவும்"
  tableGenerator:
    title: "அட்டவணை ஜெனரேட்டர்"
    sponsor: "எங்களுக்கு காபி வாங்குங்கள்"
    copy: "கிளிப்போர்டில் நகலெடுக்கவும்"
    download: "கோப்பை பதிவிறக்கவும்"
    tooltip:
      html:
        escape: "காட்சி பிழைகளைத் தடுக்க HTML சிறப்பு எழுத்துக்களை (&, <, >, \", ') எஸ்கேப் செய்யவும்"
        div: "பாரம்பரிய TABLE குறிச்சொற்களுக்குப் பதிலாக DIV+CSS அமைப்பைப் பயன்படுத்தவும், பதிலளிக்கும் வடிவமைப்புக்கு மிகவும் பொருத்தமானது"
        minify: "சுருக்கப்பட்ட HTML குறியீட்டை உருவாக்க வெள்ளை இடம் மற்றும் வரி முறிவுகளை அகற்றவும்"
        thead: "நிலையான அட்டவணை தலை (&lt;thead&gt;) மற்றும் உடல் (&lt;tbody&gt;) கட்டமைப்பை உருவாக்கவும்"
        tableCaption: "அட்டவணைக்கு மேலே விளக்க தலைப்பைச் சேர்க்கவும் (&lt;caption&gt; உறுப்பு)"
        tableClass: "எளிதான பாணி தனிப்பயனாக்கலுக்காக அட்டவணையில் CSS வகுப்பு பெயரைச் சேர்க்கவும்"
        tableId: "JavaScript கையாளுதலுக்காக அட்டவணைக்கு தனித்துவமான ID அடையாளங்காட்டியை அமைக்கவும்"
      jira:
        escape: "Jira அட்டவணை தொடரியல் மோதல்களைத் தவிர்க்க குழாய் எழுத்துக்களை (|) எஸ்கேப் செய்யவும்"
      json:
        parsingJSON: "செல்களில் உள்ள JSON சரங்களை புத்திசாலித்தனமாக பொருள்களாக பாகுபடுத்தவும்"
        minify: "கோப்பு அளவைக் குறைக்க சுருக்கமான ஒற்றை-வரி JSON வடிவத்தை உருவாக்கவும்"
        format: "வெளியீட்டு JSON தரவு கட்டமைப்பைத் தேர்ந்தெடுக்கவும்: பொருள் வரிசை, 2D வரிசை, போன்றவை"
      latex:
        escape: "சரியான தொகுப்பை உறுதிசெய்ய LaTeX சிறப்பு எழுத்துக்களை (%, &, _, #, $, போன்றவை) எஸ்கேப் செய்யவும்"
        ht: "பக்கத்தில் அட்டவணை நிலையைக் கட்டுப்படுத்த மிதக்கும் நிலை அளவுரு [!ht] ஐச் சேர்க்கவும்"
        mwe: "முழுமையான LaTeX ஆவணத்தை உருவாக்கவும்"
        tableAlign: "பக்கத்தில் அட்டவணையின் கிடைமட்ட சீரமைப்பை அமைக்கவும்"
        tableBorder: "அட்டவணை எல்லை பாணியை உள்ளமைக்கவும்: எல்லை இல்லை, பகுதி எல்லை, முழு எல்லை"
        label: "\\ref{} கட்டளை குறுக்கு-குறிப்புக்காக அட்டவணை லேபிளை அமைக்கவும்"
        caption: "அட்டவணைக்கு மேலே அல்லது கீழே காட்ட அட்டவணை தலைப்பை அமைக்கவும்"
        location: "அட்டவணை தலைப்பு காட்சி நிலையைத் தேர்ந்தெடுக்கவும்: மேலே அல்லது கீழே"
        tableType: "அட்டவணை சூழல் வகையைத் தேர்ந்தெடுக்கவும்: tabular, longtable, array, போன்றவை"
      markdown:
        escape: "வடிவ மோதல்களைத் தவிர்க்க Markdown சிறப்பு எழுத்துக்களை (*, _, |, \\, போன்றவை) எஸ்கேப் செய்யவும்"
        pretty: "மிகவும் அழகான அட்டவணை வடிவத்தை உருவாக்க நெடுவரிசை அகலங்களை தானாக-சீரமைக்கவும்"
        simple: "வெளிப்புற எல்லை செங்குத்து கோடுகளைத் தவிர்த்து எளிமைப்படுத்தப்பட்ட தொடரியலைப் பயன்படுத்தவும்"
        boldFirstRow: "முதல் வரி உரையை தடிமனாக்கவும்"
        boldFirstColumn: "முதல் நெடுவரிசை உரையை தடிமனாக்கவும்"
        firstHeader: "முதல் வரியை தலைப்பாகக் கருதி பிரிப்பான் கோட்டைச் சேர்க்கவும்"
        textAlign: "நெடுவரிசை உரை சீரமைப்பை அமைக்கவும்: இடது, மையம், வலது"
        multilineHandling: "பல வரி உரை கையாளுதல்: வரி முறிவுகளைப் பாதுகாக்கவும், \\n க்கு எஸ்கேப் செய்யவும், &lt;br&gt; குறிச்சொற்களைப் பயன்படுத்தவும்"

        includeLineNumbers: "அட்டவணையின் இடது பக்கத்தில் வரி எண் நெடுவரிசையைச் சேர்க்கவும்"
      magic:
        builtin: "முன்னரே வரையறுக்கப்பட்ட பொதுவான வார்ப்புரு வடிவங்களைத் தேர்ந்தெடுக்கவும்"
        rowsTpl: "<table> <tr> <th>மேஜிக் தொடரியல்</th> <th>விளக்கம்</th> <th>JS முறைகள் ஆதரவு</th> </tr> <tr> <td>{h1} {h2} ...</td> <td><b>தலைப்பின்</b> 1வது, 2வது ... புலம், அதாவது {hA} {hB} ...</td> <td>சரம் முறைகள்</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>தற்போதைய வரியின் 1வது, 2வது ... புலம், அதாவது {$A} {$B} ...</td> <td>சரம் முறைகள்</td> </tr> <tr> <td>{F,} {F;}</td> <td><b>F</b> க்குப் பிறகு உள்ள சரத்தால் தற்போதைய வரியைப் பிரிக்கவும்</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>தற்போதைய <b>வரியின்</b> வரி <b>எண்</b> 1 அல்லது 100 இலிருந்து</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>வரிகளின்</b> <b>இறுதி</b> வரி <b>எண்</b> </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>JavaScript குறியீட்டை <b>இயக்கவும்</b>, உதா: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> பிரேஸ்களை {...} வெளியிட பின்சாய்வு <b>\\</b> ஐப் பயன்படுத்தவும் </td> <td></td> </tr></table>"
        headerTpl: "தலைப்பு பிரிவுக்கான தனிப்பயன் வெளியீட்டு வார்ப்புரு"
        footerTpl: "அடிக்குறிப்பு பிரிவுக்கான தனிப்பயன் வெளியீட்டு வார்ப்புரு"
      textile:
        escape: "வடிவ மோதல்களைத் தவிர்க்க Textile தொடரியல் எழுத்துக்களை (|, ., -, ^) எஸ்கேப் செய்யவும்"
        rowHeader: "முதல் வரியை தலைப்பு வரியாக அமைக்கவும்"
        thead: "அட்டவணை தலை மற்றும் உடலுக்கு Textile தொடரியல் குறிப்பான்களைச் சேர்க்கவும்"
      xml:
        escape: "சரியான XML ஐ உறுதிசெய்ய XML சிறப்பு எழுத்துக்களை (&lt;, &gt;, &amp;, \", ') எஸ்கேப் செய்யவும்"
        minify: "கூடுதல் வெள்ளை இடத்தை அகற்றி சுருக்கப்பட்ட XML வெளியீட்டை உருவாக்கவும்"
        rootElement: "XML ரூட் உறுப்பு குறிச்சொல் பெயரை அமைக்கவும்"
        rowElement: "தரவின் ஒவ்வொரு வரிக்கும் XML உறுப்பு குறிச்சொல் பெயரை அமைக்கவும்"
        declaration: "XML அறிவிப்பு தலைப்பைச் சேர்க்கவும் (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "குழந்தை உறுப்புகளுக்குப் பதிலாக XML பண்புகளாக தரவை வெளியிடவும்"
        cdata: "சிறப்பு எழுத்துக்களைப் பாதுகாக்க CDATA உடன் உரை உள்ளடக்கத்தைச் சுற்றவும்"
        encoding: "XML ஆவணத்திற்கான எழுத்து குறியாக்க வடிவத்தை அமைக்கவும்"
        indentation: "XML உள்தள்ளல் எழுத்தைத் தேர்ந்தெடுக்கவும்: இடைவெளிகள் அல்லது தாவல்கள்"
      yaml:
        indentSize: "YAML படிநிலை உள்தள்ளலுக்கான இடைவெளிகளின் எண்ணிக்கையை அமைக்கவும் (பொதுவாக 2 அல்லது 4)"
        arrayStyle: "வரிசை வடிவம்: தொகுதி (ஒரு வரிக்கு ஒரு உருப்படி) அல்லது ஓட்டம் (இன்லைன் வடிவம்)"
        quotationStyle: "சரம் மேற்கோள் பாணி: மேற்கோள்கள் இல்லை, ஒற்றை மேற்கோள்கள், இரட்டை மேற்கோள்கள்"
      pdf:
        theme: "தொழில்முறை ஆவணங்களுக்கான PDF அட்டவணை காட்சி பாணியைத் தேர்ந்தெடுக்கவும்"
        headerColor: "PDF அட்டவணை தலைப்பு பின்னணி நிறத்தைத் தேர்ந்தெடுக்கவும்"
        showHead: "PDF பக்கங்களில் தலைப்பு காட்சியைக் கட்டுப்படுத்தவும்"
        docTitle: "PDF ஆவணத்திற்கான விருப்ப தலைப்பு"
        docDescription: "PDF ஆவணத்திற்கான விருப்ப விளக்க உரை"
      csv:
        bom: "Excel மற்றும் பிற மென்பொருள் குறியாக்கத்தை அடையாளம் காண உதவ UTF-8 பைட் ஆர்டர் மார்க்கைச் சேர்க்கவும்"
      excel:
        autoWidth: "உள்ளடக்கத்தின் அடிப்படையில் நெடுவரிசை அகலத்தை தானாக சரிசெய்யவும்"
        protectSheet: "கடவுச்சொல்லுடன் பணித்தாள் பாதுகாப்பை இயக்கவும்: tableconvert.com"
      sql:
        primaryKey: "CREATE TABLE அறிக்கைக்கான முதன்மை விசை புல பெயரைக் குறிப்பிடவும்"
        dialect: "தரவுத்தள வகையைத் தேர்ந்தெடுக்கவும், மேற்கோள் மற்றும் தரவு வகை தொடரியலைப் பாதிக்கிறது"
      ascii:
        forceSep: "தரவின் ஒவ்வொரு வரிக்கிடையில் பிரிப்பான் கோடுகளை கட்டாயப்படுத்தவும்"
        style: "ASCII அட்டவணை எல்லை வரைதல் பாணியைத் தேர்ந்தெடுக்கவும்"
        comment: "முழு அட்டவணையையும் சுற்ற கருத்து குறிப்பான்களைச் சேர்க்கவும்"
      mediawiki:
        minify: "கூடுதல் வெள்ளை இடத்தை அகற்றி வெளியீட்டு குறியீட்டை சுருக்கவும்"
        header: "முதல் வரியை தலைப்பு பாணியாகக் குறிக்கவும்"
        sort: "அட்டவணை கிளிக் வரிசைப்படுத்தல் செயல்பாட்டை இயக்கவும்"
      asciidoc:
        minify: "AsciiDoc வடிவ வெளியீட்டை சுருக்கவும்"
        firstHeader: "முதல் வரியை தலைப்பு வரியாக அமைக்கவும்"
        lastFooter: "கடைசி வரியை அடிக்குறிப்பு வரியாக அமைக்கவும்"
        title: "அட்டவணையில் தலைப்பு உரையைச் சேர்க்கவும்"
      tracwiki:
        rowHeader: "முதல் வரியை தலைப்பாக அமைக்கவும்"
        colHeader: "முதல் நெடுவரிசையை தலைப்பாக அமைக்கவும்"
      bbcode:
        minify: "BBCode வெளியீட்டு வடிவத்தை சுருக்கவும்"
      restructuredtext:
        style: "reStructuredText அட்டவணை எல்லை பாணியைத் தேர்ந்தெடுக்கவும்"
        forceSep: "பிரிப்பான் கோடுகளை கட்டாயப்படுத்தவும்"
    label:
      ascii:
        forceSep: "வரிசை பிரிப்பான்கள்"
        style: "எல்லை பாணி"
        comment: "கருத்து மடக்கி"
      restructuredtext:
        style: "எல்லை பாணி"
        forceSep: "கட்டாய பிரிப்பான்கள்"
      bbcode:
        minify: "வெளியீட்டை சுருக்கவும்"
      csv:
        doubleQuote: "இரட்டை மேற்கோள் மடக்கு"
        delimiter: "புல பிரிப்பான்"
        bom: "UTF-8 BOM"
        valueDelimiter: "மதிப்பு பிரிப்பான்"
        rowDelimiter: "வரிசை பிரிப்பான்"
        prefix: "வரிசை முன்னொட்டு"
        suffix: "வரிசை பின்னொட்டு"
      excel:
        autoWidth: "தானியங்கி அகலம்"
        textFormat: "உரை வடிவம்"
        protectSheet: "பணித்தாளைப் பாதுகாக்கவும்"
        boldFirstRow: "முதல் வரியை தடிமனாக்கவும்"
        boldFirstColumn: "முதல் நெடுவரிசையை தடிமனாக்கவும்"
        sheetName: "பணித்தாள் பெயர்"
      html:
        escape: "HTML எழுத்துக்களை எஸ்கேப் செய்யவும்"
        div: "DIV அட்டவணை"
        minify: "குறியீட்டை சுருக்கவும்"
        thead: "அட்டவணை தலை கட்டமைப்பு"
        tableCaption: "அட்டவணை தலைப்பு"
        tableClass: "அட்டவணை வகுப்பு"
        tableId: "அட்டவணை ID"
        rowHeader: "வரிசை தலைப்பு"
        colHeader: "நெடுவரிசை தலைப்பு"
      jira:
        escape: "எழுத்துக்களை எஸ்கேப் செய்யவும்"
        rowHeader: "வரிசை தலைப்பு"
        colHeader: "நெடுவரிசை தலைப்பு"
      json:
        parsingJSON: "JSON பாகுபடுத்தவும்"
        minify: "வெளியீட்டை சுருக்கவும்"
        format: "தரவு வடிவம்"
        rootName: "ரூட் ஆப்ஜெக்ட் பெயர்"
        indentSize: "உள்தள்ளல் அளவு"
      jsonlines:
        parsingJSON: "JSON பாகுபடுத்தவும்"
        format: "தரவு வடிவம்"
      latex:
        escape: "LaTeX அட்டவணை எழுத்துக்களை எஸ்கேப் செய்யவும்"
        ht: "மிதக்கும் நிலை"
        mwe: "முழுமையான ஆவணம்"
        tableAlign: "அட்டவணை சீரமைப்பு"
        tableBorder: "எல்லை பாணி"
        label: "குறிப்பு லேபிள்"
        caption: "அட்டவணை தலைப்பு"
        location: "தலைப்பு நிலை"
        tableType: "அட்டவணை வகை"
        boldFirstRow: "முதல் வரியை தடிமனாக்கவும்"
        boldFirstColumn: "முதல் நெடுவரிசையை தடிமனாக்கவும்"
        textAlign: "உரை சீரமைப்பு"
        borders: "எல்லை அமைப்புகள்"
      markdown:
        escape: "எழுத்துக்களை எஸ்கேப் செய்யவும்"
        pretty: "அழகான Markdown அட்டவணை"
        simple: "எளிய Markdown வடிவம்"
        boldFirstRow: "முதல் வரியை தடிமனாக்கவும்"
        boldFirstColumn: "முதல் நெடுவரிசையை தடிமனாக்கவும்"
        firstHeader: "முதல் தலைப்பு"
        textAlign: "உரை சீரமைப்பு"
        multilineHandling: "பல வரி கையாளுதல்"

        includeLineNumbers: "வரி எண்களைச் சேர்க்கவும்"
        align: "சீரமைப்பு"
      mediawiki:
        minify: "குறியீட்டை சுருக்கவும்"
        header: "தலைப்பு மார்க்அப்"
        sort: "வரிசைப்படுத்தக்கூடிய"
      asciidoc:
        minify: "வடிவத்தை சுருக்கவும்"
        firstHeader: "முதல் தலைப்பு"
        lastFooter: "கடைசி அடிக்குறிப்பு"
        title: "அட்டவணை தலைப்பு"
      tracwiki:
        rowHeader: "வரிசை தலைப்பு"
        colHeader: "நெடுவரிசை தலைப்பு"
      sql:
        drop: "அட்டவணையை நீக்கவும் (இருந்தால்)"
        create: "அட்டவணையை உருவாக்கவும்"
        oneInsert: "தொகுதி செருகல்"
        table: "அட்டவணை பெயர்"
        dialect: "தரவுத்தள வகை"
        primaryKey: "முதன்மை விசை"
      magic:
        builtin: "உள்ளமைக்கப்பட்ட டெம்ப்ளேட்"
        rowsTpl: "வரிசை டெம்ப்ளேட், தொடரியல் ->"
        headerTpl: "தலைப்பு டெம்ப்ளேட்"
        footerTpl: "அடிக்குறிப்பு டெம்ப்ளேட்"
      textile:
        escape: "எழுத்துக்களை எஸ்கேப் செய்யவும்"
        rowHeader: "வரிசை தலைப்பு"
        thead: "அட்டவணை தலை தொடரியல்"
      xml:
        escape: "XML எழுத்துக்களை எஸ்கேப் செய்யவும்"
        minify: "வெளியீட்டை சுருக்கவும்"
        rootElement: "ரூட் உறுப்பு"
        rowElement: "வரிசை உறுப்பு"
        declaration: "XML அறிவிப்பு"
        attributes: "பண்புக்கூறு முறை"
        cdata: "CDATA மடக்கி"
        encoding: "குறியாக்கம்"
        indentSize: "உள்தள்ளல் அளவு"
      yaml:
        indentSize: "உள்தள்ளல் அளவு"
        arrayStyle: "வரிசை பாணி"
        quotationStyle: "மேற்கோள் பாணி"
      pdf:
        theme: "PDF அட்டவணை தீம்"
        headerColor: "PDF தலைப்பு நிறம்"
        showHead: "PDF தலைப்பு காட்சி"
        docTitle: "PDF ஆவண தலைப்பு"
        docDescription: "PDF ஆவண விளக்கம்"
sidebar:
  all: "அனைத்து மாற்று கருவிகள்"
  dataSource:
    title: "தரவு மூலம்"
    description:
      converter: "%s ஐ %s ஆக மாற்றுவதற்காக இறக்குமதி செய்யவும். கோப்பு பதிவேற்றம், ஆன்லைன் எடிட்டிங் மற்றும் வலை தரவு பிரித்தெடுத்தலை ஆதரிக்கிறது."
      generator: "கையேடு உள்ளீடு, கோப்பு இறக்குமதி மற்றும் டெம்ப்ளேட் உருவாக்கம் உள்ளிட்ட பல உள்ளீட்டு முறைகளுக்கான ஆதரவுடன் அட்டவணை தரவை உருவாக்கவும்."
  tableEditor:
    title: "ஆன்லைன் அட்டவணை எடிட்டர்"
    description:
      converter: "எங்கள் அட்டவணை எடிட்டரைப் பயன்படுத்தி %s ஐ ஆன்லைனில் செயலாக்கவும். வெற்று வரிகளை நீக்குதல், நகல் நீக்கம், வரிசைப்படுத்துதல் மற்றும் கண்டுபிடித்து மாற்றுதல் ஆதரவுடன் Excel போன்ற செயல்பாட்டு அனுபவம்."
      generator: "Excel போன்ற செயல்பாட்டு அனுபவத்தை வழங்கும் சக்திவாய்ந்த ஆன்லைன் அட்டவணை எடிட்டர். வெற்று வரிகளை நீக்குதல், நகல் நீக்கம், வரிசைப்படுத்துதல் மற்றும் கண்டுபிடித்து மாற்றுதலை ஆதரிக்கிறது."
  tableGenerator:
    title: "அட்டவணை உருவாக்கி"
    description:
      converter: "அட்டவணை உருவாக்கியின் நிகழ்நேர முன்னோட்டத்துடன் %s ஐ விரைவாக உருவாக்கவும். பணக்கார ஏற்றுமதி விருப்பங்கள், ஒரு கிளிக் நகல் மற்றும் பதிவிறக்கம்."
      generator: "வெவ்வேறு பயன்பாட்டு சூழ்நிலைகளைப் பூர்த்தி செய்ய %s தரவை பல வடிவங்களில் ஏற்றுமதி செய்யவும். தனிப்பயன் விருப்பங்கள் மற்றும் நிகழ்நேர முன்னோட்டத்தை ஆதரிக்கிறது."
footer:
  changelog: "மாற்றங்களின் பதிவு"
  sponsor: "ஸ்பான்சர்கள்"
  contact: "எங்களைத் தொடர்பு கொள்ளவும்"
  privacyPolicy: "தனியுரிமைக் கொள்கை"
  about: "பற்றி"
  resources: "வளங்கள்"
  popularConverters: "பிரபலமான மாற்றிகள்"
  popularGenerators: "பிரபலமான ஜெனரேட்டர்கள்"
  dataSecurity: "உங்கள் தரவு பாதுகாப்பானது - அனைத்து மாற்றங்களும் உங்கள் உலாவியில் இயங்குகின்றன."
converters:
  Markdown:
    alias: "Markdown அட்டவணை"
    what: "Markdown தொழில்நுட்ப ஆவணங்கள், வலைப்பதிவு உள்ளடக்க உருவாக்கம் மற்றும் வலை மேம்பாட்டில் பரவலாகப் பயன்படுத்தப்படும் இலகுவான மார்க்அப் மொழியாகும். அதன் அட்டவணை தொடரியல் சுருக்கமானது மற்றும் உள்ளுணர்வு, உரை சீரமைப்பு, இணைப்பு உட்பொதிப்பு மற்றும் வடிவமைப்பை ஆதரிக்கிறது. இது நிரலாளர்கள் மற்றும் தொழில்நுட்ப எழுத்தாளர்களுக்கான விருப்பமான கருவியாகும், GitHub, GitLab மற்றும் பிற குறியீடு ஹோஸ்டிங் தளங்களுடன் சரியாக இணக்கமானது."
    step1: "Markdown அட்டவணை தரவை தரவு மூல பகுதியில் ஒட்டவும், அல்லது நேரடியாக .md கோப்புகளை இழுத்து விடவும். கருவி தானாகவே அட்டவணை கட்டமைப்பு மற்றும் வடிவமைப்பை பாகுபடுத்துகிறது, சிக்கலான உள்ளமைக்கப்பட்ட உள்ளடக்கம் மற்றும் சிறப்பு எழுத்து கையாளுதலை ஆதரிக்கிறது."
    step3: "பல சீரமைப்பு முறைகள், உரை தடிமனாக்கல், வரி எண் சேர்த்தல் மற்றும் பிற மேம்பட்ட வடிவ அமைப்புகளை ஆதரிக்கும் நிலையான Markdown அட்டவணை குறியீட்டை நிகழ்நேரத்தில் உருவாக்கவும். உருவாக்கப்பட்ட குறியீடு GitHub மற்றும் முக்கிய Markdown எடிட்டர்களுடன் முழுமையாக இணக்கமானது, ஒரு கிளிக் நகலுடன் பயன்படுத்த தயார்."
    from_alias: "Markdown அட்டவணை கோப்பு"
    to_alias: "Markdown அட்டவணை வடிவம்"
  Magic:
    alias: "தனிப்பயன் டெம்ப்ளேட்"
    what: "மேஜிக் டெம்ப்ளேட் இந்த கருவியின் தனித்துவமான மேம்பட்ட தரவு உருவாக்கியாகும், பயனர்கள் தனிப்பயன் டெம்ப்ளேட் தொடரியல் மூலம் தன்னிச்சையான வடிவ தரவு வெளியீட்டை உருவாக்க அனுமதிக்கிறது. மாறி மாற்றீடு, நிபந்தனை தீர்ப்பு மற்றும் லூப் செயலாக்கத்தை ஆதரிக்கிறது. சிக்கலான தரவு மாற்று தேவைகள் மற்றும் தனிப்பயனாக்கப்பட்ட வெளியீட்டு வடிவங்களைக் கையாளுவதற்கான இறுதி தீர்வு, குறிப்பாக டெவலப்பர்கள் மற்றும் தரவு பொறியாளர்களுக்கு ஏற்றது."
    step1: "உள்ளமைக்கப்பட்ட பொதுவான டெம்ப்ளேட்களைத் தேர்ந்தெடுக்கவும் அல்லது தனிப்பயன் டெம்ப்ளேட் தொடரியலை உருவாக்கவும். சிக்கலான தரவு கட்டமைப்புகள் மற்றும் வணிக தர்க்கத்தைக் கையாளக்கூடிய பணக்கார மாறிகள் மற்றும் செயல்பாடுகளை ஆதரிக்கிறது."
    step3: "தனிப்பயன் வடிவ தேவைகளை முழுமையாகப் பூர்த்தி செய்யும் தரவு வெளியீட்டை உருவாக்கவும். சிக்கலான தரவு மாற்று தர்க்கம் மற்றும் நிபந்தனை செயலாக்கத்தை ஆதரிக்கிறது, தரவு செயலாக்க திறன் மற்றும் வெளியீட்டு தரத்தை பெரிதும் மேம்படுத்துகிறது. தொகுதி தரவு செயலாக்கத்திற்கான சக்திவாய்ந்த கருவி."
    from_alias: "அட்டவணை தரவு"
    to_alias: "தனிப்பயன் வடிவ வெளியீடு"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) மிகவும் பரவலாகப் பயன்படுத்தப்படும் தரவு பரிமாற்ற வடிவமாகும், Excel, Google Sheets, தரவுத்தள அமைப்புகள் மற்றும் பல்வேறு தரவு பகுப்பாய்வு கருவிகளால் சரியாக ஆதரிக்கப்படுகிறது. அதன் எளிய கட்டமைப்பு மற்றும் வலுவான இணக்கத்தன்மை தரவு இடம்பெயர்வு, தொகுதி இறக்குமதி/ஏற்றுமதி மற்றும் குறுக்கு-தளம் தரவு பரிமாற்றத்திற்கான நிலையான வடிவமாக அமைகிறது, வணிக பகுப்பாய்வு, தரவு அறிவியல் மற்றும் அமைப்பு ஒருங்கிணைப்பில் பரவலாகப் பயன்படுத்தப்படுகிறது."
    step1: "CSV கோப்புகளைப் பதிவேற்றவும் அல்லது நேரடியாக CSV தரவை ஒட்டவும். கருவி பல்வேறு பிரிப்பான்களை (காற்புள்ளி, டேப், அரைப்புள்ளி, பைப் முதலியன) அறிவுபூர்வமாக அடையாளம் கண்டு, தானாகவே தரவு வகைகள் மற்றும் குறியாக்க வடிவங்களைக் கண்டறிகிறது, பெரிய கோப்புகள் மற்றும் சிக்கலான தரவு கட்டமைப்புகளின் வேகமான பாகுபடுத்தலை ஆதரிக்கிறது."
    step3: "தனிப்பயன் பிரிப்பான்கள், மேற்கோள் பாணிகள், குறியாக்க வடிவங்கள் மற்றும் BOM குறி அமைப்புகள் ஆதரவுடன் நிலையான CSV வடிவ கோப்புகளை உருவாக்கவும். இலக்கு அமைப்புகளுடன் சரியான இணக்கத்தன்மையை உறுதி செய்கிறது, நிறுவன-நிலை தரவு செயலாக்க தேவைகளைப் பூர்த்தி செய்ய பதிவிறக்கம் மற்றும் சுருக்க விருப்பங்களை வழங்குகிறது."
    from_alias: "CSV தரவு கோப்பு"
    to_alias: "CSV நிலையான வடிவம்"
  JSON:
    alias: "JSON வரிசை"
    what: "JSON (JavaScript Object Notation) நவீன வலை பயன்பாடுகள், REST API கள் மற்றும் மைக்ரோசர்வீஸ் கட்டமைப்புகளுக்கான நிலையான அட்டவணை தரவு வடிவமாகும். அதன் தெளிவான கட்டமைப்பு மற்றும் திறமையான பாகுபடுத்துதல் முன்-இறுதி மற்றும் பின்-இறுதி தரவு தொடர்பு, கட்டமைப்பு கோப்பு சேமிப்பு மற்றும் NoSQL தரவுத்தளங்களில் பரவலாகப் பயன்படுத்தப்படுகிறது. உள்ளமைக்கப்பட்ட பொருள்கள், வரிசை கட்டமைப்புகள் மற்றும் பல தரவு வகைகளை ஆதரிக்கிறது, இது நவீன மென்பொருள் மேம்பாட்டிற்கு இன்றியமையாத அட்டவணை தரவாக அமைகிறது."
    step1: "JSON கோப்புகளைப் பதிவேற்றவும் அல்லது JSON வரிசைகளை ஒட்டவும். பொருள் வரிசைகள், உள்ளமைக்கப்பட்ட கட்டமைப்புகள் மற்றும் சிக்கலான தரவு வகைகளின் தானியங்கி அங்கீகாரம் மற்றும் பாகுபடுத்துதலை ஆதரிக்கிறது. கருவி புத்திசாலித்தனமாக JSON தொடரியலை சரிபார்த்து பிழை அறிவிப்புகளை வழங்குகிறது."
    step3: "பல JSON வடிவ வெளியீடுகளை உருவாக்கவும்: நிலையான பொருள் வரிசைகள், 2D வரிசைகள், நெடுவரிசை வரிசைகள் மற்றும் விசை-மதிப்பு ஜோடி வடிவங்கள். அழகுபடுத்தப்பட்ட வெளியீடு, சுருக்க முறை, தனிப்பயன் ரூட் பொருள் பெயர்கள் மற்றும் உள்தள்ளல் அமைப்புகளை ஆதரிக்கிறது, பல்வேறு API இடைமுகங்கள் மற்றும் தரவு சேமிப்பு தேவைகளுக்கு சரியாக ஏற்றுக்கொள்கிறது."
    from_alias: "JSON வரிசை கோப்பு"
    to_alias: "JSON நிலையான வடிவம்"
  JSONLines:
    alias: "JSONLines வடிவம்"
    what: "JSON Lines (NDJSON என்றும் அழைக்கப்படுகிறது) பெரிய தரவு செயலாக்கம் மற்றும் ஸ்ட்ரீமிங் தரவு பரிமாற்றத்திற்கான முக்கியமான வடிவமாகும், ஒவ்வொரு வரியும் ஒரு சுயாதீன JSON பொருளைக் கொண்டுள்ளது. பதிவு பகுப்பாய்வு, தரவு ஸ்ட்ரீம் செயலாக்கம், இயந்திர கற்றல் மற்றும் விநியோகிக்கப்பட்ட அமைப்புகளில் பரவலாகப் பயன்படுத்தப்படுகிறது. அதிகரிக்கும் செயலாக்கம் மற்றும் இணையான கணினியை ஆதரிக்கிறது, இது பெரிய அளவிலான கட்டமைக்கப்பட்ட தரவைக் கையாளுவதற்கான சிறந்த தேர்வாக அமைகிறது."
    step1: "JSONLines கோப்புகளைப் பதிவேற்றவும் அல்லது தரவை ஒட்டவும். கருவி JSON பொருள்களை வரி வரியாக பாகுபடுத்துகிறது, பெரிய கோப்பு ஸ்ட்ரீமிங் செயலாக்கம் மற்றும் பிழை வரி தவிர்க்கும் செயல்பாட்டை ஆதரிக்கிறது."
    step3: "ஒவ்வொரு வரியும் முழுமையான JSON பொருளை வெளியிடும் நிலையான JSONLines வடிவத்தை உருவாக்கவும். ஸ்ட்ரீமிங் செயலாக்கம், தொகுதி இறக்குமதி மற்றும் பெரிய தரவு பகுப்பாய்வு சூழ்நிலைகளுக்கு ஏற்றது, தரவு சரிபார்ப்பு மற்றும் வடிவ மேம்படுத்தலை ஆதரிக்கிறது."
    from_alias: "JSONLines தரவு"
    to_alias: "JSONLines ஸ்ட்ரீமிங் வடிவம்"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) நிறுவன-நிலை தரவு பரிமாற்றம் மற்றும் கட்டமைப்பு மேலாண்மைக்கான நிலையான வடிவமாகும், கடுமையான தொடரியல் விவரக்குறிப்புகள் மற்றும் சக்திவாய்ந்த சரிபார்ப்பு வழிமுறைகளுடன். வலை சேவைகள், கட்டமைப்பு கோப்புகள், ஆவண சேமிப்பு மற்றும் அமைப்பு ஒருங்கிணைப்பில் பரவலாகப் பயன்படுத்தப்படுகிறது. பெயர்வெளிகள், ஸ்கீமா சரிபார்ப்பு மற்றும் XSLT மாற்றத்தை ஆதரிக்கிறது, இது நிறுவன பயன்பாடுகளுக்கு முக்கியமான அட்டவணை தரவாக அமைகிறது."
    step1: "XML கோப்புகளைப் பதிவேற்றவும் அல்லது XML தரவை ஒட்டவும். கருவி தானாகவே XML கட்டமைப்பை பாகுபடுத்தி அட்டவணை வடிவத்திற்கு மாற்றுகிறது, பெயர்வெளி, பண்புக்கூறு கையாளுதல் மற்றும் சிக்கலான உள்ளமைக்கப்பட்ட கட்டமைப்புகளை ஆதரிக்கிறது."
    step3: "XML தரநிலைகளுக்கு இணங்கும் XML வெளியீட்டை உருவாக்கவும். தனிப்பயன் ரூட் உறுப்புகள், வரிசை உறுப்பு பெயர்கள், பண்புக்கூறு முறைகள், CDATA மடக்குதல் மற்றும் எழுத்து குறியாக்க அமைப்புகளை ஆதரிக்கிறது. தரவு ஒருமைப்பாடு மற்றும் இணக்கத்தன்மையை உறுதி செய்கிறது, நிறுவன-நிலை பயன்பாட்டு தேவைகளைப் பூர்த்தி செய்கிறது."
    from_alias: "XML தரவு கோப்பு"
    to_alias: "XML நிலையான வடிவம்"
  YAML:
    alias: "YAML கட்டமைப்பு"
    what: "YAML மனித-நட்பு தரவு தொடர்மயமாக்கல் தரநிலையாகும், அதன் தெளிவான படிநிலை கட்டமைப்பு மற்றும் சுருக்கமான தொடரியலுக்காக புகழ்பெற்றது. கட்டமைப்பு கோப்புகள், DevOps கருவி சங்கிலிகள், Docker Compose மற்றும் Kubernetes வரிசைப்படுத்தலில் பரவலாகப் பயன்படுத்தப்படுகிறது. அதன் வலுவான படிக்கக்கூடிய தன்மை மற்றும் சுருக்கமான தொடரியல் நவீன கிளவுட்-நேட்டிவ் பயன்பாடுகள் மற்றும் தானியங்கு செயல்பாடுகளுக்கு முக்கியமான கட்டமைப்பு வடிவமாக அமைகிறது."
    step1: "YAML கோப்புகளைப் பதிவேற்றவும் அல்லது YAML தரவை ஒட்டவும். கருவி புத்திசாலித்தனமாக YAML கட்டமைப்பை பாகுபடுத்தி தொடரியல் சரியான தன்மையை சரிபார்க்கிறது, பல-ஆவண வடிவங்கள் மற்றும் சிக்கலான தரவு வகைகளை ஆதரிக்கிறது."
    step3: "தொகுதி மற்றும் ஓட்ட வரிசை பாணிகள், பல மேற்கோள் அமைப்புகள், தனிப்பயன் உள்தள்ளல் மற்றும் கருத்து பாதுகாப்பிற்கான ஆதரவுடன் நிலையான YAML வடிவ வெளியீட்டை உருவாக்கவும். வெளியீட்டு YAML கோப்புகள் பல்வேறு பாகுபடுத்திகள் மற்றும் கட்டமைப்பு அமைப்புகளுடன் முழுமையாக இணக்கமாக இருப்பதை உறுதி செய்கிறது."
    from_alias: "YAML கட்டமைப்பு கோப்பு"
    to_alias: "YAML நிலையான வடிவம்"
  MySQL:
      alias: "MySQL வினவல் முடிவுகள்"
      what: "MySQL உலகின் மிகவும் பிரபலமான திறந்த மூல தொடர்புடைய தரவுத்தள மேலாண்மை அமைப்பாகும், அதன் உயர் செயல்திறன், நம்பகத்தன்மை மற்றும் பயன்பாட்டின் எளிமைக்காக புகழ்பெற்றது. வலை பயன்பாடுகள், நிறுவன அமைப்புகள் மற்றும் தரவு பகுப்பாய்வு தளங்களில் பரவலாகப் பயன்படுத்தப்படுகிறது. MySQL வினவல் முடிவுகள் பொதுவாக கட்டமைக்கப்பட்ட அட்டவணை தரவைக் கொண்டுள்ளன, தரவுத்தள மேலாண்மை மற்றும் தரவு பகுப்பாய்வு வேலையில் முக்கியமான தரவு மூலமாக செயல்படுகின்றன."
      step1: "MySQL வினவல் வெளியீட்டு முடிவுகளை தரவு மூல பகுதியில் ஒட்டவும். கருவி தானாகவே MySQL கட்டளை-வரி வெளியீட்டு வடிவத்தை அங்கீகரித்து பாகுபடுத்துகிறது, பல்வேறு வினவல் முடிவு பாணிகள் மற்றும் எழுத்து குறியாக்கங்களை ஆதரிக்கிறது, தலைப்புகள் மற்றும் தரவு வரிகளை புத்திசாலித்தனமாக கையாளுகிறது."
      step3: "MySQL வினவல் முடிவுகளை பல அட்டவணை தரவு வடிவங்களுக்கு விரைவாக மாற்றவும், தரவு பகுப்பாய்வு, அறிக்கை உருவாக்கம், குறுக்கு-அமைப்பு தரவு இடம்பெயர்வு மற்றும் தரவு சரிபார்ப்பை எளிதாக்குகிறது. தரவுத்தள நிர்வாகிகள் மற்றும் தரவு ஆய்வாளர்களுக்கான நடைமுறை கருவி."
      from_alias: "MySQL வினவல் வெளியீடு"
      to_alias: "MySQL அட்டவணை தரவு"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) தொடர்புடைய தரவுத்தளங்களுக்கான நிலையான செயல்பாட்டு மொழியாகும், தரவு வினவல், செருகல், புதுப்பித்தல் மற்றும் நீக்குதல் செயல்பாடுகளுக்குப் பயன்படுத்தப்படுகிறது. தரவுத்தள மேலாண்மையின் முக்கிய தொழில்நுட்பமாக, SQL தரவு பகுப்பாய்வு, வணிக நுண்ணறிவு, ETL செயலாக்கம் மற்றும் தரவு கிடங்கு கட்டுமானத்தில் பரவலாகப் பயன்படுத்தப்படுகிறது. இது தரவு நிபுணர்களுக்கு இன்றியமையாத திறன் கருவியாகும்."
    step1: "INSERT SQL அறிக்கைகளை ஒட்டவும் அல்லது .sql கோப்புகளைப் பதிவேற்றவும். கருவி புத்திசாலித்தனமாக SQL தொடரியலை பாகுபடுத்தி அட்டவணை தரவை பிரித்தெடுக்கிறது, பல SQL பேச்சுவழக்குகள் மற்றும் சிக்கலான வினவல் அறிக்கை செயலாக்கத்தை ஆதரிக்கிறது."
    step3: "நிலையான SQL INSERT அறிக்கைகள் மற்றும் அட்டவணை உருவாக்க அறிக்கைகளை உருவாக்கவும். பல தரவுத்தள பேச்சுவழக்குகளை (MySQL, PostgreSQL, SQLite, SQL Server, Oracle) ஆதரிக்கிறது, தானாகவே தரவு வகை மேப்பிங், எழுத்து எஸ்கேப்பிங் மற்றும் முதன்மை விசை கட்டுப்பாடுகளைக் கையாளுகிறது. உருவாக்கப்பட்ட SQL குறியீட்டை நேரடியாக செயல்படுத்த முடியும் என்பதை உறுதி செய்கிறது."
    from_alias: "SQL தரவு கோப்பு"
    to_alias: "SQL நிலையான அறிக்கை"
  Qlik:
      alias: "Qlik அட்டவணை"
      what: "Qlik என்பது Tableau மற்றும் Microsoft உடன் சேர்ந்து தரவு காட்சிப்படுத்தல், நிர்வாக டாஷ்போர்டுகள் மற்றும் சுய-சேவை வணிக நுண்ணறிவு தயாரிப்புகளில் நிபுணத்துவம் பெற்ற மென்பொருள் விற்பனையாளர் ஆகும்."
      step1: ""
      step3: "இறுதியாக, [அட்டவணை உருவாக்கி](#TableGenerator) மாற்றல் முடிவுகளைக் காட்டுகிறது. உங்கள் Qlik Sense, Qlik AutoML, QlikView அல்லது பிற Qlik-இயக்கப்பட்ட மென்பொருளில் பயன்படுத்தவும்."
      from_alias: "Qlik அட்டவணை"
      to_alias: "Qlik அட்டவணை"
  DAX:
      alias: "DAX அட்டவணை"
      what: "DAX (Data Analysis Expressions) என்பது Microsoft Power BI முழுவதும் கணக்கிடப்பட்ட நெடுவரிசைகள், அளவீடுகள் மற்றும் தனிப்பயன் அட்டவணைகளை உருவாக்க பயன்படுத்தப்படும் நிரலாக்க மொழி ஆகும்."
      step1: ""
      step3: "இறுதியாக, [அட்டவணை உருவாக்கி](#TableGenerator) மாற்றல் முடிவுகளைக் காட்டுகிறது. எதிர்பார்த்தபடி, இது Microsoft Power BI, Microsoft Analysis Services மற்றும் Excel க்கான Microsoft Power Pivot உள்ளிட்ட பல Microsoft தயாரிப்புகளில் பயன்படுத்தப்படுகிறது."
      from_alias: "DAX அட்டவணை"
      to_alias: "DAX அட்டவணை"
  Firebase:
    alias: "Firebase பட்டியல்"
    what: "Firebase என்பது நிகழ்நேர தரவுத்தளம், மேக சேமிப்பு, அங்கீகாரம், செயலிழப்பு அறிக்கையிடல் போன்ற ஹோஸ்ட் செய்யப்பட்ட பின்தள சேவைகளை வழங்கும் BaaS பயன்பாட்டு மேம்பாட்டு தளம் ஆகும்."
    step1: ""
    step3: "இறுதியாக, [அட்டவணை உருவாக்கி](#TableGenerator) மாற்றல் முடிவுகளைக் காட்டுகிறது. பின்னர் Firebase தரவுத்தளத்தில் தரவுகளின் பட்டியலில் சேர்க்க Firebase API இல் push முறையைப் பயன்படுத்தலாம்."
    from_alias: "Firebase பட்டியல்"
    to_alias: "Firebase பட்டியல்"
  HTML:
    alias: "HTML அட்டவணை"
    what: "HTML அட்டவணைகள் என்பது table, tr, td மற்றும் பிற குறிச்சொற்களுடன் கட்டமைக்கப்பட்ட வலைப்பக்கங்களில் கட்டமைக்கப்பட்ட தரவைக் காட்சிப்படுத்துவதற்கான நிலையான வழி ஆகும். பணக்கார பாணி தனிப்பயனாக்கம், பதிலளிக்கும் அமைப்பு மற்றும் ஊடாடும் செயல்பாட்டை ஆதரிக்கிறது. வலைத்தள மேம்பாடு, தரவு காட்சி மற்றும் அறிக்கை உருவாக்கத்தில் பரவலாகப் பயன்படுத்தப்படுகிறது, முன்-இறுதி மேம்பாடு மற்றும் வலை வடிவமைப்பின் முக்கிய கூறுகளாக சேவை செய்கிறது."
    step1: "அட்டவணைகள் கொண்ட HTML குறியீட்டை ஒட்டவும் அல்லது HTML கோப்புகளை பதிவேற்றவும். கருவி தானாகவே பக்கங்களில் இருந்து அட்டவணை தரவை அடையாளம் கண்டு பிரித்தெடுக்கிறது, சிக்கலான HTML கட்டமைப்புகள், CSS பாணிகள் மற்றும் உள்ளமைக்கப்பட்ட அட்டவணை செயலாக்கத்தை ஆதரிக்கிறது."
    step3: "thead/tbody அமைப்பு, CSS வகுப்பு அமைப்புகள், அட்டவணை தலைப்புகள், வரிசை/நெடுவரிசை தலைப்புகள் மற்றும் பதிலளிக்கும் பண்புக்கூறு கட்டமைப்பு ஆதரவுடன் சொற்பொருள் HTML அட்டவணை குறியீட்டை உருவாக்கவும். உருவாக்கப்பட்ட அட்டவணை குறியீடு நல்ல அணுகல் மற்றும் SEO நட்பு தன்மையுடன் வலை தரநிலைகளைப் பூர்த்தி செய்வதை உறுதி செய்கிறது."
    from_alias: "HTML வலை அட்டவணை"
    to_alias: "HTML நிலையான அட்டவணை"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel என்பது உலகின் மிகவும் பிரபலமான விரிதாள் மென்பொருளாகும், இது வணிக பகுப்பாய்வு, நிதி மேலாண்மை, தரவு செயலாக்கம் மற்றும் அறிக்கை உருவாக்கத்தில் பரவலாகப் பயன்படுத்தப்படுகிறது. அதன் சக்திவாய்ந்த தரவு செயலாக்க திறன்கள், பணக்கார செயல்பாட்டு நூலகம் மற்றும் நெகிழ்வான காட்சிப்படுத்தல் அம்சங்கள் அதை அலுவலக தன்னியக்கம் மற்றும் தரவு பகுப்பாய்வுக்கான நிலையான கருவியாக ஆக்குகின்றன, கிட்டத்தட்ட அனைத்து தொழில்கள் மற்றும் துறைகளிலும் விரிவான பயன்பாடுகளைக் கொண்டுள்ளது."
    step1: "Excel கோப்புகளை பதிவேற்றவும் (.xlsx, .xls வடிவங்களை ஆதரிக்கிறது) அல்லது Excel இல் இருந்து அட்டவணை தரவை நேரடியாக நகலெடுத்து ஒட்டவும். கருவி பல-பணித்தாள் செயலாக்கம், சிக்கலான வடிவ அங்கீகாரம் மற்றும் பெரிய கோப்புகளின் வேகமான பாகுபடுத்தலை ஆதரிக்கிறது, ஒன்றிணைக்கப்பட்ட செல்கள் மற்றும் தரவு வகைகளை தானாகவே கையாளுகிறது."
    step3: "Excel இல் நேரடியாக ஒட்டக்கூடிய அல்லது நிலையான .xlsx கோப்புகளாக பதிவிறக்கம் செய்யக்கூடிய Excel-இணக்கமான அட்டவணை தரவை உருவாக்கவும். பணித்தாள் பெயரிடல், செல் வடிவமைத்தல், தானியங்கி நெடுவரிசை அகலம், தலைப்பு பாணி மற்றும் தரவு சரிபார்ப்பு அமைப்புகளை ஆதரிக்கிறது. வெளியேற்ற Excel கோப்புகள் தொழில்முறை தோற்றம் மற்றும் முழுமையான செயல்பாட்டைக் கொண்டிருப்பதை உறுதி செய்கிறது."
    from_alias: "Excel விரிதாள்"
    to_alias: "Excel நிலையான வடிவம்"
  LaTeX:
    alias: "LaTeX அட்டவணை"
    what: "LaTeX என்பது ஒரு தொழில்முறை ஆவண அச்சுமுறை அமைப்பு, குறிப்பாக கல்விப் பேப்பர்கள், தொழில்நுட்ப ஆவணங்கள் மற்றும் அறிவியல் வெளியீடுகளை உருவாக்குவதற்கு ஏற்றது. அதன் அட்டவணை செயல்பாடு சக்திவாய்ந்தது, சிக்கலான கணித சூத்திரங்கள், துல்லியமான அமைப்பு கட்டுப்பாடு மற்றும் உயர்தர PDF வெளியீட்டை ஆதரிக்கிறது. இது கல்வி மற்றும் அறிவியல் வெளியீட்டில் நிலையான கருவியாகும், இதழ் பேப்பர்கள், ஆய்வுக் கட்டுரைகள் மற்றும் தொழில்நுட்ப கையேடு அச்சுமுறையில் பரவலாகப் பயன்படுத்தப்படுகிறது."
    step1: "LaTeX அட்டவணை குறியீட்டை ஒட்டவும் அல்லது .tex கோப்புகளை பதிவேற்றவும். கருவி LaTeX அட்டவணை தொடரியல் பாகுபடுத்தி தரவு உள்ளடக்கத்தை பிரித்தெடுக்கிறது, பல அட்டவணை சூழல்கள் (tabular, longtable, array, முதலியன) மற்றும் சிக்கலான வடிவ கட்டளைகளை ஆதரிக்கிறது."
    step3: "பல அட்டவணை சூழல் தேர்வு, எல்லை பாணி கட்டமைப்பு, தலைப்பு நிலை அமைப்புகள், ஆவண வகுப்பு விவரக்குறிப்பு மற்றும் தொகுப்பு மேலாண்மை ஆதரவுடன் தொழில்முறை LaTeX அட்டவணை குறியீட்டை உருவாக்கவும். முழுமையான தொகுக்கக்கூடிய LaTeX ஆவணங்களை உருவாக்க முடியும், வெளியீட்டு அட்டவணைகள் கல்வி வெளியீட்டு தரநிலைகளைப் பூர்த்தி செய்வதை உறுதி செய்கிறது."
    from_alias: "LaTeX ஆவண அட்டவணை"
    to_alias: "LaTeX தொழில்முறை வடிவம்"
  ASCII:
    alias: "ASCII அட்டவணை"
    what: "ASCII அட்டவணைகள் அட்டவணை எல்லைகள் மற்றும் கட்டமைப்புகளை வரையவும் எளிய உரை எழுத்துக்களைப் பயன்படுத்துகின்றன, சிறந்த இணக்கத்தன்மை மற்றும் பெயர்வுத்திறனை வழங்குகின்றன. அனைத்து உரை எடிட்டர்கள், டெர்மினல் சூழல்கள் மற்றும் இயக்க அமைப்புகளுடன் இணக்கமானது. குறியீட்டு ஆவணங்கள், தொழில்நுட்ப கையேடுகள், README கோப்புகள் மற்றும் கட்டளை வரி கருவி வெளியீட்டில் பரவலாகப் பயன்படுத்தப்படுகிறது. நிரலாளர்கள் மற்றும் கணினி நிர்வாகிகளுக்கு விருப்பமான தரவு காட்சி வடிவம்."
    step1: "ASCII அட்டவணைகள் கொண்ட உரை கோப்புகளை பதிவேற்றவும் அல்லது நேரடியாக அட்டவணை தரவை ஒட்டவும். கருவி ASCII அட்டவணை கட்டமைப்புகளை அறிவுபூர்வமாக அடையாளம் கண்டு பாகுபடுத்துகிறது, பல எல்லை பாணிகள் மற்றும் சீரமைப்பு வடிவங்களை ஆதரிக்கிறது."
    step3: "பல எல்லை பாணிகள் (ஒற்றை வரி, இரட்டை வரி, வட்டமான மூலைகள் முதலியன), உரை சீரமைப்பு முறைகள் மற்றும் தானியங்கி நெடுவரிசை அகலம் ஆதரவுடன் அழகான எளிய உரை ASCII அட்டவணைகளை உருவாக்கவும். உருவாக்கப்பட்ட அட்டவணைகள் குறியீட்டு எடிட்டர்கள், ஆவணங்கள் மற்றும் கட்டளை வரிகளில் சரியாக காட்சிப்படுத்தப்படுகின்றன."
    from_alias: "ASCII உரை அட்டவணை"
    to_alias: "ASCII நிலையான வடிவம்"
  MediaWiki:
    alias: "MediaWiki அட்டவணை"
    what: "MediaWiki என்பது Wikipedia போன்ற பிரபலமான விக்கி தளங்களால் பயன்படுத்தப்படும் திறந்த மூல மென்பொருள் தளமாகும். அதன் அட்டவணை தொடரியல் சுருக்கமானது ஆனால் சக்திவாய்ந்தது, அட்டவணை பாணி தனிப்பயனாக்கம், வரிசைப்படுத்தும் செயல்பாடு மற்றும் இணைப்பு உட்பொதிப்பை ஆதரிக்கிறது. அறிவு மேலாண்மை, கூட்டு எடிட்டிங் மற்றும் உள்ளடக்க மேலாண்மை அமைப்புகளில் பரவலாகப் பயன்படுத்தப்படுகிறது, விக்கி கலைக்களஞ்சியங்கள் மற்றும் அறிவுத் தளங்களை உருவாக்குவதற்கான முக்கிய தொழில்நுட்பமாக சேவை செய்கிறது."
    step1: "MediaWiki அட்டவணை குறியீட்டை ஒட்டவும் அல்லது விக்கி மூல கோப்புகளை பதிவேற்றவும். கருவி விக்கி மார்க்அப் தொடரியல் பாகுபடுத்தி அட்டவணை தரவை பிரித்தெடுக்கிறது, சிக்கலான விக்கி தொடரியல் மற்றும் டெம்ப்ளேட் செயலாக்கத்தை ஆதரிக்கிறது."
    step3: "தலைப்பு பாணி அமைப்புகள், செல் சீரமைப்பு, வரிசைப்படுத்தும் செயல்பாட்டை இயக்குதல் மற்றும் குறியீடு சுருக்க விருப்பங்கள் ஆதரவுடன் நிலையான MediaWiki அட்டவணை குறியீட்டை உருவாக்கவும். உருவாக்கப்பட்ட குறியீடு நேரடியாக விக்கி பக்க எடிட்டிங்கிற்கு பயன்படுத்தப்படலாம், MediaWiki தளங்களில் சரியான காட்சியை உறுதி செய்கிறது."
    from_alias: "MediaWiki மூல குறியீடு"
    to_alias: "MediaWiki அட்டவணை தொடரியல்"
  TracWiki:
    alias: "TracWiki அட்டவணை"
    what: "Trac என்பது அட்டவணை உள்ளடக்கத்தை உருவாக்க எளிமைப்படுத்தப்பட்ட விக்கி தொடரியலைப் பயன்படுத்தும் வலை அடிப்படையிலான திட்ட மேலாண்மை மற்றும் பிழை கண்காணிப்பு அமைப்பு ஆகும்."
    step1: "TracWiki கோப்புகளை பதிவேற்றவும் அல்லது அட்டவணை தரவை ஒட்டவும்."
    step3: "வரிசை/நெடுவரிசை தலைப்பு அமைப்புகள் ஆதரவுடன் TracWiki-இணக்கமான அட்டவணை குறியீட்டை உருவாக்கவும், திட்ட ஆவண மேலாண்மையை எளிதாக்குகிறது."
    from_alias: "TracWiki அட்டவணை"
    to_alias: "TracWiki வடிவம்"
  AsciiDoc:
    alias: "AsciiDoc அட்டவணை"
    what: "AsciiDoc என்பது HTML, PDF, கையேடு பக்கங்கள் மற்றும் பிற வடிவங்களாக மாற்றக்கூடிய ஒரு இலகுவான மார்க்அப் மொழியாகும், தொழில்நுட்ப ஆவண எழுத்துக்கு பரவலாகப் பயன்படுத்தப்படுகிறது."
    step1: "AsciiDoc கோப்புகளை பதிவேற்றவும் அல்லது தரவை ஒட்டவும்."
    step3: "தலைப்பு, அடிக்குறிப்பு மற்றும் தலைப்பு அமைப்புகள் ஆதரவுடன் AsciiDoc அட்டவணை தொடரியலை உருவாக்கவும், AsciiDoc எடிட்டர்களில் நேரடியாக பயன்படுத்தக்கூடியது."
    from_alias: "AsciiDoc அட்டவணை"
    to_alias: "AsciiDoc வடிவம்"
  reStructuredText:
    alias: "reStructuredText அட்டவணை"
    what: "reStructuredText என்பது Python சமூகத்தின் நிலையான ஆவண வடிவமாகும், பணக்கார அட்டவணை தொடரியலை ஆதரிக்கிறது, பொதுவாக Sphinx ஆவண உருவாக்கத்திற்கு பயன்படுத்தப்படுகிறது."
    step1: ".rst கோப்புகளை பதிவேற்றவும் அல்லது reStructuredText தரவை ஒட்டவும்."
    step3: "பல எல்லை பாணிகள் ஆதரவுடன் நிலையான reStructuredText அட்டவணைகளை உருவாக்கவும், Sphinx ஆவண திட்டங்களில் நேரடியாக பயன்படுத்தக்கூடியது."
    from_alias: "reStructuredText அட்டவணை"
    to_alias: "reStructuredText வடிவம்"
  PHP:
    alias: "PHP வரிசை"
    what: "PHP என்பது ஒரு பிரபலமான சர்வர்-பக்க ஸ்கிரிப்டிங் மொழியாகும், வரிசைகள் அதன் முக்கிய தரவு கட்டமைப்பாகும், வலை மேம்பாடு மற்றும் தரவு செயலாக்கத்தில் பரவலாகப் பயன்படுத்தப்படுகிறது."
    step1: "PHP வரிசைகள் கொண்ட கோப்புகளை பதிவேற்றவும் அல்லது நேரடியாக தரவை ஒட்டவும்."
    step3: "PHP திட்டங்களில் நேரடியாகப் பயன்படுத்தக்கூடிய நிலையான PHP வரிசை குறியீட்டை உருவாக்கவும், கூட்டு மற்றும் அட்டவணைப்படுத்தப்பட்ட வரிசை வடிவங்களை ஆதரிக்கிறது."
    from_alias: "PHP வரிசை"
    to_alias: "PHP குறியீடு"
  Ruby:
    alias: "Ruby வரிசை"
    what: "Ruby என்பது சுருக்கமான மற்றும் நேர்த்தியான தொடரியலுடன் கூடிய ஒரு டைனமிக் ஆப்ஜெக்ட்-ஓரியண்டட் நிரலாக்க மொழியாகும், வரிசைகள் ஒரு முக்கியமான தரவு கட்டமைப்பாகும்."
    step1: "Ruby கோப்புகளை பதிவேற்றவும் அல்லது வரிசை தரவை ஒட்டவும்."
    step3: "Ruby தொடரியல் விவரக்குறிப்புகளுக்கு இணங்கும் Ruby வரிசை குறியீட்டை உருவாக்கவும், Ruby திட்டங்களில் நேரடியாகப் பயன்படுத்தக்கூடியது."
    from_alias: "Ruby வரிசை"
    to_alias: "Ruby குறியீடு"
  ASP:
    alias: "ASP வரிசை"
    what: "ASP (Active Server Pages) என்பது Microsoft இன் சர்வர்-பக்க ஸ்கிரிப்டிங் சூழலாகும், டைனமிக் வலைப்பக்கங்களை உருவாக்க பல நிரலாக்க மொழிகளை ஆதரிக்கிறது."
    step1: "ASP கோப்புகளை பதிவேற்றவும் அல்லது வரிசை தரவை ஒட்டவும்."
    step3: "VBScript மற்றும் JScript தொடரியல் ஆதரவுடன் ASP-இணக்கமான வரிசை குறியீட்டை உருவாக்கவும், ASP.NET திட்டங்களில் பயன்படுத்தக்கூடியது."
    from_alias: "ASP வரிசை"
    to_alias: "ASP குறியீடு"
  ActionScript:
    alias: "ActionScript வரிசை"
    what: "ActionScript என்பது முதன்மையாக Adobe Flash மற்றும் AIR பயன்பாட்டு மேம்பாட்டிற்காக பயன்படுத்தப்படும் ஒரு ஆப்ஜெக்ட்-ஓரியண்டட் நிரலாக்க மொழியாகும்."
    step1: ".as கோப்புகளை பதிவேற்றவும் அல்லது ActionScript தரவை ஒட்டவும்."
    step3: "AS3 தொடரியல் தரநிலைகளுக்கு இணங்கும் ActionScript வரிசை குறியீட்டை உருவாக்கவும், Flash மற்றும் Flex திட்ட மேம்பாட்டிற்கு பயன்படுத்தக்கூடியது."
    from_alias: "ActionScript வரிசை"
    to_alias: "ActionScript குறியீடு"
  BBCode:
    alias: "BBCode அட்டவணை"
    what: "BBCode என்பது மன்றங்கள் மற்றும் ஆன்லைன் சமூகங்களில் பொதுவாகப் பயன்படுத்தப்படும் ஒரு இலகுவான மார்க்அப் மொழியாகும், அட்டவணை ஆதரவு உட்பட எளிய வடிவமைப்பு செயல்பாட்டை வழங்குகிறது."
    step1: "BBCode கொண்ட கோப்புகளை பதிவேற்றவும் அல்லது தரவை ஒட்டவும்."
    step3: "மன்ற இடுகையிடல் மற்றும் சமூக உள்ளடக்க உருவாக்கத்திற்கு ஏற்ற BBCode அட்டவணை குறியீட்டை உருவாக்கவும், சுருக்கப்பட்ட வெளியீட்டு வடிவ ஆதரவுடன்."
    from_alias: "BBCode அட்டவணை"
    to_alias: "BBCode வடிவம்"
  PDF:
    alias: "PDF அட்டவணை"
    what: "PDF (Portable Document Format) நிலையான அமைப்பு, நிலையான காட்சி மற்றும் உயர்தர அச்சிடும் பண்புகளுடன் கூடிய குறுக்கு-தளம் ஆவண தரநிலையாகும். முறையான ஆவணங்கள், அறிக்கைகள், விலைப்பட்டியல்கள், ஒப்பந்தங்கள் மற்றும் கல்விப் பேப்பர்களில் பரவலாகப் பயன்படுத்தப்படுகிறது. வணிக தொடர்பு மற்றும் ஆவண காப்பகத்திற்கான விருப்பமான வடிவம், வெவ்வேறு சாதனங்கள் மற்றும் இயக்க அமைப்புகளில் முற்றிலும் நிலையான காட்சி விளைவுகளை உறுதி செய்கிறது."
    step1: "எந்த வடிவத்திலும் அட்டவணை தரவை இறக்குமதி செய்யவும். கருவி தானாகவே தரவு கட்டமைப்பை பகுப்பாய்வு செய்து அறிவுபூர்வமான அமைப்பு வடிவமைப்பை செய்கிறது, பெரிய அட்டவணை தானியங்கி பக்க பிரிவு மற்றும் சிக்கலான தரவு வகை செயலாக்கத்தை ஆதரிக்கிறது."
    step3: "பல தொழில்முறை தீம் பாணிகள் (வணிகம், கல்வி, குறைந்தபட்சம் முதலியன), பல மொழி எழுத்துருக்கள், தானியங்கி பக்க பிரிவு, வாட்டர்மார்க் சேர்த்தல் மற்றும் அச்சு மேம்படுத்தல் ஆதரவுடன் உயர்தர PDF அட்டவணை கோப்புகளை உருவாக்கவும். வெளியீட்டு PDF ஆவணங்கள் தொழில்முறை தோற்றத்தைக் கொண்டிருப்பதை உறுதி செய்கிறது, வணிக விளக்கக்காட்சிகள் மற்றும் முறையான வெளியீட்டிற்கு நேரடியாகப் பயன்படுத்தக்கூடியது."
    from_alias: "அட்டவணை தரவு"
    to_alias: "PDF தொழில்முறை ஆவணம்"
  JPEG:
    alias: "JPEG படம்"
    what: "JPEG சிறந்த சுருக்க விளைவுகள் மற்றும் பரந்த இணக்கத்தன்மையுடன் மிகவும் பரவலாகப் பயன்படுத்தப்படும் டிஜிட்டல் படம் வடிவமாகும். அதன் சிறிய கோப்பு அளவு மற்றும் வேகமான ஏற்றுதல் வேகம் வலை காட்சி, சமூக ஊடக பகிர்வு, ஆவண விளக்கப்படங்கள் மற்றும் ஆன்லைன் விளக்கக்காட்சிகளுக்கு ஏற்றதாக ஆக்குகிறது. டிஜிட்டல் மீடியா மற்றும் நெட்வொர்க் தொடர்புக்கான நிலையான படம் வடிவம், கிட்டத்தட்ட அனைத்து சாதனங்கள் மற்றும் மென்பொருளால் சரியாக ஆதரிக்கப்படுகிறது."
    step1: "எந்த வடிவத்திலும் அட்டவணை தரவை இறக்குமதி செய்யவும். கருவி அறிவுபூர்வமான அமைப்பு வடிவமைப்பு மற்றும் காட்சி மேம்படுத்தலை செய்கிறது, தானாகவே உகந்த அளவு மற்றும் தெளிவுத்திறனை கணக்கிடுகிறது."
    step3: "பல தீம் வண்ண திட்டங்கள் (ஒளி, இருள், கண்-நட்பு முதலியன), தகவமைப்பு அமைப்பு, உரை தெளிவு மேம்படுத்தல் மற்றும் அளவு தனிப்பயனாக்கம் ஆதரவுடன் உயர்-வரையறை JPEG அட்டவணை படங்களை உருவாக்கவும். ஆன்லைன் பகிர்வு, ஆவண செருகல் மற்றும் விளக்கக்காட்சி பயன்பாட்டிற்கு ஏற்றது, பல்வேறு காட்சி சாதனங்களில் சிறந்த காட்சி விளைவுகளை உறுதி செய்கிறது."
    from_alias: "அட்டவணை தரவு"
    to_alias: "JPEG உயர்-வரையறை படம்"
  Jira:
    alias: "Jira அட்டவணை"
    what: "JIRA Atlassian ஆல் உருவாக்கப்பட்ட தொழில்முறை திட்ட மேலாண்மை மற்றும் பிழை கண்காணிப்பு மென்பொருளாகும், சுறுசுறுப்பான மேம்பாடு, மென்பொருள் சோதனை மற்றும் திட்ட ஒத்துழைப்பில் பரவலாகப் பயன்படுத்தப்படுகிறது. அதன் அட்டவணை செயல்பாடு பணக்கார வடிவமைப்பு விருப்பங்கள் மற்றும் தரவு காட்சியை ஆதரிக்கிறது, தேவை மேலாண்மை, பிழை கண்காணிப்பு மற்றும் முன்னேற்ற அறிக்கையிடலில் மென்பொருள் மேம்பாட்டு குழுக்கள், திட்ட மேலாளர்கள் மற்றும் தர உறுதி பணியாளர்களுக்கு முக்கியமான கருவியாக சேவை செய்கிறது."
    step1: "அட்டவணை தரவு கொண்ட கோப்புகளை பதிவேற்றவும் அல்லது நேரடியாக தரவு உள்ளடக்கத்தை ஒட்டவும். கருவி தானாகவே அட்டவணை தரவு மற்றும் சிறப்பு எழுத்து எஸ்கேப்பிங்கை செயலாக்குகிறது."
    step3: "தலைப்பு பாணி அமைப்புகள், செல் சீரமைப்பு, எழுத்து எஸ்கேப் செயலாக்கம் மற்றும் வடிவ மேம்படுத்தல் ஆதரவுடன் JIRA தளம்-இணக்கமான அட்டவணை குறியீட்டை உருவாக்கவும். உருவாக்கப்பட்ட குறியீடு JIRA பிரச்சினை விளக்கங்கள், கருத்துகள் அல்லது விக்கி பக்கங்களில் நேரடியாக ஒட்டப்படலாம், JIRA அமைப்புகளில் சரியான காட்சி மற்றும் ரெண்டரிங்கை உறுதி செய்கிறது."
    from_alias: "திட்ட தரவு"
    to_alias: "Jira அட்டவணை தொடரியல்"
  Textile:
    alias: "Textile அட்டவணை"
    what: "Textile எளிய மற்றும் கற்றுக்கொள்ள எளிதான தொடரியலுடன் கூடிய சுருக்கமான இலகுவான மார்க்அப் மொழியாகும், உள்ளடக்க மேலாண்மை அமைப்புகள், வலைப்பதிவு தளங்கள் மற்றும் மன்ற அமைப்புகளில் பரவலாகப் பயன்படுத்தப்படுகிறது. அதன் அட்டவணை தொடரியல் தெளிவானது மற்றும் உள்ளுணர்வு, விரைவான வடிவமைப்பு மற்றும் பாணி அமைப்புகளை ஆதரிக்கிறது. உள்ளடக்க உருவாக்குநர்கள் மற்றும் வலைத்தள நிர்வாகிகளுக்கு விரைவான ஆவண எழுத்து மற்றும் உள்ளடக்க வெளியீட்டிற்கான சிறந்த கருவி."
    step1: "Textile வடிவ கோப்புகளை பதிவேற்றவும் அல்லது அட்டவணை தரவை ஒட்டவும். கருவி Textile மார்க்அப் தொடரியல் பாகுபடுத்தி அட்டவணை உள்ளடக்கத்தை பிரித்தெடுக்கிறது."
    step3: "தலைப்பு மார்க்அப், செல் சீரமைப்பு, சிறப்பு எழுத்து எஸ்கேப்பிங் மற்றும் வடிவ மேம்படுத்தல் ஆதரவுடன் நிலையான Textile அட்டவணை தொடரியலை உருவாக்கவும். உருவாக்கப்பட்ட குறியீடு Textile ஐ ஆதரிக்கும் CMS அமைப்புகள், வலைப்பதிவு தளங்கள் மற்றும் ஆவண அமைப்புகளில் நேரடியாகப் பயன்படுத்தப்படலாம், சரியான உள்ளடக்க ரெண்டரிங் மற்றும் காட்சியை உறுதி செய்கிறது."
    from_alias: "Textile ஆவணம்"
    to_alias: "Textile அட்டவணை தொடரியல்"
  PNG:
    alias: "PNG படம்"
    what: "PNG (Portable Network Graphics) சிறந்த சுருக்கம் மற்றும் வெளிப்படைத்தன்மை ஆதரவுடன் கூடிய இழப்பற்ற படம் வடிவமாகும். வலை வடிவமைப்பு, டிஜிட்டல் கிராபிக்ஸ் மற்றும் தொழில்முறை புகைப்படம் எடுத்தலில் பரவலாகப் பயன்படுத்தப்படுகிறது. அதன் உயர் தரம் மற்றும் பரந்த இணக்கத்தன்மை ஸ்கிரீன்ஷாட்கள், லோகோக்கள், வரைபடங்கள் மற்றும் கூர்மையான விவரங்கள் மற்றும் வெளிப்படையான பின்னணிகள் தேவைப்படும் எந்த படங்களுக்கும் சிறந்ததாக ஆக்குகிறது."
    step1: "எந்த வடிவத்திலும் அட்டவணை தரவை இறக்குமதி செய்யவும். கருவி அறிவுபூர்வமான அமைப்பு வடிவமைப்பு மற்றும் காட்சி மேம்படுத்தலை செய்கிறது, PNG வெளியீட்டிற்கு தானாகவே உகந்த அளவு மற்றும் தெளிவுத்திறனை கணக்கிடுகிறது."
    step3: "பல தீம் வண்ண திட்டங்கள், வெளிப்படையான பின்னணிகள், தகவமைப்பு அமைப்பு மற்றும் உரை தெளிவு மேம்படுத்தல் ஆதரவுடன் உயர்தர PNG அட்டவணை படங்களை உருவாக்கவும். சிறந்த காட்சி தரத்துடன் வலை பயன்பாடு, ஆவண செருகல் மற்றும் தொழில்முறை விளக்கக்காட்சிகளுக்கு சரியானது."
    from_alias: "அட்டவணை தரவு"
    to_alias: "PNG உயர்தர படம்"
  TOML:
    alias: "TOML கட்டமைப்பு"
    what: "TOML (Tom's Obvious, Minimal Language) படிக்க மற்றும் எழுத எளிதான கட்டமைப்பு கோப்பு வடிவமாகும். தெளிவற்றதாகவும் எளிமையாகவும் வடிவமைக்கப்பட்டது, இது கட்டமைப்பு மேலாண்மைக்காக நவீன மென்பொருள் திட்டங்களில் பரவலாகப் பயன்படுத்தப்படுகிறது. அதன் தெளிவான தொடரியல் மற்றும் வலுவான தட்டச்சு பயன்பாட்டு அமைப்புகள் மற்றும் திட்ட கட்டமைப்பு கோப்புகளுக்கு சிறந்த தேர்வாக அமைகிறது."
    step1: "TOML கோப்புகளை பதிவேற்றவும் அல்லது கட்டமைப்பு தரவை ஒட்டவும். கருவி TOML தொடரியல் பாகுபடுத்தி கட்டமைக்கப்பட்ட கட்டமைப்பு தகவலை பிரித்தெடுக்கிறது."
    step3: "உள்ளமைக்கப்பட்ட கட்டமைப்புகள், தரவு வகைகள் மற்றும் கருத்துகள் ஆதரவுடன் நிலையான TOML வடிவத்தை உருவாக்கவும். உருவாக்கப்பட்ட TOML கோப்புகள் பயன்பாட்டு கட்டமைப்பு, கட்டமைப்பு கருவிகள் மற்றும் திட்ட அமைப்புகளுக்கு சரியானவை."
    from_alias: "TOML கட்டமைப்பு"
    to_alias: "TOML வடிவம்"
  INI:
    alias: "INI கட்டமைப்பு"
    what: "INI கோப்புகள் பல பயன்பாடுகள் மற்றும் இயக்க அமைப்புகளால் பயன்படுத்தப்படும் எளிய கட்டமைப்பு கோப்புகளாகும். அவற்றின் நேரடியான விசை-மதிப்பு ஜோடி கட்டமைப்பு அவற்றை கைமுறையாக படிக்க மற்றும் திருத்த எளிதாக்குகிறது. Windows பயன்பாடுகள், மரபு அமைப்புகள் மற்றும் மனித படிக்கக்கூடிய தன்மை முக்கியமான எளிய கட்டமைப்பு சூழ்நிலைகளில் பரவலாகப் பயன்படுத்தப்படுகிறது."
    step1: "INI கோப்புகளை பதிவேற்றவும் அல்லது கட்டமைப்பு தரவை ஒட்டவும். கருவி INI தொடரியல் பாகுபடுத்தி பிரிவு-அடிப்படையிலான கட்டமைப்பு தகவலை பிரித்தெடுக்கிறது."
    step3: "பிரிவுகள், கருத்துகள் மற்றும் பல்வேறு தரவு வகைகள் ஆதரவுடன் நிலையான INI வடிவத்தை உருவாக்கவும். உருவாக்கப்பட்ட INI கோப்புகள் பெரும்பாலான பயன்பாடுகள் மற்றும் கட்டமைப்பு அமைப்புகளுடன் இணக்கமானவை."
    from_alias: "INI கட்டமைப்பு"
    to_alias: "INI வடிவம்"
  Avro:
    alias: "Avro ஸ்கீமா"
    what: "Apache Avro பணக்கார தரவு கட்டமைப்புகள், சுருக்கமான பைனரி வடிவம் மற்றும் ஸ்கீமா பரிணாம திறன்களை வழங்கும் தரவு தொடர்மயமாக்கல் அமைப்பாகும். பெரிய தரவு செயலாக்கம், செய்தி வரிசைகள் மற்றும் விநியோகிக்கப்பட்ட அமைப்புகளில் பரவலாகப் பயன்படுத்தப்படுகிறது. அதன் ஸ்கீமா வரையறை சிக்கலான தரவு வகைகள் மற்றும் பதிப்பு இணக்கத்தன்மையை ஆதரிக்கிறது, இது தரவு பொறியாளர்கள் மற்றும் அமைப்பு கட்டிடக் கலைஞர்களுக்கு முக்கியமான கருவியாக அமைகிறது."
    step1: "Avro ஸ்கீமா கோப்புகளை பதிவேற்றவும் அல்லது தரவை ஒட்டவும். கருவி Avro ஸ்கீமா வரையறைகளை பாகுபடுத்தி அட்டவணை கட்டமைப்பு தகவலை பிரித்தெடுக்கிறது."
    step3: "தரவு வகை மேப்பிங், புல கட்டுப்பாடுகள் மற்றும் ஸ்கீமா சரிபார்ப்பு ஆதரவுடன் நிலையான Avro ஸ்கீமா வரையறைகளை உருவாக்கவும். உருவாக்கப்பட்ட ஸ்கீமாக்கள் Hadoop சுற்றுச்சூழல் அமைப்புகள், Kafka செய்தி அமைப்புகள் மற்றும் பிற பெரிய தரவு தளங்களில் நேரடியாகப் பயன்படுத்தப்படலாம்."
    from_alias: "Avro ஸ்கீமா"
    to_alias: "Avro தரவு வடிவம்"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) கட்டமைக்கப்பட்ட தரவை தொடர்மயமாக்குவதற்கான Google இன் மொழி-நடுநிலை, தளம்-நடுநிலை, விரிவாக்கக்கூடிய வழிமுறையாகும். மைக்ரோசர்வீஸ்கள், API மேம்பாடு மற்றும் தரவு சேமிப்பில் பரவலாகப் பயன்படுத்தப்படுகிறது. அதன் திறமையான பைனரி வடிவம் மற்றும் வலுவான தட்டச்சு உயர்-செயல்திறன் பயன்பாடுகள் மற்றும் குறுக்கு-மொழி தொடர்புக்கு சிறந்ததாக ஆக்குகிறது."
    step1: ".proto கோப்புகளை பதிவேற்றவும் அல்லது Protocol Buffer வரையறைகளை ஒட்டவும். கருவி protobuf தொடரியல் பாகுபடுத்தி செய்தி கட்டமைப்பு தகவலை பிரித்தெடுக்கிறது."
    step3: "செய்தி வகைகள், புல விருப்பங்கள் மற்றும் சேவை வரையறைகள் ஆதரவுடன் நிலையான Protocol Buffer வரையறைகளை உருவாக்கவும். உருவாக்கப்பட்ட .proto கோப்புகள் பல நிரலாக்க மொழிகளுக்கு தொகுக்கப்படலாம்."
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf ஸ்கீமா"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas Python இல் மிகவும் பிரபலமான தரவு பகுப்பாய்வு நூலகமாகும், DataFrame அதன் முக்கிய தரவு கட்டமைப்பாகும். இது சக்திவாய்ந்த தரவு கையாளுதல், சுத்தம் செய்தல் மற்றும் பகுப்பாய்வு திறன்களை வழங்குகிறது, தரவு அறிவியல், இயந்திர கற்றல் மற்றும் வணிக நுண்ணறிவில் பரவலாகப் பயன்படுத்தப்படுகிறது. Python டெவலப்பர்கள் மற்றும் தரவு ஆய்வாளர்களுக்கு இன்றியமையாத கருவி."
    step1: "DataFrame குறியீடு கொண்ட Python கோப்புகளை பதிவேற்றவும் அல்லது தரவை ஒட்டவும். கருவி Pandas தொடரியல் பாகுபடுத்தி DataFrame கட்டமைப்பு தகவலை பிரித்தெடுக்கிறது."
    step3: "தரவு வகை விவரக்குறிப்புகள், குறியீட்டு அமைப்புகள் மற்றும் தரவு செயல்பாடுகள் ஆதரவுடன் நிலையான Pandas DataFrame குறியீட்டை உருவாக்கவும். உருவாக்கப்பட்ட குறியீடு தரவு பகுப்பாய்வு மற்றும் செயலாக்கத்திற்காக Python சூழலில் நேரடியாக செயல்படுத்தப்படலாம்."
    from_alias: "Pandas DataFrame"
    to_alias: "Python தரவு கட்டமைப்பு"
  RDF:
    alias: "RDF Triple"
    what: "RDF (Resource Description Framework) வலையில் தரவு பரிமாற்றத்திற்கான நிலையான மாதிரியாகும், வளங்களைப் பற்றிய தகவலை வரைபட வடிவத்தில் குறிப்பிட வடிவமைக்கப்பட்டுள்ளது. சொற்பொருள் வலை, அறிவு வரைபடங்கள் மற்றும் இணைக்கப்பட்ட தரவு பயன்பாடுகளில் பரவலாகப் பயன்படுத்தப்படுகிறது. அதன் மூன்று கட்டமைப்பு பணக்கார மெட்டாடேட்டா பிரதிநிதித்துவம் மற்றும் சொற்பொருள் உறவுகளை செயல்படுத்துகிறது."
    step1: "RDF கோப்புகளை பதிவேற்றவும் அல்லது மூன்று தரவை ஒட்டவும். கருவி RDF தொடரியல் பாகுபடுத்தி சொற்பொருள் உறவுகள் மற்றும் வள தகவலை பிரித்தெடுக்கிறது."
    step3: "பல்வேறு தொடர்மயமாக்கல்கள் (RDF/XML, Turtle, N-Triples) ஆதரவுடன் நிலையான RDF வடிவத்தை உருவாக்கவும். உருவாக்கப்பட்ட RDF சொற்பொருள் வலை பயன்பாடுகள், அறிவு தளங்கள் மற்றும் இணைக்கப்பட்ட தரவு அமைப்புகளில் பயன்படுத்தப்படலாம்."
    from_alias: "RDF தரவு"
    to_alias: "RDF சொற்பொருள் வடிவம்"
  MATLAB:
    alias: "MATLAB வரிசை"
    what: "MATLAB என்பது பொறியியல் கணினி, தரவு பகுப்பாய்வு மற்றும் அல்காரிதம் மேம்பாட்டில் பரவலாகப் பயன்படுத்தப்படும் உயர்-செயல்திறன் எண் கணினி மற்றும் காட்சிப்படுத்தல் மென்பொருளாகும். அதன் வரிசை மற்றும் மேட்ரிக்ஸ் செயல்பாடுகள் சக்திவாய்ந்தவை, சிக்கலான கணித கணக்கீடுகள் மற்றும் தரவு செயலாக்கத்தை ஆதரிக்கின்றன. பொறியாளர்கள், ஆராய்ச்சியாளர்கள் மற்றும் தரவு விஞ்ஞானிகளுக்கு இன்றியமையாத கருவி."
    step1: "MATLAB .m கோப்புகளை பதிவேற்றவும் அல்லது வரிசை தரவை ஒட்டவும். கருவி MATLAB தொடரியல் பாகுபடுத்தி வரிசை கட்டமைப்பு தகவலை பிரித்தெடுக்கிறது."
    step3: "பல-பரிமாண வரிசைகள், தரவு வகை விவரக்குறிப்புகள் மற்றும் மாறி பெயரிடல் ஆதரவுடன் நிலையான MATLAB வரிசை குறியீட்டை உருவாக்கவும். உருவாக்கப்பட்ட குறியீடு தரவு பகுப்பாய்வு மற்றும் அறிவியல் கணினிக்கு MATLAB சூழலில் நேரடியாக இயக்கப்படலாம்."
    from_alias: "MATLAB வரிசை"
    to_alias: "MATLAB குறியீடு வடிவம்"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame என்பது R நிரலாக்க மொழியின் முக்கிய தரவு கட்டமைப்பு ஆகும், இது புள்ளியியல் பகுப்பாய்வு, தரவு சுரங்கம் மற்றும் இயந்திர கற்றலில் பரவலாகப் பயன்படுத்தப்படுகிறது. R என்பது புள்ளியியல் கணினி மற்றும் கிராபிக்ஸுக்கான முதன்மை கருவி, DataFrame சக்திவாய்ந்த தரவு கையாளுதல், புள்ளியியல் பகுப்பாய்வு மற்றும் காட்சிப்படுத்தல் திறன்களை வழங்குகிறது. கட்டமைக்கப்பட்ட தரவு பகுப்பாய்வுடன் வேலை செய்யும் தரவு அறிஞர்கள், புள்ளியியலாளர்கள் மற்றும் ஆராய்ச்சியாளர்களுக்கு அத்தியாவசியம்."
    step1: "R தரவு கோப்புகளைப் பதிவேற்றவும் அல்லது DataFrame குறியீட்டை ஒட்டவும். கருவி R தொடரியல் பாகுபடுத்தி நெடுவரிசை வகைகள், வரிசை பெயர்கள் மற்றும் தரவு உள்ளடக்கம் உட்பட DataFrame கட்டமைப்பு தகவலை பிரித்தெடுக்கிறது."
    step3: "தரவு வகை விவரக்குறிப்புகள், காரணி நிலைகள், வரிசை/நெடுவரிசை பெயர்கள் மற்றும் R-குறிப்பிட்ட தரவு கட்டமைப்புகள் ஆதரவுடன் நிலையான R DataFrame குறியீட்டை உருவாக்கவும். உருவாக்கப்பட்ட குறியீடு புள்ளியியல் பகுப்பாய்வு மற்றும் தரவு செயலாக்கத்திற்காக R சூழலில் நேரடியாக இயக்கப்படலாம்."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "மாற்றத்தைத் தொடங்குங்கள்"
  start_generating: "உருவாக்கத் தொடங்கவும்"
  api_docs: "API ஆவணங்கள்"
related:
  section_title: 'மேலும் {{ if and .from (ne .from "generator") }}{{ .from }} மற்றும் {{ end }}{{ .to }} மாற்றிகள்'
  section_description: '{{ if and .from (ne .from "generator") }}{{ .from }} மற்றும் {{ end }}{{ .to }} வடிவங்களுக்கான மேலும் மாற்றிகளை ஆராயுங்கள். எங்கள் தொழில்முறை ஆன்லைன் மாற்றும் கருவிகளுடன் உங்கள் தரவை பல வடிவங்களுக்கு இடையே மாற்றுங்கள்.'
  title: "{{ .from }} முதல் {{ .to }} வரை"
howto:
  step2: "தொழில்முறை அம்சங்களுடன் எங்கள் மேம்பட்ட ஆன்லைன் அட்டவணை எடிட்டரைப் பயன்படுத்தி தரவைத் திருத்தவும். வெற்று வரிசைகளை நீக்குதல், நகல்களை அகற்றுதல், தரவு இடமாற்றம், வரிசைப்படுத்துதல், regex கண்டுபிடித்து மாற்றுதல் மற்றும் நிகழ்நேர முன்னோட்டத்தை ஆதரிக்கிறது. அனைத்து மாற்றங்களும் துல்லியமான, நம்பகமான முடிவுகளுடன் %s வடிவத்திற்கு தானாகவே மாற்றப்படும்."
  section_title: "{{ . }} ஐ எப்படி பயன்படுத்துவது"
  converter_description: "எங்கள் படிப்படியான வழிகாட்டியுடன் {{ .from }} ஐ {{ .to }} ஆக மாற்றுவதைக் கற்றுக்கொள்ளுங்கள். மேம்பட்ட அம்சங்கள் மற்றும் நிகழ்நேர முன்னோட்டத்துடன் தொழில்முறை ஆன்லைன் மாற்றி."
  generator_description: "எங்கள் ஆன்லைன் ஜெனரேட்டருடன் தொழில்முறை {{ .to }} அட்டவணைகளை உருவாக்கக் கற்றுக்கொள்ளுங்கள். Excel போன்ற திருத்தம், நிகழ்நேர முன்னோட்டம் மற்றும் உடனடி ஏற்றுமதி திறன்கள்."
extension:
  section_title: "அட்டவணை கண்டறிதல் மற்றும் பிரித்தெடுத்தல் நீட்டிப்பு"
  section_description: "ஒரே கிளிக்கில் எந்த வலைத்தளத்திலிருந்தும் அட்டவணைகளைப் பிரித்தெடுக்கவும். Excel, CSV, JSON உட்பட 30+ வடிவங்களுக்கு உடனடியாக மாற்றவும் - நகலெடுத்து ஒட்டுதல் தேவையில்லை."
  features:
    extraction_title: "ஒரு கிளிக் அட்டவணை பிரித்தெடுத்தல்"
    extraction_description: "நகலெடுத்து ஒட்டுதல் இல்லாமல் எந்த வலைப்பக்கத்திலிருந்தும் உடனடியாக அட்டவணைகளை பிரித்தெடுக்கவும் - தொழில்முறை தரவு பிரித்தெடுத்தல் எளிதாக்கப்பட்டது"
    formats_title: "30+ வடிவ மாற்றி ஆதரவு"
    formats_description: "எங்கள் மேம்பட்ட அட்டவணை மாற்றியுடன் பிரித்தெடுக்கப்பட்ட அட்டவணைகளை Excel, CSV, JSON, Markdown, SQL மற்றும் பலவற்றிற்கு மாற்றவும்"
    detection_title: "ஸ்மார்ட் அட்டவணை கண்டறிதல்"
    detection_description: "வேகமான தரவு பிரித்தெடுத்தல் மற்றும் மாற்றத்திற்காக எந்த வலைப்பக்கத்திலும் அட்டவணைகளை தானாகவே கண்டறிந்து முன்னிலைப்படுத்துகிறது"
  hover_tip: "✨ பிரித்தெடுத்தல் ஐகானைப் பார்க்க எந்த அட்டவணையின் மீதும் ஹோவர் செய்யவும்"
recommendations:
  section_title: "பல்கலைக்கழகங்கள் மற்றும் நிபுணர்களால் பரிந்துரைக்கப்பட்டது"
  section_description: "நம்பகமான அட்டவணை மாற்றம் மற்றும் தரவு செயலாக்கத்திற்காக பல்கலைக்கழகங்கள், ஆராய்ச்சி நிறுவனங்கள் மற்றும் மேம்பாட்டு குழுக்களில் உள்ள நிபுணர்களால் TableConvert நம்பப்படுகிறது."
  cards:
    university_title: "விஸ்கான்சின்-மேடிசன் பல்கலைக்கழகம்"
    university_description: "TableConvert.com - தொழில்முறை இலவச ஆன்லைன் அட்டவணை மாற்றி மற்றும் தரவு வடிவங்கள் கருவி"
    university_link: "கட்டுரையைப் படிக்கவும்"
    facebook_title: "தரவு தொழில்முறை சமூகம்"
    facebook_description: "Facebook டெவலப்பர் குழுக்களில் தரவு ஆய்வாளர்கள் மற்றும் நிபுணர்களால் பகிரப்பட்டு பரிந்துரைக்கப்பட்டது"
    facebook_link: "இடுகையைப் பார்க்கவும்"
    twitter_title: "டெவலப்பர் சமூகம்"
    twitter_description: "அட்டவணை மாற்றத்திற்காக X (Twitter) இல் @xiaoying_eth மற்றும் பிற டெவலப்பர்களால் பரிந்துரைக்கப்பட்டது"
    twitter_link: "ட்வீட்டைப் பார்க்கவும்"
faq:
  section_title: "அடிக்கடி கேட்கப்படும் கேள்விகள்"
  section_description: "எங்கள் இலவச ஆன்லைன் அட்டவணை மாற்றி, தரவு வடிவங்கள் மற்றும் மாற்றும் செயல்முறை பற்றிய பொதுவான கேள்விகள்."
  what: "%s வடிவம் என்றால் என்ன?"
  howto_convert:
    question: "{{ . }} ஐ இலவசமாக எப்படி பயன்படுத்துவது?"
    answer: "எங்கள் இலவச ஆன்லைன் அட்டவணை மாற்றியைப் பயன்படுத்தி உங்கள் {{ .from }} கோப்பை பதிவேற்றவும், தரவை ஒட்டவும் அல்லது வலைப் பக்கங்களிலிருந்து பிரித்தெடுக்கவும். எங்கள் தொழில்முறை மாற்றி கருவி உண்மையான நேர முன்னோட்டம் மற்றும் மேம்பட்ட திருத்தும் அம்சங்களுடன் உங்கள் தரவை உடனடியாக {{ .to }} வடிவத்திற்கு மாற்றுகிறது. மாற்றப்பட்ட முடிவை உடனடியாக பதிவிறக்கவும் அல்லது நகலெடுக்கவும்."
  security:
    question: "இந்த ஆன்லைன் மாற்றியைப் பயன்படுத்தும்போது எனது தரவு பாதுகாப்பானதா?"
    answer: "நிச்சயமாக! அனைத்து அட்டவணை மாற்றங்களும் உங்கள் உலாவியில் உள்ளூரில் நடக்கின்றன - உங்கள் தரவு உங்கள் சாதனத்தை விட்டு வெளியேறாது. எங்கள் ஆன்லைன் மாற்றி அனைத்தையும் கிளையன்ட்-பக்கத்தில் செயலாக்குகிறது, முழுமையான தனியுரிமை மற்றும் தரவு பாதுகாப்பை உறுதி செய்கிறது. எங்கள் சர்வர்களில் எந்த கோப்புகளும் சேமிக்கப்படவில்லை."
  free:
    question: "TableConvert உண்மையில் பயன்படுத்த இலவசமா?"
    answer: "ஆம், TableConvert முற்றிலும் இலவசம்! அனைத்து மாற்றி அம்சங்கள், அட்டவணை எடிட்டர், தரவு ஜெனரேட்டர் கருவிகள் மற்றும் ஏற்றுமதி விருப்பங்கள் செலவு, பதிவு அல்லது மறைக்கப்பட்ட கட்டணங்கள் இல்லாமல் கிடைக்கின்றன. இலவசமாக ஆன்லைனில் வரம்பற்ற கோப்புகளை மாற்றவும்."
  filesize:
    question: "ஆன்லைன் மாற்றியின் கோப்பு அளவு வரம்புகள் என்ன?"
    answer: "எங்கள் இலவச ஆன்லைன் அட்டவணை மாற்றி 10MB வரையிலான கோப்புகளை ஆதரிக்கிறது. பெரிய கோப்புகள், தொகுதி செயலாக்கம் அல்லது நிறுவன தேவைகளுக்கு, அதிக வரம்புகளுடன் எங்கள் உலாவி நீட்டிப்பு அல்லது தொழில்முறை API சேவையைப் பயன்படுத்தவும்."
stats:
  conversions: "மாற்றப்பட்ட அட்டவணைகள்"
  tables: "உருவாக்கப்பட்ட அட்டவணைகள்"
  formats: "தரவு கோப்பு வடிவங்கள்"
  rating: "பயனர் மதிப்பீடு"
