site:
  fullname: "ऑनलाइन टेबल कन्व्हर्ट"
  name: "TableConvert"
  subtitle: "मोफत ऑनलाइन टेबल कन्व्हर्टर आणि जनरेटर"
  intro: "TableConvert हे एक मोफत ऑनलाइन टेबल कन्व्हर्टर आणि डेटा जनरेटर टूल आहे जे Excel, CSV, JSON, Markdown, LaTeX, SQL आणि अधिक समावेशित 30+ फॉर्मॅट्समध्ये रूपांतरणास समर्थन देते."
  followTwitter: "X वर आम्हाला फॉलो करा"
title:
  converter: "%s ते %s"
  generator: "%s जनरेटर"
post:
  tags:
    converter: "कन्व्हर्टर"
    editor: "एडिटर"
    generator: "जनरेटर"
    maker: "बिल्डर"
  converter:
    title: "%s ते %s ऑनलाइन कन्व्हर्ट करा"
    short: "एक मोफत व शक्तिशाली %s ते %s ऑनलाइन टूल"
    intro: "वापरण्यास सोपा ऑनलाइन %s ते %s कन्व्हर्टर. आमच्या सहज रूपांतरण साधनासह टेबल डेटा सहजतेने रूपांतरित करा. जलद, विश्वसनीय आणि वापरकर्ता-अनुकूल."
  generator:
    title: "ऑनलाइन %s एडिटर आणि जनरेटर"
    short: "व्यापक वैशिष्ट्यांसह व्यावसायिक %s ऑनलाइन जनरेशन टूल"
    intro: "वापरण्यास सोपा ऑनलाइन %s जनरेटर आणि टेबल एडिटर. आमच्या सहज साधन आणि रिअल-टाइम पूर्वावलोकनासह व्यावसायिक डेटा टेबल सहजतेने तयार करा."
navbar:
  search:
    placeholder: "कन्व्हर्टर शोधा..."
  sponsor: "आम्हाला कॉफी विकत घ्या"
  extension: "एक्सटेंशन"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "डेटा स्रोत"
    placeholder: "तुमचा %s डेटा पेस्ट करा किंवा %s फाइल्स येथे ड्रॅग करा"
    example: "उदाहरण"
    upload: "फाइल अपलोड करा"
    extract:
      enter: "वेब पेजमधून एक्सट्रॅक्ट करा"
      intro: "संरचित डेटा आपोआप एक्सट्रॅक्ट करण्यासाठी टेबल डेटा असलेल्या वेब पेजचा URL प्रविष्ट करा"
      btn: "%s एक्सट्रॅक्ट करा"
    excel:
      sheet: "वर्कशीट"
      none: "काहीही नाही"
  tableEditor:
    title: "ऑनलाइन टेबल एडिटर"
    undo: "पूर्ववत करा"
    redo: "पुन्हा करा"
    transpose: "ट्रान्सपोज"
    clear: "साफ करा"
    deleteBlank: "रिकामे हटवा"
    deleteDuplicate: "डुप्लिकेट हटवा"
    uppercase: "मोठी अक्षरे"
    lowercase: "छोटी अक्षरे"
    capitalize: "पहिले अक्षर मोठे"
    replace:
      replace: "शोधा आणि बदला (Regex समर्थित)"
      subst: "यासह बदला..."
      btn: "सर्व बदला"
  tableGenerator:
    title: "टेबल जनरेटर"
    sponsor: "आम्हाला कॉफी विकत घ्या"
    copy: "क्लिपबोर्डमध्ये कॉपी करा"
    download: "फाइल डाउनलोड करा"
    tooltip:
      html:
        escape: "प्रदर्शन त्रुटी टाळण्यासाठी HTML विशेष वर्ण (&, <, >, \", ') एस्केप करा"
        div: "पारंपारिक TABLE टॅगऐवजी DIV+CSS लेआउट वापरा, रिस्पॉन्सिव्ह डिझाइनसाठी अधिक योग्य"
        minify: "संकुचित HTML कोड तयार करण्यासाठी व्हाइटस्पेस आणि लाइन ब्रेक काढा"
        thead: "मानक टेबल हेड (&lt;thead&gt;) आणि बॉडी (&lt;tbody&gt;) संरचना तयार करा"
        tableCaption: "टेबलच्या वर वर्णनात्मक शीर्षक जोडा (&lt;caption&gt; एलिमेंट)"
        tableClass: "सुलभ स्टाइल कस्टमायझेशनसाठी टेबलमध्ये CSS क्लास नाव जोडा"
        tableId: "JavaScript मॅनिप्युलेशनसाठी टेबलसाठी अनन्य ID ओळखकर्ता सेट करा"
      jira:
        escape: "Jira टेबल सिंटॅक्सशी संघर्ष टाळण्यासाठी पाइप वर्ण (|) एस्केप करा"
      json:
        parsingJSON: "सेलमधील JSON स्ट्रिंग्स बुद्धिमत्तेने ऑब्जेक्ट्समध्ये पार्स करा"
        minify: "फाइल आकार कमी करण्यासाठी कॉम्पॅक्ट सिंगल-लाइन JSON फॉर्मॅट तयार करा"
        format: "आउटपुट JSON डेटा संरचना निवडा: ऑब्जेक्ट अॅरे, 2D अॅरे, इ."
      latex:
        escape: "योग्य संकलन सुनिश्चित करण्यासाठी LaTeX विशेष वर्ण (%, &, _, #, $, वगैरे) एस्केप करा"
        ht: "पृष्ठावर टेबल स्थिती नियंत्रित करण्यासाठी फ्लोटिंग पोझिशन पॅरामीटर [!ht] जोडा"
        mwe: "संपूर्ण LaTeX दस्तऐवज तयार करा"
        tableAlign: "पृष्ठावर टेबलचे क्षैतिज संरेखन सेट करा"
        tableBorder: "टेबल बॉर्डर स्टाइल कॉन्फिगर करा: बॉर्डर नाही, आंशिक बॉर्डर, पूर्ण बॉर्डर"
        label: "\\ref{} कमांड क्रॉस-रेफरन्सिंगसाठी टेबल लेबल सेट करा"
        caption: "टेबलच्या वर किंवा खाली प्रदर्शित करण्यासाठी टेबल कॅप्शन सेट करा"
        location: "टेबल कॅप्शन प्रदर्शन स्थिती निवडा: वर किंवा खाली"
        tableType: "टेबल एनवायरनमेंट प्रकार निवडा: tabular, longtable, array, वगैरे"
      markdown:
        escape: "फॉर्मॅट संघर्ष टाळण्यासाठी Markdown विशेष वर्ण (*, _, |, \\, वगैरे) एस्केप करा"
        pretty: "अधिक सुंदर टेबल फॉर्मॅट तयार करण्यासाठी कॉलम रुंदी ऑटो-अलाइन करा"
        simple: "बाह्य बॉर्डर व्हर्टिकल लाइन्स वगळून सरलीकृत सिंटॅक्स वापरा"
        boldFirstRow: "पहिली पंक्ती मजकूर ठळक करा"
        boldFirstColumn: "पहिला कॉलम मजकूर ठळक करा"
        firstHeader: "पहिली पंक्ती हेडर म्हणून मानून विभाजक रेषा जोडा"
        textAlign: "कॉलम मजकूर संरेखन सेट करा: डावे, मध्य, उजवे"
        multilineHandling: "मल्टिलाइन मजकूर हाताळणी: लाइन ब्रेक जतन करा, \\n मध्ये एस्केप करा, &lt;br&gt; टॅग वापरा"

        includeLineNumbers: "टेबलच्या डाव्या बाजूला लाइन नंबर कॉलम जोडा"
      magic:
        builtin: "पूर्वनिर्धारित सामान्य टेम्प्लेट फॉर्मॅट निवडा"
        rowsTpl: "<table> <tr> <th>मॅजिक सिंटॅक्स</th> <th>वर्णन</th> <th>JS मेथड्स समर्थन</th> </tr> <tr> <td>{h1} {h2} ...</td> <td><b>शीर्षक</b>चे 1ले, 2रे ... फील्ड, म्हणजे {hA} {hB} ...</td> <td>स्ट्रिंग मेथड्स</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>सध्याच्या पंक्तीचे 1ले, 2रे ... फील्ड, म्हणजे {$A} {$B} ...</td> <td>स्ट्रिंग मेथड्स</td> </tr> <tr> <td>{F,} {F;}</td> <td><b>F</b> नंतरच्या स्ट्रिंगने सध्याची पंक्ती विभाजित करा</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>सध्याच्या <b>पंक्तीचा</b> लाइन <b>नंबर</b> 1 किंवा 100 पासून</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>पंक्तींचा</b> <b>शेवटचा</b> लाइन <b>नंबर</b> </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>JavaScript कोड <b>एक्झिक्यूट</b> करा, उदा: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> ब्रेसेस {...} आउटपुट करण्यासाठी बॅकस्लॅश <b>\\</b> वापरा </td> <td></td> </tr></table>"
        headerTpl: "हेडर विभागासाठी कस्टम आउटपुट टेम्प्लेट"
        footerTpl: "फूटर विभागासाठी कस्टम आउटपुट टेम्प्लेट"
      textile:
        escape: "फॉर्मॅट संघर्ष टाळण्यासाठी Textile सिंटॅक्स वर्ण (|, ., -, ^) एस्केप करा"
        rowHeader: "पहिली पंक्ती हेडर पंक्ती म्हणून सेट करा"
        thead: "टेबल हेड आणि बॉडीसाठी Textile सिंटॅक्स मार्कर जोडा"
      xml:
        escape: "वैध XML सुनिश्चित करण्यासाठी XML विशेष वर्ण (&lt;, &gt;, &amp;, \", ') एस्केप करा"
        minify: "अतिरिक्त व्हाइटस्पेस काढून संकुचित XML आउटपुट तयार करा"
        rootElement: "XML रूट एलिमेंट टॅग नाव सेट करा"
        rowElement: "डेटाच्या प्रत्येक पंक्तीसाठी XML एलिमेंट टॅग नाव सेट करा"
        declaration: "XML घोषणा हेडर जोडा (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "चाइल्ड एलिमेंट्सऐवजी XML अॅट्रिब्यूट्स म्हणून डेटा आउटपुट करा"
        cdata: "विशेष वर्ण संरक्षित करण्यासाठी CDATA सह मजकूर सामग्री गुंडाळा"
        encoding: "XML दस्तऐवजासाठी वर्ण एन्कोडिंग फॉर्मॅट सेट करा"
        indentation: "XML इंडेंटेशन वर्ण निवडा: स्पेसेस किंवा टॅब्स"
      yaml:
        indentSize: "YAML पदानुक्रम इंडेंटेशनसाठी स्पेसेसची संख्या सेट करा (सामान्यतः 2 किंवा 4)"
        arrayStyle: "अॅरे फॉर्मॅट: ब्लॉक (प्रति ओळ एक आयटम) किंवा फ्लो (इनलाइन फॉर्मॅट)"
        quotationStyle: "स्ट्रिंग कोट स्टाइल: कोट नाही, सिंगल कोट्स, डबल कोट्स"
      pdf:
        theme: "व्यावसायिक दस्तऐवजांसाठी PDF टेबल व्हिज्युअल स्टाइल निवडा"
        headerColor: "PDF टेबल हेडर बॅकग्राउंड रंग निवडा"
        showHead: "PDF पृष्ठांवर हेडर डिस्प्ले नियंत्रित करा"
        docTitle: "PDF दस्तऐवजासाठी पर्यायी शीर्षक"
        docDescription: "PDF दस्तऐवजासाठी पर्यायी वर्णन मजकूर"
      csv:
        bom: "Excel आणि इतर सॉफ्टवेअरला एन्कोडिंग ओळखण्यास मदत करण्यासाठी UTF-8 बाइट ऑर्डर मार्क जोडा"
      excel:
        autoWidth: "सामग्रीवर आधारित कॉलम रुंदी आपोआप समायोजित करा"
        protectSheet: "पासवर्डसह वर्कशीट संरक्षण सक्षम करा: tableconvert.com"
      sql:
        primaryKey: "CREATE TABLE स्टेटमेंटसाठी प्राथमिक की फील्ड नाव निर्दिष्ट करा"
        dialect: "डेटाबेस प्रकार निवडा, कोट आणि डेटा प्रकार सिंटॅक्सवर परिणाम करते"
      ascii:
        forceSep: "डेटाच्या प्रत्येक पंक्तीमध्ये विभाजक रेषा जबरदस्ती करा"
        style: "ASCII टेबल बॉर्डर ड्रॉइंग स्टाइल निवडा"
        comment: "संपूर्ण टेबल गुंडाळण्यासाठी टिप्पणी मार्कर जोडा"
      mediawiki:
        minify: "अतिरिक्त व्हाइटस्पेस काढून आउटपुट कोड संकुचित करा"
        header: "पहिली पंक्ती हेडर स्टाइल म्हणून चिन्हांकित करा"
        sort: "टेबल क्लिक सॉर्टिंग कार्यक्षमता सक्षम करा"
      asciidoc:
        minify: "AsciiDoc फॉर्मॅट आउटपुट संकुचित करा"
        firstHeader: "पहिली पंक्ती हेडर पंक्ती म्हणून सेट करा"
        lastFooter: "शेवटची पंक्ती फूटर पंक्ती म्हणून सेट करा"
        title: "टेबलमध्ये शीर्षक मजकूर जोडा"
      tracwiki:
        rowHeader: "पहिली पंक्ती हेडर म्हणून सेट करा"
        colHeader: "पहिला कॉलम हेडर म्हणून सेट करा"
      bbcode:
        minify: "BBCode आउटपुट फॉर्मॅट संकुचित करा"
      restructuredtext:
        style: "reStructuredText टेबल बॉर्डर स्टाइल निवडा"
        forceSep: "विभाजक रेषा जबरदस्ती करा"
    label:
      ascii:
        forceSep: "पंक्ती विभाजक"
        style: "बॉर्डर स्टाइल"
        comment: "टिप्पणी रॅपर"
      restructuredtext:
        style: "बॉर्डर स्टाइल"
        forceSep: "विभाजक जबरदस्ती करा"
      bbcode:
        minify: "आउटपुट लहान करा"
      csv:
        doubleQuote: "डबल कोट रॅप"
        delimiter: "फील्ड विभाजक"
        bom: "UTF-8 BOM"
        valueDelimiter: "मूल्य विभाजक"
        rowDelimiter: "पंक्ती विभाजक"
        prefix: "पंक्ती उपसर्ग"
        suffix: "पंक्ती प्रत्यय"
      excel:
        autoWidth: "ऑटो रुंदी"
        textFormat: "मजकूर फॉर्मॅट"
        protectSheet: "शीट संरक्षित करा"
        boldFirstRow: "पहिली पंक्ती ठळक"
        boldFirstColumn: "पहिला कॉलम ठळक"
        sheetName: "शीट नाव"
      html:
        escape: "HTML वर्ण एस्केप करा"
        div: "DIV टेबल"
        minify: "कोड लहान करा"
        thead: "टेबल हेड संरचना"
        tableCaption: "टेबल कॅप्शन"
        tableClass: "टेबल क्लास"
        tableId: "टेबल ID"
        rowHeader: "पंक्ती हेडर"
        colHeader: "कॉलम हेडर"
      jira:
        escape: "वर्ण एस्केप करा"
        rowHeader: "पंक्ती हेडर"
        colHeader: "कॉलम हेडर"
      json:
        parsingJSON: "JSON पार्स करा"
        minify: "आउटपुट लहान करा"
        format: "डेटा फॉर्मॅट"
        rootName: "रूट ऑब्जेक्ट नाव"
        indentSize: "इंडेंट आकार"
      jsonlines:
        parsingJSON: "JSON पार्स करा"
        format: "डेटा फॉर्मॅट"
      latex:
        escape: "LaTeX टेबल वर्ण एस्केप करा"
        ht: "फ्लोट स्थिती"
        mwe: "संपूर्ण दस्तऐवज"
        tableAlign: "टेबल संरेखन"
        tableBorder: "बॉर्डर स्टाइल"
        label: "संदर्भ लेबल"
        caption: "टेबल कॅप्शन"
        location: "कॅप्शन स्थिती"
        tableType: "टेबल प्रकार"
        boldFirstRow: "पहिली पंक्ती ठळक"
        boldFirstColumn: "पहिला कॉलम ठळक"
        textAlign: "मजकूर संरेखन"
        borders: "बॉर्डर सेटिंग्ज"
      markdown:
        escape: "वर्ण एस्केप करा"
        pretty: "सुंदर Markdown टेबल"
        simple: "सरल Markdown फॉर्मॅट"
        boldFirstRow: "पहिली पंक्ती ठळक"
        boldFirstColumn: "पहिला कॉलम ठळक"
        firstHeader: "पहिला हेडर"
        textAlign: "मजकूर संरेखन"
        multilineHandling: "मल्टिलाइन हाताळणी"

        includeLineNumbers: "ओळ क्रमांक जोडा"
        align: "संरेखन"
      mediawiki:
        minify: "कोड लहान करा"
        header: "हेडर मार्कअप"
        sort: "क्रमवारी लावता येणारे"
      asciidoc:
        minify: "फॉर्मॅट लहान करा"
        firstHeader: "पहिला हेडर"
        lastFooter: "शेवटचा फूटर"
        title: "टेबल शीर्षक"
      tracwiki:
        rowHeader: "पंक्ती हेडर"
        colHeader: "कॉलम हेडर"
      sql:
        drop: "टेबल ड्रॉप करा (अस्तित्वात असल्यास)"
        create: "टेबल तयार करा"
        oneInsert: "बॅच इन्सर्ट"
        table: "टेबल नाव"
        dialect: "डेटाबेस प्रकार"
        primaryKey: "प्राथमिक की"
      magic:
        builtin: "अंगभूत टेम्प्लेट"
        rowsTpl: "पंक्ती टेम्प्लेट, सिंटॅक्स ->"
        headerTpl: "हेडर टेम्प्लेट"
        footerTpl: "फूटर टेम्प्लेट"
      textile:
        escape: "वर्ण एस्केप करा"
        rowHeader: "पंक्ती हेडर"
        thead: "टेबल हेड सिंटॅक्स"
      xml:
        escape: "XML वर्ण एस्केप करा"
        minify: "आउटपुट लहान करा"
        rootElement: "रूट एलिमेंट"
        rowElement: "पंक्ती एलिमेंट"
        declaration: "XML घोषणा"
        attributes: "अॅट्रिब्यूट मोड"
        cdata: "CDATA रॅपर"
        encoding: "एन्कोडिंग"
        indentSize: "इंडेंट आकार"
      yaml:
        indentSize: "इंडेंट आकार"
        arrayStyle: "अॅरे स्टाइल"
        quotationStyle: "कोट स्टाइल"
      pdf:
        theme: "PDF टेबल थीम"
        headerColor: "PDF हेडर रंग"
        showHead: "PDF हेडर डिस्प्ले"
        docTitle: "PDF दस्तऐवज शीर्षक"
        docDescription: "PDF दस्तऐवज वर्णन"
sidebar:
  all: "सर्व कन्व्हर्जन टूल्स"
  dataSource:
    title: "डेटा स्रोत"
    description:
      converter: "%s ते %s कन्व्हर्जनसाठी आयात करा. फाइल अपलोड, ऑनलाइन एडिटिंग आणि वेब डेटा एक्सट्रॅक्शनला समर्थन देते."
      generator: "मॅन्युअल इनपुट, फाइल आयात आणि टेम्प्लेट जनरेशन यासह अनेक इनपुट पद्धतींच्या समर्थनासह टेबल डेटा तयार करा."
  tableEditor:
    title: "ऑनलाइन टेबल एडिटर"
    description:
      converter: "आमच्या टेबल एडिटरचा वापर करून %s ऑनलाइन प्रक्रिया करा. रिकाम्या पंक्ती हटवणे, डुप्लिकेशन, सॉर्टिंग आणि शोधा आणि बदला यांच्या समर्थनासह Excel सारखा ऑपरेशन अनुभव."
      generator: "Excel सारखा ऑपरेशन अनुभव प्रदान करणारा शक्तिशाली ऑनलाइन टेबल एडिटर. रिकाम्या पंक्ती हटवणे, डुप्लिकेशन, सॉर्टिंग आणि शोधा आणि बदला यांना समर्थन देते."
  tableGenerator:
    title: "टेबल जनरेटर"
    description:
      converter: "टेबल जनरेटरच्या रिअल-टाइम प्रीव्ह्यूसह %s जलद तयार करा. समृद्ध एक्सपोर्ट पर्याय, वन-क्लिक कॉपी आणि डाउनलोड."
      generator: "विविध वापराच्या परिस्थितींना पूर्ण करण्यासाठी %s डेटा अनेक फॉर्मॅट्समध्ये एक्सपोर्ट करा. कस्टम पर्याय आणि रिअल-टाइम प्रीव्ह्यूला समर्थन देते."
footer:
  changelog: "बदल लॉग"
  sponsor: "प्रायोजक"
  contact: "आमच्याशी संपर्क साधा"
  privacyPolicy: "गोपनीयता धोरण"
  about: "बद्दल"
  resources: "संसाधने"
  popularConverters: "लोकप्रिय कन्व्हर्टर"
  popularGenerators: "लोकप्रिय जनरेटर"
  dataSecurity: "तुमचा डेटा सुरक्षित आहे - सर्व रूपांतरणे तुमच्या ब्राउझरमध्ये चालतात."
converters:
  Markdown:
    alias: "Markdown टेबल"
    what: "Markdown हे तांत्रिक दस्तऐवजीकरण, ब्लॉग सामग्री निर्मिती आणि वेब विकासासाठी मोठ्या प्रमाणावर वापरली जाणारी हलकी मार्कअप भाषा आहे. त्याचा टेबल सिंटॅक्स संक्षिप्त आणि अंतर्ज्ञानी आहे, मजकूर संरेखन, लिंक एम्बेडिंग आणि फॉर्मॅटिंगला समर्थन देतो. हे प्रोग्रामर आणि तांत्रिक लेखकांचे पसंतीचे साधन आहे, GitHub, GitLab आणि इतर कोड होस्टिंग प्लॅटफॉर्मशी पूर्णपणे सुसंगत आहे."
    step1: "डेटा स्रोत क्षेत्रात Markdown टेबल डेटा पेस्ट करा किंवा अपलोडसाठी थेट .md फाइल्स ड्रॅग आणि ड्रॉप करा. हे साधन आपोआप टेबल संरचना आणि फॉर्मॅटिंग पार्स करते, जटिल नेस्टेड सामग्री आणि विशेष वर्ण हाताळणीला समर्थन देते."
    step3: "अनेक संरेखन पद्धती, मजकूर ठळक करणे, ओळ क्रमांक जोडणे आणि इतर प्रगत फॉर्मॅट सेटिंग्जला समर्थन देणारा मानक Markdown टेबल कोड रिअल-टाइममध्ये तयार करा. तयार केलेला कोड GitHub आणि प्रमुख Markdown एडिटर्सशी पूर्णपणे सुसंगत आहे, एक-क्लिक कॉपीसह वापरण्यासाठी तयार आहे."
    from_alias: "Markdown टेबल फाइल"
    to_alias: "Markdown टेबल फॉर्मॅट"
  Magic:
    alias: "कस्टम टेम्प्लेट"
    what: "Magic टेम्प्लेट हा या साधनाचा अनन्य प्रगत डेटा जनरेटर आहे, जो वापरकर्त्यांना कस्टम टेम्प्लेट सिंटॅक्सद्वारे अनियंत्रित फॉर्मॅट डेटा आउटपुट तयार करण्याची परवानगी देतो. व्हेरिएबल रिप्लेसमेंट, सशर्त निर्णय आणि लूप प्रोसेसिंगला समर्थन देते. जटिल डेटा कन्व्हर्जन गरजा आणि वैयक्तिकृत आउटपुट फॉर्मॅट्स हाताळण्यासाठी हा अंतिम उपाय आहे, विशेषतः डेव्हलपर्स आणि डेटा इंजिनिअर्ससाठी योग्य आहे."
    step1: "अंगभूत सामान्य टेम्प्लेट्स निवडा किंवा कस्टम टेम्प्लेट सिंटॅक्स तयार करा. समृद्ध व्हेरिएबल्स आणि फंक्शन्सना समर्थन देते जे जटिल डेटा संरचना आणि व्यावसायिक तर्कशास्त्र हाताळू शकतात."
    step3: "कस्टम फॉर्मॅट आवश्यकता पूर्णपणे पूर्ण करणारा डेटा आउटपुट तयार करा. जटिल डेटा कन्व्हर्जन लॉजिक आणि सशर्त प्रोसेसिंगला समर्थन देते, डेटा प्रोसेसिंग कार्यक्षमता आणि आउटपुट गुणवत्तेत मोठी सुधारणा करते. बॅच डेटा प्रोसेसिंगसाठी शक्तिशाली साधन."
    from_alias: "टेबल डेटा"
    to_alias: "कस्टम फॉर्मॅट आउटपुट"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) हा सर्वाधिक मोठ्या प्रमाणावर वापरला जाणारा डेटा एक्सचेंज फॉर्मॅट आहे, जो Excel, Google Sheets, डेटाबेस सिस्टम आणि विविध डेटा विश्लेषण साधनांद्वारे पूर्णपणे समर्थित आहे. त्याची सरल संरचना आणि मजबूत सुसंगतता यामुळे डेटा माइग्रेशन, बॅच आयात/निर्यात आणि क्रॉस-प्लॅटफॉर्म डेटा एक्सचेंजसाठी मानक फॉर्मॅट बनला आहे, व्यावसायिक विश्लेषण, डेटा सायन्स आणि सिस्टम इंटिग्रेशनमध्ये मोठ्या प्रमाणावर वापरला जातो."
    step1: "CSV फाइल्स अपलोड करा किंवा थेट CSV डेटा पेस्ट करा. हे साधन बुद्धिमत्तेने विविध विभाजक (स्वल्पविराम, टॅब, अर्धविराम, पाइप इ.) ओळखते, आपोआप डेटा प्रकार आणि एन्कोडिंग फॉर्मॅट्स शोधते, मोठ्या फाइल्स आणि जटिल डेटा संरचनांच्या जलद पार्सिंगला समर्थन देते."
    step3: "कस्टम विभाजक, कोट शैली, एन्कोडिंग फॉर्मॅट्स आणि BOM मार्क सेटिंग्जला समर्थन देणारी मानक CSV फॉर्मॅट फाइल्स तयार करा. लक्ष्य सिस्टमशी परिपूर्ण सुसंगतता सुनिश्चित करते, एंटरप्राइझ-स्तरीय डेटा प्रोसेसिंग गरजा पूर्ण करण्यासाठी डाउनलोड आणि कॉम्प्रेशन पर्याय प्रदान करते."
    from_alias: "CSV डेटा फाइल"
    to_alias: "CSV मानक फॉर्मॅट"
  JSON:
    alias: "JSON Array"
    what: "JSON (JavaScript Object Notation) हा आधुनिक वेब अनुप्रयोग, REST APIs आणि मायक्रोसर्व्हिस आर्किटेक्चरसाठी मानक टेबल डेटा फॉर्मॅट आहे. त्याची स्पष्ट संरचना आणि कार्यक्षम पार्सिंग यामुळे फ्रंट-एंड आणि बॅक-एंड डेटा इंटरॅक्शन, कॉन्फिगरेशन फाइल स्टोरेज आणि NoSQL डेटाबेसेसमध्ये मोठ्या प्रमाणावर वापरले जाते. नेस्टेड ऑब्जेक्ट्स, अॅरे संरचना आणि अनेक डेटा प्रकारांना समर्थन देते, आधुनिक सॉफ्टवेअर विकासासाठी अपरिहार्य टेबल डेटा बनवते."
    step1: "JSON फाइल्स अपलोड करा किंवा JSON अॅरे पेस्ट करा. ऑब्जेक्ट अॅरे, नेस्टेड संरचना आणि जटिल डेटा प्रकारांच्या स्वयंचलित ओळख आणि पार्सिंगला समर्थन देते. हे साधन बुद्धिमत्तेने JSON सिंटॅक्सचे प्रमाणीकरण करते आणि त्रुटी सूचना प्रदान करते."
    step3: "अनेक JSON फॉर्मॅट आउटपुट्स तयार करा: मानक ऑब्जेक्ट अॅरे, 2D अॅरे, कॉलम अॅरे आणि की-व्हॅल्यू पेअर फॉर्मॅट्स. सुंदरीकृत आउटपुट, कॉम्प्रेशन मोड, कस्टम रूट ऑब्जेक्ट नावे आणि इंडेंटेशन सेटिंग्जला समर्थन देते, विविध API इंटरफेसेस आणि डेटा स्टोरेज गरजांशी परिपूर्ण जुळवणी करते."
    from_alias: "JSON Array फाइल"
    to_alias: "JSON मानक फॉर्मॅट"
  JSONLines:
    alias: "JSONLines फॉर्मॅट"
    what: "JSON Lines (NDJSON म्हणूनही ओळखले जाते) हा बिग डेटा प्रोसेसिंग आणि स्ट्रीमिंग डेटा ट्रान्समिशनसाठी महत्त्वाचा फॉर्मॅट आहे, ज्यामध्ये प्रत्येक ओळीत स्वतंत्र JSON ऑब्जेक्ट असते. लॉग विश्लेषण, डेटा स्ट्रीम प्रोसेसिंग, मशीन लर्निंग आणि डिस्ट्रिब्यूटेड सिस्टममध्ये मोठ्या प्रमाणावर वापरले जाते. वाढीव प्रोसेसिंग आणि समांतर कॉम्प्युटिंगला समर्थन देते, मोठ्या प्रमाणावरील संरचित डेटा हाताळण्यासाठी आदर्श निवड बनवते."
    step1: "JSONLines फाइल्स अपलोड करा किंवा डेटा पेस्ट करा. हे साधन ओळीनुसार JSON ऑब्जेक्ट्स पार्स करते, मोठ्या फाइल स्ट्रीमिंग प्रोसेसिंग आणि त्रुटी ओळी वगळण्याच्या कार्यक्षमतेला समर्थन देते."
    step3: "प्रत्येक ओळीत संपूर्ण JSON ऑब्जेक्ट आउटपुट करणारा मानक JSONLines फॉर्मॅट तयार करा. स्ट्रीमिंग प्रोसेसिंग, बॅच आयात आणि बिग डेटा विश्लेषण परिस्थितींसाठी योग्य, डेटा प्रमाणीकरण आणि फॉर्मॅट ऑप्टिमायझेशनला समर्थन देते."
    from_alias: "JSONLines डेटा"
    to_alias: "JSONLines स्ट्रीमिंग फॉर्मॅट"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) हा एंटरप्राइझ-स्तरीय डेटा एक्सचेंज आणि कॉन्फिगरेशन व्यवस्थापनासाठी मानक फॉर्मॅट आहे, कठोर सिंटॅक्स स्पेसिफिकेशन आणि शक्तिशाली प्रमाणीकरण यंत्रणांसह. वेब सेवा, कॉन्फिगरेशन फाइल्स, दस्तऐवज स्टोरेज आणि सिस्टम इंटिग्रेशनमध्ये मोठ्या प्रमाणावर वापरले जाते. नेमस्पेसेस, स्कीमा प्रमाणीकरण आणि XSLT ट्रान्सफॉर्मेशनला समर्थन देते, एंटरप्राइझ अनुप्रयोगांसाठी महत्त्वाचा टेबल डेटा बनवते."
    step1: "XML फाइल्स अपलोड करा किंवा XML डेटा पेस्ट करा. हे साधन आपोआप XML संरचना पार्स करते आणि त्यास टेबल फॉर्मॅटमध्ये रूपांतरित करते, नेमस्पेस, अॅट्रिब्यूट हाताळणी आणि जटिल नेस्टेड संरचनांना समर्थन देते."
    step3: "XML मानकांचे पालन करणारा XML आउटपुट तयार करा. कस्टम रूट एलिमेंट्स, रो एलिमेंट नावे, अॅट्रिब्यूट मोड, CDATA रॅपिंग आणि कॅरेक्टर एन्कोडिंग सेटिंग्जला समर्थन देते. डेटा अखंडता आणि सुसंगतता सुनिश्चित करते, एंटरप्राइझ-स्तरीय अनुप्रयोग आवश्यकता पूर्ण करते."
    from_alias: "XML डेटा फाइल"
    to_alias: "XML मानक फॉर्मॅट"
  YAML:
    alias: "YAML कॉन्फिगरेशन"
    what: "YAML हा मानव-मैत्रीपूर्ण डेटा सीरियलायझेशन मानक आहे, त्याच्या स्पष्ट पदानुक्रमित संरचना आणि संक्षिप्त सिंटॅक्ससाठी प्रसिद्ध आहे. कॉन्फिगरेशन फाइल्स, DevOps टूल चेन्स, Docker Compose आणि Kubernetes तैनातीमध्ये मोठ्या प्रमाणावर वापरले जाते. त्याची मजबूत वाचनीयता आणि संक्षिप्त सिंटॅक्स यामुळे आधुनिक क्लाउड-नेटिव्ह अनुप्रयोग आणि स्वयंचलित ऑपरेशन्ससाठी महत्त्वाचा कॉन्फिगरेशन फॉर्मॅट बनला आहे."
    step1: "YAML फाइल्स अपलोड करा किंवा YAML डेटा पेस्ट करा. हे साधन बुद्धिमत्तेने YAML संरचना पार्स करते आणि सिंटॅक्स शुद्धता प्रमाणित करते, मल्टी-डॉक्युमेंट फॉर्मॅट्स आणि जटिल डेटा प्रकारांना समर्थन देते."
    step3: "ब्लॉक आणि फ्लो अॅरे शैली, अनेक कोट सेटिंग्ज, कस्टम इंडेंटेशन आणि टिप्पणी संरक्षणासह मानक YAML फॉर्मॅट आउटपुट तयार करा. आउटपुट YAML फाइल्स विविध पार्सर्स आणि कॉन्फिगरेशन सिस्टमशी पूर्णपणे सुसंगत असल्याची खात्री करते."
    from_alias: "YAML कॉन्फिगरेशन फाइल"
    to_alias: "YAML मानक फॉर्मॅट"
  MySQL:
      alias: "MySQL क्वेरी परिणाम"
      what: "MySQL हा जगातील सर्वाधिक लोकप्रिय ओपन-सोर्स रिलेशनल डेटाबेस व्यवस्थापन प्रणाली आहे, त्याच्या उच्च कार्यक्षमता, विश्वसनीयता आणि वापराच्या सुलभतेसाठी प्रसिद्ध आहे. वेब अनुप्रयोग, एंटरप्राइझ सिस्टम आणि डेटा विश्लेषण प्लॅटफॉर्ममध्ये मोठ्या प्रमाणावर वापरले जाते. MySQL क्वेरी परिणामांमध्ये सामान्यतः संरचित टेबल डेटा असतो, डेटाबेस व्यवस्थापन आणि डेटा विश्लेषण कार्यात महत्त्वाचा डेटा स्रोत म्हणून काम करतो."
      step1: "डेटा स्रोत क्षेत्रात MySQL क्वेरी आउटपुट परिणाम पेस्ट करा. हे साधन आपोआप MySQL कमांड-लाइन आउटपुट फॉर्मॅट ओळखते आणि पार्स करते, विविध क्वेरी परिणाम शैली आणि कॅरेक्टर एन्कोडिंगला समर्थन देते, हेडर आणि डेटा रो बुद्धिमत्तेने हाताळते."
      step3: "MySQL क्वेरी परिणाम अनेक टेबल डेटा फॉर्मॅट्समध्ये जलद रूपांतरित करा, डेटा विश्लेषण, अहवाल निर्मिती, क्रॉस-सिस्टम डेटा माइग्रेशन आणि डेटा प्रमाणीकरण सुलभ करते. डेटाबेस प्रशासक आणि डेटा विश्लेषकांसाठी व्यावहारिक साधन."
      from_alias: "MySQL क्वेरी आउटपुट"
      to_alias: "MySQL टेबल डेटा"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) हा रिलेशनल डेटाबेसेसची मानक ऑपरेशन भाषा आहे, डेटा क्वेरी, इन्सर्ट, अपडेट आणि डिलीट ऑपरेशन्ससाठी वापरली जाते. डेटाबेस व्यवस्थापनाचे मुख्य तंत्रज्ञान म्हणून, SQL डेटा विश्लेषण, बिझनेस इंटेलिजन्स, ETL प्रोसेसिंग आणि डेटा वेअरहाउस बांधकामात मोठ्या प्रमाणावर वापरले जाते. हे डेटा व्यावसायिकांसाठी आवश्यक कौशल्य साधन आहे."
    step1: "INSERT SQL स्टेटमेंट्स पेस्ट करा किंवा .sql फाइल्स अपलोड करा. हे साधन बुद्धिमत्तेने SQL सिंटॅक्स पार्स करते आणि टेबल डेटा काढते, अनेक SQL डायलेक्ट्स आणि जटिल क्वेरी स्टेटमेंट प्रोसेसिंगला समर्थन देते."
    step3: "मानक SQL INSERT स्टेटमेंट्स आणि टेबल क्रिएशन स्टेटमेंट्स तयार करा. अनेक डेटाबेस डायलेक्ट्स (MySQL, PostgreSQL, SQLite, SQL Server, Oracle) ला समर्थन देते, आपोआप डेटा प्रकार मॅपिंग, कॅरेक्टर एस्केपिंग आणि प्राथमिक की मर्यादा हाताळते. तयार केलेला SQL कोड थेट एक्झिक्यूट करता येईल याची खात्री करते."
    from_alias: "SQL डेटा फाइल"
    to_alias: "SQL मानक स्टेटमेंट"
  Qlik:
      alias: "Qlik टेबल"
      what: "Qlik हा डेटा व्हिज्युअलायझेशन, एक्झिक्यूटिव्ह डॅशबोर्ड आणि सेल्फ-सर्व्हिस बिझनेस इंटेलिजन्स उत्पादनांमध्ये विशेषज्ञ असलेला सॉफ्टवेअर विक्रेता आहे, Tableau आणि Microsoft सोबत."
      step1: ""
      step3: "शेवटी, [टेबल जनरेटर](#TableGenerator) कन्व्हर्जन परिणाम दाखवतो. तुमच्या Qlik Sense, Qlik AutoML, QlikView किंवा इतर Qlik-सक्षम सॉफ्टवेअरमध्ये वापरा."
      from_alias: "Qlik टेबल"
      to_alias: "Qlik टेबल"
  DAX:
      alias: "DAX टेबल"
      what: "DAX (Data Analysis Expressions) ही Microsoft Power BI मध्ये गणना केलेले कॉलम, मेझर्स आणि कस्टम टेबल्स तयार करण्यासाठी वापरली जाणारी प्रोग्रामिंग भाषा आहे."
      step1: ""
      step3: "शेवटी, [टेबल जनरेटर](#TableGenerator) कन्व्हर्जन परिणाम दाखवतो. अपेक्षेप्रमाणे, हे Microsoft Power BI, Microsoft Analysis Services आणि Microsoft Power Pivot for Excel यासह अनेक Microsoft उत्पादनांमध्ये वापरले जाते."
      from_alias: "DAX टेबल"
      to_alias: "DAX टेबल"
  Firebase:
    alias: "Firebase यादी"
    what: "Firebase हा BaaS अनुप्रयोग विकास प्लॅटफॉर्म आहे जो रिअल-टाइम डेटाबेस, क्लाउड स्टोरेज, प्रमाणीकरण, क्रॅश रिपोर्टिंग इत्यादी होस्ट केलेल्या बॅकएंड सेवा प्रदान करतो."
    step1: ""
    step3: "शेवटी, [टेबल जनरेटर](#TableGenerator) कन्व्हर्जन परिणाम दाखवतो. त्यानंतर तुम्ही Firebase डेटाबेसमधील डेटाच्या यादीत जोडण्यासाठी Firebase API मध्ये push पद्धत वापरू शकता."
    from_alias: "Firebase यादी"
    to_alias: "Firebase यादी"
  HTML:
    alias: "HTML टेबल"
    what: "HTML टेबल हे वेब पेजेसमध्ये संरचित डेटा प्रदर्शित करण्याचा मानक मार्ग आहे, जो table, tr, td आणि इतर टॅगसह तयार केला जातो. समृद्ध शैली कस्टमायझेशन, रिस्पॉन्सिव्ह लेआउट आणि इंटरॅक्टिव्ह कार्यक्षमतेला समर्थन देते. वेबसाइट विकास, डेटा प्रदर्शन आणि अहवाल निर्मितीमध्ये मोठ्या प्रमाणावर वापरले जाते, फ्रंट-एंड विकास आणि वेब डिझाइनचा महत्त्वाचा घटक म्हणून काम करते."
    step1: "टेबल असलेला HTML कोड पेस्ट करा किंवा HTML फाइल अपलोड करा. हे साधन आपोआप पेजेसमधून टेबल डेटा ओळखते आणि काढते, जटिल HTML संरचना, CSS शैली आणि नेस्टेड टेबल प्रक्रियेला समर्थन देते."
    step3: "thead/tbody संरचना, CSS क्लास सेटिंग्ज, टेबल कॅप्शन, रो/कॉलम हेडर आणि रिस्पॉन्सिव्ह अॅट्रिब्यूट कॉन्फिगरेशनसह सिमेंटिक HTML टेबल कोड तयार करा. तयार केलेला टेबल कोड चांगल्या प्रवेशयोग्यता आणि SEO मैत्रीपणासह वेब मानकांची पूर्तता करतो याची खात्री करते."
    from_alias: "HTML वेब टेबल"
    to_alias: "HTML मानक टेबल"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel हा जगातील सर्वाधिक लोकप्रिय स्प्रेडशीट सॉफ्टवेअर आहे, व्यावसायिक विश्लेषण, आर्थिक व्यवस्थापन, डेटा प्रोसेसिंग आणि अहवाल निर्मितीमध्ये मोठ्या प्रमाणावर वापरला जातो. त्याच्या शक्तिशाली डेटा प्रोसेसिंग क्षमता, समृद्ध फंक्शन लायब्ररी आणि लवचिक व्हिज्युअलायझेशन वैशिष्ट्यांमुळे ऑफिस ऑटोमेशन आणि डेटा विश्लेषणासाठी मानक साधन बनले आहे, जवळजवळ सर्व उद्योग आणि क्षेत्रांमध्ये व्यापक अनुप्रयोग आहेत."
    step1: "Excel फाइल्स अपलोड करा (.xlsx, .xls फॉर्मॅट्सला समर्थन) किंवा Excel मधून थेट टेबल डेटा कॉपी करून पेस्ट करा. हे साधन मल्टी-वर्कशीट प्रोसेसिंग, जटिल फॉर्मॅट ओळख आणि मोठ्या फाइल्सच्या जलद पार्सिंगला समर्थन देते, मर्ज केलेले सेल आणि डेटा प्रकार आपोआप हाताळते."
    step3: "Excel-सुसंगत टेबल डेटा तयार करा जो थेट Excel मध्ये पेस्ट करता येतो किंवा मानक .xlsx फाइल्स म्हणून डाउनलोड करता येतो. वर्कशीट नामकरण, सेल फॉर्मॅटिंग, ऑटो कॉलम रुंदी, हेडर स्टाइलिंग आणि डेटा व्हॅलिडेशन सेटिंग्जला समर्थन देते. आउटपुट Excel फाइल्समध्ये व्यावसायिक देखावा आणि संपूर्ण कार्यक्षमता असल्याची खात्री करते."
    from_alias: "Excel स्प्रेडशीट"
    to_alias: "Excel मानक फॉर्मॅट"
  LaTeX:
    alias: "LaTeX टेबल"
    what: "LaTeX हा व्यावसायिक दस्तऐवज टाइपसेटिंग सिस्टम आहे, विशेषतः शैक्षणिक पेपर्स, तांत्रिक दस्तऐवज आणि वैज्ञानिक प्रकाशने तयार करण्यासाठी योग्य आहे. त्याची टेबल कार्यक्षमता शक्तिशाली आहे, जटिल गणितीय सूत्रे, अचूक लेआउट नियंत्रण आणि उच्च-गुणवत्तेच्या PDF आउटपुटला समर्थन देते. हे शैक्षणिक आणि वैज्ञानिक प्रकाशनातील मानक साधन आहे, जर्नल पेपर्स, प्रबंध आणि तांत्रिक मॅन्युअल टाइपसेटिंगमध्ये मोठ्या प्रमाणावर वापरले जाते."
    step1: "LaTeX टेबल कोड पेस्ट करा किंवा .tex फाइल्स अपलोड करा. हे साधन LaTeX टेबल सिंटॅक्स पार्स करते आणि डेटा सामग्री काढते, अनेक टेबल एनवायरनमेंट्स (tabular, longtable, array इ.) आणि जटिल फॉर्मॅट कमांड्सला समर्थन देते."
    step3: "अनेक टेबल एनवायरनमेंट निवड, बॉर्डर स्टाइल कॉन्फिगरेशन, कॅप्शन स्थिती सेटिंग्ज, डॉक्युमेंट क्लास स्पेसिफिकेशन आणि पॅकेज व्यवस्थापनासह व्यावसायिक LaTeX टेबल कोड तयार करा. संपूर्ण कॉम्पाइल करण्यायोग्य LaTeX दस्तऐवज तयार करू शकते, आउटपुट टेबल्स शैक्षणिक प्रकाशन मानकांची पूर्तता करतात याची खात्री करते."
    from_alias: "LaTeX दस्तऐवज टेबल"
    to_alias: "LaTeX व्यावसायिक फॉर्मॅट"
  ASCII:
    alias: "ASCII टेबल"
    what: "ASCII टेबल्स टेबल बॉर्डर आणि संरचना काढण्यासाठी प्लेन टेक्स्ट कॅरेक्टर्स वापरतात, सर्वोत्तम सुसंगतता आणि पोर्टेबिलिटी प्रदान करतात. सर्व टेक्स्ट एडिटर्स, टर्मिनल एनवायरनमेंट्स आणि ऑपरेटिंग सिस्टमशी सुसंगत. कोड डॉक्युमेंटेशन, तांत्रिक मॅन्युअल्स, README फाइल्स आणि कमांड-लाइन टूल आउटपुटमध्ये मोठ्या प्रमाणावर वापरले जाते. प्रोग्रामर आणि सिस्टम अॅडमिनिस्ट्रेटर्ससाठी पसंतीचा डेटा डिस्प्ले फॉर्मॅट."
    step1: "ASCII टेबल्स असणाऱ्या टेक्स्ट फाइल्स अपलोड करा किंवा थेट टेबल डेटा पेस्ट करा. हे साधन बुद्धिमत्तेने ASCII टेबल संरचना ओळखते आणि पार्स करते, अनेक बॉर्डर शैली आणि संरेखन फॉर्मॅट्सला समर्थन देते."
    step3: "अनेक बॉर्डर शैली (सिंगल लाइन, डबल लाइन, गोलाकार कोपरे इ.), टेक्स्ट संरेखन पद्धती आणि ऑटो कॉलम रुंदीसह सुंदर प्लेन टेक्स्ट ASCII टेबल्स तयार करा. तयार केलेल्या टेबल्स कोड एडिटर्स, दस्तऐवज आणि कमांड लाइन्समध्ये परिपूर्ण प्रदर्शित होतात."
    from_alias: "ASCII टेक्स्ट टेबल"
    to_alias: "ASCII मानक फॉर्मॅट"
  MediaWiki:
    alias: "MediaWiki टेबल"
    what: "MediaWiki हा Wikipedia सारख्या प्रसिद्ध विकी साइटद्वारे वापरला जाणारा ओपन-सोर्स सॉफ्टवेअर प्लॅटफॉर्म आहे. त्याचा टेबल सिंटॅक्स संक्षिप्त परंतु शक्तिशाली आहे, टेबल स्टाइल कस्टमायझेशन, सॉर्टिंग कार्यक्षमता आणि लिंक एम्बेडिंगला समर्थन देतो. ज्ञान व्यवस्थापन, सहयोगी संपादन आणि सामग्री व्यवस्थापन प्रणालींमध्ये मोठ्या प्रमाणावर वापरले जाते, विकी विश्वकोश आणि ज्ञान आधार तयार करण्यासाठी मुख्य तंत्रज्ञान म्हणून काम करते."
    step1: "MediaWiki टेबल कोड पेस्ट करा किंवा विकी स्रोत फाइल्स अपलोड करा. हे साधन विकी मार्कअप सिंटॅक्स पार्स करते आणि टेबल डेटा काढते, जटिल विकी सिंटॅक्स आणि टेम्प्लेट प्रोसेसिंगला समर्थन देते."
    step3: "हेडर स्टाइल सेटिंग्ज, सेल संरेखन, सॉर्टिंग कार्यक्षमता सक्षम करणे आणि कोड कॉम्प्रेशन पर्यायांसह मानक MediaWiki टेबल कोड तयार करा. तयार केलेला कोड थेट विकी पेज संपादनासाठी वापरला जाऊ शकतो, MediaWiki प्लॅटफॉर्मवर परिपूर्ण प्रदर्शन सुनिश्चित करतो."
    from_alias: "MediaWiki स्रोत कोड"
    to_alias: "MediaWiki टेबल सिंटॅक्स"
  TracWiki:
    alias: "TracWiki टेबल"
    what: "Trac हा वेब-आधारित प्रोजेक्ट व्यवस्थापन आणि बग ट्रॅकिंग सिस्टम आहे जो टेबल सामग्री तयार करण्यासाठी सरलीकृत विकी सिंटॅक्स वापरतो."
    step1: "TracWiki फाइल्स अपलोड करा किंवा टेबल डेटा पेस्ट करा."
    step3: "रो/कॉलम हेडर सेटिंग्जसह TracWiki-सुसंगत टेबल कोड तयार करा, प्रोजेक्ट दस्तऐवज व्यवस्थापन सुलभ करते."
    from_alias: "TracWiki टेबल"
    to_alias: "TracWiki फॉर्मॅट"
  AsciiDoc:
    alias: "AsciiDoc टेबल"
    what: "AsciiDoc हा हलकी मार्कअप भाषा आहे जी HTML, PDF, मॅन्युअल पेजेस आणि इतर फॉर्मॅट्समध्ये रूपांतरित करता येते, तांत्रिक दस्तऐवजीकरण लेखनासाठी मोठ्या प्रमाणावर वापरली जाते."
    step1: "AsciiDoc फाइल्स अपलोड करा किंवा डेटा पेस्ट करा."
    step3: "हेडर, फूटर आणि शीर्षक सेटिंग्जसह AsciiDoc टेबल सिंटॅक्स तयार करा, AsciiDoc एडिटर्समध्ये थेट वापरण्यायोग्य."
    from_alias: "AsciiDoc टेबल"
    to_alias: "AsciiDoc फॉर्मॅट"
  reStructuredText:
    alias: "reStructuredText टेबल"
    what: "reStructuredText हा Python समुदायाचा मानक दस्तऐवजीकरण फॉर्मॅट आहे, समृद्ध टेबल सिंटॅक्सला समर्थन देतो, सामान्यतः Sphinx दस्तऐवजीकरण निर्मितीसाठी वापरला जातो."
    step1: ".rst फाइल्स अपलोड करा किंवा reStructuredText डेटा पेस्ट करा."
    step3: "अनेक बॉर्डर शैलींसह मानक reStructuredText टेबल्स तयार करा, Sphinx दस्तऐवजीकरण प्रोजेक्ट्समध्ये थेट वापरण्यायोग्य."
    from_alias: "reStructuredText टेबल"
    to_alias: "reStructuredText फॉर्मॅट"
  PHP:
    alias: "PHP Array"
    what: "PHP हा लोकप्रिय सर्व्हर-साइड स्क्रिप्टिंग भाषा आहे, अॅरे त्याची मुख्य डेटा संरचना आहे, वेब विकास आणि डेटा प्रोसेसिंगमध्ये मोठ्या प्रमाणावर वापरली जाते."
    step1: "PHP अॅरे असणाऱ्या फाइल्स अपलोड करा किंवा थेट डेटा पेस्ट करा."
    step3: "PHP प्रोजेक्ट्समध्ये थेट वापरता येणारा मानक PHP अॅरे कोड तयार करा, असोसिएटिव्ह आणि इंडेक्स्ड अॅरे फॉर्मॅट्सला समर्थन देतो."
    from_alias: "PHP Array"
    to_alias: "PHP Code"
  Ruby:
    alias: "Ruby Array"
    what: "Ruby हा संक्षिप्त आणि सुंदर सिंटॅक्स असलेली डायनामिक ऑब्जेक्ट-ओरिएंटेड प्रोग्रामिंग भाषा आहे, अॅरे ही महत्त्वाची डेटा संरचना आहे."
    step1: "Ruby फाइल्स अपलोड करा किंवा अॅरे डेटा पेस्ट करा."
    step3: "Ruby सिंटॅक्स स्पेसिफिकेशन्सचे पालन करणारा Ruby अॅरे कोड तयार करा, Ruby प्रोजेक्ट्समध्ये थेट वापरण्यायोग्य."
    from_alias: "Ruby Array"
    to_alias: "Ruby Code"
  ASP:
    alias: "ASP Array"
    what: "ASP (Active Server Pages) हा Microsoft चा सर्व्हर-साइड स्क्रिप्टिंग एनवायरनमेंट आहे, डायनामिक वेब पेजेस विकसित करण्यासाठी अनेक प्रोग्रामिंग भाषांना समर्थन देतो."
    step1: "ASP फाइल्स अपलोड करा किंवा अॅरे डेटा पेस्ट करा."
    step3: "VBScript आणि JScript सिंटॅक्सला समर्थन देणारा ASP-सुसंगत अॅरे कोड तयार करा, ASP.NET प्रोजेक्ट्समध्ये वापरण्यायोग्य."
    from_alias: "ASP Array"
    to_alias: "ASP Code"
  ActionScript:
    alias: "ActionScript Array"
    what: "ActionScript हा ऑब्जेक्ट-ओरिएंटेड प्रोग्रामिंग भाषा आहे जी मुख्यतः Adobe Flash आणि AIR अनुप्रयोग विकासासाठी वापरली जाते."
    step1: ".as फाइल्स अपलोड करा किंवा ActionScript डेटा पेस्ट करा."
    step3: "AS3 सिंटॅक्स मानकांचे पालन करणारा ActionScript अॅरे कोड तयार करा, Flash आणि Flex प्रोजेक्ट विकासासाठी वापरण्यायोग्य."
    from_alias: "ActionScript Array"
    to_alias: "ActionScript Code"
  BBCode:
    alias: "BBCode टेबल"
    what: "BBCode हा फोरम आणि ऑनलाइन समुदायांमध्ये सामान्यतः वापरली जाणारी हलकी मार्कअप भाषा आहे, टेबल समर्थनासह सरल फॉर्मॅटिंग कार्यक्षमता प्रदान करते."
    step1: "BBCode असणाऱ्या फाइल्स अपलोड करा किंवा डेटा पेस्ट करा."
    step3: "फोरम पोस्टिंग आणि कम्युनिटी सामग्री निर्मितीसाठी योग्य BBCode टेबल कोड तयार करा, संकुचित आउटपुट फॉर्मॅटला समर्थन देतो."
    from_alias: "BBCode टेबल"
    to_alias: "BBCode फॉर्मॅट"
  PDF:
    alias: "PDF टेबल"
    what: "PDF (Portable Document Format) हा निश्चित लेआउट, सुसंगत प्रदर्शन आणि उच्च-गुणवत्तेच्या प्रिंटिंग वैशिष्ट्यांसह क्रॉस-प्लॅटफॉर्म दस्तऐवज मानक आहे. औपचारिक दस्तऐवज, अहवाल, बीजक, करार आणि शैक्षणिक पेपर्समध्ये मोठ्या प्रमाणावर वापरले जाते. व्यावसायिक संवाद आणि दस्तऐवज संग्रहणासाठी पसंतीचा फॉर्मॅट, विविध उपकरणे आणि ऑपरेटिंग सिस्टममध्ये पूर्णपणे सुसंगत दृश्य प्रभाव सुनिश्चित करतो."
    step1: "कोणत्याही फॉर्मॅटमध्ये टेबल डेटा आयात करा. हे साधन आपोआप डेटा संरचना विश्लेषित करते आणि बुद्धिमान लेआउट डिझाइन करते, मोठ्या टेबल ऑटो-पेजिनेशन आणि जटिल डेटा प्रकार प्रोसेसिंगला समर्थन देते."
    step3: "अनेक व्यावसायिक थीम शैली (व्यावसायिक, शैक्षणिक, मिनिमलिस्ट इ.), बहुभाषिक फॉन्ट्स, ऑटो-पेजिनेशन, वॉटरमार्क जोडणे आणि प्रिंट ऑप्टिमायझेशनसह उच्च-गुणवत्तेच्या PDF टेबल फाइल्स तयार करा. आउटपुट PDF दस्तऐवजांमध्ये व्यावसायिक देखावा असल्याची खात्री करते, व्यावसायिक सादरीकरण आणि औपचारिक प्रकाशनासाठी थेट वापरण्यायोग्य."
    from_alias: "टेबल डेटा"
    to_alias: "PDF व्यावसायिक दस्तऐवज"
  JPEG:
    alias: "JPEG प्रतिमा"
    what: "JPEG हा उत्कृष्ट कॉम्प्रेशन प्रभाव आणि व्यापक सुसंगततेसह सर्वाधिक मोठ्या प्रमाणावर वापरला जाणारा डिजिटल प्रतिमा फॉर्मॅट आहे. त्याचा छोटा फाइल आकार आणि जलद लोडिंग गती वेब डिस्प्ले, सोशल मीडिया शेअरिंग, दस्तऐवज चित्रण आणि ऑनलाइन सादरीकरणासाठी योग्य बनवते. डिजिटल मीडिया आणि नेटवर्क कम्युनिकेशनसाठी मानक प्रतिमा फॉर्मॅट, जवळजवळ सर्व उपकरणे आणि सॉफ्टवेअरद्वारे परिपूर्ण समर्थित."
    step1: "कोणत्याही फॉर्मॅटमध्ये टेबल डेटा आयात करा. हे साधन बुद्धिमान लेआउट डिझाइन आणि व्हिज्युअल ऑप्टिमायझेशन करते, आपोआप इष्टतम आकार आणि रिझोल्यूशन गणना करते."
    step3: "अनेक थीम कलर स्कीम (हलकी, गडद, डोळा-मैत्रीपूर्ण इ.), अॅडॅप्टिव्ह लेआउट, टेक्स्ट स्पष्टता ऑप्टिमायझेशन आणि आकार कस्टमायझेशनसह उच्च-व्याख्या JPEG टेबल प्रतिमा तयार करा. ऑनलाइन शेअरिंग, दस्तऐवज इन्सर्शन आणि प्रेझेंटेशन वापरासाठी योग्य, विविध डिस्प्ले उपकरणांवर उत्कृष्ट व्हिज्युअल इफेक्ट्स सुनिश्चित करते."
    from_alias: "टेबल डेटा"
    to_alias: "JPEG उच्च-व्याख्या प्रतिमा"
  Jira:
    alias: "Jira टेबल"
    what: "JIRA हा Atlassian द्वारे विकसित केलेला व्यावसायिक प्रोजेक्ट व्यवस्थापन आणि बग ट्रॅकिंग सॉफ्टवेअर आहे, चपळ विकास, सॉफ्टवेअर चाचणी आणि प्रोजेक्ट सहकार्यामध्ये मोठ्या प्रमाणावर वापरला जातो. त्याची टेबल कार्यक्षमता समृद्ध फॉर्मॅटिंग पर्याय आणि डेटा डिस्प्लेला समर्थन देते, सॉफ्टवेअर विकास टीम, प्रोजेक्ट मॅनेजर आणि गुणवत्ता आश्वासन कर्मचाऱ्यांसाठी आवश्यकता व्यवस्थापन, बग ट्रॅकिंग आणि प्रगती अहवालामध्ये महत्त्वाचे साधन म्हणून काम करते."
    step1: "टेबल डेटा असणाऱ्या फाइल्स अपलोड करा किंवा थेट डेटा सामग्री पेस्ट करा. हे साधन आपोआप टेबल डेटा आणि विशेष कॅरेक्टर एस्केपिंग प्रक्रिया करते."
    step3: "हेडर स्टाइल सेटिंग्ज, सेल संरेखन, कॅरेक्टर एस्केप प्रोसेसिंग आणि फॉर्मॅट ऑप्टिमायझेशनसह JIRA प्लॅटफॉर्म-सुसंगत टेबल कोड तयार करा. तयार केलेला कोड थेट JIRA इश्यू वर्णन, टिप्पण्या किंवा विकी पेजेसमध्ये पेस्ट करता येतो, JIRA सिस्टममध्ये योग्य प्रदर्शन आणि रेंडरिंग सुनिश्चित करते."
    from_alias: "प्रोजेक्ट डेटा"
    to_alias: "Jira टेबल सिंटॅक्स"
  Textile:
    alias: "Textile टेबल"
    what: "Textile हा सरल आणि शिकण्यास सुलभ सिंटॅक्स असलेली संक्षिप्त हलकी मार्कअप भाषा आहे, सामग्री व्यवस्थापन प्रणाली, ब्लॉग प्लॅटफॉर्म आणि फोरम सिस्टममध्ये मोठ्या प्रमाणावर वापरली जाते. त्याचा टेबल सिंटॅक्स स्पष्ट आणि अंतर्ज्ञानी आहे, जलद फॉर्मॅटिंग आणि स्टाइल सेटिंग्जला समर्थन देतो. सामग्री निर्माते आणि वेबसाइट प्रशासकांसाठी जलद दस्तऐवज लेखन आणि सामग्री प्रकाशनासाठी आदर्श साधन."
    step1: "Textile फॉर्मॅट फाइल्स अपलोड करा किंवा टेबल डेटा पेस्ट करा. हे साधन Textile मार्कअप सिंटॅक्स पार्स करते आणि टेबल सामग्री काढते."
    step3: "हेडर मार्कअप, सेल संरेखन, विशेष कॅरेक्टर एस्केपिंग आणि फॉर्मॅट ऑप्टिमायझेशनसह मानक Textile टेबल सिंटॅक्स तयार करा. तयार केलेला कोड Textile समर्थन करणाऱ्या CMS सिस्टम, ब्लॉग प्लॅटफॉर्म आणि दस्तऐवज सिस्टममध्ये थेट वापरला जाऊ शकतो, योग्य सामग्री रेंडरिंग आणि प्रदर्शन सुनिश्चित करते."
    from_alias: "Textile दस्तऐवज"
    to_alias: "Textile टेबल सिंटॅक्स"
  PNG:
    alias: "PNG प्रतिमा"
    what: "PNG (Portable Network Graphics) हा उत्कृष्ट कॉम्प्रेशन आणि पारदर्शकता समर्थनासह हानिरहित प्रतिमा फॉर्मॅट आहे. वेब डिझाइन, डिजिटल ग्राफिक्स आणि व्यावसायिक फोटोग्राफीमध्ये मोठ्या प्रमाणावर वापरला जातो. त्याची उच्च गुणवत्ता आणि व्यापक सुसंगतता स्क्रीनशॉट्स, लोगो, आकृत्या आणि तीक्ष्ण तपशील आणि पारदर्शक पार्श्वभूमी आवश्यक असलेल्या कोणत्याही प्रतिमांसाठी आदर्श बनवते."
    step1: "कोणत्याही फॉर्मॅटमध्ये टेबल डेटा आयात करा. हे साधन बुद्धिमान लेआउट डिझाइन आणि व्हिज्युअल ऑप्टिमायझेशन करते, PNG आउटपुटसाठी आपोआप इष्टतम आकार आणि रिझोल्यूशन गणना करते."
    step3: "अनेक थीम कलर स्कीम, पारदर्शक पार्श्वभूमी, अॅडॅप्टिव्ह लेआउट आणि टेक्स्ट स्पष्टता ऑप्टिमायझेशनसह उच्च-गुणवत्तेच्या PNG टेबल प्रतिमा तयार करा. उत्कृष्ट व्हिज्युअल गुणवत्तेसह वेब वापर, दस्तऐवज इन्सर्शन आणि व्यावसायिक सादरीकरणासाठी परिपूर्ण."
    from_alias: "टेबल डेटा"
    to_alias: "PNG उच्च-गुणवत्ता प्रतिमा"
  TOML:
    alias: "TOML कॉन्फिगरेशन"
    what: "TOML (Tom's Obvious, Minimal Language) हा वाचण्यास आणि लिहिण्यास सुलभ कॉन्फिगरेशन फाइल फॉर्मॅट आहे. अस्पष्ट आणि सरल बनवण्यासाठी डिझाइन केलेला, कॉन्फिगरेशन व्यवस्थापनासाठी आधुनिक सॉफ्टवेअर प्रोजेक्ट्समध्ये मोठ्या प्रमाणावर वापरला जातो. त्याचा स्पष्ट सिंटॅक्स आणि मजबूत टाइपिंग अनुप्रयोग सेटिंग्ज आणि प्रोजेक्ट कॉन्फिगरेशन फाइल्ससाठी उत्कृष्ट निवड बनवते."
    step1: "TOML फाइल्स अपलोड करा किंवा कॉन्फिगरेशन डेटा पेस्ट करा. हे साधन TOML सिंटॅक्स पार्स करते आणि संरचित कॉन्फिगरेशन माहिती काढते."
    step3: "नेस्टेड संरचना, डेटा प्रकार आणि टिप्पण्यांसह मानक TOML फॉर्मॅट तयार करा. तयार केलेल्या TOML फाइल्स अनुप्रयोग कॉन्फिगरेशन, बिल्ड टूल्स आणि प्रोजेक्ट सेटिंग्जसाठी परिपूर्ण आहेत."
    from_alias: "TOML कॉन्फिगरेशन"
    to_alias: "TOML फॉर्मॅट"
  INI:
    alias: "INI कॉन्फिगरेशन"
    what: "INI फाइल्स अनेक अनुप्रयोग आणि ऑपरेटिंग सिस्टमद्वारे वापरल्या जाणाऱ्या सरल कॉन्फिगरेशन फाइल्स आहेत. त्यांची सरळ की-व्हॅल्यू पेअर संरचना त्यांना मॅन्युअली वाचणे आणि संपादित करणे सुलभ बनवते. Windows अनुप्रयोग, लेगसी सिस्टम आणि मानवी वाचनीयता महत्त्वाची असलेल्या सरल कॉन्फिगरेशन परिस्थितींमध्ये मोठ्या प्रमाणावर वापरले जाते."
    step1: "INI फाइल्स अपलोड करा किंवा कॉन्फिगरेशन डेटा पेस्ट करा. हे साधन INI सिंटॅक्स पार्स करते आणि विभाग-आधारित कॉन्फिगरेशन माहिती काढते."
    step3: "विभाग, टिप्पण्या आणि विविध डेटा प्रकारांसह मानक INI फॉर्मॅट तयार करा. तयार केलेल्या INI फाइल्स बहुतेक अनुप्रयोग आणि कॉन्फिगरेशन सिस्टमशी सुसंगत आहेत."
    from_alias: "INI कॉन्फिगरेशन"
    to_alias: "INI फॉर्मॅट"
  Avro:
    alias: "Avro स्कीमा"
    what: "Apache Avro हा डेटा सीरियलायझेशन सिस्टम आहे जो समृद्ध डेटा संरचना, संक्षिप्त बायनरी फॉर्मॅट आणि स्कीमा उत्क्रांती क्षमता प्रदान करतो. बिग डेटा प्रोसेसिंग, मेसेज क्यू आणि वितरित प्रणालींमध्ये मोठ्या प्रमाणावर वापरले जाते. त्याची स्कीमा व्याख्या जटिल डेटा प्रकार आणि आवृत्ती सुसंगततेला समर्थन देते, डेटा इंजिनियर आणि सिस्टम आर्किटेक्ट्ससाठी महत्त्वाचे साधन बनवते."
    step1: "Avro स्कीमा फाइल्स अपलोड करा किंवा डेटा पेस्ट करा. हे साधन Avro स्कीमा व्याख्या पार्स करते आणि टेबल संरचना माहिती काढते."
    step3: "डेटा प्रकार मॅपिंग, फील्ड मर्यादा आणि स्कीमा व्हॅलिडेशनसह मानक Avro स्कीमा व्याख्या तयार करा. तयार केलेले स्कीमा Hadoop इकोसिस्टम, Kafka मेसेज सिस्टम आणि इतर बिग डेटा प्लॅटफॉर्ममध्ये थेट वापरले जाऊ शकतात."
    from_alias: "Avro स्कीमा"
    to_alias: "Avro डेटा फॉर्मॅट"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) हा Google चा भाषा-तटस्थ, प्लॅटफॉर्म-तटस्थ, संरचित डेटा सीरियलायझ करण्यासाठी विस्तारणीय यंत्रणा आहे. मायक्रोसर्व्हिसेस, API विकास आणि डेटा स्टोरेजमध्ये मोठ्या प्रमाणावर वापरले जाते. त्याच्या कार्यक्षम बायनरी फॉर्मॅट आणि मजबूत टाइपिंगमुळे उच्च-कार्यक्षमता अनुप्रयोग आणि क्रॉस-भाषा कम्युनिकेशनसाठी आदर्श बनते."
    step1: ".proto फाइल्स अपलोड करा किंवा Protocol Buffer व्याख्या पेस्ट करा. हे साधन protobuf सिंटॅक्स पार्स करते आणि मेसेज संरचना माहिती काढते."
    step3: "मेसेज प्रकार, फील्ड पर्याय आणि सेवा व्याख्यांसह मानक Protocol Buffer व्याख्या तयार करा. तयार केलेल्या .proto फाइल्स अनेक प्रोग्रामिंग भाषांसाठी कंपाइल करता येतात."
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf स्कीमा"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas हा Python मधील सर्वाधिक लोकप्रिय डेटा विश्लेषण लायब्ररी आहे, DataFrame ही त्याची मुख्य डेटा संरचना आहे. हे शक्तिशाली डेटा मॅनिप्युलेशन, क्लीनिंग आणि विश्लेषण क्षमता प्रदान करते, डेटा सायन्स, मशीन लर्निंग आणि बिझनेस इंटेलिजन्समध्ये मोठ्या प्रमाणावर वापरले जाते. Python डेव्हलपर आणि डेटा विश्लेषकांसाठी अपरिहार्य साधन."
    step1: "DataFrame कोड असणाऱ्या Python फाइल्स अपलोड करा किंवा डेटा पेस्ट करा. हे साधन Pandas सिंटॅक्स पार्स करते आणि DataFrame संरचना माहिती काढते."
    step3: "डेटा प्रकार स्पेसिफिकेशन, इंडेक्स सेटिंग्ज आणि डेटा ऑपरेशन्ससह मानक Pandas DataFrame कोड तयार करा. तयार केलेला कोड डेटा विश्लेषण आणि प्रक्रियेसाठी Python एनवायरनमेंटमध्ये थेट एक्झिक्यूट करता येतो."
    from_alias: "Pandas DataFrame"
    to_alias: "Python डेटा संरचना"
  RDF:
    alias: "RDF ट्रिपल"
    what: "RDF (Resource Description Framework) हा वेबवर डेटा एक्सचेंजसाठी मानक मॉडेल आहे, संसाधनांबद्दलची माहिती ग्राफ स्वरूपात दर्शवण्यासाठी डिझाइन केलेला आहे. सिमेंटिक वेब, नॉलेज ग्राफ आणि लिंक्ड डेटा अनुप्रयोगांमध्ये मोठ्या प्रमाणावर वापरले जाते. त्याची ट्रिपल संरचना समृद्ध मेटाडेटा प्रतिनिधित्व आणि सिमेंटिक संबंध सक्षम करते."
    step1: "RDF फाइल्स अपलोड करा किंवा ट्रिपल डेटा पेस्ट करा. हे साधन RDF सिंटॅक्स पार्स करते आणि सिमेंटिक संबंध आणि संसाधन माहिती काढते."
    step3: "विविध सीरियलायझेशन (RDF/XML, Turtle, N-Triples) समर्थनासह मानक RDF फॉर्मॅट तयार करा. तयार केलेला RDF सिमेंटिक वेब अनुप्रयोग, नॉलेज बेसेस आणि लिंक्ड डेटा सिस्टममध्ये वापरला जाऊ शकतो."
    from_alias: "RDF डेटा"
    to_alias: "RDF सिमेंटिक फॉर्मॅट"
  MATLAB:
    alias: "MATLAB Array"
    what: "MATLAB हा उच्च-कार्यक्षमता संख्यात्मक संगणन आणि व्हिज्युअलायझेशन सॉफ्टवेअर आहे जो अभियांत्रिकी संगणन, डेटा विश्लेषण आणि अल्गोरिदम विकासामध्ये मोठ्या प्रमाणावर वापरला जातो. त्याच्या अॅरे आणि मॅट्रिक्स ऑपरेशन्स शक्तिशाली आहेत, जटिल गणितीय गणना आणि डेटा प्रोसेसिंगला समर्थन देतात. अभियंते, संशोधक आणि डेटा सायंटिस्ट्ससाठी आवश्यक साधन."
    step1: "MATLAB .m फाइल्स अपलोड करा किंवा अॅरे डेटा पेस्ट करा. हे साधन MATLAB सिंटॅक्स पार्स करते आणि अॅरे संरचना माहिती काढते."
    step3: "मल्टी-डायमेंशनल अॅरे, डेटा प्रकार स्पेसिफिकेशन आणि व्हेरिएबल नामकरणासह मानक MATLAB अॅरे कोड तयार करा. तयार केलेला कोड डेटा विश्लेषण आणि वैज्ञानिक संगणनासाठी MATLAB एनवायरनमेंटमध्ये थेट एक्झिक्यूट करता येतो."
    from_alias: "MATLAB Array"
    to_alias: "MATLAB कोड फॉर्मॅट"
  RDataFrame:
    alias: "R डेटाफ्रेम"
    what: "R डेटाफ्रेम ही R प्रोग्रामिंग भाषेतील मुख्य डेटा संरचना आहे, जी सांख्यिकीय विश्लेषण, डेटा मायनिंग आणि मशीन लर्निंगमध्ये मोठ्या प्रमाणावर वापरली जाते. R हे सांख्यिकीय संगणन आणि ग्राफिक्ससाठी प्रमुख साधन आहे, डेटाफ्रेम शक्तिशाली डेटा मॅनिप्युलेशन, सांख्यिकीय विश्लेषण आणि व्हिज्युअलायझेशन क्षमता प्रदान करते. संरचित डेटा विश्लेषणासह काम करणाऱ्या डेटा सायंटिस्ट, सांख्यिकीविद आणि संशोधकांसाठी अत्यावश्यक."
    step1: "R डेटा फाइल्स अपलोड करा किंवा डेटाफ्रेम कोड पेस्ट करा. हे साधन R सिंटॅक्स पार्स करते आणि कॉलम प्रकार, रो नावे आणि डेटा सामग्रीसह डेटाफ्रेम संरचना माहिती काढते."
    step3: "डेटा प्रकार स्पेसिफिकेशन, फॅक्टर पातळी, रो/कॉलम नावे आणि R-विशिष्ट डेटा संरचनांसह मानक R डेटाफ्रेम कोड तयार करा. तयार केलेला कोड सांख्यिकीय विश्लेषण आणि डेटा प्रोसेसिंगसाठी R एनवायरनमेंटमध्ये थेट एक्झिक्यूट करता येतो."
    from_alias: "R डेटाफ्रेम"
    to_alias: "R डेटाफ्रेम"
hero:
  start_converting: "रूपांतरण सुरू करा"
  start_generating: "जनरेट करणे सुरू करा"
  api_docs: "API दस्तऐवज"
related:
  section_title: 'अधिक {{ if and .from (ne .from "generator") }}{{ .from }} आणि {{ end }}{{ .to }} कन्व्हर्टर'
  section_description: '{{ if and .from (ne .from "generator") }}{{ .from }} आणि {{ end }}{{ .to }} फॉर्मॅटसाठी अधिक कन्व्हर्टर एक्सप्लोर करा. आमच्या व्यावसायिक ऑनलाइन रूपांतरण साधनांसह तुमचा डेटा अनेक फॉर्मॅटमध्ये रूपांतरित करा.'
  title: "{{ .from }} ते {{ .to }}"
howto:
  step2: "व्यावसायिक वैशिष्ट्यांसह आमचा प्रगत ऑनलाइन टेबल एडिटर वापरून डेटा संपादित करा. रिकामी पंक्ती हटवणे, डुप्लिकेट काढणे, डेटा ट्रान्सपोझिशन, सॉर्टिंग, रेजेक्स शोधा आणि बदला, आणि रिअल-टाइम पूर्वावलोकनास समर्थन देते. सर्व बदल अचूक, विश्वसनीय परिणामांसह %s फॉर्मॅटमध्ये आपोआप रूपांतरित होतात."
  section_title: "{{ . }} कसे वापरावे"
  converter_description: "आमच्या चरण-दर-चरण मार्गदर्शकासह {{ .from }} ला {{ .to }} मध्ये रूपांतरित करणे शिका. प्रगत वैशिष्ट्ये आणि रिअल-टाइम पूर्वावलोकनासह व्यावसायिक ऑनलाइन कन्व्हर्टर."
  generator_description: "आमच्या ऑनलाइन जनरेटरसह व्यावसायिक {{ .to }} टेबल तयार करणे शिका. Excel सारखे संपादन, रिअल-टाइम पूर्वावलोकन, आणि तत्काळ निर्यात क्षमता."
extension:
  section_title: "टेबल डिटेक्शन आणि एक्सट्रॅक्शन एक्सटेंशन"
  section_description: "एका क्लिकमध्ये कोणत्याही वेबसाइटवरून टेबल काढा. Excel, CSV, JSON समावेशित 30+ फॉर्मॅटमध्ये तत्काळ रूपांतरित करा - कॉपी-पेस्टिंगची आवश्यकता नाही."
  features:
    extraction_title: "वन-क्लिक टेबल एक्सट्रॅक्शन"
    extraction_description: "कॉपी-पेस्टिंग न करता कोणत्याही वेबपेजवरून तत्काळ टेबल काढा - व्यावसायिक डेटा एक्सट्रॅक्शन सोपे केले"
    formats_title: "30+ फॉर्मॅट कन्व्हर्टर समर्थन"
    formats_description: "आमच्या प्रगत टेबल कन्व्हर्टरसह काढलेले टेबल Excel, CSV, JSON, Markdown, SQL, आणि अधिकमध्ये रूपांतरित करा"
    detection_title: "स्मार्ट टेबल डिटेक्शन"
    detection_description: "जलद डेटा एक्सट्रॅक्शन आणि रूपांतरणासाठी कोणत्याही वेबपेजवरील टेबल आपोआप शोधते आणि हायलाइट करते"
  hover_tip: "✨ एक्सट्रॅक्शन आयकन पाहण्यासाठी कोणत्याही टेबलवर होवर करा"
recommendations:
  section_title: "विद्यापीठे आणि व्यावसायिकांकडून शिफारस"
  section_description: "विश्वसनीय टेबल रूपांतरण आणि डेटा प्रक्रियेसाठी विद्यापीठे, संशोधन संस्था आणि विकास संघांमधील व्यावसायिकांकडून TableConvert वर विश्वास ठेवला जातो."
  cards:
    university_title: "विस्कॉन्सिन-मॅडिसन विद्यापीठ"
    university_description: "TableConvert.com - व्यावसायिक मोफत ऑनलाइन टेबल कन्व्हर्टर आणि डेटा फॉर्मॅट साधन"
    university_link: "लेख वाचा"
    facebook_title: "डेटा व्यावसायिक समुदाय"
    facebook_description: "Facebook डेव्हलपर गटांमध्ये डेटा विश्लेषक आणि व्यावसायिकांकडून सामायिक आणि शिफारस केलेले"
    facebook_link: "पोस्ट पहा"
    twitter_title: "डेव्हलपर समुदाय"
    twitter_description: "टेबल रूपांतरणासाठी X (Twitter) वर @xiaoying_eth आणि इतर डेव्हलपरकडून शिफारस केलेले"
    twitter_link: "ट्वीट पहा"
faq:
  section_title: "वारंवार विचारले जाणारे प्रश्न"
  section_description: "आमच्या मोफत ऑनलाइन टेबल कन्व्हर्टर, डेटा फॉर्मॅट आणि रूपांतरण प्रक्रियेबद्दल सामान्य प्रश्न."
  what: "%s फॉर्मॅट काय आहे?"
  howto_convert:
    question: "{{ . }} मोफत कसे वापरावे?"
    answer: "आमचा मोफत ऑनलाइन टेबल कन्व्हर्टर वापरून तुमची {{ .from }} फाइल अपलोड करा, डेटा पेस्ट करा, किंवा वेब पेजेसमधून काढा. आमचे व्यावसायिक कन्व्हर्टर साधन रिअल-टाइम पूर्वावलोकन आणि प्रगत संपादन वैशिष्ट्यांसह तुमचा डेटा तत्काळ {{ .to }} फॉर्मॅटमध्ये रूपांतरित करते. रूपांतरित परिणाम तत्काळ डाउनलोड किंवा कॉपी करा."
  security:
    question: "हा ऑनलाइन कन्व्हर्टर वापरताना माझा डेटा सुरक्षित आहे का?"
    answer: "नक्कीच! सर्व टेबल रूपांतरणे तुमच्या ब्राउझरमध्ये स्थानिकरित्या होतात - तुमचा डेटा कधीही तुमचे डिव्हाइस सोडत नाही. आमचा ऑनलाइन कन्व्हर्टर सर्वकाही क्लायंट-साइड प्रक्रिया करतो, संपूर्ण गोपनीयता आणि डेटा सुरक्षा सुनिश्चित करतो. आमच्या सर्व्हरवर कोणत्याही फाइल संग्रहित केल्या जात नाहीत."
  free:
    question: "TableConvert खरोखर वापरण्यासाठी मोफत आहे का?"
    answer: "होय, TableConvert पूर्णपणे मोफत आहे! सर्व कन्व्हर्टर वैशिष्ट्ये, टेबल एडिटर, डेटा जनरेटर साधने आणि निर्यात पर्याय कोणत्याही खर्च, नोंदणी किंवा लपविलेल्या शुल्काशिवाय उपलब्ध आहेत. मोफत ऑनलाइन अमर्यादित फाइल रूपांतरित करा."
  filesize:
    question: "ऑनलाइन कन्व्हर्टरच्या फाइल साइज मर्यादा काय आहेत?"
    answer: "आमचा मोफत ऑनलाइन टेबल कन्व्हर्टर 10MB पर्यंतच्या फाइलना समर्थन देतो. मोठ्या फाइल, बॅच प्रक्रिया किंवा एंटरप्राइझ गरजांसाठी, उच्च मर्यादांसह आमचे ब्राउझर एक्सटेंशन किंवा व्यावसायिक API सेवा वापरा."
stats:
  conversions: "रूपांतरित टेबल"
  tables: "तयार केलेले टेबल"
  formats: "डेटा फाइल फॉर्मॅट"
  rating: "वापरकर्ता रेटिंग"
