site:
  fullname: "Table Convert"
  name: "TableConvert"
  subtitle: "Gratis Online Tabel Converter en Generator"
  intro: "TableConvert is een gratis online tabel converter en data generator tool die conversie tussen 30+ formaten ondersteunt, inclusief Excel, CSV, JSON, Markdown, LaTeX, SQL en meer."
  followTwitter: "Volg ons op X"
title:
  converter: "%s naar %s"
  generator: "%s Generator"
post:
  tags:
    converter: "Converter"
    editor: "Editor"
    generator: "Generator"
    maker: "Builder"
  converter:
    title: "Converteer %s naar %s Online"
    short: "Een gratis en krachtige %s naar %s online tool"
    intro: "Gebruiksvriendelijke online %s naar %s converter. Transformeer tabelgegevens moeiteloos met onze intuïtieve conversietool. Snel, betrouwbaar en gebruiksvriendelijk."
  generator:
    title: "Online %s Editor en Generator"
    short: "Professionele %s online generatietool met uitgebreide functies"
    intro: "Gebruiksvriendelijke online %s generator en tabeleditor. Creëer professionele datatafels moeiteloos met onze intuïtieve tool en realtime voorvertoning."
navbar:
  search:
    placeholder: "Zoek converter ..."
  sponsor: "Koop me een koffie"
  extension: "Extensie"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Gegevensbron"
    placeholder: "Plak je %s gegevens of sleep %s bestanden hierheen"
    example: "Voorbeeld"
    upload: "Bestand Uploaden"
    extract:
      enter: "Extraheren van Webpagina"
      intro: "Voer een webpagina URL in die tabelgegevens bevat om automatisch gestructureerde gegevens te extraheren"
      btn: "Extraheer %s"
    excel:
      sheet: "Werkblad"
      none: "Geen"
  tableEditor:
    title: "Online Tabeleditor"
    undo: "Ongedaan maken"
    redo: "Opnieuw"
    transpose: "Transponeren"
    clear: "Wissen"
    deleteBlank: "Lege Verwijderen"
    deleteDuplicate: "Duplicaten Verwijderen"
    uppercase: "HOOFDLETTERS"
    lowercase: "kleine letters"
    capitalize: "Hoofdletter"
    replace:
      replace: "Zoeken en Vervangen (Regex ondersteund)"
      subst: "Vervangen door..."
      btn: "Alles Vervangen"
  tableGenerator:
    title: "Tabelgenerator"
    sponsor: "Koop me een koffie"
    copy: "Kopiëren naar Klembord"
    download: "Bestand Downloaden"
    tooltip:
      html:
        escape: "Escape HTML speciale tekens (&, <, >, \", ') om weergavefouten te voorkomen"
        div: "Gebruik DIV+CSS layout in plaats van traditionele TABLE tags, beter geschikt voor responsief ontwerp"
        minify: "Verwijder witruimte en regeleinden om gecomprimeerde HTML code te genereren"
        thead: "Genereer standaard tabel hoofd (&lt;thead&gt;) en body (&lt;tbody&gt;) structuur"
        tableCaption: "Voeg beschrijvende titel toe boven de tabel (&lt;caption&gt; element)"
        tableClass: "Voeg CSS klassenaam toe aan de tabel voor eenvoudige stijlaanpassing"
        tableId: "Stel unieke ID identifier in voor de tabel voor JavaScript manipulatie"
      jira:
        escape: "Escape pipe tekens (|) om conflicten met Jira tabel syntaxis te vermijden"
      json:
        parsingJSON: "Intelligent parsen van JSON strings in cellen naar objecten"
        minify: "Genereer compact single-line JSON formaat om bestandsgrootte te verkleinen"
        format: "Selecteer output JSON datastructuur: object array, 2D array, etc."
      latex:
        escape: "Escape LaTeX speciale tekens (%, &, _, #, $, etc.) om juiste compilatie te verzekeren"
        ht: "Voeg zwevende positie parameter [!ht] toe om tabelpositie op pagina te controleren"
        mwe: "Genereer compleet LaTeX document"
        tableAlign: "Stel horizontale uitlijning van tabel op de pagina in"
        tableBorder: "Configureer tabel rand stijl: geen rand, gedeeltelijke rand, volledige rand"
        label: "Stel tabel label in voor \\ref{} commando kruisverwijzing"
        caption: "Stel tabel bijschrift in om boven of onder de tabel weer te geven"
        location: "Kies tabel bijschrift weergavepositie: boven of onder"
        tableType: "Kies tabel omgeving type: tabular, longtable, array, etc."
      markdown:
        escape: "Escape Markdown speciale tekens (*, _, |, \\, etc.) om formaat conflicten te vermijden"
        pretty: "Auto-uitlijnen kolombreedtes om mooier tabelformaat te genereren"
        simple: "Gebruik vereenvoudigde syntaxis, laat buitenste rand verticale lijnen weg"
        boldFirstRow: "Maak de eerste rij tekst vet"
        boldFirstColumn: "Maak de eerste kolom tekst vet"
        firstHeader: "Behandel eerste rij als header en voeg scheidingslijn toe"
        textAlign: "Stel kolom tekst uitlijning in: links, midden, rechts"
        multilineHandling: "Meerregelige tekst behandeling: behoud regeleinden, escape naar \\n, gebruik &lt;br&gt; tags"

        includeLineNumbers: "Voeg regelnummer kolom toe aan de linkerkant van de tabel"
      magic:
        builtin: "Selecteer voorgedefinieerde algemene sjabloon formaten"
        rowsTpl: "<table> <tr> <th>Magic Syntaxis</th> <th>Beschrijving</th> <th>Ondersteun JS Methoden</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>1e, 2e ... veld van <b>k</b>op, Aka {hA} {hB} ...</td> <td>String methoden</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>1e, 2e ... veld van huidige rij, Aka {$A} {$B} ...</td> <td>String methoden</td> </tr> <tr> <td>{F,} {F;}</td> <td>Splits de huidige rij door de string na <b>F</b></td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>Regel <b>N</b>ummer van huidige <b>R</b>ij vanaf 1 of 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>E</b>ind regel <b>N</b>ummer van <b>R</b>ijen </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>U<b>i</b>tvoeren JavaScript code, bijv.: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Gebruik backslash <b>\\</b> om accolades {...} uit te voeren </td> <td></td> </tr></table>"
        headerTpl: "Aangepaste output sjabloon voor header sectie"
        footerTpl: "Aangepaste output sjabloon voor footer sectie"
      textile:
        escape: "Escape Textile syntaxis tekens (|, ., -, ^) om formaat conflicten te vermijden"
        rowHeader: "Stel eerste rij in als header rij"
        thead: "Voeg Textile syntaxis markeringen toe voor tabel hoofd en body"
      xml:
        escape: "Escape XML speciale tekens (&lt;, &gt;, &amp;, \", ') om geldige XML te verzekeren"
        minify: "Genereer gecomprimeerde XML output, verwijder extra witruimte"
        rootElement: "Stel XML root element tag naam in"
        rowElement: "Stel XML element tag naam in voor elke rij data"
        declaration: "Voeg XML declaratie header toe (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Output data als XML attributen in plaats van child elementen"
        cdata: "Wikkel tekstinhoud met CDATA om speciale tekens te beschermen"
        encoding: "Stel karakter codering formaat in voor XML document"
        indentation: "Kies XML inspringing teken: spaties of tabs"
      yaml:
        indentSize: "Stel aantal spaties in voor YAML hiërarchie inspringing (meestal 2 of 4)"
        arrayStyle: "Array formaat: blok (één item per regel) of flow (inline formaat)"
        quotationStyle: "String aanhalingsteken stijl: geen aanhalingstekens, enkele aanhalingstekens, dubbele aanhalingstekens"
      csv:
        bom: "Voeg UTF-8 byte order mark toe om Excel en andere software te helpen codering te herkennen"
      excel:
        autoWidth: "Automatisch kolombreedte aanpassen op basis van inhoud"
        protectSheet: "Schakel werkblad bescherming in met wachtwoord: tableconvert.com"
      sql:
        primaryKey: "Specificeer primaire sleutel veldnaam voor CREATE TABLE statement"
        dialect: "Selecteer database type, beïnvloedt aanhalingsteken en datatype syntaxis"
      ascii:
        forceSep: "Forceer scheidingslijnen tussen elke rij data"
        style: "Selecteer ASCII tabel rand tekenstijl"
        comment: "Voeg commentaar markeringen toe om de hele tabel in te pakken"
      mediawiki:
        minify: "Comprimeer output code, verwijder extra witruimte"
        header: "Markeer eerste rij als header stijl"
        sort: "Schakel tabel klik sorteer functionaliteit in"
      asciidoc:
        minify: "Comprimeer AsciiDoc formaat output"
        firstHeader: "Stel eerste rij in als header rij"
        lastFooter: "Stel laatste rij in als footer rij"
        title: "Voeg titel tekst toe aan de tabel"
      tracwiki:
        rowHeader: "Stel eerste rij in als header"
        colHeader: "Stel eerste kolom in als header"
      bbcode:
        minify: "Comprimeer BBCode output formaat"
      restructuredtext:
        style: "Selecteer reStructuredText tabel rand stijl"
        forceSep: "Forceer scheidingslijnen"
      pdf:
        theme: "Selecteer PDF tabel visuele stijl voor professionele documenten"
        headerColor: "Kies header achtergrondkleur voor PDF tabellen"
        showHead: "Controleer header weergave over PDF pagina's"
        docTitle: "Optionele titel voor het PDF document"
        docDescription: "Optionele beschrijving tekst voor PDF document"
    label:
      ascii:
        forceSep: "Rij Scheidingstekens"
        style: "Rand Stijl"
        comment: "Commentaar Wrapper"
      restructuredtext:
        style: "Rand Stijl"
        forceSep: "Forceer Scheidingstekens"
      bbcode:
        minify: "Minifieer Output"
      csv:
        doubleQuote: "Dubbele Aanhalingsteken Wrap"
        delimiter: "Veld Scheidingsteken"
        bom: "UTF-8 BOM"
        valueDelimiter: "Waarde Scheidingsteken"
        rowDelimiter: "Rij Scheidingsteken"
        prefix: "Rij Voorvoegsel"
        suffix: "Rij Achtervoegsel"
      excel:
        autoWidth: "Auto Breedte"
        textFormat: "Tekst Formaat"
        protectSheet: "Bescherm Blad"
        boldFirstRow: "Vet Eerste Rij"
        boldFirstColumn: "Vet Eerste Kolom"
        sheetName: "Blad Naam"
      html:
        escape: "Escape HTML Tekens"
        div: "DIV Tabel"
        minify: "Minifieer Code"
        thead: "Tabel Hoofd Structuur"
        tableCaption: "Tabel Bijschrift"
        tableClass: "Tabel Klasse"
        tableId: "Tabel ID"
        rowHeader: "Rij Header"
        colHeader: "Kolom Header"
      jira:
        escape: "Escape Tekens"
        rowHeader: "Rij Header"
        colHeader: "Kolom Header"
      json:
        parsingJSON: "Parse JSON"
        minify: "Minifieer Output"
        format: "Data Formaat"
        rootName: "Root Object Naam"
        indentSize: "Inspringing Grootte"
      jsonlines:
        parsingJSON: "Parse JSON"
        format: "Data Formaat"
      latex:
        escape: "Escape LaTeX Tabel Tekens"
        ht: "Zwevende Positie"
        mwe: "Compleet Document"
        tableAlign: "Tabel Uitlijning"
        tableBorder: "Rand Stijl"
        label: "Referentie Label"
        caption: "Tabel Bijschrift"
        location: "Bijschrift Positie"
        tableType: "Tabel Type"
        boldFirstRow: "Vet Eerste Rij"
        boldFirstColumn: "Vet Eerste Kolom"
        textAlign: "Tekst Uitlijning"
        borders: "Rand Instellingen"
      markdown:
        escape: "Escape Tekens"
        pretty: "Mooie Markdown Tabel"
        simple: "Eenvoudig Markdown Formaat"
        boldFirstRow: "Vet Eerste Rij"
        boldFirstColumn: "Vet Eerste Kolom"
        firstHeader: "Eerste Header"
        textAlign: "Tekst Uitlijning"
        multilineHandling: "Meerregelige Behandeling"

        includeLineNumbers: "Voeg Regelnummers Toe"
        align: "Uitlijning"
      mediawiki:
        minify: "Minifieer Code"
        header: "Header Markup"
        sort: "Sorteerbaar"
      asciidoc:
        minify: "Minifieer Formaat"
        firstHeader: "Eerste Header"
        lastFooter: "Laatste Footer"
        title: "Tabel Titel"
      tracwiki:
        rowHeader: "Rij Header"
        colHeader: "Kolom Header"
      sql:
        drop: "Drop Tabel (Indien Bestaat)"
        create: "Maak Tabel"
        oneInsert: "Batch Invoegen"
        table: "Tabel Naam"
        dialect: "Database Type"
        primaryKey: "Primaire Sleutel"
      magic:
        builtin: "Ingebouwde Sjabloon"
        rowsTpl: "Rij Sjabloon, Syntaxis ->"
        headerTpl: "Header Sjabloon"
        footerTpl: "Footer Sjabloon"
      textile:
        escape: "Escape Tekens"
        rowHeader: "Rij Header"
        thead: "Tabel Hoofd Syntaxis"
      xml:
        escape: "Escape XML Tekens"
        minify: "Minifieer Output"
        rootElement: "Root Element"
        rowElement: "Rij Element"
        declaration: "XML Declaratie"
        attributes: "Attribuut Modus"
        cdata: "CDATA Wrapper"
        encoding: "Codering"
        indentSize: "Inspringing Grootte"
      yaml:
        indentSize: "Inspringing Grootte"
        arrayStyle: "Array Stijl"
        quotationStyle: "Aanhalingsteken Stijl"
      pdf:
        theme: "PDF Tabel Thema"
        headerColor: "PDF Header Kleur"
        showHead: "PDF Header Weergave"
        docTitle: "PDF Document Titel"
        docDescription: "PDF Document Beschrijving"

sidebar:
  all: "Alle Conversietools"
  dataSource:
    title: "Gegevensbron"
    description:
      converter: "Importeer %s voor conversie naar %s. Ondersteunt bestand upload, online bewerking en web data extractie."
      generator: "Creëer tabelgegevens met ondersteuning voor meerdere invoermethoden inclusief handmatige invoer, bestand import en sjabloon generatie."
  tableEditor:
    title: "Online Tabeleditor"
    description:
      converter: "Verwerk %s online met onze tabeleditor. Excel-achtige bedieningservaring met ondersteuning voor het verwijderen van lege rijen, deduplicatie, sorteren en zoeken en vervangen."
      generator: "Krachtige online tabeleditor die Excel-achtige bedieningservaring biedt. Ondersteunt het verwijderen van lege rijen, deduplicatie, sorteren en zoeken en vervangen."
  tableGenerator:
    title: "Tabelgenerator"
    description:
      converter: "Genereer snel %s met realtime voorvertoning van tabelgenerator. Rijke export opties, één-klik kopiëren en downloaden."
      generator: "Exporteer %s gegevens in meerdere formaten om verschillende gebruiksscenario's te ondersteunen. Ondersteunt aangepaste opties en realtime voorvertoning."
footer:
  changelog: "Wijzigingslog"
  sponsor: "Sponsors"
  contact: "Contact Ons"
  privacyPolicy: "Privacybeleid"
  about: "Over"
  resources: "Bronnen"
  popularConverters: "Populaire Converters"
  popularGenerators: "Populaire Generators"
  dataSecurity: "Je gegevens zijn veilig - alle conversies draaien in je browser."
converters:
  Markdown:
    alias: "Markdown Tabel"
    what: "Markdown is een lichtgewicht opmaaktaal die veel wordt gebruikt voor technische documentatie, blog content creatie en webontwikkeling. De tabelsyntaxis is beknopt en intuïtief, ondersteunt tekstuitlijning, link inbedding en opmaak. Het is het favoriete hulpmiddel voor programmeurs en technische schrijvers, perfect compatibel met GitHub, GitLab en andere code hosting platforms."
    step1: "Plak Markdown tabelgegevens in het gegevensbron gebied, of sleep .md bestanden direct voor upload. Het hulpmiddel parseert automatisch tabelstructuur en opmaak, ondersteunt complexe geneste inhoud en speciale tekens behandeling."
    step3: "Genereer standaard Markdown tabelcode in realtime, ondersteunt meerdere uitlijningsmethoden, tekst vetdruk, regelnummer toevoeging en andere geavanceerde formaat instellingen. De gegenereerde code is volledig compatibel met GitHub en grote Markdown editors, klaar voor gebruik met één-klik kopiëren."
    from_alias: "Markdown Tabel Bestand"
    to_alias: "Markdown Tabel Formaat"
  Magic:
    alias: "Aangepaste Sjabloon"
    what: "Magic sjabloon is een unieke geavanceerde data generator van dit hulpmiddel, waarmee gebruikers willekeurige formaat data output kunnen creëren via aangepaste sjabloon syntaxis. Ondersteunt variabele vervanging, voorwaardelijke beoordeling en lus verwerking. Het is de ultieme oplossing voor het afhandelen van complexe data conversie behoeften en gepersonaliseerde output formaten, vooral geschikt voor ontwikkelaars en data engineers."
    step1: "Selecteer ingebouwde algemene sjablonen of creëer aangepaste sjabloon syntaxis. Ondersteunt rijke variabelen en functies die complexe datastructuren en bedrijfslogica kunnen afhandelen."
    step3: "Genereer data output die volledig voldoet aan aangepaste formaat vereisten. Ondersteunt complexe data conversie logica en voorwaardelijke verwerking, verbetert aanzienlijk data verwerkingsefficiëntie en output kwaliteit. Een krachtig hulpmiddel voor batch data verwerking."
    from_alias: "Tabel Data"
    to_alias: "Aangepaste Formaat Output"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) is het meest gebruikte data uitwisselingsformaat, perfect ondersteund door Excel, Google Sheets, database systemen en verschillende data analyse hulpmiddelen. De eenvoudige structuur en sterke compatibiliteit maken het het standaard formaat voor data migratie, batch import/export en cross-platform data uitwisseling, veel gebruikt in bedrijfsanalyse, data wetenschap en systeem integratie."
    step1: "Upload CSV bestanden of plak CSV data direct. Het hulpmiddel herkent intelligent verschillende scheidingstekens (komma, tab, puntkomma, pipe, etc.), detecteert automatisch data types en codering formaten, ondersteunt snelle parsing van grote bestanden en complexe data structuren."
    step3: "Genereer standaard CSV formaat bestanden met ondersteuning voor aangepaste scheidingstekens, aanhalingsteken stijlen, codering formaten en BOM markering instellingen. Zorgt voor perfecte compatibiliteit met doelsystemen, biedt download en compressie opties om enterprise-niveau data verwerkingsbehoeften te voldoen."
    from_alias: "CSV Data Bestand"
    to_alias: "CSV Standaard Formaat"
  JSON:
    alias: "JSON Array"
    what: "JSON (JavaScript Object Notation) is het standaard tabel data formaat voor moderne web applicaties, REST API's en microservice architecturen. De heldere structuur en efficiënte parsing maken het veel gebruikt in front-end en back-end data interactie, configuratie bestand opslag en NoSQL databases. Ondersteunt geneste objecten, array structuren en meerdere data types, waardoor het onmisbare tabel data is voor moderne software ontwikkeling."
    step1: "Upload JSON bestanden of plak JSON arrays. Ondersteunt automatische herkenning en parsing van object arrays, geneste structuren en complexe data types. Het hulpmiddel valideert intelligent JSON syntaxis en biedt fout prompts."
    step3: "Genereer meerdere JSON formaat outputs: standaard object arrays, 2D arrays, kolom arrays en sleutel-waarde paar formaten. Ondersteunt verfraaide output, compressie modus, aangepaste root object namen en inspringing instellingen, past perfect aan verschillende API interfaces en data opslag behoeften."
    from_alias: "JSON Array Bestand"
    to_alias: "JSON Standaard Formaat"
  JSONLines:
    alias: "JSONLines Formaat"
    what: "JSON Lines (ook bekend als NDJSON) is een belangrijk formaat voor big data verwerking en streaming data transmissie, met elke regel bevattend een onafhankelijk JSON object. Veel gebruikt in log analyse, data stream verwerking, machine learning en gedistribueerde systemen. Ondersteunt incrementele verwerking en parallelle computing, waardoor het de ideale keuze is voor het afhandelen van grootschalige gestructureerde data."
    step1: "Upload JSONLines bestanden of plak data. Het hulpmiddel parseert JSON objecten regel voor regel, ondersteunt grote bestand streaming verwerking en fout regel overslaan functionaliteit."
    step3: "Genereer standaard JSONLines formaat met elke regel outputtend een compleet JSON object. Geschikt voor streaming verwerking, batch import en big data analyse scenario's, ondersteunt data validatie en formaat optimalisatie."
    from_alias: "JSONLines Data"
    to_alias: "JSONLines Streaming Formaat"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) is het standaard formaat voor enterprise-niveau data uitwisseling en configuratie beheer, met strikte syntaxis specificaties en krachtige validatie mechanismen. Veel gebruikt in web services, configuratie bestanden, document opslag en systeem integratie. Ondersteunt namespaces, schema validatie en XSLT transformatie, waardoor het belangrijke tabel data is voor enterprise applicaties."
    step1: "Upload XML bestanden of plak XML data. Het hulpmiddel parseert automatisch XML structuur en converteert het naar tabel formaat, ondersteunt namespace, attribuut behandeling en complexe geneste structuren."
    step3: "Genereer XML output die voldoet aan XML standaarden. Ondersteunt aangepaste root elementen, rij element namen, attribuut modi, CDATA wrapping en karakter codering instellingen. Zorgt voor data integriteit en compatibiliteit, voldoet aan enterprise-niveau applicatie vereisten."
    from_alias: "XML Data Bestand"
    to_alias: "XML Standaard Formaat"
  YAML:
    alias: "YAML Configuratie"
    what: "YAML is een mensenvriendelijke data serialisatie standaard, beroemd om zijn heldere hiërarchische structuur en beknopte syntaxis. Veel gebruikt in configuratie bestanden, DevOps tool chains, Docker Compose en Kubernetes deployment. De sterke leesbaarheid en beknopte syntaxis maken het een belangrijk configuratie formaat voor moderne cloud-native applicaties en geautomatiseerde operaties."
    step1: "Upload YAML bestanden of plak YAML data. Het hulpmiddel parseert intelligent YAML structuur en valideert syntaxis correctheid, ondersteunt multi-document formaten en complexe data types."
    step3: "Genereer standaard YAML formaat output met ondersteuning voor blok en flow array stijlen, meerdere aanhalingsteken instellingen, aangepaste inspringing en commentaar behoud. Zorgt ervoor dat output YAML bestanden volledig compatibel zijn met verschillende parsers en configuratie systemen."
    from_alias: "YAML Configuratie Bestand"
    to_alias: "YAML Standaard Formaat"
  MySQL:
      alias: "MySQL Query Resultaten"
      what: "MySQL is 's werelds populairste open-source relationele database management systeem, beroemd om zijn hoge prestaties, betrouwbaarheid en gebruiksgemak. Veel gebruikt in web applicaties, enterprise systemen en data analyse platforms. MySQL query resultaten bevatten typisch gestructureerde tabel data, dienend als een belangrijke data bron in database beheer en data analyse werk."
      step1: "Plak MySQL query output resultaten in het gegevensbron gebied. Het hulpmiddel herkent automatisch en parseert MySQL commandoregel output formaat, ondersteunt verschillende query resultaat stijlen en karakter coderingen, behandelt intelligent headers en data rijen."
      step3: "Converteer snel MySQL query resultaten naar meerdere tabel data formaten, faciliteert data analyse, rapport generatie, cross-systeem data migratie en data validatie. Een praktisch hulpmiddel voor database beheerders en data analisten."
      from_alias: "MySQL Query Output"
      to_alias: "MySQL Tabel Data"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) is de standaard operatie taal voor relationele databases, gebruikt voor data query, insert, update en delete operaties. Als de kerntechnologie van database beheer, wordt SQL veel gebruikt in data analyse, business intelligence, ETL verwerking en data warehouse constructie. Het is een essentieel vaardigheids hulpmiddel voor data professionals."
    step1: "Plak INSERT SQL statements of upload .sql bestanden. Het hulpmiddel parseert intelligent SQL syntaxis en extraheert tabel data, ondersteunt meerdere SQL dialecten en complexe query statement verwerking."
    step3: "Genereer standaard SQL INSERT statements en tabel creatie statements. Ondersteunt meerdere database dialecten (MySQL, PostgreSQL, SQLite, SQL Server, Oracle), behandelt automatisch data type mapping, karakter escaping en primaire sleutel beperkingen. Zorgt ervoor dat gegenereerde SQL code direct uitgevoerd kan worden."
    from_alias: "Insert SQL"
    to_alias: "SQL Statement"
  Qlik:
      alias: "Qlik Tabel"
      what: "Qlik is een software leverancier gespecialiseerd in data visualisatie, executive dashboards en self-service business intelligence producten, samen met Tableau en Microsoft."
      step1: ""
      step3: "Ten slotte toont de [Tabel Generator](#TableGenerator) de conversie resultaten. Gebruik in je Qlik Sense, Qlik AutoML, QlikView of andere Qlik-enabled software."
      from_alias: "Qlik Tabel"
      to_alias: "Qlik Tabel"
  DAX:
      alias: "DAX Tabel"
      what: "DAX (Data Analysis Expressions) is een programmeertaal gebruikt door Microsoft Power BI voor het creëren van berekende kolommen, metingen en aangepaste tabellen."
      step1: ""
      step3: "Ten slotte toont de [Tabel Generator](#TableGenerator) de conversie resultaten. Zoals verwacht wordt het gebruikt in verschillende Microsoft producten inclusief Microsoft Power BI, Microsoft Analysis Services en Microsoft Power Pivot voor Excel."
      from_alias: "DAX Tabel"
      to_alias: "DAX Tabel"
  Firebase:
    alias: "Firebase Lijst"
    what: "Firebase is een BaaS applicatie ontwikkelingsplatform dat gehoste backend services biedt zoals realtime database, cloud opslag, authenticatie, crash rapportage, etc."
    step1: ""
    step3: "Ten slotte toont de [Tabel Generator](#TableGenerator) de conversie resultaten. Je kunt dan de push methode in de Firebase API gebruiken om toe te voegen aan een lijst van data in de Firebase database."
    from_alias: "Firebase Lijst"
    to_alias: "Firebase Lijst"
  HTML:
    alias: "HTML Tabel"
    what: "HTML tabellen zijn de standaard manier om gestructureerde data weer te geven in webpagina's, gebouwd met table, tr, td en andere tags. Ondersteunt rijke stijl aanpassing, responsieve layout en interactieve functionaliteit. Veel gebruikt in website ontwikkeling, data weergave en rapport generatie, dienend als een belangrijke component van front-end ontwikkeling en web design."
    step1: "Plak HTML code bevattend tabellen of upload HTML bestanden. Het hulpmiddel herkent automatisch en extraheert tabel data van pagina's, ondersteunt complexe HTML structuren, CSS stijlen en geneste tabel verwerking."
    step3: "Genereer semantische HTML tabel code met ondersteuning voor thead/tbody structuur, CSS klasse instellingen, tabel bijschriften, rij/kolom headers en responsieve attribuut configuratie. Zorgt ervoor dat gegenereerde tabel code voldoet aan web standaarden met goede toegankelijkheid en SEO vriendelijkheid."
    from_alias: "HTML Tabel"
    to_alias: "HTML Tabel"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel is 's werelds populairste spreadsheet software, veel gebruikt in bedrijfsanalyse, financieel beheer, dataverwerking en rapportcreatie. De krachtige dataverwerkingsmogelijkheden, rijke functiebibliotheek en flexibele visualisatiefuncties maken het het standaard hulpmiddel voor kantoorautomatisering en data-analyse, met uitgebreide toepassingen in bijna alle industrieën en gebieden."
    step1: "Upload Excel bestanden (ondersteunt .xlsx, .xls formaten) of kopieer tabelgegevens direct uit Excel en plak. Het hulpmiddel ondersteunt multi-werkblad verwerking, complexe formaat herkenning en snelle parsing van grote bestanden, behandelt automatisch samengevoegde cellen en data types."
    step3: "Genereer Excel-compatibele tabelgegevens die direct in Excel geplakt kunnen worden of gedownload als standaard .xlsx bestanden. Ondersteunt werkblad naamgeving, cel opmaak, auto kolombreedte, header styling en data validatie instellingen. Zorgt ervoor dat output Excel bestanden professioneel uiterlijk en complete functionaliteit hebben."
    from_alias: "Excel Spreadsheet"
    to_alias: "Excel"
  LaTeX:
    alias: "LaTeX Tabel"
    what: "LaTeX is een professioneel document typesetting systeem, vooral geschikt voor het creëren van academische papers, technische documenten en wetenschappelijke publicaties. De tabel functionaliteit is krachtig, ondersteunt complexe wiskundige formules, precieze layout controle en hoge kwaliteit PDF output. Het is het standaard hulpmiddel in de academische wereld en wetenschappelijke publicatie, veel gebruikt in tijdschrift papers, dissertaties en technische handleiding typesetting."
    step1: "Plak LaTeX tabel code of upload .tex bestanden. Het hulpmiddel parseert LaTeX tabel syntaxis en extraheert data inhoud, ondersteunt meerdere tabel omgevingen (tabular, longtable, array, etc.) en complexe formaat commando's."
    step3: "Genereer professionele LaTeX tabel code met ondersteuning voor meerdere tabel omgeving selectie, rand stijl configuratie, bijschrift positie instellingen, document klasse specificatie en pakket beheer. Kan complete compileerbare LaTeX documenten genereren, zorgt ervoor dat output tabellen voldoen aan academische publicatie standaarden."
    from_alias: "LaTeX Tabel"
    to_alias: "LaTeX Tabel"
  ASCII:
    alias: "ASCII Tekst Tabel"
    what: "ASCII tabellen gebruiken gewone tekst karakters om tabel randen en structuren te tekenen, bieden de beste compatibiliteit en portabiliteit. Compatibel met alle tekst editors, terminal omgevingen en besturingssystemen. Veel gebruikt in code documentatie, technische handleidingen, README bestanden en commandoregel hulpmiddel output. Het voorkeursformaat voor data weergave voor programmeurs en systeembeheerders."
    step1: "Upload tekst bestanden bevattend ASCII tabellen of plak tabelgegevens direct. Het hulpmiddel herkent intelligent en parseert ASCII tabel structuren, ondersteunt meerdere rand stijlen en uitlijning formaten."
    step3: "Genereer mooie gewone tekst ASCII tabellen met ondersteuning voor meerdere rand stijlen (enkele lijn, dubbele lijn, afgeronde hoeken, etc.), tekst uitlijning methoden en auto kolombreedte. Gegenereerde tabellen worden perfect weergegeven in code editors, documenten en commandoregels."
    from_alias: "ASCII Tekst Tabel"
    to_alias: "ASCII Tekst Tabel"
  MediaWiki:
    alias: "MediaWiki Tabel"
    what: "MediaWiki is het open-source software platform gebruikt door beroemde wiki sites zoals Wikipedia. De tabel syntaxis is beknopt maar krachtig, ondersteunt tabel stijl aanpassing, sorteer functionaliteit en link inbedding. Veel gebruikt in kennisbeheer, collaboratieve bewerking en content management systemen, dienend als kerntechnologie voor het bouwen van wiki encyclopedieën en kennisbanken."
    step1: "Plak MediaWiki tabel code of upload wiki bron bestanden. Het hulpmiddel parseert wiki markup syntaxis en extraheert tabelgegevens, ondersteunt complexe wiki syntaxis en sjabloon verwerking."
    step3: "Genereer standaard MediaWiki tabel code met ondersteuning voor header stijl instellingen, cel uitlijning, sorteer functionaliteit inschakeling en code compressie opties. Gegenereerde code kan direct gebruikt worden voor wiki pagina bewerking, zorgt voor perfecte weergave op MediaWiki platforms."
    from_alias: "MediaWiki Tabel"
    to_alias: "MediaWiki Tabel"
  TracWiki:
    alias: "TracWiki Tabel"
    what: "Trac is een web-gebaseerd projectbeheer en bug tracking systeem dat vereenvoudigde wiki syntaxis gebruikt om tabel inhoud te creëren."
    step1: "Upload TracWiki bestanden of plak tabelgegevens."
    step3: "Genereer TracWiki-compatibele tabel code met ondersteuning voor rij/kolom header instellingen, faciliteert project document beheer."
    from_alias: "TracWiki Tabel"
    to_alias: "TracWiki Tabel"
  AsciiDoc:
    alias: "AsciiDoc Tabel"
    what: "AsciiDoc is een lichtgewicht markup taal die geconverteerd kan worden naar HTML, PDF, handleiding pagina's en andere formaten, veel gebruikt voor technische documentatie schrijven."
    step1: "Upload AsciiDoc bestanden of plak data."
    step3: "Genereer AsciiDoc tabel syntaxis met ondersteuning voor header, footer en titel instellingen, direct bruikbaar in AsciiDoc editors."
    from_alias: "AsciiDoc Tabel"
    to_alias: "AsciiDoc Tabel"
  reStructuredText:
    alias: "reStructuredText Tabel"
    what: "reStructuredText is het standaard documentatie formaat voor de Python gemeenschap, ondersteunt rijke tabel syntaxis, veel gebruikt voor Sphinx documentatie generatie."
    step1: "Upload .rst bestanden of plak reStructuredText data."
    step3: "Genereer standaard reStructuredText tabellen met ondersteuning voor meerdere rand stijlen, direct bruikbaar in Sphinx documentatie projecten."
    from_alias: "reStructuredText Tabel"
    to_alias: "reStructuredText Tabel"
  PHP:
    alias: "PHP Array"
    what: "PHP is een populaire server-side scripting taal, met arrays als kerngegevensstructuur, veel gebruikt in webontwikkeling en dataverwerking."
    step1: "Upload bestanden bevattend PHP arrays of plak data direct."
    step3: "Genereer standaard PHP array code die direct gebruikt kan worden in PHP projecten, ondersteunt associatieve en geïndexeerde array formaten."
    from_alias: "PHP Array"
    to_alias: "PHP Code"
  Ruby:
    alias: "Ruby Array"
    what: "Ruby is een dynamische object-georiënteerde programmeertaal met beknopte en elegante syntaxis, met arrays als belangrijke gegevensstructuur."
    step1: "Upload Ruby bestanden of plak array data."
    step3: "Genereer Ruby array code die voldoet aan Ruby syntaxis specificaties, direct bruikbaar in Ruby projecten."
    from_alias: "Ruby Array"
    to_alias: "Ruby Code"
  ASP:
    alias: "ASP Array"
    what: "ASP (Active Server Pages) is Microsoft's server-side scripting omgeving, ondersteunt meerdere programmeertalen voor het ontwikkelen van dynamische webpagina's."
    step1: "Upload ASP bestanden of plak array data."
    step3: "Genereer ASP-compatibele array code met ondersteuning voor VBScript en JScript syntaxis, bruikbaar in ASP.NET projecten."
    from_alias: "ASP Array"
    to_alias: "ASP Code"
  ActionScript:
    alias: "ActionScript Array"
    what: "ActionScript is een object-georiënteerde programmeertaal primair gebruikt voor Adobe Flash en AIR applicatie ontwikkeling."
    step1: "Upload .as bestanden of plak ActionScript data."
    step3: "Genereer ActionScript array code die voldoet aan AS3 syntaxis standaarden, bruikbaar voor Flash en Flex project ontwikkeling."
    from_alias: "ActionScript Array"
    to_alias: "ActionScript Code"
  BBCode:
    alias: "BBCode Tabel"
    what: "BBCode is een lichtgewicht markup taal veel gebruikt in forums en online gemeenschappen, biedt eenvoudige opmaak functionaliteit inclusief tabel ondersteuning."
    step1: "Upload bestanden bevattend BBCode of plak data."
    step3: "Genereer BBCode tabel code geschikt voor forum posting en gemeenschap content creatie, met ondersteuning voor gecomprimeerd output formaat."
    from_alias: "BBCode Tabel"
    to_alias: "BBCode Tabel"
  PDF:
    alias: "PDF Tabel"
    what: "PDF (Portable Document Format) is een cross-platform document standaard met vaste layout, consistente weergave en hoge kwaliteit print karakteristieken. Veel gebruikt in formele documenten, rapporten, facturen, contracten en academische papers. Het voorkeursformaat voor zakelijke communicatie en document archivering, zorgt voor volledig consistente visuele effecten over verschillende apparaten en besturingssystemen."
    step1: "Importeer tabelgegevens in elk formaat. Het hulpmiddel analyseert automatisch gegevensstructuur en voert intelligent layout ontwerp uit, ondersteunt grote tabel auto-paginering en complexe gegevenstype verwerking."
    step3: "Genereer hoge kwaliteit PDF tabel bestanden met ondersteuning voor meerdere professionele thema stijlen (zakelijk, academisch, minimalistisch, etc.), meertalige lettertypen, auto-paginering, watermerk toevoeging en print optimalisatie. Zorgt ervoor dat output PDF documenten professioneel uiterlijk hebben, direct bruikbaar voor zakelijke presentaties en formele publicatie."
    from_alias: "Tabel Data"
    to_alias: "PDF Tabel"
  JPEG:
    alias: "JPEG Afbeelding"
    what: "JPEG is het meest gebruikte digitale afbeeldingsformaat met uitstekende compressie effecten en brede compatibiliteit. De kleine bestandsgrootte en snelle laadsnelheid maken het geschikt voor web weergave, sociale media delen, document illustraties en online presentaties. Het standaard afbeeldingsformaat voor digitale media en netwerk communicatie, perfect ondersteund door bijna alle apparaten en software."
    step1: "Importeer tabelgegevens in elk formaat. Het hulpmiddel voert intelligent layout ontwerp en visuele optimalisatie uit, berekent automatisch optimale grootte en resolutie."
    step3: "Genereer hoge definitie JPEG tabel afbeeldingen met ondersteuning voor meerdere thema kleurenschema's (licht, donker, oogvriendelijk, etc.), adaptieve layout, tekst helderheid optimalisatie en grootte aanpassing. Geschikt voor online delen, document invoeging en presentatie gebruik, zorgt voor uitstekende visuele effecten op verschillende weergave apparaten."
    from_alias: "Tabel Data"
    to_alias: "JPEG Afbeelding"
  Jira:
    alias: "Jira Tabel"
    what: "JIRA is professionele projectbeheer en bug tracking software ontwikkeld door Atlassian, veel gebruikt in agile ontwikkeling, software testen en project samenwerking. De tabel functionaliteit ondersteunt rijke opmaak opties en data weergave, dienend als belangrijk hulpmiddel voor software ontwikkelingsteams, projectmanagers en kwaliteitsborging personeel in requirement management, bug tracking en voortgang rapportage."
    step1: "Upload bestanden bevattend tabelgegevens of plak data inhoud direct. Het hulpmiddel verwerkt automatisch tabelgegevens en speciale karakter escaping."
    step3: "Genereer JIRA platform-compatibele tabel code met ondersteuning voor header stijl instellingen, cel uitlijning, karakter escape verwerking en formaat optimalisatie. Gegenereerde code kan direct geplakt worden in JIRA issue beschrijvingen, commentaren of wiki pagina's, zorgt voor correcte weergave en rendering in JIRA systemen."
    from_alias: "Jira Tabel"
    to_alias: "Jira Tabel"
  Textile:
    alias: "Textile Tabel"
    what: "Textile is een beknopte lichtgewicht markup taal met eenvoudige en makkelijk te leren syntaxis, veel gebruikt in content management systemen, blog platforms en forum systemen. De tabel syntaxis is helder en intuïtief, ondersteunt snelle opmaak en stijl instellingen. Een ideaal hulpmiddel voor content creators en website beheerders voor snelle document schrijven en content publicatie."
    step1: "Upload Textile formaat bestanden of plak tabelgegevens. Het hulpmiddel parseert Textile markup syntaxis en extraheert tabel inhoud."
    step3: "Genereer standaard Textile tabel syntaxis met ondersteuning voor header markup, cel uitlijning, speciale karakter escaping en formaat optimalisatie. Gegenereerde code kan direct gebruikt worden in CMS systemen, blog platforms en document systemen die Textile ondersteunen, zorgt voor correcte inhoud rendering en weergave."
    from_alias: "Textile Tabel"
    to_alias: "Textile Tabel"
  PNG:
    alias: "PNG Afbeelding"
    what: "PNG (Portable Network Graphics) is een verliesvrij afbeeldingsformaat met uitstekende compressie en transparantie ondersteuning. Veel gebruikt in web design, digitale grafiek en professionele fotografie. De hoge kwaliteit en brede compatibiliteit maken het ideaal voor screenshots, logo's, diagrammen en alle afbeeldingen die scherpe details en transparante achtergronden vereisen."
    step1: "Importeer tabelgegevens in elk formaat. Het hulpmiddel voert intelligent layout ontwerp en visuele optimalisatie uit, berekent automatisch optimale grootte en resolutie voor PNG output."
    step3: "Genereer hoge kwaliteit PNG tabel afbeeldingen met ondersteuning voor meerdere thema kleurenschema's, transparante achtergronden, adaptieve layout en tekst helderheid optimalisatie. Perfect voor web gebruik, document invoeging en professionele presentaties met uitstekende visuele kwaliteit."
    from_alias: ""
    to_alias: "PNG Afbeelding"
  TOML:
    alias: "TOML"
    what: "TOML (Tom's Obvious, Minimal Language) is een configuratie bestandsformaat dat makkelijk te lezen en schrijven is. Ontworpen om ondubbelzinnig en eenvoudig te zijn, wordt het veel gebruikt in moderne software projecten voor configuratie beheer. De heldere syntaxis en sterke typing maken het een uitstekende keuze voor applicatie instellingen en project configuratie bestanden."
    step1: "Upload TOML bestanden of plak configuratie data. Het hulpmiddel parseert TOML syntaxis en extraheert gestructureerde configuratie informatie."
    step3: "Genereer standaard TOML formaat met ondersteuning voor geneste structuren, data types en commentaren. Gegenereerde TOML bestanden zijn perfect voor applicatie configuratie, build tools en project instellingen."
    from_alias: "TOML"
    to_alias: "TOML Formaat"
  INI:
    alias: "INI"
    what: "INI bestanden zijn eenvoudige configuratie bestanden gebruikt door veel applicaties en besturingssystemen. Hun rechttoe rechtaan sleutel-waarde paar structuur maakt ze makkelijk handmatig te lezen en bewerken. Veel gebruikt in Windows applicaties, legacy systemen en eenvoudige configuratie scenario's waar menselijke leesbaarheid belangrijk is."
    step1: "Upload INI bestanden of plak configuratie data. Het hulpmiddel parseert INI syntaxis en extraheert sectie-gebaseerde configuratie informatie."
    step3: "Genereer standaard INI formaat met ondersteuning voor secties, commentaren en verschillende data types. Gegenereerde INI bestanden zijn compatibel met de meeste applicaties en configuratie systemen."
    from_alias: "INI"
    to_alias: "INI Formaat"
  Avro:
    alias: "Avro Schema"
    what: "Apache Avro is een data serialisatie systeem dat rijke gegevensstructuren, compact binair formaat en schema evolutie mogelijkheden biedt. Veel gebruikt in big data verwerking, bericht wachtrijen en gedistribueerde systemen. De schema definitie ondersteunt complexe data types en versie compatibiliteit, waardoor het een belangrijk hulpmiddel is voor data engineers en systeem architecten."
    step1: "Upload Avro schema bestanden of plak data. Het hulpmiddel parseert Avro schema definities en extraheert tabel structuur informatie."
    step3: "Genereer standaard Avro schema definities met ondersteuning voor data type mapping, veld beperkingen en schema validatie. Gegenereerde schema's kunnen direct gebruikt worden in Hadoop ecosystemen, Kafka bericht systemen en andere big data platforms."
    from_alias: "Avro Schema"
    to_alias: "Avro Schema"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) is Google's taal-neutrale, platform-neutrale, uitbreidbare mechanisme voor het serialiseren van gestructureerde data. Veel gebruikt in microservices, API ontwikkeling en data opslag. Het efficiënte binaire formaat en sterke typing maken het ideaal voor hoge prestatie applicaties en cross-taal communicatie."
    step1: "Upload .proto bestanden of plak Protocol Buffer definities. Het hulpmiddel parseert protobuf syntaxis en extraheert bericht structuur informatie."
    step3: "Genereer standaard Protocol Buffer definities met ondersteuning voor bericht types, veld opties en service definities. Gegenereerde .proto bestanden kunnen gecompileerd worden voor meerdere programmeertalen."
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf Schema"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas is de populairste data analyse bibliotheek in Python, met DataFrame als kerngegevensstructuur. Het biedt krachtige data manipulatie, schoonmaak en analyse mogelijkheden, veel gebruikt in data wetenschap, machine learning en business intelligence. Een onmisbaar hulpmiddel voor Python ontwikkelaars en data analisten."
    step1: "Upload Python bestanden bevattend DataFrame code of plak data. Het hulpmiddel parseert Pandas syntaxis en extraheert DataFrame structuur informatie."
    step3: "Genereer standaard Pandas DataFrame code met ondersteuning voor data type specificaties, index instellingen en data operaties. Gegenereerde code kan direct uitgevoerd worden in Python omgeving voor data analyse en verwerking."
    from_alias: "Pandas DataFrame"
    to_alias: "Pandas DataFrame"
  RDF:
    alias: "RDF Triple"
    what: "RDF (Resource Description Framework) is een standaard model voor data uitwisseling op het Web, ontworpen om informatie over bronnen in een grafiek vorm te representeren. Veel gebruikt in semantisch web, kennis grafieken en gekoppelde data applicaties. De triple structuur maakt rijke metadata representatie en semantische relaties mogelijk."
    step1: "Upload RDF bestanden of plak triple data. Het hulpmiddel parseert RDF syntaxis en extraheert semantische relaties en bron informatie."
    step3: "Genereer standaard RDF formaat met ondersteuning voor verschillende serialisaties (RDF/XML, Turtle, N-Triples). Gegenereerde RDF kan gebruikt worden in semantische web applicaties, kennisbanken en gekoppelde data systemen."
    from_alias: "RDF"
    to_alias: "RDF Triple"
  MATLAB:
    alias: "MATLAB Array"
    what: "MATLAB is een hoge prestatie numerieke computing en visualisatie software veel gebruikt in engineering computing, data analyse en algoritme ontwikkeling. De array en matrix operaties zijn krachtig, ondersteunen complexe wiskundige berekeningen en data verwerking. Een essentieel hulpmiddel voor ingenieurs, onderzoekers en data wetenschappers."
    step1: "Upload MATLAB .m bestanden of plak array data. Het hulpmiddel parseert MATLAB syntaxis en extraheert array structuur informatie."
    step3: "Genereer standaard MATLAB array code met ondersteuning voor multi-dimensionale arrays, data type specificaties en variabele naamgeving. Gegenereerde code kan direct uitgevoerd worden in MATLAB omgeving voor data analyse en wetenschappelijke computing."
    from_alias: "MATLAB Array"
    to_alias: "MATLAB Array"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame is de kerngegevensstructuur in de R programmeertaal, veel gebruikt in statistische analyse, data mining en machine learning. R is het premier hulpmiddel voor statistische computing en grafiek, met DataFrame die krachtige data manipulatie, statistische analyse en visualisatie mogelijkheden biedt. Essentieel voor data wetenschappers, statistici en onderzoekers die werken met gestructureerde data analyse."
    step1: "Upload R data bestanden of plak DataFrame code. Het hulpmiddel parseert R syntaxis en extraheert DataFrame structuur informatie inclusief kolom types, rij namen en data inhoud."
    step3: "Genereer standaard R DataFrame code met ondersteuning voor data type specificaties, factor niveaus, rij/kolom namen en R-specifieke gegevensstructuren. Gegenereerde code kan direct uitgevoerd worden in R omgeving voor statistische analyse en data verwerking."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Start Converteren"
  start_generating: "Start Genereren"
  api_docs: "API Documentatie"
related:
  section_title: 'Meer {{ if and .from (ne .from "generator") }}{{ .from }} en {{ end }}{{ .to }} Converters'
  section_description: 'Ontdek meer converters voor {{ if and .from (ne .from "generator") }}{{ .from }} en {{ end }}{{ .to }} formaten. Transformeer je data tussen meerdere formaten met onze professionele online conversietools.'
  title: "{{ .from }} naar {{ .to }}"
howto:
  step2: "Bewerk data met onze geavanceerde online tabeleditor met professionele functies. Ondersteunt het verwijderen van lege rijen, duplicaten verwijderen, data transpositie, sorteren, regex zoeken en vervangen, en realtime voorvertoning. Alle wijzigingen worden automatisch geconverteerd naar %s formaat met nauwkeurige, betrouwbare resultaten."
  section_title: "Hoe gebruik je de {{ . }}"
  converter_description: "Leer hoe je {{ .from }} naar {{ .to }} converteert met onze stap-voor-stap gids. Professionele online converter met geavanceerde functies en realtime voorvertoning."
  generator_description: "Leer hoe je professionele {{ .to }} tabellen maakt met onze online generator. Excel-achtige bewerking, realtime voorvertoning en directe export mogelijkheden."
extension:
  section_title: "Tabel Detectie & Extractie Extensie"
  section_description: "Extraheer tabellen van elke website met één klik. Converteer naar 30+ formaten inclusief Excel, CSV, JSON direct - geen kopiëren en plakken vereist."
  features:
    extraction_title: "Eén-Klik Tabel Extractie"
    extraction_description: "Extraheer direct tabellen van elke webpagina zonder kopiëren en plakken - professionele data extractie eenvoudig gemaakt"
    formats_title: "30+ Formaat Converter Ondersteuning"
    formats_description: "Converteer geëxtraheerde tabellen naar Excel, CSV, JSON, Markdown, SQL, en meer met onze geavanceerde tabelconverter"
    detection_title: "Slimme Tabel Detectie"
    detection_description: "Detecteert automatisch en markeert tabellen op elke webpagina voor snelle data extractie en conversie"
  hover_tip: "✨ Hover over elke tabel om het extractie icoon te zien"
recommendations:
  section_title: "Aanbevolen door Universiteiten & Professionals"
  section_description: "TableConvert wordt vertrouwd door professionals van universiteiten, onderzoeksinstellingen en ontwikkelteams voor betrouwbare tabelconversie en dataverwerking."
  cards:
    university_title: "University of Wisconsin-Madison"
    university_description: "TableConvert.com - Professionele gratis online tabelconverter en dataformaten tool"
    university_link: "Lees Artikel"
    facebook_title: "Data Professional Community"
    facebook_description: "Gedeeld en aanbevolen door data analisten en professionals in Facebook ontwikkelaar groepen"
    facebook_link: "Bekijk Post"
    twitter_title: "Ontwikkelaar Community"
    twitter_description: "Aanbevolen door @xiaoying_eth en andere ontwikkelaars op X (Twitter) voor tabelconversie"
    twitter_link: "Bekijk Tweet"
faq:
  section_title: "Veelgestelde Vragen"
  section_description: "Veelvoorkomende vragen over onze gratis online tabelconverter, dataformaten en conversieproces."
  what: "Wat is %s formaat?"
  howto_convert:
    question: "Hoe gebruik je de {{ . }} gratis?"
    answer: "Upload je {{ .from }} bestand, plak data, of extraheer van webpagina's met onze gratis online tabelconverter. Onze professionele converter tool transformeert je data direct naar {{ .to }} formaat met realtime voorvertoning en geavanceerde bewerkingsfuncties. Download of kopieer het geconverteerde resultaat direct."
  security:
    question: "Zijn mijn gegevens veilig bij het gebruik van deze online converter?"
    answer: "Absoluut! Alle tabelconversies gebeuren lokaal in je browser - je data verlaat nooit je apparaat. Onze online converter verwerkt alles client-side, wat volledige privacy en databeveiliging garandeert. Geen bestanden worden opgeslagen op onze servers."
  free:
    question: "Is TableConvert echt gratis te gebruiken?"
    answer: "Ja, TableConvert is volledig gratis! Alle converter functies, tabeleditor, data generator tools en export opties zijn beschikbaar zonder kosten, registratie of verborgen kosten. Converteer onbeperkt bestanden online gratis."
  filesize:
    question: "Welke bestandsgrootte limieten heeft de online converter?"
    answer: "Onze gratis online tabelconverter ondersteunt bestanden tot 10MB. Voor grotere bestanden, batch verwerking, of zakelijke behoeften, gebruik onze browser extensie of professionele API service met hogere limieten."
stats:
  conversions: "Tabellen Geconverteerd"
  tables: "Tabellen Gegenereerd"
  formats: "Data Bestand Formaten"
  rating: "Gebruiker Beoordeling"
