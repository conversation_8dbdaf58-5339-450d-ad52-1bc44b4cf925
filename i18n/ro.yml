site:
  fullname: "Table Convert"
  name: "TableConvert"
  subtitle: "Convertor și Generator de Tabele Online Gratuit"
  intro: "TableConvert este un instrument gratuit online de conversie a tabelelor și generator de date care suportă conversia între 30+ formate incluzând Excel, CSV, JSON, Markdown, LaTeX, SQL și multe altele."
  followTwitter: "Urmărește-ne pe X"
title:
  converter: "%s către %s"
  generator: "Generator %s"
post:
  tags:
    converter: "Convertor"
    editor: "Editor"
    generator: "Generator"
    maker: "Constructor"
  converter:
    title: "Convertește %s către %s Online"
    short: "Un instrument online gratuit și puternic %s către %s"
    intro: "Convertor online %s către %s ușor de folosit. Transformă datele din tabele fără efort cu instrumentul nostru intuitiv de conversie. Rapid, fiabil și prietenos cu utilizatorul."
  generator:
    title: "Editor și Generator %s Online"
    short: "Instrument profesional de generare online %s cu funcții cuprinzătoare"
    intro: "Generator online %s și editor de tabele ușor de folosit. Creează tabele de date profesionale fără efort cu instrumentul nostru intuitiv și previzualizarea în timp real."
navbar:
  search:
    placeholder: "Caută convertor ..."
  sponsor: "Cumpără-mi o Cafea"
  extension: "Extensie"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Sursa de Date"
    placeholder: "Lipește datele tale %s sau trage fișierele %s aici"
    example: "Exemplu"
    upload: "Încarcă Fișier"
    extract:
      enter: "Extrage din Pagina Web"
      intro: "Introdu URL-ul unei pagini web care conține date de tabel pentru a extrage automat date structurate"
      btn: "Extrage %s"
    excel:
      sheet: "Foaie de Lucru"
      none: "Niciunul"
  tableEditor:
    title: "Editor de Tabele Online"
    undo: "Anulează"
    redo: "Refă"
    transpose: "Transpune"
    clear: "Șterge"
    deleteBlank: "Șterge Goale"
    deleteDuplicate: "Elimină Duplicate"
    uppercase: "MAJUSCULE"
    lowercase: "minuscule"
    capitalize: "Prima Literă Mare"
    replace:
      replace: "Găsește și Înlocuiește (Regex suportat)"
      subst: "Înlocuiește cu..."
      btn: "Înlocuiește Tot"
  tableGenerator:
    title: "Generator de Tabele"
    sponsor: "Cumpără-mi o Cafea"
    copy: "Copiază în Clipboard"
    download: "Descarcă Fișier"
    tooltip:
      html:
        escape: "Escape caracterele speciale HTML (&, <, >, \", ') pentru a preveni erorile de afișare"
        div: "Folosiți layout-ul DIV+CSS în loc de tag-urile TABLE tradiționale, mai potrivit pentru design responsiv"
        minify: "Eliminați spațiile și întreruperile de linie pentru a genera cod HTML comprimat"
        thead: "Generați structura standard de cap de tabel (&lt;thead&gt;) și corp (&lt;tbody&gt;)"
        tableCaption: "Adaugă titlu descriptiv deasupra tabelului (elementul &lt;caption&gt;)"
        tableClass: "Adaugă numele clasei CSS la tabel pentru personalizarea ușoară a stilului"
        tableId: "Setează identificatorul ID unic pentru tabel pentru manipularea JavaScript"
      jira:
        escape: "Escapează caracterele pipe (|) pentru a evita conflictele cu sintaxa tabelului Jira"
      json:
        parsingJSON: "Analizează inteligent șirurile JSON din celule în obiecte"
        minify: "Generează format JSON compact pe o singură linie pentru a reduce dimensiunea fișierului"
        format: "Selectați structura datelor JSON de ieșire: array de obiecte, array 2D, etc."
      latex:
        escape: "Escapează caracterele speciale LaTeX (%, &, _, #, $, etc.) pentru a asigura compilarea corectă"
        ht: "Adaugă parametrul de poziție flotantă [!ht] pentru a controla poziția tabelului pe pagină"
        mwe: "Generează document LaTeX complet"
        tableAlign: "Setează alinierea orizontală a tabelului pe pagină"
        tableBorder: "Configurează stilul marginii tabelului: fără margine, margine parțială, margine completă"
        label: "Setează eticheta tabelului pentru referințele încrucișate ale comenzii \\ref{}"
        caption: "Setează legenda tabelului pentru afișare deasupra sau dedesubtul tabelului"
        location: "Alege poziția de afișare a legendei tabelului: deasupra sau dedesubt"
        tableType: "Alege tipul mediului tabelului: tabular, longtable, array, etc."
      markdown:
        escape: "Escapează caracterele speciale Markdown (*, _, |, \\, etc.) pentru a evita conflictele de format"
        pretty: "Aliniază automat lățimile coloanelor pentru a genera un format de tabel mai frumos"
        simple: "Folosește sintaxa simplificată, omițând liniile verticale ale marginii exterioare"
        boldFirstRow: "Fă textul primului rând îngroșat"
        boldFirstColumn: "Fă textul primei coloane îngroșat"
        firstHeader: "Tratează primul rând ca antet și adaugă linia separatoare"
        textAlign: "Setează alinierea textului coloanei: stânga, centru, dreapta"
        multilineHandling: "Gestionarea textului pe mai multe linii: păstrează întreruperile de linie, escapează la \\n, folosește taguri &lt;br&gt;"

        includeLineNumbers: "Adaugă coloana cu numărul liniei în partea stângă a tabelului"
      magic:
        builtin: "Selectează formatele de șablon comune predefinite"
        rowsTpl: "<table> <tr> <th>Sintaxă Magică</th> <th>Descriere</th> <th>Metode JS Suportate</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>Al 1-lea, al 2-lea ... câmp din <b>a</b>ntet, adică {hA} {hB} ...</td> <td>Metode pentru șiruri</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>Al 1-lea, al 2-lea ... câmp din rândul curent, adică {$A} {$B} ...</td> <td>Metode pentru șiruri</td> </tr> <tr> <td>{F,} {F;}</td> <td>Împarte rândul curent prin șirul de după <b>F</b></td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td><b>N</b>umărul de <b>r</b>ând curent de la 1 sau 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>N</b>umărul <b>f</b>inal de <b>r</b>ânduri </td> <td></td> </tr> <tr> <td>{x ...}</td> <td><b>E</b>xecută cod JavaScript, ex: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Folosește backslash <b>\\</b> pentru a afișa acolade {...} </td> <td></td> </tr></table>"
        headerTpl: "Șablon de ieșire personalizat pentru secțiunea antet"
        footerTpl: "Șablon de ieșire personalizat pentru secțiunea subsol"
      textile:
        escape: "Escapează caracterele de sintaxă Textile (|, ., -, ^) pentru a evita conflictele de format"
        rowHeader: "Setează primul rând ca rând antet"
        thead: "Add Textile syntax markers for table head and body"
      xml:
        escape: "Escape XML special characters (&lt;, &gt;, &amp;, \", ') to ensure valid XML"
        minify: "Generează ieșire XML comprimată, eliminând spațiile suplimentare"
        rootElement: "Set XML root element tag name"
        rowElement: "Set XML element tag name for each row of data"
        declaration: "Adaugă antetul declarației XML (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Output data as XML attributes instead of child elements"
        cdata: "Wrap text content with CDATA to protect special characters"
        encoding: "Set character encoding format for XML document"
        indentation: "Choose XML indentation character: spaces or tabs"
      yaml:
        indentSize: "Set number of spaces for YAML hierarchy indentation (usually 2 or 4)"
        arrayStyle: "Array format: block (one item per line) or flow (inline format)"
        quotationStyle: "Stilul ghilimelelor pentru șiruri: fără ghilimele, ghilimele simple, ghilimele duble"
      csv:
        bom: "Add UTF-8 byte order mark to help Excel and other software recognize encoding"
      excel:
        autoWidth: "Automatically adjust column width based on content"
        protectSheet: "Activează protecția foii de lucru cu parola: tableconvert.com"
      sql:
        primaryKey: "Specifică numele câmpului cheii primare pentru declarația CREATE TABLE"
        dialect: "Selectează tipul bazei de date, afectând sintaxa ghilimelelor și tipurilor de date"
      ascii:
        forceSep: "Forțează linii separatoare între fiecare rând de date"
        style: "Selectează stilul de desenare a bordurii tabelului ASCII"
        comment: "Adaugă marcatori de comentarii pentru a înfășura întregul tabel"
      mediawiki:
        minify: "Comprimă codul de ieșire, eliminând spațiile albe suplimentare"
        header: "Marchează primul rând ca stil de antet"
        sort: "Activează funcționalitatea de sortare prin clic pe tabel"
      asciidoc:
        minify: "Comprimă ieșirea în format AsciiDoc"
        firstHeader: "Setează primul rând ca rând de antet"
        lastFooter: "Setează ultimul rând ca rând de subsol"
        title: "Adaugă text de titlu la tabel"
      tracwiki:
        rowHeader: "Setează primul rând ca antet"
        colHeader: "Setează prima coloană ca antet"
      bbcode:
        minify: "Comprimă formatul de ieșire BBCode"
      restructuredtext:
        style: "Selectează stilul bordurii tabelului reStructuredText"
        forceSep: "Forțează linii separatoare"
      pdf:
        theme: "Selectează stilul vizual al tabelului PDF pentru documente profesionale"
        headerColor: "Alege culoarea de fundal a antetului pentru tabelele PDF"
        showHead: "Controlează afișarea antetului pe paginile PDF"
        docTitle: "Titlu opțional pentru documentul PDF"
        docDescription: "Text de descriere opțional pentru documentul PDF"
    label:
      ascii:
        forceSep: "Separatori de Rând"
        style: "Stilul marginii"
        comment: "Învelitorul comentariului"
      restructuredtext:
        style: "Stilul marginii"
        forceSep: "Forțează Separatorii"
      bbcode:
        minify: "Minifică Ieșirea"
      csv:
        doubleQuote: "Învelire cu Ghilimele Duble"
        delimiter: "Delimitator de Câmp"
        bom: "UTF-8 BOM"
        valueDelimiter: "Delimitator de Valoare"
        rowDelimiter: "Delimitator de Rând"
        prefix: "Prefix de Rând"
        suffix: "Sufix de Rând"
      excel:
        autoWidth: "Lățime Automată"
        textFormat: "Format Text"
        protectSheet: "Protejează Foaia"
        boldFirstRow: "Prima linie îngroșată"
        boldFirstColumn: "Prima coloană îngroșată"
        sheetName: "Numele foii"
      html:
        escape: "Escape caractere HTML"
        div: "Tabel DIV"
        minify: "Minificare cod"
        thead: "Structura Antetului Tabelului"
        tableCaption: "Legenda tabelului"
        tableClass: "Clasa tabelului"
        tableId: "ID-ul tabelului"
        rowHeader: "Antet de Rând"
        colHeader: "Antet de Coloană"
      jira:
        escape: "Escape Caractere"
        rowHeader: "Antet de Rând"
        colHeader: "Antet de Coloană"
      json:
        parsingJSON: "Analizează JSON"
        minify: "Minifică Ieșirea"
        format: "Format Date"
        rootName: "Numele Obiectului Rădăcină"
        indentSize: "Mărimea indentării"
      jsonlines:
        parsingJSON: "Analizează JSON"
        format: "Format Date"
      latex:
        escape: "Escape Caractere Tabel LaTeX"
        ht: "Poziție Plutitoare"
        mwe: "Document Complet"
        tableAlign: "Alinierea Tabelului"
        tableBorder: "Stilul Marginii"
        label: "Etichetă de Referință"
        caption: "Legenda Tabelului"
        location: "Poziția Legendei"
        tableType: "Tipul Tabelului"
        boldFirstRow: "Prima Linie Îngroșată"
        boldFirstColumn: "Prima Coloană Îngroșată"
        textAlign: "Alinierea Textului"
        borders: "Setări Margini"
      markdown:
        escape: "Escape Caractere"
        pretty: "Tabel Markdown Frumos"
        simple: "Format Markdown Simplu"
        boldFirstRow: "Prima Linie Îngroșată"
        boldFirstColumn: "Prima Coloană Îngroșată"
        firstHeader: "Primul antet"
        textAlign: "Alinierea textului"
        multilineHandling: "Gestionarea Multilinie"

        includeLineNumbers: "Adaugă Numere de Rând"
        align: "Aliniere"
      mediawiki:
        minify: "Minifică Codul"
        header: "Markup Antet"
        sort: "Sortabil"
      asciidoc:
        minify: "Minifică Formatul"
        firstHeader: "Primul Antet"
        lastFooter: "Ultimul Subsol"
        title: "Titlul Tabelului"
      tracwiki:
        rowHeader: "Antet de Rând"
        colHeader: "Antet de Coloană"
      sql:
        drop: "Șterge Tabelul (Dacă Există)"
        create: "Creează Tabel"
        oneInsert: "Inserare în Lot"
        table: "Numele Tabelului"
        dialect: "Tipul Bazei de Date"
        primaryKey: "Cheia Primară"
      magic:
        builtin: "Șablon Încorporat"
        rowsTpl: "Șablon Rând, Sintaxă ->"
        headerTpl: "Șablon Antet"
        footerTpl: "Șablon Subsol"
      textile:
        escape: "Escape Caractere"
        rowHeader: "Antet de Rând"
        thead: "Sintaxa Antetului Tabelului"
      xml:
        escape: "Escape Caractere XML"
        minify: "Minifică Ieșirea"
        rootElement: "Element Rădăcină"
        rowElement: "Element Rând"
        declaration: "Declarație XML"
        attributes: "Mod Atribute"
        cdata: "Înveliș CDATA"
        encoding: "Codificare"
        indentSize: "Mărimea Indentării"
      yaml:
        indentSize: "Mărimea Indentării"
        arrayStyle: "Stilul Array-ului"
        quotationStyle: "Stilul Ghilimelelor"
      pdf:
        theme: "Tema Tabelului PDF"
        headerColor: "Culoarea Antetului PDF"
        showHead: "Afișarea Antetului PDF"
        docTitle: "Titlul Documentului PDF"
        docDescription: "Descrierea Documentului PDF"

sidebar:
  all: "Toate Instrumentele de Conversie"
  dataSource:
    title: "Sursă de Date"
    description:
      converter: "Importați %s pentru conversie în %s. Suportă încărcarea fișierelor, editarea online și extragerea datelor web."
      generator: "Creați date de tabel cu suport pentru multiple metode de intrare incluzând intrarea manuală, importul de fișiere și generarea de șabloane."
  tableEditor:
    title: "Editor de Tabele Online"
    description:
      converter: "Procesați %s online folosind editorul nostru de tabele. Experiență de operare similară Excel cu suport pentru ștergerea rândurilor goale, deduplicarea, sortarea și căutarea și înlocuirea."
      generator: "Editor de tabele online puternic oferind experiență de operare similară Excel. Suportă ștergerea rândurilor goale, deduplicarea, sortarea și căutarea și înlocuirea."
  tableGenerator:
    title: "Generator de Tabele"
    description:
      converter: "Generați rapid %s cu previzualizare în timp real a generatorului de tabele. Opțiuni de export bogate, copiere și descărcare cu un clic."
      generator: "Exportați datele %s în multiple formate pentru a satisface diferite scenarii de utilizare. Suportă opțiuni personalizate și previzualizare în timp real."
footer:
  changelog: "Jurnal de Modificări"
  sponsor: "Sponsori"
  contact: "Contactați-ne"
  privacyPolicy: "Politica de Confidențialitate"
  about: "Despre"
  resources: "Resurse"
  popularConverters: "Convertoare Populare"
  popularGenerators: "Generatoare Populare"
  dataSecurity: "Datele dumneavoastră sunt sigure - toate conversiile rulează în browserul dumneavoastră."
converters:
  Markdown:
    alias: "Tabel Markdown"
    what: "Markdown este un limbaj de marcare ușor utilizat pe scară largă pentru documentația tehnică, crearea de conținut pentru blog și dezvoltarea web. Sintaxa tabelului său este concisă și intuitivă, suportând alinierea textului, încorporarea linkurilor și formatarea. Este instrumentul preferat pentru programatori și scriitori tehnici, perfect compatibil cu GitHub, GitLab și alte platforme de găzduire cod."
    step1: "Lipiți datele tabelului Markdown în zona sursă de date sau trageți și plasați direct fișierele .md pentru încărcare. Instrumentul analizează automat structura și formatarea tabelului, suportând conținut imbricat complex și gestionarea caracterelor speciale."
    step3: "Generați cod de tabel Markdown standard în timp real, suportând multiple metode de aliniere, îngroșarea textului, adăugarea numerelor de linie și alte setări avansate de format. Codul generat este complet compatibil cu GitHub și editorii Markdown majori, gata de utilizare cu o copiere cu un clic."
    from_alias: "Fișier Tabel Markdown"
    to_alias: "Format Tabel Markdown"
  Magic:
    alias: "Șablon Personalizat"
    what: "Șablonul Magic este un generator de date avansat unic al acestui instrument, permițând utilizatorilor să creeze ieșiri de date în format arbitrar prin sintaxa șablonului personalizat. Suportă înlocuirea variabilelor, judecata condițională și procesarea în buclă. Este soluția finală pentru gestionarea nevoilor complexe de conversie a datelor și formatelor de ieșire personalizate, deosebit de potrivit pentru dezvoltatori și ingineri de date."
    step1: "Selectați șabloane comune încorporate sau creați sintaxa șablonului personalizat. Suportă variabile și funcții bogate care pot gestiona structuri de date complexe și logica de afaceri."
    step3: "Generați ieșiri de date care îndeplinesc complet cerințele de format personalizat. Suportă logica complexă de conversie a datelor și procesarea condițională, îmbunătățind semnificativ eficiența procesării datelor și calitatea ieșirii. Un instrument puternic pentru procesarea datelor în lot."
    from_alias: "Date Tabel"
    to_alias: "Ieșire Format Personalizat"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) este formatul de schimb de date cel mai utilizat, perfect suportat de Excel, Google Sheets, sistemele de baze de date și diverse instrumente de analiză a datelor. Structura sa simplă și compatibilitatea puternică îl fac formatul standard pentru migrarea datelor, importul/exportul în lot și schimbul de date între platforme, utilizat pe scară largă în analiza de afaceri, știința datelor și integrarea sistemelor."
    step1: "Încărcați fișiere CSV sau lipiți direct datele CSV. Instrumentul recunoaște inteligent diverse delimitatoare (virgulă, tab, punct și virgulă, pipe, etc.), detectează automat tipurile de date și formatele de codificare, suportând analiza rapidă a fișierelor mari și structurilor de date complexe."
    step3: "Generați fișiere în format CSV standard cu suport pentru delimitatoare personalizate, stiluri de ghilimele, formate de codificare și setări de marcă BOM. Asigură compatibilitatea perfectă cu sistemele țintă, oferind opțiuni de descărcare și compresie pentru a satisface nevoile de procesare a datelor la nivel de întreprindere."
    from_alias: "Fișier Date CSV"
    to_alias: "Format Standard CSV"
  JSON:
    alias: "Array JSON"
    what: "JSON (JavaScript Object Notation) este formatul standard de date tabel pentru aplicațiile web moderne, API-urile REST și arhitecturile de microservicii. Structura sa clară și analiza eficientă îl fac utilizat pe scară largă în interacțiunea datelor frontend și backend, stocarea fișierelor de configurare și bazele de date NoSQL. Suportă obiecte imbricate, structuri de array și multiple tipuri de date, făcându-l date de tabel indispensabile pentru dezvoltarea software modernă."
    step1: "Încărcați fișiere JSON sau lipiți array-uri JSON. Suportă recunoașterea automată și analiza array-urilor de obiecte, structurilor imbricate și tipurilor de date complexe. Instrumentul validează inteligent sintaxa JSON și oferă prompturi de eroare."
    step3: "Generați multiple ieșiri în format JSON: array-uri de obiecte standard, array-uri 2D, array-uri de coloane și formate de perechi cheie-valoare. Suportă ieșire înfrumusețată, modul de compresie, nume de obiecte rădăcină personalizate și setări de indentare, adaptându-se perfect la diverse interfețe API și nevoi de stocare a datelor."
    from_alias: "Fișier Array JSON"
    to_alias: "Format Standard JSON"
  JSONLines:
    alias: "Format JSONLines"
    what: "JSON Lines (cunoscut și ca NDJSON) este un format important pentru procesarea big data și transmisia datelor în flux, cu fiecare linie conținând un obiect JSON independent. Folosit pe scară largă în analiza jurnalelor, procesarea fluxurilor de date, învățarea automată și sistemele distribuite. Suportă procesarea incrementală și calculul paralel, făcându-l alegerea ideală pentru gestionarea datelor structurate la scară largă."
    step1: "Încarcă fișiere JSONLines sau lipește date. Instrumentul parsează obiectele JSON linie cu linie, suportând procesarea în flux a fișierelor mari și funcționalitatea de omitere a liniilor cu erori."
    step3: "Generează format JSONLines standard cu fiecare linie outputând un obiect JSON complet. Potrivit pentru procesarea în flux, importul în lot și scenariile de analiză big data, suportând validarea datelor și optimizarea formatului."
    from_alias: "Date JSONLines"
    to_alias: "Format Streaming JSONLines"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) este formatul standard pentru schimbul de date la nivel de întreprindere și managementul configurației, cu specificații de sintaxă stricte și mecanisme puternice de validare. Folosit pe scară largă în serviciile web, fișierele de configurare, stocarea documentelor și integrarea sistemelor. Suportă spațiile de nume, validarea schemei și transformarea XSLT, făcându-l date de tabel importante pentru aplicațiile de întreprindere."
    step1: "Încarcă fișiere XML sau lipește date XML. Instrumentul parsează automat structura XML și o convertește în format de tabel, suportând spațiul de nume, gestionarea atributelor și structurile imbricate complexe."
    step3: "Generează ieșire XML care respectă standardele XML. Suportă elemente rădăcină personalizate, numele elementelor de rând, modurile de atribute, învelirea CDATA și setările de codificare a caracterelor. Asigură integritatea datelor și compatibilitatea, îndeplinind cerințele aplicațiilor la nivel de întreprindere."
    from_alias: "Fișier Date XML"
    to_alias: "Format Standard XML"
  YAML:
    alias: "Configurație YAML"
    what: "YAML este un standard de serializare a datelor prietenos cu oamenii, renumit pentru structura sa ierarhică clară și sintaxa concisă. Folosit pe scară largă în fișierele de configurare, lanțurile de instrumente DevOps, Docker Compose și implementarea Kubernetes. Lizibilitatea sa puternică și sintaxa concisă îl fac un format de configurare important pentru aplicațiile cloud-native moderne și operațiunile automatizate."
    step1: "Încarcă fișiere YAML sau lipește date YAML. Instrumentul parsează inteligent structura YAML și validează corectitudinea sintaxei, suportând formatele multi-document și tipurile de date complexe."
    step3: "Generează ieșire în format YAML standard cu suport pentru stilurile de array bloc și flux, setări multiple de ghilimele, indentare personalizată și păstrarea comentariilor. Asigură că fișierele YAML de ieșire sunt complet compatibile cu diverse parsere și sisteme de configurare."
    from_alias: "Fișier Configurație YAML"
    to_alias: "Format Standard YAML"
  MySQL:
      alias: "Rezultate Interogare MySQL"
      what: "MySQL este cel mai popular sistem de management al bazelor de date relaționale open-source din lume, renumit pentru performanța sa înaltă, fiabilitate și ușurința în utilizare. Folosit pe scară largă în aplicațiile web, sistemele de întreprindere și platformele de analiză a datelor. Rezultatele interogărilor MySQL conțin de obicei date de tabel structurate, servind ca o sursă importantă de date în managementul bazelor de date și munca de analiză a datelor."
      step1: "Lipește rezultatele ieșirii interogării MySQL în zona sursă de date. Instrumentul recunoaște automat și parsează formatul de ieșire din linia de comandă MySQL, suportând diverse stiluri de rezultate ale interogărilor și codificări de caractere, gestionând inteligent anteturile și rândurile de date."
      step3: "Convertește rapid rezultatele interogărilor MySQL în multiple formate de date de tabel, facilitând analiza datelor, generarea rapoartelor, migrarea datelor între sisteme și validarea datelor. Un instrument practic pentru administratorii de baze de date și analiștii de date."
      from_alias: "Ieșire Interogare MySQL"
      to_alias: "Date Tabel MySQL"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) este limbajul standard de operare pentru bazele de date relaționale, folosit pentru operațiuni de interogare, inserare, actualizare și ștergere a datelor. Ca tehnologie de bază a managementului bazelor de date, SQL este folosit pe scară largă în analiza datelor, business intelligence, procesarea ETL și construcția depozitelor de date. Este un instrument de abilități esențial pentru profesioniștii din domeniul datelor."
    step1: "Lipește declarații INSERT SQL sau încarcă fișiere .sql. Instrumentul parsează inteligent sintaxa SQL și extrage datele tabelului, suportând multiple dialecte SQL și procesarea declarațiilor de interogare complexe."
    step3: "Generează declarații INSERT SQL standard și declarații de creare a tabelelor. Suportă multiple dialecte de baze de date (MySQL, PostgreSQL, SQLite, SQL Server, Oracle), gestionează automat maparea tipurilor de date, escaparea caracterelor și constrângerile cheilor primare. Asigură că codul SQL generat poate fi executat direct."
    from_alias: "Insert SQL"
    to_alias: "Instrucțiune SQL"
  Qlik:
      alias: "Tabel Qlik"
      what: "Qlik este un furnizor de software specializat în vizualizarea datelor, dashboard-uri executive și produse de business intelligence self-service, alături de Tableau și Microsoft."
      step1: ""
      step3: "În final, [Generatorul de Tabele](#TableGenerator) afișează rezultatele conversiei. Folosește în Qlik Sense, Qlik AutoML, QlikView sau alte software-uri compatibile cu Qlik."
      from_alias: "Tabel Qlik"
      to_alias: "Tabel Qlik"
  DAX:
      alias: "Tabel DAX"
      what: "DAX (Data Analysis Expressions) este un limbaj de programare folosit în Microsoft Power BI pentru crearea de coloane calculate, măsuri și tabele personalizate."
      step1: ""
      step3: "În final, [Generatorul de Tabele](#TableGenerator) afișează rezultatele conversiei. Așa cum era de așteptat, este folosit în mai multe produse Microsoft, inclusiv Microsoft Power BI, Microsoft Analysis Services și Microsoft Power Pivot pentru Excel."
      from_alias: "Tabel DAX"
      to_alias: "Tabel DAX"
  Firebase:
    alias: "Listă Firebase"
    what: "Firebase este o platformă de dezvoltare aplicații BaaS care oferă servicii backend găzduite precum baza de date în timp real, stocare cloud, autentificare, raportare crash-uri, etc."
    step1: ""
    step3: "În final, [Generatorul de Tabele](#TableGenerator) afișează rezultatele conversiei. Apoi poți folosi metoda push din API-ul Firebase pentru a adăuga la o listă de date în baza de date Firebase."
    from_alias: "Listă Firebase"
    to_alias: "Listă Firebase"
  HTML:
    alias: "Tabel HTML"
    what: "Tabelele HTML sunt modalitatea standard de afișare a datelor structurate în paginile web, construite cu tag-urile table, tr, td și altele. Suportă personalizarea bogată a stilurilor, layout responsiv și funcționalitate interactivă. Folosite pe scară largă în dezvoltarea site-urilor web, afișarea datelor și generarea rapoartelor, servind ca o componentă importantă a dezvoltării front-end și design-ului web."
    step1: "Lipește codul HTML care conține tabele sau încarcă fișiere HTML. Instrumentul recunoaște automat și extrage datele tabelelor din pagini, suportând structuri HTML complexe, stiluri CSS și procesarea tabelelor imbricate."
    step3: "Generează cod de tabel HTML semantic cu suport pentru structura thead/tbody, setări clase CSS, legende de tabel, anteturi rând/coloană și configurarea atributelor responsive. Asigură că codul de tabel generat respectă standardele web cu accesibilitate bună și prietenie SEO."
    from_alias: "Tabel HTML"
    to_alias: "Tabel HTML"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel este cel mai popular software de foi de calcul din lume, folosit pe scară largă în analiza de afaceri, managementul financiar, procesarea datelor și crearea rapoartelor. Capacitățile sale puternice de procesare a datelor, biblioteca bogată de funcții și caracteristicile flexibile de vizualizare îl fac instrumentul standard pentru automatizarea biroului și analiza datelor, cu aplicații extinse în aproape toate industriile și domeniile."
    step1: "Încarcă fișiere Excel (suportă formatele .xlsx, .xls) sau copiază datele tabelului direct din Excel și lipește. Instrumentul suportă procesarea multi-foaie, recunoașterea formatelor complexe și parsarea rapidă a fișierelor mari, gestionând automat celulele îmbinate și tipurile de date."
    step3: "Generează date de tabel compatibile cu Excel care pot fi lipite direct în Excel sau descărcate ca fișiere .xlsx standard. Suportă denumirea foilor de lucru, formatarea celulelor, lățimea automată a coloanelor, stilizarea anteturilor și setările de validare a datelor. Asigură că fișierele Excel de ieșire au o apariție profesională și funcționalitate completă."
    from_alias: "Foaie de Calcul Excel"
    to_alias: "Excel"
  LaTeX:
    alias: "Tabel LaTeX"
    what: "LaTeX este un sistem profesional de tipărire a documentelor, deosebit de potrivit pentru crearea lucrărilor academice, documentelor tehnice și publicațiilor științifice. Funcționalitatea sa de tabele este puternică, suportând formule matematice complexe, controlul precis al layout-ului și ieșirea PDF de înaltă calitate. Este instrumentul standard în mediul academic și în publicarea științifică, folosit pe scară largă în articolele de reviste, disertații și tipărirea manualelor tehnice."
    step1: "Lipește codul de tabel LaTeX sau încarcă fișiere .tex. Instrumentul parsează sintaxa tabelului LaTeX și extrage conținutul datelor, suportând multiple medii de tabel (tabular, longtable, array, etc.) și comenzi de format complexe."
    step3: "Generează cod de tabel LaTeX profesional cu suport pentru selecția multiplelor medii de tabel, configurarea stilului marginilor, setările poziției legendei, specificarea clasei documentului și managementul pachetelor. Poate genera documente LaTeX complete compilabile, asigurând că tabelele de ieșire respectă standardele de publicare academică."
    from_alias: "Tabel LaTeX"
    to_alias: "Tabel LaTeX"
  ASCII:
    alias: "Tabel Text ASCII"
    what: "Tabelele ASCII folosesc caractere text simplu pentru a desena marginile și structurile tabelelor, oferind cea mai bună compatibilitate și portabilitate. Compatibile cu toate editoarele de text, mediile de terminal și sistemele de operare. Folosite pe scară largă în documentația codului, manualele tehnice, fișierele README și ieșirea instrumentelor din linia de comandă. Formatul preferat de afișare a datelor pentru programatori și administratorii de sistem."
    step1: "Încarcă fișiere text care conțin tabele ASCII sau lipește direct datele tabelului. Instrumentul recunoaște inteligent și parsează structurile tabelelor ASCII, suportând multiple stiluri de margini și formate de aliniere."
    step3: "Generează tabele ASCII text simplu frumoase cu suport pentru multiple stiluri de margini (linie simplă, linie dublă, colțuri rotunjite, etc.), metode de aliniere a textului și lățimea automată a coloanelor. Tabelele generate se afișează perfect în editoarele de cod, documente și liniile de comandă."
    from_alias: "Tabel Text ASCII"
    to_alias: "Tabel Text ASCII"
  MediaWiki:
    alias: "Tabel MediaWiki"
    what: "MediaWiki este platforma software open-source folosită de site-urile wiki celebre precum Wikipedia. Sintaxa sa de tabele este concisă dar puternică, suportând personalizarea stilului tabelelor, funcționalitatea de sortare și încorporarea linkurilor. Folosită pe scară largă în managementul cunoștințelor, editarea colaborativă și sistemele de management al conținutului, servind ca tehnologie de bază pentru construirea enciclopediilor wiki și bazelor de cunoștințe."
    step1: "Lipește codul de tabel MediaWiki sau încarcă fișiere sursă wiki. Instrumentul parsează sintaxa markup wiki și extrage datele tabelului, suportând sintaxa wiki complexă și procesarea șabloanelor."
    step3: "Generează cod de tabel MediaWiki standard cu suport pentru setările stilului antetului, alinierea celulelor, activarea funcționalității de sortare și opțiunile de compresie a codului. Codul generat poate fi folosit direct pentru editarea paginilor wiki, asigurând afișarea perfectă pe platformele MediaWiki."
    from_alias: "Tabel MediaWiki"
    to_alias: "Tabel MediaWiki"
  TracWiki:
    alias: "Tabel TracWiki"
    what: "Trac este un sistem web de management de proiecte și urmărire a bug-urilor care folosește sintaxa wiki simplificată pentru a crea conținut de tabele."
    step1: "Încarcă fișiere TracWiki sau lipește datele tabelului."
    step3: "Generează cod de tabel compatibil cu TracWiki cu suport pentru setările anteturilor rând/coloană, facilitând managementul documentelor de proiect."
    from_alias: "Tabel TracWiki"
    to_alias: "Tabel TracWiki"
  AsciiDoc:
    alias: "Tabel AsciiDoc"
    what: "AsciiDoc este un limbaj de markup ușor care poate fi convertit în HTML, PDF, pagini de manual și alte formate, folosit pe scară largă pentru scrierea documentației tehnice."
    step1: "Încarcă fișiere AsciiDoc sau lipește date."
    step3: "Generează sintaxa tabelului AsciiDoc cu suport pentru setările antetului, subsolului și titlului, direct utilizabilă în editoarele AsciiDoc."
    from_alias: "Tabel AsciiDoc"
    to_alias: "Tabel AsciiDoc"
  reStructuredText:
    alias: "Tabel reStructuredText"
    what: "reStructuredText este formatul standard de documentație pentru comunitatea Python, suportând sintaxa bogată de tabele, folosit în mod obișnuit pentru generarea documentației Sphinx."
    step1: "Încarcă fișiere .rst sau lipește date reStructuredText."
    step3: "Generează tabele reStructuredText standard cu suport pentru multiple stiluri de margini, direct utilizabile în proiectele de documentație Sphinx."
    from_alias: "Tabel reStructuredText"
    to_alias: "Tabel reStructuredText"
  PHP:
    alias: "Array PHP"
    what: "PHP este un limbaj de scripting server-side popular, cu array-urile fiind structura sa de date de bază, utilizat pe scară largă în dezvoltarea web și procesarea datelor."
    step1: "Încarcă fișiere care conțin array-uri PHP sau lipește datele direct."
    step3: "Generează cod de array PHP standard care poate fi folosit direct în proiecte PHP, suportând formate de array asociative și indexate."
    from_alias: "Array PHP"
    to_alias: "Cod PHP"
  Ruby:
    alias: "Array Ruby"
    what: "Ruby este un limbaj de programare orientat pe obiecte dinamic cu sintaxă concisă și elegantă, cu array-urile fiind o structură de date importantă."
    step1: "Încarcă fișiere Ruby sau lipește datele array-ului."
    step3: "Generează cod de array Ruby care respectă specificațiile sintaxei Ruby, direct utilizabil în proiecte Ruby."
    from_alias: "Array Ruby"
    to_alias: "Cod Ruby"
  ASP:
    alias: "Array ASP"
    what: "ASP (Active Server Pages) este mediul de scripting server-side al Microsoft, suportând multiple limbaje de programare pentru dezvoltarea paginilor web dinamice."
    step1: "Încarcă fișiere ASP sau lipește datele array-ului."
    step3: "Generează cod de array compatibil cu ASP cu suport pentru sintaxa VBScript și JScript, utilizabil în proiecte ASP.NET."
    from_alias: "Array ASP"
    to_alias: "Cod ASP"
  ActionScript:
    alias: "Array ActionScript"
    what: "ActionScript este un limbaj de programare orientat pe obiecte folosit în principal pentru dezvoltarea aplicațiilor Adobe Flash și AIR."
    step1: "Încarcă fișiere .as sau lipește date ActionScript."
    step3: "Generează cod de array ActionScript care respectă standardele sintaxei AS3, utilizabil pentru dezvoltarea proiectelor Flash și Flex."
    from_alias: "Array ActionScript"
    to_alias: "Cod ActionScript"
  BBCode:
    alias: "Tabel BBCode"
    what: "BBCode este un limbaj de markup ușor folosit în mod obișnuit în forumuri și comunități online, oferind funcționalitate de formatare simplă inclusiv suport pentru tabele."
    step1: "Încarcă fișiere care conțin BBCode sau lipește date."
    step3: "Generează cod de tabel BBCode potrivit pentru postarea în forumuri și crearea de conținut comunitar, cu suport pentru format de ieșire comprimat."
    from_alias: "Tabel BBCode"
    to_alias: "Tabel BBCode"
  PDF:
    alias: "Tabel PDF"
    what: "PDF (Portable Document Format) este un standard de document cross-platform cu layout fix, afișare consistentă și caracteristici de tipărire de înaltă calitate. Folosit pe scară largă în documente formale, rapoarte, facturi, contracte și lucrări academice. Formatul preferat pentru comunicarea de afaceri și arhivarea documentelor, asigurând efecte vizuale complet consistente pe diferite dispozitive și sisteme de operare."
    step1: "Importă date de tabel în orice format. Instrumentul analizează automat structura datelor și efectuează design inteligent de layout, suportând paginarea automată a tabelelor mari și procesarea tipurilor de date complexe."
    step3: "Generează fișiere de tabel PDF de înaltă calitate cu suport pentru multiple stiluri de temă profesionale (afaceri, academic, minimalist, etc.), fonturi multilingve, paginare automată, adăugare de filigran și optimizare pentru tipărire. Asigură că documentele PDF de ieșire au o apariție profesională, direct utilizabile pentru prezentări de afaceri și publicare formală."
    from_alias: "Date Tabel"
    to_alias: "Tabel PDF"
  JPEG:
    alias: "Imagine JPEG"
    what: "JPEG este cel mai utilizat format de imagine digitală cu efecte de compresie excelente și compatibilitate largă. Dimensiunea sa mică a fișierului și viteza rapidă de încărcare îl fac potrivit pentru afișarea web, partajarea pe rețelele sociale, ilustrațiile documentelor și prezentările online. Formatul standard de imagine pentru media digitală și comunicarea în rețea, perfect suportat de aproape toate dispozitivele și software-urile."
    step1: "Importă date de tabel în orice format. Instrumentul efectuează design inteligent de layout și optimizare vizuală, calculând automat dimensiunea și rezoluția optimă."
    step3: "Generează imagini de tabel JPEG de înaltă definiție cu suport pentru multiple scheme de culori tematice (luminos, întunecat, prietenos cu ochii, etc.), layout adaptiv, optimizarea clarității textului și personalizarea dimensiunii. Potrivit pentru partajarea online, inserarea în documente și utilizarea în prezentări, asigurând efecte vizuale excelente pe diverse dispozitive de afișare."
    from_alias: "Date Tabel"
    to_alias: "Imagine JPEG"
  Jira:
    alias: "Tabel Jira"
    what: "JIRA este software profesional de management de proiecte și urmărire a bug-urilor dezvoltat de Atlassian, folosit pe scară largă în dezvoltarea agile, testarea software-ului și colaborarea la proiecte. Funcționalitatea sa de tabele suportă opțiuni bogate de formatare și afișare a datelor, servind ca un instrument important pentru echipele de dezvoltare software, managerii de proiecte și personalul de asigurare a calității în managementul cerințelor, urmărirea bug-urilor și raportarea progresului."
    step1: "Încarcă fișiere care conțin date de tabel sau lipește direct conținutul datelor. Instrumentul procesează automat datele tabelului și escaparea caracterelor speciale."
    step3: "Generează cod de tabel compatibil cu platforma JIRA cu suport pentru setările stilului antetului, alinierea celulelor, procesarea escapării caracterelor și optimizarea formatului. Codul generat poate fi lipit direct în descrierile problemelor JIRA, comentarii sau pagini wiki, asigurând afișarea și redarea corectă în sistemele JIRA."
    from_alias: "Tabel Jira"
    to_alias: "Tabel Jira"
  Textile:
    alias: "Tabel Textile"
    what: "Textile este un limbaj de markup ușor concis cu sintaxă simplă și ușor de învățat, folosit pe scară largă în sistemele de management al conținutului, platformele de blog și sistemele de forum. Sintaxa sa de tabele este clară și intuitivă, suportând formatarea rapidă și setările de stil. Un instrument ideal pentru creatorii de conținut și administratorii de site-uri web pentru scrierea rapidă a documentelor și publicarea conținutului."
    step1: "Încarcă fișiere în format Textile sau lipește datele tabelului. Instrumentul parsează sintaxa markup Textile și extrage conținutul tabelului."
    step3: "Generează sintaxa standard de tabel Textile cu suport pentru markup-ul antetului, alinierea celulelor, escaparea caracterelor speciale și optimizarea formatului. Codul generat poate fi folosit direct în sistemele CMS, platformele de blog și sistemele de documente care suportă Textile, asigurând redarea și afișarea corectă a conținutului."
    from_alias: "Tabel Textile"
    to_alias: "Tabel Textile"
  PNG:
    alias: "Imagine PNG"
    what: "PNG (Portable Network Graphics) este un format de imagine fără pierderi cu compresie excelentă și suport pentru transparență. Folosit pe scară largă în design-ul web, grafica digitală și fotografia profesională. Calitatea sa înaltă și compatibilitatea largă îl fac ideal pentru capturi de ecran, logo-uri, diagrame și orice imagini care necesită detalii clare și fundal transparent."
    step1: "Importă date de tabel în orice format. Instrumentul efectuează design inteligent de layout și optimizare vizuală, calculând automat dimensiunea și rezoluția optimă pentru ieșirea PNG."
    step3: "Generează imagini de tabel PNG de înaltă calitate cu suport pentru multiple scheme de culori tematice, fundal transparent, layout adaptiv și optimizarea clarității textului. Perfect pentru utilizarea web, inserarea în documente și prezentări profesionale cu calitate vizuală excelentă."
    from_alias: ""
    to_alias: "Imagine PNG"
  TOML:
    alias: "TOML"
    what: "TOML (Tom's Obvious, Minimal Language) este un format de fișier de configurare care este ușor de citit și scris. Proiectat să fie neambiguu și simplu, este folosit pe scară largă în proiectele software moderne pentru managementul configurației. Sintaxa sa clară și tipizarea puternică îl fac o alegere excelentă pentru setările aplicațiilor și fișierele de configurare ale proiectelor."
    step1: "Încarcă fișiere TOML sau lipește datele de configurare. Instrumentul parsează sintaxa TOML și extrage informațiile de configurare structurate."
    step3: "Generează format TOML standard cu suport pentru structuri imbricate, tipuri de date și comentarii. Fișierele TOML generate sunt perfecte pentru configurarea aplicațiilor, instrumentele de build și setările proiectelor."
    from_alias: "TOML"
    to_alias: "Format TOML"
  INI:
    alias: "INI"
    what: "Fișierele INI sunt fișiere de configurare simple folosite de multe aplicații și sisteme de operare. Structura lor directă de perechi cheie-valoare le face ușor de citit și editat manual. Folosite pe scară largă în aplicațiile Windows, sistemele legacy și scenariile de configurare simple unde lizibilitatea umană este importantă."
    step1: "Încarcă fișiere INI sau lipește datele de configurare. Instrumentul parsează sintaxa INI și extrage informațiile de configurare bazate pe secțiuni."
    step3: "Generează format INI standard cu suport pentru secțiuni, comentarii și diverse tipuri de date. Fișierele INI generate sunt compatibile cu majoritatea aplicațiilor și sistemelor de configurare."
    from_alias: "INI"
    to_alias: "Format INI"
  Avro:
    alias: "Schema Avro"
    what: "Apache Avro este un sistem de serializare a datelor care oferă structuri de date bogate, format binar compact și capacități de evoluție a schemei. Folosit pe scară largă în procesarea big data, cozile de mesaje și sistemele distribuite. Definiția sa de schemă suportă tipuri de date complexe și compatibilitatea versiunilor, făcându-l un instrument important pentru inginerii de date și arhitecții de sistem."
    step1: "Încarcă fișiere de schemă Avro sau lipește date. Instrumentul parsează definițiile schemei Avro și extrage informațiile structurii tabelului."
    step3: "Generează definiții standard de schemă Avro cu suport pentru maparea tipurilor de date, constrângerile câmpurilor și validarea schemei. Schemele generate pot fi folosite direct în ecosistemele Hadoop, sistemele de mesaje Kafka și alte platforme big data."
    from_alias: "Schemă Avro"
    to_alias: "Schemă Avro"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) este mecanismul Google neutru din punct de vedere al limbajului, neutru din punct de vedere al platformei, extensibil pentru serializarea datelor structurate. Folosit pe scară largă în microservicii, dezvoltarea API și stocarea datelor. Formatul său binar eficient și tipizarea puternică îl fac ideal pentru aplicațiile de înaltă performanță și comunicarea între limbaje."
    step1: "Încarcă fișiere .proto sau lipește definițiile Protocol Buffer. Instrumentul parsează sintaxa protobuf și extrage informațiile structurii mesajului."
    step3: "Generează definiții standard Protocol Buffer cu suport pentru tipurile de mesaje, opțiunile câmpurilor și definițiile serviciilor. Fișierele .proto generate pot fi compilate pentru multiple limbaje de programare."
    from_alias: "Protocol Buffer"
    to_alias: "Schemă Protobuf"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas este cea mai populară bibliotecă de analiză a datelor în Python, cu DataFrame fiind structura sa de date de bază. Oferă capacități puternice de manipulare, curățare și analiză a datelor, folosită pe scară largă în știința datelor, învățarea automată și business intelligence. Un instrument indispensabil pentru dezvoltatorii Python și analiștii de date."
    step1: "Încarcă fișiere Python care conțin cod DataFrame sau lipește date. Instrumentul parsează sintaxa Pandas și extrage informațiile structurii DataFrame."
    step3: "Generează cod standard Pandas DataFrame cu suport pentru specificațiile tipurilor de date, setările indexului și operațiile cu date. Codul generat poate fi executat direct în mediul Python pentru analiza și procesarea datelor."
    from_alias: "Pandas DataFrame"
    to_alias: "Pandas DataFrame"
  RDF:
    alias: "Triplet RDF"
    what: "RDF (Resource Description Framework) este un model standard pentru schimbul de date pe Web, proiectat să reprezinte informații despre resurse într-o formă de graf. Folosit pe scară largă în web-ul semantic, grafurile de cunoștințe și aplicațiile de date legate. Structura sa de triplet permite reprezentarea bogată a metadatelor și relațiile semantice."
    step1: "Încarcă fișiere RDF sau lipește date triplet. Instrumentul parsează sintaxa RDF și extrage relațiile semantice și informațiile resurselor."
    step3: "Generează format RDF standard cu suport pentru diverse serializări (RDF/XML, Turtle, N-Triples). RDF-ul generat poate fi folosit în aplicațiile web semantice, bazele de cunoștințe și sistemele de date legate."
    from_alias: "RDF"
    to_alias: "Triplet RDF"
  MATLAB:
    alias: "Array MATLAB"
    what: "MATLAB este un software de calcul numeric și vizualizare de înaltă performanță folosit pe scară largă în calculul ingineresc, analiza datelor și dezvoltarea algoritmilor. Operațiile sale cu array-uri și matrici sunt puternice, suportând calcule matematice complexe și procesarea datelor. Un instrument esențial pentru ingineri, cercetători și oameni de știință ai datelor."
    step1: "Încarcă fișiere MATLAB .m sau lipește date array. Instrumentul parsează sintaxa MATLAB și extrage informațiile structurii array-ului."
    step3: "Generează cod standard de array MATLAB cu suport pentru array-uri multidimensionale, specificațiile tipurilor de date și denumirea variabilelor. Codul generat poate fi executat direct în mediul MATLAB pentru analiza datelor și calculul științific."
    from_alias: "Array MATLAB"
    to_alias: "Array MATLAB"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame este structura de date de bază în limbajul de programare R, folosită pe scară largă în analiza statistică, mineritul datelor și învățarea automată. R este instrumentul principal pentru calculul statistic și grafica, cu DataFrame oferind capacități puternice de manipulare a datelor, analiză statistică și vizualizare. Esențial pentru oamenii de știință ai datelor, statisticienii și cercetătorii care lucrează cu analiza datelor structurate."
    step1: "Încarcă fișiere de date R sau lipește cod DataFrame. Instrumentul parsează sintaxa R și extrage informațiile structurii DataFrame inclusiv tipurile coloanelor, numele rândurilor și conținutul datelor."
    step3: "Generează cod standard R DataFrame cu suport pentru specificațiile tipurilor de date, nivelurile factorilor, numele rândurilor/coloanelor și structurile de date specifice R. Codul generat poate fi executat direct în mediul R pentru analiza statistică și procesarea datelor."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Începe Conversia"
  start_generating: "Începe Generarea"
  api_docs: "Documentația API"
related:
  section_title: 'Mai Multe Convertoare {{ if and .from (ne .from "generator") }}{{ .from }} și {{ end }}{{ .to }}'
  section_description: 'Explorează mai multe convertoare pentru formatele {{ if and .from (ne .from "generator") }}{{ .from }} și {{ end }}{{ .to }}. Transformă datele tale între multiple formate cu instrumentele noastre profesionale de conversie online.'
  title: "{{ .from }} către {{ .to }}"
howto:
  step2: "Editează datele folosind editorul nostru avansat de tabele online cu funcții profesionale. Suportă ștergerea rândurilor goale, eliminarea duplicatelor, transpunerea datelor, sortarea, căutarea și înlocuirea regex, și previzualizarea în timp real. Toate modificările se convertesc automat în formatul %s cu rezultate precise și fiabile."
  section_title: "Cum să folosești {{ . }}"
  converter_description: "Învață să convertești {{ .from }} către {{ .to }} cu ghidul nostru pas cu pas. Convertor online profesional cu funcții avansate și previzualizare în timp real."
  generator_description: "Învață să creezi tabele {{ .to }} profesionale cu generatorul nostru online. Editare similară cu Excel, previzualizare în timp real și capacități de export instant."
extension:
  section_title: "Extensia de Detectare și Extragere a Tabelelor"
  section_description: "Extrage tabele de pe orice site web cu un singur clic. Convertește în 30+ formate incluzând Excel, CSV, JSON instant - nu este necesară copierea și lipirea."
  features:
    extraction_title: "Extragerea Tabelelor cu Un Clic"
    extraction_description: "Extrage instant tabele de pe orice pagină web fără copiere și lipire - extragerea profesională de date făcută simplu"
    formats_title: "Suport Convertor 30+ Formate"
    formats_description: "Convertește tabelele extrase în Excel, CSV, JSON, Markdown, SQL și multe altele cu convertorul nostru avansat de tabele"
    detection_title: "Detectarea Inteligentă a Tabelelor"
    detection_description: "Detectează automat și evidențiază tabelele pe orice pagină web pentru extragerea rapidă și conversia datelor"
  hover_tip: "✨ Treci cu mouse-ul peste orice tabel pentru a vedea iconița de extragere"
recommendations:
  section_title: "Recomandat de Universități și Profesioniști"
  section_description: "TableConvert este de încredere pentru profesioniștii din universități, instituții de cercetare și echipe de dezvoltare pentru conversia fiabilă a tabelelor și procesarea datelor."
  cards:
    university_title: "University of Wisconsin-Madison"
    university_description: "TableConvert.com - Instrument profesional gratuit online de conversie a tabelelor și formate de date"
    university_link: "Citește Articolul"
    facebook_title: "Comunitatea Profesioniștilor în Date"
    facebook_description: "Împărtășit și recomandat de analiștii de date și profesioniștii din grupurile de dezvoltatori Facebook"
    facebook_link: "Vezi Postarea"
    twitter_title: "Comunitatea Dezvoltatorilor"
    twitter_description: "Recomandat de @xiaoying_eth și alți dezvoltatori pe X (Twitter) pentru conversia tabelelor"
    twitter_link: "Vezi Tweet-ul"
faq:
  section_title: "Întrebări Frecvente"
  section_description: "Întrebări comune despre convertorul nostru gratuit online de tabele, formatele de date și procesul de conversie."
  what: "Ce este formatul %s?"
  howto_convert:
    question: "Cum să folosești {{ . }} gratuit?"
    answer: "Încarcă fișierul tău {{ .from }}, lipește datele sau extrage din paginile web folosind convertorul nostru gratuit online de tabele. Instrumentul nostru profesional de conversie transformă instant datele tale în formatul {{ .to }} cu previzualizare în timp real și funcții avansate de editare. Descarcă sau copiază rezultatul convertit imediat."
  security:
    question: "Sunt datele mele sigure când folosesc acest convertor online?"
    answer: "Absolut! Toate conversiile de tabele se întâmplă local în browser-ul tău - datele tale nu părăsesc niciodată dispozitivul tău. Convertorul nostru online procesează totul pe partea clientului, asigurând confidențialitatea completă și securitatea datelor. Niciun fișier nu este stocat pe serverele noastre."
  free:
    question: "Este TableConvert cu adevărat gratuit de folosit?"
    answer: "Da, TableConvert este complet gratuit! Toate funcțiile convertorului, editorul de tabele, instrumentele generatoare de date și opțiunile de export sunt disponibile fără cost, înregistrare sau taxe ascunse. Convertește fișiere nelimitate online gratuit."
  filesize:
    question: "Ce limite de dimensiune a fișierelor are convertorul online?"
    answer: "Convertorul nostru gratuit online de tabele suportă fișiere până la 10MB. Pentru fișiere mai mari, procesare în lot sau nevoi de întreprindere, folosește extensia noastră de browser sau serviciul API profesional cu limite mai mari."
stats:
  conversions: "Tabele Convertite"
  tables: "Tabele Generate"
  formats: "Formate de Fișiere de Date"
  rating: "Evaluarea Utilizatorilor"
