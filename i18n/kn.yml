site:
  fullname: "ಆನ್‌ಲೈನ್ ಟೇಬಲ್ ಕನ್ವರ್ಟ್"
  name: "TableConvert"
  subtitle: "ಉಚಿತ ಆನ್‌ಲೈನ್ ಟೇಬಲ್ ಕನ್ವರ್ಟರ್ ಮತ್ತು ಜೆನರೇಟರ್"
  intro: "TableConvert ಒಂದು ಉಚಿತ ಆನ್‌ಲೈನ್ ಟೇಬಲ್ ಕನ್ವರ್ಟರ್ ಮತ್ತು ಡೇಟಾ ಜೆನರೇಟರ್ ಟೂಲ್ ಆಗಿದ್ದು, Excel, CSV, JSON, Markdown, LaTeX, SQL ಮತ್ತು ಇನ್ನಷ್ಟು ಸೇರಿದಂತೆ 30+ ಫಾರ್ಮ್ಯಾಟ್‌ಗಳ ನಡುವೆ ಪರಿವರ್ತನೆಯನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
  followTwitter: "X ನಲ್ಲಿ ನಮ್ಮನ್ನು ಫಾಲೋ ಮಾಡಿ"
title:
  converter: "%s ರಿಂದ %s ಗೆ"
  generator: "%s ಜೆನರೇಟರ್"
post:
  tags:
    converter: "ಕನ್ವರ್ಟರ್"
    editor: "ಎಡಿಟರ್"
    generator: "ಜೆನರೇಟರ್"
    maker: "ಬಿಲ್ಡರ್"
  converter:
    title: "%s ಅನ್ನು %s ಗೆ ಆನ್‌ಲೈನ್‌ನಲ್ಲಿ ಕನ್ವರ್ಟ್ ಮಾಡಿ"
    short: "ಉಚಿತ ಮತ್ತು ಶಕ್ತಿಶಾಲಿ %s ರಿಂದ %s ಆನ್‌ಲೈನ್ ಟೂಲ್"
    intro: "ಬಳಸಲು ಸುಲಭವಾದ ಆನ್‌ಲೈನ್ %s ರಿಂದ %s ಕನ್ವರ್ಟರ್. ನಮ್ಮ ಅರ್ಥಗರ್ಭಿತ ಪರಿವರ್ತನೆ ಸಾಧನದೊಂದಿಗೆ ಟೇಬಲ್ ಡೇಟಾವನ್ನು ಸಲೀಸಾಗಿ ಪರಿವರ್ತಿಸಿ. ವೇಗವಾದ, ವಿಶ್ವಾಸಾರ್ಹ ಮತ್ತು ಬಳಕೆದಾರ-ಸ್ನೇಹಿ."
  generator:
    title: "ಆನ್‌ಲೈನ್ %s ಎಡಿಟರ್ ಮತ್ತು ಜೆನರೇಟರ್"
    short: "ಸಮಗ್ರ ವೈಶಿಷ್ಟ್ಯಗಳೊಂದಿಗೆ ವೃತ್ತಿಪರ %s ಆನ್‌ಲೈನ್ ಜೆನರೇಷನ್ ಟೂಲ್"
    intro: "ಬಳಸಲು ಸುಲಭವಾದ ಆನ್‌ಲೈನ್ %s ಜೆನರೇಟರ್ ಮತ್ತು ಟೇಬಲ್ ಎಡಿಟರ್. ನಮ್ಮ ಅರ್ಥಗರ್ಭಿತ ಸಾಧನ ಮತ್ತು ನೈಜ-ಸಮಯದ ಪೂರ್ವವೀಕ್ಷಣೆಯೊಂದಿಗೆ ವೃತ್ತಿಪರ ಡೇಟಾ ಟೇಬಲ್‌ಗಳನ್ನು ಸಲೀಸಾಗಿ ರಚಿಸಿ."
navbar:
  search:
    placeholder: "ಕನ್ವರ್ಟರ್ ಹುಡುಕಿ..."
  sponsor: "ನಮಗೆ ಕಾಫಿ ಖರೀದಿಸಿ"
  extension: "ಎಕ್ಸ್‌ಟೆನ್ಷನ್"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "ಡೇಟಾ ಮೂಲ"
    placeholder: "ನಿಮ್ಮ %s ಡೇಟಾವನ್ನು ಇಲ್ಲಿ ಅಂಟಿಸಿ ಅಥವಾ %s ಫೈಲ್‌ಗಳನ್ನು ಎಳೆಯಿರಿ"
    example: "ಉದಾಹರಣೆ"
    upload: "ಫೈಲ್ ಅಪ್‌ಲೋಡ್ ಮಾಡಿ"
    extract:
      enter: "ವೆಬ್ ಪುಟದಿಂದ ಹೊರತೆಗೆಯಿರಿ"
      intro: "ರಚನಾತ್ಮಕ ಡೇಟಾವನ್ನು ಸ್ವಯಂಚಾಲಿತವಾಗಿ ಹೊರತೆಗೆಯಲು ಟೇಬಲ್ ಡೇಟಾವನ್ನು ಹೊಂದಿರುವ ವೆಬ್ ಪುಟದ URL ಅನ್ನು ನಮೂದಿಸಿ"
      btn: "%s ಹೊರತೆಗೆಯಿರಿ"
    excel:
      sheet: "ವರ್ಕ್‌ಶೀಟ್"
      none: "ಯಾವುದೂ ಇಲ್ಲ"
  tableEditor:
    title: "ಆನ್‌ಲೈನ್ ಟೇಬಲ್ ಎಡಿಟರ್"
    undo: "ರದ್ದುಗೊಳಿಸಿ"
    redo: "ಮರುಮಾಡಿ"
    transpose: "ಟ್ರಾನ್ಸ್‌ಪೋಸ್"
    clear: "ತೆರವುಗೊಳಿಸಿ"
    deleteBlank: "ಖಾಲಿ ಅಳಿಸಿ"
    deleteDuplicate: "ನಕಲು ತೆಗೆದುಹಾಕಿ"
    uppercase: "ದೊಡ್ಡಕ್ಷರ"
    lowercase: "ಚಿಕ್ಕಕ್ಷರ"
    capitalize: "ಮೊದಲಕ್ಷರ ದೊಡ್ಡದು"
    replace:
      replace: "ಹುಡುಕಿ ಮತ್ತು ಬದಲಾಯಿಸಿ (Regex ಬೆಂಬಲಿತ)"
      subst: "ಇದರೊಂದಿಗೆ ಬದಲಾಯಿಸಿ..."
      btn: "ಎಲ್ಲವನ್ನೂ ಬದಲಾಯಿಸಿ"
  tableGenerator:
    title: "ಟೇಬಲ್ ಜೆನರೇಟರ್"
    sponsor: "ನಮಗೆ ಕಾಫಿ ಖರೀದಿಸಿ"
    copy: "ಕ್ಲಿಪ್‌ಬೋರ್ಡ್‌ಗೆ ನಕಲಿಸಿ"
    download: "ಫೈಲ್ ಡೌನ್‌ಲೋಡ್ ಮಾಡಿ"
    tooltip:
      html:
        escape: "ಪ್ರದರ್ಶನ ದೋಷಗಳನ್ನು ತಡೆಗಟ್ಟಲು HTML ವಿಶೇಷ ಅಕ್ಷರಗಳನ್ನು (&, <, >, \", ') ಎಸ್ಕೇಪ್ ಮಾಡಿ"
        div: "ಸಾಂಪ್ರದಾಯಿಕ TABLE ಟ್ಯಾಗ್‌ಗಳ ಬದಲಿಗೆ DIV+CSS ಲೇಔಟ್ ಬಳಸಿ, ರೆಸ್ಪಾನ್ಸಿವ್ ಡಿಸೈನ್‌ಗೆ ಹೆಚ್ಚು ಸೂಕ್ತ"
        minify: "ಸಂಕುಚಿತ HTML ಕೋಡ್ ಉತ್ಪಾದಿಸಲು ವೈಟ್‌ಸ್ಪೇಸ್ ಮತ್ತು ಲೈನ್ ಬ್ರೇಕ್‌ಗಳನ್ನು ತೆಗೆದುಹಾಕಿ"
        thead: "ಪ್ರಮಾಣಿತ ಟೇಬಲ್ ಹೆಡ್ (&lt;thead&gt;) ಮತ್ತು ಬಾಡಿ (&lt;tbody&gt;) ರಚನೆಯನ್ನು ಉತ್ಪಾದಿಸಿ"
        tableCaption: "ಟೇಬಲ್‌ನ ಮೇಲೆ ವಿವರಣಾತ್ಮಕ ಶೀರ್ಷಿಕೆ ಸೇರಿಸಿ (&lt;caption&gt; ಎಲಿಮೆಂಟ್)"
        tableClass: "ಸುಲಭ ಶೈಲಿ ಕಸ್ಟಮೈಸೇಶನ್‌ಗಾಗಿ ಟೇಬಲ್‌ಗೆ CSS ಕ್ಲಾಸ್ ಹೆಸರು ಸೇರಿಸಿ"
        tableId: "JavaScript ಮ್ಯಾನಿಪ್ಯುಲೇಶನ್‌ಗಾಗಿ ಟೇಬಲ್‌ಗೆ ಅನನ್ಯ ID ಗುರುತಿಸುವಿಕೆ ಸೆಟ್ ಮಾಡಿ"
      jira:
        escape: "Jira ಟೇಬಲ್ ಸಿಂಟ್ಯಾಕ್ಸ್‌ನೊಂದಿಗೆ ಸಂಘರ್ಷಗಳನ್ನು ತಪ್ಪಿಸಲು ಪೈಪ್ ಅಕ್ಷರಗಳನ್ನು (|) ಎಸ್ಕೇಪ್ ಮಾಡಿ"
      json:
        parsingJSON: "ಸೆಲ್‌ಗಳಲ್ಲಿನ JSON ಸ್ಟ್ರಿಂಗ್‌ಗಳನ್ನು ಬುದ್ಧಿವಂತಿಕೆಯಿಂದ ಆಬ್ಜೆಕ್ಟ್‌ಗಳಾಗಿ ಪಾರ್ಸ್ ಮಾಡಿ"
        minify: "ಫೈಲ್ ಗಾತ್ರವನ್ನು ಕಡಿಮೆ ಮಾಡಲು ಕಾಂಪ್ಯಾಕ್ಟ್ ಸಿಂಗಲ್-ಲೈನ್ JSON ಫಾರ್ಮ್ಯಾಟ್ ಉತ್ಪಾದಿಸಿ"
        format: "ಔಟ್‌ಪುಟ್ JSON ಡೇಟಾ ರಚನೆಯನ್ನು ಆಯ್ಕೆಮಾಡಿ: ಆಬ್ಜೆಕ್ಟ್ ಅರೇ, 2D ಅರೇ, ಇತ್ಯಾದಿ"
      latex:
        escape: "ಸರಿಯಾದ ಕಂಪೈಲೇಶನ್ ಖಚಿತಪಡಿಸಲು LaTeX ವಿಶೇಷ ಅಕ್ಷರಗಳನ್ನು (%, &, _, #, $, ಇತ್ಯಾದಿ) ಎಸ್ಕೇಪ್ ಮಾಡಿ"
        ht: "ಪುಟದಲ್ಲಿ ಟೇಬಲ್ ಸ್ಥಾನವನ್ನು ನಿಯಂತ್ರಿಸಲು ಫ್ಲೋಟಿಂಗ್ ಪೊಸಿಷನ್ ಪ್ಯಾರಾಮೀಟರ್ [!ht] ಸೇರಿಸಿ"
        mwe: "ಸಂಪೂರ್ಣ LaTeX ದಾಖಲೆಯನ್ನು ಉತ್ಪಾದಿಸಿ"
        tableAlign: "ಪುಟದಲ್ಲಿ ಟೇಬಲ್‌ನ ಸಮತಲ ಜೋಡಣೆಯನ್ನು ಸೆಟ್ ಮಾಡಿ"
        tableBorder: "ಟೇಬಲ್ ಬಾರ್ಡರ್ ಶೈಲಿಯನ್ನು ಕಾನ್ಫಿಗರ್ ಮಾಡಿ: ಬಾರ್ಡರ್ ಇಲ್ಲ, ಭಾಗಶಃ ಬಾರ್ಡರ್, ಪೂರ್ಣ ಬಾರ್ಡರ್"
        label: "\\ref{} ಕಮಾಂಡ್ ಕ್ರಾಸ್-ರೆಫರೆನ್ಸಿಂಗ್‌ಗಾಗಿ ಟೇಬಲ್ ಲೇಬಲ್ ಸೆಟ್ ಮಾಡಿ"
        caption: "ಟೇಬಲ್‌ನ ಮೇಲೆ ಅಥವಾ ಕೆಳಗೆ ಪ್ರದರ್ಶಿಸಲು ಟೇಬಲ್ ಕ್ಯಾಪ್ಶನ್ ಸೆಟ್ ಮಾಡಿ"
        location: "ಟೇಬಲ್ ಕ್ಯಾಪ್ಶನ್ ಪ್ರದರ್ಶನ ಸ್ಥಾನವನ್ನು ಆಯ್ಕೆಮಾಡಿ: ಮೇಲೆ ಅಥವಾ ಕೆಳಗೆ"
        tableType: "ಟೇಬಲ್ ಎನ್ವಿರಾನ್‌ಮೆಂಟ್ ಪ್ರಕಾರವನ್ನು ಆಯ್ಕೆಮಾಡಿ: tabular, longtable, array, ಇತ್ಯಾದಿ"
      markdown:
        escape: "ಫಾರ್ಮ್ಯಾಟ್ ಸಂಘರ್ಷಗಳನ್ನು ತಪ್ಪಿಸಲು Markdown ವಿಶೇಷ ಅಕ್ಷರಗಳನ್ನು (*, _, |, \\, ಇತ್ಯಾದಿ) ಎಸ್ಕೇಪ್ ಮಾಡಿ"
        pretty: "ಹೆಚ್ಚು ಸುಂದರವಾದ ಟೇಬಲ್ ಫಾರ್ಮ್ಯಾಟ್ ಉತ್ಪಾದಿಸಲು ಕಾಲಮ್ ಅಗಲಗಳನ್ನು ಸ್ವಯಂ-ಜೋಡಿಸಿ"
        simple: "ಬಾಹ್ಯ ಬಾರ್ಡರ್ ವರ್ಟಿಕಲ್ ಲೈನ್‌ಗಳನ್ನು ಬಿಟ್ಟುಬಿಟ್ಟು ಸರಳೀಕೃತ ಸಿಂಟ್ಯಾಕ್ಸ್ ಬಳಸಿ"
        boldFirstRow: "ಮೊದಲ ಸಾಲಿನ ಪಠ್ಯವನ್ನು ದಪ್ಪಗೆ ಮಾಡಿ"
        boldFirstColumn: "ಮೊದಲ ಕಾಲಮ್‌ನ ಪಠ್ಯವನ್ನು ದಪ್ಪಗೆ ಮಾಡಿ"
        firstHeader: "ಮೊದಲ ಸಾಲನ್ನು ಹೆಡರ್ ಆಗಿ ಪರಿಗಣಿಸಿ ಮತ್ತು ಸೆಪರೇಟರ್ ಲೈನ್ ಸೇರಿಸಿ"
        textAlign: "ಕಾಲಮ್ ಪಠ್ಯ ಜೋಡಣೆಯನ್ನು ಸೆಟ್ ಮಾಡಿ: ಎಡ, ಮಧ್ಯ, ಬಲ"
        multilineHandling: "ಮಲ್ಟಿಲೈನ್ ಪಠ್ಯ ನಿರ್ವಹಣೆ: ಲೈನ್ ಬ್ರೇಕ್‌ಗಳನ್ನು ಸಂರಕ್ಷಿಸಿ, \\n ಗೆ ಎಸ್ಕೇಪ್ ಮಾಡಿ, &lt;br&gt; ಟ್ಯಾಗ್‌ಗಳನ್ನು ಬಳಸಿ"

        includeLineNumbers: "ಟೇಬಲ್‌ನ ಎಡಭಾಗದಲ್ಲಿ ಲೈನ್ ನಂಬರ್ ಕಾಲಮ್ ಸೇರಿಸಿ"
      magic:
        builtin: "ಪೂರ್ವನಿರ್ಧಾರಿತ ಸಾಮಾನ್ಯ ಟೆಂಪ್ಲೇಟ್ ಫಾರ್ಮ್ಯಾಟ್‌ಗಳನ್ನು ಆಯ್ಕೆಮಾಡಿ"
        rowsTpl: "<table> <tr> <th>ಮ್ಯಾಜಿಕ್ ಸಿಂಟ್ಯಾಕ್ಸ್</th> <th>ವಿವರಣೆ</th> <th>JS ಮೆಥಡ್‌ಗಳ ಬೆಂಬಲ</th> </tr> <tr> <td>{h1} {h2} ...</td> <td><b>ಹೆಡಿಂಗ್</b>ನ 1ನೇ, 2ನೇ ... ಫೀಲ್ಡ್, ಅಂದರೆ {hA} {hB} ...</td> <td>ಸ್ಟ್ರಿಂಗ್ ಮೆಥಡ್‌ಗಳು</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>ಪ್ರಸ್ತುತ ಸಾಲಿನ 1ನೇ, 2ನೇ ... ಫೀಲ್ಡ್, ಅಂದರೆ {$A} {$B} ...</td> <td>ಸ್ಟ್ರಿಂಗ್ ಮೆಥಡ್‌ಗಳು</td> </tr> <tr> <td>{F,} {F;}</td> <td><b>F</b> ನಂತರದ ಸ್ಟ್ರಿಂಗ್‌ನಿಂದ ಪ್ರಸ್ತುತ ಸಾಲನ್ನು ವಿಭಜಿಸಿ</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>ಪ್ರಸ್ತುತ <b>ಸಾಲಿನ</b> ಲೈನ್ <b>ಸಂಖ್ಯೆ</b> 1 ಅಥವಾ 100 ರಿಂದ</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>ಸಾಲುಗಳ</b> <b>ಅಂತಿಮ</b> ಲೈನ್ <b>ಸಂಖ್ಯೆ</b> </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>JavaScript ಕೋಡ್ <b>ಎಕ್ಸಿಕ್ಯೂಟ್</b> ಮಾಡಿ, ಉದಾ: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> ಬ್ರೇಸ್‌ಗಳನ್ನು {...} ಔಟ್‌ಪುಟ್ ಮಾಡಲು ಬ್ಯಾಕ್‌ಸ್ಲ್ಯಾಶ್ <b>\\</b> ಬಳಸಿ </td> <td></td> </tr></table>"
        headerTpl: "ಹೆಡರ್ ವಿಭಾಗಕ್ಕಾಗಿ ಕಸ್ಟಮ್ ಔಟ್‌ಪುಟ್ ಟೆಂಪ್ಲೇಟ್"
        footerTpl: "ಫೂಟರ್ ವಿಭಾಗಕ್ಕಾಗಿ ಕಸ್ಟಮ್ ಔಟ್‌ಪುಟ್ ಟೆಂಪ್ಲೇಟ್"
      textile:
        escape: "ಫಾರ್ಮ್ಯಾಟ್ ಸಂಘರ್ಷಗಳನ್ನು ತಪ್ಪಿಸಲು Textile ಸಿಂಟ್ಯಾಕ್ಸ್ ಅಕ್ಷರಗಳನ್ನು (|, ., -, ^) ಎಸ್ಕೇಪ್ ಮಾಡಿ"
        rowHeader: "ಮೊದಲ ಸಾಲನ್ನು ಹೆಡರ್ ಸಾಲಾಗಿ ಸೆಟ್ ಮಾಡಿ"
        thead: "ಟೇಬಲ್ ಹೆಡ್ ಮತ್ತು ಬಾಡಿಗಾಗಿ Textile ಸಿಂಟ್ಯಾಕ್ಸ್ ಮಾರ್ಕರ್‌ಗಳನ್ನು ಸೇರಿಸಿ"
      xml:
        escape: "ಮಾನ್ಯ XML ಖಚಿತಪಡಿಸಲು XML ವಿಶೇಷ ಅಕ್ಷರಗಳನ್ನು (&lt;, &gt;, &amp;, \", ') ಎಸ್ಕೇಪ್ ಮಾಡಿ"
        minify: "ಹೆಚ್ಚುವರಿ ವೈಟ್‌ಸ್ಪೇಸ್ ತೆಗೆದುಹಾಕಿ ಸಂಕುಚಿತ XML ಔಟ್‌ಪುಟ್ ಉತ್ಪಾದಿಸಿ"
        rootElement: "XML ರೂಟ್ ಎಲಿಮೆಂಟ್ ಟ್ಯಾಗ್ ಹೆಸರನ್ನು ಸೆಟ್ ಮಾಡಿ"
        rowElement: "ಡೇಟಾದ ಪ್ರತಿ ಸಾಲಿಗಾಗಿ XML ಎಲಿಮೆಂಟ್ ಟ್ಯಾಗ್ ಹೆಸರನ್ನು ಸೆಟ್ ಮಾಡಿ"
        declaration: "XML ಘೋಷಣೆ ಹೆಡರ್ ಸೇರಿಸಿ (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "ಚೈಲ್ಡ್ ಎಲಿಮೆಂಟ್‌ಗಳ ಬದಲಿಗೆ XML ಗುಣಲಕ್ಷಣಗಳಾಗಿ ಡೇಟಾವನ್ನು ಔಟ್‌ಪುಟ್ ಮಾಡಿ"
        cdata: "ವಿಶೇಷ ಅಕ್ಷರಗಳನ್ನು ರಕ್ಷಿಸಲು CDATA ನೊಂದಿಗೆ ಪಠ್ಯ ವಿಷಯವನ್ನು ಸುತ್ತಿ"
        encoding: "XML ದಾಖಲೆಗಾಗಿ ಅಕ್ಷರ ಎನ್‌ಕೋಡಿಂಗ್ ಫಾರ್ಮ್ಯಾಟ್ ಸೆಟ್ ಮಾಡಿ"
        indentation: "XML ಇಂಡೆಂಟೇಶನ್ ಅಕ್ಷರವನ್ನು ಆಯ್ಕೆಮಾಡಿ: ಸ್ಪೇಸ್‌ಗಳು ಅಥವಾ ಟ್ಯಾಬ್‌ಗಳು"
      yaml:
        indentSize: "YAML ಶ್ರೇಣಿ ಇಂಡೆಂಟೇಶನ್‌ಗಾಗಿ ಸ್ಪೇಸ್‌ಗಳ ಸಂಖ್ಯೆಯನ್ನು ಸೆಟ್ ಮಾಡಿ (ಸಾಮಾನ್ಯವಾಗಿ 2 ಅಥವಾ 4)"
        arrayStyle: "ಅರೇ ಫಾರ್ಮ್ಯಾಟ್: ಬ್ಲಾಕ್ (ಪ್ರತಿ ಲೈನ್‌ಗೆ ಒಂದು ಐಟಂ) ಅಥವಾ ಫ್ಲೋ (ಇನ್‌ಲೈನ್ ಫಾರ್ಮ್ಯಾಟ್)"
        quotationStyle: "ಸ್ಟ್ರಿಂಗ್ ಕೋಟ್ ಶೈಲಿ: ಕೋಟ್‌ಗಳಿಲ್ಲ, ಸಿಂಗಲ್ ಕೋಟ್‌ಗಳು, ಡಬಲ್ ಕೋಟ್‌ಗಳು"
      pdf:
        theme: "ವೃತ್ತಿಪರ ದಾಖಲೆಗಳಿಗಾಗಿ PDF ಟೇಬಲ್ ದೃಶ್ಯ ಶೈಲಿಯನ್ನು ಆಯ್ಕೆಮಾಡಿ"
        headerColor: "PDF ಟೇಬಲ್ ಹೆಡರ್ ಹಿನ್ನೆಲೆ ಬಣ್ಣವನ್ನು ಆಯ್ಕೆಮಾಡಿ"
        showHead: "PDF ಪುಟಗಳಲ್ಲಿ ಹೆಡರ್ ಪ್ರದರ್ಶನವನ್ನು ನಿಯಂತ್ರಿಸಿ"
        docTitle: "PDF ದಾಖಲೆಗಾಗಿ ಐಚ್ಛಿಕ ಶೀರ್ಷಿಕೆ"
        docDescription: "PDF ದಾಖಲೆಗಾಗಿ ಐಚ್ಛಿಕ ವಿವರಣೆ ಪಠ್ಯ"
      csv:
        bom: "Excel ಮತ್ತು ಇತರ ಸಾಫ್ಟ್‌ವೇರ್‌ಗಳಿಗೆ ಎನ್‌ಕೋಡಿಂಗ್ ಗುರುತಿಸಲು ಸಹಾಯ ಮಾಡಲು UTF-8 ಬೈಟ್ ಆರ್ಡರ್ ಮಾರ್ಕ್ ಸೇರಿಸಿ"
      excel:
        autoWidth: "ವಿಷಯದ ಆಧಾರದ ಮೇಲೆ ಕಾಲಮ್ ಅಗಲವನ್ನು ಸ್ವಯಂಚಾಲಿತವಾಗಿ ಹೊಂದಿಸಿ"
        protectSheet: "ಪಾಸ್‌ವರ್ಡ್‌ನೊಂದಿಗೆ ವರ್ಕ್‌ಶೀಟ್ ರಕ್ಷಣೆಯನ್ನು ಸಕ್ರಿಯಗೊಳಿಸಿ: tableconvert.com"
      sql:
        primaryKey: "CREATE TABLE ಸ್ಟೇಟ್‌ಮೆಂಟ್‌ಗಾಗಿ ಪ್ರಾಥಮಿಕ ಕೀ ಫೀಲ್ಡ್ ಹೆಸರನ್ನು ನಿರ್ದಿಷ್ಟಪಡಿಸಿ"
        dialect: "ಡೇಟಾಬೇಸ್ ಪ್ರಕಾರವನ್ನು ಆಯ್ಕೆಮಾಡಿ, ಕೋಟ್ ಮತ್ತು ಡೇಟಾ ಟೈಪ್ ಸಿಂಟ್ಯಾಕ್ಸ್‌ನ ಮೇಲೆ ಪರಿಣಾಮ ಬೀರುತ್ತದೆ"
      ascii:
        forceSep: "ಡೇಟಾದ ಪ್ರತಿ ಸಾಲಿನ ನಡುವೆ ಸೆಪರೇಟರ್ ಲೈನ್‌ಗಳನ್ನು ಬಲವಂತಪಡಿಸಿ"
        style: "ASCII ಟೇಬಲ್ ಬಾರ್ಡರ್ ಡ್ರಾಯಿಂಗ್ ಶೈಲಿಯನ್ನು ಆಯ್ಕೆಮಾಡಿ"
        comment: "ಸಂಪೂರ್ಣ ಟೇಬಲ್ ಅನ್ನು ಸುತ್ತಲು ಕಾಮೆಂಟ್ ಮಾರ್ಕರ್‌ಗಳನ್ನು ಸೇರಿಸಿ"
      mediawiki:
        minify: "ಹೆಚ್ಚುವರಿ ವೈಟ್‌ಸ್ಪೇಸ್ ತೆಗೆದುಹಾಕಿ ಔಟ್‌ಪುಟ್ ಕೋಡ್ ಸಂಕುಚಿತಗೊಳಿಸಿ"
        header: "ಮೊದಲ ಸಾಲನ್ನು ಹೆಡರ್ ಶೈಲಿಯಾಗಿ ಗುರುತಿಸಿ"
        sort: "ಟೇಬಲ್ ಕ್ಲಿಕ್ ಸಾರ್ಟಿಂಗ್ ಕಾರ್ಯಚಟುವಟಿಕೆಯನ್ನು ಸಕ್ರಿಯಗೊಳಿಸಿ"
      asciidoc:
        minify: "AsciiDoc ಫಾರ್ಮ್ಯಾಟ್ ಔಟ್‌ಪುಟ್ ಸಂಕುಚಿತಗೊಳಿಸಿ"
        firstHeader: "ಮೊದಲ ಸಾಲನ್ನು ಹೆಡರ್ ಸಾಲಾಗಿ ಸೆಟ್ ಮಾಡಿ"
        lastFooter: "ಕೊನೆಯ ಸಾಲನ್ನು ಫೂಟರ್ ಸಾಲಾಗಿ ಸೆಟ್ ಮಾಡಿ"
        title: "ಟೇಬಲ್‌ಗೆ ಶೀರ್ಷಿಕೆ ಪಠ್ಯವನ್ನು ಸೇರಿಸಿ"
      tracwiki:
        rowHeader: "ಮೊದಲ ಸಾಲನ್ನು ಹೆಡರ್ ಆಗಿ ಸೆಟ್ ಮಾಡಿ"
        colHeader: "ಮೊದಲ ಕಾಲಮ್ ಅನ್ನು ಹೆಡರ್ ಆಗಿ ಸೆಟ್ ಮಾಡಿ"
      bbcode:
        minify: "BBCode ಔಟ್‌ಪುಟ್ ಫಾರ್ಮ್ಯಾಟ್ ಸಂಕುಚಿತಗೊಳಿಸಿ"
      restructuredtext:
        style: "reStructuredText ಟೇಬಲ್ ಬಾರ್ಡರ್ ಶೈಲಿಯನ್ನು ಆಯ್ಕೆಮಾಡಿ"
        forceSep: "ಸೆಪರೇಟರ್ ಲೈನ್‌ಗಳನ್ನು ಬಲವಂತಪಡಿಸಿ"
    label:
      ascii:
        forceSep: "ಸಾಲು ವಿಭಾಜಕಗಳು"
        style: "ಬಾರ್ಡರ್ ಶೈಲಿ"
        comment: "ಕಾಮೆಂಟ್ ಸುತ್ತುವಿಕೆ"
      restructuredtext:
        style: "ಬಾರ್ಡರ್ ಶೈಲಿ"
        forceSep: "ವಿಭಾಜಕಗಳನ್ನು ಬಲವಂತಪಡಿಸಿ"
      bbcode:
        minify: "ಔಟ್‌ಪುಟ್ ಕಿರುಗೊಳಿಸಿ"
      csv:
        doubleQuote: "ಡಬಲ್ ಕೋಟ್ ಸುತ್ತುವಿಕೆ"
        delimiter: "ಫೀಲ್ಡ್ ವಿಭಾಜಕ"
        bom: "UTF-8 BOM"
        valueDelimiter: "ಮೌಲ್ಯ ವಿಭಾಜಕ"
        rowDelimiter: "ಸಾಲು ವಿಭಾಜಕ"
        prefix: "ಸಾಲು ಪೂರ್ವಪ್ರತ್ಯಯ"
        suffix: "ಸಾಲು ಪ್ರತ್ಯಯ"
      excel:
        autoWidth: "ಸ್ವಯಂ ಅಗಲ"
        textFormat: "ಪಠ್ಯ ಫಾರ್ಮ್ಯಾಟ್"
        protectSheet: "ಶೀಟ್ ರಕ್ಷಿಸಿ"
        boldFirstRow: "ಮೊದಲ ಸಾಲು ದಪ್ಪ"
        boldFirstColumn: "ಮೊದಲ ಕಾಲಮ್ ದಪ್ಪ"
        sheetName: "ಶೀಟ್ ಹೆಸರು"
      html:
        escape: "HTML ಅಕ್ಷರಗಳನ್ನು ಎಸ್ಕೇಪ್ ಮಾಡಿ"
        div: "DIV ಟೇಬಲ್"
        minify: "ಕೋಡ್ ಕಿರುಗೊಳಿಸಿ"
        thead: "ಟೇಬಲ್ ಹೆಡ್ ರಚನೆ"
        tableCaption: "ಟೇಬಲ್ ಕ್ಯಾಪ್ಶನ್"
        tableClass: "ಟೇಬಲ್ ಕ್ಲಾಸ್"
        tableId: "ಟೇಬಲ್ ID"
        rowHeader: "ಸಾಲು ಹೆಡರ್"
        colHeader: "ಕಾಲಮ್ ಹೆಡರ್"
      jira:
        escape: "ಅಕ್ಷರಗಳನ್ನು ಎಸ್ಕೇಪ್ ಮಾಡಿ"
        rowHeader: "ಸಾಲು ಹೆಡರ್"
        colHeader: "ಕಾಲಮ್ ಹೆಡರ್"
      json:
        parsingJSON: "JSON ಪಾರ್ಸ್ ಮಾಡಿ"
        minify: "ಔಟ್‌ಪುಟ್ ಕಿರುಗೊಳಿಸಿ"
        format: "ಡೇಟಾ ಫಾರ್ಮ್ಯಾಟ್"
        rootName: "ರೂಟ್ ಆಬ್ಜೆಕ್ಟ್ ಹೆಸರು"
        indentSize: "ಇಂಡೆಂಟ್ ಗಾತ್ರ"
      jsonlines:
        parsingJSON: "JSON ಪಾರ್ಸ್ ಮಾಡಿ"
        format: "ಡೇಟಾ ಫಾರ್ಮ್ಯಾಟ್"
      latex:
        escape: "LaTeX ಟೇಬಲ್ ಅಕ್ಷರಗಳನ್ನು ಎಸ್ಕೇಪ್ ಮಾಡಿ"
        ht: "ಫ್ಲೋಟ್ ಸ್ಥಾನ"
        mwe: "ಸಂಪೂರ್ಣ ದಾಖಲೆ"
        tableAlign: "ಟೇಬಲ್ ಜೋಡಣೆ"
        tableBorder: "ಬಾರ್ಡರ್ ಶೈಲಿ"
        label: "ಉಲ್ಲೇಖ ಲೇಬಲ್"
        caption: "ಟೇಬಲ್ ಕ್ಯಾಪ್ಶನ್"
        location: "ಕ್ಯಾಪ್ಶನ್ ಸ್ಥಾನ"
        tableType: "ಟೇಬಲ್ ಪ್ರಕಾರ"
        boldFirstRow: "ಮೊದಲ ಸಾಲು ದಪ್ಪ"
        boldFirstColumn: "ಮೊದಲ ಕಾಲಮ್ ದಪ್ಪ"
        textAlign: "ಪಠ್ಯ ಜೋಡಣೆ"
        borders: "ಬಾರ್ಡರ್ ಸೆಟ್ಟಿಂಗ್‌ಗಳು"
      markdown:
        escape: "ಅಕ್ಷರಗಳನ್ನು ಎಸ್ಕೇಪ್ ಮಾಡಿ"
        pretty: "ಸುಂದರ Markdown ಟೇಬಲ್"
        simple: "ಸರಳ Markdown ಫಾರ್ಮ್ಯಾಟ್"
        boldFirstRow: "ಮೊದಲ ಸಾಲು ದಪ್ಪ"
        boldFirstColumn: "ಮೊದಲ ಕಾಲಮ್ ದಪ್ಪ"
        firstHeader: "ಮೊದಲ ಹೆಡರ್"
        textAlign: "ಪಠ್ಯ ಜೋಡಣೆ"
        multilineHandling: "ಮಲ್ಟಿಲೈನ್ ನಿರ್ವಹಣೆ"

        includeLineNumbers: "ಲೈನ್ ಸಂಖ್ಯೆಗಳನ್ನು ಸೇರಿಸಿ"
        align: "ಜೋಡಣೆ"
      mediawiki:
        minify: "ಕೋಡ್ ಕಿರುಗೊಳಿಸಿ"
        header: "ಹೆಡರ್ ಮಾರ್ಕಪ್"
        sort: "ವಿಂಗಡಿಸಬಹುದಾದ"
      asciidoc:
        minify: "ಫಾರ್ಮ್ಯಾಟ್ ಕಿರುಗೊಳಿಸಿ"
        firstHeader: "ಮೊದಲ ಹೆಡರ್"
        lastFooter: "ಕೊನೆಯ ಫೂಟರ್"
        title: "ಟೇಬಲ್ ಶೀರ್ಷಿಕೆ"
      tracwiki:
        rowHeader: "ಸಾಲು ಹೆಡರ್"
        colHeader: "ಕಾಲಮ್ ಹೆಡರ್"
      sql:
        drop: "ಟೇಬಲ್ ಡ್ರಾಪ್ ಮಾಡಿ (ಇದ್ದರೆ)"
        create: "ಟೇಬಲ್ ರಚಿಸಿ"
        oneInsert: "ಬ್ಯಾಚ್ ಇನ್ಸರ್ಟ್"
        table: "ಟೇಬಲ್ ಹೆಸರು"
        dialect: "ಡೇಟಾಬೇಸ್ ಪ್ರಕಾರ"
        primaryKey: "ಪ್ರಾಥಮಿಕ ಕೀ"
      magic:
        builtin: "ಅಂತರ್ನಿರ್ಮಿತ ಟೆಂಪ್ಲೇಟ್"
        rowsTpl: "ಸಾಲು ಟೆಂಪ್ಲೇಟ್, ಸಿಂಟ್ಯಾಕ್ಸ್ ->"
        headerTpl: "ಹೆಡರ್ ಟೆಂಪ್ಲೇಟ್"
        footerTpl: "ಫೂಟರ್ ಟೆಂಪ್ಲೇಟ್"
      textile:
        escape: "ಅಕ್ಷರಗಳನ್ನು ಎಸ್ಕೇಪ್ ಮಾಡಿ"
        rowHeader: "ಸಾಲು ಹೆಡರ್"
        thead: "ಟೇಬಲ್ ಹೆಡ್ ಸಿಂಟ್ಯಾಕ್ಸ್"
      xml:
        escape: "XML ಅಕ್ಷರಗಳನ್ನು ಎಸ್ಕೇಪ್ ಮಾಡಿ"
        minify: "ಔಟ್‌ಪುಟ್ ಕಿರುಗೊಳಿಸಿ"
        rootElement: "ರೂಟ್ ಎಲಿಮೆಂಟ್"
        rowElement: "ಸಾಲು ಎಲಿಮೆಂಟ್"
        declaration: "XML ಘೋಷಣೆ"
        attributes: "ಗುಣಲಕ್ಷಣ ಮೋಡ್"
        cdata: "CDATA ಸುತ್ತುವಿಕೆ"
        encoding: "ಎನ್‌ಕೋಡಿಂಗ್"
        indentSize: "ಇಂಡೆಂಟ್ ಗಾತ್ರ"
      yaml:
        indentSize: "ಇಂಡೆಂಟ್ ಗಾತ್ರ"
        arrayStyle: "ಅರೇ ಶೈಲಿ"
        quotationStyle: "ಕೋಟ್ ಶೈಲಿ"
      pdf:
        theme: "PDF ಟೇಬಲ್ ಥೀಮ್"
        headerColor: "PDF ಹೆಡರ್ ಬಣ್ಣ"
        showHead: "PDF ಹೆಡರ್ ಪ್ರದರ್ಶನ"
        docTitle: "PDF ದಾಖಲೆ ಶೀರ್ಷಿಕೆ"
        docDescription: "PDF ದಾಖಲೆ ವಿವರಣೆ"
sidebar:
  all: "ಎಲ್ಲಾ ಕನ್ವರ್ಷನ್ ಟೂಲ್‌ಗಳು"
  dataSource:
    title: "ಡೇಟಾ ಮೂಲ"
    description:
      converter: "%s ಅನ್ನು %s ಗೆ ಕನ್ವರ್ಷನ್‌ಗಾಗಿ ಆಮದು ಮಾಡಿ. ಫೈಲ್ ಅಪ್‌ಲೋಡ್, ಆನ್‌ಲೈನ್ ಎಡಿಟಿಂಗ್ ಮತ್ತು ವೆಬ್ ಡೇಟಾ ಹೊರತೆಗೆಯುವಿಕೆಯನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
      generator: "ಮ್ಯಾನುಯಲ್ ಇನ್‌ಪುಟ್, ಫೈಲ್ ಆಮದು ಮತ್ತು ಟೆಂಪ್ಲೇಟ್ ಜೆನರೇಷನ್ ಸೇರಿದಂತೆ ಬಹು ಇನ್‌ಪುಟ್ ವಿಧಾನಗಳ ಬೆಂಬಲದೊಂದಿಗೆ ಟೇಬಲ್ ಡೇಟಾವನ್ನು ರಚಿಸಿ."
  tableEditor:
    title: "ಆನ್‌ಲೈನ್ ಟೇಬಲ್ ಎಡಿಟರ್"
    description:
      converter: "ನಮ್ಮ ಟೇಬಲ್ ಎಡಿಟರ್ ಬಳಸಿ %s ಅನ್ನು ಆನ್‌ಲೈನ್‌ನಲ್ಲಿ ಪ್ರಕ್ರಿಯೆಗೊಳಿಸಿ. ಖಾಲಿ ಸಾಲುಗಳನ್ನು ಅಳಿಸುವುದು, ನಕಲು ತೆಗೆದುಹಾಕುವುದು, ವಿಂಗಡಿಸುವುದು ಮತ್ತು ಹುಡುಕಿ ಮತ್ತು ಬದಲಾಯಿಸುವುದರ ಬೆಂಬಲದೊಂದಿಗೆ Excel ರೀತಿಯ ಕಾರ್ಯಾಚರಣೆ ಅನುಭವ."
      generator: "Excel ರೀತಿಯ ಕಾರ್ಯಾಚರಣೆ ಅನುಭವವನ್ನು ಒದಗಿಸುವ ಶಕ್ತಿಶಾಲಿ ಆನ್‌ಲೈನ್ ಟೇಬಲ್ ಎಡಿಟರ್. ಖಾಲಿ ಸಾಲುಗಳನ್ನು ಅಳಿಸುವುದು, ನಕಲು ತೆಗೆದುಹಾಕುವುದು, ವಿಂಗಡಿಸುವುದು ಮತ್ತು ಹುಡುಕಿ ಮತ್ತು ಬದಲಾಯಿಸುವುದನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
  tableGenerator:
    title: "ಟೇಬಲ್ ಜೆನರೇಟರ್"
    description:
      converter: "ಟೇಬಲ್ ಜೆನರೇಟರ್‌ನ ರಿಯಲ್-ಟೈಮ್ ಪ್ರಿವ್ಯೂದೊಂದಿಗೆ %s ಅನ್ನು ತ್ವರಿತವಾಗಿ ಉತ್ಪಾದಿಸಿ. ಸಮೃದ್ಧ ಎಕ್ಸ್‌ಪೋರ್ಟ್ ಆಯ್ಕೆಗಳು, ಒಂದು ಕ್ಲಿಕ್‌ನಲ್ಲಿ ನಕಲು ಮತ್ತು ಡೌನ್‌ಲೋಡ್."
      generator: "ವಿವಿಧ ಬಳಕೆಯ ಸನ್ನಿವೇಶಗಳನ್ನು ಪೂರೈಸಲು %s ಡೇಟಾವನ್ನು ಬಹು ಫಾರ್ಮ್ಯಾಟ್‌ಗಳಲ್ಲಿ ಎಕ್ಸ್‌ಪೋರ್ಟ್ ಮಾಡಿ. ಕಸ್ಟಮ್ ಆಯ್ಕೆಗಳು ಮತ್ತು ರಿಯಲ್-ಟೈಮ್ ಪ್ರಿವ್ಯೂವನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
footer:
  changelog: "ಬದಲಾವಣೆ ಲಾಗ್"
  sponsor: "ಪ್ರಾಯೋಜಕರು"
  contact: "ನಮ್ಮನ್ನು ಸಂಪರ್ಕಿಸಿ"
  privacyPolicy: "ಗೌಪ್ಯತಾ ನೀತಿ"
  about: "ಬಗ್ಗೆ"
  resources: "ಸಂಪನ್ಮೂಲಗಳು"
  popularConverters: "ಜನಪ್ರಿಯ ಕನ್ವರ್ಟರ್‌ಗಳು"
  popularGenerators: "ಜನಪ್ರಿಯ ಜೆನರೇಟರ್‌ಗಳು"
  dataSecurity: "ನಿಮ್ಮ ಡೇಟಾ ಸುರಕ್ಷಿತವಾಗಿದೆ - ಎಲ್ಲಾ ಪರಿವರ್ತನೆಗಳು ನಿಮ್ಮ ಬ್ರೌಸರ್‌ನಲ್ಲಿ ಚಲಿಸುತ್ತವೆ."
converters:
  Markdown:
    alias: "Markdown ಟೇಬಲ್"
    what: "Markdown ಎಂಬುದು ತಾಂತ್ರಿಕ ದಾಖಲೀಕರಣ, ಬ್ಲಾಗ್ ವಿಷಯ ರಚನೆ ಮತ್ತು ವೆಬ್ ಅಭಿವೃದ್ಧಿಗಾಗಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುವ ಹಗುರವಾದ ಮಾರ್ಕಪ್ ಭಾಷೆಯಾಗಿದೆ. ಇದರ ಟೇಬಲ್ ಸಿಂಟ್ಯಾಕ್ಸ್ ಸಂಕ್ಷಿಪ್ತ ಮತ್ತು ಅರ್ಥಗರ್ಭಿತವಾಗಿದೆ, ಪಠ್ಯ ಜೋಡಣೆ, ಲಿಂಕ್ ಎಂಬೆಡಿಂಗ್ ಮತ್ತು ಫಾರ್ಮ್ಯಾಟಿಂಗ್ ಅನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ. ಇದು ಪ್ರೋಗ್ರಾಮರ್‌ಗಳು ಮತ್ತು ತಾಂತ್ರಿಕ ಬರಹಗಾರರಿಗೆ ಆದ್ಯತೆಯ ಸಾಧನವಾಗಿದೆ, GitHub, GitLab ಮತ್ತು ಇತರ ಕೋಡ್ ಹೋಸ್ಟಿಂಗ್ ಪ್ಲಾಟ್‌ಫಾರ್ಮ್‌ಗಳೊಂದಿಗೆ ಸಂಪೂರ್ಣವಾಗಿ ಹೊಂದಿಕೊಳ್ಳುತ್ತದೆ."
    step1: "Markdown ಟೇಬಲ್ ಡೇಟಾವನ್ನು ಡೇಟಾ ಮೂಲ ಪ್ರದೇಶಕ್ಕೆ ಅಂಟಿಸಿ, ಅಥವಾ ಅಪ್‌ಲೋಡ್‌ಗಾಗಿ .md ಫೈಲ್‌ಗಳನ್ನು ನೇರವಾಗಿ ಎಳೆದು ಬಿಡಿ. ಟೂಲ್ ಸ್ವಯಂಚಾಲಿತವಾಗಿ ಟೇಬಲ್ ರಚನೆ ಮತ್ತು ಫಾರ್ಮ್ಯಾಟಿಂಗ್ ಅನ್ನು ಪಾರ್ಸ್ ಮಾಡುತ್ತದೆ, ಸಂಕೀರ್ಣ ನೆಸ್ಟೆಡ್ ವಿಷಯ ಮತ್ತು ವಿಶೇಷ ಅಕ್ಷರ ನಿರ್ವಹಣೆಯನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
    step3: "ಬಹು ಜೋಡಣೆ ವಿಧಾನಗಳು, ಪಠ್ಯ ದಪ್ಪಗೊಳಿಸುವಿಕೆ, ಲೈನ್ ಸಂಖ್ಯೆ ಸೇರ್ಪಡೆ ಮತ್ತು ಇತರ ಸುಧಾರಿತ ಫಾರ್ಮ್ಯಾಟ್ ಸೆಟ್ಟಿಂಗ್‌ಗಳನ್ನು ಬೆಂಬಲಿಸುವ ಪ್ರಮಾಣಿತ Markdown ಟೇಬಲ್ ಕೋಡ್ ಅನ್ನು ರಿಯಲ್-ಟೈಮ್‌ನಲ್ಲಿ ಉತ್ಪಾದಿಸಿ. ಉತ್ಪಾದಿಸಿದ ಕೋಡ್ GitHub ಮತ್ತು ಪ್ರಮುಖ Markdown ಎಡಿಟರ್‌ಗಳೊಂದಿಗೆ ಸಂಪೂರ್ಣವಾಗಿ ಹೊಂದಿಕೊಳ್ಳುತ್ತದೆ, ಒಂದು ಕ್ಲಿಕ್ ನಕಲಿನೊಂದಿಗೆ ಬಳಸಲು ಸಿದ್ಧವಾಗಿದೆ."
    from_alias: "Markdown ಟೇಬಲ್ ಫೈಲ್"
    to_alias: "Markdown ಟೇಬಲ್ ಫಾರ್ಮ್ಯಾಟ್"
  Magic:
    alias: "ಕಸ್ಟಮ್ ಟೆಂಪ್ಲೇಟ್"
    what: "Magic ಟೆಂಪ್ಲೇಟ್ ಈ ಟೂಲ್‌ನ ಅನನ್ಯ ಸುಧಾರಿತ ಡೇಟಾ ಜೆನರೇಟರ್ ಆಗಿದೆ, ಕಸ್ಟಮ್ ಟೆಂಪ್ಲೇಟ್ ಸಿಂಟ್ಯಾಕ್ಸ್ ಮೂಲಕ ಅನಿಯಂತ್ರಿತ ಫಾರ್ಮ್ಯಾಟ್ ಡೇಟಾ ಔಟ್‌ಪುಟ್ ರಚಿಸಲು ಬಳಕೆದಾರರಿಗೆ ಅನುಮತಿಸುತ್ತದೆ. ವೇರಿಯೇಬಲ್ ಬದಲಾವಣೆ, ಷರತ್ತುಬದ್ಧ ನಿರ್ಣಯ ಮತ್ತು ಲೂಪ್ ಪ್ರೊಸೆಸಿಂಗ್ ಅನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ. ಸಂಕೀರ್ಣ ಡೇಟಾ ಕನ್ವರ್ಷನ್ ಅಗತ್ಯಗಳು ಮತ್ತು ವೈಯಕ್ತಿಕಗೊಳಿಸಿದ ಔಟ್‌ಪುಟ್ ಫಾರ್ಮ್ಯಾಟ್‌ಗಳನ್ನು ನಿರ್ವಹಿಸಲು ಇದು ಅಂತಿಮ ಪರಿಹಾರವಾಗಿದೆ, ವಿಶೇಷವಾಗಿ ಡೆವಲಪರ್‌ಗಳು ಮತ್ತು ಡೇಟಾ ಇಂಜಿನಿಯರ್‌ಗಳಿಗೆ ಸೂಕ್ತವಾಗಿದೆ."
    step1: "ಅಂತರ್ನಿರ್ಮಿತ ಸಾಮಾನ್ಯ ಟೆಂಪ್ಲೇಟ್‌ಗಳನ್ನು ಆಯ್ಕೆಮಾಡಿ ಅಥವಾ ಕಸ್ಟಮ್ ಟೆಂಪ್ಲೇಟ್ ಸಿಂಟ್ಯಾಕ್ಸ್ ರಚಿಸಿ. ಸಂಕೀರ್ಣ ಡೇಟಾ ರಚನೆಗಳು ಮತ್ತು ವ್ಯಾಪಾರ ತರ್ಕವನ್ನು ನಿರ್ವಹಿಸಬಲ್ಲ ಸಮೃದ್ಧ ವೇರಿಯೇಬಲ್‌ಗಳು ಮತ್ತು ಫಂಕ್ಷನ್‌ಗಳನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
    step3: "ಕಸ್ಟಮ್ ಫಾರ್ಮ್ಯಾಟ್ ಅವಶ್ಯಕತೆಗಳನ್ನು ಸಂಪೂರ್ಣವಾಗಿ ಪೂರೈಸುವ ಡೇಟಾ ಔಟ್‌ಪುಟ್ ಉತ್ಪಾದಿಸಿ. ಸಂಕೀರ್ಣ ಡೇಟಾ ಕನ್ವರ್ಷನ್ ತರ್ಕ ಮತ್ತು ಷರತ್ತುಬದ್ಧ ಪ್ರೊಸೆಸಿಂಗ್ ಅನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ, ಡೇಟಾ ಪ್ರೊಸೆಸಿಂಗ್ ದಕ್ಷತೆ ಮತ್ತು ಔಟ್‌ಪುಟ್ ಗುಣಮಟ್ಟವನ್ನು ಗಣನೀಯವಾಗಿ ಸುಧಾರಿಸುತ್ತದೆ. ಬ್ಯಾಚ್ ಡೇಟಾ ಪ್ರೊಸೆಸಿಂಗ್‌ಗಾಗಿ ಶಕ್ತಿಶಾಲಿ ಸಾಧನ."
    from_alias: "ಟೇಬಲ್ ಡೇಟಾ"
    to_alias: "ಕಸ್ಟಮ್ ಫಾರ್ಮ್ಯಾಟ್ ಔಟ್‌ಪುಟ್"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) ಅತ್ಯಂತ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುವ ಡೇಟಾ ವಿನಿಮಯ ಫಾರ್ಮ್ಯಾಟ್ ಆಗಿದೆ, Excel, Google Sheets, ಡೇಟಾಬೇಸ್ ಸಿಸ್ಟಮ್‌ಗಳು ಮತ್ತು ವಿವಿಧ ಡೇಟಾ ವಿಶ್ಲೇಷಣೆ ಸಾಧನಗಳಿಂದ ಸಂಪೂರ್ಣವಾಗಿ ಬೆಂಬಲಿತವಾಗಿದೆ. ಇದರ ಸರಳ ರಚನೆ ಮತ್ತು ಬಲವಾದ ಹೊಂದಾಣಿಕೆಯು ಡೇಟಾ ವಲಸೆ, ಬ್ಯಾಚ್ ಆಮದು/ರಫ್ತು ಮತ್ತು ಕ್ರಾಸ್-ಪ್ಲಾಟ್‌ಫಾರ್ಮ್ ಡೇಟಾ ವಿನಿಮಯಕ್ಕೆ ಪ್ರಮಾಣಿತ ಫಾರ್ಮ್ಯಾಟ್ ಮಾಡುತ್ತದೆ, ವ್ಯಾಪಾರ ವಿಶ್ಲೇಷಣೆ, ಡೇಟಾ ಸೈನ್ಸ್ ಮತ್ತು ಸಿಸ್ಟಮ್ ಇಂಟಿಗ್ರೇಷನ್‌ನಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ."
    step1: "CSV ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ನೇರವಾಗಿ CSV ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ. ಟೂಲ್ ಬುದ್ಧಿವಂತಿಕೆಯಿಂದ ವಿವಿಧ ವಿಭಾಜಕಗಳನ್ನು (ಅಲ್ಪವಿರಾಮ, ಟ್ಯಾಬ್, ಅರ್ಧವಿರಾಮ, ಪೈಪ್, ಇತ್ಯಾದಿ) ಗುರುತಿಸುತ್ತದೆ, ಸ್ವಯಂಚಾಲಿತವಾಗಿ ಡೇಟಾ ಪ್ರಕಾರಗಳು ಮತ್ತು ಎನ್‌ಕೋಡಿಂಗ್ ಫಾರ್ಮ್ಯಾಟ್‌ಗಳನ್ನು ಪತ್ತೆ ಮಾಡುತ್ತದೆ, ದೊಡ್ಡ ಫೈಲ್‌ಗಳು ಮತ್ತು ಸಂಕೀರ್ಣ ಡೇಟಾ ರಚನೆಗಳ ವೇಗದ ಪಾರ್ಸಿಂಗ್ ಅನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
    step3: "ಕಸ್ಟಮ್ ವಿಭಾಜಕಗಳು, ಕೋಟ್ ಶೈಲಿಗಳು, ಎನ್‌ಕೋಡಿಂಗ್ ಫಾರ್ಮ್ಯಾಟ್‌ಗಳು ಮತ್ತು BOM ಮಾರ್ಕ್ ಸೆಟ್ಟಿಂಗ್‌ಗಳ ಬೆಂಬಲದೊಂದಿಗೆ ಪ್ರಮಾಣಿತ CSV ಫಾರ್ಮ್ಯಾಟ್ ಫೈಲ್‌ಗಳನ್ನು ಉತ್ಪಾದಿಸಿ. ಗುರಿ ಸಿಸ್ಟಮ್‌ಗಳೊಂದಿಗೆ ಸಂಪೂರ್ಣ ಹೊಂದಾಣಿಕೆಯನ್ನು ಖಚಿತಪಡಿಸುತ್ತದೆ, ಎಂಟರ್‌ಪ್ರೈಸ್-ಮಟ್ಟದ ಡೇಟಾ ಪ್ರೊಸೆಸಿಂಗ್ ಅಗತ್ಯಗಳನ್ನು ಪೂರೈಸಲು ಡೌನ್‌ಲೋಡ್ ಮತ್ತು ಕಂಪ್ರೆಷನ್ ಆಯ್ಕೆಗಳನ್ನು ಒದಗಿಸುತ್ತದೆ."
    from_alias: "CSV ಡೇಟಾ ಫೈಲ್"
    to_alias: "CSV ಪ್ರಮಾಣಿತ ಫಾರ್ಮ್ಯಾಟ್"
  JSON:
    alias: "JSON ಅರೇ"
    what: "JSON (JavaScript Object Notation) ಆಧುನಿಕ ವೆಬ್ ಅಪ್ಲಿಕೇಶನ್‌ಗಳು, REST API ಗಳು ಮತ್ತು ಮೈಕ್ರೋಸರ್ವಿಸ್ ಆರ್ಕಿಟೆಕ್ಚರ್‌ಗಳಿಗೆ ಪ್ರಮಾಣಿತ ಟೇಬಲ್ ಡೇಟಾ ಫಾರ್ಮ್ಯಾಟ್ ಆಗಿದೆ. ಇದರ ಸ್ಪಷ್ಟ ರಚನೆ ಮತ್ತು ಪರಿಣಾಮಕಾರಿ ಪಾರ್ಸಿಂಗ್ ಇದನ್ನು ಫ್ರಂಟ್-ಎಂಡ್ ಮತ್ತು ಬ್ಯಾಕ್-ಎಂಡ್ ಡೇಟಾ ಇಂಟರ್‌ಆಕ್ಷನ್, ಕಾನ್ಫಿಗರೇಶನ್ ಫೈಲ್ ಸಂಗ್ರಹಣೆ ಮತ್ತು NoSQL ಡೇಟಾಬೇಸ್‌ಗಳಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸುವಂತೆ ಮಾಡುತ್ತದೆ. ನೆಸ್ಟೆಡ್ ಆಬ್ಜೆಕ್ಟ್‌ಗಳು, ಅರೇ ರಚನೆಗಳು ಮತ್ತು ಬಹು ಡೇಟಾ ಪ್ರಕಾರಗಳನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ, ಆಧುನಿಕ ಸಾಫ್ಟ್‌ವೇರ್ ಅಭಿವೃದ್ಧಿಗೆ ಅಗತ್ಯವಾದ ಟೇಬಲ್ ಡೇಟಾವನ್ನು ಮಾಡುತ್ತದೆ."
    step1: "JSON ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ JSON ಅರೇಗಳನ್ನು ಅಂಟಿಸಿ. ಆಬ್ಜೆಕ್ಟ್ ಅರೇಗಳು, ನೆಸ್ಟೆಡ್ ರಚನೆಗಳು ಮತ್ತು ಸಂಕೀರ್ಣ ಡೇಟಾ ಪ್ರಕಾರಗಳ ಸ್ವಯಂಚಾಲಿತ ಗುರುತಿಸುವಿಕೆ ಮತ್ತು ಪಾರ್ಸಿಂಗ್ ಅನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ. ಟೂಲ್ ಬುದ್ಧಿವಂತಿಕೆಯಿಂದ JSON ಸಿಂಟ್ಯಾಕ್ಸ್ ಅನ್ನು ಮೌಲ್ಯೀಕರಿಸುತ್ತದೆ ಮತ್ತು ದೋಷ ಪ್ರಾಂಪ್ಟ್‌ಗಳನ್ನು ಒದಗಿಸುತ್ತದೆ."
    step3: "ಬಹು JSON ಫಾರ್ಮ್ಯಾಟ್ ಔಟ್‌ಪುಟ್‌ಗಳನ್ನು ಉತ್ಪಾದಿಸಿ: ಪ್ರಮಾಣಿತ ಆಬ್ಜೆಕ್ಟ್ ಅರೇಗಳು, 2D ಅರೇಗಳು, ಕಾಲಮ್ ಅರೇಗಳು ಮತ್ತು ಕೀ-ವ್ಯಾಲ್ಯೂ ಜೋಡಿ ಫಾರ್ಮ್ಯಾಟ್‌ಗಳು. ಸುಂದರಗೊಳಿಸಿದ ಔಟ್‌ಪುಟ್, ಕಂಪ್ರೆಷನ್ ಮೋಡ್, ಕಸ್ಟಮ್ ರೂಟ್ ಆಬ್ಜೆಕ್ಟ್ ಹೆಸರುಗಳು ಮತ್ತು ಇಂಡೆಂಟೇಶನ್ ಸೆಟ್ಟಿಂಗ್‌ಗಳನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ, ವಿವಿಧ API ಇಂಟರ್‌ಫೇಸ್‌ಗಳು ಮತ್ತು ಡೇಟಾ ಸಂಗ್ರಹಣೆ ಅಗತ್ಯಗಳಿಗೆ ಸಂಪೂರ್ಣವಾಗಿ ಹೊಂದಿಕೊಳ್ಳುತ್ತದೆ."
    from_alias: "JSON ಅರೇ ಫೈಲ್"
    to_alias: "JSON ಪ್ರಮಾಣಿತ ಫಾರ್ಮ್ಯಾಟ್"
  JSONLines:
    alias: "JSONLines ಫಾರ್ಮ್ಯಾಟ್"
    what: "JSON Lines (NDJSON ಎಂದೂ ಕರೆಯಲಾಗುತ್ತದೆ) ಬಿಗ್ ಡೇಟಾ ಪ್ರೊಸೆಸಿಂಗ್ ಮತ್ತು ಸ್ಟ್ರೀಮಿಂಗ್ ಡೇಟಾ ಟ್ರಾನ್ಸ್‌ಮಿಷನ್‌ಗೆ ಪ್ರಮುಖ ಫಾರ್ಮ್ಯಾಟ್ ಆಗಿದೆ, ಪ್ರತಿ ಲೈನ್ ಸ್ವತಂತ್ರ JSON ಆಬ್ಜೆಕ್ಟ್ ಅನ್ನು ಹೊಂದಿರುತ್ತದೆ. ಲಾಗ್ ವಿಶ್ಲೇಷಣೆ, ಡೇಟಾ ಸ್ಟ್ರೀಮ್ ಪ್ರೊಸೆಸಿಂಗ್, ಮೆಷಿನ್ ಲರ್ನಿಂಗ್ ಮತ್ತು ವಿತರಿಸಿದ ಸಿಸ್ಟಮ್‌ಗಳಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ. ಇಂಕ್ರಿಮೆಂಟಲ್ ಪ್ರೊಸೆಸಿಂಗ್ ಮತ್ತು ಪ್ಯಾರಲಲ್ ಕಂಪ್ಯೂಟಿಂಗ್ ಅನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ, ದೊಡ್ಡ-ಪ್ರಮಾಣದ ರಚನಾತ್ಮಕ ಡೇಟಾವನ್ನು ನಿರ್ವಹಿಸಲು ಆದರ್ಶ ಆಯ್ಕೆಯಾಗಿದೆ."
    step1: "JSONLines ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ. ಟೂಲ್ JSON ಆಬ್ಜೆಕ್ಟ್‌ಗಳನ್ನು ಲೈನ್ ಬೈ ಲೈನ್ ಪಾರ್ಸ್ ಮಾಡುತ್ತದೆ, ದೊಡ್ಡ ಫೈಲ್ ಸ್ಟ್ರೀಮಿಂಗ್ ಪ್ರೊಸೆಸಿಂಗ್ ಮತ್ತು ದೋಷ ಲೈನ್ ಸ್ಕಿಪ್ಪಿಂಗ್ ಕಾರ್ಯಚಟುವಟಿಕೆಯನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
    step3: "ಪ್ರತಿ ಲೈನ್ ಸಂಪೂರ್ಣ JSON ಆಬ್ಜೆಕ್ಟ್ ಅನ್ನು ಔಟ್‌ಪುಟ್ ಮಾಡುವ ಪ್ರಮಾಣಿತ JSONLines ಫಾರ್ಮ್ಯಾಟ್ ಉತ್ಪಾದಿಸಿ. ಸ್ಟ್ರೀಮಿಂಗ್ ಪ್ರೊಸೆಸಿಂಗ್, ಬ್ಯಾಚ್ ಆಮದು ಮತ್ತು ಬಿಗ್ ಡೇಟಾ ವಿಶ್ಲೇಷಣೆ ಸನ್ನಿವೇಶಗಳಿಗೆ ಸೂಕ್ತ, ಡೇಟಾ ಮೌಲ್ಯೀಕರಣ ಮತ್ತು ಫಾರ್ಮ್ಯಾಟ್ ಆಪ್ಟಿಮೈಸೇಶನ್ ಅನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
    from_alias: "JSONLines ಡೇಟಾ"
    to_alias: "JSONLines ಸ್ಟ್ರೀಮಿಂಗ್ ಫಾರ್ಮ್ಯಾಟ್"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) ಎಂಟರ್‌ಪ್ರೈಸ್-ಮಟ್ಟದ ಡೇಟಾ ವಿನಿಮಯ ಮತ್ತು ಕಾನ್ಫಿಗರೇಶನ್ ನಿರ್ವಹಣೆಗೆ ಪ್ರಮಾಣಿತ ಫಾರ್ಮ್ಯಾಟ್ ಆಗಿದೆ, ಕಟ್ಟುನಿಟ್ಟಾದ ಸಿಂಟ್ಯಾಕ್ಸ್ ವಿಶೇಷಣೆಗಳು ಮತ್ತು ಶಕ್ತಿಶಾಲಿ ಮೌಲ್ಯೀಕರಣ ಕಾರ್ಯವಿಧಾನಗಳೊಂದಿಗೆ. ವೆಬ್ ಸೇವೆಗಳು, ಕಾನ್ಫಿಗರೇಶನ್ ಫೈಲ್‌ಗಳು, ದಾಖಲೆ ಸಂಗ್ರಹಣೆ ಮತ್ತು ಸಿಸ್ಟಮ್ ಇಂಟಿಗ್ರೇಷನ್‌ನಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ. ನೇಮ್‌ಸ್ಪೇಸ್‌ಗಳು, ಸ್ಕೀಮಾ ಮೌಲ್ಯೀಕರಣ ಮತ್ತು XSLT ರೂಪಾಂತರವನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ, ಎಂಟರ್‌ಪ್ರೈಸ್ ಅಪ್ಲಿಕೇಶನ್‌ಗಳಿಗೆ ಪ್ರಮುಖ ಟೇಬಲ್ ಡೇಟಾವನ್ನು ಮಾಡುತ್ತದೆ."
    step1: "XML ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ XML ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ. ಟೂಲ್ ಸ್ವಯಂಚಾಲಿತವಾಗಿ XML ರಚನೆಯನ್ನು ಪಾರ್ಸ್ ಮಾಡುತ್ತದೆ ಮತ್ತು ಅದನ್ನು ಟೇಬಲ್ ಫಾರ್ಮ್ಯಾಟ್‌ಗೆ ಪರಿವರ್ತಿಸುತ್ತದೆ, ನೇಮ್‌ಸ್ಪೇಸ್, ಗುಣಲಕ್ಷಣ ನಿರ್ವಹಣೆ ಮತ್ತು ಸಂಕೀರ್ಣ ನೆಸ್ಟೆಡ್ ರಚನೆಗಳನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
    step3: "XML ಮಾನದಂಡಗಳಿಗೆ ಅನುಗುಣವಾದ XML ಔಟ್‌ಪುಟ್ ಉತ್ಪಾದಿಸಿ. ಕಸ್ಟಮ್ ರೂಟ್ ಎಲಿಮೆಂಟ್‌ಗಳು, ಸಾಲು ಎಲಿಮೆಂಟ್ ಹೆಸರುಗಳು, ಗುಣಲಕ್ಷಣ ಮೋಡ್‌ಗಳು, CDATA ಸುತ್ತುವಿಕೆ ಮತ್ತು ಅಕ್ಷರ ಎನ್‌ಕೋಡಿಂಗ್ ಸೆಟ್ಟಿಂಗ್‌ಗಳನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ. ಡೇಟಾ ಸಮಗ್ರತೆ ಮತ್ತು ಹೊಂದಾಣಿಕೆಯನ್ನು ಖಚಿತಪಡಿಸುತ್ತದೆ, ಎಂಟರ್‌ಪ್ರೈಸ್-ಮಟ್ಟದ ಅಪ್ಲಿಕೇಶನ್ ಅವಶ್ಯಕತೆಗಳನ್ನು ಪೂರೈಸುತ್ತದೆ."
    from_alias: "XML ಡೇಟಾ ಫೈಲ್"
    to_alias: "XML ಪ್ರಮಾಣಿತ ಫಾರ್ಮ್ಯಾಟ್"
  YAML:
    alias: "YAML ಕಾನ್ಫಿಗರೇಶನ್"
    what: "YAML ಮಾನವ-ಸ್ನೇಹಿ ಡೇಟಾ ಸೀರಿಯಲೈಸೇಶನ್ ಮಾನದಂಡವಾಗಿದೆ, ಅದರ ಸ್ಪಷ್ಟ ಶ್ರೇಣೀಕೃತ ರಚನೆ ಮತ್ತು ಸಂಕ್ಷಿಪ್ತ ಸಿಂಟ್ಯಾಕ್ಸ್‌ಗೆ ಪ್ರಸಿದ್ಧವಾಗಿದೆ. ಕಾನ್ಫಿಗರೇಶನ್ ಫೈಲ್‌ಗಳು, DevOps ಟೂಲ್ ಚೇನ್‌ಗಳು, Docker Compose ಮತ್ತು Kubernetes ನಿಯೋಜನೆಯಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ. ಇದರ ಬಲವಾದ ಓದುವಿಕೆ ಮತ್ತು ಸಂಕ್ಷಿಪ್ತ ಸಿಂಟ್ಯಾಕ್ಸ್ ಆಧುನಿಕ ಕ್ಲೌಡ್-ನೇಟಿವ್ ಅಪ್ಲಿಕೇಶನ್‌ಗಳು ಮತ್ತು ಸ್ವಯಂಚಾಲಿತ ಕಾರ್ಯಾಚರಣೆಗಳಿಗೆ ಪ್ರಮುಖ ಕಾನ್ಫಿಗರೇಶನ್ ಫಾರ್ಮ್ಯಾಟ್ ಮಾಡುತ್ತದೆ."
    step1: "YAML ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ YAML ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ. ಟೂಲ್ ಬುದ್ಧಿವಂತಿಕೆಯಿಂದ YAML ರಚನೆಯನ್ನು ಪಾರ್ಸ್ ಮಾಡುತ್ತದೆ ಮತ್ತು ಸಿಂಟ್ಯಾಕ್ಸ್ ಸರಿಯಾಗಿದೆಯೇ ಎಂದು ಮೌಲ್ಯೀಕರಿಸುತ್ತದೆ, ಬಹು-ದಾಖಲೆ ಫಾರ್ಮ್ಯಾಟ್‌ಗಳು ಮತ್ತು ಸಂಕೀರ್ಣ ಡೇಟಾ ಪ್ರಕಾರಗಳನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
    step3: "ಬ್ಲಾಕ್ ಮತ್ತು ಫ್ಲೋ ಅರೇ ಶೈಲಿಗಳು, ಬಹು ಕೋಟ್ ಸೆಟ್ಟಿಂಗ್‌ಗಳು, ಕಸ್ಟಮ್ ಇಂಡೆಂಟೇಶನ್ ಮತ್ತು ಕಾಮೆಂಟ್ ಸಂರಕ್ಷಣೆಯ ಬೆಂಬಲದೊಂದಿಗೆ ಪ್ರಮಾಣಿತ YAML ಫಾರ್ಮ್ಯಾಟ್ ಔಟ್‌ಪುಟ್ ಉತ್ಪಾದಿಸಿ. ಔಟ್‌ಪುಟ್ YAML ಫೈಲ್‌ಗಳು ವಿವಿಧ ಪಾರ್ಸರ್‌ಗಳು ಮತ್ತು ಕಾನ್ಫಿಗರೇಶನ್ ಸಿಸ್ಟಮ್‌ಗಳೊಂದಿಗೆ ಸಂಪೂರ್ಣವಾಗಿ ಹೊಂದಿಕೊಳ್ಳುವುದನ್ನು ಖಚಿತಪಡಿಸುತ್ತದೆ."
    from_alias: "YAML ಕಾನ್ಫಿಗರೇಶನ್ ಫೈಲ್"
    to_alias: "YAML ಪ್ರಮಾಣಿತ ಫಾರ್ಮ್ಯಾಟ್"
  MySQL:
      alias: "MySQL ಕ್ವೆರಿ ಫಲಿತಾಂಶಗಳು"
      what: "MySQL ವಿಶ್ವದ ಅತ್ಯಂತ ಜನಪ್ರಿಯ ಓಪನ್-ಸೋರ್ಸ್ ರಿಲೇಶನಲ್ ಡೇಟಾಬೇಸ್ ಮ್ಯಾನೇಜ್‌ಮೆಂಟ್ ಸಿಸ್ಟಮ್ ಆಗಿದೆ, ಅದರ ಹೆಚ್ಚಿನ ಕಾರ್ಯಕ್ಷಮತೆ, ವಿಶ್ವಾಸಾರ್ಹತೆ ಮತ್ತು ಬಳಕೆಯ ಸುಲಭತೆಗೆ ಪ್ರಸಿದ್ಧವಾಗಿದೆ. ವೆಬ್ ಅಪ್ಲಿಕೇಶನ್‌ಗಳು, ಎಂಟರ್‌ಪ್ರೈಸ್ ಸಿಸ್ಟಮ್‌ಗಳು ಮತ್ತು ಡೇಟಾ ವಿಶ್ಲೇಷಣೆ ಪ್ಲಾಟ್‌ಫಾರ್ಮ್‌ಗಳಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ. MySQL ಕ್ವೆರಿ ಫಲಿತಾಂಶಗಳು ಸಾಮಾನ್ಯವಾಗಿ ರಚನಾತ್ಮಕ ಟೇಬಲ್ ಡೇಟಾವನ್ನು ಹೊಂದಿರುತ್ತವೆ, ಡೇಟಾಬೇಸ್ ನಿರ್ವಹಣೆ ಮತ್ತು ಡೇಟಾ ವಿಶ್ಲೇಷಣೆ ಕೆಲಸದಲ್ಲಿ ಪ್ರಮುಖ ಡೇಟಾ ಮೂಲವಾಗಿ ಕಾರ್ಯನಿರ್ವಹಿಸುತ್ತವೆ."
      step1: "MySQL ಕ್ವೆರಿ ಔಟ್‌ಪುಟ್ ಫಲಿತಾಂಶಗಳನ್ನು ಡೇಟಾ ಮೂಲ ಪ್ರದೇಶಕ್ಕೆ ಅಂಟಿಸಿ. ಟೂಲ್ ಸ್ವಯಂಚಾಲಿತವಾಗಿ MySQL ಕಮಾಂಡ್-ಲೈನ್ ಔಟ್‌ಪುಟ್ ಫಾರ್ಮ್ಯಾಟ್ ಅನ್ನು ಗುರುತಿಸುತ್ತದೆ ಮತ್ತು ಪಾರ್ಸ್ ಮಾಡುತ್ತದೆ, ವಿವಿಧ ಕ್ವೆರಿ ಫಲಿತಾಂಶ ಶೈಲಿಗಳು ಮತ್ತು ಅಕ್ಷರ ಎನ್‌ಕೋಡಿಂಗ್‌ಗಳನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ, ಹೆಡರ್‌ಗಳು ಮತ್ತು ಡೇಟಾ ಸಾಲುಗಳನ್ನು ಬುದ್ಧಿವಂತಿಕೆಯಿಂದ ನಿರ್ವಹಿಸುತ್ತದೆ."
      step3: "MySQL ಕ್ವೆರಿ ಫಲಿತಾಂಶಗಳನ್ನು ಬಹು ಟೇಬಲ್ ಡೇಟಾ ಫಾರ್ಮ್ಯಾಟ್‌ಗಳಿಗೆ ತ್ವರಿತವಾಗಿ ಪರಿವರ್ತಿಸಿ, ಡೇಟಾ ವಿಶ್ಲೇಷಣೆ, ವರದಿ ಉತ್ಪಾದನೆ, ಕ್ರಾಸ್-ಸಿಸ್ಟಮ್ ಡೇಟಾ ವಲಸೆ ಮತ್ತು ಡೇಟಾ ಮೌಲ್ಯೀಕರಣವನ್ನು ಸುಗಮಗೊಳಿಸುತ್ತದೆ. ಡೇಟಾಬೇಸ್ ನಿರ್ವಾಹಕರು ಮತ್ತು ಡೇಟಾ ವಿಶ್ಲೇಷಕರಿಗೆ ಪ್ರಾಯೋಗಿಕ ಸಾಧನ."
      from_alias: "MySQL ಕ್ವೆರಿ ಔಟ್‌ಪುಟ್"
      to_alias: "MySQL ಟೇಬಲ್ ಡೇಟಾ"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) ರಿಲೇಶನಲ್ ಡೇಟಾಬೇಸ್‌ಗಳಿಗೆ ಪ್ರಮಾಣಿತ ಕಾರ್ಯಾಚರಣೆ ಭಾಷೆಯಾಗಿದೆ, ಡೇಟಾ ಕ್ವೆರಿ, ಇನ್ಸರ್ಟ್, ಅಪ್‌ಡೇಟ್ ಮತ್ತು ಡಿಲೀಟ್ ಕಾರ್ಯಾಚರಣೆಗಳಿಗೆ ಬಳಸಲಾಗುತ್ತದೆ. ಡೇಟಾಬೇಸ್ ನಿರ್ವಹಣೆಯ ಮುಖ್ಯ ತಂತ್ರಜ್ಞಾನವಾಗಿ, SQL ಅನ್ನು ಡೇಟಾ ವಿಶ್ಲೇಷಣೆ, ಬಿಸಿನೆಸ್ ಇಂಟೆಲಿಜೆನ್ಸ್, ETL ಪ್ರೊಸೆಸಿಂಗ್ ಮತ್ತು ಡೇಟಾ ವೇರ್‌ಹೌಸ್ ನಿರ್ಮಾಣದಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ. ಇದು ಡೇಟಾ ವೃತ್ತಿಪರರಿಗೆ ಅತ್ಯಗತ್ಯ ಕೌಶಲ್ಯ ಸಾಧನವಾಗಿದೆ."
    step1: "INSERT SQL ಸ್ಟೇಟ್‌ಮೆಂಟ್‌ಗಳನ್ನು ಅಂಟಿಸಿ ಅಥವಾ .sql ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ. ಟೂಲ್ ಬುದ್ಧಿವಂತಿಕೆಯಿಂದ SQL ಸಿಂಟ್ಯಾಕ್ಸ್ ಅನ್ನು ಪಾರ್ಸ್ ಮಾಡುತ್ತದೆ ಮತ್ತು ಟೇಬಲ್ ಡೇಟಾವನ್ನು ಹೊರತೆಗೆಯುತ್ತದೆ, ಬಹು SQL ಉಪಭಾಷೆಗಳು ಮತ್ತು ಸಂಕೀರ್ಣ ಕ್ವೆರಿ ಸ್ಟೇಟ್‌ಮೆಂಟ್ ಪ್ರೊಸೆಸಿಂಗ್ ಅನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
    step3: "ಪ್ರಮಾಣಿತ SQL INSERT ಸ್ಟೇಟ್‌ಮೆಂಟ್‌ಗಳು ಮತ್ತು ಟೇಬಲ್ ರಚನೆ ಸ್ಟೇಟ್‌ಮೆಂಟ್‌ಗಳನ್ನು ಉತ್ಪಾದಿಸಿ. ಬಹು ಡೇಟಾಬೇಸ್ ಉಪಭಾಷೆಗಳನ್ನು (MySQL, PostgreSQL, SQLite, SQL Server, Oracle) ಬೆಂಬಲಿಸುತ್ತದೆ, ಸ್ವಯಂಚಾಲಿತವಾಗಿ ಡೇಟಾ ಟೈಪ್ ಮ್ಯಾಪಿಂಗ್, ಅಕ್ಷರ ಎಸ್ಕೇಪಿಂಗ್ ಮತ್ತು ಪ್ರಾಥಮಿಕ ಕೀ ನಿರ್ಬಂಧಗಳನ್ನು ನಿರ್ವಹಿಸುತ್ತದೆ. ಉತ್ಪಾದಿಸಿದ SQL ಕೋಡ್ ಅನ್ನು ನೇರವಾಗಿ ಕಾರ್ಯಗತಗೊಳಿಸಬಹುದೆಂದು ಖಚಿತಪಡಿಸುತ್ತದೆ."
    from_alias: "SQL ಡೇಟಾ ಫೈಲ್"
    to_alias: "SQL ಪ್ರಮಾಣಿತ ಸ್ಟೇಟ್‌ಮೆಂಟ್"
  Qlik:
      alias: "Qlik ಟೇಬಲ್"
      what: "Qlik ಎಂಬುದು ಡೇಟಾ ವಿಸುವಲೈಸೇಶನ್, ಎಕ್ಸಿಕ್ಯೂಟಿವ್ ಡ್ಯಾಶ್‌ಬೋರ್ಡ್‌ಗಳು ಮತ್ತು ಸ್ವಯಂ-ಸೇವಾ ಬಿಸಿನೆಸ್ ಇಂಟೆಲಿಜೆನ್ಸ್ ಉತ್ಪನ್ನಗಳಲ್ಲಿ ಪರಿಣತಿ ಹೊಂದಿರುವ ಸಾಫ್ಟ್‌ವೇರ್ ಮಾರಾಟಗಾರ, Tableau ಮತ್ತು Microsoft ಜೊತೆಗೆ."
      step1: ""
      step3: "ಅಂತಿಮವಾಗಿ, [ಟೇಬಲ್ ಜೆನರೇಟರ್](#TableGenerator) ಕನ್ವರ್ಷನ್ ಫಲಿತಾಂಶಗಳನ್ನು ತೋರಿಸುತ್ತದೆ. ನಿಮ್ಮ Qlik Sense, Qlik AutoML, QlikView, ಅಥವಾ ಇತರ Qlik-ಸಕ್ರಿಯಗೊಳಿಸಿದ ಸಾಫ್ಟ್‌ವೇರ್‌ನಲ್ಲಿ ಬಳಸಿ."
      from_alias: "Qlik ಟೇಬಲ್"
      to_alias: "Qlik ಟೇಬಲ್"
  DAX:
      alias: "DAX ಟೇಬಲ್"
      what: "DAX (Data Analysis Expressions) ಎಂಬುದು ಲೆಕ್ಕಾಚಾರದ ಕಾಲಮ್‌ಗಳು, ಅಳತೆಗಳು ಮತ್ತು ಕಸ್ಟಮ್ ಟೇಬಲ್‌ಗಳನ್ನು ರಚಿಸಲು Microsoft Power BI ಯಾದ್ಯಂತ ಬಳಸಲಾಗುವ ಪ್ರೋಗ್ರಾಮಿಂಗ್ ಭಾಷೆಯಾಗಿದೆ."
      step1: ""
      step3: "ಅಂತಿಮವಾಗಿ, [ಟೇಬಲ್ ಜೆನರೇಟರ್](#TableGenerator) ಕನ್ವರ್ಷನ್ ಫಲಿತಾಂಶಗಳನ್ನು ತೋರಿಸುತ್ತದೆ. ನಿರೀಕ್ಷಿಸಿದಂತೆ, ಇದನ್ನು Microsoft Power BI, Microsoft Analysis Services, ಮತ್ತು Excel ಗಾಗಿ Microsoft Power Pivot ಸೇರಿದಂತೆ ಹಲವಾರು Microsoft ಉತ್ಪನ್ನಗಳಲ್ಲಿ ಬಳಸಲಾಗುತ್ತದೆ."
      from_alias: "DAX ಟೇಬಲ್"
      to_alias: "DAX ಟೇಬಲ್"
  Firebase:
    alias: "Firebase ಪಟ್ಟಿ"
    what: "Firebase ಎಂಬುದು ರಿಯಲ್-ಟೈಮ್ ಡೇಟಾಬೇಸ್, ಕ್ಲೌಡ್ ಸಂಗ್ರಹಣೆ, ಪ್ರಮಾಣೀಕರಣ, ಕ್ರ್ಯಾಶ್ ವರದಿ ಮಾಡುವಿಕೆ ಇತ್ಯಾದಿಗಳಂತಹ ಹೋಸ್ಟ್ ಮಾಡಿದ ಬ್ಯಾಕೆಂಡ್ ಸೇವೆಗಳನ್ನು ಒದಗಿಸುವ BaaS ಅಪ್ಲಿಕೇಶನ್ ಅಭಿವೃದ್ಧಿ ಪ್ಲಾಟ್‌ಫಾರ್ಮ್ ಆಗಿದೆ."
    step1: ""
    step3: "ಅಂತಿಮವಾಗಿ, [ಟೇಬಲ್ ಜೆನರೇಟರ್](#TableGenerator) ಕನ್ವರ್ಷನ್ ಫಲಿತಾಂಶಗಳನ್ನು ತೋರಿಸುತ್ತದೆ. ನಂತರ ನೀವು Firebase ಡೇಟಾಬೇಸ್‌ನಲ್ಲಿ ಡೇಟಾದ ಪಟ್ಟಿಗೆ ಸೇರಿಸಲು Firebase API ನಲ್ಲಿ push ವಿಧಾನವನ್ನು ಬಳಸಬಹುದು."
    from_alias: "Firebase ಪಟ್ಟಿ"
    to_alias: "Firebase ಪಟ್ಟಿ"
  HTML:
    alias: "HTML ಟೇಬಲ್"
    what: "HTML ಟೇಬಲ್‌ಗಳು ವೆಬ್ ಪುಟಗಳಲ್ಲಿ ರಚನಾತ್ಮಕ ಡೇಟಾವನ್ನು ಪ್ರದರ್ಶಿಸುವ ಪ್ರಮಾಣಿತ ವಿಧಾನವಾಗಿದೆ, table, tr, td ಮತ್ತು ಇತರ ಟ್ಯಾಗ್‌ಗಳೊಂದಿಗೆ ನಿರ್ಮಿಸಲಾಗಿದೆ. ಸಮೃದ್ಧ ಶೈಲಿ ಕಸ್ಟಮೈಸೇಶನ್, ರೆಸ್ಪಾನ್ಸಿವ್ ಲೇಔಟ್ ಮತ್ತು ಇಂಟರ್‌ಆಕ್ಟಿವ್ ಕಾರ್ಯಚಟುವಟಿಕೆಯನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ. ವೆಬ್‌ಸೈಟ್ ಅಭಿವೃದ್ಧಿ, ಡೇಟಾ ಪ್ರದರ್ಶನ ಮತ್ತು ವರದಿ ಉತ್ಪಾದನೆಯಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ, ಫ್ರಂಟ್-ಎಂಡ್ ಅಭಿವೃದ್ಧಿ ಮತ್ತು ವೆಬ್ ಡಿಸೈನ್‌ನ ಪ್ರಮುಖ ಘಟಕವಾಗಿ ಕಾರ್ಯನಿರ್ವಹಿಸುತ್ತದೆ."
    step1: "ಟೇಬಲ್‌ಗಳನ್ನು ಹೊಂದಿರುವ HTML ಕೋಡ್ ಅನ್ನು ಅಂಟಿಸಿ ಅಥವಾ HTML ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ. ಟೂಲ್ ಸ್ವಯಂಚಾಲಿತವಾಗಿ ಪುಟಗಳಿಂದ ಟೇಬಲ್ ಡೇಟಾವನ್ನು ಗುರುತಿಸುತ್ತದೆ ಮತ್ತು ಹೊರತೆಗೆಯುತ್ತದೆ, ಸಂಕೀರ್ಣ HTML ರಚನೆಗಳು, CSS ಶೈಲಿಗಳು ಮತ್ತು ನೆಸ್ಟೆಡ್ ಟೇಬಲ್ ಪ್ರೊಸೆಸಿಂಗ್ ಅನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
    step3: "thead/tbody ರಚನೆ, CSS ಕ್ಲಾಸ್ ಸೆಟ್ಟಿಂಗ್‌ಗಳು, ಟೇಬಲ್ ಕ್ಯಾಪ್ಶನ್‌ಗಳು, ಸಾಲು/ಕಾಲಮ್ ಹೆಡರ್‌ಗಳು ಮತ್ತು ರೆಸ್ಪಾನ್ಸಿವ್ ಗುಣಲಕ್ಷಣ ಕಾನ್ಫಿಗರೇಶನ್‌ಗೆ ಬೆಂಬಲದೊಂದಿಗೆ ಸೆಮ್ಯಾಂಟಿಕ್ HTML ಟೇಬಲ್ ಕೋಡ್ ಉತ್ಪಾದಿಸಿ. ಉತ್ಪಾದಿಸಿದ ಟೇಬಲ್ ಕೋಡ್ ಉತ್ತಮ ಪ್ರವೇಶಿಸುವಿಕೆ ಮತ್ತು SEO ಸ್ನೇಹಿತತೆಯೊಂದಿಗೆ ವೆಬ್ ಮಾನದಂಡಗಳನ್ನು ಪೂರೈಸುವುದನ್ನು ಖಚಿತಪಡಿಸುತ್ತದೆ."
    from_alias: "HTML ವೆಬ್ ಟೇಬಲ್"
    to_alias: "HTML ಪ್ರಮಾಣಿತ ಟೇಬಲ್"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel ವಿಶ್ವದ ಅತ್ಯಂತ ಜನಪ್ರಿಯ ಸ್ಪ್ರೆಡ್‌ಶೀಟ್ ಸಾಫ್ಟ್‌ವೇರ್ ಆಗಿದೆ, ವ್ಯಾಪಾರ ವಿಶ್ಲೇಷಣೆ, ಹಣಕಾಸು ನಿರ್ವಹಣೆ, ಡೇಟಾ ಪ್ರೊಸೆಸಿಂಗ್ ಮತ್ತು ವರದಿ ರಚನೆಯಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ. ಇದರ ಶಕ್ತಿಶಾಲಿ ಡೇಟಾ ಪ್ರೊಸೆಸಿಂಗ್ ಸಾಮರ್ಥ್ಯಗಳು, ಸಮೃದ್ಧ ಫಂಕ್ಷನ್ ಲೈಬ್ರರಿ ಮತ್ತು ಹೊಂದಿಕೊಳ್ಳುವ ವಿಸುವಲೈಸೇಶನ್ ವೈಶಿಷ್ಟ್ಯಗಳು ಇದನ್ನು ಕಚೇರಿ ಯಾಂತ್ರೀಕರಣ ಮತ್ತು ಡೇಟಾ ವಿಶ್ಲೇಷಣೆಗೆ ಪ್ರಮಾಣಿತ ಸಾಧನವನ್ನಾಗಿ ಮಾಡುತ್ತದೆ, ಬಹುತೇಕ ಎಲ್ಲಾ ಉದ್ಯಮಗಳು ಮತ್ತು ಕ್ಷೇತ್ರಗಳಲ್ಲಿ ವ್ಯಾಪಕ ಅಪ್ಲಿಕೇಶನ್‌ಗಳೊಂದಿಗೆ."
    step1: "Excel ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ (.xlsx, .xls ಫಾರ್ಮ್ಯಾಟ್‌ಗಳನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ) ಅಥವಾ Excel ನಿಂದ ನೇರವಾಗಿ ಟೇಬಲ್ ಡೇಟಾವನ್ನು ನಕಲಿಸಿ ಮತ್ತು ಅಂಟಿಸಿ. ಟೂಲ್ ಮಲ್ಟಿ-ವರ್ಕ್‌ಶೀಟ್ ಪ್ರೊಸೆಸಿಂಗ್, ಸಂಕೀರ್ಣ ಫಾರ್ಮ್ಯಾಟ್ ಗುರುತಿಸುವಿಕೆ ಮತ್ತು ದೊಡ್ಡ ಫೈಲ್‌ಗಳ ವೇಗದ ಪಾರ್ಸಿಂಗ್ ಅನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ, ಸ್ವಯಂಚಾಲಿತವಾಗಿ ವಿಲೀನಗೊಂಡ ಸೆಲ್‌ಗಳು ಮತ್ತು ಡೇಟಾ ಪ್ರಕಾರಗಳನ್ನು ನಿರ್ವಹಿಸುತ್ತದೆ."
    step3: "Excel ಗೆ ನೇರವಾಗಿ ಅಂಟಿಸಬಹುದಾದ ಅಥವಾ ಪ್ರಮಾಣಿತ .xlsx ಫೈಲ್‌ಗಳಾಗಿ ಡೌನ್‌ಲೋಡ್ ಮಾಡಬಹುದಾದ Excel-ಹೊಂದಾಣಿಕೆಯ ಟೇಬಲ್ ಡೇಟಾವನ್ನು ಉತ್ಪಾದಿಸಿ. ವರ್ಕ್‌ಶೀಟ್ ಹೆಸರಿಸುವಿಕೆ, ಸೆಲ್ ಫಾರ್ಮ್ಯಾಟಿಂಗ್, ಸ್ವಯಂ ಕಾಲಮ್ ಅಗಲ, ಹೆಡರ್ ಶೈಲಿ ಮತ್ತು ಡೇಟಾ ಮೌಲ್ಯೀಕರಣ ಸೆಟ್ಟಿಂಗ್‌ಗಳನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ. ಔಟ್‌ಪುಟ್ Excel ಫೈಲ್‌ಗಳು ವೃತ್ತಿಪರ ನೋಟ ಮತ್ತು ಸಂಪೂರ್ಣ ಕಾರ್ಯಚಟುವಟಿಕೆಯನ್ನು ಹೊಂದಿವೆ ಎಂದು ಖಚಿತಪಡಿಸುತ್ತದೆ."
    from_alias: "Excel ಸ್ಪ್ರೆಡ್‌ಶೀಟ್"
    to_alias: "Excel ಪ್ರಮಾಣಿತ ಫಾರ್ಮ್ಯಾಟ್"
  LaTeX:
    alias: "LaTeX ಟೇಬಲ್"
    what: "LaTeX ಒಂದು ವೃತ್ತಿಪರ ದಾಖಲೆ ಟೈಪ್‌ಸೆಟ್ಟಿಂಗ್ ಸಿಸ್ಟಮ್ ಆಗಿದೆ, ವಿಶೇಷವಾಗಿ ಶೈಕ್ಷಣಿಕ ಪತ್ರಿಕೆಗಳು, ತಾಂತ್ರಿಕ ದಾಖಲೆಗಳು ಮತ್ತು ವೈಜ್ಞಾನಿಕ ಪ್ರಕಟಣೆಗಳನ್ನು ರಚಿಸಲು ಸೂಕ್ತವಾಗಿದೆ. ಇದರ ಟೇಬಲ್ ಕಾರ್ಯಚಟುವಟಿಕೆ ಶಕ್ತಿಶಾಲಿಯಾಗಿದೆ, ಸಂಕೀರ್ಣ ಗಣಿತದ ಸೂತ್ರಗಳು, ನಿಖರವಾದ ಲೇಔಟ್ ನಿಯಂತ್ರಣ ಮತ್ತು ಉನ್ನತ-ಗುಣಮಟ್ಟದ PDF ಔಟ್‌ಪುಟ್ ಅನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ. ಇದು ಶೈಕ್ಷಣಿಕ ಮತ್ತು ವೈಜ್ಞಾನಿಕ ಪ್ರಕಾಶನದಲ್ಲಿ ಪ್ರಮಾಣಿತ ಸಾಧನವಾಗಿದೆ, ಜರ್ನಲ್ ಪೇಪರ್‌ಗಳು, ಪ್ರಬಂಧಗಳು ಮತ್ತು ತಾಂತ್ರಿಕ ಕೈಪಿಡಿ ಟೈಪ್‌ಸೆಟ್ಟಿಂಗ್‌ನಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ."
    step1: "LaTeX ಟೇಬಲ್ ಕೋಡ್ ಅನ್ನು ಅಂಟಿಸಿ ಅಥವಾ .tex ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ. ಟೂಲ್ LaTeX ಟೇಬಲ್ ಸಿಂಟ್ಯಾಕ್ಸ್ ಅನ್ನು ಪಾರ್ಸ್ ಮಾಡುತ್ತದೆ ಮತ್ತು ಡೇಟಾ ವಿಷಯವನ್ನು ಹೊರತೆಗೆಯುತ್ತದೆ, ಬಹು ಟೇಬಲ್ ಪರಿಸರಗಳು (tabular, longtable, array, ಇತ್ಯಾದಿ) ಮತ್ತು ಸಂಕೀರ್ಣ ಫಾರ್ಮ್ಯಾಟ್ ಆಜ್ಞೆಗಳನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
    step3: "ಬಹು ಟೇಬಲ್ ಪರಿಸರ ಆಯ್ಕೆ, ಬಾರ್ಡರ್ ಶೈಲಿ ಕಾನ್ಫಿಗರೇಶನ್, ಕ್ಯಾಪ್ಶನ್ ಸ್ಥಾನ ಸೆಟ್ಟಿಂಗ್‌ಗಳು, ದಾಖಲೆ ವರ್ಗ ವಿಶೇಷಣೆ ಮತ್ತು ಪ್ಯಾಕೇಜ್ ನಿರ್ವಹಣೆಗೆ ಬೆಂಬಲದೊಂದಿಗೆ ವೃತ್ತಿಪರ LaTeX ಟೇಬಲ್ ಕೋಡ್ ಉತ್ಪಾದಿಸಿ. ಸಂಪೂರ್ಣ ಕಂಪೈಲ್ ಮಾಡಬಹುದಾದ LaTeX ದಾಖಲೆಗಳನ್ನು ಉತ್ಪಾದಿಸಬಹುದು, ಔಟ್‌ಪುಟ್ ಟೇಬಲ್‌ಗಳು ಶೈಕ್ಷಣಿಕ ಪ್ರಕಾಶನ ಮಾನದಂಡಗಳನ್ನು ಪೂರೈಸುವುದನ್ನು ಖಚಿತಪಡಿಸುತ್ತದೆ."
    from_alias: "LaTeX ದಾಖಲೆ ಟೇಬಲ್"
    to_alias: "LaTeX ವೃತ್ತಿಪರ ಫಾರ್ಮ್ಯಾಟ್"
  ASCII:
    alias: "ASCII ಟೇಬಲ್"
    what: "ASCII ಟೇಬಲ್‌ಗಳು ಟೇಬಲ್ ಬಾರ್ಡರ್‌ಗಳು ಮತ್ತು ರಚನೆಗಳನ್ನು ಚಿತ್ರಿಸಲು ಸರಳ ಪಠ್ಯ ಅಕ್ಷರಗಳನ್ನು ಬಳಸುತ್ತವೆ, ಅತ್ಯುತ್ತಮ ಹೊಂದಾಣಿಕೆ ಮತ್ತು ಪೋರ್ಟೆಬಿಲಿಟಿಯನ್ನು ಒದಗಿಸುತ್ತವೆ. ಎಲ್ಲಾ ಪಠ್ಯ ಸಂಪಾದಕರು, ಟರ್ಮಿನಲ್ ಪರಿಸರಗಳು ಮತ್ತು ಆಪರೇಟಿಂಗ್ ಸಿಸ್ಟಮ್‌ಗಳೊಂದಿಗೆ ಹೊಂದಿಕೊಳ್ಳುತ್ತದೆ. ಕೋಡ್ ದಾಖಲೀಕರಣ, ತಾಂತ್ರಿಕ ಕೈಪಿಡಿಗಳು, README ಫೈಲ್‌ಗಳು ಮತ್ತು ಕಮಾಂಡ್-ಲೈನ್ ಟೂಲ್ ಔಟ್‌ಪುಟ್‌ನಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ. ಪ್ರೋಗ್ರಾಮರ್‌ಗಳು ಮತ್ತು ಸಿಸ್ಟಮ್ ನಿರ್ವಾಹಕರಿಗೆ ಆದ್ಯತೆಯ ಡೇಟಾ ಪ್ರದರ್ಶನ ಫಾರ್ಮ್ಯಾಟ್."
    step1: "ASCII ಟೇಬಲ್‌ಗಳನ್ನು ಹೊಂದಿರುವ ಪಠ್ಯ ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ನೇರವಾಗಿ ಟೇಬಲ್ ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ. ಟೂಲ್ ಬುದ್ಧಿವಂತಿಕೆಯಿಂದ ASCII ಟೇಬಲ್ ರಚನೆಗಳನ್ನು ಗುರುತಿಸುತ್ತದೆ ಮತ್ತು ಪಾರ್ಸ್ ಮಾಡುತ್ತದೆ, ಬಹು ಬಾರ್ಡರ್ ಶೈಲಿಗಳು ಮತ್ತು ಅಲೈನ್‌ಮೆಂಟ್ ಫಾರ್ಮ್ಯಾಟ್‌ಗಳನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
    step3: "ಬಹು ಬಾರ್ಡರ್ ಶೈಲಿಗಳು (ಸಿಂಗಲ್ ಲೈನ್, ಡಬಲ್ ಲೈನ್, ರೌಂಡೆಡ್ ಕಾರ್ನರ್‌ಗಳು, ಇತ್ಯಾದಿ), ಪಠ್ಯ ಅಲೈನ್‌ಮೆಂಟ್ ವಿಧಾನಗಳು ಮತ್ತು ಸ್ವಯಂ ಕಾಲಮ್ ಅಗಲಕ್ಕೆ ಬೆಂಬಲದೊಂದಿಗೆ ಸುಂದರವಾದ ಸರಳ ಪಠ್ಯ ASCII ಟೇಬಲ್‌ಗಳನ್ನು ಉತ್ಪಾದಿಸಿ. ಉತ್ಪಾದಿಸಿದ ಟೇಬಲ್‌ಗಳು ಕೋಡ್ ಸಂಪಾದಕರು, ದಾಖಲೆಗಳು ಮತ್ತು ಕಮಾಂಡ್ ಲೈನ್‌ಗಳಲ್ಲಿ ಸಂಪೂರ್ಣವಾಗಿ ಪ್ರದರ್ಶಿಸಲ್ಪಡುತ್ತವೆ."
    from_alias: "ASCII ಪಠ್ಯ ಟೇಬಲ್"
    to_alias: "ASCII ಪ್ರಮಾಣಿತ ಫಾರ್ಮ್ಯಾಟ್"
  MediaWiki:
    alias: "MediaWiki ಟೇಬಲ್"
    what: "MediaWiki ಎಂಬುದು Wikipedia ನಂತಹ ಪ್ರಸಿದ್ಧ ವಿಕಿ ಸೈಟ್‌ಗಳಿಂದ ಬಳಸಲ್ಪಡುವ ಓಪನ್-ಸೋರ್ಸ್ ಸಾಫ್ಟ್‌ವೇರ್ ಪ್ಲಾಟ್‌ಫಾರ್ಮ್ ಆಗಿದೆ. ಇದರ ಟೇಬಲ್ ಸಿಂಟ್ಯಾಕ್ಸ್ ಸಂಕ್ಷಿಪ್ತವಾಗಿದ್ದರೂ ಶಕ್ತಿಶಾಲಿಯಾಗಿದೆ, ಟೇಬಲ್ ಶೈಲಿ ಕಸ್ಟಮೈಸೇಶನ್, ಸಾರ್ಟಿಂಗ್ ಕಾರ್ಯಚಟುವಟಿಕೆ ಮತ್ತು ಲಿಂಕ್ ಎಂಬೆಡಿಂಗ್ ಅನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ. ಜ್ಞಾನ ನಿರ್ವಹಣೆ, ಸಹಯೋಗದ ಸಂಪಾದನೆ ಮತ್ತು ವಿಷಯ ನಿರ್ವಹಣಾ ವ್ಯವಸ್ಥೆಗಳಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ, ವಿಕಿ ವಿಶ್ವಕೋಶಗಳು ಮತ್ತು ಜ್ಞಾನ ಬೇಸ್‌ಗಳನ್ನು ನಿರ್ಮಿಸಲು ಮುಖ್ಯ ತಂತ್ರಜ್ಞಾನವಾಗಿ ಕಾರ್ಯನಿರ್ವಹಿಸುತ್ತದೆ."
    step1: "MediaWiki ಟೇಬಲ್ ಕೋಡ್ ಅನ್ನು ಅಂಟಿಸಿ ಅಥವಾ ವಿಕಿ ಮೂಲ ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ. ಟೂಲ್ ವಿಕಿ ಮಾರ್ಕಪ್ ಸಿಂಟ್ಯಾಕ್ಸ್ ಅನ್ನು ಪಾರ್ಸ್ ಮಾಡುತ್ತದೆ ಮತ್ತು ಟೇಬಲ್ ಡೇಟಾವನ್ನು ಹೊರತೆಗೆಯುತ್ತದೆ, ಸಂಕೀರ್ಣ ವಿಕಿ ಸಿಂಟ್ಯಾಕ್ಸ್ ಮತ್ತು ಟೆಂಪ್ಲೇಟ್ ಪ್ರೊಸೆಸಿಂಗ್ ಅನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
    step3: "ಹೆಡರ್ ಶೈಲಿ ಸೆಟ್ಟಿಂಗ್‌ಗಳು, ಸೆಲ್ ಅಲೈನ್‌ಮೆಂಟ್, ಸಾರ್ಟಿಂಗ್ ಕಾರ್ಯಚಟುವಟಿಕೆ ಸಕ್ರಿಯಗೊಳಿಸುವಿಕೆ ಮತ್ತು ಕೋಡ್ ಕಂಪ್ರೆಷನ್ ಆಯ್ಕೆಗಳಿಗೆ ಬೆಂಬಲದೊಂದಿಗೆ ಪ್ರಮಾಣಿತ MediaWiki ಟೇಬಲ್ ಕೋಡ್ ಉತ್ಪಾದಿಸಿ. ಉತ್ಪಾದಿಸಿದ ಕೋಡ್ ಅನ್ನು ವಿಕಿ ಪುಟ ಸಂಪಾದನೆಗೆ ನೇರವಾಗಿ ಬಳಸಬಹುದು, MediaWiki ಪ್ಲಾಟ್‌ಫಾರ್ಮ್‌ಗಳಲ್ಲಿ ಸಂಪೂರ್ಣ ಪ್ರದರ್ಶನವನ್ನು ಖಚಿತಪಡಿಸುತ್ತದೆ."
    from_alias: "MediaWiki ಮೂಲ ಕೋಡ್"
    to_alias: "MediaWiki ಟೇಬಲ್ ಸಿಂಟ್ಯಾಕ್ಸ್"
  TracWiki:
    alias: "TracWiki ಟೇಬಲ್"
    what: "Trac ಎಂಬುದು ಟೇಬಲ್ ವಿಷಯವನ್ನು ರಚಿಸಲು ಸರಳೀಕೃತ ವಿಕಿ ಸಿಂಟ್ಯಾಕ್ಸ್ ಅನ್ನು ಬಳಸುವ ವೆಬ್-ಆಧಾರಿತ ಪ್ರಾಜೆಕ್ಟ್ ನಿರ್ವಹಣೆ ಮತ್ತು ಬಗ್ ಟ್ರ್ಯಾಕಿಂಗ್ ಸಿಸ್ಟಮ್ ಆಗಿದೆ."
    step1: "TracWiki ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ಟೇಬಲ್ ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ."
    step3: "ಸಾಲು/ಕಾಲಮ್ ಹೆಡರ್ ಸೆಟ್ಟಿಂಗ್‌ಗಳಿಗೆ ಬೆಂಬಲದೊಂದಿಗೆ TracWiki-ಹೊಂದಾಣಿಕೆಯ ಟೇಬಲ್ ಕೋಡ್ ಉತ್ಪಾದಿಸಿ, ಪ್ರಾಜೆಕ್ಟ್ ದಾಖಲೆ ನಿರ್ವಹಣೆಯನ್ನು ಸುಗಮಗೊಳಿಸುತ್ತದೆ."
    from_alias: "TracWiki ಟೇಬಲ್"
    to_alias: "TracWiki ಫಾರ್ಮ್ಯಾಟ್"
  AsciiDoc:
    alias: "AsciiDoc ಟೇಬಲ್"
    what: "AsciiDoc ಎಂಬುದು HTML, PDF, ಮ್ಯಾನುಯಲ್ ಪುಟಗಳು ಮತ್ತು ಇತರ ಫಾರ್ಮ್ಯಾಟ್‌ಗಳಿಗೆ ಪರಿವರ್ತಿಸಬಹುದಾದ ಹಗುರವಾದ ಮಾರ್ಕಪ್ ಭಾಷೆಯಾಗಿದೆ, ತಾಂತ್ರಿಕ ದಾಖಲೀಕರಣ ಬರವಣಿಗೆಗೆ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ."
    step1: "AsciiDoc ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ."
    step3: "ಹೆಡರ್, ಫೂಟರ್ ಮತ್ತು ಶೀರ್ಷಿಕೆ ಸೆಟ್ಟಿಂಗ್‌ಗಳಿಗೆ ಬೆಂಬಲದೊಂದಿಗೆ AsciiDoc ಟೇಬಲ್ ಸಿಂಟ್ಯಾಕ್ಸ್ ಉತ್ಪಾದಿಸಿ, AsciiDoc ಸಂಪಾದಕರಲ್ಲಿ ನೇರವಾಗಿ ಬಳಸಬಹುದಾಗಿದೆ."
    from_alias: "AsciiDoc ಟೇಬಲ್"
    to_alias: "AsciiDoc ಫಾರ್ಮ್ಯಾಟ್"
  reStructuredText:
    alias: "reStructuredText ಟೇಬಲ್"
    what: "reStructuredText ಎಂಬುದು Python ಸಮುದಾಯಕ್ಕೆ ಪ್ರಮಾಣಿತ ದಾಖಲೀಕರಣ ಫಾರ್ಮ್ಯಾಟ್ ಆಗಿದೆ, ಸಮೃದ್ಧ ಟೇಬಲ್ ಸಿಂಟ್ಯಾಕ್ಸ್ ಅನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ, ಸಾಮಾನ್ಯವಾಗಿ Sphinx ದಾಖಲೀಕರಣ ಉತ್ಪಾದನೆಗೆ ಬಳಸಲಾಗುತ್ತದೆ."
    step1: ".rst ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ reStructuredText ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ."
    step3: "ಬಹು ಬಾರ್ಡರ್ ಶೈಲಿಗಳಿಗೆ ಬೆಂಬಲದೊಂದಿಗೆ ಪ್ರಮಾಣಿತ reStructuredText ಟೇಬಲ್‌ಗಳನ್ನು ಉತ್ಪಾದಿಸಿ, Sphinx ದಾಖಲೀಕರಣ ಪ್ರಾಜೆಕ್ಟ್‌ಗಳಲ್ಲಿ ನೇರವಾಗಿ ಬಳಸಬಹುದಾಗಿದೆ."
    from_alias: "reStructuredText ಟೇಬಲ್"
    to_alias: "reStructuredText ಫಾರ್ಮ್ಯಾಟ್"
  PHP:
    alias: "PHP ಅರೇ"
    what: "PHP ಒಂದು ಜನಪ್ರಿಯ ಸರ್ವರ್-ಸೈಡ್ ಸ್ಕ್ರಿಪ್ಟಿಂಗ್ ಭಾಷೆಯಾಗಿದೆ, ಅರೇಗಳು ಅದರ ಮುಖ್ಯ ಡೇಟಾ ರಚನೆಯಾಗಿದೆ, ವೆಬ್ ಅಭಿವೃದ್ಧಿ ಮತ್ತು ಡೇಟಾ ಪ್ರೊಸೆಸಿಂಗ್‌ನಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ."
    step1: "PHP ಅರೇಗಳನ್ನು ಹೊಂದಿರುವ ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ನೇರವಾಗಿ ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ."
    step3: "PHP ಪ್ರಾಜೆಕ್ಟ್‌ಗಳಲ್ಲಿ ನೇರವಾಗಿ ಬಳಸಬಹುದಾದ ಪ್ರಮಾಣಿತ PHP ಅರೇ ಕೋಡ್ ಉತ್ಪಾದಿಸಿ, ಅಸೋಸಿಯೇಟಿವ್ ಮತ್ತು ಇಂಡೆಕ್ಸ್ಡ್ ಅರೇ ಫಾರ್ಮ್ಯಾಟ್‌ಗಳನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
    from_alias: "PHP ಅರೇ"
    to_alias: "PHP ಕೋಡ್"
  Ruby:
    alias: "Ruby ಅರೇ"
    what: "Ruby ಸಂಕ್ಷಿಪ್ತ ಮತ್ತು ಸೊಗಸಾದ ಸಿಂಟ್ಯಾಕ್ಸ್ ಹೊಂದಿರುವ ಡೈನಾಮಿಕ್ ಆಬ್ಜೆಕ್ಟ್-ಓರಿಯೆಂಟೆಡ್ ಪ್ರೋಗ್ರಾಮಿಂಗ್ ಭಾಷೆಯಾಗಿದೆ, ಅರೇಗಳು ಪ್ರಮುಖ ಡೇಟಾ ರಚನೆಯಾಗಿದೆ."
    step1: "Ruby ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ಅರೇ ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ."
    step3: "Ruby ಸಿಂಟ್ಯಾಕ್ಸ್ ವಿಶೇಷಣೆಗಳಿಗೆ ಅನುಗುಣವಾದ Ruby ಅರೇ ಕೋಡ್ ಉತ್ಪಾದಿಸಿ, Ruby ಪ್ರಾಜೆಕ್ಟ್‌ಗಳಲ್ಲಿ ನೇರವಾಗಿ ಬಳಸಬಹುದಾಗಿದೆ."
    from_alias: "Ruby ಅರೇ"
    to_alias: "Ruby ಕೋಡ್"
  ASP:
    alias: "ASP ಅರೇ"
    what: "ASP (Active Server Pages) ಎಂಬುದು Microsoft ನ ಸರ್ವರ್-ಸೈಡ್ ಸ್ಕ್ರಿಪ್ಟಿಂಗ್ ಪರಿಸರವಾಗಿದೆ, ಡೈನಾಮಿಕ್ ವೆಬ್ ಪುಟಗಳನ್ನು ಅಭಿವೃದ್ಧಿಪಡಿಸಲು ಬಹು ಪ್ರೋಗ್ರಾಮಿಂಗ್ ಭಾಷೆಗಳನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
    step1: "ASP ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ಅರೇ ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ."
    step3: "VBScript ಮತ್ತು JScript ಸಿಂಟ್ಯಾಕ್ಸ್‌ಗೆ ಬೆಂಬಲದೊಂದಿಗೆ ASP-ಹೊಂದಾಣಿಕೆಯ ಅರೇ ಕೋಡ್ ಉತ್ಪಾದಿಸಿ, ASP.NET ಪ್ರಾಜೆಕ್ಟ್‌ಗಳಲ್ಲಿ ಬಳಸಬಹುದಾಗಿದೆ."
    from_alias: "ASP ಅರೇ"
    to_alias: "ASP ಕೋಡ್"
  ActionScript:
    alias: "ActionScript ಅರೇ"
    what: "ActionScript ಎಂಬುದು ಮುಖ್ಯವಾಗಿ Adobe Flash ಮತ್ತು AIR ಅಪ್ಲಿಕೇಶನ್ ಅಭಿವೃದ್ಧಿಗೆ ಬಳಸಲಾಗುವ ಆಬ್ಜೆಕ್ಟ್-ಓರಿಯೆಂಟೆಡ್ ಪ್ರೋಗ್ರಾಮಿಂಗ್ ಭಾಷೆಯಾಗಿದೆ."
    step1: ".as ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ActionScript ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ."
    step3: "AS3 ಸಿಂಟ್ಯಾಕ್ಸ್ ಮಾನದಂಡಗಳಿಗೆ ಅನುಗುಣವಾದ ActionScript ಅರೇ ಕೋಡ್ ಉತ್ಪಾದಿಸಿ, Flash ಮತ್ತು Flex ಪ್ರಾಜೆಕ್ಟ್ ಅಭಿವೃದ್ಧಿಗೆ ಬಳಸಬಹುದಾಗಿದೆ."
    from_alias: "ActionScript ಅರೇ"
    to_alias: "ActionScript ಕೋಡ್"
  BBCode:
    alias: "BBCode ಟೇಬಲ್"
    what: "BBCode ಎಂಬುದು ಫೋರಮ್‌ಗಳು ಮತ್ತು ಆನ್‌ಲೈನ್ ಸಮುದಾಯಗಳಲ್ಲಿ ಸಾಮಾನ್ಯವಾಗಿ ಬಳಸಲಾಗುವ ಹಗುರವಾದ ಮಾರ್ಕಪ್ ಭಾಷೆಯಾಗಿದೆ, ಟೇಬಲ್ ಬೆಂಬಲ ಸೇರಿದಂತೆ ಸರಳ ಫಾರ್ಮ್ಯಾಟಿಂಗ್ ಕಾರ್ಯಚಟುವಟಿಕೆಯನ್ನು ಒದಗಿಸುತ್ತದೆ."
    step1: "BBCode ಅನ್ನು ಹೊಂದಿರುವ ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ."
    step3: "ಫೋರಮ್ ಪೋಸ್ಟಿಂಗ್ ಮತ್ತು ಸಮುದಾಯ ವಿಷಯ ರಚನೆಗೆ ಸೂಕ್ತವಾದ BBCode ಟೇಬಲ್ ಕೋಡ್ ಉತ್ಪಾದಿಸಿ, ಕಂಪ್ರೆಸ್ಡ್ ಔಟ್‌ಪುಟ್ ಫಾರ್ಮ್ಯಾಟ್‌ಗೆ ಬೆಂಬಲದೊಂದಿಗೆ."
    from_alias: "BBCode ಟೇಬಲ್"
    to_alias: "BBCode ಫಾರ್ಮ್ಯಾಟ್"
  PDF:
    alias: "PDF ಟೇಬಲ್"
    what: "PDF (Portable Document Format) ಎಂಬುದು ಸ್ಥಿರ ಲೇಔಟ್, ಸ್ಥಿರ ಪ್ರದರ್ಶನ ಮತ್ತು ಉನ್ನತ-ಗುಣಮಟ್ಟದ ಮುದ್ರಣ ಗುಣಲಕ್ಷಣಗಳೊಂದಿಗೆ ಕ್ರಾಸ್-ಪ್ಲಾಟ್‌ಫಾರ್ಮ್ ದಾಖಲೆ ಮಾನದಂಡವಾಗಿದೆ. ಔಪಚಾರಿಕ ದಾಖಲೆಗಳು, ವರದಿಗಳು, ಇನ್‌ವಾಯ್ಸ್‌ಗಳು, ಒಪ್ಪಂದಗಳು ಮತ್ತು ಶೈಕ್ಷಣಿಕ ಪತ್ರಿಕೆಗಳಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ. ವ್ಯಾಪಾರ ಸಂವಹನ ಮತ್ತು ದಾಖಲೆ ಆರ್ಕೈವಿಂಗ್‌ಗೆ ಆದ್ಯತೆಯ ಫಾರ್ಮ್ಯಾಟ್, ವಿವಿಧ ಸಾಧನಗಳು ಮತ್ತು ಆಪರೇಟಿಂಗ್ ಸಿಸ್ಟಮ್‌ಗಳಲ್ಲಿ ಸಂಪೂರ್ಣವಾಗಿ ಸ್ಥಿರವಾದ ದೃಶ್ಯ ಪರಿಣಾಮಗಳನ್ನು ಖಚಿತಪಡಿಸುತ್ತದೆ."
    step1: "ಯಾವುದೇ ಫಾರ್ಮ್ಯಾಟ್‌ನಲ್ಲಿ ಟೇಬಲ್ ಡೇಟಾವನ್ನು ಆಮದು ಮಾಡಿ. ಟೂಲ್ ಸ್ವಯಂಚಾಲಿತವಾಗಿ ಡೇಟಾ ರಚನೆಯನ್ನು ವಿಶ್ಲೇಷಿಸುತ್ತದೆ ಮತ್ತು ಬುದ್ಧಿವಂತ ಲೇಔಟ್ ವಿನ್ಯಾಸವನ್ನು ನಿರ್ವಹಿಸುತ್ತದೆ, ದೊಡ್ಡ ಟೇಬಲ್ ಸ್ವಯಂ-ಪುಟ ವಿಭಜನೆ ಮತ್ತು ಸಂಕೀರ್ಣ ಡೇಟಾ ಪ್ರಕಾರ ಪ್ರೊಸೆಸಿಂಗ್ ಅನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ."
    step3: "ಬಹು ವೃತ್ತಿಪರ ಥೀಮ್ ಶೈಲಿಗಳು (ವ್ಯಾಪಾರ, ಶೈಕ್ಷಣಿಕ, ಸರಳವಾದ, ಇತ್ಯಾದಿ), ಬಹುಭಾಷಾ ಫಾಂಟ್‌ಗಳು, ಸ್ವಯಂ-ಪುಟ ವಿಭಜನೆ, ವಾಟರ್‌ಮಾರ್ಕ್ ಸೇರ್ಪಡೆ ಮತ್ತು ಮುದ್ರಣ ಆಪ್ಟಿಮೈಸೇಶನ್‌ಗೆ ಬೆಂಬಲದೊಂದಿಗೆ ಉನ್ನತ-ಗುಣಮಟ್ಟದ PDF ಟೇಬಲ್ ಫೈಲ್‌ಗಳನ್ನು ಉತ್ಪಾದಿಸಿ. ಔಟ್‌ಪುಟ್ PDF ದಾಖಲೆಗಳು ವೃತ್ತಿಪರ ನೋಟವನ್ನು ಹೊಂದಿವೆ ಎಂದು ಖಚಿತಪಡಿಸುತ್ತದೆ, ವ್ಯಾಪಾರ ಪ್ರಸ್ತುತಿಗಳು ಮತ್ತು ಔಪಚಾರಿಕ ಪ್ರಕಟಣೆಗೆ ನೇರವಾಗಿ ಬಳಸಬಹುದಾಗಿದೆ."
    from_alias: "ಟೇಬಲ್ ಡೇಟಾ"
    to_alias: "PDF ವೃತ್ತಿಪರ ದಾಖಲೆ"
  JPEG:
    alias: "JPEG ಚಿತ್ರ"
    what: "JPEG ಅತ್ಯುತ್ತಮ ಕಂಪ್ರೆಷನ್ ಪರಿಣಾಮಗಳು ಮತ್ತು ವಿಶಾಲ ಹೊಂದಾಣಿಕೆಯೊಂದಿಗೆ ಅತ್ಯಂತ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುವ ಡಿಜಿಟಲ್ ಇಮೇಜ್ ಫಾರ್ಮ್ಯಾಟ್ ಆಗಿದೆ. ಇದರ ಸಣ್ಣ ಫೈಲ್ ಗಾತ್ರ ಮತ್ತು ವೇಗದ ಲೋಡಿಂಗ್ ವೇಗವು ಇದನ್ನು ವೆಬ್ ಪ್ರದರ್ಶನ, ಸಾಮಾಜಿಕ ಮಾಧ್ಯಮ ಹಂಚಿಕೆ, ದಾಖಲೆ ವಿವರಣೆಗಳು ಮತ್ತು ಆನ್‌ಲೈನ್ ಪ್ರಸ್ತುತಿಗಳಿಗೆ ಸೂಕ್ತವಾಗಿಸುತ್ತದೆ. ಡಿಜಿಟಲ್ ಮಾಧ್ಯಮ ಮತ್ತು ನೆಟ್‌ವರ್ಕ್ ಸಂವಹನಕ್ಕೆ ಪ್ರಮಾಣಿತ ಇಮೇಜ್ ಫಾರ್ಮ್ಯಾಟ್, ಬಹುತೇಕ ಎಲ್ಲಾ ಸಾಧನಗಳು ಮತ್ತು ಸಾಫ್ಟ್‌ವೇರ್‌ಗಳಿಂದ ಸಂಪೂರ್ಣವಾಗಿ ಬೆಂಬಲಿತವಾಗಿದೆ."
    step1: "ಯಾವುದೇ ಫಾರ್ಮ್ಯಾಟ್‌ನಲ್ಲಿ ಟೇಬಲ್ ಡೇಟಾವನ್ನು ಆಮದು ಮಾಡಿ. ಟೂಲ್ ಬುದ್ಧಿವಂತ ಲೇಔಟ್ ವಿನ್ಯಾಸ ಮತ್ತು ದೃಶ್ಯ ಆಪ್ಟಿಮೈಸೇಶನ್ ಅನ್ನು ನಿರ್ವಹಿಸುತ್ತದೆ, ಸ್ವಯಂಚಾಲಿತವಾಗಿ ಅತ್ಯುತ್ತಮ ಗಾತ್ರ ಮತ್ತು ರೆಸಲ್ಯೂಶನ್ ಅನ್ನು ಲೆಕ್ಕಾಚಾರ ಮಾಡುತ್ತದೆ."
    step3: "ಬಹು ಥೀಮ್ ಬಣ್ಣ ಯೋಜನೆಗಳು (ಹಗುರ, ಗಾಢ, ಕಣ್ಣು-ಸ್ನೇಹಿ, ಇತ್ಯಾದಿ), ಅಡಾಪ್ಟಿವ್ ಲೇಔಟ್, ಪಠ್ಯ ಸ್ಪಷ್ಟತೆ ಆಪ್ಟಿಮೈಸೇಶನ್ ಮತ್ತು ಗಾತ್ರ ಕಸ್ಟಮೈಸೇಶನ್‌ಗೆ ಬೆಂಬಲದೊಂದಿಗೆ ಹೆಚ್ಚಿನ ರೆಸಲ್ಯೂಶನ್ JPEG ಟೇಬಲ್ ಚಿತ್ರಗಳನ್ನು ಉತ್ಪಾದಿಸಿ. ಆನ್‌ಲೈನ್ ಹಂಚಿಕೆ, ದಾಖಲೆ ಸೇರ್ಪಡೆ ಮತ್ತು ಪ್ರಸ್ತುತಿ ಬಳಕೆಗೆ ಸೂಕ್ತ, ವಿವಿಧ ಪ್ರದರ್ಶನ ಸಾಧನಗಳಲ್ಲಿ ಅತ್ಯುತ್ತಮ ದೃಶ್ಯ ಪರಿಣಾಮಗಳನ್ನು ಖಚಿತಪಡಿಸುತ್ತದೆ."
    from_alias: "ಟೇಬಲ್ ಡೇಟಾ"
    to_alias: "JPEG ಹೆಚ್ಚಿನ ರೆಸಲ್ಯೂಶನ್ ಚಿತ್ರ"
  Jira:
    alias: "Jira ಟೇಬಲ್"
    what: "JIRA ಎಂಬುದು Atlassian ನಿಂದ ಅಭಿವೃದ್ಧಿಪಡಿಸಿದ ವೃತ್ತಿಪರ ಪ್ರಾಜೆಕ್ಟ್ ನಿರ್ವಹಣೆ ಮತ್ತು ಬಗ್ ಟ್ರ್ಯಾಕಿಂಗ್ ಸಾಫ್ಟ್‌ವೇರ್ ಆಗಿದೆ, ಚುರುಕು ಅಭಿವೃದ್ಧಿ, ಸಾಫ್ಟ್‌ವೇರ್ ಪರೀಕ್ಷೆ ಮತ್ತು ಪ್ರಾಜೆಕ್ಟ್ ಸಹಯೋಗದಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ. ಇದರ ಟೇಬಲ್ ಕಾರ್ಯಚಟುವಟಿಕೆ ಸಮೃದ್ಧ ಫಾರ್ಮ್ಯಾಟಿಂಗ್ ಆಯ್ಕೆಗಳು ಮತ್ತು ಡೇಟಾ ಪ್ರದರ್ಶನವನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ, ಅವಶ್ಯಕತೆ ನಿರ್ವಹಣೆ, ಬಗ್ ಟ್ರ್ಯಾಕಿಂಗ್ ಮತ್ತು ಪ್ರಗತಿ ವರದಿಯಲ್ಲಿ ಸಾಫ್ಟ್‌ವೇರ್ ಅಭಿವೃದ್ಧಿ ತಂಡಗಳು, ಪ್ರಾಜೆಕ್ಟ್ ಮ್ಯಾನೇಜರ್‌ಗಳು ಮತ್ತು ಗುಣಮಟ್ಟ ಭರವಸೆ ಸಿಬ್ಬಂದಿಗೆ ಪ್ರಮುಖ ಸಾಧನವಾಗಿ ಕಾರ್ಯನಿರ್ವಹಿಸುತ್ತದೆ."
    step1: "ಟೇಬಲ್ ಡೇಟಾವನ್ನು ಹೊಂದಿರುವ ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ನೇರವಾಗಿ ಡೇಟಾ ವಿಷಯವನ್ನು ಅಂಟಿಸಿ. ಟೂಲ್ ಸ್ವಯಂಚಾಲಿತವಾಗಿ ಟೇಬಲ್ ಡೇಟಾ ಮತ್ತು ವಿಶೇಷ ಅಕ್ಷರ ಎಸ್ಕೇಪಿಂಗ್ ಅನ್ನು ಪ್ರಕ್ರಿಯೆಗೊಳಿಸುತ್ತದೆ."
    step3: "ಹೆಡರ್ ಶೈಲಿ ಸೆಟ್ಟಿಂಗ್‌ಗಳು, ಸೆಲ್ ಅಲೈನ್‌ಮೆಂಟ್, ಅಕ್ಷರ ಎಸ್ಕೇಪ್ ಪ್ರೊಸೆಸಿಂಗ್ ಮತ್ತು ಫಾರ್ಮ್ಯಾಟ್ ಆಪ್ಟಿಮೈಸೇಶನ್‌ಗೆ ಬೆಂಬಲದೊಂದಿಗೆ JIRA ಪ್ಲಾಟ್‌ಫಾರ್ಮ್-ಹೊಂದಾಣಿಕೆಯ ಟೇಬಲ್ ಕೋಡ್ ಉತ್ಪಾದಿಸಿ. ಉತ್ಪಾದಿಸಿದ ಕೋಡ್ ಅನ್ನು JIRA ಸಮಸ್ಯೆ ವಿವರಣೆಗಳು, ಕಾಮೆಂಟ್‌ಗಳು ಅಥವಾ ವಿಕಿ ಪುಟಗಳಲ್ಲಿ ನೇರವಾಗಿ ಅಂಟಿಸಬಹುದು, JIRA ಸಿಸ್ಟಮ್‌ಗಳಲ್ಲಿ ಸರಿಯಾದ ಪ್ರದರ್ಶನ ಮತ್ತು ರೆಂಡರಿಂಗ್ ಅನ್ನು ಖಚಿತಪಡಿಸುತ್ತದೆ."
    from_alias: "ಪ್ರಾಜೆಕ್ಟ್ ಡೇಟಾ"
    to_alias: "Jira ಟೇಬಲ್ ಸಿಂಟ್ಯಾಕ್ಸ್"
  Textile:
    alias: "Textile ಟೇಬಲ್"
    what: "Textile ಎಂಬುದು ಸರಳ ಮತ್ತು ಕಲಿಯಲು ಸುಲಭವಾದ ಸಿಂಟ್ಯಾಕ್ಸ್ ಹೊಂದಿರುವ ಸಂಕ್ಷಿಪ್ತ ಹಗುರವಾದ ಮಾರ್ಕಪ್ ಭಾಷೆಯಾಗಿದೆ, ವಿಷಯ ನಿರ್ವಹಣಾ ವ್ಯವಸ್ಥೆಗಳು, ಬ್ಲಾಗ್ ಪ್ಲಾಟ್‌ಫಾರ್ಮ್‌ಗಳು ಮತ್ತು ಫೋರಮ್ ಸಿಸ್ಟಮ್‌ಗಳಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ. ಇದರ ಟೇಬಲ್ ಸಿಂಟ್ಯಾಕ್ಸ್ ಸ್ಪಷ್ಟ ಮತ್ತು ಅರ್ಥಗರ್ಭಿತವಾಗಿದೆ, ತ್ವರಿತ ಫಾರ್ಮ್ಯಾಟಿಂಗ್ ಮತ್ತು ಶೈಲಿ ಸೆಟ್ಟಿಂಗ್‌ಗಳನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ. ತ್ವರಿತ ದಾಖಲೆ ಬರವಣಿಗೆ ಮತ್ತು ವಿಷಯ ಪ್ರಕಟಣೆಗಾಗಿ ವಿಷಯ ಸೃಷ್ಟಿಕರ್ತರು ಮತ್ತು ವೆಬ್‌ಸೈಟ್ ನಿರ್ವಾಹಕರಿಗೆ ಆದರ್ಶ ಸಾಧನ."
    step1: "Textile ಫಾರ್ಮ್ಯಾಟ್ ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ಟೇಬಲ್ ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ. ಟೂಲ್ Textile ಮಾರ್ಕಪ್ ಸಿಂಟ್ಯಾಕ್ಸ್ ಅನ್ನು ಪಾರ್ಸ್ ಮಾಡುತ್ತದೆ ಮತ್ತು ಟೇಬಲ್ ವಿಷಯವನ್ನು ಹೊರತೆಗೆಯುತ್ತದೆ."
    step3: "ಹೆಡರ್ ಮಾರ್ಕಪ್, ಸೆಲ್ ಅಲೈನ್‌ಮೆಂಟ್, ವಿಶೇಷ ಅಕ್ಷರ ಎಸ್ಕೇಪಿಂಗ್ ಮತ್ತು ಫಾರ್ಮ್ಯಾಟ್ ಆಪ್ಟಿಮೈಸೇಶನ್‌ಗೆ ಬೆಂಬಲದೊಂದಿಗೆ ಪ್ರಮಾಣಿತ Textile ಟೇಬಲ್ ಸಿಂಟ್ಯಾಕ್ಸ್ ಉತ್ಪಾದಿಸಿ. ಉತ್ಪಾದಿಸಿದ ಕೋಡ್ ಅನ್ನು CMS ಸಿಸ್ಟಮ್‌ಗಳು, ಬ್ಲಾಗ್ ಪ್ಲಾಟ್‌ಫಾರ್ಮ್‌ಗಳು ಮತ್ತು Textile ಅನ್ನು ಬೆಂಬಲಿಸುವ ದಾಖಲೆ ಸಿಸ್ಟಮ್‌ಗಳಲ್ಲಿ ನೇರವಾಗಿ ಬಳಸಬಹುದು, ಸರಿಯಾದ ವಿಷಯ ರೆಂಡರಿಂಗ್ ಮತ್ತು ಪ್ರದರ್ಶನವನ್ನು ಖಚಿತಪಡಿಸುತ್ತದೆ."
    from_alias: "Textile ದಾಖಲೆ"
    to_alias: "Textile ಟೇಬಲ್ ಸಿಂಟ್ಯಾಕ್ಸ್"
  PNG:
    alias: "PNG ಚಿತ್ರ"
    what: "PNG (Portable Network Graphics) ಅತ್ಯುತ್ತಮ ಕಂಪ್ರೆಷನ್ ಮತ್ತು ಪಾರದರ್ಶಕತೆ ಬೆಂಬಲದೊಂದಿಗೆ ನಷ್ಟರಹಿತ ಇಮೇಜ್ ಫಾರ್ಮ್ಯಾಟ್ ಆಗಿದೆ. ವೆಬ್ ವಿನ್ಯಾಸ, ಡಿಜಿಟಲ್ ಗ್ರಾಫಿಕ್ಸ್ ಮತ್ತು ವೃತ್ತಿಪರ ಛಾಯಾಗ್ರಹಣದಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ. ಇದರ ಉನ್ನತ ಗುಣಮಟ್ಟ ಮತ್ತು ವಿಶಾಲ ಹೊಂದಾಣಿಕೆಯು ಇದನ್ನು ಸ್ಕ್ರೀನ್‌ಶಾಟ್‌ಗಳು, ಲೋಗೋಗಳು, ರೇಖಾಚಿತ್ರಗಳು ಮತ್ತು ಸ್ಪಷ್ಟ ವಿವರಗಳು ಮತ್ತು ಪಾರದರ್ಶಕ ಹಿನ್ನೆಲೆಗಳ ಅಗತ್ಯವಿರುವ ಯಾವುದೇ ಚಿತ್ರಗಳಿಗೆ ಆದರ್ಶವಾಗಿಸುತ್ತದೆ."
    step1: "ಯಾವುದೇ ಫಾರ್ಮ್ಯಾಟ್‌ನಲ್ಲಿ ಟೇಬಲ್ ಡೇಟಾವನ್ನು ಆಮದು ಮಾಡಿ. ಟೂಲ್ ಬುದ್ಧಿವಂತ ಲೇಔಟ್ ವಿನ್ಯಾಸ ಮತ್ತು ದೃಶ್ಯ ಆಪ್ಟಿಮೈಸೇಶನ್ ಅನ್ನು ನಿರ್ವಹಿಸುತ್ತದೆ, PNG ಔಟ್‌ಪುಟ್‌ಗಾಗಿ ಸ್ವಯಂಚಾಲಿತವಾಗಿ ಅತ್ಯುತ್ತಮ ಗಾತ್ರ ಮತ್ತು ರೆಸಲ್ಯೂಶನ್ ಅನ್ನು ಲೆಕ್ಕಾಚಾರ ಮಾಡುತ್ತದೆ."
    step3: "ಬಹು ಥೀಮ್ ಬಣ್ಣ ಯೋಜನೆಗಳು, ಪಾರದರ್ಶಕ ಹಿನ್ನೆಲೆಗಳು, ಅಡಾಪ್ಟಿವ್ ಲೇಔಟ್ ಮತ್ತು ಪಠ್ಯ ಸ್ಪಷ್ಟತೆ ಆಪ್ಟಿಮೈಸೇಶನ್‌ಗೆ ಬೆಂಬಲದೊಂದಿಗೆ ಉನ್ನತ-ಗುಣಮಟ್ಟದ PNG ಟೇಬಲ್ ಚಿತ್ರಗಳನ್ನು ಉತ್ಪಾದಿಸಿ. ಅತ್ಯುತ್ತಮ ದೃಶ್ಯ ಗುಣಮಟ್ಟದೊಂದಿಗೆ ವೆಬ್ ಬಳಕೆ, ದಾಖಲೆ ಸೇರ್ಪಡೆ ಮತ್ತು ವೃತ್ತಿಪರ ಪ್ರಸ್ತುತಿಗಳಿಗೆ ಪರಿಪೂರ್ಣವಾಗಿದೆ."
    from_alias: "ಟೇಬಲ್ ಡೇಟಾ"
    to_alias: "PNG ಉನ್ನತ-ಗುಣಮಟ್ಟದ ಚಿತ್ರ"
  TOML:
    alias: "TOML ಕಾನ್ಫಿಗರೇಶನ್"
    what: "TOML (Tom's Obvious, Minimal Language) ಓದಲು ಮತ್ತು ಬರೆಯಲು ಸುಲಭವಾದ ಕಾನ್ಫಿಗರೇಶನ್ ಫೈಲ್ ಫಾರ್ಮ್ಯಾಟ್ ಆಗಿದೆ. ಅಸ್ಪಷ್ಟತೆಯಿಲ್ಲದ ಮತ್ತು ಸರಳವಾಗಿರಲು ವಿನ್ಯಾಸಗೊಳಿಸಲಾಗಿದೆ, ಇದನ್ನು ಕಾನ್ಫಿಗರೇಶನ್ ನಿರ್ವಹಣೆಗಾಗಿ ಆಧುನಿಕ ಸಾಫ್ಟ್‌ವೇರ್ ಪ್ರಾಜೆಕ್ಟ್‌ಗಳಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ. ಇದರ ಸ್ಪಷ್ಟ ಸಿಂಟ್ಯಾಕ್ಸ್ ಮತ್ತು ಬಲವಾದ ಟೈಪಿಂಗ್ ಇದನ್ನು ಅಪ್ಲಿಕೇಶನ್ ಸೆಟ್ಟಿಂಗ್‌ಗಳು ಮತ್ತು ಪ್ರಾಜೆಕ್ಟ್ ಕಾನ್ಫಿಗರೇಶನ್ ಫೈಲ್‌ಗಳಿಗೆ ಅತ್ಯುತ್ತಮ ಆಯ್ಕೆಯನ್ನಾಗಿ ಮಾಡುತ್ತದೆ."
    step1: "TOML ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ಕಾನ್ಫಿಗರೇಶನ್ ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ. ಟೂಲ್ TOML ಸಿಂಟ್ಯಾಕ್ಸ್ ಅನ್ನು ಪಾರ್ಸ್ ಮಾಡುತ್ತದೆ ಮತ್ತು ರಚನಾತ್ಮಕ ಕಾನ್ಫಿಗರೇಶನ್ ಮಾಹಿತಿಯನ್ನು ಹೊರತೆಗೆಯುತ್ತದೆ."
    step3: "ನೆಸ್ಟೆಡ್ ರಚನೆಗಳು, ಡೇಟಾ ಪ್ರಕಾರಗಳು ಮತ್ತು ಕಾಮೆಂಟ್‌ಗಳಿಗೆ ಬೆಂಬಲದೊಂದಿಗೆ ಪ್ರಮಾಣಿತ TOML ಫಾರ್ಮ್ಯಾಟ್ ಉತ್ಪಾದಿಸಿ. ಉತ್ಪಾದಿಸಿದ TOML ಫೈಲ್‌ಗಳು ಅಪ್ಲಿಕೇಶನ್ ಕಾನ್ಫಿಗರೇಶನ್, ಬಿಲ್ಡ್ ಟೂಲ್‌ಗಳು ಮತ್ತು ಪ್ರಾಜೆಕ್ಟ್ ಸೆಟ್ಟಿಂಗ್‌ಗಳಿಗೆ ಪರಿಪೂರ್ಣವಾಗಿವೆ."
    from_alias: "TOML ಕಾನ್ಫಿಗರೇಶನ್"
    to_alias: "TOML ಫಾರ್ಮ್ಯಾಟ್"
  INI:
    alias: "INI ಕಾನ್ಫಿಗರೇಶನ್"
    what: "INI ಫೈಲ್‌ಗಳು ಅನೇಕ ಅಪ್ಲಿಕೇಶನ್‌ಗಳು ಮತ್ತು ಆಪರೇಟಿಂಗ್ ಸಿಸ್ಟಮ್‌ಗಳಿಂದ ಬಳಸಲ್ಪಡುವ ಸರಳ ಕಾನ್ಫಿಗರೇಶನ್ ಫೈಲ್‌ಗಳಾಗಿವೆ. ಅವುಗಳ ನೇರವಾದ ಕೀ-ವ್ಯಾಲ್ಯೂ ಜೋಡಿ ರಚನೆಯು ಅವುಗಳನ್ನು ಓದಲು ಮತ್ತು ಹಸ್ತಚಾಲಿತವಾಗಿ ಸಂಪಾದಿಸಲು ಸುಲಭಗೊಳಿಸುತ್ತದೆ. Windows ಅಪ್ಲಿಕೇಶನ್‌ಗಳು, ಪರಂಪರೆ ಸಿಸ್ಟಮ್‌ಗಳು ಮತ್ತು ಮಾನವ ಓದುವಿಕೆ ಮುಖ್ಯವಾದ ಸರಳ ಕಾನ್ಫಿಗರೇಶನ್ ಸನ್ನಿವೇಶಗಳಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ."
    step1: "INI ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ಕಾನ್ಫಿಗರೇಶನ್ ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ. ಟೂಲ್ INI ಸಿಂಟ್ಯಾಕ್ಸ್ ಅನ್ನು ಪಾರ್ಸ್ ಮಾಡುತ್ತದೆ ಮತ್ತು ವಿಭಾಗ-ಆಧಾರಿತ ಕಾನ್ಫಿಗರೇಶನ್ ಮಾಹಿತಿಯನ್ನು ಹೊರತೆಗೆಯುತ್ತದೆ."
    step3: "ವಿಭಾಗಗಳು, ಕಾಮೆಂಟ್‌ಗಳು ಮತ್ತು ವಿವಿಧ ಡೇಟಾ ಪ್ರಕಾರಗಳಿಗೆ ಬೆಂಬಲದೊಂದಿಗೆ ಪ್ರಮಾಣಿತ INI ಫಾರ್ಮ್ಯಾಟ್ ಉತ್ಪಾದಿಸಿ. ಉತ್ಪಾದಿಸಿದ INI ಫೈಲ್‌ಗಳು ಹೆಚ್ಚಿನ ಅಪ್ಲಿಕೇಶನ್‌ಗಳು ಮತ್ತು ಕಾನ್ಫಿಗರೇಶನ್ ಸಿಸ್ಟಮ್‌ಗಳೊಂದಿಗೆ ಹೊಂದಿಕೊಳ್ಳುತ್ತವೆ."
    from_alias: "INI ಕಾನ್ಫಿಗರೇಶನ್"
    to_alias: "INI ಫಾರ್ಮ್ಯಾಟ್"
  Avro:
    alias: "Avro ಸ್ಕೀಮಾ"
    what: "Apache Avro ಸಮೃದ್ಧ ಡೇಟಾ ರಚನೆಗಳು, ಸಂಕುಚಿತ ಬೈನರಿ ಫಾರ್ಮ್ಯಾಟ್ ಮತ್ತು ಸ್ಕೀಮಾ ವಿಕಸನ ಸಾಮರ್ಥ್ಯಗಳನ್ನು ಒದಗಿಸುವ ಡೇಟಾ ಸೀರಿಯಲೈಸೇಶನ್ ಸಿಸ್ಟಮ್ ಆಗಿದೆ. ಬಿಗ್ ಡೇಟಾ ಪ್ರೊಸೆಸಿಂಗ್, ಮೆಸೇಜ್ ಕ್ಯೂಗಳು ಮತ್ತು ವಿತರಿಸಿದ ಸಿಸ್ಟಮ್‌ಗಳಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ. ಇದರ ಸ್ಕೀಮಾ ವ್ಯಾಖ್ಯಾನವು ಸಂಕೀರ್ಣ ಡೇಟಾ ಪ್ರಕಾರಗಳು ಮತ್ತು ಆವೃತ್ತಿ ಹೊಂದಾಣಿಕೆಯನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ, ಇದು ಡೇಟಾ ಇಂಜಿನಿಯರ್‌ಗಳು ಮತ್ತು ಸಿಸ್ಟಮ್ ಆರ್ಕಿಟೆಕ್ಟ್‌ಗಳಿಗೆ ಪ್ರಮುಖ ಸಾಧನವಾಗಿದೆ."
    step1: "Avro ಸ್ಕೀಮಾ ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ. ಟೂಲ್ Avro ಸ್ಕೀಮಾ ವ್ಯಾಖ್ಯಾನಗಳನ್ನು ಪಾರ್ಸ್ ಮಾಡುತ್ತದೆ ಮತ್ತು ಟೇಬಲ್ ರಚನೆ ಮಾಹಿತಿಯನ್ನು ಹೊರತೆಗೆಯುತ್ತದೆ."
    step3: "ಡೇಟಾ ಟೈಪ್ ಮ್ಯಾಪಿಂಗ್, ಫೀಲ್ಡ್ ನಿರ್ಬಂಧಗಳು ಮತ್ತು ಸ್ಕೀಮಾ ಮೌಲ್ಯೀಕರಣಕ್ಕೆ ಬೆಂಬಲದೊಂದಿಗೆ ಪ್ರಮಾಣಿತ Avro ಸ್ಕೀಮಾ ವ್ಯಾಖ್ಯಾನಗಳನ್ನು ಉತ್ಪಾದಿಸಿ. ಉತ್ಪಾದಿಸಿದ ಸ್ಕೀಮಾಗಳನ್ನು Hadoop ಪರಿಸರ ವ್ಯವಸ್ಥೆಗಳು, Kafka ಮೆಸೇಜ್ ಸಿಸ್ಟಮ್‌ಗಳು ಮತ್ತು ಇತರ ಬಿಗ್ ಡೇಟಾ ಪ್ಲಾಟ್‌ಫಾರ್ಮ್‌ಗಳಲ್ಲಿ ನೇರವಾಗಿ ಬಳಸಬಹುದು."
    from_alias: "Avro ಸ್ಕೀಮಾ"
    to_alias: "Avro ಡೇಟಾ ಫಾರ್ಮ್ಯಾಟ್"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) ಎಂಬುದು ರಚನಾತ್ಮಕ ಡೇಟಾವನ್ನು ಸೀರಿಯಲೈಸ್ ಮಾಡಲು Google ನ ಭಾಷಾ-ತಟಸ್ಥ, ಪ್ಲಾಟ್‌ಫಾರ್ಮ್-ತಟಸ್ಥ, ವಿಸ್ತರಿಸಬಹುದಾದ ಕಾರ್ಯವಿಧಾನವಾಗಿದೆ. ಮೈಕ್ರೋಸರ್ವಿಸ್‌ಗಳು, API ಅಭಿವೃದ್ಧಿ ಮತ್ತು ಡೇಟಾ ಸಂಗ್ರಹಣೆಯಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ. ಇದರ ಪರಿಣಾಮಕಾರಿ ಬೈನರಿ ಫಾರ್ಮ್ಯಾಟ್ ಮತ್ತು ಬಲವಾದ ಟೈಪಿಂಗ್ ಇದನ್ನು ಹೆಚ್ಚಿನ ಕಾರ್ಯಕ್ಷಮತೆಯ ಅಪ್ಲಿಕೇಶನ್‌ಗಳು ಮತ್ತು ಕ್ರಾಸ್-ಲ್ಯಾಂಗ್ವೇಜ್ ಸಂವಹನಕ್ಕೆ ಆದರ್ಶವಾಗಿಸುತ್ತದೆ."
    step1: ".proto ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ Protocol Buffer ವ್ಯಾಖ್ಯಾನಗಳನ್ನು ಅಂಟಿಸಿ. ಟೂಲ್ protobuf ಸಿಂಟ್ಯಾಕ್ಸ್ ಅನ್ನು ಪಾರ್ಸ್ ಮಾಡುತ್ತದೆ ಮತ್ತು ಮೆಸೇಜ್ ರಚನೆ ಮಾಹಿತಿಯನ್ನು ಹೊರತೆಗೆಯುತ್ತದೆ."
    step3: "ಮೆಸೇಜ್ ಪ್ರಕಾರಗಳು, ಫೀಲ್ಡ್ ಆಯ್ಕೆಗಳು ಮತ್ತು ಸೇವಾ ವ್ಯಾಖ್ಯಾನಗಳಿಗೆ ಬೆಂಬಲದೊಂದಿಗೆ ಪ್ರಮಾಣಿತ Protocol Buffer ವ್ಯಾಖ್ಯಾನಗಳನ್ನು ಉತ್ಪಾದಿಸಿ. ಉತ್ಪಾದಿಸಿದ .proto ಫೈಲ್‌ಗಳನ್ನು ಬಹು ಪ್ರೋಗ್ರಾಮಿಂಗ್ ಭಾಷೆಗಳಿಗೆ ಕಂಪೈಲ್ ಮಾಡಬಹುದು."
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf ಸ್ಕೀಮಾ"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas ಎಂಬುದು Python ನಲ್ಲಿ ಅತ್ಯಂತ ಜನಪ್ರಿಯ ಡೇಟಾ ವಿಶ್ಲೇಷಣೆ ಲೈಬ್ರರಿಯಾಗಿದೆ, DataFrame ಅದರ ಮುಖ್ಯ ಡೇಟಾ ರಚನೆಯಾಗಿದೆ. ಇದು ಶಕ್ತಿಶಾಲಿ ಡೇಟಾ ಮ್ಯಾನಿಪ್ಯುಲೇಶನ್, ಶುಚಿಗೊಳಿಸುವಿಕೆ ಮತ್ತು ವಿಶ್ಲೇಷಣೆ ಸಾಮರ್ಥ್ಯಗಳನ್ನು ಒದಗಿಸುತ್ತದೆ, ಡೇಟಾ ಸೈನ್ಸ್, ಮೆಷಿನ್ ಲರ್ನಿಂಗ್ ಮತ್ತು ಬಿಸಿನೆಸ್ ಇಂಟೆಲಿಜೆನ್ಸ್‌ನಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ. Python ಡೆವಲಪರ್‌ಗಳು ಮತ್ತು ಡೇಟಾ ವಿಶ್ಲೇಷಕರಿಗೆ ಅಗತ್ಯವಾದ ಸಾಧನ."
    step1: "DataFrame ಕೋಡ್ ಹೊಂದಿರುವ Python ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ. ಟೂಲ್ Pandas ಸಿಂಟ್ಯಾಕ್ಸ್ ಅನ್ನು ಪಾರ್ಸ್ ಮಾಡುತ್ತದೆ ಮತ್ತು DataFrame ರಚನೆ ಮಾಹಿತಿಯನ್ನು ಹೊರತೆಗೆಯುತ್ತದೆ."
    step3: "ಡೇಟಾ ಟೈಪ್ ವಿಶೇಷಣೆಗಳು, ಇಂಡೆಕ್ಸ್ ಸೆಟ್ಟಿಂಗ್‌ಗಳು ಮತ್ತು ಡೇಟಾ ಕಾರ್ಯಾಚರಣೆಗಳಿಗೆ ಬೆಂಬಲದೊಂದಿಗೆ ಪ್ರಮಾಣಿತ Pandas DataFrame ಕೋಡ್ ಉತ್ಪಾದಿಸಿ. ಉತ್ಪಾದಿಸಿದ ಕೋಡ್ ಅನ್ನು ಡೇಟಾ ವಿಶ್ಲೇಷಣೆ ಮತ್ತು ಪ್ರೊಸೆಸಿಂಗ್‌ಗಾಗಿ Python ಪರಿಸರದಲ್ಲಿ ನೇರವಾಗಿ ಕಾರ್ಯಗತಗೊಳಿಸಬಹುದು."
    from_alias: "Pandas DataFrame"
    to_alias: "Python ಡೇಟಾ ರಚನೆ"
  RDF:
    alias: "RDF ಟ್ರಿಪಲ್"
    what: "RDF (Resource Description Framework) ವೆಬ್‌ನಲ್ಲಿ ಡೇಟಾ ವಿನಿಮಯಕ್ಕಾಗಿ ಪ್ರಮಾಣಿತ ಮಾದರಿಯಾಗಿದೆ, ಸಂಪನ್ಮೂಲಗಳ ಬಗ್ಗೆ ಮಾಹಿತಿಯನ್ನು ಗ್ರಾಫ್ ರೂಪದಲ್ಲಿ ಪ್ರತಿನಿಧಿಸಲು ವಿನ್ಯಾಸಗೊಳಿಸಲಾಗಿದೆ. ಸೆಮ್ಯಾಂಟಿಕ್ ವೆಬ್, ಜ್ಞಾನ ಗ್ರಾಫ್‌ಗಳು ಮತ್ತು ಲಿಂಕ್ಡ್ ಡೇಟಾ ಅಪ್ಲಿಕೇಶನ್‌ಗಳಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ. ಇದರ ಟ್ರಿಪಲ್ ರಚನೆಯು ಸಮೃದ್ಧ ಮೆಟಾಡೇಟಾ ಪ್ರಾತಿನಿಧ್ಯ ಮತ್ತು ಸೆಮ್ಯಾಂಟಿಕ್ ಸಂಬಂಧಗಳನ್ನು ಸಕ್ರಿಯಗೊಳಿಸುತ್ತದೆ."
    step1: "RDF ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ಟ್ರಿಪಲ್ ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ. ಟೂಲ್ RDF ಸಿಂಟ್ಯಾಕ್ಸ್ ಅನ್ನು ಪಾರ್ಸ್ ಮಾಡುತ್ತದೆ ಮತ್ತು ಸೆಮ್ಯಾಂಟಿಕ್ ಸಂಬಂಧಗಳು ಮತ್ತು ಸಂಪನ್ಮೂಲ ಮಾಹಿತಿಯನ್ನು ಹೊರತೆಗೆಯುತ್ತದೆ."
    step3: "ವಿವಿಧ ಸೀರಿಯಲೈಸೇಶನ್‌ಗಳಿಗೆ (RDF/XML, Turtle, N-Triples) ಬೆಂಬಲದೊಂದಿಗೆ ಪ್ರಮಾಣಿತ RDF ಫಾರ್ಮ್ಯಾಟ್ ಉತ್ಪಾದಿಸಿ. ಉತ್ಪಾದಿಸಿದ RDF ಅನ್ನು ಸೆಮ್ಯಾಂಟಿಕ್ ವೆಬ್ ಅಪ್ಲಿಕೇಶನ್‌ಗಳು, ಜ್ಞಾನ ಬೇಸ್‌ಗಳು ಮತ್ತು ಲಿಂಕ್ಡ್ ಡೇಟಾ ಸಿಸ್ಟಮ್‌ಗಳಲ್ಲಿ ಬಳಸಬಹುದು."
    from_alias: "RDF ಡೇಟಾ"
    to_alias: "RDF ಸೆಮ್ಯಾಂಟಿಕ್ ಫಾರ್ಮ್ಯಾಟ್"
  MATLAB:
    alias: "MATLAB ಅರೇ"
    what: "MATLAB ಎಂಬುದು ಇಂಜಿನಿಯರಿಂಗ್ ಕಂಪ್ಯೂಟಿಂಗ್, ಡೇಟಾ ವಿಶ್ಲೇಷಣೆ ಮತ್ತು ಅಲ್ಗಾರಿದಮ್ ಅಭಿವೃದ್ಧಿಯಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುವ ಹೆಚ್ಚಿನ ಕಾರ್ಯಕ್ಷಮತೆಯ ಸಂಖ್ಯಾತ್ಮಕ ಕಂಪ್ಯೂಟಿಂಗ್ ಮತ್ತು ವಿಸುವಲೈಸೇಶನ್ ಸಾಫ್ಟ್‌ವೇರ್ ಆಗಿದೆ. ಇದರ ಅರೇ ಮತ್ತು ಮ್ಯಾಟ್ರಿಕ್ಸ್ ಕಾರ್ಯಾಚರಣೆಗಳು ಶಕ್ತಿಶಾಲಿಯಾಗಿದ್ದು, ಸಂಕೀರ್ಣ ಗಣಿತದ ಲೆಕ್ಕಾಚಾರಗಳು ಮತ್ತು ಡೇಟಾ ಪ್ರೊಸೆಸಿಂಗ್ ಅನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ. ಇಂಜಿನಿಯರ್‌ಗಳು, ಸಂಶೋಧಕರು ಮತ್ತು ಡೇಟಾ ವಿಜ್ಞಾನಿಗಳಿಗೆ ಅತ್ಯಗತ್ಯ ಸಾಧನ."
    step1: "MATLAB .m ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ಅರೇ ಡೇಟಾವನ್ನು ಅಂಟಿಸಿ. ಟೂಲ್ MATLAB ಸಿಂಟ್ಯಾಕ್ಸ್ ಅನ್ನು ಪಾರ್ಸ್ ಮಾಡುತ್ತದೆ ಮತ್ತು ಅರೇ ರಚನೆ ಮಾಹಿತಿಯನ್ನು ಹೊರತೆಗೆಯುತ್ತದೆ."
    step3: "ಬಹು-ಆಯಾಮದ ಅರೇಗಳು, ಡೇಟಾ ಟೈಪ್ ವಿಶೇಷಣೆಗಳು ಮತ್ತು ವೇರಿಯೇಬಲ್ ನಾಮಕರಣಕ್ಕೆ ಬೆಂಬಲದೊಂದಿಗೆ ಪ್ರಮಾಣಿತ MATLAB ಅರೇ ಕೋಡ್ ಉತ್ಪಾದಿಸಿ. ಉತ್ಪಾದಿಸಿದ ಕೋಡ್ ಅನ್ನು ಡೇಟಾ ವಿಶ್ಲೇಷಣೆ ಮತ್ತು ವೈಜ್ಞಾನಿಕ ಕಂಪ್ಯೂಟಿಂಗ್‌ಗಾಗಿ MATLAB ಪರಿಸರದಲ್ಲಿ ನೇರವಾಗಿ ಕಾರ್ಯಗತಗೊಳಿಸಬಹುದು."
    from_alias: "MATLAB ಅರೇ"
    to_alias: "MATLAB ಕೋಡ್ ಫಾರ್ಮ್ಯಾಟ್"
  RDataFrame:
    alias: "R ಡೇಟಾಫ್ರೇಮ್"
    what: "R ಡೇಟಾಫ್ರೇಮ್ ಎಂಬುದು R ಪ್ರೋಗ್ರಾಮಿಂಗ್ ಭಾಷೆಯಲ್ಲಿನ ಮುಖ್ಯ ಡೇಟಾ ರಚನೆಯಾಗಿದೆ, ಇದನ್ನು ಅಂಕಿಅಂಶಗಳ ವಿಶ್ಲೇಷಣೆ, ಡೇಟಾ ಮೈನಿಂಗ್ ಮತ್ತು ಮೆಷಿನ್ ಲರ್ನಿಂಗ್‌ನಲ್ಲಿ ವ್ಯಾಪಕವಾಗಿ ಬಳಸಲಾಗುತ್ತದೆ. R ಎಂಬುದು ಅಂಕಿಅಂಶಗಳ ಕಂಪ್ಯೂಟಿಂಗ್ ಮತ್ತು ಗ್ರಾಫಿಕ್ಸ್‌ಗೆ ಪ್ರಮುಖ ಸಾಧನವಾಗಿದೆ, ಡೇಟಾಫ್ರೇಮ್ ಶಕ್ತಿಶಾಲಿ ಡೇಟಾ ಮ್ಯಾನಿಪ್ಯುಲೇಶನ್, ಅಂಕಿಅಂಶಗಳ ವಿಶ್ಲೇಷಣೆ ಮತ್ತು ವಿಸುವಲೈಸೇಶನ್ ಸಾಮರ್ಥ್ಯಗಳನ್ನು ಒದಗಿಸುತ್ತದೆ. ರಚನಾತ್ಮಕ ಡೇಟಾ ವಿಶ್ಲೇಷಣೆಯೊಂದಿಗೆ ಕೆಲಸ ಮಾಡುವ ಡೇಟಾ ವಿಜ್ಞಾನಿಗಳು, ಅಂಕಿಅಂಶಶಾಸ್ತ್ರಜ್ಞರು ಮತ್ತು ಸಂಶೋಧಕರಿಗೆ ಅತ್ಯಗತ್ಯ."
    step1: "R ಡೇಟಾ ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ಡೇಟಾಫ್ರೇಮ್ ಕೋಡ್ ಅನ್ನು ಅಂಟಿಸಿ. ಟೂಲ್ R ಸಿಂಟ್ಯಾಕ್ಸ್ ಅನ್ನು ಪಾರ್ಸ್ ಮಾಡುತ್ತದೆ ಮತ್ತು ಕಾಲಮ್ ಪ್ರಕಾರಗಳು, ಸಾಲು ಹೆಸರುಗಳು ಮತ್ತು ಡೇಟಾ ವಿಷಯವನ್ನು ಒಳಗೊಂಡಂತೆ ಡೇಟಾಫ್ರೇಮ್ ರಚನೆ ಮಾಹಿತಿಯನ್ನು ಹೊರತೆಗೆಯುತ್ತದೆ."
    step3: "ಡೇಟಾ ಟೈಪ್ ವಿಶೇಷಣೆಗಳು, ಫ್ಯಾಕ್ಟರ್ ಮಟ್ಟಗಳು, ಸಾಲು/ಕಾಲಮ್ ಹೆಸರುಗಳು ಮತ್ತು R-ನಿರ್ದಿಷ್ಟ ಡೇಟಾ ರಚನೆಗಳಿಗೆ ಬೆಂಬಲದೊಂದಿಗೆ ಪ್ರಮಾಣಿತ R ಡೇಟಾಫ್ರೇಮ್ ಕೋಡ್ ಉತ್ಪಾದಿಸಿ. ಉತ್ಪಾದಿಸಿದ ಕೋಡ್ ಅನ್ನು ಅಂಕಿಅಂಶಗಳ ವಿಶ್ಲೇಷಣೆ ಮತ್ತು ಡೇಟಾ ಪ್ರೊಸೆಸಿಂಗ್‌ಗಾಗಿ R ಪರಿಸರದಲ್ಲಿ ನೇರವಾಗಿ ಕಾರ್ಯಗತಗೊಳಿಸಬಹುದು."
    from_alias: "R ಡೇಟಾಫ್ರೇಮ್"
    to_alias: "R ಡೇಟಾಫ್ರೇಮ್"
hero:
  start_converting: "ಪರಿವರ್ತನೆ ಪ್ರಾರಂಭಿಸಿ"
  start_generating: "ಉತ್ಪಾದನೆ ಪ್ರಾರಂಭಿಸಿ"
  api_docs: "API ದಾಖಲೆಗಳು"
related:
  section_title: 'ಹೆಚ್ಚಿನ {{ if and .from (ne .from "generator") }}{{ .from }} ಮತ್ತು {{ end }}{{ .to }} ಕನ್ವರ್ಟರ್‌ಗಳು'
  section_description: '{{ if and .from (ne .from "generator") }}{{ .from }} ಮತ್ತು {{ end }}{{ .to }} ಫಾರ್ಮ್ಯಾಟ್‌ಗಳಿಗಾಗಿ ಹೆಚ್ಚಿನ ಕನ್ವರ್ಟರ್‌ಗಳನ್ನು ಅನ್ವೇಷಿಸಿ. ನಮ್ಮ ವೃತ್ತಿಪರ ಆನ್‌ಲೈನ್ ಪರಿವರ್ತನೆ ಸಾಧನಗಳೊಂದಿಗೆ ನಿಮ್ಮ ಡೇಟಾವನ್ನು ಬಹು ಫಾರ್ಮ್ಯಾಟ್‌ಗಳ ನಡುವೆ ಪರಿವರ್ತಿಸಿ.'
  title: "{{ .from }} ರಿಂದ {{ .to }}"
howto:
  step2: "ವೃತ್ತಿಪರ ವೈಶಿಷ್ಟ್ಯಗಳೊಂದಿಗೆ ನಮ್ಮ ಸುಧಾರಿತ ಆನ್‌ಲೈನ್ ಟೇಬಲ್ ಎಡಿಟರ್ ಬಳಸಿ ಡೇಟಾವನ್ನು ಸಂಪಾದಿಸಿ. ಖಾಲಿ ಸಾಲುಗಳನ್ನು ಅಳಿಸುವುದು, ನಕಲುಗಳನ್ನು ತೆಗೆದುಹಾಕುವುದು, ಡೇಟಾ ಟ್ರಾನ್ಸ್‌ಪೋಸಿಷನ್, ವಿಂಗಡಣೆ, ರೆಗೆಕ್ಸ್ ಹುಡುಕಿ ಮತ್ತು ಬದಲಾಯಿಸಿ, ಮತ್ತು ನೈಜ-ಸಮಯದ ಪೂರ್ವವೀಕ್ಷಣೆಯನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ. ಎಲ್ಲಾ ಬದಲಾವಣೆಗಳು ನಿಖರವಾದ, ವಿಶ್ವಾಸಾರ್ಹ ಫಲಿತಾಂಶಗಳೊಂದಿಗೆ %s ಫಾರ್ಮ್ಯಾಟ್‌ಗೆ ಸ್ವಯಂಚಾಲಿತವಾಗಿ ಪರಿವರ್ತನೆಯಾಗುತ್ತವೆ."
  section_title: "{{ . }} ಅನ್ನು ಹೇಗೆ ಬಳಸುವುದು"
  converter_description: "ನಮ್ಮ ಹಂತ-ಹಂತದ ಮಾರ್ಗದರ್ಶಿಯೊಂದಿಗೆ {{ .from }} ಅನ್ನು {{ .to }} ಗೆ ಪರಿವರ್ತಿಸುವುದನ್ನು ಕಲಿಯಿರಿ. ಸುಧಾರಿತ ವೈಶಿಷ್ಟ್ಯಗಳು ಮತ್ತು ನೈಜ-ಸಮಯದ ಪೂರ್ವವೀಕ್ಷಣೆಯೊಂದಿಗೆ ವೃತ್ತಿಪರ ಆನ್‌ಲೈನ್ ಕನ್ವರ್ಟರ್."
  generator_description: "ನಮ್ಮ ಆನ್‌ಲೈನ್ ಜೆನರೇಟರ್‌ನೊಂದಿಗೆ ವೃತ್ತಿಪರ {{ .to }} ಟೇಬಲ್‌ಗಳನ್ನು ರಚಿಸುವುದನ್ನು ಕಲಿಯಿರಿ. Excel ತರಹದ ಸಂಪಾದನೆ, ನೈಜ-ಸಮಯದ ಪೂರ್ವವೀಕ್ಷಣೆ, ಮತ್ತು ತತ್ಕ್ಷಣ ರಫ್ತು ಸಾಮರ್ಥ್ಯಗಳು."
extension:
  section_title: "ಟೇಬಲ್ ಪತ್ತೆ ಮತ್ತು ಹೊರತೆಗೆಯುವಿಕೆ ಎಕ್ಸ್‌ಟೆನ್ಷನ್"
  section_description: "ಒಂದು ಕ್ಲಿಕ್‌ನಲ್ಲಿ ಯಾವುದೇ ವೆಬ್‌ಸೈಟ್‌ನಿಂದ ಟೇಬಲ್‌ಗಳನ್ನು ಹೊರತೆಗೆಯಿರಿ. Excel, CSV, JSON ಸೇರಿದಂತೆ 30+ ಫಾರ್ಮ್ಯಾಟ್‌ಗಳಿಗೆ ತಕ್ಷಣವೇ ಪರಿವರ್ತಿಸಿ - ಕಾಪಿ-ಪೇಸ್ಟಿಂಗ್ ಅಗತ್ಯವಿಲ್ಲ."
  features:
    extraction_title: "ಒಂದು-ಕ್ಲಿಕ್ ಟೇಬಲ್ ಹೊರತೆಗೆಯುವಿಕೆ"
    extraction_description: "ಕಾಪಿ-ಪೇಸ್ಟಿಂಗ್ ಇಲ್ಲದೆ ಯಾವುದೇ ವೆಬ್‌ಪೇಜ್‌ನಿಂದ ತಕ್ಷಣವೇ ಟೇಬಲ್‌ಗಳನ್ನು ಹೊರತೆಗೆಯಿರಿ - ವೃತ್ತಿಪರ ಡೇಟಾ ಹೊರತೆಗೆಯುವಿಕೆಯನ್ನು ಸರಳಗೊಳಿಸಲಾಗಿದೆ"
    formats_title: "30+ ಫಾರ್ಮ್ಯಾಟ್ ಕನ್ವರ್ಟರ್ ಬೆಂಬಲ"
    formats_description: "ನಮ್ಮ ಸುಧಾರಿತ ಟೇಬಲ್ ಕನ್ವರ್ಟರ್‌ನೊಂದಿಗೆ ಹೊರತೆಗೆದ ಟೇಬಲ್‌ಗಳನ್ನು Excel, CSV, JSON, Markdown, SQL, ಮತ್ತು ಇನ್ನಷ್ಟಕ್ಕೆ ಪರಿವರ್ತಿಸಿ"
    detection_title: "ಸ್ಮಾರ್ಟ್ ಟೇಬಲ್ ಪತ್ತೆ"
    detection_description: "ವೇಗದ ಡೇಟಾ ಹೊರತೆಗೆಯುವಿಕೆ ಮತ್ತು ಪರಿವರ್ತನೆಗಾಗಿ ಯಾವುದೇ ವೆಬ್‌ಪೇಜ್‌ನಲ್ಲಿ ಟೇಬಲ್‌ಗಳನ್ನು ಸ್ವಯಂಚಾಲಿತವಾಗಿ ಪತ್ತೆ ಮಾಡುತ್ತದೆ ಮತ್ತು ಹೈಲೈಟ್ ಮಾಡುತ್ತದೆ"
  hover_tip: "✨ ಹೊರತೆಗೆಯುವ ಐಕಾನ್ ನೋಡಲು ಯಾವುದೇ ಟೇಬಲ್‌ನ ಮೇಲೆ ಹೋವರ್ ಮಾಡಿ"
recommendations:
  section_title: "ವಿಶ್ವವಿದ್ಯಾಲಯಗಳು ಮತ್ತು ವೃತ್ತಿಪರರಿಂದ ಶಿಫಾರಸು ಮಾಡಲಾಗಿದೆ"
  section_description: "ವಿಶ್ವಾಸಾರ್ಹ ಟೇಬಲ್ ಪರಿವರ್ತನೆ ಮತ್ತು ಡೇಟಾ ಪ್ರಕ್ರಿಯೆಗಾಗಿ ವಿಶ್ವವಿದ್ಯಾಲಯಗಳು, ಸಂಶೋಧನಾ ಸಂಸ್ಥೆಗಳು ಮತ್ತು ಅಭಿವೃದ್ಧಿ ತಂಡಗಳಾದ್ಯಂತ ವೃತ್ತಿಪರರು TableConvert ಅನ್ನು ನಂಬುತ್ತಾರೆ."
  cards:
    university_title: "ವಿಸ್ಕಾನ್ಸಿನ್-ಮ್ಯಾಡಿಸನ್ ವಿಶ್ವವಿದ್ಯಾಲಯ"
    university_description: "TableConvert.com - ವೃತ್ತಿಪರ ಉಚಿತ ಆನ್‌ಲೈನ್ ಟೇಬಲ್ ಕನ್ವರ್ಟರ್ ಮತ್ತು ಡೇಟಾ ಫಾರ್ಮ್ಯಾಟ್ ಸಾಧನ"
    university_link: "ಲೇಖನ ಓದಿ"
    facebook_title: "ಡೇಟಾ ವೃತ್ತಿಪರ ಸಮುದಾಯ"
    facebook_description: "Facebook ಡೆವಲಪರ್ ಗುಂಪುಗಳಲ್ಲಿ ಡೇಟಾ ವಿಶ್ಲೇಷಕರು ಮತ್ತು ವೃತ್ತಿಪರರಿಂದ ಹಂಚಲಾಗಿದೆ ಮತ್ತು ಶಿಫಾರಸು ಮಾಡಲಾಗಿದೆ"
    facebook_link: "ಪೋಸ್ಟ್ ವೀಕ್ಷಿಸಿ"
    twitter_title: "ಡೆವಲಪರ್ ಸಮುದಾಯ"
    twitter_description: "ಟೇಬಲ್ ಪರಿವರ್ತನೆಗಾಗಿ X (Twitter) ನಲ್ಲಿ @xiaoying_eth ಮತ್ತು ಇತರ ಡೆವಲಪರ್‌ಗಳಿಂದ ಶಿಫಾರಸು ಮಾಡಲಾಗಿದೆ"
    twitter_link: "ಟ್ವೀಟ್ ವೀಕ್ಷಿಸಿ"
faq:
  section_title: "ಆಗಾಗ್ಗೆ ಕೇಳಲಾಗುವ ಪ್ರಶ್ನೆಗಳು"
  section_description: "ನಮ್ಮ ಉಚಿತ ಆನ್‌ಲೈನ್ ಟೇಬಲ್ ಕನ್ವರ್ಟರ್, ಡೇಟಾ ಫಾರ್ಮ್ಯಾಟ್‌ಗಳು ಮತ್ತು ಪರಿವರ್ತನೆ ಪ್ರಕ್ರಿಯೆಯ ಬಗ್ಗೆ ಸಾಮಾನ್ಯ ಪ್ರಶ್ನೆಗಳು."
  what: "%s ಫಾರ್ಮ್ಯಾಟ್ ಎಂದರೇನು?"
  howto_convert:
    question: "{{ . }} ಅನ್ನು ಉಚಿತವಾಗಿ ಹೇಗೆ ಬಳಸುವುದು?"
    answer: "ನಮ್ಮ ಉಚಿತ ಆನ್‌ಲೈನ್ ಟೇಬಲ್ ಕನ್ವರ್ಟರ್ ಬಳಸಿ ನಿಮ್ಮ {{ .from }} ಫೈಲ್ ಅಪ್‌ಲೋಡ್ ಮಾಡಿ, ಡೇಟಾವನ್ನು ಪೇಸ್ಟ್ ಮಾಡಿ, ಅಥವಾ ವೆಬ್ ಪುಟಗಳಿಂದ ಹೊರತೆಗೆಯಿರಿ. ನಮ್ಮ ವೃತ್ತಿಪರ ಕನ್ವರ್ಟರ್ ಸಾಧನವು ನೈಜ-ಸಮಯದ ಪೂರ್ವವೀಕ್ಷಣೆ ಮತ್ತು ಸುಧಾರಿತ ಸಂಪಾದನೆ ವೈಶಿಷ್ಟ್ಯಗಳೊಂದಿಗೆ ನಿಮ್ಮ ಡೇಟಾವನ್ನು ತಕ್ಷಣವೇ {{ .to }} ಫಾರ್ಮ್ಯಾಟ್‌ಗೆ ಪರಿವರ್ತಿಸುತ್ತದೆ. ಪರಿವರ್ತಿತ ಫಲಿತಾಂಶವನ್ನು ತಕ್ಷಣವೇ ಡೌನ್‌ಲೋಡ್ ಮಾಡಿ ಅಥವಾ ಕಾಪಿ ಮಾಡಿ."
  security:
    question: "ಈ ಆನ್‌ಲೈನ್ ಕನ್ವರ್ಟರ್ ಬಳಸುವಾಗ ನನ್ನ ಡೇಟಾ ಸುರಕ್ಷಿತವಾಗಿದೆಯೇ?"
    answer: "ಖಂಡಿತವಾಗಿ! ಎಲ್ಲಾ ಟೇಬಲ್ ಪರಿವರ್ತನೆಗಳು ನಿಮ್ಮ ಬ್ರೌಸರ್‌ನಲ್ಲಿ ಸ್ಥಳೀಯವಾಗಿ ನಡೆಯುತ್ತವೆ - ನಿಮ್ಮ ಡೇಟಾ ಎಂದಿಗೂ ನಿಮ್ಮ ಸಾಧನವನ್ನು ಬಿಡುವುದಿಲ್ಲ. ನಮ್ಮ ಆನ್‌ಲೈನ್ ಕನ್ವರ್ಟರ್ ಎಲ್ಲವನ್ನೂ ಕ್ಲೈಂಟ್-ಸೈಡ್‌ನಲ್ಲಿ ಪ್ರಕ್ರಿಯೆಗೊಳಿಸುತ್ತದೆ, ಸಂಪೂರ್ಣ ಗೌಪ್ಯತೆ ಮತ್ತು ಡೇಟಾ ಸುರಕ್ಷತೆಯನ್ನು ಖಾತ್ರಿಪಡಿಸುತ್ತದೆ. ನಮ್ಮ ಸರ್ವರ್‌ಗಳಲ್ಲಿ ಯಾವುದೇ ಫೈಲ್‌ಗಳನ್ನು ಸಂಗ್ರಹಿಸಲಾಗುವುದಿಲ್ಲ."
  free:
    question: "TableConvert ನಿಜವಾಗಿಯೂ ಬಳಸಲು ಉಚಿತವೇ?"
    answer: "ಹೌದು, TableConvert ಸಂಪೂರ್ಣವಾಗಿ ಉಚಿತವಾಗಿದೆ! ಎಲ್ಲಾ ಕನ್ವರ್ಟರ್ ವೈಶಿಷ್ಟ್ಯಗಳು, ಟೇಬಲ್ ಎಡಿಟರ್, ಡೇಟಾ ಜೆನರೇಟರ್ ಸಾಧನಗಳು ಮತ್ತು ರಫ್ತು ಆಯ್ಕೆಗಳು ಯಾವುದೇ ವೆಚ್ಚ, ನೋಂದಣಿ ಅಥವಾ ಗುಪ್ತ ಶುಲ್ಕಗಳಿಲ್ಲದೆ ಲಭ್ಯವಿದೆ. ಉಚಿತವಾಗಿ ಆನ್‌ಲೈನ್‌ನಲ್ಲಿ ಅನಿಯಮಿತ ಫೈಲ್‌ಗಳನ್ನು ಪರಿವರ್ತಿಸಿ."
  filesize:
    question: "ಆನ್‌ಲೈನ್ ಕನ್ವರ್ಟರ್‌ನ ಫೈಲ್ ಗಾತ್ರದ ಮಿತಿಗಳು ಯಾವುವು?"
    answer: "ನಮ್ಮ ಉಚಿತ ಆನ್‌ಲೈನ್ ಟೇಬಲ್ ಕನ್ವರ್ಟರ್ 10MB ವರೆಗಿನ ಫೈಲ್‌ಗಳನ್ನು ಬೆಂಬಲಿಸುತ್ತದೆ. ದೊಡ್ಡ ಫೈಲ್‌ಗಳು, ಬ್ಯಾಚ್ ಪ್ರಕ್ರಿಯೆ ಅಥವಾ ಎಂಟರ್‌ಪ್ರೈಸ್ ಅಗತ್ಯಗಳಿಗಾಗಿ, ಹೆಚ್ಚಿನ ಮಿತಿಗಳೊಂದಿಗೆ ನಮ್ಮ ಬ್ರೌಸರ್ ಎಕ್ಸ್‌ಟೆನ್ಷನ್ ಅಥವಾ ವೃತ್ತಿಪರ API ಸೇವೆಯನ್ನು ಬಳಸಿ."
stats:
  conversions: "ಪರಿವರ್ತಿಸಿದ ಟೇಬಲ್‌ಗಳು"
  tables: "ಉತ್ಪನ್ನಗೊಳಿಸಿದ ಟೇಬಲ್‌ಗಳು"
  formats: "ಡೇಟಾ ಫೈಲ್ ಫಾರ್ಮ್ಯಾಟ್‌ಗಳು"
  rating: "ಬಳಕೆದಾರ ರೇಟಿಂಗ್"
