site:
  fullname: "آن لائن ٹیبل کنورٹ"
  name: "TableConvert"
  subtitle: "مفت آن لائن ٹیبل کنورٹر اور جنریٹر"
  intro: "TableConvert ایک مفت آن لائن ٹیبل کنورٹر اور ڈیٹا جنریٹر ٹول ہے جو Excel, CSV, JSON, Markdown, LaTeX, SQL اور مزید سمیت 30+ فارمیٹس کے درمیان تبدیلی کو سپورٹ کرتا ہے."
  followTwitter: "X پر ہمیں فالو کریں"
title:
  converter: "%s سے %s"
  generator: "%s جنریٹر"
post:
  tags:
    converter: "کنورٹر"
    editor: "ایڈیٹر"
    generator: "جنریٹر"
    maker: "بلڈر"
  converter:
    title: "%s کو %s میں آن لائن کنورٹ کریں"
    short: "ایک مفت اور طاقتور %s سے %s آن لائن ٹول"
    intro: "استعمال میں آسان آن لائن %s سے %s کنورٹر۔ ہمارے بدیہی تبدیلی ٹول کے ساتھ ٹیبل ڈیٹا کو آسانی سے تبدیل کریں۔ تیز، قابل اعتماد اور صارف دوست۔"
  generator:
    title: "آن لائن %s ایڈیٹر اور جنریٹر"
    short: "جامع خصوصیات کے ساتھ پیشہ ورانہ %s آن لائن جنریشن ٹول"
    intro: "استعمال میں آسان آن لائن %s جنریٹر اور ٹیبل ایڈیٹر۔ ہمارے بدیہی ٹول اور ریئل ٹائم پیش منظر کے ساتھ پیشہ ورانہ ڈیٹا ٹیبلز آسانی سے بنائیں۔"
navbar:
  search:
    placeholder: "کنورٹر تلاش کریں..."
  sponsor: "ہمیں کافی خریدیں"
  extension: "ایکسٹینشن"
  api: "API دستاویزات"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "ڈیٹا سورس"
    placeholder: "اپنا %s ڈیٹا پیسٹ کریں یا %s فائلیں یہاں ڈریگ کریں"
    example: "مثال"
    upload: "فائل اپ لوڈ کریں"
    extract:
      enter: "ویب پیج سے نکالیں"
      intro: "ساختی ڈیٹا خودکار طور پر نکالنے کے لیے ٹیبل ڈیٹا والے ویب پیج کا URL داخل کریں"
      btn: "%s نکالیں"
    excel:
      sheet: "ورک شیٹ"
      none: "کوئی نہیں"
  tableEditor:
    title: "آن لائن ٹیبل ایڈیٹر"
    undo: "واپس کریں"
    redo: "دوبارہ کریں"
    transpose: "ٹرانسپوز"
    clear: "صاف کریں"
    deleteBlank: "خالی حذف کریں"
    deleteDuplicate: "ڈپلیکیٹ حذف کریں"
    uppercase: "بڑے حروف"
    lowercase: "چھوٹے حروف"
    capitalize: "پہلا حرف بڑا"
    replace:
      replace: "تلاش اور تبدیل کریں (Regex سپورٹ)"
      subst: "اس سے تبدیل کریں..."
      btn: "سب تبدیل کریں"
  tableGenerator:
    title: "ٹیبل جنریٹر"
    sponsor: "ہمیں کافی خریدیں"
    copy: "کلپ بورڈ میں کاپی کریں"
    download: "فائل ڈاؤن لوڈ کریں"
    tooltip:
      html:
        escape: "ڈسپلے کی خرابیوں سے بچنے کے لیے HTML خصوصی حروف (&, <, >, \", ') کو escape کریں"
        div: "روایتی TABLE ٹیگز کی بجائے DIV+CSS لے آؤٹ استعمال کریں، responsive ڈیزائن کے لیے بہتر موزوں"
        minify: "compressed HTML کوڈ بنانے کے لیے whitespace اور line breaks ہٹائیں"
        thead: "معیاری table head (&lt;thead&gt;) اور body (&lt;tbody&gt;) ڈھانچہ بنائیں"
        tableCaption: "table کے اوپر وضاحتی عنوان شامل کریں (&lt;caption&gt; element)"
        tableClass: "آسان style customization کے لیے table میں CSS class کا نام شامل کریں"
        tableId: "JavaScript manipulation کے لیے table کے لیے منفرد ID identifier سیٹ کریں"
      jira:
        escape: "Jira table syntax کے ساتھ تصادم سے بچنے کے لیے pipe characters (|) کو escape کریں"
      json:
        parsingJSON: "cells میں JSON strings کو ذہانت سے objects میں parse کریں"
        minify: "فائل کا سائز کم کرنے کے لیے compact single-line JSON format بنائیں"
        format: "output JSON data structure منتخب کریں: object array، 2D array، وغیرہ"
      latex:
        escape: "مناسب compilation کو یقینی بنانے کے لیے LaTeX خصوصی حروف (%, &, _, #, $، وغیرہ) کو escape کریں"
        ht: "صفحے پر table کی position کنٹرول کرنے کے لیے floating position parameter [!ht] شامل کریں"
        mwe: "مکمل LaTeX document بنائیں"
        tableAlign: "صفحے پر table کی horizontal alignment سیٹ کریں"
        tableBorder: "table border style کو configure کریں: کوئی border نہیں، جزوی border، مکمل border"
        label: "\\ref{} command cross-referencing کے لیے table label سیٹ کریں"
        caption: "table کے اوپر یا نیچے دکھانے کے لیے table caption سیٹ کریں"
        location: "table caption display position منتخب کریں: اوپر یا نیچے"
        tableType: "table environment type منتخب کریں: tabular، longtable، array، وغیرہ"
      markdown:
        escape: "format conflicts سے بچنے کے لیے Markdown خصوصی حروف (*, _, |, \\، وغیرہ) کو escape کریں"
        pretty: "زیادہ خوبصورت table format بنانے کے لیے column widths کو auto-align کریں"
        simple: "بیرونی border vertical lines کو چھوڑ کر simplified syntax استعمال کریں"
        boldFirstRow: "پہلی row کے text کو bold بنائیں"
        boldFirstColumn: "پہلے column کے text کو bold بنائیں"
        firstHeader: "پہلی row کو header کے طور پر treat کریں اور separator line شامل کریں"
        textAlign: "column text alignment سیٹ کریں: بائیں، وسط، دائیں"
        multilineHandling: "Multiline text handling: line breaks محفوظ رکھیں، \\n میں escape کریں، &lt;br&gt; tags استعمال کریں"

        includeLineNumbers: "table کے بائیں جانب line number column شامل کریں"
      magic:
        builtin: "پہلے سے طے شدہ عام template formats منتخب کریں"
        rowsTpl: "<table> <tr> <th>جادوئی Syntax</th> <th>تفصیل</th> <th>JS Methods کی سپورٹ</th> </tr> <tr> <td>{h1} {h2} ...</td> <td><b>سرخی</b> کا پہلا، دوسرا ... field، یعنی {hA} {hB} ...</td> <td>String methods</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>موجودہ row کا پہلا، دوسرا ... field، یعنی {$A} {$B} ...</td> <td>String methods</td> </tr> <tr> <td>{F,} {F;}</td> <td><b>F</b> کے بعد والی string سے موجودہ row کو تقسیم کریں</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>موجودہ <b>Row</b> کا Line <b>Number</b> 1 یا 100 سے</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>Rows</b> کا <b>آخری</b> Line <b>Number</b> </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>JavaScript code <b>execute</b> کریں، مثال: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> braces {...} output کرنے کے لیے backslash <b>\\</b> استعمال کریں </td> <td></td> </tr></table>"
        headerTpl: "header section کے لیے custom output template"
        footerTpl: "footer section کے لیے custom output template"
      textile:
        escape: "فارمیٹ تصادم سے بچنے کے لیے Textile syntax حروف (|, ., -, ^) کو escape کریں"
        rowHeader: "پہلی صف کو ہیڈر صف کے طور پر سیٹ کریں"
        thead: "ٹیبل ہیڈ اور باڈی کے لیے Textile syntax نشانات شامل کریں"
      xml:
        escape: "درست XML کو یقینی بنانے کے لیے XML خصوصی حروف (&lt;, &gt;, &amp;, \", ') کو escape کریں"
        minify: "اضافی whitespace ہٹا کر compressed XML آؤٹ پٹ بنائیں"
        rootElement: "XML root element tag نام سیٹ کریں"
        rowElement: "ڈیٹا کی ہر صف کے لیے XML element tag نام سیٹ کریں"
        declaration: "XML declaration header (&lt;?xml version=\"1.0\"?&gt;) شامل کریں"
        attributes: "child elements کی بجائے XML attributes کے طور پر ڈیٹا آؤٹ پٹ کریں"
        cdata: "خصوصی حروف کی حفاظت کے لیے متن کو CDATA سے wrap کریں"
        encoding: "XML دستاویز کے لیے character encoding فارمیٹ سیٹ کریں"
        indentation: "XML indentation character منتخب کریں: spaces یا tabs"
      yaml:
        indentSize: "YAML hierarchy indentation کے لیے spaces کی تعداد سیٹ کریں (عام طور پر 2 یا 4)"
        arrayStyle: "Array فارمیٹ: block (ایک آئٹم فی لائن) یا flow (inline فارمیٹ)"
        quotationStyle: "String quote style: کوئی quotes نہیں، single quotes، double quotes"
      pdf:
        theme: "پیشہ ورانہ دستاویزات کے لیے PDF ٹیبل ویژول اسٹائل منتخب کریں"
        headerColor: "PDF ٹیبل ہیڈر بیک گراؤنڈ رنگ منتخب کریں"
        showHead: "PDF صفحات پر ہیڈر ڈسپلے کنٹرول کریں"
        docTitle: "PDF دستاویز کے لیے اختیاری ٹائٹل"
        docDescription: "PDF دستاویز کے لیے اختیاری تفصیلی متن"
      csv:
        bom: "Excel اور دیگر سافٹ ویئر کو encoding پہچاننے میں مدد کے لیے UTF-8 byte order mark شامل کریں"
      excel:
        autoWidth: "مواد کی بنیاد پر کالم کی چوڑائی خودکار طور پر ایڈجسٹ کریں"
        protectSheet: "password کے ساتھ worksheet protection کو enable کریں: tableconvert.com"
      sql:  
        primaryKey: "CREATE TABLE statement کے لیے primary key field نام مخصوص کریں"
        dialect: "database type منتخب کریں، جو quote اور data type syntax کو متاثر کرتا ہے"
      ascii:
        forceSep: "ڈیٹا کی ہر صف کے درمیان separator lines کو force کریں"
        style: "ASCII table border drawing style منتخب کریں"
        comment: "پوری table کو wrap کرنے کے لیے comment markers شامل کریں"
      mediawiki:
        minify: "اضافی whitespace ہٹا کر output code کو compress کریں"
        header: "پہلی صف کو header style کے طور پر نشان زد کریں"
        sort: "table click sorting functionality کو enable کریں"
      asciidoc:
        minify: "AsciiDoc format output کو compress کریں"
        firstHeader: "پہلی صف کو header row کے طور پر سیٹ کریں"
        lastFooter: "آخری صف کو footer row کے طور پر سیٹ کریں"
        title: "table میں title text شامل کریں"
      tracwiki:
        rowHeader: "پہلی صف کو header کے طور پر سیٹ کریں"
        colHeader: "پہلا کالم header کے طور پر سیٹ کریں"
      bbcode:
        minify: "BBCode output format کو compress کریں"
      restructuredtext:
        style: "reStructuredText table border style منتخب کریں"
        forceSep: "separator lines کو force کریں"
    label:
      ascii:
        forceSep: "قطار علیحدگی"
        style: "بارڈر سٹائل"
        comment: "کمنٹ ریپر"
      restructuredtext:
        style: "بارڈر سٹائل"
        forceSep: "علیحدگی کو مجبور کریں"
      bbcode:
        minify: "آؤٹ پٹ کم کریں"
      csv:
        doubleQuote: "ڈبل کوٹ ریپ"
        delimiter: "فیلڈ ڈیلیمیٹر"
        bom: "UTF-8 BOM"
        valueDelimiter: "ویلیو ڈیلیمیٹر"
        rowDelimiter: "قطار ڈیلیمیٹر"
        prefix: "قطار پریفکس"
        suffix: "قطار سفکس"
      excel:
        autoWidth: "خودکار چوڑائی"
        textFormat: "ٹیکسٹ فارمیٹ"
        protectSheet: "شیٹ کی حفاظت"
        boldFirstRow: "پہلی قطار کو موٹا کریں"
        boldFirstColumn: "پہلا کالم موٹا کریں"
        sheetName: "شیٹ کا نام"
      html:
        escape: "HTML حروف کو Escape کریں"
        div: "DIV ٹیبل"
        minify: "کوڈ کم کریں"
        thead: "ٹیبل ہیڈ ڈھانچہ"
        tableCaption: "ٹیبل کیپشن"
        tableClass: "ٹیبل کلاس"
        tableId: "ٹیبل ID"
        rowHeader: "قطار ہیڈر"
        colHeader: "کالم ہیڈر"
      jira:
        escape: "حروف کو Escape کریں"
        rowHeader: "قطار ہیڈر"
        colHeader: "کالم ہیڈر"
      json:
        parsingJSON: "JSON کو Parse کریں"
        minify: "آؤٹ پٹ کم کریں"
        format: "ڈیٹا فارمیٹ"
        rootName: "روٹ آبجیکٹ نام"
        indentSize: "انڈنٹ سائز"
      jsonlines:
        parsingJSON: "JSON کو Parse کریں"
        format: "ڈیٹا فارمیٹ"
      latex:
        escape: "LaTeX ٹیبل حروف کو Escape کریں"
        ht: "فلوٹ پوزیشن"
        mwe: "مکمل دستاویز"
        tableAlign: "ٹیبل سیدھ"
        tableBorder: "بارڈر سٹائل"
        label: "حوالہ لیبل"
        caption: "ٹیبل کیپشن"
        location: "کیپشن پوزیشن"
        tableType: "ٹیبل ٹائپ"
        boldFirstRow: "پہلی قطار کو موٹا کریں"
        boldFirstColumn: "پہلا کالم موٹا کریں"
        textAlign: "ٹیکسٹ سیدھ"
        borders: "بارڈر سیٹنگز"
      markdown:
        escape: "حروف کو Escape کریں"
        pretty: "خوبصورت Markdown ٹیبل"
        simple: "سادہ Markdown فارمیٹ"
        boldFirstRow: "پہلی قطار کو موٹا کریں"
        boldFirstColumn: "پہلا کالم موٹا کریں"
        firstHeader: "پہلا ہیڈر"
        textAlign: "ٹیکسٹ سیدھ"
        multilineHandling: "کثیر لائن ہینڈلنگ"

        includeLineNumbers: "لائن نمبرز شامل کریں"
        align: "سیدھ"
      mediawiki:
        minify: "کوڈ کم کریں"
        header: "ہیڈر مارک اپ"
        sort: "ترتیب دینے کے قابل"
      asciidoc:
        minify: "فارمیٹ کم کریں"
        firstHeader: "پہلا ہیڈر"
        lastFooter: "آخری فوٹر"
        title: "ٹیبل ٹائٹل"
      tracwiki:
        rowHeader: "قطار ہیڈر"
        colHeader: "کالم ہیڈر"
      sql:
        drop: "ٹیبل ڈراپ کریں (اگر موجود ہے)"
        create: "ٹیبل بنائیں"
        oneInsert: "بیچ انسرٹ"
        table: "ٹیبل نام"
        dialect: "ڈیٹابیس ٹائپ"
        primaryKey: "پرائمری کی"
      magic:
        builtin: "بلٹ ان ٹیمپلیٹ"
        rowsTpl: "قطار ٹیمپلیٹ، سنٹیکس ->"
        headerTpl: "ہیڈر ٹیمپلیٹ"
        footerTpl: "فوٹر ٹیمپلیٹ"
      textile:
        escape: "حروف کو Escape کریں"
        rowHeader: "قطار ہیڈر"
        thead: "ٹیبل ہیڈ سنٹیکس"
      xml:
        escape: "XML حروف کو Escape کریں"
        minify: "آؤٹ پٹ کو کم کریں"
        rootElement: "روٹ ایلیمنٹ"
        rowElement: "رو ایلیمنٹ"
        declaration: "XML ڈکلیریشن"
        attributes: "ایٹریبیوٹ موڈ"
        cdata: "CDATA ریپر"
        encoding: "انکوڈنگ"
        indentSize: "انڈنٹ سائز"
      yaml:
        indentSize: "انڈنٹ سائز"
        arrayStyle: "ایرے سٹائل"
        quotationStyle: "اقتباس سٹائل"
      pdf:
        theme: "PDF ٹیبل تھیم"
        headerColor: "PDF ہیڈر رنگ"
        showHead: "PDF ہیڈر ڈسپلے"
        docTitle: "PDF دستاویز ٹائٹل"
        docDescription: "PDF دستاویز تفصیل"
sidebar:
  all: "تمام کنورژن ٹولز"
  dataSource:
    title: "ڈیٹا سورس"
    description:
      converter: "%s کو %s میں کنورٹ کرنے کے لیے درآمد کریں۔ فائل اپ لوڈ، آن لائن ایڈٹنگ، اور ویب ڈیٹا نکالنے کو سپورٹ کرتا ہے۔"
      generator: "دستی ان پٹ، فائل درآمد، اور ٹیمپلیٹ جنریشن سمیت متعدد ان پٹ طریقوں کے ساتھ ٹیبل ڈیٹا بنائیں۔"
  tableEditor:
    title: "آن لائن ٹیبل ایڈیٹر"
    description:
      converter: "ہماری ٹیبل ایڈیٹر استعمال کرتے ہوئے %s کو آن لائن پروسیس کریں۔ Excel جیسا آپریشن تجربہ جو خالی قطاریں حذف کرنے، ڈپلیکیٹ ہٹانے، ترتیب دینے، اور تلاش اور تبدیل کرنے کو سپورٹ کرتا ہے۔"
      generator: "طاقتور آن لائن ٹیبل ایڈیٹر جو Excel جیسا آپریشن تجربہ فراہم کرتا ہے۔ خالی قطاریں حذف کرنے، ڈپلیکیٹ ہٹانے، ترتیب دینے، اور تلاش اور تبدیل کرنے کو سپورٹ کرتا ہے۔"
  tableGenerator:
    title: "ٹیبل جنریٹر"
    description:
      converter: "ٹیبل جنریٹر کی ریئل ٹائم پیش نظارہ کے ساتھ %s کو تیزی سے جنریٹ کریں۔ بھرپور ایکسپورٹ آپشنز، ایک کلک کاپی اور ڈاؤن لوڈ۔"
      generator: "مختلف استعمال کے منظرناموں کو پورا کرنے کے لیے %s ڈیٹا کو متعدد فارمیٹس میں ایکسپورٹ کریں۔ کسٹم آپشنز اور ریئل ٹائم پیش نظارہ کو سپورٹ کرتا ہے۔"
footer:
  changelog: "تبدیلیوں کا ریکارڈ"
  sponsor: "سپانسرز"
  contact: "ہم سے رابطہ کریں"
  privacyPolicy: "پرائیویسی پالیسی"
  about: "ہمارے بارے میں"
  resources: "وسائل"
  popularConverters: "مقبول کنورٹرز"
  popularGenerators: "مقبول جنریٹرز"
  dataSecurity: "آپ کا ڈیٹا محفوظ ہے - تمام تبدیلیاں آپ کے براؤزر میں چلتی ہیں."
converters:
  Markdown:
    alias: "Markdown ٹیبل"
    what: "Markdown ایک ہلکا پھلکا مارک اپ زبان ہے جو تکنیکی دستاویزات، بلاگ مواد کی تخلیق، اور ویب ڈیولپمنٹ میں بڑے پیمانے پر استعمال ہوتا ہے۔ اس کا ٹیبل سنٹیکس مختصر اور بدیہی ہے، متن کی سیدھ، لنک ایمبیڈنگ، اور فارمیٹنگ کو سپورٹ کرتا ہے۔ یہ پروگرامرز اور تکنیکی مصنفین کا ترجیحی ٹول ہے، GitHub، GitLab، اور دیگر کوڈ ہوسٹنگ پلیٹ فارمز کے ساتھ مکمل طور پر موافق ہے۔"
    step1: "ڈیٹا سورس ایریا میں Markdown ٹیبل ڈیٹا پیسٹ کریں، یا براہ راست .md فائلیں drag اور drop کرکے اپ لوڈ کریں۔ ٹول خودکار طور پر ٹیبل ڈھانچہ اور فارمیٹنگ پارس کرتا ہے، پیچیدہ nested مواد اور خصوصی کریکٹر ہینڈلنگ کو سپورٹ کرتا ہے۔"
    step3: "حقیقی وقت میں معیاری Markdown ٹیبل کوڈ تیار کریں، متعدد alignment طریقوں، متن کو بولڈ کرنے، لائن نمبر شامل کرنے، اور دیگر پیشرفتہ فارمیٹ سیٹنگز کو سپورٹ کرتا ہے۔ تیار شدہ کوڈ GitHub اور بڑے Markdown editors کے ساتھ مکمل طور پر موافق ہے، ایک کلک کاپی کے ساتھ استعمال کے لیے تیار ہے۔"
    from_alias: "Markdown ٹیبل فائل"
    to_alias: "Markdown ٹیبل فارمیٹ"
  Magic:
    alias: "کسٹم ٹیمپلیٹ"
    what: "Magic template اس ٹول کا منفرد پیشرفتہ ڈیٹا جنریٹر ہے، جو صارفین کو کسٹم ٹیمپلیٹ سنٹیکس کے ذریعے خودسری فارمیٹ ڈیٹا آؤٹ پٹ بنانے کی اجازت دیتا ہے۔ متغیر تبدیلی، شرطی فیصلہ، اور لوپ پروسیسنگ کو سپورٹ کرتا ہے۔ یہ پیچیدہ ڈیٹا تبدیلی کی ضروریات اور ذاتی آؤٹ پٹ فارمیٹس کو سنبھالنے کا حتمی حل ہے، خاص طور پر ڈیولپرز اور ڈیٹا انجینئرز کے لیے موزوں ہے۔"
    step1: "بلٹ ان عام ٹیمپلیٹس منتخب کریں یا کسٹم ٹیمپلیٹ سنٹیکس بنائیں۔ بھرپور متغیرات اور functions کو سپورٹ کرتا ہے جو پیچیدہ ڈیٹا ڈھانچوں اور کاروباری منطق کو سنبھال سکتے ہیں۔"
    step3: "کسٹم فارمیٹ کی ضروریات کو مکمل طور پر پورا کرنے والا ڈیٹا آؤٹ پٹ تیار کریں۔ پیچیدہ ڈیٹا تبدیلی کی منطق اور شرطی پروسیسنگ کو سپورٹ کرتا ہے، ڈیٹا پروسیسنگ کی کارکردگی اور آؤٹ پٹ کی کوالٹی میں نمایاں بہتری۔ بیچ ڈیٹا پروسیسنگ کے لیے طاقتور ٹول۔"
    from_alias: "ٹیبل ڈیٹا"
    to_alias: "کسٹم فارمیٹ آؤٹ پٹ"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) سب سے زیادہ استعمال ہونے والا ڈیٹا ایکسچینج فارمیٹ ہے، جو Excel، Google Sheets، ڈیٹابیس سسٹمز، اور مختلف ڈیٹا تجزیہ ٹولز کے ذریعے مکمل طور پر سپورٹ ہوتا ہے۔ اس کا سادہ ڈھانچہ اور مضبوط مطابقت اسے ڈیٹا منتقلی، بیچ import/export، اور کراس پلیٹ فارم ڈیٹا ایکسچینج کے لیے معیاری فارمیٹ بناتا ہے، کاروباری تجزیہ، ڈیٹا سائنس، اور سسٹم انضمام میں بڑے پیمانے پر استعمال ہوتا ہے۔"
    step1: "CSV فائلیں اپ لوڈ کریں یا براہ راست CSV ڈیٹا پیسٹ کریں۔ ٹول ذہانت سے مختلف delimiters (کاما، ٹیب، سیمی کالن، پائپ، وغیرہ) کو پہچانتا ہے، خودکار طور پر ڈیٹا کی اقسام اور انکوڈنگ فارمیٹس کا پتہ لگاتا ہے، بڑی فائلوں اور پیچیدہ ڈیٹا ڈھانچوں کی تیز parsing کو سپورٹ کرتا ہے۔"
    step3: "معیاری CSV فارمیٹ فائلیں تیار کریں جو کسٹم delimiters، quote styles، انکوڈنگ فارمیٹس، اور BOM mark سیٹنگز کو سپورٹ کرتی ہیں۔ ہدف سسٹمز کے ساتھ مکمل مطابقت کو یقینی بناتا ہے، انٹرپرائز لیول ڈیٹا پروسیسنگ کی ضروریات کو پورا کرنے کے لیے ڈاؤن لوڈ اور کمپریشن آپشنز فراہم کرتا ہے۔"
    from_alias: "CSV ڈیٹا فائل"
    to_alias: "CSV معیاری فارمیٹ"
  JSON:
    alias: "JSON ایرے"
    what: "JSON (JavaScript Object Notation) جدید ویب ایپلیکیشنز، REST APIs، اور microservice architectures کے لیے معیاری ٹیبل ڈیٹا فارمیٹ ہے۔ اس کا واضح ڈھانچہ اور موثر parsing اسے front-end اور back-end ڈیٹا interaction، کنفیگریشن فائل اسٹوریج، اور NoSQL ڈیٹابیسز میں بڑے پیمانے پر استعمال کے قابل بناتا ہے۔ nested objects، array structures، اور متعدد ڈیٹا اقسام کو سپورٹ کرتا ہے، جو اسے جدید سافٹ ویئر ڈیولپمنٹ کے لیے ناگزیر ٹیبل ڈیٹا بناتا ہے۔"
    step1: "JSON فائلیں اپ لوڈ کریں یا JSON arrays پیسٹ کریں۔ object arrays، nested structures، اور پیچیدہ ڈیٹا اقسام کی خودکار شناخت اور parsing کو سپورٹ کرتا ہے۔ ٹول ذہانت سے JSON syntax کی تصدیق کرتا ہے اور خرابی کے اشارے فراہم کرتا ہے۔"
    step3: "متعدد JSON فارمیٹ آؤٹ پٹس تیار کریں: معیاری object arrays، 2D arrays، column arrays، اور key-value pair formats۔ beautified output، compression mode، کسٹم root object names، اور indentation سیٹنگز کو سپورٹ کرتا ہے، مختلف API interfaces اور ڈیٹا اسٹوریج کی ضروریات کے ساتھ مکمل طور پر موافق ہے۔"
    from_alias: "JSON ایرے فائل"
    to_alias: "JSON معیاری فارمیٹ"
  JSONLines:
    alias: "JSONLines فارمیٹ"
    what: "JSON Lines (NDJSON کے نام سے بھی جانا جاتا ہے) بگ ڈیٹا پروسیسنگ اور streaming ڈیٹا منتقلی کے لیے اہم فارمیٹ ہے، جس میں ہر لائن میں ایک آزاد JSON object ہوتا ہے۔ لاگ تجزیہ، ڈیٹا سٹریم پروسیسنگ، مشین لرننگ، اور تقسیم شدہ سسٹمز میں بڑے پیمانے پر استعمال ہوتا ہے۔ incremental پروسیسنگ اور parallel computing کو سپورٹ کرتا ہے، جو اسے بڑے پیمانے کے structured ڈیٹا کو سنبھالنے کے لیے مثالی انتخاب بناتا ہے۔"
    step1: "JSONLines فائلیں اپ لوڈ کریں یا ڈیٹا پیسٹ کریں۔ ٹول JSON objects کو لائن بائی لائن parse کرتا ہے، بڑی فائل streaming پروسیسنگ اور خرابی والی لائنوں کو چھوڑنے کی فعالیت کو سپورٹ کرتا ہے۔"
    step3: "معیاری JSONLines فارمیٹ تیار کریں جس میں ہر لائن مکمل JSON object آؤٹ پٹ کرتی ہے۔ streaming پروسیسنگ، بیچ import، اور بگ ڈیٹا تجزیہ کے منظورات کے لیے موزوں، ڈیٹا validation اور فارمیٹ optimization کو سپورٹ کرتا ہے۔"
    from_alias: "JSONLines ڈیٹا"
    to_alias: "JSONLines سٹریمنگ فارمیٹ"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) انٹرپرائز لیول ڈیٹا ایکسچینج اور کنفیگریشن منیجمنٹ کے لیے معیاری فارمیٹ ہے، جس میں سخت syntax specifications اور طاقتور validation mechanisms ہیں۔ ویب سروسز، کنفیگریشن فائلوں، دستاویز اسٹوریج، اور سسٹم انضمام میں بڑے پیمانے پر استعمال ہوتا ہے۔ namespaces، schema validation، اور XSLT transformation کو سپورٹ کرتا ہے، جو اسے انٹرپرائز ایپلیکیشنز کے لیے اہم ٹیبل ڈیٹا بناتا ہے۔"
    step1: "XML فائلیں اپ لوڈ کریں یا XML ڈیٹا پیسٹ کریں۔ ٹول خودکار طور پر XML structure parse کرتا ہے اور اسے ٹیبل فارمیٹ میں تبدیل کرتا ہے، namespace، attribute handling، اور پیچیدہ nested structures کو سپورٹ کرتا ہے۔"
    step3: "XML معیارات کے ساتھ موافق XML آؤٹ پٹ تیار کریں۔ کسٹم root elements، row element names، attribute modes، CDATA wrapping، اور character encoding سیٹنگز کو سپورٹ کرتا ہے۔ ڈیٹا کی سالمیت اور مطابقت کو یقینی بناتا ہے، انٹرپرائز لیول ایپلیکیشن کی ضروریات کو پورا کرتا ہے۔"
    from_alias: "XML ڈیٹا فائل"
    to_alias: "XML معیاری فارمیٹ"
  YAML:
    alias: "YAML کنفیگریشن"
    what: "YAML انسان دوست ڈیٹا serialization معیار ہے، جو اپنے واضح hierarchical structure اور مختصر syntax کے لیے مشہور ہے۔ کنفیگریشن فائلوں، DevOps ٹول chains، Docker Compose، اور Kubernetes deployment میں بڑے پیمانے پر استعمال ہوتا ہے۔ اس کی مضبوط readability اور مختصر syntax اسے جدید cloud-native ایپلیکیشنز اور automated operations کے لیے اہم کنفیگریشن فارمیٹ بناتا ہے۔"
    step1: "YAML فائلیں اپ لوڈ کریں یا YAML ڈیٹا پیسٹ کریں۔ ٹول ذہانت سے YAML structure parse کرتا ہے اور syntax کی درستگی کی تصدیق کرتا ہے، multi-document formats اور پیچیدہ ڈیٹا اقسام کو سپورٹ کرتا ہے۔"
    step3: "معیاری YAML فارمیٹ آؤٹ پٹ تیار کریں جو block اور flow array styles، متعدد quote سیٹنگز، کسٹم indentation، اور comment preservation کو سپورٹ کرتا ہے۔ آؤٹ پٹ YAML فائلوں کو مختلف parsers اور کنفیگریشن سسٹمز کے ساتھ مکمل طور پر موافق ہونے کو یقینی بناتا ہے۔"
    from_alias: "YAML کنفیگریشن فائل"
    to_alias: "YAML معیاری فارمیٹ"
  MySQL:
      alias: "MySQL کوئری نتائج"
      what: "MySQL دنیا کا سب سے مقبول اوپن سورس relational ڈیٹابیس منیجمنٹ سسٹم ہے، جو اپنی اعلیٰ کارکردگی، قابل اعتماد، اور استعمال میں آسانی کے لیے مشہور ہے۔ ویب ایپلیکیشنز، انٹرپرائز سسٹمز، اور ڈیٹا تجزیہ پلیٹ فارمز میں بڑے پیمانے پر استعمال ہوتا ہے۔ MySQL کے query نتائج عام طور پر structured ٹیبل ڈیٹا پر مشتمل ہوتے ہیں، جو ڈیٹابیس منیجمنٹ اور ڈیٹا تجزیہ کے کام میں اہم ڈیٹا سورس کا کام کرتے ہیں۔"
      step1: "ڈیٹا سورس ایریا میں MySQL query کے آؤٹ پٹ نتائج پیسٹ کریں۔ ٹول خودکار طور پر MySQL command-line آؤٹ پٹ فارمیٹ کو پہچانتا اور parse کرتا ہے، مختلف query result styles اور character encodings کو سپورٹ کرتا ہے، headers اور ڈیٹا rows کو ذہانت سے handle کرتا ہے۔"
      step3: "MySQL query کے نتائج کو متعدد ٹیبل ڈیٹا فارمیٹس میں تیزی سے تبدیل کریں، ڈیٹا تجزیہ، رپورٹ تیاری، کراس سسٹم ڈیٹا منتقلی، اور ڈیٹا validation میں سہولت فراہم کرتا ہے۔ ڈیٹابیس منتظمین اور ڈیٹا تجزیہ کاروں کے لیے عملی ٹول۔"
      from_alias: "MySQL کوئری آؤٹ پٹ"
      to_alias: "MySQL ٹیبل ڈیٹا"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) relational ڈیٹابیسز کے لیے معیاری operation زبان ہے، جو ڈیٹا query، insert، update، اور delete operations کے لیے استعمال ہوتی ہے۔ ڈیٹابیس منیجمنٹ کی بنیادی ٹیکنالوجی کے طور پر، SQL ڈیٹا تجزیہ، business intelligence، ETL پروسیسنگ، اور ڈیٹا warehouse کی تعمیر میں بڑے پیمانے پر استعمال ہوتا ہے۔ یہ ڈیٹا پیشہ ور افراد کے لیے ضروری مہارتی ٹول ہے۔"
    step1: "INSERT SQL statements پیسٹ کریں یا .sql فائلیں اپ لوڈ کریں۔ ٹول ذہانت سے SQL syntax parse کرتا ہے اور ٹیبل ڈیٹا نکالتا ہے، متعدد SQL dialects اور پیچیدہ query statement پروسیسنگ کو سپورٹ کرتا ہے۔"
    step3: "معیاری SQL INSERT statements اور table creation statements تیار کریں۔ متعدد ڈیٹابیس dialects (MySQL، PostgreSQL، SQLite، SQL Server، Oracle) کو سپورٹ کرتا ہے، خودکار طور پر ڈیٹا type mapping، character escaping، اور primary key constraints کو handle کرتا ہے۔ تیار شدہ SQL کوڈ کو براہ راست execute کیا جا سکتا ہے۔"
    from_alias: "SQL ڈیٹا فائل"
    to_alias: "SQL معیاری سٹیٹمنٹ"
  Qlik:
      alias: "Qlik ٹیبل"
      what: "Qlik ایک سافٹ ویئر vendor ہے جو ڈیٹا visualization، executive dashboards، اور self-service business intelligence products میں مہارت رکھتا ہے، Tableau اور Microsoft کے ساتھ۔"
      step1: ""
      step3: "آخر میں، [Table Generator](#TableGenerator) تبدیلی کے نتائج دکھاتا ہے۔ اپنے Qlik Sense، Qlik AutoML، QlikView، یا دیگر Qlik-enabled سافٹ ویئر میں استعمال کریں۔"
      from_alias: "Qlik ٹیبل"
      to_alias: "Qlik ٹیبل"
  DAX:
      alias: "DAX ٹیبل"
      what: "DAX (Data Analysis Expressions) ایک programming زبان ہے جو Microsoft Power BI میں calculated columns، measures، اور custom tables بنانے کے لیے استعمال ہوتی ہے۔"
      step1: ""
      step3: "آخر میں، [Table Generator](#TableGenerator) تبدیلی کے نتائج دکھاتا ہے۔ جیسا کہ متوقع ہے، یہ Microsoft کے کئی products میں استعمال ہوتا ہے جن میں Microsoft Power BI، Microsoft Analysis Services، اور Microsoft Power Pivot for Excel شامل ہیں۔"
      from_alias: "DAX ٹیبل"
      to_alias: "DAX ٹیبل"
  Firebase:
    alias: "Firebase فہرست"
    what: "Firebase ایک BaaS application development platform ہے جو hosted backend services فراہم کرتا ہے جیسے real-time database، cloud storage، authentication، crash reporting وغیرہ۔"
    step1: ""
    step3: "آخر میں، [Table Generator](#TableGenerator) تبدیلی کے نتائج دکھاتا ہے۔ پھر آپ Firebase API میں push method استعمال کرکے Firebase database میں ڈیٹا کی فہرست میں اضافہ کر سکتے ہیں۔"
    from_alias: "Firebase فہرست"
    to_alias: "Firebase فہرست"
  HTML:
    alias: "HTML ٹیبل"
    what: "HTML ٹیبلز ویب صفحات میں structured ڈیٹا دکھانے کا معیاری طریقہ ہیں، جو table، tr، td اور دیگر tags کے ساتھ بنائے جاتے ہیں۔ بھرپور style customization، responsive layout، اور interactive functionality کو سپورٹ کرتے ہیں۔ ویب سائٹ ڈیولپمنٹ، ڈیٹا ڈسپلے، اور رپورٹ تیاری میں بڑے پیمانے پر استعمال ہوتے ہیں، front-end development اور web design کے اہم جزو کا کام کرتے ہیں۔"
    step1: "ٹیبلز پر مشتمل HTML کوڈ پیسٹ کریں یا HTML فائلیں اپ لوڈ کریں۔ ٹول خودکار طور پر صفحات سے ٹیبل ڈیٹا کو پہچانتا اور نکالتا ہے، پیچیدہ HTML structures، CSS styles، اور nested table پروسیسنگ کو سپورٹ کرتا ہے۔"
    step3: "semantic HTML ٹیبل کوڈ تیار کریں جو thead/tbody structure، CSS class سیٹنگز، table captions، row/column headers، اور responsive attribute configuration کو سپورٹ کرتا ہے۔ تیار شدہ ٹیبل کوڈ کو web معیارات کے ساتھ موافق ہونے اور اچھی accessibility اور SEO دوستی کو یقینی بناتا ہے۔"
    from_alias: "HTML ویب ٹیبل"
    to_alias: "HTML معیاری ٹیبل"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel دنیا کا سب سے مقبول spreadsheet سافٹ ویئر ہے، جو کاروباری تجزیہ، مالی منیجمنٹ، ڈیٹا پروسیسنگ، اور رپورٹ تیاری میں بڑے پیمانے پر استعمال ہوتا ہے۔ اس کی طاقتور ڈیٹا پروسیسنگ صلاحیات، بھرپور function library، اور لچکدار visualization features اسے office automation اور ڈیٹا تجزیہ کے لیے معیاری ٹول بناتے ہیں، تقریباً تمام صنعتوں اور شعبوں میں وسیع استعمال کے ساتھ۔"
    step1: "Excel فائلیں اپ لوڈ کریں (.xlsx، .xls formats کو سپورٹ کرتا ہے) یا Excel سے براہ راست ٹیبل ڈیٹا کاپی کرکے پیسٹ کریں۔ ٹول multi-worksheet پروسیسنگ، پیچیدہ فارمیٹ کی شناخت، اور بڑی فائلوں کی تیز parsing کو سپورٹ کرتا ہے، merged cells اور ڈیٹا types کو خودکار طور پر handle کرتا ہے۔"
    step3: "Excel-compatible ٹیبل ڈیٹا تیار کریں جو براہ راست Excel میں پیسٹ کیا جا سکتا ہے یا معیاری .xlsx فائلوں کے طور پر ڈاؤن لوڈ کیا جا سکتا ہے۔ worksheet naming، cell formatting، auto column width، header styling، اور data validation سیٹنگز کو سپورٹ کرتا ہے۔ آؤٹ پٹ Excel فائلوں کو پیشہ ورانہ شکل اور مکمل فعالیت کو یقینی بناتا ہے۔"
    from_alias: "Excel اسپریڈشیٹ"
    to_alias: "Excel معیاری فارمیٹ"
  LaTeX:
    alias: "LaTeX ٹیبل"
    what: "LaTeX ایک پیشہ ورانہ دستاویز typesetting سسٹم ہے، خاص طور پر تعلیمی مقالات، تکنیکی دستاویزات، اور سائنسی publications بنانے کے لیے موزوں ہے۔ اس کی ٹیبل فعالیت طاقتور ہے، پیچیدہ ریاضی کے فارمولے، درست layout control، اور اعلیٰ معیار کی PDF آؤٹ پٹ کو سپورٹ کرتی ہے۔ یہ تعلیمی اور سائنسی publishing میں معیاری ٹول ہے، journal papers، dissertations، اور تکنیکی manual typesetting میں بڑے پیمانے پر استعمال ہوتا ہے۔"
    step1: "LaTeX ٹیبل کوڈ پیسٹ کریں یا .tex فائلیں اپ لوڈ کریں۔ ٹول LaTeX ٹیبل syntax parse کرتا ہے اور ڈیٹا مواد نکالتا ہے، متعدد table environments (tabular، longtable، array وغیرہ) اور پیچیدہ فارمیٹ commands کو سپورٹ کرتا ہے۔"
    step3: "پیشہ ورانہ LaTeX ٹیبل کوڈ تیار کریں جو متعدد table environment کے انتخاب، border style configuration، caption position سیٹنگز، document class specification، اور package management کو سپورٹ کرتا ہے۔ مکمل compilable LaTeX دستاویزات تیار کر سکتا ہے، آؤٹ پٹ ٹیبلز کو تعلیمی publishing کے معیارات کو پورا کرنے کو یقینی بناتا ہے۔"
    from_alias: "LaTeX دستاویز ٹیبل"
    to_alias: "LaTeX پیشہ ورانہ فارمیٹ"
  ASCII:
    alias: "ASCII ٹیبل"
    what: "ASCII ٹیبلز plain text characters استعمال کرکے ٹیبل borders اور structures بناتے ہیں، بہترین compatibility اور portability فراہم کرتے ہیں۔ تمام text editors، terminal environments، اور operating systems کے ساتھ compatible ہیں۔ کوڈ documentation، تکنیکی manuals، README فائلوں، اور command-line tool آؤٹ پٹ میں بڑے پیمانے پر استعمال ہوتے ہیں۔ پروگرامرز اور system administrators کے لیے ترجیحی ڈیٹا ڈسپلے فارمیٹ۔"
    step1: "ASCII ٹیبلز پر مشتمل text فائلیں اپ لوڈ کریں یا براہ راست ٹیبل ڈیٹا پیسٹ کریں۔ ٹول ذہانت سے ASCII ٹیبل structures کو پہچانتا اور parse کرتا ہے، متعدد border styles اور alignment formats کو سپورٹ کرتا ہے۔"
    step3: "خوبصورت plain text ASCII ٹیبلز تیار کریں جو متعدد border styles (single line، double line، rounded corners وغیرہ)، text alignment طریقوں، اور auto column width کو سپورٹ کرتے ہیں۔ تیار شدہ ٹیبلز code editors، دستاویزات، اور command lines میں مکمل طور پر display ہوتے ہیں۔"
    from_alias: "ASCII ٹیکسٹ ٹیبل"
    to_alias: "ASCII معیاری فارمیٹ"
  MediaWiki:
    alias: "MediaWiki ٹیبل"
    what: "MediaWiki اوپن سورس سافٹ ویئر platform ہے جو Wikipedia جیسی مشہور wiki sites میں استعمال ہوتا ہے۔ اس کا ٹیبل syntax مختصر لیکن طاقتور ہے، ٹیبل style customization، sorting functionality، اور link embedding کو سپورٹ کرتا ہے۔ knowledge management، collaborative editing، اور content management systems میں بڑے پیمانے پر استعمال ہوتا ہے، wiki encyclopedias اور knowledge bases بنانے کے لیے بنیادی ٹیکنالوجی کا کام کرتا ہے۔"
    step1: "MediaWiki ٹیبل کوڈ پیسٹ کریں یا wiki source فائلیں اپ لوڈ کریں۔ ٹول wiki markup syntax parse کرتا ہے اور ٹیبل ڈیٹا نکالتا ہے، پیچیدہ wiki syntax اور template پروسیسنگ کو سپورٹ کرتا ہے۔"
    step3: "معیاری MediaWiki ٹیبل کوڈ تیار کریں جو header style سیٹنگز، cell alignment، sorting functionality enabling، اور code compression آپشنز کو سپورٹ کرتا ہے۔ تیار شدہ کوڈ براہ راست wiki page editing کے لیے استعمال ہو سکتا ہے، MediaWiki platforms پر مکمل display کو یقینی بناتا ہے۔"
    from_alias: "MediaWiki سورس کوڈ"
    to_alias: "MediaWiki ٹیبل سنٹیکس"
  TracWiki:
    alias: "TracWiki ٹیبل"
    what: "Trac ایک ویب پر مبنی پروجیکٹ منیجمنٹ اور بگ ٹریکنگ سسٹم ہے جو ٹیبل مواد بنانے کے لیے آسان wiki syntax استعمال کرتا ہے۔"
    step1: "TracWiki فائلیں اپ لوڈ کریں یا ٹیبل ڈیٹا پیسٹ کریں۔"
    step3: "TracWiki-compatible ٹیبل کوڈ تیار کریں جو row/column header سیٹنگز کو سپورٹ کرتا ہے، پروجیکٹ document management میں سہولت فراہم کرتا ہے۔"
    from_alias: "TracWiki ٹیبل"
    to_alias: "TracWiki فارمیٹ"
  AsciiDoc:
    alias: "AsciiDoc ٹیبل"
    what: "AsciiDoc ایک ہلکا پھلکا markup زبان ہے جو HTML، PDF، manual pages، اور دیگر فارمیٹس میں تبدیل ہو سکتی ہے، تکنیکی documentation لکھنے میں بڑے پیمانے پر استعمال ہوتی ہے۔"
    step1: "AsciiDoc فائلیں اپ لوڈ کریں یا ڈیٹا پیسٹ کریں۔"
    step3: "AsciiDoc ٹیبل syntax تیار کریں جو header، footer، اور title سیٹنگز کو سپورٹ کرتا ہے، AsciiDoc editors میں براہ راست استعمال کے قابل۔"
    from_alias: "AsciiDoc ٹیبل"
    to_alias: "AsciiDoc فارمیٹ"
  reStructuredText:
    alias: "reStructuredText ٹیبل"
    what: "reStructuredText Python community کے لیے معیاری documentation فارمیٹ ہے، بھرپور ٹیبل syntax کو سپورٹ کرتا ہے، Sphinx documentation generation کے لیے عام طور پر استعمال ہوتا ہے۔"
    step1: ".rst فائلیں اپ لوڈ کریں یا reStructuredText ڈیٹا پیسٹ کریں۔"
    step3: "معیاری reStructuredText ٹیبلز تیار کریں جو متعدد border styles کو سپورٹ کرتے ہیں، Sphinx documentation projects میں براہ راست استعمال کے قابل۔"
    from_alias: "reStructuredText ٹیبل"
    to_alias: "reStructuredText فارمیٹ"
  PHP:
    alias: "PHP ایرے"
    what: "PHP ایک مقبول server-side scripting زبان ہے، جس میں arrays اس کا بنیادی ڈیٹا ڈھانچہ ہے، ویب ڈیولپمنٹ اور ڈیٹا پروسیسنگ میں بڑے پیمانے پر استعمال ہوتی ہے۔"
    step1: "PHP arrays پر مشتمل فائلیں اپ لوڈ کریں یا براہ راست ڈیٹا پیسٹ کریں۔"
    step3: "معیاری PHP array کوڈ تیار کریں جو PHP projects میں براہ راست استعمال ہو سکتا ہے، associative اور indexed array formats کو سپورٹ کرتا ہے۔"
    from_alias: "PHP ایرے"
    to_alias: "PHP کوڈ"
  Ruby:
    alias: "Ruby ایرے"
    what: "Ruby ایک dynamic object-oriented programming زبان ہے جس کا syntax مختصر اور خوبصورت ہے، arrays اس کا اہم ڈیٹا ڈھانچہ ہے۔"
    step1: "Ruby فائلیں اپ لوڈ کریں یا array ڈیٹا پیسٹ کریں۔"
    step3: "Ruby array کوڈ تیار کریں جو Ruby syntax specifications کے ساتھ موافق ہے، Ruby projects میں براہ راست استعمال کے قابل۔"
    from_alias: "Ruby ایرے"
    to_alias: "Ruby کوڈ"
  ASP:
    alias: "ASP ایرے"
    what: "ASP (Active Server Pages) Microsoft کا server-side scripting environment ہے، جو dynamic ویب صفحات کی تیاری کے لیے متعدد programming زبانوں کو سپورٹ کرتا ہے۔"
    step1: "ASP فائلیں اپ لوڈ کریں یا array ڈیٹا پیسٹ کریں۔"
    step3: "ASP-compatible array کوڈ تیار کریں جو VBScript اور JScript syntax کو سپورٹ کرتا ہے، ASP.NET projects میں استعمال کے قابل۔"
    from_alias: "ASP ایرے"
    to_alias: "ASP کوڈ"
  ActionScript:
    alias: "ActionScript ایرے"
    what: "ActionScript ایک object-oriented programming زبان ہے جو بنیادی طور پر Adobe Flash اور AIR application development کے لیے استعمال ہوتی ہے۔"
    step1: ".as فائلیں اپ لوڈ کریں یا ActionScript ڈیٹا پیسٹ کریں۔"
    step3: "ActionScript array کوڈ تیار کریں جو AS3 syntax standards کے ساتھ موافق ہے، Flash اور Flex project development کے لیے استعمال کے قابل۔"
    from_alias: "ActionScript ایرے"
    to_alias: "ActionScript کوڈ"
  BBCode:
    alias: "BBCode ٹیبل"
    what: "BBCode ایک ہلکا پھلکا markup زبان ہے جو forums اور online communities میں عام طور پر استعمال ہوتی ہے، ٹیبل سپورٹ سمیت سادہ formatting functionality فراہم کرتی ہے۔"
    step1: "BBCode پر مشتمل فائلیں اپ لوڈ کریں یا ڈیٹا پیسٹ کریں۔"
    step3: "forum posting اور community content creation کے لیے موزوں BBCode ٹیبل کوڈ تیار کریں، compressed output format کو سپورٹ کرتا ہے۔"
    from_alias: "BBCode ٹیبل"
    to_alias: "BBCode فارمیٹ"
  PDF:
    alias: "PDF ٹیبل"
    what: "PDF (Portable Document Format) ایک کراس پلیٹ فارم دستاویز معیار ہے جس میں مقرر layout، مستقل display، اور اعلیٰ معیار کی printing خصوصیات ہیں۔ رسمی دستاویزات، رپورٹس، invoices، معاہدات، اور تعلیمی مقالات میں بڑے پیمانے پر استعمال ہوتا ہے۔ کاروباری رابطے اور دستاویز archiving کے لیے ترجیحی فارمیٹ، مختلف devices اور operating systems میں مکمل طور پر مستقل visual effects کو یقینی بناتا ہے۔"
    step1: "کسی بھی فارمیٹ میں ٹیبل ڈیٹا import کریں۔ ٹول خودکار طور پر ڈیٹا structure کا تجزیہ کرتا ہے اور ذہین layout design کرتا ہے، بڑے ٹیبل auto-pagination اور پیچیدہ ڈیٹا type پروسیسنگ کو سپورٹ کرتا ہے۔"
    step3: "اعلیٰ معیار کی PDF ٹیبل فائلیں تیار کریں جو متعدد پیشہ ورانہ theme styles (business، academic، minimalist وغیرہ)، multilingual fonts، auto-pagination، watermark اضافہ، اور print optimization کو سپورٹ کرتی ہیں۔ آؤٹ پٹ PDF دستاویزات کو پیشہ ورانہ شکل کو یقینی بناتا ہے، کاروباری presentations اور رسمی publication کے لیے براہ راست استعمال کے قابل۔"
    from_alias: "ٹیبل ڈیٹا"
    to_alias: "PDF پیشہ ورانہ دستاویز"
  JPEG:
    alias: "JPEG تصویر"
    what: "JPEG سب سے زیادہ استعمال ہونے والا digital image فارمیٹ ہے جس میں بہترین compression effects اور وسیع compatibility ہے۔ اس کا چھوٹا فائل سائز اور تیز loading رفتار اسے ویب display، social media sharing، document illustrations، اور online presentations کے لیے موزوں بناتا ہے۔ digital media اور network communication کے لیے معیاری image فارمیٹ، تقریباً تمام devices اور software کے ذریعے مکمل طور پر سپورٹ ہوتا ہے۔"
    step1: "کسی بھی فارمیٹ میں ٹیبل ڈیٹا import کریں۔ ٹول ذہین layout design اور visual optimization کرتا ہے، خودکار طور پر optimal سائز اور resolution کا حساب لگاتا ہے۔"
    step3: "اعلیٰ تعریف والی JPEG ٹیبل images تیار کریں جو متعدد theme color schemes (light، dark، eye-friendly وغیرہ)، adaptive layout، text clarity optimization، اور size customization کو سپورٹ کرتی ہیں۔ online sharing، document insertion، اور presentation استعمال کے لیے موزوں، مختلف display devices پر بہترین visual effects کو یقینی بناتا ہے۔"
    from_alias: "ٹیبل ڈیٹا"
    to_alias: "JPEG اعلیٰ تعریف تصویر"
  Jira:
    alias: "Jira ٹیبل"
    what: "JIRA Atlassian کی طرف سے تیار کردہ پیشہ ورانہ پروجیکٹ مینجمنٹ اور بگ ٹریکنگ سافٹ ویئر ہے، جو ایگائل ڈیولپمنٹ، سافٹ ویئر ٹیسٹنگ، اور پروجیکٹ تعاون میں بڑے پیمانے پر استعمال ہوتا ہے۔ اس کی ٹیبل فیچر بھرپور فارمیٹنگ آپشنز اور ڈیٹا ڈسپلے کو سپورٹ کرتی ہے، سافٹ ویئر ڈیولپمنٹ ٹیمز، پروجیکٹ منیجرز، اور کوالٹی ایشورنس پرسنل کے لیے ضروریات کی انتظامیہ، بگ ٹریکنگ، اور پیشرفت کی رپورٹنگ میں اہم ٹول کا کام کرتی ہے۔"
    step1: "ٹیبل ڈیٹا والی فائلیں اپ لوڈ کریں یا براہ راست ڈیٹا مواد پیسٹ کریں۔ ٹول خودکار طور پر ٹیبل ڈیٹا اور خصوصی کردار escape پروسیسنگ کرتا ہے۔"
    step3: "JIRA پلیٹ فارم کے ساتھ موافق ٹیبل کوڈ تیار کریں جو ہیڈر سٹائل سیٹنگز، سیل الائنمنٹ، کریکٹر escape پروسیسنگ، اور فارمیٹ آپٹیمائزیشن کو سپورٹ کرتا ہے۔ تیار شدہ کوڈ براہ راست JIRA ایشو تفصیلات، تبصروں، یا ویکی صفحات میں پیسٹ کیا جا سکتا ہے، جو JIRA سسٹمز میں درست ڈسپلے اور رینڈرنگ کو یقینی بناتا ہے۔"
    from_alias: "پروجیکٹ ڈیٹا"
    to_alias: "Jira ٹیبل سنٹیکس"
  Textile:
    alias: "Textile ٹیبل"
    what: "Textile ایک مختصر ہلکا پھلکا مارک اپ زبان ہے جس کا سادہ اور آسان سیکھنے والا نحو ہے، جو مواد منظوری سسٹمز، بلاگ پلیٹ فارمز، اور فورم سسٹمز میں بڑے پیمانے پر استعمال ہوتا ہے۔ اس کا ٹیبل سنٹیکس واضح اور بدیہی ہے، تیز فارمیٹنگ اور سٹائل سیٹنگز کو سپورٹ کرتا ہے۔ تیز دستاویز لکھنے اور مواد اشاعت کے لیے مواد تخلیق کاروں اور ویب سائٹ منتظمین کے لیے مثالی ٹول ہے۔"
    step1: "Textile فارمیٹ فائلیں اپ لوڈ کریں یا ٹیبل ڈیٹا پیسٹ کریں۔ ٹول Textile markup syntax parse کرتا ہے اور ٹیبل مواد نکالتا ہے۔"
    step3: "معیاری Textile ٹیبل سنٹیکس تیار کریں جو ہیڈر مارک اپ، سیل الائنمنٹ، خصوصی کردار escape، اور فارمیٹ آپٹیمائزیشن کو سپورٹ کرتا ہے۔ تیار شدہ کوڈ براہ راست CMS سسٹمز، بلاگ پلیٹ فارمز، اور Textile کو سپورٹ کرنے والے دستاویزی سسٹمز میں استعمال ہو سکتا ہے، جو درست مواد رینڈرنگ اور ڈسپلے کو یقینی بناتا ہے۔"
    from_alias: "Textile دستاویز"
    to_alias: "Textile ٹیبل سنٹیکس"
  PNG:
    alias: "PNG تصویر"
    what: "PNG (Portable Network Graphics) ایک بے نقصان تصویری فارمیٹ ہے جس میں بہترین کمپریشن اور شفافیت کی حمایت ہے۔ یہ ویب ڈیزائن، ڈیجیٹل گرافکس، اور پیشہ ورانہ فوٹوگرافی میں بڑے پیمانے پر استعمال ہوتا ہے۔ اس کا اعلیٰ معیار اور وسیع مطابقت اسے اسکرین شاٹس، لوگوز، ڈایاگرامز، اور واضح تفصیلات اور شفاف پس منظر کی ضرورت والی کسی بھی تصاویر کے لیے مثالی بناتا ہے۔"
    step1: "کسی بھی فارمیٹ میں ٹیبل ڈیٹا درآمد کریں۔ ٹول ذہین لے آؤٹ ڈیزائن اور بصری آپٹیمائزیشن کرتا ہے، PNG آؤٹ پٹ کے لیے خودکار طور پر بہترین سائز اور ریزولیوشن کا حساب لگاتا ہے۔"
    step3: "اعلیٰ معیار کی PNG ٹیبل تصاویر تیار کریں جو متعدد تھیم کلر اسکیمز، شفاف پس منظر، موافقت پذیر لے آؤٹ، اور متن کی وضاحت آپٹیمائزیشن کو سپورٹ کرتی ہیں۔ ویب استعمال، دستاویز داخل کرنے، اور بہترین بصری معیار کے ساتھ پیشہ ورانہ پیش کاریوں کے لیے بہترین۔"
    from_alias: "ٹیبل ڈیٹا"
    to_alias: "PNG اعلیٰ معیار تصویر"
  TOML:
    alias: "TOML کنفیگریشن"
    what: "TOML (Tom's Obvious, Minimal Language) ایک کنفیگریشن فائل فارمیٹ ہے جو پڑھنے اور لکھنے میں آسان ہے۔ غیر مبہم اور سادہ ہونے کے لیے ڈیزائن کیا گیا، یہ کنفیگریشن انتظام کے لیے جدید سافٹ ویئر پروجیکٹس میں بڑے پیمانے پر استعمال ہوتا ہے۔ اس کا واضح نحو اور مضبوط ٹائپنگ اسے ایپلیکیشن سیٹنگز اور پروجیکٹ کنفیگریشن فائلوں کے لیے بہترین انتخاب بناتا ہے۔"
    step1: "TOML فائلیں اپ لوڈ کریں یا کنفیگریشن ڈیٹا پیسٹ کریں۔ ٹول TOML سنٹیکس پارس کرتا ہے اور منظم کنفیگریشن معلومات نکالتا ہے۔"
    step3: "معیاری TOML فارمیٹ تیار کریں جو نیسٹیڈ ڈھانچوں، ڈیٹا اقسام، اور تبصروں کو سپورٹ کرتا ہے۔ تیار شدہ TOML فائلیں ایپلیکیشن کنفیگریشن، بلڈ ٹولز، اور پروجیکٹ سیٹنگز کے لیے بہترین ہیں۔"
    from_alias: "TOML کنفیگریشن"
    to_alias: "TOML فارمیٹ"
  INI:
    alias: "INI کنفیگریشن"
    what: "INI فائلیں سادہ کنفیگریشن فائلیں ہیں جو بہت سی ایپلیکیشنز اور آپریٹنگ سسٹمز استعمال کرتے ہیں۔ ان کا سیدھا سادہ کلیدی-قدر جوڑا ڈھانچہ انہیں دستی طور پر پڑھنے اور ترمیم کرنے میں آسان بناتا ہے۔ Windows ایپلیکیشنز، قدیمی سسٹمز، اور سادہ کنفیگریشن منظرناموں میں بڑے پیمانے پر استعمال ہوتا ہے جہاں انسانی قابلیت اہم ہے۔"
    step1: "INI فائلیں اپ لوڈ کریں یا کنفیگریشن ڈیٹا پیسٹ کریں۔ ٹول INI سنٹیکس پارس کرتا ہے اور سیکشن پر مبنی کنفیگریشن معلومات نکالتا ہے۔"
    step3: "معیاری INI فارمیٹ تیار کریں جو سیکشنز، تبصروں، اور مختلف ڈیٹا اقسام کو سپورٹ کرتا ہے۔ تیار شدہ INI فائلیں زیادہ تر ایپلیکیشنز اور کنفیگریشن سسٹمز کے ساتھ موافق ہیں۔"
    from_alias: "INI کنفیگریشن"
    to_alias: "INI فارمیٹ"
  Avro:
    alias: "Avro اسکیما"
    what: "Apache Avro ایک ڈیٹا سیریلائزیشن سسٹم ہے جو بھرپور ڈیٹا ڈھانچے، کمپیکٹ بائنری فارمیٹ، اور اسکیما ارتقاء کی صلاحیات فراہم کرتا ہے۔ بگ ڈیٹا پروسیسنگ، پیغام کیوز، اور تقسیم شدہ سسٹمز میں بڑے پیمانے پر استعمال ہوتا ہے۔ اس کی اسکیما تعریف پیچیدہ ڈیٹا اقسام اور ورژن مطابقت کو سپورٹ کرتی ہے، جو اسے ڈیٹا انجینئرز اور سسٹم آرکیٹیکٹس کے لیے اہم ٹول بناتا ہے۔"
    step1: "Avro اسکیما فائلیں اپ لوڈ کریں یا ڈیٹا پیسٹ کریں۔ ٹول Avro اسکیما تعاریف پارس کرتا ہے اور ٹیبل ڈھانچہ معلومات نکالتا ہے۔"
    step3: "معیاری Avro اسکیما تعاریف تیار کریں جو ڈیٹا ٹائپ میپنگ، فیلڈ قیود، اور اسکیما توثیق کو سپورٹ کرتی ہیں۔ تیار شدہ اسکیمز براہ راست Hadoop ایکو سسٹمز، Kafka پیغام سسٹمز، اور دیگر بگ ڈیٹا پلیٹ فارمز میں استعمال ہو سکتے ہیں۔"
    from_alias: "Avro اسکیما"
    to_alias: "Avro ڈیٹا فارمیٹ"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) Google کا زبان غیر جانبدار، پلیٹ فارم غیر جانبدار، منظم ڈیٹا کو سیریلائز کرنے کے لیے قابل توسیع میکانزم ہے۔ مائیکرو سروسز، API ڈیولپمنٹ، اور ڈیٹا اسٹوریج میں بڑے پیمانے پر استعمال ہوتا ہے۔ اس کا موثر بائنری فارمیٹ اور مضبوط ٹائپنگ اسے اعلیٰ کارکردگی ایپلیکیشنز اور کراس لینگویج کمیونیکیشن کے لیے مثالی بناتا ہے۔"
    step1: ".proto فائلیں اپ لوڈ کریں یا Protocol Buffer تعاریف پیسٹ کریں۔ ٹول protobuf سنٹیکس پارس کرتا ہے اور پیغام ڈھانچہ معلومات نکالتا ہے۔"
    step3: "معیاری Protocol Buffer تعاریف تیار کریں جو پیغام اقسام، فیلڈ آپشنز، اور سروس تعاریف کو سپورٹ کرتی ہیں۔ تیار شدہ .proto فائلیں متعدد پروگرامنگ زبانوں کے لیے کمپائل ہو سکتی ہیں۔"
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf اسکیما"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas Python میں سب سے مقبول ڈیٹا تجزیہ لائبریری ہے، جس میں DataFrame اس کا بنیادی ڈیٹا ڈھانچہ ہے۔ یہ طاقتور ڈیٹا میں ہیرا پھیری، صفائی، اور تجزیہ کی صلاحیات فراہم کرتا ہے، ڈیٹا سائنس، مشین لرننگ، اور بزنس انٹیلیجنس میں بڑے پیمانے پر استعمال ہوتا ہے۔ Python ڈیولپرز اور ڈیٹا تجزیہ کاروں کے لیے ناگزیر ٹول ہے۔"
    step1: "DataFrame کوڈ والی Python فائلیں اپ لوڈ کریں یا ڈیٹا پیسٹ کریں۔ ٹول Pandas سنٹیکس پارس کرتا ہے اور DataFrame ڈھانچہ معلومات نکالتا ہے۔"
    step3: "معیاری Pandas DataFrame کوڈ تیار کریں جو ڈیٹا ٹائپ تصریحات، انڈیکس سیٹنگز، اور ڈیٹا آپریشنز کو سپورٹ کرتا ہے۔ تیار شدہ کوڈ براہ راست Python ماحول میں ڈیٹا تجزیہ اور پروسیسنگ کے لیے چلایا جا سکتا ہے۔"
    from_alias: "Pandas DataFrame"
    to_alias: "Python ڈیٹا ڈھانچہ"
  RDF:
    alias: "RDF ٹرپل"
    what: "RDF (Resource Description Framework) ویب پر ڈیٹا تبادلے کے لیے ایک معیاری ماڈل ہے، جو وسائل کے بارے میں معلومات کو گراف شکل میں پیش کرنے کے لیے ڈیزائن کیا گیا ہے۔ semantic web، علم گرافس، اور منسلک ڈیٹا ایپلیکیشنز میں بڑے پیمانے پر استعمال ہوتا ہے۔ اس کا ٹرپل ڈھانچہ بھرپور میٹاڈیٹا نمائندگی اور semantic تعلقات کو قابل بناتا ہے۔"
    step1: "RDF فائلیں اپ لوڈ کریں یا ٹرپل ڈیٹا پیسٹ کریں۔ ٹول RDF سنٹیکس پارس کرتا ہے اور semantic تعلقات اور وسائل کی معلومات نکالتا ہے۔"
    step3: "معیاری RDF فارمیٹ تیار کریں جو مختلف سیریلائزیشنز (RDF/XML، Turtle، N-Triples) کو سپورٹ کرتا ہے۔ تیار شدہ RDF semantic web ایپلیکیشنز، علم بنیادوں، اور منسلک ڈیٹا سسٹمز میں استعمال ہو سکتا ہے۔"
    from_alias: "RDF ڈیٹا"
    to_alias: "RDF Semantic فارمیٹ"
  MATLAB:
    alias: "MATLAB ایرے"
    what: "MATLAB ایک اعلیٰ کارکردگی عددی کمپیوٹنگ اور تصوری سافٹ ویئر ہے جو انجینئرنگ کمپیوٹنگ، ڈیٹا تجزیہ، اور الگورتھم ڈیولپمنٹ میں بڑے پیمانے پر استعمال ہوتا ہے۔ اس کے array اور matrix آپریشنز طاقتور ہیں، پیچیدہ ریاضی کیلکولیشنز اور ڈیٹا پروسیسنگ کو سپورٹ کرتے ہیں۔ انجینئرز، محققین، اور ڈیٹا سائنسدانوں کے لیے ضروری ٹول ہے۔"
    step1: "MATLAB .m فائلیں اپ لوڈ کریں یا array ڈیٹا پیسٹ کریں۔ ٹول MATLAB سنٹیکس پارس کرتا ہے اور array ڈھانچہ معلومات نکالتا ہے۔"
    step3: "معیاری MATLAB array کوڈ تیار کریں جو کثیر جہتی arrays، ڈیٹا ٹائپ تصریحات، اور متغیر نام کو سپورٹ کرتا ہے۔ تیار شدہ کوڈ براہ راست MATLAB ماحول میں ڈیٹا تجزیہ اور سائنسی کمپیوٹنگ کے لیے چلایا جا سکتا ہے۔"
    from_alias: "MATLAB ایرے"
    to_alias: "MATLAB کوڈ فارمیٹ"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame یہ R پروگرامنگ زبان کی بنیادی ڈیٹا ساخت ہے، جو شماریاتی تجزیے، ڈیٹا مائننگ، اور مشین لرننگ میں بڑے پیمانے پر استعمال ہوتی ہے۔ R شماریاتی کمپیوٹنگ اور گرافکس کے لیے ایک اہم ٹول ہے، DataFrame طاقتور ڈیٹا ہینڈلنگ، شماریاتی تجزیہ، اور ویژولائزیشن کی صلاحیات فراہم کرتا ہے۔ منظم ڈیٹا تجزیے میں کام کرنے والے ڈیٹا سائنسدان، شماریات دان، اور محققین کے لیے انتہائی ضروری ہے۔"
    step1: "R ڈیٹا فائلز اپ لوڈ کریں یا DataFrame کوڈ پیسٹ کریں۔ یہ ٹول R سنٹیکس پارس کرتا ہے اور کالم کی اقسام، قطار کے نام، اور ڈیٹا مواد سمیت DataFrame ڈھانچے کی معلومات نکالتا ہے۔"
    step3: "ڈیٹا قسم کی تفصیلات، فیکٹر لیولز، قطار/کالم نام، اور R-مخصوص ڈیٹا ڈھانچوں کی سپورٹ کے ساتھ معیاری R DataFrame کوڈ تیار کریں۔ تیار کردہ کوڈ شماریاتی تجزیہ اور ڈیٹا پروسیسنگ کے لیے R ماحول میں براہ راست چلایا جا سکتا ہے۔"
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "تبدیلی شروع کریں"
  start_generating: "تولید شروع کریں"
  api_docs: "API دستاویزات"
related:
  section_title: 'مزید {{ if and .from (ne .from "generator") }}{{ .from }} اور {{ end }}{{ .to }} کنورٹرز'
  section_description: '{{ if and .from (ne .from "generator") }}{{ .from }} اور {{ end }}{{ .to }} فارمیٹس کے لیے مزید کنورٹرز دریافت کریں۔ ہمارے پیشہ ورانہ آن لائن تبدیلی کے ٹولز کے ساتھ اپنے ڈیٹا کو متعدد فارمیٹس کے درمیان تبدیل کریں۔'
  title: "{{ .from }} سے {{ .to }}"
howto:
  step2: "پیشہ ورانہ فیچرز کے ساتھ ہمارے ایڈوانس آن لائن ٹیبل ایڈیٹر کا استعمال کرتے ہوئے ڈیٹا میں ترمیم کریں۔ خالی قطاروں کو حذف کرنا، ڈپلیکیٹس ہٹانا، ڈیٹا ٹرانسپوزیشن، سارٹنگ، ریجیکس تلاش اور تبدیل، اور ریئل ٹائم پیش منظر کو سپورٹ کرتا ہے۔ تمام تبدیلیاں درست، قابل اعتماد نتائج کے ساتھ خودکار طور پر %s فارمیٹ میں تبدیل ہو جاتی ہیں۔"
  section_title: "{{ . }} کا استعمال کیسے کریں"
  converter_description: "ہمارے قدم بہ قدم گائیڈ کے ساتھ {{ .from }} کو {{ .to }} میں تبدیل کرنا سیکھیں۔ ایڈوانس فیچرز اور ریئل ٹائم پیش منظر کے ساتھ پیشہ ورانہ آن لائن کنورٹر۔"
  generator_description: "ہمارے آن لائن جنریٹر کے ساتھ پیشہ ورانہ {{ .to }} ٹیبلز بنانا سیکھیں۔ Excel جیسی ایڈٹنگ، ریئل ٹائم پیش منظر، اور فوری ایکسپورٹ کی صلاحیات۔"
extension:
  section_title: "ٹیبل کی شناخت اور نکالنے کی توسیع"
  section_description: "ایک کلک سے کسی بھی ویب سائٹ سے ٹیبلز نکالیں۔ Excel، CSV، JSON سمیت 30+ فارمیٹس میں فوری طور پر تبدیل کریں - کاپی پیسٹ کی ضرورت نہیں۔"
  features:
    extraction_title: "ون کلک ٹیبل ایکسٹریکشن"
    extraction_description: "کاپی پیسٹ کے بغیر کسی بھی ویب پیج سے فوری طور پر ٹیبلز نکالیں - پیشہ ورانہ ڈیٹا ایکسٹریکشن آسان بنایا گیا"
    formats_title: "30+ فارمیٹ کنورٹر سپورٹ"
    formats_description: "ہمارے ایڈوانس ٹیبل کنورٹر کے ساتھ نکالے گئے ٹیبلز کو Excel، CSV، JSON، Markdown، SQL اور مزید میں تبدیل کریں"
    detection_title: "سمارٹ ٹیبل ڈیٹیکشن"
    detection_description: "تیز ڈیٹا ایکسٹریکشن اور تبدیلی کے لیے کسی بھی ویب پیج پر ٹیبلز کو خودکار طور پر تلاش اور ہائی لائٹ کرتا ہے"
  hover_tip: "✨ ایکسٹریکشن آئیکن دیکھنے کے لیے کسی بھی ٹیبل پر ہوور کریں"
recommendations:
  section_title: "یونیورسٹیوں اور پیشہ ور افراد کی جانب سے تجویز کردہ"
  section_description: "قابل اعتماد ٹیبل تبدیلی اور ڈیٹا پروسیسنگ کے لیے یونیورسٹیوں، تحقیقی اداروں اور ڈیولپمنٹ ٹیموں کے پیشہ ور افراد TableConvert پر بھروسہ کرتے ہیں۔"
  cards:
    university_title: "یونیورسٹی آف وسکونسن-میڈیسن"
    university_description: "TableConvert.com - پیشہ ورانہ مفت آن لائن ٹیبل کنورٹر اور ڈیٹا فارمیٹس ٹول"
    university_link: "مضمون پڑھیں"
    facebook_title: "ڈیٹا پیشہ ور کمیونٹی"
    facebook_description: "Facebook ڈیولپر گروپس میں ڈیٹا تجزیہ کاروں اور پیشہ ور افراد کی جانب سے شیئر اور تجویز کیا گیا"
    facebook_link: "پوسٹ دیکھیں"
    twitter_title: "ڈیولپر کمیونٹی"
    twitter_description: "ٹیبل تبدیلی کے لیے X (Twitter) پر @xiaoying_eth اور دیگر ڈیولپرز کی جانب سے تجویز کردہ"
    twitter_link: "ٹویٹ دیکھیں"
faq:
  section_title: "اکثر پوچھے جانے والے سوالات"
  section_description: "ہمارے مفت آن لائن ٹیبل کنورٹر، ڈیٹا فارمیٹس اور تبدیلی کے عمل کے بارے میں عام سوالات."
  what: "%s فارمیٹ کیا ہے؟"
  howto_convert:
    question: "{{ . }} کو مفت میں کیسے استعمال کریں؟"
    answer: "ہمارے مفت آن لائن ٹیبل کنورٹر کا استعمال کرتے ہوئے اپنی {{ .from }} فائل اپ لوڈ کریں، ڈیٹا پیسٹ کریں، یا ویب پیجز سے نکالیں۔ ہمارا پیشہ ورانہ کنورٹر ٹول ریئل ٹائم پیش منظر اور ایڈوانس ایڈٹنگ فیچرز کے ساتھ آپ کے ڈیٹا کو فوری طور پر {{ .to }} فارمیٹ میں تبدیل کر دیتا ہے۔ تبدیل شدہ نتیجہ فوری طور پر ڈاؤن لوڈ یا کاپی کریں۔"
  security:
    question: "کیا اس آن لائن کنورٹر کا استعمال کرتے وقت میرا ڈیٹا محفوظ ہے؟"
    answer: "بالکل! تمام ٹیبل تبدیلیاں آپ کے براؤزر میں مقامی طور پر ہوتی ہیں - آپ کا ڈیٹا کبھی آپ کے ڈیوائس کو نہیں چھوڑتا۔ ہمارا آن لائن کنورٹر سب کچھ کلائنٹ سائیڈ پر پروسیس کرتا ہے، مکمل رازداری اور ڈیٹا سیکیورٹی کو یقینی بناتا ہے۔ ہمارے سرورز پر کوئی فائلیں محفوظ نہیں کی جاتیں۔"
  free:
    question: "کیا TableConvert واقعی استعمال کے لیے مفت ہے؟"
    answer: "جی ہاں، TableConvert مکمل طور پر مفت ہے! تمام کنورٹر فیچرز، ٹیبل ایڈیٹر، ڈیٹا جنریٹر ٹولز، اور ایکسپورٹ آپشنز بغیر کسی لاگت، رجسٹریشن، یا چھپی ہوئی فیسوں کے دستیاب ہیں۔ مفت میں آن لائن لامحدود فائلیں تبدیل کریں۔"
  filesize:
    question: "آن لائن کنورٹر کی فائل سائز کی حدود کیا ہیں؟"
    answer: "ہمارا مفت آن لائن ٹیبل کنورٹر 10MB تک کی فائلوں کو سپورٹ کرتا ہے۔ بڑی فائلوں، بیچ پروسیسنگ، یا انٹرپرائز ضروریات کے لیے، زیادہ حدود کے ساتھ ہمارا براؤزر ایکسٹینشن یا پیشہ ورانہ API سروس استعمال کریں۔"
stats:
  conversions: "تبدیل شدہ ٹیبلز"
  tables: "تیار کردہ ٹیبلز"
  formats: "ڈیٹا فائل فارمیٹس"
  rating: "صارف کی درجہ بندی"
