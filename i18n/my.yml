site:
  fullname: "အွန်လိုင်း ဇယား ပြောင်းလဲခြင်း"
  name: "TableConvert"
  subtitle: "အခမဲ့ အွန်လိုင်း ဇယား ပြောင်းလဲကိရိယာနှင့် ဂျင်နရေတာ"
  intro: "TableConvert သည် Excel, CSV, JSON, Markdown, LaTeX, SQL နှင့် အခြားအပါအဝင် 30+ ဖော်မတ်များကြားတွင် ပြောင်းလဲခြင်းကို ပံ့ပိုးပေးသော အခမဲ့ အွန်လိုင်း ဇယား ပြောင်းလဲကိရိယာနှင့် ဒေတာ ဂျင်နရေတာ ကိရိယာဖြစ်သည်."
  followTwitter: "X တွင် ကျွန်ုပ်တို့ကို လိုက်ပါ"
title:
  converter: "%s မှ %s သို့"
  generator: "%s ဂျင်နရေတာ"
post:
  tags:
    converter: "ပြောင်းလဲကိရိယာ"
    editor: "တည်းဖြတ်ကိရိယာ"
    generator: "ဂျင်နရေတာ"
    maker: "တည်ဆောက်ကိရိယာ"
  converter:
    title: "%s ကို %s အဖြစ် အွန်လိုင်းတွင် ပြောင်းလဲပါ"
    short: "အခမဲ့နှင့် အားကောင်းသော %s မှ %s အွန်လိုင်း ကိရိယာ"
    intro: "အသုံးပြုရလွယ်ကူသော အွန်လိုင်း %s မှ %s ပြောင်းလဲကိရိယာ။ ကျွန်ုပ်တို့၏ အလိုအလျောက် ပြောင်းလဲကိရိယာဖြင့် ဇယားဒေတာကို အလွယ်တကူ ပြောင်းလဲပါ။ မြန်ဆန်၊ ယုံကြည်စိတ်ချရပြီး အသုံးပြုသူ-ဖော်ရွေသော။"
  generator:
    title: "အွန်လိုင်း %s တည်းဖြတ်ကိရိယာနှင့် ဂျင်နရေတာ"
    short: "ကျယ်ပြန့်သော လုပ်ဆောင်ချက်များပါရှိသော ပရော်ဖက်ရှင်နယ် %s အွန်လိုင်း ဖန်တီးကိရိယာ"
    intro: "အသုံးပြုရလွယ်ကူသော အွန်လိုင်း %s ဂျင်နရေတာနှင့် ဇယား တည်းဖြတ်ကိရိယာ။ ကျွန်ုပ်တို့၏ အလိုအလျောက် ကိရိယာနှင့် အချိန်နှင့်တပြေးညီ ကြိုကြည့်ခြင်းဖြင့် ပရော်ဖက်ရှင်နယ် ဒေတာဇယားများကို အလွယ်တကူ ဖန်တီးပါ။"
navbar:
  search:
    placeholder: "ပြောင်းလဲကိရိယာ ရှာပါ..."
  sponsor: "ကျွန်ုပ်တို့အား ကော်ဖီ ဝယ်ပေးပါ"
  extension: "တိုးချဲ့ကိရိယာ"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "ဒေတာ အရင်းအမြစ်"
    placeholder: "သင်၏ %s ဒေတာကို ကူးထည့်ပါ သို့မဟုတ် %s ဖိုင်များကို ဤနေရာသို့ ဆွဲယူပါ"
    example: "ဥပမာ"
    upload: "ဖိုင် အပ်လုဒ်လုပ်ပါ"
    extract:
      enter: "ဝဘ်စာမျက်နှာမှ ထုတ်ယူပါ"
      intro: "ဖွဲ့စည်းထားသော ဒေတာကို အလိုအလျောက် ထုတ်ယူရန် ဇယားဒေတာ ပါရှိသော ဝဘ်စာမျက်နှာ URL ကို ထည့်သွင်းပါ"
      btn: "%s ကို ထုတ်ယူပါ"
    excel:
      sheet: "အလုပ်စာရွက်"
      none: "မရှိပါ"
  tableEditor:
    title: "အွန်လိုင်း ဇယား တည်းဖြတ်ကိရိယာ"
    undo: "ပြန်ဖြေရှင်းပါ"
    redo: "ပြန်လုပ်ပါ"
    transpose: "ပြောင်းလဲပါ"
    clear: "ရှင်းလင်းပါ"
    deleteBlank: "အလွတ်များကို ဖျက်ပါ"
    deleteDuplicate: "ပွားများကို ဖျက်ပါ"
    uppercase: "အကြီးစာလုံးများ"
    lowercase: "အသေးစာလုံးများ"
    capitalize: "ပထမစာလုံး အကြီး"
    replace:
      replace: "ရှာပြီး အစားထိုးပါ (Regex ပံ့ပိုးမှု)"
      subst: "ဤဖြင့် အစားထိုးပါ..."
      btn: "အားလုံး အစားထိုးပါ"
  tableGenerator:
    title: "ဇယား ဂျင်နရေတာ"
    sponsor: "ကျွန်ုပ်တို့အား ကော်ဖီ ဝယ်ပေးပါ"
    copy: "ကလစ်ဘုတ်သို့ ကူးပါ"
    download: "ဖိုင် ဒေါင်းလုဒ်လုပ်ပါ"
    tooltip:
      html:
        escape: "ပြသမှုအမှားများကို ကာကွယ်ရန် HTML အထူးအက္ခရာများ (&, <, >, \", ') ကို escape လုပ်ပါ"
        div: "ရိုးရာ TABLE tags များအစား DIV+CSS layout ကို အသုံးပြုပါ၊ responsive design အတွက် ပိုမိုသင့်တော်သည်"
        minify: "ချုံ့ထားသော HTML ကုဒ်ကို ထုတ်လုပ်ရန် whitespace နှင့် line breaks များကို ဖယ်ရှားပါ"
        thead: "စံ table head (&lt;thead&gt;) နှင့် body (&lt;tbody&gt;) ဖွဲ့စည်းပုံကို ထုတ်လုပ်ပါ"
        tableCaption: "ဇယား၏အပေါ်တွင် ဖော်ပြချက်ခေါင်းစဉ်ကို ထည့်ပါ (&lt;caption&gt; element)"
        tableClass: "လွယ်ကူသော style customization အတွက် ဇယားတွင် CSS class အမည်ကို ထည့်ပါ"
        tableId: "JavaScript manipulation အတွက် ဇယားအတွက် ထူးခြားသော ID identifier ကို သတ်မှတ်ပါ"
      jira:
        escape: "Jira table syntax နှင့် ပဋိပက္ခများကို ရှောင်ရှားရန် pipe characters (|) ကို escape လုပ်ပါ"
      json:
        parsingJSON: "ဆဲလ်များရှိ JSON strings များကို objects များအဖြစ် ဉာဏ်ရည်ဖြင့် parse လုပ်ပါ"
        minify: "ဖိုင်အရွယ်အစားကို လျှော့ချရန် ကျစ်လစ်သော single-line JSON format ကို ထုတ်လုပ်ပါ"
        format: "output JSON data structure ကို ရွေးချယ်ပါ: object array၊ 2D array၊ စသည်"
      latex:
        escape: "သင့်လျော်သော compilation ကို သေချာစေရန် LaTeX အထူးအက္ခရာများ (%, &, _, #, $၊ စသည်) ကို escape လုပ်ပါ"
        ht: "စာမျက်နှာပေါ်တွင် ဇယားအနေအထားကို ထိန်းချုပ်ရန် floating position parameter [!ht] ကို ထည့်ပါ"
        mwe: "ပြီးစုံသော LaTeX document ကို ထုတ်လုပ်ပါ"
        tableAlign: "စာမျက်နှာပေါ်တွင် ဇယား၏ အလျားလိုက်ချိန်ညှိမှုကို သတ်မှတ်ပါ"
        tableBorder: "ဇယား border style ကို configure လုပ်ပါ: border မရှိ၊ တစ်စိတ်တစ်ပိုင်း border၊ အပြည့်အစုံ border"
        label: "\\ref{} command cross-referencing အတွက် ဇယား label ကို သတ်မှတ်ပါ"
        caption: "ဇယား၏အပေါ် သို့မဟုတ် အောက်တွင် ပြသရန် ဇယား caption ကို သတ်မှတ်ပါ"
        location: "ဇယား caption ပြသမှုအနေအထားကို ရွေးချယ်ပါ: အပေါ် သို့မဟုတ် အောက်"
        tableType: "ဇယား environment အမျိုးအစားကို ရွေးချယ်ပါ: tabular၊ longtable၊ array၊ စသည်"
      markdown:
        escape: "format ပဋိပက္ခများကို ရှောင်ရှားရန် Markdown အထူးအက္ခရာများ (*, _, |, \\၊ စသည်) ကို escape လုပ်ပါ"
        pretty: "ပိုမိုလှပသော ဇယား format ကို ထုတ်လုပ်ရန် column widths များကို auto-align လုပ်ပါ"
        simple: "ပြင်ပ border vertical lines များကို ချန်လှပ်ထားပြီး ရိုးရှင်းသော syntax ကို အသုံးပြုပါ"
        boldFirstRow: "ပထမတန်း၏ စာသားကို bold လုပ်ပါ"
        boldFirstColumn: "ပထမကော်လံ၏ စာသားကို bold လုပ်ပါ"
        firstHeader: "ပထမတန်းကို header အဖြစ် သတ်မှတ်ပြီး separator line ကို ထည့်ပါ"
        textAlign: "ကော်လံစာသားချိန်ညှိမှုကို သတ်မှတ်ပါ: ဘယ်၊ အလယ်၊ ညာ"
        multilineHandling: "Multiline စာသားကိုင်တွယ်မှု: line breaks များကို ထိန်းသိမ်းပါ၊ \\n သို့ escape လုပ်ပါ၊ &lt;br&gt; tags များကို အသုံးပြုပါ"

        includeLineNumbers: "ဇယား၏ဘယ်ဘက်တွင် line number ကော်လံကို ထည့်ပါ"
      magic:
        builtin: "ကြိုတင်သတ်မှတ်ထားသော ဘုံ template formats များကို ရွေးချယ်ပါ"
        rowsTpl: "<table> <tr> <th>Magic Syntax</th> <th>ဖော်ပြချက်</th> <th>JS Methods ပံ့ပိုးမှု</th> </tr> <tr> <td>{h1} {h2} ...</td> <td><b>ခေါင်းစဉ်</b>၏ ပထမ၊ ဒုတိယ ... field၊ ဆိုလိုသည်မှာ {hA} {hB} ...</td> <td>String methods</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>လက်ရှိတန်း၏ ပထမ၊ ဒုတိယ ... field၊ ဆိုလိုသည်မှာ {$A} {$B} ...</td> <td>String methods</td> </tr> <tr> <td>{F,} {F;}</td> <td><b>F</b> နောက်ရှိ string ဖြင့် လက်ရှိတန်းကို ခွဲပါ</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>လက်ရှိ <b>တန်း</b>၏ လိုင်း <b>နံပါတ်</b> ၁ သို့မဟုတ် ၁၀၀ မှ</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>တန်းများ</b>၏ <b>နောက်ဆုံး</b> လိုင်း <b>နံပါတ်</b> </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>JavaScript ကုဒ်ကို <b>လုပ်ဆောင်</b>ပါ၊ ဥပမာ: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> braces {...} ကို output လုပ်ရန် backslash <b>\\</b> ကို အသုံးပြုပါ </td> <td></td> </tr></table>"
        headerTpl: "header section အတွက် စိတ်ကြိုက် output template"
        footerTpl: "footer section အတွက် စိတ်ကြိုက် output template"
      textile:
        escape: "ပုံစံ ပဋိပက္ခများကို ရှောင်ရှားရန် Textile syntax အက္ခရာများ (|, ., -, ^) ကို escape လုပ်ပါ"
        rowHeader: "ပထမတန်းကို header တန်းအဖြစ် သတ်မှတ်ပါ"
        thead: "ဇယား head နှင့် body အတွက် Textile syntax markers များကို ထည့်ပါ"
      xml:
        escape: "တရားဝင် XML ကို သေချာစေရန် XML အထူးအက္ခရာများ (&lt;, &gt;, &amp;, \", ') ကို escape လုပ်ပါ"
        minify: "အပိုဆောင်း whitespace ကို ဖယ်ရှားပြီး ချုံ့ထားသော XML output ကို ထုတ်လုပ်ပါ"
        rootElement: "XML root element tag အမည်ကို သတ်မှတ်ပါ"
        rowElement: "ဒေတာ၏ တန်းတစ်ခုစီအတွက် XML element tag အမည်ကို သတ်မှတ်ပါ"
        declaration: "XML declaration header ကို ထည့်ပါ (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "child elements များအစား XML attributes များအဖြစ် ဒေတာကို output လုပ်ပါ"
        cdata: "အထူးအက္ခရာများကို ကာကွယ်ရန် စာသား အကြောင်းအရာကို CDATA ဖြင့် ဖုံးပါ"
        encoding: "XML document အတွက် အက္ခရာ encoding format ကို သတ်မှတ်ပါ"
        indentation: "XML indentation အက္ခရာကို ရွေးချယ်ပါ: spaces သို့မဟုတ် tabs"
      yaml:
        indentSize: "YAML hierarchy indentation အတွက် spaces အရေအတွက်ကို သတ်မှတ်ပါ (များသောအားဖြင့် 2 သို့မဟုတ် 4)"
        arrayStyle: "Array format: block (တစ်လိုင်းတွင် တစ်ခု) သို့မဟုတ် flow (inline format)"
        quotationStyle: "String quote style: quotes မရှိ၊ single quotes၊ double quotes"
      pdf:
        theme: "ပရော်ဖက်ရှင်နယ် စာရွက်စာတမ်းများအတွက် PDF ဇယား visual style ကို ရွေးချယ်ပါ"
        headerColor: "PDF ဇယား header နောက်ခံအရောင်ကို ရွေးချယ်ပါ"
        showHead: "PDF စာမျက်နှာများတွင် header ပြသမှုကို ထိန်းချုပ်ပါ"
        docTitle: "PDF စာရွက်စာတမ်းအတွက် ရွေးချယ်နိုင်သော ခေါင်းစဉ်"
        docDescription: "PDF စာရွက်စာတမ်းအတွက် ရွေးချယ်နိုင်သော ဖော်ပြချက် စာသား"
      csv:
        bom: "Excel နှင့် အခြားဆော့ဖ်ဝဲများကို encoding ကို အသိအမှတ်ပြုစေရန် UTF-8 byte order mark ကို ထည့်ပါ"
      excel:
        autoWidth: "အကြောင်းအရာအပေါ် အခြေခံပြီး ကော်လံအကျယ်ကို အလိုအလျောက် ချိန်ညှိပါ"
        protectSheet: "password ဖြင့် worksheet ကာကွယ်မှုကို ဖွင့်ပါ: tableconvert.com"
      sql:
        primaryKey: "CREATE TABLE statement အတွက် primary key field အမည်ကို သတ်မှတ်ပါ"
        dialect: "database အမျိုးအစားကို ရွေးချယ်ပါ၊ quote နှင့် data type syntax ကို သက်ရောက်မှုရှိသည်"
      ascii:
        forceSep: "ဒေတာ၏ တန်းတစ်ခုစီကြားတွင် separator lines များကို အတင်းအကြပ် ထည့်ပါ"
        style: "ASCII ဇယား border ဆွဲခြင်း style ကို ရွေးချယ်ပါ"
        comment: "ဇယားတစ်ခုလုံးကို ဖုံးရန် comment markers များကို ထည့်ပါ"
      mediawiki:
        minify: "အပိုဆောင်း whitespace ကို ဖယ်ရှားပြီး output code ကို ချုံ့ပါ"
        header: "ပထမတန်းကို header style အဖြစ် မှတ်သားပါ"
        sort: "ဇယား click sorting လုပ်ဆောင်ချက်ကို ဖွင့်ပါ"
      asciidoc:
        minify: "AsciiDoc format output ကို ချုံ့ပါ"
        firstHeader: "ပထမတန်းကို header တန်းအဖြစ် သတ်မှတ်ပါ"
        lastFooter: "နောက်ဆုံးတန်းကို footer တန်းအဖြစ် သတ်မှတ်ပါ"
        title: "ဇယားတွင် ခေါင်းစဉ်စာသားကို ထည့်ပါ"
      tracwiki:
        rowHeader: "ပထမတန်းကို header အဖြစ် သတ်မှတ်ပါ"
        colHeader: "ပထမကော်လံကို header အဖြစ် သတ်မှတ်ပါ"
      bbcode:
        minify: "BBCode output format ကို ချုံ့ပါ"
      restructuredtext:
        style: "reStructuredText ဇယား border style ကို ရွေးချယ်ပါ"
        forceSep: "separator lines များကို အတင်းအကြပ် ထည့်ပါ"
    label:
      ascii:
        forceSep: "တန်း ခွဲခြားကိရိယာများ"
        style: "Border Style"
        comment: "မှတ်ချက် ဖုံးကိရိယာ"
      restructuredtext:
        style: "Border Style"
        forceSep: "ခွဲခြားကိရိယာများ အတင်းအကြပ်"
      bbcode:
        minify: "Output ကို ချုံ့ပါ"
      csv:
        doubleQuote: "Double Quote ဖုံးခြင်း"
        delimiter: "Field ခွဲခြားကိရိယာ"
        bom: "UTF-8 BOM"
        valueDelimiter: "တန်ဖိုး ခွဲခြားကိရိယာ"
        rowDelimiter: "တန်း ခွဲခြားကိရိယာ"
        prefix: "တန်း ရှေ့ဆက်"
        suffix: "တန်း နောက်ဆက်"
      excel:
        autoWidth: "အလိုအလျောက် အကျယ်"
        textFormat: "စာသား ပုံစံ"
        protectSheet: "Sheet ကာကွယ်ပါ"
        boldFirstRow: "ပထမတန်း ထင်ရှားစေပါ"
        boldFirstColumn: "ပထမကော်လံ ထင်ရှားစေပါ"
        sheetName: "Sheet အမည်"
      html:
        escape: "HTML အက္ခရာများကို Escape လုပ်ပါ"
        div: "DIV ဇယား"
        minify: "ကုဒ် ချုံ့ပါ"
        thead: "ဇယား Head ဖွဲ့စည်းပုံ"
        tableCaption: "ဇယား ခေါင်းစဉ်"
        tableClass: "ဇယား Class"
        tableId: "ဇယား ID"
        rowHeader: "တန်း Header"
        colHeader: "ကော်လံ Header"
      jira:
        escape: "အက္ခရာများကို Escape လုပ်ပါ"
        rowHeader: "တန်း Header"
        colHeader: "ကော်လံ Header"
      json:
        parsingJSON: "JSON ကို Parse လုပ်ပါ"
        minify: "Output ကို ချုံ့ပါ"
        format: "ဒေတာ ပုံစံ"
        rootName: "Root Object အမည်"
        indentSize: "Indent အရွယ်အစား"
      jsonlines:
        parsingJSON: "JSON ကို Parse လုပ်ပါ"
        format: "ဒေတာ ပုံစံ"
      latex:
        escape: "LaTeX ဇယား အက္ခရာများကို Escape လုပ်ပါ"
        ht: "Float အနေအထား"
        mwe: "ပြီးစုံသော စာရွက်စာတမ်း"
        tableAlign: "ဇယား ချိန်ညှိမှု"
        tableBorder: "Border Style"
        label: "ရည်ညွှန်း Label"
        caption: "ဇယား ခေါင်းစဉ်"
        location: "ခေါင်းစဉ် အနေအထား"
        tableType: "ဇယား အမျိုးအစား"
        boldFirstRow: "ပထမတန်း ထင်ရှားစေပါ"
        boldFirstColumn: "ပထမကော်လံ ထင်ရှားစေပါ"
        textAlign: "စာသား ချိန်ညှိမှု"
        borders: "Border ဆက်တင်များ"
      markdown:
        escape: "အက္ခရာများကို Escape လုပ်ပါ"
        pretty: "လှပသော Markdown ဇယား"
        simple: "ရိုးရှင်းသော Markdown ပုံစံ"
        boldFirstRow: "ပထမတန်း ထင်ရှားစေပါ"
        boldFirstColumn: "ပထမကော်လံ ထင်ရှားစေပါ"
        firstHeader: "ပထမ Header"
        textAlign: "စာသား ချိန်ညှိမှု"
        multilineHandling: "Multiline ကိုင်တွယ်မှု"

        includeLineNumbers: "လိုင်း နံပါတ်များ ထည့်ပါ"
        align: "ချိန်ညှိမှု"
      mediawiki:
        minify: "ကုဒ် ချုံ့ပါ"
        header: "Header Markup"
        sort: "စီရီလုပ်နိုင်သော"
      asciidoc:
        minify: "ပုံစံ ချုံ့ပါ"
        firstHeader: "ပထမ Header"
        lastFooter: "နောက်ဆုံး Footer"
        title: "ဇယား ခေါင်းစဉ်"
      tracwiki:
        rowHeader: "တန်း Header"
        colHeader: "ကော်လံ Header"
      sql:
        drop: "ဇယား ဖျက်ပါ (ရှိလျှင်)"
        create: "ဇယား ဖန်တီးပါ"
        oneInsert: "Batch Insert"
        table: "ဇယား အမည်"
        dialect: "Database အမျိုးအစား"
        primaryKey: "Primary Key"
      magic:
        builtin: "အတွင်းပါ Template"
        rowsTpl: "တန်း Template, Syntax ->"
        headerTpl: "Header Template"
        footerTpl: "Footer Template"
      textile:
        escape: "အက္ခရာများကို Escape လုပ်ပါ"
        rowHeader: "တန်း Header"
        thead: "ဇယား Head Syntax"
      xml:
        escape: "XML အက္ခရာများကို Escape လုပ်ပါ"
        minify: "Output ကို ချုံ့ပါ"
        rootElement: "Root Element"
        rowElement: "တန်း Element"
        declaration: "XML ကြေညာချက်"
        attributes: "Attribute Mode"
        cdata: "CDATA ဖုံးကိရိယာ"
        encoding: "Encoding"
        indentSize: "Indent အရွယ်အစား"
      yaml:
        indentSize: "Indent အရွယ်အစား"
        arrayStyle: "Array Style"
        quotationStyle: "Quote Style"
      pdf:
        theme: "PDF ဇယား Theme"
        headerColor: "PDF Header အရောင်"
        showHead: "PDF Header ပြသမှု"
        docTitle: "PDF စာရွက်စာတမ်း ခေါင်းစဉ်"
        docDescription: "PDF စာရွက်စာတမ်း ဖော်ပြချက်"
sidebar:
  all: "ပြောင်းလဲခြင်း ကိရိယာများ အားလုံး"
  dataSource:
    title: "ဒေတာ အရင်းအမြစ်"
    description:
      converter: "%s ကို %s သို့ ပြောင်းလဲရန် ထည့်သွင်းပါ။ ဖိုင် အပ်လုဒ်လုပ်ခြင်း၊ အွန်လိုင်း တည်းဖြတ်ခြင်း နှင့် ဝဘ် ဒေတာ ထုတ်ယူခြင်းကို ပံ့ပိုးပေးသည်။"
      generator: "လက်ဖြင့် ထည့်သွင်းခြင်း၊ ဖိုင် ထည့်သွင်းခြင်း နှင့် template ထုတ်လုပ်ခြင်း အပါအဝင် ထည့်သွင်းမှု နည်းလမ်းများစွာ၏ ပံ့ပိုးမှုဖြင့် ဇယား ဒေတာကို ဖန်တီးပါ။"
  tableEditor:
    title: "အွန်လိုင်း ဇယား တည်းဖြတ်ကိရိယာ"
    description:
      converter: "ကျွန်ုပ်တို့၏ ဇယား တည်းဖြတ်ကိရိယာကို အသုံးပြုပြီး %s ကို အွန်လိုင်းတွင် လုပ်ဆောင်ပါ။ အလွတ် တန်းများ ဖျက်ခြင်း၊ ပွားများ ဖယ်ရှားခြင်း၊ စီရီခြင်း နှင့် ရှာပြီး အစားထိုးခြင်းတို့၏ ပံ့ပိုးမှုဖြင့် Excel ကဲ့သို့ လုပ်ဆောင်မှု အတွေ့အကြုံ။"
      generator: "Excel ကဲ့သို့ လုပ်ဆောင်မှု အတွေ့အကြုံကို ပေးသော အားကောင်းသော အွန်လိုင်း ဇယား တည်းဖြတ်ကိရိယာ။ အလွတ် တန်းများ ဖျက်ခြင်း၊ ပွားများ ဖယ်ရှားခြင်း၊ စီရီခြင်း နှင့် ရှာပြီး အစားထိုးခြင်းကို ပံ့ပိုးပေးသည်။"
  tableGenerator:
    title: "ဇယား ဂျင်နရေတာ"
    description:
      converter: "ဇယား ဂျင်နရေတာ၏ အချိန်နှင့်တပြေးညီ ကြိုကြည့်ခြင်းဖြင့် %s ကို မြန်ဆန်စွာ ထုတ်လုပ်ပါ။ ကြွယ်ဝသော ထုတ်ပို့ ရွေးချယ်မှုများ၊ တစ်ကလစ် ကူးခြင်း နှင့် ဒေါင်းလုဒ်လုပ်ခြင်း။"
      generator: "မတူညီသော အသုံးပြုမှု အခြေအနေများကို ဖြည့်ဆည်းရန် %s ဒေတာကို ပုံစံများစွာဖြင့် ထုတ်ပို့ပါ။ စိတ်ကြိုက် ရွေးချယ်မှုများ နှင့် အချိန်နှင့်တပြေးညီ ကြိုကြည့်ခြင်းကို ပံ့ပိုးပေးသည်။"
footer:
  changelog: "ပြောင်းလဲမှု မှတ်တမ်း"
  sponsor: "ပံ့ပိုးသူများ"
  contact: "ကျွန်ုပ်တို့ကို ဆက်သွယ်ပါ"
  privacyPolicy: "ကိုယ်ရေးကိုယ်တာ မူဝါဒ"
  about: "အကြောင်း"
  resources: "အရင်းအမြစ်များ"
  popularConverters: "လူကြိုက်များသော ပြောင်းလဲကိရိယာများ"
  popularGenerators: "လူကြိုက်များသော ဂျင်နရေတာများ"
  dataSecurity: "သင်၏ ဒေတာ လုံခြုံပါသည် - ပြောင်းလဲမှုအားလုံး သင်၏ ဘရောက်ဇာတွင် လုပ်ဆောင်သည်."
converters:
  Markdown:
    alias: "Markdown ဇယား"
    what: "Markdown သည် နည်းပညာ စာရွက်စာတမ်းများ၊ ဘလော့ဂ် အကြောင်းအရာ ဖန်တီးမှု နှင့် ဝဘ် ဖွံ့ဖြိုးတိုးတက်မှုအတွက် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသော ပေါ့ပါးသော markup language တစ်ခုဖြစ်သည်။ ၎င်း၏ ဇယား syntax သည် ကျစ်လစ်ပြီး အလိုအလျောက် နားလည်နိုင်သော်လည်း စာသား ချိန်ညှိမှု၊ လင့်ခ် ထည့်သွင်းမှု နှင့် ပုံစံပြင်ဆင်မှုကို ပံ့ပိုးပေးသည်။ ၎င်းသည် ပရိုဂရမ်မာများ နှင့် နည်းပညာ စာရေးဆရာများအတွက် နှစ်သက်ရွေးချယ်သော ကိရိယာဖြစ်ပြီး GitHub၊ GitLab နှင့် အခြား ကုဒ် hosting platform များနှင့် ပြီးပြည့်စုံစွာ တွဲဖက်အသုံးပြုနိုင်သည်။"
    step1: "Markdown ဇယား ဒေတာကို ဒေတာ အရင်းအမြစ် ဧရိယာတွင် ကူးထည့်ပါ သို့မဟုတ် .md ဖိုင်များကို အပ်လုဒ်လုပ်ရန် တိုက်ရိုက် ဆွဲယူပါ။ ကိရိယာသည် ဇယား ဖွဲ့စည်းပုံ နှင့် ပုံစံပြင်ဆင်မှုကို အလိုအလျောက် ခွဲခြမ်းစိတ်ဖြာပြီး ရှုပ်ထွေးသော nested အကြောင်းအရာ နှင့် အထူး အက္ခရာ ကိုင်တွယ်မှုကို ပံ့ပိုးပေးသည်။"
    step3: "အချိန်နှင့်တပြေးညီ စံ Markdown ဇယား ကုဒ်ကို ထုတ်လုပ်ပါ၊ ချိန်ညှိမှု နည်းလမ်းများစွာ၊ စာသား ထင်ရှားစေခြင်း၊ လိုင်း နံပါတ် ထည့်ခြင်း နှင့် အခြား အဆင့်မြင့် ပုံစံ ဆက်တင်များကို ပံ့ပိုးပေးသည်။ ထုတ်လုပ်ထားသော ကုဒ်သည် GitHub နှင့် အဓိက Markdown တည်းဖြတ်ကိရိယာများနှင့် လုံးဝ တွဲဖက်အသုံးပြုနိုင်ပြီး တစ်ကလစ် ကူးခြင်းဖြင့် အသုံးပြုရန် အဆင်သင့်ဖြစ်သည်။"
    from_alias: "Markdown ဇယား ဖိုင်"
    to_alias: "Markdown ဇယား ပုံစံ"
  Magic:
    alias: "စိတ်ကြိုက် Template"
    what: "Magic template သည် ဤကိရိယာ၏ ထူးခြားသော အဆင့်မြင့် ဒေတာ ဂျင်နရေတာတစ်ခုဖြစ်ပြီး အသုံးပြုသူများကို စိတ်ကြိုက် template syntax မှတစ်ဆင့် မည်သည့် ပုံစံမဆို ဒေတာ ထုတ်လုပ်မှုကို ဖန်တီးနိုင်စေသည်။ variable အစားထိုးမှု၊ အခြေအနေ ဆုံးဖြတ်ချက် နှင့် loop လုပ်ဆောင်မှုကို ပံ့ပိုးပေးသည်။ ၎င်းသည် ရှုပ်ထွေးသော ဒေတာ ပြောင်းလဲမှု လိုအပ်ချက်များ နှင့် ကိုယ်ပိုင် ထုတ်လုပ်မှု ပုံစံများကို ကိုင်တွယ်ရန်အတွက် နောက်ဆုံး ဖြေရှင်းချက်ဖြစ်ပြီး အထူးသဖြင့် ဖွံ့ဖြိုးတိုးတက်ရေးသမားများ နှင့် ဒေတာ အင်ဂျင်နီယာများအတွက် သင့်လျော်သည်။"
    step1: "အတွင်းပါ ဘုံ template များကို ရွေးချယ်ပါ သို့မဟုတ် စိတ်ကြိုက် template syntax ကို ဖန်တီးပါ။ ရှုပ်ထွေးသော ဒေတာ ဖွဲ့စည်းပုံများ နှင့် လုပ်ငန်း logic ကို ကိုင်တွယ်နိုင်သော ကြွယ်ဝသော variable များ နှင့် function များကို ပံ့ပိုးပေးသည်။"
    step3: "စိတ်ကြိုက် ပုံစံ လိုအပ်ချက်များကို လုံးဝ ဖြည့်ဆည်းပေးသော ဒေတာ ထုတ်လုပ်မှုကို ထုတ်လုပ်ပါ။ ရှုပ်ထွေးသော ဒေတာ ပြောင်းလဲမှု logic နှင့် အခြေအနေ လုပ်ဆောင်မှုကို ပံ့ပိုးပေးပြီး ဒေတာ လုပ်ဆောင်မှု ထိရောက်မှု နှင့် ထုတ်လုပ်မှု အရည်အသွေးကို များစွာ တိုးတက်စေသည်။ အစုလိုက် ဒေတာ လုပ်ဆောင်မှုအတွက် အားကောင်းသော ကိရိယာတစ်ခုဖြစ်သည်။"
    from_alias: "ဇယား ဒေတာ"
    to_alias: "စိတ်ကြိုက် ပုံစံ ထုတ်လုပ်မှု"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) သည် အကျယ်ပြန့်ဆုံး အသုံးပြုသော ဒေတာ လဲလှယ်မှု ပုံစံဖြစ်ပြီး Excel၊ Google Sheets၊ database စနစ်များ နှင့် ဒေတာ ခွဲခြမ်းစိတ်ဖြာမှု ကိရိယာများစွာမှ ပြီးပြည့်စုံစွာ ပံ့ပိုးပေးသည်။ ၎င်း၏ ရိုးရှင်းသော ဖွဲ့စည်းပုံ နှင့် ခိုင်မာသော တွဲဖက်အသုံးပြုနိုင်မှုက ၎င်းကို ဒေတာ ရွှေ့ပြောင်းမှု၊ အစုလိုက် ထည့်သွင်းမှု/ထုတ်ပို့မှု နှင့် platform များကြား ဒေတာ လဲလှယ်မှုအတွက် စံပုံစံ ဖြစ်စေပြီး လုပ်ငန်း ခွဲခြမ်းစိတ်ဖြာမှု၊ ဒေတာ သိပ္ပံ နှင့် စနစ် ပေါင်းစပ်မှုတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။"
    step1: "CSV ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် CSV ဒေတာကို တိုက်ရိုက် ကူးထည့်ပါ။ ကိရိယာသည် ခွဲခြားကိရိယာများစွာ (ကော်မာ၊ tab၊ semicolon၊ pipe၊ စသည်) ကို ဉာဏ်ရည်ဖြင့် အသိအမှတ်ပြုပြီး ဒေတာ အမျိုးအစားများ နှင့် encoding ပုံစံများကို အလိုအလျောက် ရှာဖွေတွေ့ရှိကာ ကြီးမားသော ဖိုင်များ နှင့် ရှုပ်ထွေးသော ဒေတာ ဖွဲ့စည်းပုံများ၏ မြန်ဆန်သော ခွဲခြမ်းစိတ်ဖြာမှုကို ပံ့ပိုးပေးသည်။"
    step3: "စိတ်ကြိုက် ခွဲခြားကိရိယာများ၊ quote စတိုင်များ၊ encoding ပုံစံများ နှင့် BOM mark ဆက်တင်များ၏ ပံ့ပိုးမှုဖြင့် စံ CSV ပုံစံ ဖိုင်များကို ထုတ်လုပ်ပါ။ ပစ်မှတ် စနစ်များနှင့် ပြီးပြည့်စုံသော တွဲဖက်အသုံးပြုနိုင်မှုကို သေချာစေပြီး enterprise အဆင့် ဒေတာ လုပ်ဆောင်မှု လိုအပ်ချက်များကို ဖြည့်ဆည်းရန် ဒေါင်းလုဒ် နှင့် ချုံ့ခြင်း ရွေးချယ်မှုများကို ပေးပါသည်။"
    from_alias: "CSV ဒေတာ ဖိုင်"
    to_alias: "CSV စံ ပုံစံ"
  JSON:
    alias: "JSON Array"
    what: "JSON (JavaScript Object Notation) သည် ခေတ်သစ် ဝဘ် အပလီကေးရှင်းများ၊ REST API များ နှင့် microservice architecture များအတွက် စံ ဇယား ဒေတာ ပုံစံဖြစ်သည်။ ၎င်း၏ ရှင်းလင်းသော ဖွဲ့စည်းပုံ နှင့် ထိရောက်သော ခွဲခြမ်းစိတ်ဖြာမှုက ၎င်းကို front-end နှင့် back-end ဒေတာ အပြန်အလှန်လုပ်ဆောင်မှု၊ configuration ဖိုင် သိမ်းဆည်းမှု နှင့် NoSQL database များတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုစေသည်။ nested object များ၊ array ဖွဲ့စည်းပုံများ နှင့် ဒေတာ အမျိုးအစားများစွာကို ပံ့ပိုးပေးပြီး ခေတ်သစ် ဆော့ဖ်ဝဲ ဖွံ့ဖြိုးတိုးတက်မှုအတွက် မရှိမဖြစ် လိုအပ်သော ဇယား ဒေတာ ဖြစ်စေသည်။"
    step1: "JSON ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် JSON array များကို ကူးထည့်ပါ။ object array များ၊ nested ဖွဲ့စည်းပုံများ နှင့် ရှုပ်ထွေးသော ဒေတာ အမျိုးအစားများ၏ အလိုအလျောက် အသိအမှတ်ပြုမှု နှင့် ခွဲခြမ်းစိတ်ဖြာမှုကို ပံ့ပိုးပေးသည်။ ကိရိယာသည် JSON syntax ကို ဉာဏ်ရည်ဖြင့် အတည်ပြုပြီး အမှား သတိပေးချက်များကို ပေးပါသည်။"
    step3: "JSON ပုံစံ ထုတ်လုပ်မှုများစွာကို ထုတ်လုပ်ပါ: စံ object array များ၊ 2D array များ၊ column array များ နှင့် key-value pair ပုံစံများ။ လှပသော ထုတ်လုပ်မှု၊ ချုံ့ခြင်း mode၊ စိတ်ကြိုက် root object အမည်များ နှင့် indentation ဆက်တင်များကို ပံ့ပိုးပေးပြီး API interface များစွာ နှင့် ဒေတာ သိမ်းဆည်းမှု လိုအပ်ချက်များနှင့် ပြီးပြည့်စုံစွာ လိုက်လျောညီထွေ ဖြစ်စေသည်။"
    from_alias: "JSON Array ဖိုင်"
    to_alias: "JSON စံ ပုံစံ"
  JSONLines:
    alias: "JSONLines ပုံစံ"
    what: "JSON Lines (NDJSON ဟုလည်း လူသိများသည်) သည် big data လုပ်ဆောင်မှု နှင့် streaming ဒေတာ ပေးပို့မှုအတွက် အရေးကြီးသော ပုံစံတစ်ခုဖြစ်ပြီး လိုင်းတစ်ခုစီတွင် လွတ်လပ်သော JSON object တစ်ခုစီ ပါဝင်သည်။ log ခွဲခြမ်းစိတ်ဖြာမှု၊ ဒေတာ stream လုပ်ဆောင်မှု၊ machine learning နှင့် ဖြန့်ဝေထားသော စနစ်များတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။ တိုးတက်မှု လုပ်ဆောင်မှု နှင့် parallel computing ကို ပံ့ပိုးပေးပြီး ကြီးမားသော ဖွဲ့စည်းထားသော ဒေတာကို ကိုင်တွယ်ရန်အတွက် အကောင်းဆုံး ရွေးချယ်မှု ဖြစ်စေသည်။"
    step1: "JSONLines ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် ဒေတာကို ကူးထည့်ပါ။ ကိရိယာသည် JSON object များကို လိုင်းအလိုက် ခွဲခြမ်းစိတ်ဖြာပြီး ကြီးမားသော ဖိုင် streaming လုပ်ဆောင်မှု နှင့် အမှား လိုင်း ကျော်ခြင်း လုပ်ဆောင်ချက်ကို ပံ့ပိုးပေးသည်။"
    step3: "လိုင်းတစ်ခုစီတွင် ပြီးစုံသော JSON object တစ်ခုစီကို ထုတ်လုပ်သော စံ JSONLines ပုံစံကို ထုတ်လုပ်ပါ။ streaming လုပ်ဆောင်မှု၊ အစုလိုက် ထည့်သွင်းမှု နှင့် big data ခွဲခြမ်းစိတ်ဖြာမှု အခြေအနေများအတွက် သင့်လျော်ပြီး ဒေတာ အတည်ပြုမှု နှင့် ပုံစံ ပိုမိုကောင်းမွန်စေမှုကို ပံ့ပိုးပေးသည်။"
    from_alias: "JSONLines ဒေတာ"
    to_alias: "JSONLines Streaming ပုံစံ"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) သည် enterprise အဆင့် ဒေတာ လဲလှယ်မှု နှင့် configuration စီမံခန့်ခွဲမှုအတွက် စံပုံစံဖြစ်ပြီး တင်းကြပ်သော syntax သတ်မှတ်ချက်များ နှင့် အားကောင်းသော အတည်ပြုမှု ယန္တရားများ ရှိသည်။ ဝဘ် ဝန်ဆောင်မှုများ၊ configuration ဖိုင်များ၊ စာရွက်စာတမ်း သိမ်းဆည်းမှု နှင့် စနစ် ပေါင်းစပ်မှုတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။ namespace များ၊ schema အတည်ပြုမှု နှင့် XSLT ပြောင်းလဲမှုကို ပံ့ပိုးပေးပြီး enterprise အပလီကေးရှင်းများအတွက် အရေးကြီးသော ဇယား ဒေတာ ဖြစ်စေသည်။"
    step1: "XML ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် XML ဒေတာကို ကူးထည့်ပါ။ ကိရိယာသည် XML ဖွဲ့စည်းပုံကို အလိုအလျောက် ခွဲခြမ်းစိတ်ဖြာပြီး ဇယား ပုံစံသို့ ပြောင်းလဲကာ namespace၊ attribute ကိုင်တွယ်မှု နှင့် ရှုပ်ထွေးသော nested ဖွဲ့စည်းပုံများကို ပံ့ပိုးပေးသည်။"
    step3: "XML စံချိန်စံညွှန်းများနှင့် ကိုက်ညီသော XML ထုတ်လုပ်မှုကို ထုတ်လုပ်ပါ။ စိတ်ကြိုက် root element များ၊ တန်း element အမည်များ၊ attribute mode များ၊ CDATA ဖုံးခြင်း နှင့် အက္ခရာ encoding ဆက်တင်များကို ပံ့ပိုးပေးသည်။ ဒေတာ ခိုင်မာမှု နှင့် တွဲဖက်အသုံးပြုနိုင်မှုကို သေချာစေပြီး enterprise အဆင့် အပလီကေးရှင်း လိုအပ်ချက်များကို ဖြည့်ဆည်းပေးသည်။"
    from_alias: "XML ဒေတာ ဖိုင်"
    to_alias: "XML စံ ပုံစံ"
  YAML:
    alias: "YAML Configuration"
    what: "YAML သည် လူသားများအတွက် ဖော်ရွေသော ဒေတာ serialization စံချိန်စံညွှန်းတစ်ခုဖြစ်ပြီး ၎င်း၏ ရှင်းလင်းသော အဆင့်ဆင့် ဖွဲ့စည်းပုံ နှင့် ကျစ်လစ်သော syntax ကြောင့် ကျော်ကြားသည်။ configuration ဖိုင်များ၊ DevOps ကိရိယာ ကွင်းဆက်များ၊ Docker Compose နှင့် Kubernetes deployment တွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။ ၎င်း၏ ခိုင်မာသော ဖတ်ရှုနိုင်မှု နှင့် ကျစ်လစ်သော syntax က ၎င်းကို ခေတ်သစ် cloud-native အပလီကေးရှင်းများ နှင့် အလိုအလျောက် လုပ်ဆောင်မှုများအတွက် အရေးကြီးသော configuration ပုံစံ ဖြစ်စေသည်။"
    step1: "YAML ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် YAML ဒေတာကို ကူးထည့်ပါ။ ကိရိယာသည် YAML ဖွဲ့စည်းပုံကို ဉာဏ်ရည်ဖြင့် ခွဲခြမ်းစိတ်ဖြာပြီး syntax ၏ မှန်ကန်မှုကို အတည်ပြုကာ multi-document ပုံစံများ နှင့် ရှုပ်ထွေးသော ဒေတာ အမျိုးအစားများကို ပံ့ပိုးပေးသည်။"
    step3: "block နှင့် flow array စတိုင်များ၊ quote ဆက်တင်များစွာ၊ စိတ်ကြိုက် indentation နှင့် မှတ်ချက် ထိန်းသိမ်းမှု၏ ပံ့ပိုးမှုဖြင့် စံ YAML ပုံစံ ထုတ်လုပ်မှုကို ထုတ်လုပ်ပါ။ ထုတ်လုပ်ထားသော YAML ဖိုင်များသည် parser များစွာ နှင့် configuration စနစ်များနှင့် လုံးဝ တွဲဖက်အသုံးပြုနိုင်ကြောင်း သေချာစေသည်။"
    from_alias: "YAML Configuration ဖိုင်"
    to_alias: "YAML စံ ပုံစံ"
  MySQL:
      alias: "MySQL Query ရလဒ်များ"
      what: "MySQL သည် ကမ္ဘာ့အကျော်ကြားဆုံး open-source relational database စီမံခန့်ခွဲမှု စနစ်ဖြစ်ပြီး ၎င်း၏ မြင့်မားသော စွမ်းဆောင်ရည်၊ ယုံကြည်ရမှု နှင့် အသုံးပြုရလွယ်ကူမှုကြောင့် ကျော်ကြားသည်။ ဝဘ် အပလီကေးရှင်းများ၊ enterprise စနစ်များ နှင့် ဒေတာ ခွဲခြမ်းစိတ်ဖြာမှု platform များတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။ MySQL query ရလဒ်များတွင် များသောအားဖြင့် ဖွဲ့စည်းထားသော ဇယား ဒေတာများ ပါဝင်ပြီး database စီမံခန့်ခွဲမှု နှင့် ဒေတာ ခွဲခြမ်းစိတ်ဖြာမှု အလုပ်တွင် အရေးကြီးသော ဒေတာ အရင်းအမြစ်အဖြစ် ဆောင်ရွက်သည်။"
      step1: "MySQL query ထုတ်လုပ်မှု ရလဒ်များကို ဒေတာ အရင်းအမြစ် ဧရိယာတွင် ကူးထည့်ပါ။ ကိရိယာသည် MySQL command-line ထုတ်လုပ်မှု ပုံစံကို အလိုအလျောက် အသိအမှတ်ပြုပြီး ခွဲခြမ်းစိတ်ဖြာကာ query ရလဒ် စတိုင်များစွာ နှင့် အက္ခရာ encoding များကို ပံ့ပိုးပေးပြီး ခေါင်းစီးများ နှင့် ဒေတာ တန်းများကို ဉာဏ်ရည်ဖြင့် ကိုင်တွယ်သည်။"
      step3: "MySQL query ရလဒ်များကို ဇယား ဒေတာ ပုံစံများစွာသို့ မြန်ဆန်စွာ ပြောင်းလဲပြီး ဒေတာ ခွဲခြမ်းစိတ်ဖြာမှု၊ အစီရင်ခံစာ ပြုလုပ်မှု၊ စနစ်များကြား ဒေတာ ရွှေ့ပြောင်းမှု နှင့် ဒေတာ အတည်ပြုမှုကို လွယ်ကူစေသည်။ database စီမံခန့်ခွဲသူများ နှင့် ဒေတာ ခွဲခြမ်းစိတ်ဖြာသူများအတွက် လက်တွေ့ကျသော ကိရိယာတစ်ခုဖြစ်သည်။"
      from_alias: "MySQL Query ထုတ်လုပ်မှု"
      to_alias: "MySQL ဇယား ဒေတာ"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) သည် relational database များအတွက် စံ လုပ်ဆောင်မှု ဘာသာစကားဖြစ်ပြီး ဒေတာ query၊ insert၊ update နှင့် delete လုပ်ဆောင်မှုများအတွက် အသုံးပြုသည်။ database စီမံခန့်ခွဲမှု၏ အဓိက နည်းပညာအဖြစ် SQL သည် ဒေတာ ခွဲခြမ်းစိတ်ဖြာမှု၊ လုပ်ငန်း ဉာဏ်ရည်၊ ETL လုပ်ဆောင်မှု နှင့် ဒေတာ သိုလှောင်ရုံ တည်ဆောက်မှုတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။ ၎င်းသည် ဒေတာ ပရော်ဖက်ရှင်နယ်များအတွက် မရှိမဖြစ် လိုအပ်သော ကျွမ်းကျင်မှု ကိရိယာတစ်ခုဖြစ်သည်။"
    step1: "INSERT SQL statement များကို ကူးထည့်ပါ သို့မဟုတ် .sql ဖိုင်များကို အပ်လုဒ်လုပ်ပါ။ ကိရိယာသည် SQL syntax ကို ဉာဏ်ရည်ဖြင့် ခွဲခြမ်းစိတ်ဖြာပြီး ဇယား ဒေတာကို ထုတ်ယူကာ SQL dialect များစွာ နှင့် ရှုပ်ထွေးသော query statement လုပ်ဆောင်မှုကို ပံ့ပိုးပေးသည်။"
    step3: "စံ SQL INSERT statement များ နှင့် ဇယား ဖန်တီးမှု statement များကို ထုတ်လုပ်ပါ။ database dialect များစွာ (MySQL၊ PostgreSQL၊ SQLite၊ SQL Server၊ Oracle) ကို ပံ့ပိုးပေးပြီး ဒေတာ အမျိုးအစား mapping၊ အက္ခရာ escaping နှင့် primary key ကန့်သတ်ချက်များကို အလိုအလျောက် ကိုင်တွယ်သည်။ ထုတ်လုပ်ထားသော SQL ကုဒ်ကို တိုက်ရိုက် လုပ်ဆောင်နိုင်ကြောင်း သေချာစေသည်။"
    from_alias: "SQL ဒေတာ ဖိုင်"
    to_alias: "SQL စံ Statement"
  Qlik:
      alias: "Qlik ဇယား"
      what: "Qlik သည် ဒေတာ မြင်ယောင်ခြင်း၊ အမှုဆောင် dashboard များ နှင့် ကိုယ်တိုင်ဝန်ဆောင်မှု လုပ်ငန်း ဉာဏ်ရည် ထုတ်ကုန်များတွင် အထူးပြုသော ဆော့ဖ်ဝဲ ရောင်းချသူတစ်ခုဖြစ်ပြီး Tableau နှင့် Microsoft တို့နှင့်အတူ ရှိသည်။"
      step1: ""
      step3: "နောက်ဆုံးတွင် [ဇယား ဂျင်နရေတာ](#TableGenerator) သည် ပြောင်းလဲမှု ရလဒ်များကို ပြသည်။ သင်၏ Qlik Sense၊ Qlik AutoML၊ QlikView သို့မဟုတ် အခြား Qlik-enabled ဆော့ဖ်ဝဲများတွင် အသုံးပြုပါ။"
      from_alias: "Qlik ဇယား"
      to_alias: "Qlik ဇယား"
  DAX:
      alias: "DAX ဇယား"
      what: "DAX (Data Analysis Expressions) သည် Microsoft Power BI တစ်လျှောက်လုံးတွင် တွက်ချက်ထားသော ကော်လံများ၊ တိုင်းတာမှုများ နှင့် စိတ်ကြိုက် ဇယားများ ဖန်တီးရန်အတွက် အသုံးပြုသော ပရိုဂရမ်မင်း ဘာသာစကားတစ်ခုဖြစ်သည်။"
      step1: ""
      step3: "နောက်ဆုံးတွင် [ဇယား ဂျင်နရေတာ](#TableGenerator) သည် ပြောင်းလဲမှု ရလဒ်များကို ပြသည်။ မျှော်လင့်ထားသည့်အတိုင်း ၎င်းကို Microsoft Power BI၊ Microsoft Analysis Services နှင့် Excel အတွက် Microsoft Power Pivot အပါအဝင် Microsoft ထုတ်ကုန်များစွာတွင် အသုံးပြုသည်။"
      from_alias: "DAX ဇယား"
      to_alias: "DAX ဇယား"
  Firebase:
    alias: "Firebase စာရင်း"
    what: "Firebase သည် အချိန်နှင့်တပြေးညီ database၊ cloud သိမ်းဆည်းမှု၊ အထောက်အထား အတည်ပြုမှု၊ crash အစီရင်ခံမှု စသည့် hosted backend ဝန်ဆောင်မှုများကို ပေးသော BaaS အပလီကေးရှင်း ဖွံ့ဖြိုးတိုးတက်မှု platform တစ်ခုဖြစ်သည်။"
    step1: ""
    step3: "နောက်ဆုံးတွင် [ဇယား ဂျင်နရေတာ](#TableGenerator) သည် ပြောင်းလဲမှု ရလဒ်များကို ပြသည်။ ထို့နောက် Firebase database ရှိ ဒေတာ စာရင်းတစ်ခုသို့ ထည့်ရန် Firebase API ရှိ push method ကို အသုံးပြုနိုင်သည်။"
    from_alias: "Firebase စာရင်း"
    to_alias: "Firebase စာရင်း"
  HTML:
    alias: "HTML ဇယား"
    what: "HTML ဇယားများသည် ဝဘ်စာမျက်နှာများတွင် ဖွဲ့စည်းထားသော ဒေတာများကို ပြသရန် စံနည်းလမ်းဖြစ်ပြီး table, tr, td နှင့် အခြား tag များဖြင့် တည်ဆောက်ထားသည်။ ကြွယ်ဝသော စတိုင် စိတ်ကြိုက်ပြင်ဆင်မှု၊ တုံ့ပြန်မှု layout နှင့် အပြန်အလှန်လုပ်ဆောင်နိုင်သော လုပ်ဆောင်ချက်များကို ပံ့ပိုးပေးသည်။ ဝဘ်ဆိုဒ် ဖွံ့ဖြိုးတိုးတက်မှု၊ ဒေတာ ပြသမှု နှင့် အစီရင်ခံစာ ပြုလုပ်မှုတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုကြပြီး front-end ဖွံ့ဖြိုးတိုးတက်မှု နှင့် ဝဘ် ဒီဇိုင်း၏ အရေးကြီးသော အစိတ်အပိုင်းအဖြစ် ဆောင်ရွက်သည်။"
    step1: "ဇယားများပါဝင်သော HTML ကုဒ်ကို ကူးထည့်ပါ သို့မဟုတ် HTML ဖိုင်များကို အပ်လုဒ်လုပ်ပါ။ ကိရိယာသည် စာမျက်နှာများမှ ဇယားဒေတာကို အလိုအလျောက် အသိအမှတ်ပြုပြီး ထုတ်ယူသည်၊ ရှုပ်ထွေးသော HTML ဖွဲ့စည်းပုံများ၊ CSS စတိုင်များ နှင့် nested ဇယား လုပ်ဆောင်မှုကို ပံ့ပိုးပေးသည်။"
    step3: "thead/tbody ဖွဲ့စည်းပုံ၊ CSS class ဆက်တင်များ၊ ဇယား ခေါင်းစဉ်များ၊ တန်း/ကော်လံ ခေါင်းစီးများ နှင့် တုံ့ပြန်မှု attribute ဖွဲ့စည်းမှုကို ပံ့ပိုးပေးသော semantic HTML ဇယား ကုဒ်ကို ထုတ်လုပ်ပါ။ ထုတ်လုပ်ထားသော ဇယား ကုဒ်သည် ကောင်းမွန်သော ရယူနိုင်မှု နှင့် SEO ဖော်ရွေမှုဖြင့် ဝဘ် စံချိန်စံညွှန်းများနှင့် ကိုက်ညီကြောင်း သေချာစေသည်။"
    from_alias: "HTML ဝဘ် ဇယား"
    to_alias: "HTML စံ ဇယား"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel သည် ကမ္ဘာ့အကျော်ကြားဆုံး spreadsheet ဆော့ဖ်ဝဲဖြစ်ပြီး လုပ်ငန်း ခွဲခြမ်းစိတ်ဖြာမှု၊ ငွေကြေး စီမံခန့်ခွဲမှု၊ ဒေတာ လုပ်ဆောင်မှု နှင့် အစီရင်ခံစာ ပြုလုပ်မှုတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။ ၎င်း၏ အားကောင်းသော ဒေတာ လုပ်ဆောင်မှု စွမ်းရည်များ၊ ကြွယ်ဝသော function library နှင့် ပြောင်းလွယ်ပြင်လွယ် မြင်ယောင်ခြင်း လုပ်ဆောင်ချက်များက ၎င်းကို ရုံးခန်း အလိုအလျောက်စနစ် နှင့် ဒေတာ ခွဲခြမ်းစိတ်ဖြာမှုအတွက် စံကိရိယာ ဖြစ်စေပြီး လုပ်ငန်းနယ်ပယ် နှင့် နယ်ပယ် အားလုံးနီးပါးတွင် ကျယ်ပြန့်သော အသုံးချမှုများ ရှိသည်။"
    step1: "Excel ဖိုင်များကို အပ်လုဒ်လုပ်ပါ (.xlsx၊ .xls ပုံစံများကို ပံ့ပိုးပေးသည်) သို့မဟုတ် Excel မှ ဇယား ဒေတာကို တိုက်ရိုက် ကူးပြီး ကူးထည့်ပါ။ ကိရိယာသည် multi-worksheet လုပ်ဆောင်မှု၊ ရှုပ်ထွေးသော ပုံစံ အသိအမှတ်ပြုမှု နှင့် ကြီးမားသော ဖိုင်များ၏ မြန်ဆန်သော ခွဲခြမ်းစိတ်ဖြာမှုကို ပံ့ပိုးပေးပြီး ပေါင်းစပ်ထားသော ဆဲလ်များ နှင့် ဒေတာ အမျိုးအစားများကို အလိုအလျောက် ကိုင်တွယ်သည်။"
    step3: "Excel သို့ တိုက်ရိုက် ကူးထည့်နိုင်သော သို့မဟုတ် စံ .xlsx ဖိုင်များအဖြစ် ဒေါင်းလုဒ်လုပ်နိုင်သော Excel-compatible ဇယား ဒေတာကို ထုတ်လုပ်ပါ။ worksheet အမည်ပေးခြင်း၊ ဆဲလ် ပုံစံပြင်ဆင်ခြင်း၊ အလိုအလျောက် ကော်လံ အကျယ်၊ ခေါင်းစီး စတိုင်ပြင်ဆင်ခြင်း နှင့် ဒေတာ အတည်ပြုမှု ဆက်တင်များကို ပံ့ပိုးပေးသည်။ ထုတ်လုပ်ထားသော Excel ဖိုင်များသည် ပရော်ဖက်ရှင်နယ် အသွင်အပြင် နှင့် ပြီးစုံသော လုပ်ဆောင်ချက်များ ရှိကြောင်း သေချာစေသည်။"
    from_alias: "Excel Spreadsheet"
    to_alias: "Excel စံ ပုံစံ"
  LaTeX:
    alias: "LaTeX ဇယား"
    what: "LaTeX သည် ပရော်ဖက်ရှင်နယ် စာရွက်စာတမ်း စာလုံးပုံစံ ပြင်ဆင်မှု စနစ်တစ်ခုဖြစ်ပြီး အထူးသဖြင့် ပညာရေး စာတမ်းများ၊ နည်းပညာ စာရွက်စာတမ်းများ နှင့် သိပ္ပံ ထုတ်ဝေမှုများ ဖန်တီးရန်အတွက် သင့်လျော်သည်။ ၎င်း၏ ဇယား လုပ်ဆောင်ချက်သည် အားကောင်းပြီး ရှုပ်ထွေးသော သင်္ချာ ဖော်မြူလာများ၊ တိကျသော layout ထိန်းချုပ်မှု နှင့် အရည်အသွေးမြင့် PDF ထုတ်လုပ်မှုကို ပံ့ပိုးပေးသည်။ ၎င်းသည် ပညာရေး နှင့် သိပ္ပံ ထုတ်ဝေမှုတွင် စံကိရိယာဖြစ်ပြီး ဂျာနယ် စာတမ်းများ၊ ဘွဲ့လွန် စာတမ်းများ နှင့် နည်းပညာ လက်စွဲ စာလုံးပုံစံ ပြင်ဆင်မှုတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။"
    step1: "LaTeX ဇယား ကုဒ်ကို ကူးထည့်ပါ သို့မဟုတ် .tex ဖိုင်များကို အပ်လုဒ်လုပ်ပါ။ ကိရိယာသည် LaTeX ဇယား syntax ကို ခွဲခြမ်းစိတ်ဖြာပြီး ဒေတာ အကြောင်းအရာကို ထုတ်ယူကာ ဇယား environment များစွာ (tabular၊ longtable၊ array၊ စသည်) နှင့် ရှုပ်ထွေးသော ပုံစံ command များကို ပံ့ပိုးပေးသည်။"
    step3: "ဇယား environment ရွေးချယ်မှုများစွာ၊ ဘောင် စတိုင် ဖွဲ့စည်းမှု၊ ခေါင်းစဉ် အနေအထား ဆက်တင်များ၊ စာရွက်စာတမ်း class သတ်မှတ်ချက် နှင့် package စီမံခန့်ခွဲမှု၏ ပံ့ပိုးမှုဖြင့် ပရော်ဖက်ရှင်နယ် LaTeX ဇယား ကုဒ်ကို ထုတ်လုပ်ပါ။ ပြီးစုံသော compile လုပ်နိုင်သော LaTeX စာရွက်စာတမ်းများကို ထုတ်လုပ်နိုင်ပြီး ထုတ်လုပ်ထားသော ဇယားများသည် ပညာရေး ထုတ်ဝေမှု စံချိန်စံညွှန်းများနှင့် ကိုက်ညီကြောင်း သေချာစေသည်။"
    from_alias: "LaTeX စာရွက်စာတမ်း ဇယား"
    to_alias: "LaTeX ပရော်ဖက်ရှင်နယ် ပုံစံ"
  ASCII:
    alias: "ASCII ဇယား"
    what: "ASCII ဇယားများသည် ဇယား ဘောင်များ နှင့် ဖွဲ့စည်းပုံများကို ရေးဆွဲရန် ရိုးရှင်းသော စာသား အက္ခရာများကို အသုံးပြုပြီး အကောင်းဆုံး တွဲဖက်အသုံးပြုနိုင်မှု နှင့် သယ်ဆောင်နိုင်မှုကို ပေးပါသည်။ စာသား တည်းဖြတ်ကိရိယာများ၊ terminal ပတ်ဝန်းကျင်များ နှင့် operating system များအားလုံးနှင့် တွဲဖက်အသုံးပြုနိုင်သည်။ ကုဒ် စာရွက်စာတမ်းများ၊ နည်းပညာ လက်စွဲများ၊ README ဖိုင်များ နှင့် command-line ကိရိယာ ထုတ်လုပ်မှုတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။ ပရိုဂရမ်မာများ နှင့် စနစ် စီမံခန့်ခွဲသူများအတွက် နှစ်သက်ရွေးချယ်သော ဒေတာ ပြသမှု ပုံစံဖြစ်သည်။"
    step1: "ASCII ဇယားများပါဝင်သော စာသား ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် ဇယား ဒေတာကို တိုက်ရိုက် ကူးထည့်ပါ။ ကိရိယာသည် ASCII ဇယား ဖွဲ့စည်းပုံများကို ဉာဏ်ရည်ဖြင့် အသိအမှတ်ပြုပြီး ခွဲခြမ်းစိတ်ဖြာကာ ဘောင် စတိုင်များစွာ နှင့် ချိန်ညှိမှု ပုံစံများကို ပံ့ပိုးပေးသည်။"
    step3: "ဘောင် စတိုင်များစွာ (တစ်လိုင်း၊ နှစ်လိုင်း၊ ဝိုင်းထောင့်များ၊ စသည်)၊ စာသား ချိန်ညှိမှု နည်းလမ်းများ နှင့် အလိုအလျောက် ကော်လံ အကျယ်၏ ပံ့ပိုးမှုဖြင့် လှပသော ရိုးရှင်းသော စာသား ASCII ဇယားများကို ထုတ်လုပ်ပါ။ ထုတ်လုပ်ထားသော ဇယားများသည် ကုဒ် တည်းဖြတ်ကိရိယာများ၊ စာရွက်စာတမ်းများ နှင့် command line များတွင် ပြီးပြည့်စုံစွာ ပြသသည်။"
    from_alias: "ASCII စာသား ဇယား"
    to_alias: "ASCII စံ ပုံစံ"
  MediaWiki:
    alias: "MediaWiki ဇယား"
    what: "MediaWiki သည် Wikipedia ကဲ့သို့သော ကျော်ကြားသော wiki ဆိုဒ်များမှ အသုံးပြုသော open-source ဆော့ဖ်ဝဲ platform တစ်ခုဖြစ်သည်။ ၎င်း၏ ဇယား syntax သည် ကျစ်လစ်သော်လည်း အားကောင်းပြီး ဇယား စတိုင် စိတ်ကြိုက်ပြင်ဆင်မှု၊ အမျိုးအစား ခွဲခြမ်းမှု လုပ်ဆောင်ချက် နှင့် လင့်ခ် ထည့်သွင်းမှုကို ပံ့ပိုးပေးသည်။ အသိပညာ စီမံခန့်ခွဲမှု၊ ပူးပေါင်း တည်းဖြတ်မှု နှင့် အကြောင်းအရာ စီမံခန့်ခွဲမှု စနစ်များတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုကြပြီး wiki စွယ်စုံကျမ်းများ နှင့် အသိပညာ အခြေခံများ တည်ဆောက်ရန်အတွက် အဓိက နည်းပညာအဖြစ် ဆောင်ရွက်သည်။"
    step1: "MediaWiki ဇယား ကုဒ်ကို ကူးထည့်ပါ သို့မဟုတ် wiki အရင်းအမြစ် ဖိုင်များကို အပ်လုဒ်လုပ်ပါ။ ကိရိယာသည် wiki markup syntax ကို ခွဲခြမ်းစိတ်ဖြာပြီး ဇယား ဒေတာကို ထုတ်ယူကာ ရှုပ်ထွေးသော wiki syntax နှင့် template လုပ်ဆောင်မှုကို ပံ့ပိုးပေးသည်။"
    step3: "ခေါင်းစီး စတိုင် ဆက်တင်များ၊ ဆဲလ် ချိန်ညှိမှု၊ အမျိုးအစား ခွဲခြမ်းမှု လုပ်ဆောင်ချက် ဖွင့်ခြင်း နှင့် ကုဒ် ချုံ့ခြင်း ရွေးချယ်မှုများ၏ ပံ့ပိုးမှုဖြင့် စံ MediaWiki ဇယား ကုဒ်ကို ထုတ်လုပ်ပါ။ ထုတ်လုပ်ထားသော ကုဒ်ကို wiki စာမျက်နှာ တည်းဖြတ်မှုအတွက် တိုက်ရိုက် အသုံးပြုနိုင်ပြီး MediaWiki platform များတွင် ပြီးပြည့်စုံသော ပြသမှုကို သေချာစေသည်။"
    from_alias: "MediaWiki အရင်းအမြစ် ကုဒ်"
    to_alias: "MediaWiki ဇယား Syntax"
  TracWiki:
    alias: "TracWiki ဇယား"
    what: "Trac သည် ဇယား အကြောင်းအရာ ဖန်တီးရန် ရိုးရှင်းသော wiki syntax ကို အသုံးပြုသော ဝဘ်အခြေခံ ပရောဂျက် စီမံခန့်ခွဲမှု နှင့် bug ခြေရာခံမှု စနစ်တစ်ခုဖြစ်သည်။"
    step1: "TracWiki ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် ဇယား ဒေတာကို ကူးထည့်ပါ။"
    step3: "တန်း/ကော်လံ ခေါင်းစီး ဆက်တင်များ၏ ပံ့ပိုးမှုဖြင့် TracWiki-compatible ဇယား ကုဒ်ကို ထုတ်လုပ်ပြီး ပရောဂျက် စာရွက်စာတမ်း စီမံခန့်ခွဲမှုကို လွယ်ကူစေသည်။"
    from_alias: "TracWiki ဇယား"
    to_alias: "TracWiki ပုံစံ"
  AsciiDoc:
    alias: "AsciiDoc ဇယား"
    what: "AsciiDoc သည် HTML၊ PDF၊ လက်စွဲ စာမျက်နှာများ နှင့် အခြား ပုံစံများသို့ ပြောင်းလဲနိုင်သော ပေါ့ပါးသော markup language တစ်ခုဖြစ်ပြီး နည်းပညာ စာရွက်စာတမ်း ရေးသားမှုအတွက် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။"
    step1: "AsciiDoc ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် ဒေတာကို ကူးထည့်ပါ။"
    step3: "ခေါင်းစီး၊ ခြေစာ နှင့် ခေါင်းစဉ် ဆက်တင်များ၏ ပံ့ပိုးမှုဖြင့် AsciiDoc ဇယား syntax ကို ထုတ်လုပ်ပြီး AsciiDoc တည်းဖြတ်ကိရိယာများတွင် တိုက်ရိုက် အသုံးပြုနိုင်သည်။"
    from_alias: "AsciiDoc ဇယား"
    to_alias: "AsciiDoc ပုံစံ"
  reStructuredText:
    alias: "reStructuredText ဇယား"
    what: "reStructuredText သည် Python အသိုင်းအဝိုင်းအတွက် စံ စာရွက်စာတမ်း ပုံစံဖြစ်ပြီး ကြွယ်ဝသော ဇယား syntax ကို ပံ့ပိုးပေးကာ Sphinx စာရွက်စာတမ်း ထုတ်လုပ်မှုအတွက် အများအားဖြင့် အသုံးပြုသည်။"
    step1: ".rst ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် reStructuredText ဒေတာကို ကူးထည့်ပါ။"
    step3: "ဘောင် စတိုင်များစွာ၏ ပံ့ပိုးမှုဖြင့် စံ reStructuredText ဇယားများကို ထုတ်လုပ်ပြီး Sphinx စာရွက်စာတမ်း ပရောဂျက်များတွင် တိုက်ရိုက် အသုံးပြုနိုင်သည်။"
    from_alias: "reStructuredText ဇယား"
    to_alias: "reStructuredText ပုံစံ"
  PHP:
    alias: "PHP Array"
    what: "PHP သည် ကျော်ကြားသော server-side scripting language တစ်ခုဖြစ်ပြီး array များသည် ၎င်း၏ အဓိက ဒေတာ ဖွဲ့စည်းပုံဖြစ်ကာ ဝဘ် ဖွံ့ဖြိုးတိုးတက်မှု နှင့် ဒေတာ လုပ်ဆောင်မှုတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။"
    step1: "PHP array များပါဝင်သော ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် ဒေတာကို တိုက်ရိုက် ကူးထည့်ပါ။"
    step3: "PHP ပရောဂျက်များတွင် တိုက်ရိုက် အသုံးပြုနိုင်သော စံ PHP array ကုဒ်ကို ထုတ်လုပ်ပြီး associative နှင့် indexed array ပုံစံများကို ပံ့ပိုးပေးသည်။"
    from_alias: "PHP Array"
    to_alias: "PHP ကုဒ်"
  Ruby:
    alias: "Ruby Array"
    what: "Ruby သည် ကျစ်လစ်ပြီး လှပသော syntax ရှိသော dynamic object-oriented programming language တစ်ခုဖြစ်ပြီး array များသည် အရေးကြီးသော ဒေတာ ဖွဲ့စည်းပုံတစ်ခုဖြစ်သည်။"
    step1: "Ruby ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် array ဒေတာကို ကူးထည့်ပါ။"
    step3: "Ruby syntax သတ်မှတ်ချက်များနှင့် ကိုက်ညီသော Ruby array ကုဒ်ကို ထုတ်လုပ်ပြီး Ruby ပရောဂျက်များတွင် တိုက်ရိုက် အသုံးပြုနိုင်သည်။"
    from_alias: "Ruby Array"
    to_alias: "Ruby ကုဒ်"
  ASP:
    alias: "ASP Array"
    what: "ASP (Active Server Pages) သည် Microsoft ၏ server-side scripting environment တစ်ခုဖြစ်ပြီး dynamic ဝဘ်စာမျက်နှာများ ဖွံ့ဖြိုးတိုးတက်ရန်အတွက် programming language များစွာကို ပံ့ပိုးပေးသည်။"
    step1: "ASP ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် array ဒေတာကို ကူးထည့်ပါ။"
    step3: "VBScript နှင့် JScript syntax ၏ ပံ့ပိုးမှုဖြင့် ASP-compatible array ကုဒ်ကို ထုတ်လုပ်ပြီး ASP.NET ပရောဂျက်များတွင် အသုံးပြုနိုင်သည်။"
    from_alias: "ASP Array"
    to_alias: "ASP ကုဒ်"
  ActionScript:
    alias: "ActionScript Array"
    what: "ActionScript သည် အဓိကအားဖြင့် Adobe Flash နှင့် AIR အပလီကေးရှင်း ဖွံ့ဖြိုးတိုးတက်မှုအတွက် အသုံးပြုသော object-oriented programming language တစ်ခုဖြစ်သည်။"
    step1: ".as ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် ActionScript ဒေတာကို ကူးထည့်ပါ။"
    step3: "AS3 syntax စံချိန်စံညွှန်းများနှင့် ကိုက်ညီသော ActionScript array ကုဒ်ကို ထုတ်လုပ်ပြီး Flash နှင့် Flex ပရောဂျက် ဖွံ့ဖြိုးတိုးတက်မှုအတွက် အသုံးပြုနိုင်သည်။"
    from_alias: "ActionScript Array"
    to_alias: "ActionScript ကုဒ်"
  BBCode:
    alias: "BBCode ဇယား"
    what: "BBCode သည် ဖိုရမ်များ နှင့် အွန်လိုင်း အသိုင်းအဝိုင်းများတွင် အများအားဖြင့် အသုံးပြုသော ပေါ့ပါးသော markup language တစ်ခုဖြစ်ပြီး ဇယား ပံ့ပိုးမှု အပါအဝင် ရိုးရှင်းသော ပုံစံပြင်ဆင်မှု လုပ်ဆောင်ချက်ကို ပေးပါသည်။"
    step1: "BBCode ပါဝင်သော ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် ဒေတာကို ကူးထည့်ပါ။"
    step3: "ချုံ့ထားသော ထုတ်လုပ်မှု ပုံစံ၏ ပံ့ပိုးမှုဖြင့် ဖိုရမ် ပို့စ်တင်ခြင်း နှင့် အသိုင်းအဝိုင်း အကြောင်းအရာ ဖန်တီးမှုအတွက် သင့်လျော်သော BBCode ဇယား ကုဒ်ကို ထုတ်လုပ်ပါ။"
    from_alias: "BBCode ဇယား"
    to_alias: "BBCode ပုံစံ"
  PDF:
    alias: "PDF ဇယား"
    what: "PDF (Portable Document Format) သည် ပုံသေ layout၊ တသမတ်တည်း ပြသမှု နှင့် အရည်အသွေးမြင့် ပုံနှိပ်မှု လက္ခဏာများရှိသော cross-platform စာရွက်စာတမ်း စံချိန်စံညွှန်းတစ်ခုဖြစ်သည်။ တရားဝင် စာရွက်စာတမ်းများ၊ အစီရင်ခံစာများ၊ ငွေတောင်းခံလွှာများ၊ စာချုပ်များ နှင့် ပညာရေး စာတမ်းများတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။ လုပ်ငန်း ဆက်သွယ်မှု နှင့် စာရွက်စာတမ်း သိမ်းဆည်းမှုအတွက် နှစ်သက်ရွေးချယ်သော ပုံစံဖြစ်ပြီး ကွဲပြားသော ကိရိယာများ နှင့် operating system များတွင် လုံးဝ တသမတ်တည်း မြင်ယောင်ခြင်း အကျိုးသက်ရောက်မှုများကို သေချာစေသည်။"
    step1: "မည်သည့် ပုံစံမဆို ဇယား ဒေတာကို ထည့်သွင်းပါ။ ကိရိယာသည် ဒေတာ ဖွဲ့စည်းပုံကို အလိုအလျောက် ခွဲခြမ်းစိတ်ဖြာပြီး ဉာဏ်ရည်ဖြင့် layout ဒီဇိုင်းကို လုပ်ဆောင်ကာ ကြီးမားသော ဇယား အလိုအလျောက် စာမျက်နှာ ခွဲခြမ်းမှု နှင့် ရှုပ်ထွေးသော ဒေတာ အမျိုးအစား လုပ်ဆောင်မှုကို ပံ့ပိုးပေးသည်။"
    step3: "ပရော်ဖက်ရှင်နယ် theme စတိုင်များစွာ (လုပ်ငန်း၊ ပညာရေး၊ အနည်းဆုံး၊ စသည်)၊ ဘာသာစကားများစွာ ဖောင့်များ၊ အလိုအလျောက် စာမျက်နှာ ခွဲခြမ်းမှု၊ ရေမှတ် ထည့်ခြင်း နှင့် ပုံနှိပ်မှု ပိုမိုကောင်းမွန်စေမှု၏ ပံ့ပိုးမှုဖြင့် အရည်အသွေးမြင့် PDF ဇယား ဖိုင်များကို ထုတ်လုပ်ပါ။ ထုတ်လုပ်ထားသော PDF စာရွက်စာတမ်းများသည် ပရော်ဖက်ရှင်နယ် အသွင်အပြင် ရှိပြီး လုပ်ငန်း တင်ပြမှုများ နှင့် တရားဝင် ထုတ်ဝေမှုအတွက် တိုက်ရိုက် အသုံးပြုနိုင်ကြောင်း သေချာစေသည်။"
    from_alias: "ဇယား ဒေတာ"
    to_alias: "PDF ပရော်ဖက်ရှင်နယ် စာရွက်စာတမ်း"
  JPEG:
    alias: "JPEG ပုံ"
    what: "JPEG သည် ထူးခြားသော ချုံ့ခြင်း အကျိုးသက်ရောက်မှုများ နှင့် ကျယ်ပြန့်သော တွဲဖက်အသုံးပြုနိုင်မှုရှိသော အကျယ်ပြန့်ဆုံး အသုံးပြုသော ဒစ်ဂျစ်တယ် ပုံ ပုံစံဖြစ်သည်။ ၎င်း၏ သေးငယ်သော ဖိုင် အရွယ်အစား နှင့် မြန်ဆန်သော loading အမြန်နှုန်းက ၎င်းကို ဝဘ် ပြသမှု၊ လူမှုကွန်ယက် မျှဝေမှု၊ စာရွက်စာတမ်း သရုပ်ဖော်မှု နှင့် အွန်လိုင်း တင်ပြမှုများအတွက် သင့်လျော်စေသည်။ ဒစ်ဂျစ်တယ် မီဒီယာ နှင့် ကွန်ယက် ဆက်သွယ်မှုအတွက် စံ ပုံ ပုံစံဖြစ်ပြီး ကိရိယာများ နှင့် ဆော့ဖ်ဝဲများ အားလုံးနီးပါးမှ ပြီးပြည့်စုံစွာ ပံ့ပိုးပေးသည်။"
    step1: "မည်သည့် ပုံစံမဆို ဇယား ဒေတာကို ထည့်သွင်းပါ။ ကိရိယာသည် ဉာဏ်ရည်ဖြင့် layout ဒီဇိုင်း နှင့် မြင်ယောင်ခြင်း ပိုမိုကောင်းမွန်စေမှုကို လုပ်ဆောင်ပြီး အကောင်းဆုံး အရွယ်အစား နှင့် resolution ကို အလိုအလျောက် တွက်ချက်သည်။"
    step3: "theme အရောင် အစီအစဉ်များစွာ (အလင်း၊ အမှောင်၊ မျက်လုံးအတွက် ဖော်ရွေသော၊ စသည်)၊ လိုက်လျောညီထွေ layout၊ စာသား ရှင်းလင်းမှု ပိုမိုကောင်းမွန်စေမှု နှင့် အရွယ်အစား စိတ်ကြိုက်ပြင်ဆင်မှု၏ ပံ့ပိုးမှုဖြင့် အရည်အသွေးမြင့် JPEG ဇယား ပုံများကို ထုတ်လုပ်ပါ။ အွန်လိုင်း မျှဝေမှု၊ စာရွက်စာတမ်း ထည့်သွင်းမှု နှင့် တင်ပြမှု အသုံးပြုမှုအတွက် သင့်လျော်ပြီး ပြသမှု ကိရိယာများစွာတွင် ထူးခြားသော မြင်ယောင်ခြင်း အကျိုးသက်ရောက်မှုများကို သေချာစေသည်။"
    from_alias: "ဇယား ဒေတာ"
    to_alias: "JPEG အရည်အသွေးမြင့် ပုံ"
  Jira:
    alias: "Jira ဇယား"
    what: "JIRA သည် Atlassian မှ ဖွံ့ဖြိုးတိုးတက်ထားသော ပရော်ဖက်ရှင်နယ် ပရောဂျက် စီမံခန့်ခွဲမှု နှင့် bug ခြေရာခံမှု ဆော့ဖ်ဝဲဖြစ်ပြီး agile ဖွံ့ဖြိုးတိုးတက်မှု၊ ဆော့ဖ်ဝဲ စမ်းသပ်မှု နှင့် ပရောဂျက် ပူးပေါင်းဆောင်ရွက်မှုတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။ ၎င်း၏ ဇယား လုပ်ဆောင်ချက်သည် ကြွယ်ဝသော ပုံစံပြင်ဆင်မှု ရွေးချယ်မှုများ နှင့် ဒေတာ ပြသမှုကို ပံ့ပိုးပေးပြီး လိုအပ်ချက် စီမံခန့်ခွဲမှု၊ bug ခြေရာခံမှု နှင့် တိုးတက်မှု အစီရင်ခံမှုတွင် ဆော့ဖ်ဝဲ ဖွံ့ဖြိုးတိုးတက်မှု အဖွဲ့များ၊ ပရောဂျက် မန်နေဂျာများ နှင့် အရည်အသွေး အာမခံ ဝန်ထမ်းများအတွက် အရေးကြီးသော ကိရိယာအဖြစ် ဆောင်ရွက်သည်။"
    step1: "ဇယား ဒေတာပါဝင်သော ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် ဒေတာ အကြောင်းအရာကို တိုက်ရိုက် ကူးထည့်ပါ။ ကိရိယာသည် ဇယား ဒေတာ နှင့် အထူး အက္ခရာ escaping ကို အလိုအလျောက် လုပ်ဆောင်သည်။"
    step3: "ခေါင်းစီး စတိုင် ဆက်တင်များ၊ ဆဲလ် ချိန်ညှိမှု၊ အက္ခရာ escape လုပ်ဆောင်မှု နှင့် ပုံစံ ပိုမိုကောင်းမွန်စေမှု၏ ပံ့ပိုးမှုဖြင့် JIRA platform-compatible ဇယား ကုဒ်ကို ထုတ်လုပ်ပါ။ ထုတ်လုပ်ထားသော ကုဒ်ကို JIRA ပြဿနာ ဖော်ပြချက်များ၊ မှတ်ချက်များ သို့မဟုတ် wiki စာမျက်နှာများတွင် တိုက်ရိုက် ကူးထည့်နိုင်ပြီး JIRA စနစ်များတွင် မှန်ကန်သော ပြသမှု နှင့် rendering ကို သေချာစေသည်။"
    from_alias: "ပရောဂျက် ဒေတာ"
    to_alias: "Jira ဇယား Syntax"
  Textile:
    alias: "Textile ဇယား"
    what: "Textile သည် ရိုးရှင်းပြီး လေ့လာရလွယ်ကူသော syntax ရှိသော ကျစ်လစ်သော ပေါ့ပါးသော markup language တစ်ခုဖြစ်ပြီး အကြောင်းအရာ စီမံခန့်ခွဲမှု စနစ်များ၊ ဘလော့ဂ် platform များ နှင့် ဖိုရမ် စနစ်များတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။ ၎င်း၏ ဇယား syntax သည် ရှင်းလင်းပြီး အလိုအလျောက် နားလည်နိုင်သော်လည်း မြန်ဆန်သော ပုံစံပြင်ဆင်မှု နှင့် စတိုင် ဆက်တင်များကို ပံ့ပိုးပေးသည်။ အကြောင်းအရာ ဖန်တီးသူများ နှင့် ဝဘ်ဆိုဒ် စီမံခန့်ခွဲသူများအတွက် မြန်ဆန်သော စာရွက်စာတမ်း ရေးသားမှု နှင့် အကြောင်းအရာ ထုတ်ဝေမှုအတွက် အကောင်းဆုံး ကိရိယာတစ်ခုဖြစ်သည်။"
    step1: "Textile ပုံစံ ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် ဇယား ဒေတာကို ကူးထည့်ပါ။ ကိရိယာသည် Textile markup syntax ကို ခွဲခြမ်းစိတ်ဖြာပြီး ဇယား အကြောင်းအရာကို ထုတ်ယူသည်။"
    step3: "ခေါင်းစီး markup၊ ဆဲလ် ချိန်ညှိမှု၊ အထူး အက္ခရာ escaping နှင့် ပုံစံ ပိုမိုကောင်းမွန်စေမှု၏ ပံ့ပိုးမှုဖြင့် စံ Textile ဇယား syntax ကို ထုတ်လုပ်ပါ။ ထုတ်လုပ်ထားသော ကုဒ်ကို Textile ကို ပံ့ပိုးပေးသော CMS စနစ်များ၊ ဘလော့ဂ် platform များ နှင့် စာရွက်စာတမ်း စနစ်များတွင် တိုက်ရိုက် အသုံးပြုနိုင်ပြီး မှန်ကန်သော အကြောင်းအရာ rendering နှင့် ပြသမှုကို သေချာစေသည်။"
    from_alias: "Textile စာရွက်စာတမ်း"
    to_alias: "Textile ဇယား Syntax"
  PNG:
    alias: "PNG ပုံ"
    what: "PNG (Portable Network Graphics) သည် ထူးခြားသော ချုံ့ခြင်း နှင့် ပွင့်လင်းမြင်သာမှု ပံ့ပိုးမှုရှိသော အရှုံးမရှိ ပုံ ပုံစံတစ်ခုဖြစ်သည်။ ဝဘ် ဒီဇိုင်း၊ ဒစ်ဂျစ်တယ် ဂရပ်ဖစ်များ နှင့် ပရော်ဖက်ရှင်နယ် ဓာတ်ပုံရိုက်ခြင်းတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။ ၎င်း၏ အရည်အသွေးမြင့်မှု နှင့် ကျယ်ပြန့်သော တွဲဖက်အသုံးပြုနိုင်မှုက ၎င်းကို screenshot များ၊ လိုဂို များ၊ ပုံကြမ်းများ နှင့် ကြည်လင်သော အသေးစိတ်များ နှင့် ပွင့်လင်းမြင်သာသော နောက်ခံများ လိုအပ်သော ပုံများအတွက် အကောင်းဆုံး ဖြစ်စေသည်။"
    step1: "မည်သည့် ပုံစံမဆို ဇယား ဒေတာကို ထည့်သွင်းပါ။ ကိရိယာသည် ဉာဏ်ရည်ဖြင့် layout ဒီဇိုင်း နှင့် မြင်ယောင်ခြင်း ပိုမိုကောင်းမွန်စေမှုကို လုပ်ဆောင်ပြီး PNG ထုတ်လုပ်မှုအတွက် အကောင်းဆုံး အရွယ်အစား နှင့် resolution ကို အလိုအလျောက် တွက်ချက်သည်။"
    step3: "theme အရောင် အစီအစဉ်များစွာ၊ ပွင့်လင်းမြင်သာသော နောက်ခံများ၊ လိုက်လျောညီထွေ layout နှင့် စာသား ရှင်းလင်းမှု ပိုမိုကောင်းမွန်စေမှု၏ ပံ့ပိုးမှုဖြင့် အရည်အသွေးမြင့် PNG ဇယား ပုံများကို ထုတ်လုပ်ပါ။ ထူးခြားသော မြင်ယောင်ခြင်း အရည်အသွေးဖြင့် ဝဘ် အသုံးပြုမှု၊ စာရွက်စာတမ်း ထည့်သွင်းမှု နှင့် ပရော်ဖက်ရှင်နယ် တင်ပြမှုများအတွက် ပြီးပြည့်စုံသည်။"
    from_alias: "ဇယား ဒေတာ"
    to_alias: "PNG အရည်အသွေးမြင့် ပုံ"
  TOML:
    alias: "TOML Configuration"
    what: "TOML (Tom's Obvious, Minimal Language) သည် ဖတ်ရှုရလွယ်ကူပြီး ရေးသားရလွယ်ကူသော configuration ဖိုင် ပုံစံတစ်ခုဖြစ်သည်။ မရေရာမှု မရှိပြီး ရိုးရှင်းအောင် ဒီဇိုင်းပြုလုပ်ထားပြီး configuration စီမံခန့်ခွဲမှုအတွက် ခေတ်သစ် ဆော့ဖ်ဝဲ ပရောဂျက်များတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။ ၎င်း၏ ရှင်းလင်းသော syntax နှင့် ခိုင်မာသော typing က ၎င်းကို အပလီကေးရှင်း ဆက်တင်များ နှင့် ပရောဂျက် configuration ဖိုင်များအတွက် ထူးခြားသော ရွေးချယ်မှု ဖြစ်စေသည်။"
    step1: "TOML ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် configuration ဒေတာကို ကူးထည့်ပါ။ ကိရိယာသည် TOML syntax ကို ခွဲခြမ်းစိတ်ဖြာပြီး ဖွဲ့စည်းထားသော configuration အချက်အလက်များကို ထုတ်ယူသည်။"
    step3: "nested ဖွဲ့စည်းပုံများ၊ ဒေတာ အမျိုးအစားများ နှင့် မှတ်ချက်များ၏ ပံ့ပိုးမှုဖြင့် စံ TOML ပုံစံကို ထုတ်လုပ်ပါ။ ထုတ်လုပ်ထားသော TOML ဖိုင်များသည် အပလီကေးရှင်း configuration၊ build ကိရိယာများ နှင့် ပရောဂျက် ဆက်တင်များအတွက် ပြီးပြည့်စုံသည်။"
    from_alias: "TOML Configuration"
    to_alias: "TOML ပုံစံ"
  INI:
    alias: "INI Configuration"
    what: "INI ဖိုင်များသည် အပလီကေးရှင်းများစွာ နှင့် operating system များမှ အသုံးပြုသော ရိုးရှင်းသော configuration ဖိုင်များဖြစ်သည်။ ၎င်းတို့၏ တိုက်ရိုက် key-value pair ဖွဲ့စည်းပုံက ၎င်းတို့ကို လက်ဖြင့် ဖတ်ရှုရလွယ်ကူပြီး တည်းဖြတ်ရလွယ်ကူစေသည်။ လူသားများ ဖတ်ရှုနိုင်မှု အရေးကြီးသော Windows အပလီကေးရှင်းများ၊ legacy စနစ်များ နှင့် ရိုးရှင်းသော configuration အခြေအနေများတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။"
    step1: "INI ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် configuration ဒေတာကို ကူးထည့်ပါ။ ကိရိယာသည် INI syntax ကို ခွဲခြမ်းစိတ်ဖြာပြီး section-based configuration အချက်အလက်များကို ထုတ်ယူသည်။"
    step3: "section များ၊ မှတ်ချက်များ နှင့် ဒေတာ အမျိုးအစားများစွာ၏ ပံ့ပိုးမှုဖြင့် စံ INI ပုံစံကို ထုတ်လုပ်ပါ။ ထုတ်လုပ်ထားသော INI ဖိုင်များသည် အပလီကေးရှင်းများ နှင့် configuration စနစ်များ အများစုနှင့် တွဲဖက်အသုံးပြုနိုင်သည်။"
    from_alias: "INI Configuration"
    to_alias: "INI ပုံစံ"
  Avro:
    alias: "Avro Schema"
    what: "Apache Avro သည် ကြွယ်ဝသော ဒေတာ ဖွဲ့စည်းပုံများ၊ ကျစ်လစ်သော binary ပုံစံ နှင့် schema ဆင့်ကဲ ပြောင်းလဲမှု စွမ်းရည်များကို ပေးသော ဒေတာ serialization စနစ်တစ်ခုဖြစ်သည်။ big data လုပ်ဆောင်မှု၊ မက်ဆေ့ခ် တန်းစီများ နှင့် ဖြန့်ဝေထားသော စနစ်များတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။ ၎င်း၏ schema သတ်မှတ်ချက်သည် ရှုပ်ထွေးသော ဒေတာ အမျိုးအစားများ နှင့် ဗားရှင်း တွဲဖက်အသုံးပြုနိုင်မှုကို ပံ့ပိုးပေးပြီး ဒေတာ အင်ဂျင်နီယာများ နှင့် စနစ် ဗိသုကာပညာရှင်များအတွက် အရေးကြီးသော ကိရိယာတစ်ခု ဖြစ်စေသည်။"
    step1: "Avro schema ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် ဒေတာကို ကူးထည့်ပါ။ ကိရိယာသည် Avro schema သတ်မှတ်ချက်များကို ခွဲခြမ်းစိတ်ဖြာပြီး ဇယား ဖွဲ့စည်းပုံ အချက်အလက်များကို ထုတ်ယူသည်။"
    step3: "ဒေတာ အမျိုးအစား mapping၊ field ကန့်သတ်ချက်များ နှင့် schema အတည်ပြုမှု၏ ပံ့ပိုးမှုဖြင့် စံ Avro schema သတ်မှတ်ချက်များကို ထုတ်လုပ်ပါ။ ထုတ်လုပ်ထားသော schema များကို Hadoop ecosystem များ၊ Kafka မက်ဆေ့ခ် စနစ်များ နှင့် အခြား big data platform များတွင် တိုက်ရိုက် အသုံးပြုနိုင်သည်။"
    from_alias: "Avro Schema"
    to_alias: "Avro ဒေတာ ပုံစံ"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) သည် Google ၏ ဘာသာစကား-ကြားနေ၊ platform-ကြားနေ၊ တိုးချဲ့နိုင်သော ဖွဲ့စည်းထားသော ဒေတာ serializing အတွက် ယန္တရားတစ်ခုဖြစ်သည်။ microservice များ၊ API ဖွံ့ဖြိုးတိုးတက်မှု နှင့် ဒေတာ သိမ်းဆည်းမှုတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။ ၎င်း၏ ထိရောက်သော binary ပုံစံ နှင့် ခိုင်မာသော typing က ၎င်းကို မြင့်မားသော စွမ်းဆောင်ရည် အပလီကေးရှင်းများ နှင့် ဘာသာစကားများကြား ဆက်သွယ်မှုအတွက် အကောင်းဆုံး ဖြစ်စေသည်။"
    step1: ".proto ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် Protocol Buffer သတ်မှတ်ချက်များကို ကူးထည့်ပါ။ ကိရိယာသည် protobuf syntax ကို ခွဲခြမ်းစိတ်ဖြာပြီး မက်ဆေ့ခ် ဖွဲ့စည်းပုံ အချက်အလက်များကို ထုတ်ယူသည်။"
    step3: "မက်ဆေ့ခ် အမျိုးအစားများ၊ field ရွေးချယ်မှုများ နှင့် ဝန်ဆောင်မှု သတ်မှတ်ချက်များ၏ ပံ့ပိုးမှုဖြင့် စံ Protocol Buffer သတ်မှတ်ချက်များကို ထုတ်လုပ်ပါ။ ထုတ်လုပ်ထားသော .proto ဖိုင်များကို programming language များစွာအတွက် compile လုပ်နိုင်သည်။"
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf Schema"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas သည် Python တွင် အကျော်ကြားဆုံး ဒေတာ ခွဲခြမ်းစိတ်ဖြာမှု library ဖြစ်ပြီး DataFrame သည် ၎င်း၏ အဓိက ဒေတာ ဖွဲ့စည်းပုံဖြစ်သည်။ ၎င်းသည် အားကောင်းသော ဒေတာ ကိုင်တွယ်မှု၊ သန့်ရှင်းရေး နှင့် ခွဲခြမ်းစိတ်ဖြာမှု စွမ်းရည်များကို ပေးပြီး ဒေတာ သိပ္ပံ၊ machine learning နှင့် လုပ်ငန်း ဉာဏ်ရည်တွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။ Python ဖွံ့ဖြိုးတိုးတက်ရေးသမားများ နှင့် ဒေတာ ခွဲခြမ်းစိတ်ဖြာသူများအတွက် မရှိမဖြစ် လိုအပ်သော ကိရိယာတစ်ခုဖြစ်သည်။"
    step1: "DataFrame ကုဒ်ပါဝင်သော Python ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် ဒေတာကို ကူးထည့်ပါ။ ကိရိယာသည် Pandas syntax ကို ခွဲခြမ်းစိတ်ဖြာပြီး DataFrame ဖွဲ့စည်းပုံ အချက်အလက်များကို ထုတ်ယူသည်။"
    step3: "ဒေတာ အမျိုးအစား သတ်မှတ်ချက်များ၊ index ဆက်တင်များ နှင့် ဒေတာ လုပ်ဆောင်မှုများ၏ ပံ့ပိုးမှုဖြင့် စံ Pandas DataFrame ကုဒ်ကို ထုတ်လုပ်ပါ။ ထုတ်လုပ်ထားသော ကုဒ်ကို ဒေတာ ခွဲခြမ်းစိတ်ဖြာမှု နှင့် လုပ်ဆောင်မှုအတွက် Python ပတ်ဝန်းကျင်တွင် တိုက်ရိုက် လုပ်ဆောင်နိုင်သည်။"
    from_alias: "Pandas DataFrame"
    to_alias: "Python ဒေတာ ဖွဲ့စည်းပုံ"
  RDF:
    alias: "RDF Triple"
    what: "RDF (Resource Description Framework) သည် ဝဘ်တွင် ဒေတာ လဲလှယ်မှုအတွက် စံ မော်ဒယ်တစ်ခုဖြစ်ပြီး အရင်းအမြစ်များအကြောင်း အချက်အလက်များကို ဂရပ်ဖ် ပုံစံဖြင့် ကိုယ်စားပြုရန် ဒီဇိုင်းပြုလုပ်ထားသည်။ semantic ဝဘ်၊ အသိပညာ ဂရပ်ဖ်များ နှင့် ချိတ်ဆက်ထားသော ဒေတာ အပလီကေးရှင်းများတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသည်။ ၎င်း၏ triple ဖွဲ့စည်းပုံသည် ကြွယ်ဝသော metadata ကိုယ်စားပြုမှု နှင့် semantic ဆက်နွယ်မှုများကို ဖြစ်စေသည်။"
    step1: "RDF ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် triple ဒေတာကို ကူးထည့်ပါ။ ကိရိယာသည် RDF syntax ကို ခွဲခြမ်းစိတ်ဖြာပြီး semantic ဆက်နွယ်မှုများ နှင့် အရင်းအမြစ် အချက်အလက်များကို ထုတ်ယူသည်။"
    step3: "serialization များစွာ (RDF/XML၊ Turtle၊ N-Triples) ၏ ပံ့ပိုးမှုဖြင့် စံ RDF ပုံစံကို ထုတ်လုပ်ပါ။ ထုတ်လုပ်ထားသော RDF ကို semantic ဝဘ် အပလီကေးရှင်းများ၊ အသိပညာ အခြေခံများ နှင့် ချိတ်ဆက်ထားသော ဒေတာ စနစ်များတွင် အသုံးပြုနိုင်သည်။"
    from_alias: "RDF ဒေတာ"
    to_alias: "RDF Semantic ပုံစံ"
  MATLAB:
    alias: "MATLAB Array"
    what: "MATLAB သည် အင်ဂျင်နီယာ တွက်ချက်မှု၊ ဒေတာ ခွဲခြမ်းစိတ်ဖြာမှု နှင့် algorithm ဖွံ့ဖြိုးတိုးတက်မှုတွင် ကျယ်ကျယ်ပြန့်ပြန့် အသုံးပြုသော မြင့်မားသော စွမ်းဆောင်ရည် ကိန်းဂဏန်း တွက်ချက်မှု နှင့် မြင်ယောင်ခြင်း ဆော့ဖ်ဝဲတစ်ခုဖြစ်သည်။ ၎င်း၏ array နှင့် matrix လုပ်ဆောင်မှုများသည် အားကောင်းပြီး ရှုပ်ထွေးသော သင်္ချာ တွက်ချက်မှုများ နှင့် ဒေတာ လုပ်ဆောင်မှုကို ပံ့ပိုးပေးသည်။ အင်ဂျင်နီယာများ၊ သုတေသီများ နှင့် ဒေတာ သိပ္ပံပညာရှင်များအတွက် မရှိမဖြစ် လိုအပ်သော ကိရိယာတစ်ခုဖြစ်သည်။"
    step1: "MATLAB .m ဖိုင်များကို အပ်လုဒ်လုပ်ပါ သို့မဟုတ် array ဒေတာကို ကူးထည့်ပါ။ ကိရိယာသည် MATLAB syntax ကို ခွဲခြမ်းစိတ်ဖြာပြီး array ဖွဲ့စည်းပုံ အချက်အလက်များကို ထုတ်ယူသည်။"
    step3: "multi-dimensional array များ၊ ဒေတာ အမျိုးအစား သတ်မှတ်ချက်များ နှင့် variable အမည်ပေးခြင်း၏ ပံ့ပိုးမှုဖြင့် စံ MATLAB array ကုဒ်ကို ထုတ်လုပ်ပါ။ ထုတ်လုပ်ထားသော ကုဒ်ကို ဒေတာ ခွဲခြမ်းစိတ်ဖြာမှု နှင့် သိပ္ပံ တွက်ချက်မှုအတွက် MATLAB ပတ်ဝန်းကျင်တွင် တိုက်ရိုက် လုပ်ဆောင်နိုင်သည်။"
    from_alias: "MATLAB Array"
    to_alias: "MATLAB ကုဒ် ပုံစံ"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame သည် R ပရိုဂရမ်မင်းဘာသာစကားရှိ အဓိကဒေတာဖွဲ့စည်းပုံဖြစ်ပြီး၊ စာရင်းအင်းခွဲခြမ်းစိတ်ဖြာမှု၊ ဒေတာတူးဖော်ခြင်းနှင့် စက်သင်ခြင်းတို့တွင် ကျယ်ကျယ်ပြန့်ပြန့်အသုံးပြုသည်။ R သည် စာရင်းအင်းတွက်ချက်မှုနှင့် ဂရပ်ဖစ်များအတွက် ထိပ်တန်းကိရိယာဖြစ်ပြီး၊ DataFrame က ခိုင်မာသော ဒေတာကိုင်တွယ်မှု၊ စာရင်းအင်းခွဲခြမ်းစိတ်ဖြာမှုနှင့် ရူပဖော်စွမ်းရည်များကို ပေးစွမ်းသည်။ ဖွဲ့စည်းပုံရှိသော ဒေတာခွဲခြမ်းစိတ်ဖြာမှုနှင့် လုပ်ဆောင်သော ဒေတာသိပ္ပံပညာရှင်များ၊ စာရင်းအင်းပညာရှင်များနှင့် သုတေသီများအတွက် မရှိမဖြစ်လိုအပ်သည်။"
    step1: "R ဒေတာဖိုင်များကို အပ်လုပ်ဒ်လုပ်ပါ သို့မဟုတ် DataFrame ကုဒ်ကို ကူးထည့်ပါ။ ကိရိယာက R စာရေးဟန်ကို ပါးစပ်ပြီး ကော်လံအမျိုးအစားများ၊ တန်းနာမည်များနှင့် ဒေတာအကြောင်းအရာများအပါအဝင် DataFrame ဖွဲ့စည်းပုံအချက်အလက်ကို ထုတ်ယူသည်။"
    step3: "ဒေတာအမျိုးအစားသတ်မှတ်ချက်များ၊ အတွဲ့အဆင့်များ၊ တန်း/ကော်လံနာမည်များနှင့် R-သတ်သတ်မှတ်ထားသော ဒေတာဖွဲ့စည်းပုံများအတွက် ထောက်ပံ့မှုဖြင့် စံ R DataFrame ကုဒ်ကို ထုတ်လုပ်ပါ။ ထုတ်လုပ်ထားသော ကုဒ်ကို စာရင်းအင်းခွဲခြမ်းစိတ်ဖြာမှုနှင့် ဒေတာစနစ်တကျလုပ်ဆောင်မှုအတွက် R ပတ်ဝန်းကျင်တွင် တိုက်ရိုက်လုပ်ဆောင်နိုင်သည်။"
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "ပြောင်းလဲခြင်း စတင်ပါ"
  start_generating: "ထုတ်လုပ်ခြင်းစတင်ပါ"
  api_docs: "API စာရွက်စာတမ်းများ"
related:
  section_title: 'နောက်ထပ် {{ if and .from (ne .from "generator") }}{{ .from }} နှင့် {{ end }}{{ .to }} ပြောင်းလဲကိရိယာများ'
  section_description: '{{ if and .from (ne .from "generator") }}{{ .from }} နှင့် {{ end }}{{ .to }} ဖော်မတ်များအတွက် နောက်ထပ် ပြောင်းလဲကိရိယာများကို ရှာဖွေပါ။ ကျွန်ုပ်တို့၏ ပရော်ဖက်ရှင်နယ် အွန်လိုင်း ပြောင်းလဲကိရိယာများဖြင့် သင်၏ ဒေတာကို ဖော်မတ်များစွာကြားတွင် ပြောင်းလဲပါ။'
  title: "{{ .from }} မှ {{ .to }}"
howto:
  step2: "ပရော်ဖက်ရှင်နယ် လုပ်ဆောင်ချက်များပါရှိသော ကျွန်ုပ်တို့၏ အဆင့်မြင့် အွန်လိုင်း ဇယား တည်းဖြတ်ကိရိယာကို အသုံးပြု၍ ဒေတာကို တည်းဖြတ်ပါ။ ဗလာ အတန်းများ ဖျက်ခြင်း၊ ပွားများ ဖယ်ရှားခြင်း၊ ဒေတာ အပြောင်းအရွှေ့၊ အစီအစဉ်ခြင်း၊ regex ရှာဖွေ & အစားထိုးခြင်း၊ နှင့် အချိန်နှင့်တပြေးညီ ကြိုကြည့်ခြင်းကို ပံ့ပိုးပေးသည်။ အပြောင်းအလဲအားလုံးသည် တိကျပြီး ယုံကြည်စိတ်ချရသော ရလဒ်များဖြင့် %s ဖော်မတ်သို့ အလိုအလျောက် ပြောင်းလဲသည်။"
  section_title: "{{ . }} ကို မည်သို့ အသုံးပြုရမည်"
  converter_description: "ကျွန်ုပ်တို့၏ အဆင့်ဆင့် လမ်းညွှန်ချက်ဖြင့် {{ .from }} ကို {{ .to }} သို့ ပြောင်းလဲရန် လေ့လာပါ။ အဆင့်မြင့် လုပ်ဆောင်ချက်များနှင့် အချိန်နှင့်တပြေးညီ ကြိုကြည့်ခြင်းပါရှိသော ပရော်ဖက်ရှင်နယ် အွန်လိုင်း ပြောင်းလဲကိရိယာ။"
  generator_description: "ကျွန်ုပ်တို့၏ အွန်လိုင်း ဂျင်နရေတာဖြင့် ပရော်ဖက်ရှင်နယ် {{ .to }} ဇယားများ ဖန်တီးရန် လေ့လာပါ။ Excel ကဲ့သို့ တည်းဖြတ်ခြင်း၊ အချိန်နှင့်တပြေးညီ ကြိုကြည့်ခြင်း၊ နှင့် ချက်ချင်း ထုတ်ယူခြင်း စွမ်းရည်များ။"
extension:
  section_title: "ဇယား ရှာဖွေခြင်းနှင့် ထုတ်ယူခြင်း တိုးချဲ့ကိရိယာ"
  section_description: "တစ်ကလစ်ဖြင့် မည်သည့်ဝဘ်ဆိုက်မှမဆို ဇယားများကို ထုတ်ယူပါ။ Excel, CSV, JSON အပါအဝင် 30+ ဖော်မတ်များသို့ ချက်ချင်း ပြောင်းလဲပါ - ကူးထည့်ခြင်း မလိုအပ်ပါ။"
  features:
    extraction_title: "တစ်ကလစ် ဇယား ထုတ်ယူခြင်း"
    extraction_description: "ကူးထည့်ခြင်း မလုပ်ဘဲ မည်သည့်ဝဘ်စာမျက်နှာမှမဆို ဇယားများကို ချက်ချင်း ထုတ်ယူပါ - ပရော်ဖက်ရှင်နယ် ဒေတာ ထုတ်ယူခြင်းကို ရိုးရှင်းအောင် ပြုလုပ်ထားသည်"
    formats_title: "30+ ဖော်မတ် ပြောင်းလဲကိရိယာ ပံ့ပိုးမှု"
    formats_description: "ကျွန်ုပ်တို့၏ အဆင့်မြင့် ဇယား ပြောင်းလဲကိရိယာဖြင့် ထုတ်ယူထားသော ဇယားများကို Excel, CSV, JSON, Markdown, SQL နှင့် အခြားများသို့ ပြောင်းလဲပါ"
    detection_title: "စမတ် ဇယား ရှာဖွေခြင်း"
    detection_description: "မြန်ဆန်သော ဒေတာ ထုတ်ယူခြင်းနှင့် ပြောင်းလဲခြင်းအတွက် မည်သည့်ဝဘ်စာမျက်နှာတွင်မဆို ဇယားများကို အလိုအလျောက် ရှာဖွေပြီး မီးမောင်းထိုးပြသည်"
  hover_tip: "✨ ထုတ်ယူခြင်း အိုင်ကွန်ကို မြင်ရန် မည်သည့်ဇယားပေါ်တွင်မဆို ကြွက်ခလုတ်ကို တင်ပါ"
recommendations:
  section_title: "တက္ကသိုလ်များနှင့် ပရော်ဖက်ရှင်နယ်များမှ အကြံပြုထားသည်"
  section_description: "ယုံကြည်စိတ်ချရသော ဇယား ပြောင်းလဲခြင်းနှင့် ဒေတာ လုပ်ဆောင်ခြင်းအတွက် တက္ကသိုလ်များ၊ သုတေသန အင်စတီကျူးများနှင့် ဖွံ့ဖြိုးတိုးတက်ရေး အဖွဲ့များရှိ ပရော်ဖက်ရှင်နယ်များမှ TableConvert ကို ယုံကြည်ကြသည်။"
  cards:
    university_title: "ဝစ်ကွန်ဆင်-မက်ဒီဆင် တက္ကသိုလ်"
    university_description: "TableConvert.com - ပရော်ဖက်ရှင်နယ် အခမဲ့ အွန်လိုင်း ဇယား ပြောင်းလဲကိရိယာနှင့် ဒေတာ ဖော်မတ် ကိရိယာ"
    university_link: "ဆောင်းပါး ဖတ်ပါ"
    facebook_title: "ဒေတာ ပရော်ဖက်ရှင်နယ် အသိုင်းအဝိုင်း"
    facebook_description: "Facebook ဒေဗလပ်ပါ အုပ်စုများတွင် ဒေတာ ခွဲခြမ်းစိတ်ဖြာသူများနှင့် ပရော်ဖက်ရှင်နယ်များမှ မျှဝေပြီး အကြံပြုထားသည်"
    facebook_link: "ပို့စ် ကြည့်ပါ"
    twitter_title: "ဒေဗလပ်ပါ အသိုင်းအဝိုင်း"
    twitter_description: "ဇယား ပြောင်းလဲခြင်းအတွက် X (Twitter) တွင် @xiaoying_eth နှင့် အခြား ဒေဗလပ်ပါများမှ အကြံပြုထားသည်"
    twitter_link: "တွစ် ကြည့်ပါ"
faq:
  section_title: "မကြာခဏ မေးလေ့ရှိသော မေးခွန်းများ"
  section_description: "ကျွန်ုပ်တို့၏ အခမဲ့ အွန်လိုင်း ဇယား ပြောင်းလဲကိရိယာ၊ ဒေတာ ဖော်မတ်များနှင့် ပြောင်းလဲခြင်း လုပ်ငန်းစဉ်အကြောင်း ဘုံ မေးခွန်းများ."
  what: "%s ဖော်မတ် ဆိုတာ ဘာလဲ?"
  howto_convert:
    question: "{{ . }} ကို အခမဲ့ မည်သို့ အသုံးပြုရမည်နည်း?"
    answer: "ကျွန်ုပ်တို့၏ အခမဲ့ အွန်လိုင်း ဇယား ပြောင်းလဲကိရိယာကို အသုံးပြု၍ သင်၏ {{ .from }} ဖိုင်ကို အပ်လုဒ်လုပ်ပါ၊ ဒေတာ ကူးထည့်ပါ၊ သို့မဟုတ် ဝဘ်စာမျက်နှာများမှ ထုတ်ယူပါ။ ကျွန်ုပ်တို့၏ ပရော်ဖက်ရှင်နယ် ပြောင်းလဲကိရိယာသည် အချိန်နှင့်တပြေးညီ ကြိုကြည့်ခြင်းနှင့် အဆင့်မြင့် တည်းဖြတ်ခြင်း လုပ်ဆောင်ချက်များဖြင့် သင်၏ ဒေတာကို {{ .to }} ဖော်မတ်သို့ ချက်ချင်း ပြောင်းလဲပေးသည်။ ပြောင်းလဲပြီးသော ရလဒ်ကို ချက်ချင်း ဒေါင်းလုဒ်လုပ် သို့မဟုတ် ကူးယူပါ။"
  security:
    question: "ဤ အွန်လိုင်း ပြောင်းလဲကိရိယာကို အသုံးပြုသောအခါ ကျွန်ုပ်၏ ဒေတာ လုံခြုံပါသလား?"
    answer: "လုံးဝ လုံခြုံပါသည်! ဇယား ပြောင်းလဲမှုအားလုံးသည် သင်၏ ဘရောက်ဇာတွင် ဒေသတွင်း ဖြစ်ပွားသည် - သင်၏ ဒေတာသည် သင်၏ စက်ပစ္စည်းကို ဘယ်တော့မှ မထွက်ခွာပါ။ ကျွန်ုပ်တို့၏ အွန်လိုင်း ပြောင်းလဲကိရိယာသည် အရာအားလုံးကို ကလိုင်းယင့်-ဘက်တွင် လုပ်ဆောင်ပြီး၊ ပြီးပြည့်စုံသော ကိုယ်ရေးကိုယ်တာ လုံခြုံမှုနှင့် ဒေတာ လုံခြုံမှုကို သေချာစေသည်။ ကျွန်ုပ်တို့၏ ဆာဗာများတွင် မည်သည့်ဖိုင်မှ သိမ်းဆည်းမထားပါ။"
  free:
    question: "TableConvert သည် အမှန်တကယ် အသုံးပြုရန် အခမဲ့လား?"
    answer: "ဟုတ်ကဲ့၊ TableConvert သည် လုံးဝ အခမဲ့ဖြစ်သည်! ပြောင်းလဲကိရိယာ လုပ်ဆောင်ချက်အားလုံး၊ ဇယား တည်းဖြတ်ကိရိယာ၊ ဒေတာ ဂျင်နရေတာ ကိရိယာများနှင့် ထုတ်ယူခြင်း ရွေးချယ်မှုများသည် ကုန်ကျစရိတ်၊ မှတ်ပုံတင်ခြင်း သို့မဟုတ် ဝှက်ထားသော ကြေးများ မရှိဘဲ ရရှိနိုင်သည်။ အွန်လိုင်းတွင် အကန့်အသတ်မရှိ ဖိုင်များကို အခမဲ့ ပြောင်းလဲပါ။"
  filesize:
    question: "အွန်လိုင်း ပြောင်းလဲကိရိယာ၏ ဖိုင်အရွယ်အစား ကန့်သတ်ချက်များ ဘာတွေရှိသလဲ?"
    answer: "ကျွန်ုပ်တို့၏ အခမဲ့ အွန်လိုင်း ဇယား ပြောင်းလဲကိရိယာသည် 10MB အထိ ဖိုင်များကို ပံ့ပိုးပေးသည်။ ပိုကြီးသော ဖိုင်များ၊ အစုလိုက် လုပ်ဆောင်ခြင်း သို့မဟုတ် လုပ်ငန်းရေး လိုအပ်ချက်များအတွက်၊ ပိုမြင့်သော ကန့်သတ်ချက်များပါရှိသော ကျွန်ုပ်တို့၏ ဘရောက်ဇာ တိုးချဲ့ကိရိယာ သို့မဟုတ် ပရော်ဖက်ရှင်နယ် API ဝန်ဆောင်မှုကို အသုံးပြုပါ။"
stats:
  conversions: "ပြောင်းလဲပြီးသော ဇယားများ"
  tables: "ဖန်တီးပြီးသော ဇယားများ"
  formats: "ဒေတာ ဖိုင် ဖော်မတ်များ"
  rating: "အသုံးပြုသူ အဆင့်သတ်မှတ်ချက်"
