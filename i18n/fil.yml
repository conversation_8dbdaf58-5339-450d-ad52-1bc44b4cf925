site:
  fullname: "Table Convert"
  name: "TableConvert"
  subtitle: "Libreng Online Table Converter at Generator"
  intro: "Ang TableConvert ay isang libreng online table converter at data generator tool na sumusuporta sa conversion sa pagitan ng 30+ formats kasama ang Excel, CSV, JSON, Markdown, LaTeX, SQL at marami pa."
  followTwitter: "Sundan mo kami sa X"
title:
  converter: "%s sa %s"
  generator: "%s Generator"
post:
  tags:
    converter: "Converter"
    editor: "Editor"
    generator: "Generator"
    maker: "Builder"
  converter:
    title: "I-convert ang %s sa %s Online"
    short: "Isang libreng at malakas na %s sa %s online tool"
    intro: "Madaling gamitin na online %s sa %s converter. I-transform ang table data nang walang hirap gamit ang aming intuitive conversion tool. Ma<PERSON>is, maaasahan, at user-friendly."
  generator:
    title: "Online %s Editor at Generator"
    short: "Professional %s online generation tool na may comprehensive features"
    intro: "Madaling gamitin na online %s generator at table editor. Gumawa ng professional data tables nang walang hirap gamit ang aming intuitive tool at real-time preview."
navbar:
  search:
    placeholder: "Maghanap ng converter ..."
  sponsor: "Bilhan Mo Ako ng Kape"
  extension: "Extension"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Pinagmulan ng Data"
    placeholder: "I-paste ang inyong %s data o i-drag ang %s files dito"
    example: "Halimbawa"
    upload: "Mag-upload ng File"
    extract:
      enter: "I-extract mula sa Web Page"
      intro: "Maglagay ng web page URL na naglalaman ng table data para sa automatic extraction ng structured data"
      btn: "I-extract ang %s"
    excel:
      sheet: "Worksheet"
      none: "Wala"
  tableEditor:
    title: "Online Table Editor"
    undo: "I-undo"
    redo: "I-redo"
    transpose: "I-transpose"
    clear: "I-clear"
    deleteBlank: "Tanggalin ang Blangko"
    deleteDuplicate: "Tanggalin ang Duplicate"
    uppercase: "MALAKING TITIK"
    lowercase: "maliit na titik"
    capitalize: "Una ay Malaking Titik"
    replace:
      replace: "Hanapin at Palitan (Regex supported)"
      subst: "Palitan ng..."
      btn: "Palitan Lahat"
  tableGenerator:
    title: "Table Generator"
    sponsor: "Bilhan Mo Ako ng Kape"
    copy: "I-copy sa Clipboard"
    download: "I-download ang File"
    tooltip:
      html:
        escape: "I-escape ang mga special character ng HTML (&, <, >, \\, ') para maiwasan ang display errors"
        div: "Gamitin ang DIV+CSS layout sa halip na traditional TABLE tags, mas angkop para sa responsive design"
        minify: "Alisin ang whitespace at line breaks para makabuo ng compressed HTML code"
        thead: "Bumuo ng standard na table head (&lt;thead&gt;) at body (&lt;tbody&gt;) na istraktura"
        tableCaption: "Magdagdag ng descriptive title sa itaas ng table (&lt;caption&gt; element)"
        tableClass: "Magdagdag ng CSS class name sa table para sa madaling style customization"
        tableId: "Magtakda ng unique ID identifier para sa table para sa JavaScript manipulation"
      jira:
        escape: "I-escape ang pipe characters (|) para maiwasan ang conflicts sa Jira table syntax"
      json:
        parsingJSON: "Intelligent na pag-parse ng JSON strings sa cells into objects"
        minify: "Bumuo ng compact na single-line JSON format para mabawasan ang file size"
        format: "Piliin ang output JSON data structure: object array, 2D array, atbp."
      latex:
        escape: "Escape LaTeX special characters (%, &, _, #, $, atbp.) para masiguro ang proper compilation"
        ht: "Magdagdag ng floating position parameter [!ht] para kontrolin ang table position sa page"
        mwe: "Bumuo ng kumpletong LaTeX document"
        tableAlign: "Itakda ang horizontal alignment ng table sa page"
        tableBorder: "I-configure ang table border style: walang border, partial border, full border"
        label: "Itakda ang table label para sa \\ref{} command cross-referencing"
        caption: "Itakda ang table caption na ipapakita sa itaas o ibaba ng table"
        location: "Piliin ang table caption display position: itaas o ibaba"
        tableType: "Piliin ang table environment type: tabular, longtable, array, atbp."
      markdown:
        escape: "Escape Markdown special characters (*, _, |, \\, atbp.) para maiwasan ang format conflicts"
        pretty: "Auto-align column widths para makabuo ng mas magandang table format"
        simple: "Gamitin ang simplified syntax, tinatanggal ang outer border vertical lines"
        boldFirstRow: "Gawing bold ang first row text"
        boldFirstColumn: "Gawing bold ang first column text"
        firstHeader: "Tratuhin ang first row bilang header at magdagdag ng separator line"
        textAlign: "Itakda ang column text alignment: kaliwa, gitna, kanan"
        multilineHandling: "Multiline text handling: panatilihin ang line breaks, escape sa \\n, gamitin ang &lt;br&gt; tags"

        includeLineNumbers: "Magdagdag ng line number column sa kaliwang bahagi ng table"
      magic:
        builtin: "Piliin ang predefined common template formats"
        rowsTpl: "<table> <tr> <th>Magic Syntax</th> <th>Paglalarawan</th> <th>Suportadong JS Methods</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>Ika-1, ika-2 ... field ng <b>h</b>eading, Aka {hA} {hB} ...</td> <td>String methods</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>Ika-1, ika-2 ... field ng kasalukuyang row, Aka {$A} {$B} ...</td> <td>String methods</td> </tr> <tr> <td>{F,} {F;}</td> <td>Hatiin ang kasalukuyang row sa pamamagitan ng string pagkatapos ng <b>F</b></td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td><b>N</b>umero ng linya ng kasalukuyang <b>R</b>ow mula sa 1 o 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>H</b>uling numero ng linya ng mga <b>R</b>ow </td> <td></td> </tr> <tr> <td>{x ...}</td> <td><b>I</b>sagawa ang JavaScript code, hal: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Gamitin ang backslash <b>\\</b> para sa output ng braces {...} </td> <td></td> </tr></table>"
        headerTpl: "Custom output template para sa header section"
        footerTpl: "Custom output template para sa footer section"
      textile:
        escape: "Escape Textile syntax characters (|, ., -, ^) para maiwasan ang format conflicts"
        rowHeader: "Itakda ang first row bilang header row"
        thead: "Magdagdag ng Textile syntax markers para sa table head at body"
      xml:
        escape: "I-escape ang mga special character ng XML (&lt;, &gt;, &amp;, \\, ') para masiguro ang valid XML"
        minify: "Bumuo ng compressed XML output, tinatanggal ang extra whitespace"
        rootElement: "Itakda ang XML root element tag name"
        rowElement: "Itakda ang XML element tag name para sa bawat row ng data"
        declaration: "Magdagdag ng XML declaration header (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "I-output ang data bilang XML attributes sa halip na child elements"
        cdata: "I-wrap ang text content gamit ang CDATA para protektahan ang special characters"
        encoding: "Itakda ang character encoding format para sa XML document"
        indentation: "Piliin ang XML indentation character: spaces o tabs"
      yaml:
        indentSize: "Itakda ang bilang ng spaces para sa YAML hierarchy indentation (karaniwang 2 o 4)"
        arrayStyle: "Array format: block (isang item bawat linya) o flow (inline format)"
        quotationStyle: "String quote style: walang quotes, single quotes, double quotes"
      csv:
        bom: "Magdagdag ng UTF-8 byte order mark para tulungang makilala ng Excel at iba pang software ang encoding"
      excel:
        autoWidth: "Awtomatikong i-adjust ang column width base sa content"
        protectSheet: "I-enable ang worksheet protection na may password: tableconvert.com"
      sql:
        primaryKey: "Tukuyin ang primary key field name para sa CREATE TABLE statement"
        dialect: "Piliin ang database type, nakakaapekto sa quote at data type syntax"
      ascii:
        forceSep: "Pilitin ang separator lines sa pagitan ng bawat row ng data"
        style: "Piliin ang ASCII table border drawing style"
        comment: "Magdagdag ng comment markers para i-wrap ang buong table"
      mediawiki:
        minify: "I-compress ang output code, tinatanggal ang extra whitespace"
        header: "Markahan ang first row bilang header style"
        sort: "I-enable ang table click sorting functionality"
      asciidoc:
        minify: "I-compress ang AsciiDoc format output"
        firstHeader: "Itakda ang first row bilang header row"
        lastFooter: "Itakda ang last row bilang footer row"
        title: "Magdagdag ng title text sa table"
      tracwiki:
        rowHeader: "Itakda ang first row bilang header"
        colHeader: "Itakda ang first column bilang header"
      bbcode:
        minify: "I-compress ang BBCode output format"
      restructuredtext:
        style: "Piliin ang reStructuredText table border style"
        forceSep: "Pilitin ang separator lines"
      pdf:
        theme: "Piliin ang PDF table visual style para sa professional documents"
        headerColor: "Piliin ang header background color para sa PDF tables"
        showHead: "Kontrolin ang header display sa PDF pages"
        docTitle: "Optional na title para sa PDF document"
        docDescription: "Optional na description text para sa PDF document"
    label:
      ascii:
        forceSep: "Mga Separator ng Row"
        style: "Estilo ng Border"
        comment: "Balot ng Komento"
      restructuredtext:
        style: "Istilo ng Hangganan"
        forceSep: "Pilitin ang Separators"
      bbcode:
        minify: "I-minify ang Output"
      csv:
        doubleQuote: "Balot ng Dobleng Panipi"
        delimiter: "Delimiter ng Field"
        bom: "UTF-8 BOM"
        valueDelimiter: "Delimiter ng Halaga"
        rowDelimiter: "Delimiter ng Hilera"
        prefix: "Prefix ng Hilera"
        suffix: "Suffix ng Hilera"
      excel:
        autoWidth: "Awtomatikong Lapad"
        textFormat: "Format ng Teksto"
        protectSheet: "Protektahan ang Sheet"
        boldFirstRow: "Bold ang Unang Hilera"
        boldFirstColumn: "Bold ang Unang Kolum"
        sheetName: "Pangalan ng Sheet"
      html:
        escape: "I-escape ang mga HTML Character"
        div: "DIV Table"
        minify: "Paliitin ang Code"
        thead: "Istraktura ng Table Head"
        tableCaption: "Caption ng Table"
        tableClass: "Class ng Table"
        tableId: "ID ng Table"
        rowHeader: "Header ng Hilera"
        colHeader: "Header ng Kolum"
      jira:
        escape: "I-escape ang mga Character"
        rowHeader: "Header ng Hilera"
        colHeader: "Header ng Kolum"
      json:
        parsingJSON: "I-parse ang JSON"
        minify: "I-minify ang Output"
        format: "Format ng Data"
        rootName: "Pangalan ng Root Object"
        indentSize: "Laki ng Indent"
      jsonlines:
        parsingJSON: "I-parse ang JSON"
        format: "Format ng Data"
      latex:
        escape: "I-escape ang mga LaTeX Table Character"
        ht: "Posisyon ng Float"
        mwe: "Kumpletong Dokumento"
        tableAlign: "Alignment ng Table"
        tableBorder: "Istilo ng Hangganan"
        label: "Label ng Reference"
        caption: "Caption ng Table"
        location: "Posisyon ng Caption"
        tableType: "Uri ng Table"
        boldFirstRow: "Bold ang Unang Hilera"
        boldFirstColumn: "Bold ang Unang Kolum"
        textAlign: "Alignment ng Teksto"
        borders: "Settings ng Hangganan"
      markdown:
        escape: "I-escape ang mga Character"
        pretty: "Magandang Markdown Table"
        simple: "Simpleng Markdown Format"
        boldFirstRow: "Bold ang Unang Hilera"
        boldFirstColumn: "Bold ang Unang Kolum"
        firstHeader: "Unang Header"
        textAlign: "Alignment ng Teksto"
        multilineHandling: "Pag-handle ng Multiline"

        includeLineNumbers: "Magdagdag ng Line Numbers"
        align: "Alignment"
      mediawiki:
        minify: "Paliitin ang Code"
        header: "Markup ng Header"
        sort: "Maaaring I-sort"
      asciidoc:
        minify: "Paliitin ang Format"
        firstHeader: "Unang Header"
        lastFooter: "Huling Footer"
        title: "Titulo ng Table"
      tracwiki:
        rowHeader: "Header ng Hilera"
        colHeader: "Header ng Kolum"
      sql:
        drop: "I-drop ang Table (Kung Mayroon)"
        create: "Gumawa ng Table"
        oneInsert: "Batch Insert"
        table: "Pangalan ng Table"
        dialect: "Uri ng Database"
        primaryKey: "Primary Key"
      magic:
        builtin: "Built-in na Template"
        rowsTpl: "Template ng Hilera, Syntax ->"
        headerTpl: "Template ng Header"
        footerTpl: "Template ng Footer"
      textile:
        escape: "I-escape ang mga Character"
        rowHeader: "Header ng Hilera"
        thead: "Syntax ng Table Head"
      xml:
        escape: "I-escape ang mga XML Character"
        minify: "I-minify ang Output"
        rootElement: "Root Element"
        rowElement: "Element ng Hilera"
        declaration: "XML Declaration"
        attributes: "Attribute Mode"
        cdata: "CDATA Wrapper"
        encoding: "Encoding"
        indentSize: "Laki ng Indent"
      yaml:
        indentSize: "Laki ng Indent"
        arrayStyle: "Istilo ng Array"
        quotationStyle: "Istilo ng Panipi"
      pdf:
        theme: "Theme ng PDF Table"
        headerColor: "Kulay ng PDF Header"
        showHead: "Display ng PDF Header"
        docTitle: "Titulo ng PDF Document"
        docDescription: "Paglalarawan ng PDF Document"

sidebar:
  all: "Lahat ng Conversion Tools"
  dataSource:
    title: "Pinagmulan ng Data"
    description:
      converter: "Mag-import ng %s para sa conversion sa %s. Sumusuporta sa file upload, online editing, at web data extraction."
      generator: "Lumikha ng table data na may suporta sa maraming input methods kasama ang manual input, file import, at template generation."
  tableEditor:
    title: "Online Table Editor"
    description:
      converter: "I-process ang %s online gamit ang aming table editor. Excel-like na operation experience na may suporta sa pagtanggal ng empty rows, deduplication, sorting, at find & replace."
      generator: "Malakas na online table editor na nagbibigay ng Excel-like na operation experience. Sumusuporta sa pagtanggal ng empty rows, deduplication, sorting, at find & replace."
  tableGenerator:
    title: "Table Generator"
    description:
      converter: "Mabilis na mag-generate ng %s na may real-time preview ng table generator. Maraming export options, one-click copy & download."
      generator: "Mag-export ng %s data sa maraming format para sa iba't ibang usage scenarios. Sumusuporta sa custom options at real-time preview."
footer:
  changelog: "Changelog"
  sponsor: "Mga Sponsor"
  contact: "Makipag-ugnayan sa Amin"
  privacyPolicy: "Patakaran sa Privacy"
  about: "Tungkol"
  resources: "Mga Resources"
  popularConverters: "Mga Sikat na Converter"
  popularGenerators: "Mga Sikat na Generator"
  dataSecurity: "Secure ang inyong data - lahat ng conversion ay tumatakbo sa inyong browser."
converters:
  Markdown:
    alias: "Markdown Table"
    what: "Ang Markdown ay isang magaan na markup language na malawakang ginagamit para sa technical documentation, paglikha ng blog content, at web development. Ang table syntax nito ay maikli at intuitive, sumusuporta sa text alignment, link embedding, at formatting. Ito ang piniling tool ng mga programmer at technical writer, perpektong compatible sa GitHub, GitLab, at iba pang code hosting platform."
    step1: "I-paste ang Markdown table data sa data source area, o direktang i-drag at i-drop ang .md files para sa upload. Ang tool ay awtomatikong nag-parse ng table structure at formatting, sumusuporta sa complex nested content at special character handling."
    step3: "Mag-generate ng standard Markdown table code nang real-time, sumusuporta sa multiple alignment methods, text bolding, line number addition, at iba pang advanced format settings. Ang generated code ay fully compatible sa GitHub at major Markdown editors, ready to use sa one-click copy."
    from_alias: "Markdown Table File"
    to_alias: "Markdown Table Format"
  Magic:
    alias: "Custom Template"
    what: "Ang Magic template ay isang natatanging advanced data generator ng tool na ito, nagbibigay-daan sa mga user na lumikha ng arbitrary format data output sa pamamagitan ng custom template syntax. Sumusuporta sa variable replacement, conditional judgment, at loop processing. Ito ang ultimate solution para sa pag-handle ng complex data conversion needs at personalized output formats, lalo na para sa mga developer at data engineer."
    step1: "Pumili ng built-in common templates o lumikha ng custom template syntax. Sumusuporta sa rich variables at functions na kaya mag-handle ng complex data structures at business logic."
    step3: "Mag-generate ng data output na lubos na nakakatugon sa custom format requirements. Sumusuporta sa complex data conversion logic at conditional processing, malaking pagpapabuti sa data processing efficiency at output quality. Isang powerful tool para sa batch data processing."
    from_alias: "Table Data"
    to_alias: "Custom Format Output"
  CSV:
    alias: "CSV"
    what: "Ang CSV (Comma-Separated Values) ay ang pinaka-malawakang ginagamit na data exchange format, perpektong suportado ng Excel, Google Sheets, database systems, at iba't ibang data analysis tools. Ang simple structure at malakas na compatibility nito ay ginagawa itong standard format para sa data migration, batch import/export, at cross-platform data exchange, malawakang ginagamit sa business analysis, data science, at system integration."
    step1: "Mag-upload ng CSV files o direktang i-paste ang CSV data. Ang tool ay intelligently nag-recognize ng iba't ibang delimiters (comma, tab, semicolon, pipe, atbp.), awtomatikong nag-detect ng data types at encoding formats, sumusuporta sa mabilis na parsing ng malalaking files at complex data structures."
    step3: "Mag-generate ng standard CSV format files na may suporta para sa custom delimiters, quote styles, encoding formats, at BOM mark settings. Nagsisiguro ng perpektong compatibility sa target systems, nagbibigay ng download at compression options para matugunan ang enterprise-level data processing needs."
    from_alias: "CSV Data File"
    to_alias: "Standard na CSV Format"
  JSON:
    alias: "JSON Array"
    what: "Ang JSON (JavaScript Object Notation) ay ang standard table data format para sa modern web applications, REST APIs, at microservice architectures. Ang malinaw na structure at efficient parsing nito ay ginagawa itong malawakang ginagamit sa front-end at back-end data interaction, configuration file storage, at NoSQL databases. Sumusuporta sa nested objects, array structures, at multiple data types, ginagawa itong indispensable table data para sa modern software development."
    step1: "Mag-upload ng JSON files o i-paste ang JSON arrays. Sumusuporta sa automatic recognition at parsing ng object arrays, nested structures, at complex data types. Ang tool ay intelligently nag-validate ng JSON syntax at nagbibigay ng error prompts."
    step3: "Mag-generate ng multiple JSON format outputs: standard object arrays, 2D arrays, column arrays, at key-value pair formats. Sumusuporta sa beautified output, compression mode, custom root object names, at indentation settings, perpektong umaangkop sa iba't ibang API interfaces at data storage needs."
    from_alias: "JSON Array File"
    to_alias: "Standard na JSON Format"
  JSONLines:
    alias: "JSONLines Format"
    what: "Ang JSON Lines (kilala rin bilang NDJSON) ay isang mahalagang format para sa big data processing at streaming data transmission, na may bawat linya na naglalaman ng independent JSON object. Malawakang ginagamit sa log analysis, data stream processing, machine learning, at distributed systems. Sumusuporta sa incremental processing at parallel computing, ginagawa itong ideal na pagpipilian para sa pag-handle ng large-scale structured data."
    step1: "Mag-upload ng JSONLines files o i-paste ang data. Ang tool ay nag-parse ng JSON objects line by line, sumusuporta sa large file streaming processing at error line skipping functionality."
    step3: "Mag-generate ng standard JSONLines format na may bawat linya na nag-output ng complete JSON object. Angkop para sa streaming processing, batch import, at big data analysis scenarios, sumusuporta sa data validation at format optimization."
    from_alias: "JSONLines Data"
    to_alias: "JSONLines Streaming Format"
  XML:
    alias: "XML"
    what: "Ang XML (eXtensible Markup Language) ay ang standard format para sa enterprise-level data exchange at configuration management, na may strict syntax specifications at powerful validation mechanisms. Malawakang ginagamit sa web services, configuration files, document storage, at system integration. Sumusuporta sa namespaces, schema validation, at XSLT transformation, ginagawa itong mahalagang table data para sa enterprise applications."
    step1: "Mag-upload ng XML files o i-paste ang XML data. Ang tool ay awtomatikong nag-parse ng XML structure at nag-convert nito sa table format, sumusuporta sa namespace, attribute handling, at complex nested structures."
    step3: "Mag-generate ng XML output na sumusunod sa XML standards. Sumusuporta sa custom root elements, row element names, attribute modes, CDATA wrapping, at character encoding settings. Nagsisiguro ng data integrity at compatibility, nakakatugon sa enterprise-level application requirements."
    from_alias: "XML Data File"
    to_alias: "Standard na XML Format"
  YAML:
    alias: "YAML Configuration"
    what: "Ang YAML ay isang human-friendly data serialization standard, kilala sa malinaw na hierarchical structure at concise syntax nito. Malawakang ginagamit sa configuration files, DevOps tool chains, Docker Compose, at Kubernetes deployment. Ang malakas na readability at concise syntax nito ay ginagawa itong mahalagang configuration format para sa modern cloud-native applications at automated operations."
    step1: "Mag-upload ng YAML files o i-paste ang YAML data. Ang tool ay intelligently nag-parse ng YAML structure at nag-validate ng syntax correctness, sumusuporta sa multi-document formats at complex data types."
    step3: "Mag-generate ng standard YAML format output na may suporta para sa block at flow array styles, multiple quote settings, custom indentation, at comment preservation. Nagsisiguro na ang output YAML files ay fully compatible sa iba't ibang parsers at configuration systems."
    from_alias: "YAML Configuration File"
    to_alias: "Standard na YAML Format"
  MySQL:
      alias: "Mga Resulta ng MySQL Query"
      what: "Ang MySQL ay ang pinaka-popular na open-source relational database management system sa mundo, kilala sa mataas na performance, reliability, at ease of use. Malawakang ginagamit sa web applications, enterprise systems, at data analysis platforms. Ang MySQL query results ay karaniwang naglalaman ng structured table data, nagsisilbi bilang mahalagang data source sa database management at data analysis work."
      step1: "I-paste ang MySQL query output results sa data source area. Ang tool ay awtomatikong nag-recognize at nag-parse ng MySQL command-line output format, sumusuporta sa iba't ibang query result styles at character encodings, intelligently nag-handle ng headers at data rows."
      step3: "Mabilis na i-convert ang MySQL query results sa multiple table data formats, napadali ang data analysis, report generation, cross-system data migration, at data validation. Isang practical tool para sa database administrators at data analysts."
      from_alias: "Output ng MySQL Query"
      to_alias: "MySQL Table Data"
  SQL:
    alias: "Insert SQL"
    what: "Ang SQL (Structured Query Language) ay ang standard operation language para sa relational databases, ginagamit para sa data query, insert, update, at delete operations. Bilang core technology ng database management, ang SQL ay malawakang ginagamit sa data analysis, business intelligence, ETL processing, at data warehouse construction. Ito ay essential skill tool para sa data professionals."
    step1: "I-paste ang INSERT SQL statements o mag-upload ng .sql files. Ang tool ay intelligently nag-parse ng SQL syntax at nag-extract ng table data, sumusuporta sa multiple SQL dialects at complex query statement processing."
    step3: "Mag-generate ng standard SQL INSERT statements at table creation statements. Sumusuporta sa multiple database dialects (MySQL, PostgreSQL, SQLite, SQL Server, Oracle), awtomatikong nag-handle ng data type mapping, character escaping, at primary key constraints. Nagsisiguro na ang generated SQL code ay maaaring ma-execute nang direkta."
    from_alias: "Insert SQL"
    to_alias: "SQL Statement"
  Qlik:
      alias: "Qlik Table"
      what: "Ang Qlik ay isang software vendor na nag-specialize sa data visualization, executive dashboards, at self-service business intelligence products, kasama ang Tableau at Microsoft."
      step1: ""
      step3: "Sa wakas, ang [Table Generator](#TableGenerator) ay nagpapakita ng conversion results. Gamitin sa inyong Qlik Sense, Qlik AutoML, QlikView, o iba pang Qlik-enabled software."
      from_alias: "Qlik Table"
      to_alias: "Qlik Table"
  DAX:
      alias: "DAX Table"
      what: "Ang DAX (Data Analysis Expressions) ay isang programming language na ginagamit sa buong Microsoft Power BI para sa paglikha ng calculated columns, measures, at custom tables."
      step1: ""
      step3: "Sa wakas, ang [Table Generator](#TableGenerator) ay nagpapakita ng conversion results. Tulad ng inaasahan, ginagamit ito sa ilang Microsoft products kabilang ang Microsoft Power BI, Microsoft Analysis Services, at Microsoft Power Pivot para sa Excel."
      from_alias: "DAX Table"
      to_alias: "DAX Table"
  Firebase:
    alias: "Firebase List"
    what: "Ang Firebase ay isang BaaS application development platform na nagbibigay ng hosted backend services tulad ng real-time database, cloud storage, authentication, crash reporting, atbp."
    step1: ""
    step3: "Sa wakas, ang [Table Generator](#TableGenerator) ay nagpapakita ng conversion results. Maaari mo nang gamitin ang push method sa Firebase API para magdagdag sa list ng data sa Firebase database."
    from_alias: "Firebase List"
    to_alias: "Firebase List"
  HTML:
    alias: "HTML Table"
    what: "Ang HTML tables ay ang standard na paraan para ipakita ang structured data sa web pages, ginawa gamit ang table, tr, td at iba pang tags. Sumusuporta sa rich style customization, responsive layout, at interactive functionality. Malawakang ginagamit sa website development, data display, at report generation, nagsisilbi bilang mahalagang component ng front-end development at web design."
    step1: "I-paste ang HTML code na naglalaman ng tables o mag-upload ng HTML files. Ang tool ay awtomatikong nag-recognize at nag-extract ng table data mula sa pages, sumusuporta sa complex HTML structures, CSS styles, at nested table processing."
    step3: "Mag-generate ng semantic HTML table code na may suporta para sa thead/tbody structure, CSS class settings, table captions, row/column headers, at responsive attribute configuration. Nagsisiguro na ang generated table code ay nakakatugon sa web standards na may magandang accessibility at SEO friendliness."
    from_alias: "HTML Table"
    to_alias: "HTML Table"
  Excel:
    alias: "Excel"
    what: "Ang Microsoft Excel ay ang pinaka-popular na spreadsheet software sa mundo, malawakang ginagamit sa business analysis, financial management, data processing, at report creation. Ang powerful data processing capabilities, rich function library, at flexible visualization features nito ay ginagawa itong standard tool para sa office automation at data analysis, na may malawakang applications sa halos lahat ng industries at fields."
    step1: "Mag-upload ng Excel files (sumusuporta sa .xlsx, .xls formats) o direktang kopyahin ang table data mula sa Excel at i-paste. Ang tool ay sumusuporta sa multi-worksheet processing, complex format recognition, at mabilis na parsing ng malalaking files, awtomatikong nag-handle ng merged cells at data types."
    step3: "Mag-generate ng Excel-compatible table data na maaaring direktang i-paste sa Excel o ma-download bilang standard .xlsx files. Sumusuporta sa worksheet naming, cell formatting, auto column width, header styling, at data validation settings. Nagsisiguro na ang output Excel files ay may professional appearance at complete functionality."
    from_alias: "Excel Spreadsheet"
    to_alias: "Excel"
  LaTeX:
    alias: "LaTeX Table"
    what: "Ang LaTeX ay isang professional document typesetting system, lalo na angkop para sa paglikha ng academic papers, technical documents, at scientific publications. Ang table functionality nito ay powerful, sumusuporta sa complex mathematical formulas, precise layout control, at high-quality PDF output. Ito ang standard tool sa academia at scientific publishing, malawakang ginagamit sa journal papers, dissertations, at technical manual typesetting."
    step1: "I-paste ang LaTeX table code o mag-upload ng .tex files. Ang tool ay nag-parse ng LaTeX table syntax at nag-extract ng data content, sumusuporta sa multiple table environments (tabular, longtable, array, atbp.) at complex format commands."
    step3: "Mag-generate ng professional LaTeX table code na may suporta para sa multiple table environment selection, border style configuration, caption position settings, document class specification, at package management. Maaaring mag-generate ng complete compilable LaTeX documents, nagsisiguro na ang output tables ay nakakatugon sa academic publishing standards."
    from_alias: "LaTeX Table"
    to_alias: "LaTeX Table"
  ASCII:
    alias: "ASCII Text Table"
    what: "Ang ASCII tables ay gumagamit ng plain text characters para gumuhit ng table borders at structures, nagbibigay ng pinakamahusay na compatibility at portability. Compatible sa lahat ng text editors, terminal environments, at operating systems. Malawakang ginagamit sa code documentation, technical manuals, README files, at command-line tool output. Ang piniling data display format ng mga programmer at system administrators."
    step1: "Mag-upload ng text files na naglalaman ng ASCII tables o direktang i-paste ang table data. Ang tool ay intelligently nag-recognize at nag-parse ng ASCII table structures, sumusuporta sa multiple border styles at alignment formats."
    step3: "Mag-generate ng magagandang plain text ASCII tables na may suporta para sa multiple border styles (single line, double line, rounded corners, atbp.), text alignment methods, at auto column width. Ang generated tables ay perpektong nagdi-display sa code editors, documents, at command lines."
    from_alias: "ASCII Text Table"
    to_alias: "ASCII Text Table"
  MediaWiki:
    alias: "MediaWiki Table"
    what: "Ang MediaWiki ay ang open-source software platform na ginagamit ng mga sikat na wiki sites tulad ng Wikipedia. Ang table syntax nito ay concise ngunit powerful, sumusuporta sa table style customization, sorting functionality, at link embedding. Malawakang ginagamit sa knowledge management, collaborative editing, at content management systems, nagsisilbi bilang core technology para sa pagbuo ng wiki encyclopedias at knowledge bases."
    step1: "I-paste ang MediaWiki table code o mag-upload ng wiki source files. Ang tool ay nag-parse ng wiki markup syntax at nag-extract ng table data, sumusuporta sa complex wiki syntax at template processing."
    step3: "Mag-generate ng standard MediaWiki table code na may suporta para sa header style settings, cell alignment, sorting functionality enabling, at code compression options. Ang generated code ay maaaring direktang gamitin para sa wiki page editing, nagsisiguro ng perpektong display sa MediaWiki platforms."
    from_alias: "MediaWiki Table"
    to_alias: "MediaWiki Table"
  TracWiki:
    alias: "TracWiki Table"
    what: "Ang Trac ay isang web-based project management at bug tracking system na gumagamit ng simplified wiki syntax para lumikha ng table content."
    step1: "Mag-upload ng TracWiki files o i-paste ang table data."
    step3: "Mag-generate ng TracWiki-compatible table code na may suporta para sa row/column header settings, napadali ang project document management."
    from_alias: "TracWiki Table"
    to_alias: "TracWiki Table"
  AsciiDoc:
    alias: "AsciiDoc Table"
    what: "Ang AsciiDoc ay isang lightweight markup language na maaaring ma-convert sa HTML, PDF, manual pages, at iba pang formats, malawakang ginagamit para sa technical documentation writing."
    step1: "Mag-upload ng AsciiDoc files o i-paste ang data."
    step3: "Mag-generate ng AsciiDoc table syntax na may suporta para sa header, footer, at title settings, direktang magagamit sa AsciiDoc editors."
    from_alias: "AsciiDoc Table"
    to_alias: "AsciiDoc Table"
  reStructuredText:
    alias: "reStructuredText Table"
    what: "Ang reStructuredText ay ang standard documentation format para sa Python community, sumusuporta sa rich table syntax, karaniwang ginagamit para sa Sphinx documentation generation."
    step1: "Mag-upload ng .rst files o i-paste ang reStructuredText data."
    step3: "Mag-generate ng standard reStructuredText tables na may suporta para sa multiple border styles, direktang magagamit sa Sphinx documentation projects."
    from_alias: "reStructuredText Table"
    to_alias: "reStructuredText Table"
  PHP:
    alias: "PHP Array"
    what: "Ang PHP ay isang popular na server-side scripting language, na may arrays bilang core data structure nito, malawakang ginagamit sa web development at data processing."
    step1: "Mag-upload ng files na naglalaman ng PHP arrays o direktang i-paste ang data."
    step3: "Mag-generate ng standard PHP array code na maaaring direktang gamitin sa PHP projects, sumusuporta sa associative at indexed array formats."
    from_alias: "PHP Array"
    to_alias: "PHP Code"
  Ruby:
    alias: "Ruby Array"
    what: "Ang Ruby ay isang dynamic object-oriented programming language na may concise at elegant na syntax, na may arrays bilang mahalagang data structure."
    step1: "Mag-upload ng Ruby files o i-paste ang array data."
    step3: "Mag-generate ng Ruby array code na sumusunod sa Ruby syntax specifications, direktang magagamit sa Ruby projects."
    from_alias: "Ruby Array"
    to_alias: "Ruby Code"
  ASP:
    alias: "ASP Array"
    what: "Ang ASP (Active Server Pages) ay Microsoft's server-side scripting environment, sumusuporta sa multiple programming languages para sa pagde-develop ng dynamic web pages."
    step1: "Mag-upload ng ASP files o i-paste ang array data."
    step3: "Mag-generate ng ASP-compatible array code na may suporta para sa VBScript at JScript syntax, magagamit sa ASP.NET projects."
    from_alias: "ASP Array"
    to_alias: "ASP Code"
  ActionScript:
    alias: "ActionScript Array"
    what: "Ang ActionScript ay isang object-oriented programming language na pangunahing ginagamit para sa Adobe Flash at AIR application development."
    step1: "Mag-upload ng .as files o i-paste ang ActionScript data."
    step3: "Mag-generate ng ActionScript array code na sumusunod sa AS3 syntax standards, magagamit para sa Flash at Flex project development."
    from_alias: "ActionScript Array"
    to_alias: "ActionScript Code"
  BBCode:
    alias: "BBCode Table"
    what: "Ang BBCode ay isang lightweight markup language na karaniwang ginagamit sa forums at online communities, nagbibigay ng simple formatting functionality kasama ang table support."
    step1: "Mag-upload ng files na naglalaman ng BBCode o i-paste ang data."
    step3: "Mag-generate ng BBCode table code na angkop para sa forum posting at community content creation, na may suporta para sa compressed output format."
    from_alias: "BBCode Table"
    to_alias: "BBCode Table"
  PDF:
    alias: "PDF Table"
    what: "Ang PDF (Portable Document Format) ay isang cross-platform document standard na may fixed layout, consistent display, at high-quality printing characteristics. Malawakang ginagamit sa formal documents, reports, invoices, contracts, at academic papers. Ang piniling format para sa business communication at document archiving, nagsisiguro ng lubos na consistent na visual effects sa iba't ibang devices at operating systems."
    step1: "Mag-import ng table data sa anumang format. Ang tool ay awtomatikong nag-aanalyze ng data structure at nagsasagawa ng intelligent layout design, sumusuporta sa large table auto-pagination at complex data type processing."
    step3: "Mag-generate ng high-quality PDF table files na may suporta para sa multiple professional theme styles (business, academic, minimalist, atbp.), multilingual fonts, auto-pagination, watermark addition, at print optimization. Nagsisiguro na ang output PDF documents ay may professional appearance, direktang magagamit para sa business presentations at formal publication."
    from_alias: "Table Data"
    to_alias: "PDF Table"
  JPEG:
    alias: "JPEG Image"
    what: "Ang JPEG ay ang pinaka-malawakang ginagamit na digital image format na may excellent compression effects at broad compatibility. Ang maliit na file size at mabilis na loading speed nito ay ginagawa itong angkop para sa web display, social media sharing, document illustrations, at online presentations. Ang standard image format para sa digital media at network communication, perpektong suportado ng halos lahat ng devices at software."
    step1: "Mag-import ng table data sa anumang format. Ang tool ay nagsasagawa ng intelligent layout design at visual optimization, awtomatikong kinakalkula ang optimal size at resolution."
    step3: "Mag-generate ng high-definition JPEG table images na may suporta para sa multiple theme color schemes (light, dark, eye-friendly, atbp.), adaptive layout, text clarity optimization, at size customization. Angkop para sa online sharing, document insertion, at presentation use, nagsisiguro ng excellent visual effects sa iba't ibang display devices."
    from_alias: "Table Data"
    to_alias: "JPEG Image"
  Jira:
    alias: "Jira Table"
    what: "Ang JIRA ay professional project management at bug tracking software na ginawa ng Atlassian, malawakang ginagamit sa agile development, software testing, at project collaboration. Ang table functionality nito ay sumusuporta sa rich formatting options at data display, nagsisilbi bilang mahalagang tool para sa software development teams, project managers, at quality assurance personnel sa requirement management, bug tracking, at progress reporting."
    step1: "Mag-upload ng files na naglalaman ng table data o direktang i-paste ang data content. Ang tool ay awtomatikong nagpo-process ng table data at special character escaping."
    step3: "Mag-generate ng JIRA platform-compatible table code na may suporta para sa header style settings, cell alignment, character escape processing, at format optimization. Ang generated code ay maaaring direktang i-paste sa JIRA issue descriptions, comments, o wiki pages, nagsisiguro ng tamang display at rendering sa JIRA systems."
    from_alias: "Jira Table"
    to_alias: "Jira Table"
  Textile:
    alias: "Textile Table"
    what: "Ang Textile ay isang concise lightweight markup language na may simple at easy-to-learn na syntax, malawakang ginagamit sa content management systems, blog platforms, at forum systems. Ang table syntax nito ay malinaw at intuitive, sumusuporta sa mabilis na formatting at style settings. Isang ideal na tool para sa content creators at website administrators para sa mabilis na document writing at content publishing."
    step1: "Mag-upload ng Textile format files o i-paste ang table data. Ang tool ay nag-parse ng Textile markup syntax at nag-extract ng table content."
    step3: "Mag-generate ng standard Textile table syntax na may suporta para sa header markup, cell alignment, special character escaping, at format optimization. Ang generated code ay maaaring direktang gamitin sa CMS systems, blog platforms, at document systems na sumusuporta sa Textile, nagsisiguro ng tamang content rendering at display."
    from_alias: "Textile Table"
    to_alias: "Textile Table"
  PNG:
    alias: "PNG Image"
    what: "Ang PNG (Portable Network Graphics) ay isang lossless image format na may excellent compression at transparency support. Malawakang ginagamit sa web design, digital graphics, at professional photography. Ang mataas na kalidad at malawakang compatibility nito ay ginagawa itong ideal para sa screenshots, logos, diagrams, at anumang images na nangangailangan ng crisp details at transparent backgrounds."
    step1: "Mag-import ng table data sa anumang format. Ang tool ay nagsasagawa ng intelligent layout design at visual optimization, awtomatikong kinakalkula ang optimal size at resolution para sa PNG output."
    step3: "Mag-generate ng high-quality PNG table images na may suporta para sa multiple theme color schemes, transparent backgrounds, adaptive layout, at text clarity optimization. Perpekto para sa web use, document insertion, at professional presentations na may excellent visual quality."
    from_alias: ""
    to_alias: "PNG Image"
  TOML:
    alias: "TOML"
    what: "Ang TOML (Tom's Obvious, Minimal Language) ay isang configuration file format na madaling basahin at isulat. Dinisenyo na maging walang pagkakalito at simple, malawakang ginagamit sa modern software projects para sa configuration management. Ang malinaw na syntax at malakas na typing nito ay ginagawa itong excellent na pagpipilian para sa application settings at project configuration files."
    step1: "Mag-upload ng TOML files o i-paste ang configuration data. Ang tool ay nag-parse ng TOML syntax at nag-extract ng structured configuration information."
    step3: "Mag-generate ng standard TOML format na may suporta para sa nested structures, data types, at comments. Ang generated TOML files ay perpekto para sa application configuration, build tools, at project settings."
    from_alias: "TOML"
    to_alias: "TOML Format"
  INI:
    alias: "INI"
    what: "Ang INI files ay simple configuration files na ginagamit ng maraming applications at operating systems. Ang straightforward key-value pair structure nila ay ginagawa silang madaling basahin at i-edit nang manual. Malawakang ginagamit sa Windows applications, legacy systems, at simple configuration scenarios kung saan ang human readability ay mahalaga."
    step1: "Mag-upload ng INI files o i-paste ang configuration data. Ang tool ay nag-parse ng INI syntax at nag-extract ng section-based configuration information."
    step3: "Mag-generate ng standard INI format na may suporta para sa sections, comments, at iba't ibang data types. Ang generated INI files ay compatible sa karamihan ng applications at configuration systems."
    from_alias: "INI"
    to_alias: "INI Format"
  Avro:
    alias: "Avro Schema"
    what: "Ang Apache Avro ay isang data serialization system na nagbibigay ng rich data structures, compact binary format, at schema evolution capabilities. Malawakang ginagamit sa big data processing, message queues, at distributed systems. Ang schema definition nito ay sumusuporta sa complex data types at version compatibility, ginagawa itong mahalagang tool para sa data engineers at system architects."
    step1: "Mag-upload ng Avro schema files o i-paste ang data. Ang tool ay nag-parse ng Avro schema definitions at nag-extract ng table structure information."
    step3: "Mag-generate ng standard Avro schema definitions na may suporta para sa data type mapping, field constraints, at schema validation. Ang generated schemas ay maaaring direktang gamitin sa Hadoop ecosystems, Kafka message systems, at iba pang big data platforms."
    from_alias: "Avro Schema"
    to_alias: "Avro Schema"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Ang Protocol Buffers (protobuf) ay Google's language-neutral, platform-neutral, extensible mechanism para sa serializing structured data. Malawakang ginagamit sa microservices, API development, at data storage. Ang efficient binary format at malakas na typing nito ay ginagawa itong ideal para sa high-performance applications at cross-language communication."
    step1: "Mag-upload ng .proto files o i-paste ang Protocol Buffer definitions. Ang tool ay nag-parse ng protobuf syntax at nag-extract ng message structure information."
    step3: "Mag-generate ng standard Protocol Buffer definitions na may suporta para sa message types, field options, at service definitions. Ang generated .proto files ay maaaring ma-compile para sa multiple programming languages."
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf Schema"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Ang Pandas ay ang pinaka-popular na data analysis library sa Python, na may DataFrame bilang core data structure nito. Nagbibigay ito ng powerful data manipulation, cleaning, at analysis capabilities, malawakang ginagamit sa data science, machine learning, at business intelligence. Isang indispensable tool para sa Python developers at data analysts."
    step1: "Mag-upload ng Python files na naglalaman ng DataFrame code o i-paste ang data. Ang tool ay nag-parse ng Pandas syntax at nag-extract ng DataFrame structure information."
    step3: "Mag-generate ng standard Pandas DataFrame code na may suporta para sa data type specifications, index settings, at data operations. Ang generated code ay maaaring direktang ma-execute sa Python environment para sa data analysis at processing."
    from_alias: "Pandas DataFrame"
    to_alias: "Pandas DataFrame"
  RDF:
    alias: "RDF Triple"
    what: "Ang RDF (Resource Description Framework) ay isang standard model para sa data interchange sa Web, dinisenyo para mag-represent ng information tungkol sa resources sa graph form. Malawakang ginagamit sa semantic web, knowledge graphs, at linked data applications. Ang triple structure nito ay nagbibigay-daan sa rich metadata representation at semantic relationships."
    step1: "Mag-upload ng RDF files o i-paste ang triple data. Ang tool ay nag-parse ng RDF syntax at nag-extract ng semantic relationships at resource information."
    step3: "Mag-generate ng standard RDF format na may suporta para sa iba't ibang serializations (RDF/XML, Turtle, N-Triples). Ang generated RDF ay maaaring gamitin sa semantic web applications, knowledge bases, at linked data systems."
    from_alias: "RDF"
    to_alias: "RDF Triple"
  MATLAB:
    alias: "MATLAB Array"
    what: "Ang MATLAB ay isang high-performance numerical computing at visualization software na malawakang ginagamit sa engineering computing, data analysis, at algorithm development. Ang array at matrix operations nito ay powerful, sumusuporta sa complex mathematical calculations at data processing. Isang essential tool para sa engineers, researchers, at data scientists."
    step1: "Mag-upload ng MATLAB .m files o i-paste ang array data. Ang tool ay nag-parse ng MATLAB syntax at nag-extract ng array structure information."
    step3: "Mag-generate ng standard MATLAB array code na may suporta para sa multi-dimensional arrays, data type specifications, at variable naming. Ang generated code ay maaaring direktang ma-execute sa MATLAB environment para sa data analysis at scientific computing."
    from_alias: "MATLAB Array"
    to_alias: "MATLAB Array"
  RDataFrame:
    alias: "R DataFrame"
    what: "Ang R DataFrame ay ang core data structure sa R programming language, malawakang ginagamit sa statistical analysis, data mining, at machine learning. Ang R ay ang premier tool para sa statistical computing at graphics, na may DataFrame na nagbibigay ng powerful data manipulation, statistical analysis, at visualization capabilities. Essential para sa data scientists, statisticians, at researchers na gumagawa ng structured data analysis."
    step1: "Mag-upload ng R data files o i-paste ang DataFrame code. Ang tool ay nag-parse ng R syntax at nag-extract ng DataFrame structure information kasama ang column types, row names, at data content."
    step3: "Mag-generate ng standard R DataFrame code na may suporta para sa data type specifications, factor levels, row/column names, at R-specific data structures. Ang generated code ay maaaring direktang ma-execute sa R environment para sa statistical analysis at data processing."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Simulan ang Pag-convert"
  start_generating: "Simulan ang Pag-generate"
  api_docs: "API Docs"
related:
  section_title: 'Higit Pang {{ if and .from (ne .from "generator") }}{{ .from }} at {{ end }}{{ .to }} Converters'
  section_description: 'Tuklasin ang higit pang converters para sa {{ if and .from (ne .from "generator") }}{{ .from }} at {{ end }}{{ .to }} formats. I-transform ang inyong data sa pagitan ng maraming formats gamit ang aming professional online conversion tools.'
  title: "{{ .from }} sa {{ .to }}"
howto:
  step2: "I-edit ang data gamit ang aming advanced online table editor na may professional features. Sumusuporta sa pagtanggal ng empty rows, pag-aalis ng duplicates, data transposition, pag-sort, regex find & replace, at real-time preview. Lahat ng pagbabago ay automatic na nagiging %s format na may tumpak at maaasahang mga resulta."
  section_title: "Paano gamitin ang {{ . }}"
  converter_description: "Matuto mag-convert ng {{ .from }} sa {{ .to }} gamit ang aming step-by-step guide. Professional online converter na may advanced features at real-time preview."
  generator_description: "Matuto gumawa ng professional {{ .to }} tables gamit ang aming online generator. Excel-like editing, real-time preview, at instant export capabilities."
extension:
  section_title: "Extension para sa Table Detection at Extraction"
  section_description: "I-extract ang tables mula sa kahit anong website sa isang click. I-convert sa 30+ formats kasama ang Excel, CSV, JSON agad - walang copy-pasting na kailangan."
  features:
    extraction_title: "One-Click na Table Extraction"
    extraction_description: "Agad na i-extract ang tables mula sa kahit anong webpage nang walang copy-pasting - professional data extraction na ginawang simple"
    formats_title: "30+ Format Converter Support"
    formats_description: "I-convert ang na-extract na tables sa Excel, CSV, JSON, Markdown, SQL, at marami pa gamit ang aming advanced table converter"
    detection_title: "Smart na Table Detection"
    detection_description: "Automatic na nag-detect at nag-highlight ng tables sa kahit anong webpage para sa mabilis na data extraction at conversion"
  hover_tip: "✨ I-hover sa kahit anong table para makita ang extraction icon"
recommendations:
  section_title: "Inirekomenda ng mga Unibersidad at Propesyonal"
  section_description: "Ang TableConvert ay pinagkakatiwalaan ng mga propesyonal sa mga unibersidad, research institutions, at development teams para sa maaasahang table conversion at data processing."
  cards:
    university_title: "University of Wisconsin-Madison"
    university_description: "TableConvert.com - Professional libreng online table converter at data formats tool"
    university_link: "Basahin ang Artikulo"
    facebook_title: "Data Professional Community"
    facebook_description: "Ibinahagi at inirekomenda ng mga data analysts at propesyonal sa Facebook developer groups"
    facebook_link: "Tingnan ang Post"
    twitter_title: "Komunidad ng mga Developer"
    twitter_description: "Inirekomenda ni @xiaoying_eth at ibang developers sa X (Twitter) para sa table conversion"
    twitter_link: "Tingnan ang Tweet"
faq:
  section_title: "Mga Madalas na Tanong"
  section_description: "Mga karaniwang tanong tungkol sa aming libreng online table converter, data formats, at conversion process."
  what: "Ano ang %s format?"
  howto_convert:
    question: "Paano gamitin ang {{ . }} nang libre?"
    answer: "I-upload ang inyong {{ .from }} file, i-paste ang data, o i-extract mula sa web pages gamit ang aming libreng online table converter. Ang aming professional converter tool ay agad na nag-transform ng inyong data sa {{ .to }} format na may real-time preview at advanced editing features. I-download o i-copy ang converted result agad."
  security:
    question: "Secure ba ang aking data kapag ginagamit ko ang online converter na ito?"
    answer: "Oo talaga! Lahat ng table conversions ay nangyayari locally sa inyong browser - ang inyong data ay hindi kailanman umaalis sa inyong device. Ang aming online converter ay nag-process ng lahat sa client-side, na nagsisiguro ng kumpletong privacy at data security. Walang files na naka-store sa aming servers."
  free:
    question: "Talaga bang libre gamitin ang TableConvert?"
    answer: "Oo, ang TableConvert ay kumpletong libre! Lahat ng converter features, table editor, data generator tools, at export options ay available nang walang bayad, registration, o nakatagong fees. Mag-convert ng unlimited files online nang libre."
  filesize:
    question: "Ano ang file size limits ng online converter?"
    answer: "Ang aming libreng online table converter ay sumusuporta sa files hanggang 10MB. Para sa mas malalaking files, batch processing, o enterprise needs, gamitin ang aming browser extension o professional API service na may mas mataas na limits."
stats:
  conversions: "Mga Tables na Na-convert"
  tables: "Mga Tables na Na-generate"
  formats: "Mga Data File Formats"
  rating: "User Rating"
