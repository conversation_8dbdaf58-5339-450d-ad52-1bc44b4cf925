site:
  fullname: "Table Convert"
  name: "TableConvert"
  subtitle: "Δωρεάν Online Μετατροπέας και Γεννήτρια Πινάκων"
  intro: "Το TableConvert είναι ένα δωρεάν online εργαλείο μετατροπής πινάκων και γεννήτρια δεδομένων που υποστηρίζει μετατροπή μεταξύ 30+ μορφών συμπεριλαμβανομένων Excel, CSV, JSON, Markdown, LaTeX, SQL και άλλων."
  followTwitter: "Ακολουθήστε μας στο X"
title:
  converter: "%s σε %s"
  generator: "Γεννήτρια %s"
post:
  tags:
    converter: "Μετατροπέας"
    editor: "Επεξεργαστής"
    generator: "Γεννήτρια"
    maker: "Κατασκευαστής"
  converter:
    title: "Μετατροπή %s σε %s Online"
    short: "Ένα δωρεάν και ισχυρό %s σε %s online εργαλείο"
    intro: "Εύκολος στη χρήση online μετατροπέας %s σε %s. Μετατρέψτε δεδομένα πινάκων αβίαστα με το διαισθητικό εργαλείο μετατροπής μας. Γρήγορο, αξιόπιστο και φιλικό προς τον χρήστη."
  generator:
    title: "Online Επεξεργαστής και Γεννήτρια %s"
    short: "Επαγγελματικό %s online εργαλείο γέννησης με ολοκληρωμένες λειτουργίες"
    intro: "Εύκολος στη χρήση online γεννήτρια %s και επεξεργαστής πινάκων. Δημιουργήστε επαγγελματικούς πίνακες δεδομένων αβίαστα με το διαισθητικό εργαλείο μας και προεπισκόπηση σε πραγματικό χρόνο."
navbar:
  search:
    placeholder: "Αναζήτηση μετατροπέα ..."
  sponsor: "Αγοράστε μου έναν καφέ"
  extension: "Επέκταση"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Πηγή Δεδομένων"
    placeholder: "Επικολλήστε τα %s δεδομένα σας ή σύρετε %s αρχεία εδώ"
    example: "Παράδειγμα"
    upload: "Μεταφόρτωση Αρχείου"
    extract:
      enter: "Εξαγωγή από Ιστοσελίδα"
      intro: "Εισάγετε μια διεύθυνση URL ιστοσελίδας που περιέχει δεδομένα πίνακα για αυτόματη εξαγωγή δομημένων δεδομένων"
      btn: "Εξαγωγή %s"
    excel:
      sheet: "Φύλλο Εργασίας"
      none: "Κανένα"
  tableEditor:
    title: "Online Επεξεργαστής Πινάκων"
    undo: "Αναίρεση"
    redo: "Επανάληψη"
    transpose: "Μεταφορά"
    clear: "Εκκαθάριση"
    deleteBlank: "Διαγραφή Κενών"
    deleteDuplicate: "Αφαίρεση Διπλότυπων"
    uppercase: "ΚΕΦΑΛΑΙΑ"
    lowercase: "πεζά"
    capitalize: "Κεφαλαίο Πρώτο Γράμμα"
    replace:
      replace: "Εύρεση και Αντικατάσταση (Υποστηρίζεται Regex)"
      subst: "Αντικατάσταση με..."
      btn: "Αντικατάσταση Όλων"
  tableGenerator:
    title: "Γεννήτρια Πινάκων"
    sponsor: "Αγοράστε μου έναν καφέ"
    copy: "Αντιγραφή στο Πρόχειρο"
    download: "Λήψη Αρχείου"
    tooltip:
      html:
        escape: "Escape HTML ειδικούς χαρακτήρες (&, <, >, \", ') για αποφυγή σφαλμάτων εμφάνισης"
        div: "Χρησιμοποιήστε διάταξη DIV+CSS αντί για παραδοσιακές ετικέτες TABLE, καλύτερα κατάλληλη για responsive σχεδιασμό"
        minify: "Αφαιρέστε κενά και αλλαγές γραμμής για δημιουργία συμπιεσμένου HTML κώδικα"
        thead: "Δημιουργήστε τυπική δομή κεφαλίδας πίνακα (&lt;thead&gt;) και σώματος (&lt;tbody&gt;)"
        tableCaption: "Προσθέστε περιγραφικό τίτλο πάνω από τον πίνακα (στοιχείο &lt;caption&gt;)"
        tableClass: "Προσθέστε όνομα κλάσης CSS στον πίνακα για εύκολη προσαρμογή στυλ"
        tableId: "Ορίστε μοναδικό αναγνωριστικό ID για τον πίνακα για χειρισμό JavaScript"
      jira:
        escape: "Διαφυγή χαρακτήρων pipe (|) για αποφυγή συγκρούσεων με τη σύνταξη πίνακα Jira"
      json:
        parsingJSON: "Έξυπνη ανάλυση JSON συμβολοσειρών σε κελιά σε αντικείμενα"
        minify: "Δημιουργία συμπαγούς μονής γραμμής JSON μορφής για μείωση μεγέθους αρχείου"
        format: "Επιλογή δομής εξόδου JSON δεδομένων: πίνακας αντικειμένων, 2D πίνακας, κλπ."
      latex:
        escape: "Escape LaTeX ειδικούς χαρακτήρες (%, &, _, #, $, κλπ.) για εξασφάλιση σωστής μεταγλώττισης"
        ht: "Προσθήκη παραμέτρου πλωτής θέσης [!ht] για έλεγχο θέσης πίνακα στη σελίδα"
        mwe: "Δημιουργία πλήρους LaTeX εγγράφου"
        tableAlign: "Ορισμός οριζόντιας στοίχισης πίνακα στη σελίδα"
        tableBorder: "Διαμόρφωση στυλ περιγράμματος πίνακα: χωρίς περίγραμμα, μερικό περίγραμμα, πλήρες περίγραμμα"
        label: "Ορισμός ετικέτας πίνακα για διασταυρούμενες αναφορές εντολής \\ref{}"
        caption: "Ορισμός λεζάντας πίνακα για εμφάνιση πάνω ή κάτω από τον πίνακα"
        location: "Επιλογή θέσης εμφάνισης λεζάντας πίνακα: πάνω ή κάτω"
        tableType: "Επιλογή τύπου περιβάλλοντος πίνακα: tabular, longtable, array, κλπ."
      markdown:
        escape: "Διαφυγή ειδικών χαρακτήρων Markdown (*, _, |, \\, κλπ.) για αποφυγή συγκρούσεων μορφής"
        pretty: "Αυτόματη στοίχιση πλάτους στηλών για δημιουργία πιο όμορφου μορφότυπου πίνακα"
        simple: "Χρήση απλοποιημένης σύνταξης, παραλείποντας τις εξωτερικές κάθετες γραμμές περιγράμματος"
        boldFirstRow: "Κάντε το κείμενο της πρώτης γραμμής έντονο"
        boldFirstColumn: "Κάντε το κείμενο της πρώτης στήλης έντονο"
        firstHeader: "Αντιμετωπίστε την πρώτη γραμμή ως κεφαλίδα και προσθέστε γραμμή διαχωρισμού"
        textAlign: "Ορίστε στοίχιση κειμένου στήλης: αριστερά, κέντρο, δεξιά"
        multilineHandling: "Χειρισμός πολυγραμμικού κειμένου: διατήρηση αλλαγών γραμμής, διαφυγή σε \\n, χρήση ετικετών &lt;br&gt;"

        includeLineNumbers: "Προσθήκη στήλης αριθμού γραμμής στην αριστερή πλευρά του πίνακα"
      magic:
        builtin: "Επιλέξτε προκαθορισμένα κοινά μορφότυπα προτύπων"
        rowsTpl: "<table> <tr> <th>Μαγική Σύνταξη</th> <th>Περιγραφή</th> <th>Υποστηριζόμενες JS Μέθοδοι</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>1ο, 2ο ... πεδίο <b>ε</b>πικεφαλίδας, δηλαδή {hA} {hB} ...</td> <td>Μέθοδοι συμβολοσειράς</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>1ο, 2ο ... πεδίο τρέχουσας γραμμής, δηλαδή {$A} {$B} ...</td> <td>Μέθοδοι συμβολοσειράς</td> </tr> <tr> <td>{F,} {F;}</td> <td>Διαχωρισμός τρέχουσας γραμμής με τη συμβολοσειρά μετά το <b>F</b></td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td><b>Α</b>ριθμός γραμμής τρέχουσας <b>γ</b>ραμμής από 1 ή 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>Τ</b>ελικός αριθμός <b>γ</b>ραμμών </td> <td></td> </tr> <tr> <td>{x ...}</td> <td><b>Ε</b>κτέλεση κώδικα JavaScript, π.χ.: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Χρήση κάθετης κάτω γραμμής <b>\\</b> για έξοδο αγκυλών {...} </td> <td></td> </tr></table>"
        headerTpl: "Προσαρμοσμένο πρότυπο εξόδου για την ενότητα κεφαλίδας"
        footerTpl: "Προσαρμοσμένο πρότυπο εξόδου για την ενότητα υποσέλιδου"
      textile:
        escape: "Διαφυγή χαρακτήρων σύνταξης Textile (|, ., -, ^) για αποφυγή συγκρούσεων μορφής"
        rowHeader: "Ορίστε την πρώτη γραμμή ως γραμμή κεφαλίδας"
        thead: "Προσθέστε δείκτες σύνταξης Textile για κεφαλίδα και σώμα πίνακα"
      xml:
        escape: "Διαφυγή ειδικών χαρακτήρων XML (&lt;, &gt;, &amp;, \", ') για εξασφάλιση έγκυρου XML"
        minify: "Δημιουργία συμπιεσμένης εξόδου XML, αφαιρώντας επιπλέον κενά"
        rootElement: "Ορίστε όνομα ετικέτας ριζικού στοιχείου XML"
        rowElement: "Ορίστε όνομα ετικέτας στοιχείου XML για κάθε γραμμή δεδομένων"
        declaration: "Προσθέστε κεφαλίδα δήλωσης XML (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Εξαγωγή δεδομένων ως χαρακτηριστικά XML αντί για θυγατρικά στοιχεία"
        cdata: "Τυλίξτε το περιεχόμενο κειμένου με CDATA για προστασία ειδικών χαρακτήρων"
        encoding: "Ορίστε μορφή κωδικοποίησης χαρακτήρων για έγγραφο XML"
        indentation: "Επιλέξτε χαρακτήρα εσοχής XML: κενά ή tabs"
      yaml:
        indentSize: "Ορίστε τον αριθμό των κενών για εσοχή ιεραρχίας YAML (συνήθως 2 ή 4)"
        arrayStyle: "Μορφή πίνακα: block (ένα στοιχείο ανά γραμμή) ή flow (inline μορφή)"
        quotationStyle: "Στυλ εισαγωγικών συμβολοσειράς: χωρίς εισαγωγικά, μονά εισαγωγικά, διπλά εισαγωγικά"
      csv:
        bom: "Προσθέστε UTF-8 byte order mark για να βοηθήσετε το Excel και άλλο λογισμικό να αναγνωρίσει την κωδικοποίηση"
      excel:
        autoWidth: "Αυτόματη προσαρμογή πλάτους στήλης βάσει περιεχομένου"
        protectSheet: "Ενεργοποίηση προστασίας φύλλου εργασίας με κωδικό: tableconvert.com"
      sql:  
        primaryKey: "Καθορίστε όνομα πεδίου πρωτεύοντος κλειδιού για δήλωση CREATE TABLE"
        dialect: "Επιλέξτε τύπο βάσης δεδομένων, επηρεάζοντας τη σύνταξη εισαγωγικών και τύπων δεδομένων"
      ascii:
        forceSep: "Επιβολή γραμμών διαχωρισμού μεταξύ κάθε γραμμής δεδομένων"
        style: "Επιλέξτε στυλ σχεδίασης περιγράμματος ASCII πίνακα"
        comment: "Προσθέστε δείκτες σχολίων για να τυλίξετε ολόκληρο τον πίνακα"
      mediawiki:
        minify: "Συμπίεση κώδικα εξόδου, αφαίρεση επιπλέον κενών"
        header: "Σημειώστε την πρώτη γραμμή ως στυλ κεφαλίδας"
        sort: "Ενεργοποίηση λειτουργίας ταξινόμησης με κλικ στον πίνακα"
      asciidoc:
        minify: "Συμπίεση εξόδου μορφής AsciiDoc"
        firstHeader: "Ορίστε την πρώτη γραμμή ως γραμμή κεφαλίδας"
        lastFooter: "Ορίστε την τελευταία γραμμή ως γραμμή υποσέλιδου"
        title: "Προσθέστε κείμενο τίτλου στον πίνακα"
      tracwiki:
        rowHeader: "Ορίστε την πρώτη γραμμή ως κεφαλίδα"
        colHeader: "Ορίστε την πρώτη στήλη ως κεφαλίδα"
      bbcode:
        minify: "Συμπίεση μορφής εξόδου BBCode"
      restructuredtext:
        style: "Επιλέξτε στυλ περιγράμματος πίνακα reStructuredText"
        forceSep: "Επιβολή γραμμών διαχωρισμού"
      pdf:
        theme: "Επιλέξτε οπτικό στυλ πίνακα PDF για επαγγελματικά έγγραφα"
        headerColor: "Επιλέξτε χρώμα φόντου κεφαλίδας για πίνακες PDF"
        showHead: "Έλεγχος εμφάνισης κεφαλίδας σε σελίδες PDF"
        docTitle: "Προαιρετικός τίτλος για το έγγραφο PDF"
        docDescription: "Προαιρετικό κείμενο περιγραφής για έγγραφο PDF"
    label:
      ascii:
        forceSep: "Διαχωριστικά Γραμμών"
        style: "Στυλ Περιγράμματος"
        comment: "Περιτύλιγμα Σχολίου"
      restructuredtext:
        style: "Στυλ Περιγράμματος"
        forceSep: "Εξαναγκασμός Διαχωριστικών"
      bbcode:
        minify: "Ελαχιστοποίηση Εξόδου"
      csv:
        doubleQuote: "Περιτύλιγμα Διπλών Εισαγωγικών"
        delimiter: "Διαχωριστικό Πεδίου"
        bom: "UTF-8 BOM"
        valueDelimiter: "Διαχωριστικό Αξίας"
        rowDelimiter: "Διαχωριστικό Γραμμής"
        prefix: "Πρόθεμα Γραμμής"
        suffix: "Επίθεμα Γραμμής"
      excel:
        autoWidth: "Αυτόματο Πλάτος"
        textFormat: "Μορφή Κειμένου"
        protectSheet: "Προστασία Φύλλου"
        boldFirstRow: "Έντονη Πρώτη Γραμμή"
        boldFirstColumn: "Έντονη Πρώτη Στήλη"
        sheetName: "Όνομα Φύλλου"
      html:
        escape: "Διαφυγή Χαρακτήρων HTML"
        div: "Πίνακας DIV"
        minify: "Ελαχιστοποίηση Κώδικα"
        thead: "Δομή Κεφαλίδας Πίνακα"
        tableCaption: "Τίτλος Πίνακα"
        tableClass: "Κλάση Πίνακα"
        tableId: "ID Πίνακα"
        rowHeader: "Κεφαλίδα Γραμμής"
        colHeader: "Κεφαλίδα Στήλης"
      jira:
        escape: "Διαφυγή Χαρακτήρων"
        rowHeader: "Κεφαλίδα Γραμμής"
        colHeader: "Κεφαλίδα Στήλης"
      json:
        parsingJSON: "Ανάλυση JSON"
        minify: "Ελαχιστοποίηση Εξόδου"
        format: "Μορφή Δεδομένων"
        rootName: "Όνομα Ριζικού Αντικειμένου"
        indentSize: "Μέγεθος Εσοχής"
      jsonlines:
        parsingJSON: "Ανάλυση JSON"
        format: "Μορφή Δεδομένων"
      latex:
        escape: "Διαφυγή Χαρακτήρων Πίνακα LaTeX"
        ht: "Θέση Αιώρησης"
        mwe: "Πλήρες Έγγραφο"
        tableAlign: "Στοίχιση Πίνακα"
        tableBorder: "Στυλ Περιγράμματος"
        label: "Ετικέτα Αναφοράς"
        caption: "Τίτλος Πίνακα"
        location: "Θέση Τίτλου"
        tableType: "Τύπος Πίνακα"
        boldFirstRow: "Έντονη Πρώτη Γραμμή"
        boldFirstColumn: "Έντονη Πρώτη Στήλη"
        textAlign: "Στοίχιση Κειμένου"
        borders: "Ρυθμίσεις Περιγράμματος"
      markdown:
        escape: "Διαφυγή Χαρακτήρων"
        pretty: "Όμορφος Πίνακας Markdown"
        simple: "Απλή Μορφή Markdown"
        boldFirstRow: "Έντονη Πρώτη Γραμμή"
        boldFirstColumn: "Έντονη Πρώτη Στήλη"
        firstHeader: "Πρώτη Κεφαλίδα"
        textAlign: "Στοίχιση Κειμένου"
        multilineHandling: "Χειρισμός Πολλαπλών Γραμμών"

        includeLineNumbers: "Προσθήκη Αριθμών Γραμμής"
        align: "Στοίχιση"
      mediawiki:
        minify: "Ελαχιστοποίηση Κώδικα"
        header: "Σήμανση Κεφαλίδας"
        sort: "Ταξινομήσιμο"
      asciidoc:
        minify: "Ελαχιστοποίηση Μορφής"
        firstHeader: "Πρώτη Κεφαλίδα"
        lastFooter: "Τελευταίο Υποσέλιδο"
        title: "Τίτλος Πίνακα"
      tracwiki:
        rowHeader: "Κεφαλίδα Γραμμής"
        colHeader: "Κεφαλίδα Στήλης"
      sql:
        drop: "Διαγραφή Πίνακα (Αν Υπάρχει)"
        create: "Δημιουργία Πίνακα"
        oneInsert: "Μαζική Εισαγωγή"
        table: "Όνομα Πίνακα"
        dialect: "Τύπος Βάσης Δεδομένων"
        primaryKey: "Πρωτεύον Κλειδί"
      magic:
        builtin: "Ενσωματωμένο Πρότυπο"
        rowsTpl: "Πρότυπο Γραμμής, Σύνταξη ->"
        headerTpl: "Πρότυπο Κεφαλίδας"
        footerTpl: "Πρότυπο Υποσέλιδου"
      textile:
        escape: "Διαφυγή Χαρακτήρων"
        rowHeader: "Κεφαλίδα Γραμμής"
        thead: "Σύνταξη Κεφαλίδας Πίνακα"
      xml:
        escape: "Διαφυγή Χαρακτήρων XML"
        minify: "Ελαχιστοποίηση Εξόδου"
        rootElement: "Ριζικό Στοιχείο"
        rowElement: "Στοιχείο Γραμμής"
        declaration: "Δήλωση XML"
        attributes: "Λειτουργία Χαρακτηριστικών"
        cdata: "Περιτύλιγμα CDATA"
        encoding: "Κωδικοποίηση"
        indentSize: "Μέγεθος Εσοχής"
      yaml:
        indentSize: "Μέγεθος Εσοχής"
        arrayStyle: "Στυλ Πίνακα"
        quotationStyle: "Στυλ Εισαγωγικών"
      pdf:
        theme: "Θέμα Πίνακα PDF"
        headerColor: "Χρώμα Κεφαλίδας PDF"
        showHead: "Εμφάνιση Κεφαλίδας PDF"
        docTitle: "Τίτλος Εγγράφου PDF"
        docDescription: "Περιγραφή Εγγράφου PDF"

sidebar:
  all: "Όλα τα Εργαλεία Μετατροπής"
  dataSource:
    title: "Πηγή Δεδομένων"
    description:
      converter: "Εισάγετε %s για μετατροπή σε %s. Υποστηρίζει μεταφόρτωση αρχείων, online επεξεργασία και εξαγωγή δεδομένων ιστού."
      generator: "Δημιουργήστε δεδομένα πίνακα με υποστήριξη πολλαπλών μεθόδων εισαγωγής συμπεριλαμβανομένης χειροκίνητης εισαγωγής, εισαγωγής αρχείων και δημιουργίας προτύπων."
  tableEditor:
    title: "Online Επεξεργαστής Πινάκων"
    description:
      converter: "Επεξεργαστείτε %s online χρησιμοποιώντας τον επεξεργαστή πινάκων μας. Εμπειρία λειτουργίας τύπου Excel με υποστήριξη διαγραφής κενών γραμμών, αφαίρεσης διπλότυπων, ταξινόμησης και εύρεσης & αντικατάστασης."
      generator: "Ισχυρός online επεξεργαστής πινάκων που παρέχει εμπειρία λειτουργίας τύπου Excel. Υποστηρίζει διαγραφή κενών γραμμών, αφαίρεση διπλότυπων, ταξινόμηση και εύρεση & αντικατάσταση."
  tableGenerator:
    title: "Γεννήτρια Πινάκων"
    description:
      converter: "Δημιουργήστε γρήγορα %s με προεπισκόπηση σε πραγματικό χρόνο της γεννήτριας πινάκων. Πλούσιες επιλογές εξαγωγής, αντιγραφή & λήψη με ένα κλικ."
      generator: "Εξάγετε %s δεδομένα σε πολλαπλές μορφές για να καλύψετε διαφορετικά σενάρια χρήσης. Υποστηρίζει προσαρμοσμένες επιλογές και προεπισκόπηση σε πραγματικό χρόνο."
footer:
  changelog: "Αρχείο Αλλαγών"
  sponsor: "Χορηγοί"
  contact: "Επικοινωνήστε Μαζί Μας"
  privacyPolicy: "Πολιτική Απορρήτου"
  about: "Σχετικά"
  resources: "Πόροι"
  popularConverters: "Δημοφιλείς Μετατροπείς"
  popularGenerators: "Δημοφιλείς Γεννήτριες"
  dataSecurity: "Τα δεδομένα σας είναι ασφαλή - όλες οι μετατροπές εκτελούνται στον περιηγητή σας."
converters:
  Markdown:
    alias: "Πίνακας Markdown"
    what: "Το Markdown είναι μια ελαφριά γλώσσα σήμανσης που χρησιμοποιείται ευρέως για τεχνική τεκμηρίωση, δημιουργία περιεχομένου blog και ανάπτυξη ιστού. Η σύνταξη πίνακα είναι συνοπτική και διαισθητική, υποστηρίζει στοίχιση κειμένου, ενσωμάτωση συνδέσμων και μορφοποίηση. Είναι το προτιμώμενο εργαλείο για προγραμματιστές και τεχνικούς συγγραφείς, απόλυτα συμβατό με GitHub, GitLab και άλλες πλατφόρμες φιλοξενίας κώδικα."
    step1: "Επικολλήστε δεδομένα πίνακα Markdown στην περιοχή πηγής δεδομένων ή σύρετε και αποθέστε αρχεία .md απευθείας για μεταφόρτωση. Το εργαλείο αναλύει αυτόματα τη δομή και μορφοποίηση πίνακα, υποστηρίζει πολύπλοκο ένθετο περιεχόμενο και χειρισμό ειδικών χαρακτήρων."
    step3: "Δημιουργήστε τυπικό κώδικα πίνακα Markdown σε πραγματικό χρόνο, υποστηρίζει πολλαπλές μεθόδους στοίχισης, έντονη γραφή κειμένου, προσθήκη αριθμών γραμμών και άλλες προηγμένες ρυθμίσεις μορφής. Ο παραγόμενος κώδικας είναι πλήρως συμβατός με GitHub και μεγάλους επεξεργαστές Markdown, έτοιμος για χρήση με ένα κλικ αντιγραφής."
    from_alias: "Αρχείο Πίνακα Markdown"
    to_alias: "Μορφή Πίνακα Markdown"
  Magic:
    alias: "Προσαρμοσμένο Πρότυπο"
    what: "Το πρότυπο Magic είναι ένας μοναδικός προηγμένος γεννήτορας δεδομένων αυτού του εργαλείου, που επιτρέπει στους χρήστες να δημιουργούν αυθαίρετη μορφή εξόδου δεδομένων μέσω προσαρμοσμένης σύνταξης προτύπου. Υποστηρίζει αντικατάσταση μεταβλητών, υπό όρους κρίση και επεξεργασία βρόχου. Είναι η απόλυτη λύση για χειρισμό πολύπλοκων αναγκών μετατροπής δεδομένων και εξατομικευμένων μορφών εξόδου, ιδιαίτερα κατάλληλη για προγραμματιστές και μηχανικούς δεδομένων."
    step1: "Επιλέξτε ενσωματωμένα κοινά πρότυπα ή δημιουργήστε προσαρμοσμένη σύνταξη προτύπου. Υποστηρίζει πλούσιες μεταβλητές και συναρτήσεις που μπορούν να χειριστούν πολύπλοκες δομές δεδομένων και επιχειρηματική λογική."
    step3: "Δημιουργήστε έξοδο δεδομένων που ικανοποιεί πλήρως τις προσαρμοσμένες απαιτήσεις μορφής. Υποστηρίζει πολύπλοκη λογική μετατροπής δεδομένων και υπό όρους επεξεργασία, βελτιώνει σημαντικά την αποδοτικότητα επεξεργασίας δεδομένων και την ποιότητα εξόδου. Ένα ισχυρό εργαλείο για μαζική επεξεργασία δεδομένων."
    from_alias: "Δεδομένα Πίνακα"
    to_alias: "Προσαρμοσμένη Έξοδος Μορφής"
  CSV:
    alias: "CSV"
    what: "Το CSV (Comma-Separated Values) είναι η πιο ευρέως χρησιμοποιούμενη μορφή ανταλλαγής δεδομένων, απόλυτα υποστηριζόμενη από Excel, Google Sheets, συστήματα βάσεων δεδομένων και διάφορα εργαλεία ανάλυσης δεδομένων. Η απλή δομή και η ισχυρή συμβατότητά του το καθιστούν την τυπική μορφή για μετανάστευση δεδομένων, μαζική εισαγωγή/εξαγωγή και ανταλλαγή δεδομένων μεταξύ πλατφορμών, ευρέως χρησιμοποιούμενη σε επιχειρηματική ανάλυση, επιστήμη δεδομένων και ενσωμάτωση συστημάτων."
    step1: "Μεταφορτώστε αρχεία CSV ή επικολλήστε δεδομένα CSV απευθείας. Το εργαλείο αναγνωρίζει έξυπνα διάφορους διαχωριστές (κόμμα, tab, ερωτηματικό, pipe, κλπ.), ανιχνεύει αυτόματα τύπους δεδομένων και μορφές κωδικοποίησης, υποστηρίζει γρήγορη ανάλυση μεγάλων αρχείων και πολύπλοκων δομών δεδομένων."
    step3: "Δημιουργήστε τυπικά αρχεία μορφής CSV με υποστήριξη προσαρμοσμένων διαχωριστών, στυλ εισαγωγικών, μορφών κωδικοποίησης και ρυθμίσεων σήματος BOM. Εξασφαλίζει τέλεια συμβατότητα με συστήματα προορισμού, παρέχει επιλογές λήψης και συμπίεσης για να καλύψει τις ανάγκες επεξεργασίας δεδομένων επιχειρηματικού επιπέδου."
    from_alias: "Αρχείο Δεδομένων CSV"
    to_alias: "Τυπική Μορφή CSV"
  JSON:
    alias: "Πίνακας JSON"
    what: "Το JSON (JavaScript Object Notation) είναι η τυπική μορφή δεδομένων πίνακα για σύγχρονες εφαρμογές ιστού, REST APIs και αρχιτεκτονικές μικροϋπηρεσιών. Η σαφής δομή και η αποδοτική ανάλυσή του το καθιστούν ευρέως χρησιμοποιούμενο σε αλληλεπίδραση δεδομένων frontend και backend, αποθήκευση αρχείων διαμόρφωσης και βάσεις δεδομένων NoSQL. Υποστηρίζει ένθετα αντικείμενα, δομές πίνακα και πολλαπλούς τύπους δεδομένων, καθιστώντας το απαραίτητα δεδομένα πίνακα για σύγχρονη ανάπτυξη λογισμικού."
    step1: "Μεταφορτώστε αρχεία JSON ή επικολλήστε πίνακες JSON. Υποστηρίζει αυτόματη αναγνώριση και ανάλυση πινάκων αντικειμένων, ένθετων δομών και πολύπλοκων τύπων δεδομένων. Το εργαλείο επικυρώνει έξυπνα τη σύνταξη JSON και παρέχει προειδοποιήσεις σφαλμάτων."
    step3: "Δημιουργήστε πολλαπλές εξόδους μορφής JSON: τυπικούς πίνακες αντικειμένων, 2D πίνακες, πίνακες στηλών και μορφές ζευγών κλειδιού-αξίας. Υποστηρίζει ωραιοποιημένη έξοδο, λειτουργία συμπίεσης, προσαρμοσμένα ονόματα ριζικών αντικειμένων και ρυθμίσεις εσοχής, προσαρμόζεται τέλεια σε διάφορες διεπαφές API και ανάγκες αποθήκευσης δεδομένων."
    from_alias: "Αρχείο Πίνακα JSON"
    to_alias: "Τυπική Μορφή JSON"
  JSONLines:
    alias: "Μορφή JSONLines"
    what: "Το JSON Lines (γνωστό και ως NDJSON) είναι μια σημαντική μορφή για επεξεργασία μεγάλων δεδομένων και μετάδοση δεδομένων ροής, με κάθε γραμμή να περιέχει ένα ανεξάρτητο αντικείμενο JSON. Ευρέως χρησιμοποιούμενο σε ανάλυση αρχείων καταγραφής, επεξεργασία ροής δεδομένων, μηχανική μάθηση και κατανεμημένα συστήματα. Υποστηρίζει σταδιακή επεξεργασία και παράλληλο υπολογισμό, καθιστώντας το την ιδανική επιλογή για χειρισμό δομημένων δεδομένων μεγάλης κλίμακας."
    step1: "Μεταφορτώστε αρχεία JSONLines ή επικολλήστε δεδομένα. Το εργαλείο αναλύει αντικείμενα JSON γραμμή προς γραμμή, υποστηρίζει επεξεργασία ροής μεγάλων αρχείων και λειτουργία παράλειψης γραμμών σφάλματος."
    step3: "Δημιουργήστε τυπική μορφή JSONLines με κάθε γραμμή να εξάγει ένα πλήρες αντικείμενο JSON. Κατάλληλη για επεξεργασία ροής, μαζική εισαγωγή και σενάρια ανάλυσης μεγάλων δεδομένων, υποστηρίζει επικύρωση δεδομένων και βελτιστοποίηση μορφής."
    from_alias: "Δεδομένα JSONLines"
    to_alias: "Μορφή Ροής JSONLines"
  XML:
    alias: "XML"
    what: "Το XML (eXtensible Markup Language) είναι η τυπική μορφή για ανταλλαγή δεδομένων επιχειρηματικού επιπέδου και διαχείριση διαμόρφωσης, με αυστηρές προδιαγραφές σύνταξης και ισχυρούς μηχανισμούς επικύρωσης. Ευρέως χρησιμοποιούμενο σε υπηρεσίες ιστού, αρχεία διαμόρφωσης, αποθήκευση εγγράφων και ενσωμάτωση συστημάτων. Υποστηρίζει χώρους ονομάτων, επικύρωση σχήματος και μετασχηματισμό XSLT, καθιστώντας το σημαντικά δεδομένα πίνακα για εφαρμογές επιχειρήσεων."
    step1: "Μεταφορτώστε αρχεία XML ή επικολλήστε δεδομένα XML. Το εργαλείο αναλύει αυτόματα τη δομή XML και τη μετατρέπει σε μορφή πίνακα, υποστηρίζει χώρο ονομάτων, χειρισμό χαρακτηριστικών και πολύπλοκες ένθετες δομές."
    step3: "Δημιουργήστε έξοδο XML που συμμορφώνεται με τα πρότυπα XML. Υποστηρίζει προσαρμοσμένα στοιχεία ρίζας, ονόματα στοιχείων γραμμής, λειτουργίες χαρακτηριστικών, περιτύλιξη CDATA και ρυθμίσεις κωδικοποίησης χαρακτήρων. Εξασφαλίζει ακεραιότητα δεδομένων και συμβατότητα, ικανοποιώντας τις απαιτήσεις εφαρμογών επιχειρηματικού επιπέδου."
    from_alias: "Αρχείο Δεδομένων XML"
    to_alias: "Τυπική Μορφή XML"
  YAML:
    alias: "Διαμόρφωση YAML"
    what: "Το YAML είναι ένα φιλικό προς τον άνθρωπο πρότυπο σειριοποίησης δεδομένων, φημισμένο για τη σαφή ιεραρχική δομή και τη συνοπτική σύνταξή του. Ευρέως χρησιμοποιούμενο σε αρχεία διαμόρφωσης, αλυσίδες εργαλείων DevOps, Docker Compose και ανάπτυξη Kubernetes. Η ισχυρή αναγνωσιμότητα και η συνοπτική σύνταξή του το καθιστούν σημαντική μορφή διαμόρφωσης για σύγχρονες εφαρμογές cloud-native και αυτοματοποιημένες λειτουργίες."
    step1: "Μεταφορτώστε αρχεία YAML ή επικολλήστε δεδομένα YAML. Το εργαλείο αναλύει έξυπνα τη δομή YAML και επικυρώνει τη σωστότητα σύνταξης, υποστηρίζει μορφές πολλαπλών εγγράφων και πολύπλοκους τύπους δεδομένων."
    step3: "Δημιουργήστε τυπική έξοδο μορφής YAML με υποστήριξη στυλ πίνακα μπλοκ και ροής, πολλαπλές ρυθμίσεις εισαγωγικών, προσαρμοσμένη εσοχή και διατήρηση σχολίων. Εξασφαλίζει ότι τα αρχεία YAML εξόδου είναι πλήρως συμβατά με διάφορους αναλυτές και συστήματα διαμόρφωσης."
    from_alias: "Αρχείο Διαμόρφωσης YAML"
    to_alias: "Τυπική Μορφή YAML"
  MySQL:
      alias: "Αποτελέσματα Ερωτήματος MySQL"
      what: "Η MySQL είναι το πιο δημοφιλές σύστημα διαχείρισης σχεσιακών βάσεων δεδομένων ανοιχτού κώδικα στον κόσμο, φημισμένη για την υψηλή απόδοση, αξιοπιστία και ευκολία χρήσης. Ευρέως χρησιμοποιούμενη σε εφαρμογές ιστού, συστήματα επιχειρήσεων και πλατφόρμες ανάλυσης δεδομένων. Τα αποτελέσματα ερωτημάτων MySQL περιέχουν συνήθως δομημένα δεδομένα πίνακα, λειτουργώντας ως σημαντική πηγή δεδομένων στη διαχείριση βάσεων δεδομένων και την εργασία ανάλυσης δεδομένων."
      step1: "Επικολλήστε αποτελέσματα εξόδου ερωτήματος MySQL στην περιοχή πηγής δεδομένων. Το εργαλείο αναγνωρίζει αυτόματα και αναλύει τη μορφή εξόδου γραμμής εντολών MySQL, υποστηρίζει διάφορα στυλ αποτελεσμάτων ερωτήματος και κωδικοποιήσεις χαρακτήρων, χειρίζεται έξυπνα κεφαλίδες και γραμμές δεδομένων."
      step3: "Μετατρέψτε γρήγορα αποτελέσματα ερωτημάτων MySQL σε πολλαπλές μορφές δεδομένων πίνακα, διευκολύνοντας την ανάλυση δεδομένων, τη δημιουργία αναφορών, τη μετανάστευση δεδομένων μεταξύ συστημάτων και την επικύρωση δεδομένων. Ένα πρακτικό εργαλείο για διαχειριστές βάσεων δεδομένων και αναλυτές δεδομένων."
      from_alias: "Έξοδος Ερωτήματος MySQL"
      to_alias: "Δεδομένα Πίνακα MySQL"
  SQL:
    alias: "Insert SQL"
    what: "Η SQL (Structured Query Language) είναι η τυπική γλώσσα λειτουργίας για σχεσιακές βάσεις δεδομένων, χρησιμοποιούμενη για ερωτήματα δεδομένων, εισαγωγή, ενημέρωση και διαγραφή λειτουργιών. Ως η βασική τεχνολογία διαχείρισης βάσεων δεδομένων, η SQL χρησιμοποιείται ευρέως στην ανάλυση δεδομένων, επιχειρηματική νοημοσύνη, επεξεργασία ETL και κατασκευή αποθήκης δεδομένων. Είναι ένα απαραίτητο εργαλείο δεξιοτήτων για επαγγελματίες δεδομένων."
    step1: "Επικολλήστε δηλώσεις INSERT SQL ή μεταφορτώστε αρχεία .sql. Το εργαλείο αναλύει έξυπνα τη σύνταξη SQL και εξάγει δεδομένα πίνακα, υποστηρίζει πολλαπλές διαλέκτους SQL και επεξεργασία πολύπλοκων δηλώσεων ερωτήματος."
    step3: "Δημιουργήστε τυπικές δηλώσεις SQL INSERT και δηλώσεις δημιουργίας πίνακα. Υποστηρίζει πολλαπλές διαλέκτους βάσεων δεδομένων (MySQL, PostgreSQL, SQLite, SQL Server, Oracle), χειρίζεται αυτόματα αντιστοίχιση τύπων δεδομένων, διαφυγή χαρακτήρων και περιορισμούς πρωτεύοντος κλειδιού. Εξασφαλίζει ότι ο παραγόμενος κώδικας SQL μπορεί να εκτελεστεί απευθείας."
    from_alias: "Insert SQL"
    to_alias: "Δήλωση SQL"
  Qlik:
      alias: "Πίνακας Qlik"
      what: "Η Qlik είναι προμηθευτής λογισμικού που ειδικεύεται σε οπτικοποίηση δεδομένων, ταμπλό διευθυντικών στελεχών και προϊόντα επιχειρηματικής νοημοσύνης αυτοεξυπηρέτησης, μαζί με την Tableau και τη Microsoft."
      step1: ""
      step3: "Τέλος, ο [Γεννήτορας Πίνακα](#TableGenerator) εμφανίζει τα αποτελέσματα μετατροπής. Χρησιμοποιήστε στο Qlik Sense, Qlik AutoML, QlikView ή άλλο λογισμικό με δυνατότητα Qlik."
      from_alias: "Πίνακας Qlik"
      to_alias: "Πίνακας Qlik"
  DAX:
      alias: "Πίνακας DAX"
      what: "Το DAX (Data Analysis Expressions) είναι μια γλώσσα προγραμματισμού που χρησιμοποιείται σε όλο το Microsoft Power BI για τη δημιουργία υπολογιζόμενων στηλών, μέτρων και προσαρμοσμένων πινάκων."
      step1: ""
      step3: "Τέλος, ο [Γεννήτορας Πίνακα](#TableGenerator) εμφανίζει τα αποτελέσματα μετατροπής. Όπως αναμενόταν, χρησιμοποιείται σε διάφορα προϊόντα Microsoft συμπεριλαμβανομένων των Microsoft Power BI, Microsoft Analysis Services και Microsoft Power Pivot για Excel."
      from_alias: "Πίνακας DAX"
      to_alias: "Πίνακας DAX"
  Firebase:
    alias: "Λίστα Firebase"
    what: "Το Firebase είναι μια πλατφόρμα ανάπτυξης εφαρμογών BaaS που παρέχει φιλοξενούμενες υπηρεσίες backend όπως βάση δεδομένων πραγματικού χρόνου, αποθήκευση cloud, αυθεντικοποίηση, αναφορά σφαλμάτων, κλπ."
    step1: ""
    step3: "Τέλος, ο [Γεννήτορας Πίνακα](#TableGenerator) εμφανίζει τα αποτελέσματα μετατροπής. Στη συνέχεια μπορείτε να χρησιμοποιήσετε τη μέθοδο push στο Firebase API για να προσθέσετε σε μια λίστα δεδομένων στη βάση δεδομένων Firebase."
    from_alias: "Λίστα Firebase"
    to_alias: "Λίστα Firebase"
  HTML:
    alias: "Πίνακας HTML"
    what: "Οι πίνακες HTML είναι ο τυπικός τρόπος εμφάνισης δομημένων δεδομένων σε ιστοσελίδες, κατασκευασμένοι με τα tags table, tr, td και άλλα. Υποστηρίζει πλούσια προσαρμογή στυλ, responsive διάταξη και διαδραστική λειτουργικότητα. Ευρέως χρησιμοποιούμενοι στην ανάπτυξη ιστοσελίδων, εμφάνιση δεδομένων και δημιουργία αναφορών, λειτουργώντας ως σημαντικό στοιχείο της front-end ανάπτυξης και του web design."
    step1: "Επικολλήστε κώδικα HTML που περιέχει πίνακες ή μεταφορτώστε αρχεία HTML. Το εργαλείο αναγνωρίζει αυτόματα και εξάγει δεδομένα πίνακα από σελίδες, υποστηρίζει πολύπλοκες δομές HTML, στυλ CSS και επεξεργασία ένθετων πινάκων."
    step3: "Δημιουργήστε σημασιολογικό κώδικα πίνακα HTML με υποστήριξη δομής thead/tbody, ρυθμίσεις κλάσεων CSS, λεζάντες πίνακα, κεφαλίδες σειρών/στηλών και διαμόρφωση responsive χαρακτηριστικών. Εξασφαλίζει ότι ο παραγόμενος κώδικας πίνακα πληροί τα πρότυπα web με καλή προσβασιμότητα και φιλικότητα SEO."
    from_alias: "Πίνακας HTML"
    to_alias: "Πίνακας HTML"
  Excel:
    alias: "Excel"
    what: "Το Microsoft Excel είναι το πιο δημοφιλές λογισμικό υπολογιστικών φύλλων στον κόσμο, ευρέως χρησιμοποιούμενο σε επιχειρηματική ανάλυση, οικονομική διαχείριση, επεξεργασία δεδομένων και δημιουργία αναφορών. Οι ισχυρές δυνατότητες επεξεργασίας δεδομένων, η πλούσια βιβλιοθήκη συναρτήσεων και τα ευέλικτα χαρακτηριστικά οπτικοποίησης το καθιστούν το τυπικό εργαλείο για αυτοματισμό γραφείου και ανάλυση δεδομένων, με εκτεταμένες εφαρμογές σε σχεδόν όλες τις βιομηχανίες και τομείς."
    step1: "Μεταφορτώστε αρχεία Excel (υποστηρίζει μορφές .xlsx, .xls) ή αντιγράψτε δεδομένα πίνακα απευθείας από το Excel και επικολλήστε. Το εργαλείο υποστηρίζει επεξεργασία πολλαπλών φύλλων εργασίας, αναγνώριση πολύπλοκων μορφών και γρήγορη ανάλυση μεγάλων αρχείων, χειρίζεται αυτόματα συγχωνευμένα κελιά και τύπους δεδομένων."
    step3: "Δημιουργήστε δεδομένα πίνακα συμβατά με Excel που μπορούν να επικολληθούν απευθείας στο Excel ή να ληφθούν ως τυπικά αρχεία .xlsx. Υποστηρίζει ονομασία φύλλων εργασίας, μορφοποίηση κελιών, αυτόματο πλάτος στήλης, στυλ κεφαλίδας και ρυθμίσεις επικύρωσης δεδομένων. Εξασφαλίζει ότι τα αρχεία Excel εξόδου έχουν επαγγελματική εμφάνιση και πλήρη λειτουργικότητα."
    from_alias: "Υπολογιστικό Φύλλο Excel"
    to_alias: "Excel"
  LaTeX:
    alias: "Πίνακας LaTeX"
    what: "Το LaTeX είναι ένα επαγγελματικό σύστημα στοιχειοθεσίας εγγράφων, ιδιαίτερα κατάλληλο για τη δημιουργία ακαδημαϊκών εργασιών, τεχνικών εγγράφων και επιστημονικών δημοσιεύσεων. Η λειτουργικότητα πίνακά του είναι ισχυρή, υποστηρίζει πολύπλοκους μαθηματικούς τύπους, ακριβή έλεγχο διάταξης και υψηλής ποιότητας έξοδο PDF. Είναι το τυπικό εργαλείο στην ακαδημαϊκή κοινότητα και την επιστημονική δημοσίευση, ευρέως χρησιμοποιούμενο σε άρθρα περιοδικών, διατριβές και στοιχειοθεσία τεχνικών εγχειριδίων."
    step1: "Επικολλήστε κώδικα πίνακα LaTeX ή μεταφορτώστε αρχεία .tex. Το εργαλείο αναλύει τη σύνταξη πίνακα LaTeX και εξάγει περιεχόμενο δεδομένων, υποστηρίζει πολλαπλά περιβάλλοντα πίνακα (tabular, longtable, array, κλπ.) και πολύπλοκες εντολές μορφής."
    step3: "Δημιουργήστε επαγγελματικό κώδικα πίνακα LaTeX με υποστήριξη επιλογής πολλαπλών περιβαλλόντων πίνακα, διαμόρφωση στυλ περιγράμματος, ρυθμίσεις θέσης λεζάντας, προδιαγραφή κλάσης εγγράφου και διαχείριση πακέτων. Μπορεί να δημιουργήσει πλήρη μεταγλωττίσιμα έγγραφα LaTeX, εξασφαλίζοντας ότι οι πίνακες εξόδου πληρούν τα πρότυπα ακαδημαϊκής δημοσίευσης."
    from_alias: "Πίνακας LaTeX"
    to_alias: "Πίνακας LaTeX"
  ASCII:
    alias: "Πίνακας Κειμένου ASCII"
    what: "Οι πίνακες ASCII χρησιμοποιούν απλούς χαρακτήρες κειμένου για να σχεδιάσουν περιγράμματα και δομές πίνακα, παρέχοντας τη βέλτιστη συμβατότητα και φορητότητα. Συμβατοί με όλους τους επεξεργαστές κειμένου, περιβάλλοντα τερματικού και λειτουργικά συστήματα. Ευρέως χρησιμοποιούμενοι σε τεκμηρίωση κώδικα, τεχνικά εγχειρίδια, αρχεία README και έξοδο εργαλείων γραμμής εντολών. Η προτιμώμενη μορφή εμφάνισης δεδομένων για προγραμματιστές και διαχειριστές συστημάτων."
    step1: "Μεταφορτώστε αρχεία κειμένου που περιέχουν πίνακες ASCII ή επικολλήστε δεδομένα πίνακα απευθείας. Το εργαλείο αναγνωρίζει έξυπνα και αναλύει δομές πίνακα ASCII, υποστηρίζει πολλαπλά στυλ περιγράμματος και μορφές στοίχισης."
    step3: "Δημιουργήστε όμορφους πίνακες ASCII απλού κειμένου με υποστήριξη πολλαπλών στυλ περιγράμματος (μονή γραμμή, διπλή γραμμή, στρογγυλεμένες γωνίες, κλπ.), μεθόδους στοίχισης κειμένου και αυτόματο πλάτος στήλης. Οι παραγόμενοι πίνακες εμφανίζονται τέλεια σε επεξεργαστές κώδικα, έγγραφα και γραμμές εντολών."
    from_alias: "Πίνακας Κειμένου ASCII"
    to_alias: "Πίνακας Κειμένου ASCII"
  MediaWiki:
    alias: "Πίνακας MediaWiki"
    what: "Το MediaWiki είναι η πλατφόρμα λογισμικού ανοιχτού κώδικα που χρησιμοποιείται από διάσημους ιστότοπους wiki όπως η Wikipedia. Η σύνταξη πίνακά του είναι συνοπτική αλλά ισχυρή, υποστηρίζει προσαρμογή στυλ πίνακα, λειτουργικότητα ταξινόμησης και ενσωμάτωση συνδέσμων. Ευρέως χρησιμοποιούμενο σε διαχείριση γνώσης, συνεργατική επεξεργασία και συστήματα διαχείρισης περιεχομένου, λειτουργώντας ως βασική τεχνολογία για την κατασκευή εγκυκλοπαιδειών wiki και βάσεων γνώσης."
    step1: "Επικολλήστε κώδικα πίνακα MediaWiki ή μεταφορτώστε αρχεία πηγής wiki. Το εργαλείο αναλύει τη σύνταξη σήμανσης wiki και εξάγει δεδομένα πίνακα, υποστηρίζει πολύπλοκη σύνταξη wiki και επεξεργασία προτύπων."
    step3: "Δημιουργήστε τυπικό κώδικα πίνακα MediaWiki με υποστήριξη ρυθμίσεων στυλ κεφαλίδας, στοίχιση κελιών, ενεργοποίηση λειτουργικότητας ταξινόμησης και επιλογές συμπίεσης κώδικα. Ο παραγόμενος κώδικας μπορεί να χρησιμοποιηθεί απευθείας για επεξεργασία σελίδων wiki, εξασφαλίζοντας τέλεια εμφάνιση σε πλατφόρμες MediaWiki."
    from_alias: "Πίνακας MediaWiki"
    to_alias: "Πίνακας MediaWiki"
  TracWiki:
    alias: "Πίνακας TracWiki"
    what: "Το Trac είναι ένα web-based σύστημα διαχείρισης έργων και παρακολούθησης σφαλμάτων που χρησιμοποιεί απλοποιημένη σύνταξη wiki για τη δημιουργία περιεχομένου πίνακα."
    step1: "Μεταφορτώστε αρχεία TracWiki ή επικολλήστε δεδομένα πίνακα."
    step3: "Δημιουργήστε κώδικα πίνακα συμβατό με TracWiki με υποστήριξη ρυθμίσεων κεφαλίδων σειρών/στηλών, διευκολύνοντας τη διαχείριση εγγράφων έργου."
    from_alias: "Πίνακας TracWiki"
    to_alias: "Πίνακας TracWiki"
  AsciiDoc:
    alias: "Πίνακας AsciiDoc"
    what: "Το AsciiDoc είναι μια ελαφριά γλώσσα σήμανσης που μπορεί να μετατραπεί σε HTML, PDF, σελίδες εγχειριδίου και άλλες μορφές, ευρέως χρησιμοποιούμενη για τη συγγραφή τεχνικής τεκμηρίωσης."
    step1: "Μεταφορτώστε αρχεία AsciiDoc ή επικολλήστε δεδομένα."
    step3: "Δημιουργήστε σύνταξη πίνακα AsciiDoc με υποστήριξη ρυθμίσεων κεφαλίδας, υποσέλιδου και τίτλου, άμεσα χρησιμοποιήσιμη σε επεξεργαστές AsciiDoc."
    from_alias: "Πίνακας AsciiDoc"
    to_alias: "Πίνακας AsciiDoc"
  reStructuredText:
    alias: "Πίνακας reStructuredText"
    what: "Το reStructuredText είναι η τυπική μορφή τεκμηρίωσης για την κοινότητα Python, υποστηρίζει πλούσια σύνταξη πίνακα, συνήθως χρησιμοποιούμενη για τη δημιουργία τεκμηρίωσης Sphinx."
    step1: "Μεταφορτώστε αρχεία .rst ή επικολλήστε δεδομένα reStructuredText."
    step3: "Δημιουργήστε τυπικούς πίνακες reStructuredText με υποστήριξη πολλαπλών στυλ περιγράμματος, άμεσα χρησιμοποιήσιμους σε έργα τεκμηρίωσης Sphinx."
    from_alias: "Πίνακας reStructuredText"
    to_alias: "Πίνακας reStructuredText"
  PHP:
    alias: "Πίνακας PHP"
    what: "Η PHP είναι μια δημοφιλής γλώσσα προγραμματισμού server-side, με τους πίνακες να αποτελούν την κύρια δομή δεδομένων της, ευρέως χρησιμοποιούμενη στην ανάπτυξη ιστοσελίδων και την επεξεργασία δεδομένων."
    step1: "Ανεβάστε αρχεία που περιέχουν PHP arrays ή επικολλήστε δεδομένα απευθείας."
    step3: "Δημιουργήστε τυπικό κώδικα PHP array που μπορεί να χρησιμοποιηθεί απευθείας σε PHP projects, υποστηρίζοντας associative και indexed array formats."
    from_alias: "Πίνακας PHP"
    to_alias: "Κώδικας PHP"
  Ruby:
    alias: "Πίνακας Ruby"
    what: "Η Ruby είναι μια δυναμική αντικειμενοστραφής γλώσσα προγραμματισμού με συνοπτική και κομψή σύνταξη, με τους πίνακες να αποτελούν σημαντική δομή δεδομένων."
    step1: "Ανεβάστε αρχεία Ruby ή επικολλήστε δεδομένα πίνακα."
    step3: "Δημιουργήστε κώδικα Ruby array που συμμορφώνεται με τις προδιαγραφές σύνταξης Ruby, άμεσα χρησιμοποιήσιμο σε Ruby projects."
    from_alias: "Πίνακας Ruby"
    to_alias: "Κώδικας Ruby"
  ASP:
    alias: "Πίνακας ASP"
    what: "Το ASP (Active Server Pages) είναι το server-side scripting περιβάλλον της Microsoft, υποστηρίζοντας πολλαπλές γλώσσες προγραμματισμού για την ανάπτυξη δυναμικών ιστοσελίδων."
    step1: "Ανεβάστε ASP αρχεία ή επικολλήστε δεδομένα πίνακα."
    step3: "Δημιουργήστε ASP-συμβατό κώδικα πίνακα με υποστήριξη για VBScript και JScript σύνταξη, χρησιμοποιήσιμο σε ASP.NET projects."
    from_alias: "Πίνακας ASP"
    to_alias: "Κώδικας ASP"
  ActionScript:
    alias: "Πίνακας ActionScript"
    what: "Η ActionScript είναι μια αντικειμενοστραφής γλώσσα προγραμματισμού που χρησιμοποιείται κυρίως για την ανάπτυξη εφαρμογών Adobe Flash και AIR."
    step1: "Μεταφορτώστε αρχεία .as ή επικολλήστε δεδομένα ActionScript."
    step3: "Δημιουργήστε κώδικα πίνακα ActionScript που συμμορφώνεται με τα πρότυπα σύνταξης AS3, χρησιμοποιήσιμο για ανάπτυξη έργων Flash και Flex."
    from_alias: "Πίνακας ActionScript"
    to_alias: "Κώδικας ActionScript"
  BBCode:
    alias: "Πίνακας BBCode"
    what: "Το BBCode είναι μια ελαφριά γλώσσα σήμανσης που χρησιμοποιείται συνήθως σε φόρουμ και online κοινότητες, παρέχοντας απλή λειτουργικότητα μορφοποίησης συμπεριλαμβανομένης υποστήριξης πινάκων."
    step1: "Μεταφορτώστε αρχεία που περιέχουν BBCode ή επικολλήστε δεδομένα."
    step3: "Δημιουργήστε κώδικα πίνακα BBCode κατάλληλο για δημοσίευση σε φόρουμ και δημιουργία περιεχομένου κοινότητας, με υποστήριξη συμπιεσμένης μορφής εξόδου."
    from_alias: "Πίνακας BBCode"
    to_alias: "Πίνακας BBCode"
  PDF:
    alias: "Πίνακας PDF"
    what: "Το PDF (Portable Document Format) είναι ένα διαπλατφορμικό πρότυπο εγγράφων με σταθερή διάταξη, συνεπή εμφάνιση και χαρακτηριστικά υψηλής ποιότητας εκτύπωσης. Ευρέως χρησιμοποιείται σε επίσημα έγγραφα, αναφορές, τιμολόγια, συμβόλαια και ακαδημαϊκές εργασίες. Το προτιμώμενο format για επιχειρηματική επικοινωνία και αρχειοθέτηση εγγράφων, εξασφαλίζοντας πλήρως συνεπή οπτικά αποτελέσματα σε διαφορετικές συσκευές και λειτουργικά συστήματα."
    step1: "Εισάγετε δεδομένα πίνακα σε οποιοδήποτε format. Το εργαλείο αναλύει αυτόματα τη δομή δεδομένων και εκτελεί έξυπνο σχεδιασμό διάταξης, υποστηρίζοντας αυτόματη σελιδοποίηση μεγάλων πινάκων και επεξεργασία πολύπλοκων τύπων δεδομένων."
    step3: "Δημιουργήστε υψηλής ποιότητας αρχεία PDF πίνακα με υποστήριξη πολλαπλών επαγγελματικών θεματικών στυλ (επιχειρηματικό, ακαδημαϊκό, μινιμαλιστικό, κλπ.), πολυγλωσσικές γραμματοσειρές, αυτόματη σελιδοποίηση, προσθήκη υδατογραφήματος και βελτιστοποίηση εκτύπωσης. Εξασφαλίζει ότι τα έγγραφα PDF εξόδου έχουν επαγγελματική εμφάνιση, άμεσα χρησιμοποιήσιμα για επιχειρηματικές παρουσιάσεις και επίσημη δημοσίευση."
    from_alias: "Δεδομένα Πίνακα"
    to_alias: "Πίνακας PDF"
  JPEG:
    alias: "Εικόνα JPEG"
    what: "Το JPEG είναι το πιο ευρέως χρησιμοποιούμενο ψηφιακό format εικόνας με εξαιρετικά αποτελέσματα συμπίεσης και ευρεία συμβατότητα. Το μικρό μέγεθος αρχείου και η γρήγορη ταχύτητα φόρτωσης το καθιστούν κατάλληλο για web εμφάνιση, κοινοποίηση σε social media, εικονογραφήσεις εγγράφων και online παρουσιάσεις. Το standard format εικόνας για ψηφιακά μέσα και δικτυακή επικοινωνία, τέλεια υποστηριζόμενο από σχεδόν όλες τις συσκευές και λογισμικό."
    step1: "Εισάγετε δεδομένα πίνακα σε οποιοδήποτε format. Το εργαλείο εκτελεί έξυπνο σχεδιασμό διάταξης και οπτική βελτιστοποίηση, υπολογίζοντας αυτόματα το βέλτιστο μέγεθος και ανάλυση."
    step3: "Δημιουργήστε υψηλής ευκρίνειας JPEG εικόνες πίνακα με υποστήριξη πολλαπλών θεματικών χρωματικών σχημάτων (φωτεινό, σκοτεινό, φιλικό προς τα μάτια, κλπ.), προσαρμοστική διάταξη, βελτιστοποίηση σαφήνειας κειμένου και προσαρμογή μεγέθους. Κατάλληλο για online κοινοποίηση, εισαγωγή εγγράφων και χρήση παρουσιάσεων, εξασφαλίζοντας εξαιρετικά οπτικά αποτελέσματα σε διάφορες συσκευές εμφάνισης."
    from_alias: "Δεδομένα Πίνακα"
    to_alias: "Εικόνα JPEG"
  Jira:
    alias: "Πίνακας Jira"
    what: "Το JIRA είναι επαγγελματικό λογισμικό διαχείρισης έργων και παρακολούθησης σφαλμάτων που αναπτύχθηκε από την Atlassian, ευρέως χρησιμοποιούμενο στην agile ανάπτυξη, δοκιμές λογισμικού και συνεργασία έργων. Η λειτουργικότητα πίνακά του υποστηρίζει πλούσιες επιλογές μορφοποίησης και εμφάνιση δεδομένων, λειτουργώντας ως σημαντικό εργαλείο για ομάδες ανάπτυξης λογισμικού, διαχειριστές έργων και προσωπικό διασφάλισης ποιότητας στη διαχείριση απαιτήσεων, παρακολούθηση σφαλμάτων και αναφορά προόδου."
    step1: "Μεταφορτώστε αρχεία που περιέχουν δεδομένα πίνακα ή επικολλήστε απευθείας περιεχόμενο δεδομένων. Το εργαλείο επεξεργάζεται αυτόματα δεδομένα πίνακα και διαφυγή ειδικών χαρακτήρων."
    step3: "Δημιουργήστε κώδικα πίνακα συμβατό με την πλατφόρμα JIRA με υποστήριξη ρυθμίσεων στυλ κεφαλίδας, στοίχιση κελιών, επεξεργασία διαφυγής χαρακτήρων και βελτιστοποίηση μορφής. Ο παραγόμενος κώδικας μπορεί να επικολληθεί απευθείας σε περιγραφές ζητημάτων JIRA, σχόλια ή σελίδες wiki, εξασφαλίζοντας σωστή εμφάνιση και απόδοση σε συστήματα JIRA."
    from_alias: "Πίνακας Jira"
    to_alias: "Πίνακας Jira"
  Textile:
    alias: "Πίνακας Textile"
    what: "Το Textile είναι μια συνοπτική ελαφριά γλώσσα σήμανσης με απλή και εύκολη στην εκμάθηση σύνταξη, ευρέως χρησιμοποιούμενη σε συστήματα διαχείρισης περιεχομένου, πλατφόρμες blog και συστήματα φόρουμ. Η σύνταξη πίνακά του είναι σαφής και διαισθητική, υποστηρίζοντας γρήγορη μορφοποίηση και ρυθμίσεις στυλ. Ένα ιδανικό εργαλείο για δημιουργούς περιεχομένου και διαχειριστές ιστοσελίδων για γρήγορη συγγραφή εγγράφων και δημοσίευση περιεχομένου."
    step1: "Μεταφορτώστε αρχεία μορφής Textile ή επικολλήστε δεδομένα πίνακα. Το εργαλείο αναλύει τη σύνταξη σήμανσης Textile και εξάγει περιεχόμενο πίνακα."
    step3: "Δημιουργήστε τυπική σύνταξη πίνακα Textile με υποστήριξη σήμανσης κεφαλίδας, στοίχιση κελιών, διαφυγή ειδικών χαρακτήρων και βελτιστοποίηση μορφής. Ο παραγόμενος κώδικας μπορεί να χρησιμοποιηθεί απευθείας σε συστήματα CMS, πλατφόρμες blog και συστήματα εγγράφων που υποστηρίζουν Textile, εξασφαλίζοντας σωστή απόδοση και εμφάνιση περιεχομένου."
    from_alias: "Πίνακας Textile"
    to_alias: "Πίνακας Textile"
  PNG:
    alias: "Εικόνα PNG"
    what: "Το PNG (Portable Network Graphics) είναι μια μορφή εικόνας χωρίς απώλειες με εξαιρετική συμπίεση και υποστήριξη διαφάνειας. Ευρέως χρησιμοποιούμενο στο web design, ψηφιακά γραφικά και επαγγελματική φωτογραφία. Η υψηλή ποιότητα και η ευρεία συμβατότητά του το καθιστούν ιδανικό για στιγμιότυπα οθόνης, λογότυπα, διαγράμματα και οποιεσδήποτε εικόνες που απαιτούν ευκρινείς λεπτομέρειες και διαφανή φόντα."
    step1: "Εισάγετε δεδομένα πίνακα σε οποιαδήποτε μορφή. Το εργαλείο εκτελεί έξυπνο σχεδιασμό διάταξης και οπτική βελτιστοποίηση, υπολογίζοντας αυτόματα το βέλτιστο μέγεθος και ανάλυση για έξοδο PNG."
    step3: "Δημιουργήστε υψηλής ποιότητας εικόνες πίνακα PNG με υποστήριξη πολλαπλών θεματικών χρωματικών σχημάτων, διαφανή φόντα, προσαρμοστική διάταξη και βελτιστοποίηση σαφήνειας κειμένου. Τέλειο για χρήση στο web, εισαγωγή εγγράφων και επαγγελματικές παρουσιάσεις με εξαιρετική οπτική ποιότητα."
    from_alias: ""
    to_alias: "Εικόνα PNG"
  TOML:
    alias: "TOML"
    what: "Το TOML (Tom's Obvious, Minimal Language) είναι ένα format αρχείου διαμόρφωσης που είναι εύκολο να διαβαστεί και να γραφτεί. Σχεδιασμένο να είναι σαφές και απλό, χρησιμοποιείται ευρέως σε σύγχρονα software projects για διαχείριση διαμόρφωσης. Η σαφής σύνταξη και η ισχυρή τυποποίηση το καθιστούν εξαιρετική επιλογή για ρυθμίσεις εφαρμογών και αρχεία διαμόρφωσης projects."
    step1: "Ανεβάστε TOML αρχεία ή επικολλήστε δεδομένα διαμόρφωσης. Το εργαλείο αναλύει τη TOML σύνταξη και εξάγει δομημένες πληροφορίες διαμόρφωσης."
    step3: "Δημιουργήστε τυπικό TOML format με υποστήριξη για nested δομές, τύπους δεδομένων και σχόλια. Τα παραγόμενα TOML αρχεία είναι τέλεια για διαμόρφωση εφαρμογών, build tools και ρυθμίσεις projects."
    from_alias: "TOML"
    to_alias: "Μορφή TOML"
  INI:
    alias: "INI"
    what: "Τα αρχεία INI είναι απλά αρχεία διαμόρφωσης που χρησιμοποιούνται από πολλές εφαρμογές και λειτουργικά συστήματα. Η απλή δομή τους με ζεύγη κλειδιού-αξίας τα καθιστά εύκολα στην ανάγνωση και χειροκίνητη επεξεργασία. Ευρέως χρησιμοποιούνται σε εφαρμογές Windows, παλαιά συστήματα και απλές διαμορφώσεις όπου η ανθρώπινη αναγνωσιμότητα είναι σημαντική."
    step1: "Ανεβάστε INI αρχεία ή επικολλήστε δεδομένα διαμόρφωσης. Το εργαλείο αναλύει τη σύνταξη INI και εξάγει πληροφορίες διαμόρφωσης βασισμένες σε ενότητες."
    step3: "Δημιουργήστε τυπικό INI format με υποστήριξη για ενότητες, σχόλια και διάφορους τύπους δεδομένων. Τα παραγόμενα INI αρχεία είναι συμβατά με τις περισσότερες εφαρμογές και συστήματα διαμόρφωσης."
    from_alias: "INI"
    to_alias: "Μορφή INI"
  Avro:
    alias: "Σχήμα Avro"
    what: "Το Apache Avro είναι ένα σύστημα σειριοποίησης δεδομένων που παρέχει πλούσιες δομές δεδομένων, συμπαγές δυαδικό format και δυνατότητες εξέλιξης σχήματος. Ευρέως χρησιμοποιείται στην επεξεργασία μεγάλων δεδομένων, ουρές μηνυμάτων και κατανεμημένα συστήματα. Ο ορισμός του σχήματός του υποστηρίζει πολύπλοκους τύπους δεδομένων και συμβατότητα εκδόσεων, καθιστώντας το σημαντικό εργαλείο για μηχανικούς δεδομένων και αρχιτέκτονες συστημάτων."
    step1: "Ανεβάστε αρχεία Avro schema ή επικολλήστε δεδομένα. Το εργαλείο αναλύει τους ορισμούς Avro schema και εξάγει πληροφορίες δομής πινάκων."
    step3: "Δημιουργήστε τυπικούς ορισμούς Avro schema με υποστήριξη για αντιστοίχιση τύπων δεδομένων, περιορισμούς πεδίων και επικύρωση σχήματος. Τα παραγόμενα σχήματα μπορούν να χρησιμοποιηθούν απευθείας σε οικοσυστήματα Hadoop, συστήματα μηνυμάτων Kafka και άλλες πλατφόρμες μεγάλων δεδομένων."
    from_alias: "Σχήμα Avro"
    to_alias: "Σχήμα Avro"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Τα Protocol Buffers (protobuf) είναι ο μηχανισμός της Google που είναι ανεξάρτητος από γλώσσα και πλατφόρμα, επεκτάσιμος για τη σειριοποίηση δομημένων δεδομένων. Ευρέως χρησιμοποιείται σε microservices, ανάπτυξη API και αποθήκευση δεδομένων. Το αποδοτικό δυαδικό του format και η ισχυρή τυποποίηση το καθιστούν ιδανικό για εφαρμογές υψηλής απόδοσης και επικοινωνία μεταξύ γλωσσών."
    step1: "Ανεβάστε αρχεία .proto ή επικολλήστε ορισμούς Protocol Buffer. Το εργαλείο αναλύει τη σύνταξη protobuf και εξάγει πληροφορίες δομής μηνυμάτων."
    step3: "Δημιουργήστε τυπικούς ορισμούς Protocol Buffer με υποστήριξη για τύπους μηνυμάτων, επιλογές πεδίων και ορισμούς υπηρεσιών. Τα παραγόμενα αρχεία .proto μπορούν να μεταγλωττιστούν για πολλές γλώσσες προγραμματισμού."
    from_alias: "Protocol Buffer"
    to_alias: "Σχήμα Protobuf"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Το Pandas είναι η πιο δημοφιλής βιβλιοθήκη ανάλυσης δεδομένων στην Python, με το DataFrame να είναι η κύρια δομή δεδομένων του. Παρέχει ισχυρές δυνατότητες χειρισμού, καθαρισμού και ανάλυσης δεδομένων, ευρέως χρησιμοποιούμενο στην επιστήμη δεδομένων, μηχανική μάθηση και business intelligence. Ένα απαραίτητο εργαλείο για Python developers και αναλυτές δεδομένων."
    step1: "Ανεβάστε Python αρχεία που περιέχουν DataFrame κώδικα ή επικολλήστε δεδομένα. Το εργαλείο αναλύει τη σύνταξη Pandas και εξάγει πληροφορίες δομής DataFrame."
    step3: "Δημιουργήστε τυπικό Pandas DataFrame κώδικα με υποστήριξη προδιαγραφών τύπων δεδομένων, ρυθμίσεων ευρετηρίου και λειτουργιών δεδομένων. Ο παραγόμενος κώδικας μπορεί να εκτελεστεί απευθείας σε Python περιβάλλον για ανάλυση και επεξεργασία δεδομένων."
    from_alias: "Pandas DataFrame"
    to_alias: "Pandas DataFrame"
  RDF:
    alias: "Τριπλέτα RDF"
    what: "Το RDF (Resource Description Framework) είναι ένα τυπικό μοντέλο για ανταλλαγή δεδομένων στον Ιστό, σχεδιασμένο να αναπαριστά πληροφορίες για πόρους σε μορφή γραφήματος. Ευρέως χρησιμοποιείται σε σημασιολογικό ιστό, γραφήματα γνώσης και εφαρμογές συνδεδεμένων δεδομένων. Η δομή τριπλέτων του επιτρέπει πλούσια αναπαράσταση μεταδεδομένων και σημασιολογικές σχέσεις."
    step1: "Ανεβάστε αρχεία RDF ή επικολλήστε δεδομένα τριπλέτων. Το εργαλείο αναλύει τη σύνταξη RDF και εξάγει σημασιολογικές σχέσεις και πληροφορίες πόρων."
    step3: "Δημιουργήστε τυπικό RDF format με υποστήριξη για διάφορες σειριοποιήσεις (RDF/XML, Turtle, N-Triples). Το παραγόμενο RDF μπορεί να χρησιμοποιηθεί σε εφαρμογές σημασιολογικού ιστού, βάσεις γνώσης και συστήματα συνδεδεμένων δεδομένων."
    from_alias: "RDF"
    to_alias: "Τριπλέτα RDF"
  MATLAB:
    alias: "Πίνακας MATLAB"
    what: "Το MATLAB είναι λογισμικό υψηλής απόδοσης για αριθμητικές υπολογισμούς και οπτικοποίηση που χρησιμοποιείται ευρέως σε μηχανική υπολογιστική, ανάλυση δεδομένων και ανάπτυξη αλγορίθμων. Οι λειτουργίες πινάκων και μητρών του είναι ισχυρές, υποστηρίζοντας πολύπλοκους μαθηματικούς υπολογισμούς και επεξεργασία δεδομένων. Ένα απαραίτητο εργαλείο για μηχανικούς, ερευνητές και επιστήμονες δεδομένων."
    step1: "Ανεβάστε αρχεία MATLAB .m ή επικολλήστε δεδομένα πινάκων. Το εργαλείο αναλύει τη σύνταξη MATLAB και εξάγει πληροφορίες δομής πινάκων."
    step3: "Δημιουργήστε τυπικό κώδικα MATLAB πινάκων με υποστήριξη για πολυδιάστατους πίνακες, προδιαγραφές τύπων δεδομένων και ονομασία μεταβλητών. Ο παραγόμενος κώδικας μπορεί να εκτελεστεί απευθείας σε περιβάλλον MATLAB για ανάλυση δεδομένων και επιστημονικούς υπολογισμούς."
    from_alias: "Πίνακας MATLAB"
    to_alias: "Πίνακας MATLAB"
  RDataFrame:
    alias: "R DataFrame"
    what: "Το R DataFrame είναι η κεντρική δομή δεδομένων στη γλώσσα προγραμματισμού R, ευρέως χρησιμοποιούμενη σε στατιστική ανάλυση, εξόρυξη δεδομένων και μηχανική μάθηση. Η R είναι το κυρίαρχο εργαλείο για στατιστικούς υπολογισμούς και γραφικά, με το DataFrame να παρέχει ισχυρές δυνατότητες χειρισμού δεδομένων, στατιστικής ανάλυσης και οπτικοποίησης. Απαραίτητο για επιστήμονες δεδομένων, στατιστικολόγους και ερευνητές που εργάζονται με δομημένη ανάλυση δεδομένων."
    step1: "Ανεβάστε αρχεία δεδομένων R ή επικολλήστε κώδικα DataFrame. Το εργαλείο αναλύει τη σύνταξη R και εξάγει πληροφορίες δομής DataFrame συμπεριλαμβανομένων τύπων στηλών, ονομάτων γραμμών και περιεχομένου δεδομένων."
    step3: "Δημιουργήστε τυπικό κώδικα R DataFrame με υποστήριξη για προδιαγραφές τύπων δεδομένων, επίπεδα παραγόντων, ονόματα γραμμών/στηλών και δομές δεδομένων ειδικές για R. Ο παραγόμενος κώδικας μπορεί να εκτελεστεί απευθείας σε περιβάλλον R για στατιστική ανάλυση και επεξεργασία δεδομένων."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Έναρξη Μετατροπής"
  start_generating: "Έναρξη Γέννησης"
  api_docs: "Τεκμηρίωση API"
related:
  section_title: 'Περισσότεροι {{ if and .from (ne .from "generator") }}{{ .from }} και {{ end }}{{ .to }} Μετατροπείς'
  section_description: 'Εξερευνήστε περισσότερους μετατροπείς για {{ if and .from (ne .from "generator") }}{{ .from }} και {{ end }}{{ .to }} μορφές. Μετατρέψτε τα δεδομένα σας μεταξύ πολλαπλών μορφών με τα επαγγελματικά online εργαλεία μετατροπής μας.'
  title: "{{ .from }} σε {{ .to }}"
howto:
  step2: "Επεξεργαστείτε δεδομένα χρησιμοποιώντας τον προηγμένο online επεξεργαστή πινάκων μας με επαγγελματικές λειτουργίες. Υποστηρίζει διαγραφή κενών γραμμών, αφαίρεση διπλότυπων, μεταφορά δεδομένων, ταξινόμηση, regex εύρεση και αντικατάσταση, και προεπισκόπηση σε πραγματικό χρόνο. Όλες οι αλλαγές μετατρέπονται αυτόματα σε %s μορφή με ακριβή, αξιόπιστα αποτελέσματα."
  section_title: "Πώς να χρησιμοποιήσετε το {{ . }}"
  converter_description: "Μάθετε να μετατρέπετε {{ .from }} σε {{ .to }} με τον βήμα προς βήμα οδηγό μας. Επαγγελματικός online μετατροπέας με προηγμένες λειτουργίες και προεπισκόπηση σε πραγματικό χρόνο."
  generator_description: "Μάθετε να δημιουργείτε επαγγελματικούς {{ .to }} πίνακες με την online γεννήτρια μας. Επεξεργασία τύπου Excel, προεπισκόπηση σε πραγματικό χρόνο και άμεσες δυνατότητες εξαγωγής."
extension:
  section_title: "Επέκταση Ανίχνευσης και Εξαγωγής Πινάκων"
  section_description: "Εξάγετε πίνακες από οποιαδήποτε ιστοσελίδα με ένα κλικ. Μετατρέψτε σε 30+ μορφές συμπεριλαμβανομένων Excel, CSV, JSON άμεσα - δεν απαιτείται αντιγραφή και επικόλληση."
  features:
    extraction_title: "Εξαγωγή Πίνακα με Ένα Κλικ"
    extraction_description: "Εξάγετε πίνακες άμεσα από οποιαδήποτε ιστοσελίδα χωρίς αντιγραφή και επικόλληση - επαγγελματική εξαγωγή δεδομένων γίνεται απλή"
    formats_title: "Υποστήριξη Μετατροπέα 30+ Μορφών"
    formats_description: "Μετατρέψτε εξαγόμενους πίνακες σε Excel, CSV, JSON, Markdown, SQL, και περισσότερα με τον προηγμένο μετατροπέα πινάκων μας"
    detection_title: "Έξυπνη Ανίχνευση Πινάκων"
    detection_description: "Ανιχνεύει αυτόματα και επισημαίνει πίνακες σε οποιαδήποτε ιστοσελίδα για γρήγορη εξαγωγή και μετατροπή δεδομένων"
  hover_tip: "✨ Περάστε το ποντίκι πάνω από οποιονδήποτε πίνακα για να δείτε το εικονίδιο εξαγωγής"
recommendations:
  section_title: "Συνιστάται από Πανεπιστήμια και Επαγγελματίες"
  section_description: "Το TableConvert είναι αξιόπιστο από επαγγελματίες σε πανεπιστήμια, ερευνητικά ιδρύματα και ομάδες ανάπτυξης για αξιόπιστη μετατροπή πινάκων και επεξεργασία δεδομένων."
  cards:
    university_title: "University of Wisconsin-Madison"
    university_description: "TableConvert.com - Επαγγελματικός δωρεάν online μετατροπέας πινάκων και εργαλείο μορφών δεδομένων"
    university_link: "Διαβάστε το άρθρο"
    facebook_title: "Κοινότητα Επαγγελματιών Δεδομένων"
    facebook_description: "Μοιράστηκε και συνιστάται από αναλυτές δεδομένων και επαγγελματίες σε ομάδες προγραμματιστών Facebook"
    facebook_link: "Δείτε την ανάρτηση"
    twitter_title: "Κοινότητα Προγραμματιστών"
    twitter_description: "Συνιστάται από @xiaoying_eth και άλλους προγραμματιστές στο X (Twitter) για μετατροπή πινάκων"
    twitter_link: "Δείτε το tweet"
faq:
  section_title: "Συχνές Ερωτήσεις"
  section_description: "Κοινές ερωτήσεις σχετικά με τον δωρεάν online μετατροπέα πινάκων μας, τις μορφές δεδομένων και τη διαδικασία μετατροπής."
  what: "Τι είναι η μορφή %s;"
  howto_convert:
    question: "Πώς να χρησιμοποιήσετε το {{ . }} δωρεάν;"
    answer: "Μεταφορτώστε το {{ .from }} αρχείο σας, επικολλήστε δεδομένα ή εξάγετε από ιστοσελίδες χρησιμοποιώντας τον δωρεάν online μετατροπέα πινάκων μας. Το επαγγελματικό εργαλείο μετατροπής μας μετατρέπει άμεσα τα δεδομένα σας σε {{ .to }} μορφή με προεπισκόπηση σε πραγματικό χρόνο και προηγμένες λειτουργίες επεξεργασίας. Κατεβάστε ή αντιγράψτε το μετατρεπόμενο αποτέλεσμα άμεσα."
  security:
    question: "Είναι τα δεδομένα μου ασφαλή όταν χρησιμοποιώ αυτόν τον online μετατροπέα;"
    answer: "Απολύτως! Όλες οι μετατροπές πινάκων γίνονται τοπικά στον περιηγητή σας - τα δεδομένα σας δεν φεύγουν ποτέ από τη συσκευή σας. Ο online μετατροπέας μας επεξεργάζεται τα πάντα από την πλευρά του πελάτη, εξασφαλίζοντας πλήρη ιδιωτικότητα και ασφάλεια δεδομένων. Δεν αποθηκεύονται αρχεία στους διακομιστές μας."
  free:
    question: "Είναι το TableConvert πραγματικά δωρεάν για χρήση;"
    answer: "Ναι, το TableConvert είναι εντελώς δωρεάν! Όλες οι λειτουργίες μετατροπέα, επεξεργαστής πινάκων, εργαλεία γεννήτριας δεδομένων και επιλογές εξαγωγής είναι διαθέσιμες χωρίς κόστος, εγγραφή ή κρυφές χρεώσεις. Μετατρέψτε απεριόριστα αρχεία online δωρεάν."
  filesize:
    question: "Ποια όρια μεγέθους αρχείου έχει ο online μετατροπέας;"
    answer: "Ο δωρεάν online μετατροπέας πινάκων μας υποστηρίζει αρχεία έως 10MB. Για μεγαλύτερα αρχεία, επεξεργασία παρτίδας ή εταιρικές ανάγκες, χρησιμοποιήστε την επέκταση περιηγητή μας ή την επαγγελματική υπηρεσία API με υψηλότερα όρια."
stats:
  conversions: "Πίνακες Μετατράπηκαν"
  tables: "Πίνακες Δημιουργήθηκαν"
  formats: "Μορφές Αρχείων Δεδομένων"
  rating: "Αξιολόγηση Χρήστη"
