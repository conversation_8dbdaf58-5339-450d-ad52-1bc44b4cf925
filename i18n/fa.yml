site:
  fullname: "تبدیل جدول آنلاین"
  name: "TableConvert"
  subtitle: "مبدل و تولیدکننده جدول آنلاین رایگان"
  intro: "TableConvert یک ابزار رایگان آنلاین برای تبدیل جدول و تولید داده است که از تبدیل بین بیش از ۳۰ فرمت شامل Excel، CSV، JSON، Markdown، LaTeX، SQL و موارد دیگر پشتیبانی می‌کند."
  followTwitter: "ما را در X دنبال کنید"
title:
  converter: "%s به %s"
  generator: "تولیدکننده %s"
post:
  tags:
    converter: "مبدل"
    editor: "ویرایشگر"
    generator: "تولیدکننده"
    maker: "سازنده"
  converter:
    title: "تبدیل %s به %s آنلاین"
    short: "ابزار آنلاین رایگان و قدرتمند %s به %s"
    intro: "مبدل آنلاین %s به %s آسان برای استفاده. داده‌های جدول را بدون زحمت با ابزار تبدیل شهودی ما تغییر دهید. سریع، قابل اعتماد و کاربرپسند."
  generator:
    title: "ویرایشگر و تولیدکننده %s آنلاین"
    short: "ابزار تولید آنلاین حرفه‌ای %s با ویژگی‌های جامع"
    intro: "تولیدکننده %s آنلاین و ویرایشگر جدول آسان برای استفاده. جداول داده حرفه‌ای را بدون زحمت با ابزار شهودی ما و پیش‌نمایش زمان واقعی ایجاد کنید."
navbar:
  search:
    placeholder: "جستجوی مبدل..."
  sponsor: "قهوه‌ای برای ما بخرید"
  extension: "افزونه"
  api: "رابط برنامه‌نویسی"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "منبع داده"
    placeholder: "داده‌های %s خود را بچسبانید یا فایل‌های %s را اینجا بکشید"
    example: "مثال"
    upload: "آپلود فایل"
    extract:
      enter: "استخراج از صفحه وب"
      intro: "URL صفحه وب حاوی داده‌های جدول را وارد کنید تا داده‌های ساختاریافته به طور خودکار استخراج شوند"
      btn: "استخراج %s"
    excel:
      sheet: "کاربرگ"
      none: "هیچ"
  tableEditor:
    title: "ویرایشگر جدول آنلاین"
    undo: "بازگردانی"
    redo: "تکرار"
    transpose: "ترانهاده"
    clear: "پاک کردن"
    deleteBlank: "حذف خالی‌ها"
    deleteDuplicate: "حذف تکراری‌ها"
    uppercase: "حروف بزرگ"
    lowercase: "حروف کوچک"
    capitalize: "حرف اول بزرگ"
    replace:
      replace: "جستجو و جایگزینی (پشتیبانی از Regex)"
      subst: "جایگزینی با..."
      btn: "جایگزینی همه"
  tableGenerator:
    title: "تولیدکننده جدول"
    sponsor: "قهوه‌ای برای ما بخرید"
    copy: "کپی در کلیپ‌بورد"
    download: "دانلود فایل"
    tooltip:
      html:
        escape: "کاراکترهای ویژه HTML (&, <, >, \", ') را برای جلوگیری از خطاهای نمایش فرار دهید"
        div: "به جای تگ‌های سنتی TABLE از چیدمان DIV+CSS استفاده کنید، برای طراحی واکنش‌گرا مناسب‌تر است"
        minify: "فضاهای خالی و شکست خط را حذف کنید تا کد HTML فشرده تولید شود"
        thead: "ساختار استاندارد سر جدول (&lt;thead&gt;) و بدنه (&lt;tbody&gt;) تولید کنید"
        tableCaption: "عنوان توضیحی بالای جدول اضافه کنید (عنصر &lt;caption&gt;)"
        tableClass: "نام کلاس CSS به جدول اضافه کنید برای سفارشی‌سازی آسان استایل"
        tableId: "شناسه منحصربه‌فرد ID برای جدول تنظیم کنید برای دستکاری JavaScript"
      jira:
        escape: "کاراکترهای پایپ (|) را برای جلوگیری از تداخل با نحو جدول Jira فرار دهید"
      json:
        parsingJSON: "رشته‌های JSON در سلول‌ها را به طور هوشمند به اشیاء تجزیه کنید"
        minify: "فرمت JSON فشرده تک‌خطی تولید کنید تا اندازه فایل کاهش یابد"
        format: "ساختار داده JSON خروجی را انتخاب کنید: آرایه شیء، آرایه 2D، و غیره"
      latex:
        escape: "کاراکترهای ویژه LaTeX (%, &, _, #, $, و غیره) را برای اطمینان از کامپایل صحیح فرار دهید"
        ht: "پارامتر موقعیت شناور [!ht] اضافه کنید تا موقعیت جدول در صفحه کنترل شود"
        mwe: "سند کامل LaTeX تولید کنید"
        tableAlign: "تراز افقی جدول در صفحه را تنظیم کنید"
        tableBorder: "استایل حاشیه جدول را پیکربندی کنید: بدون حاشیه، حاشیه جزئی، حاشیه کامل"
        label: "برچسب جدول را برای ارجاع متقابل دستور \\ref{} تنظیم کنید"
        caption: "عنوان جدول را برای نمایش بالا یا پایین جدول تنظیم کنید"
        location: "موقعیت نمایش عنوان جدول را انتخاب کنید: بالا یا پایین"
        tableType: "نوع محیط جدول را انتخاب کنید: tabular، longtable، array، و غیره"
      markdown:
        escape: "کاراکترهای ویژه Markdown (*, _, |, \\, و غیره) را برای جلوگیری از تداخل فرمت فرار دهید"
        pretty: "عرض ستون‌ها را خودکار تراز کنید تا فرمت جدول زیباتری تولید شود"
        simple: "نحو ساده‌شده استفاده کنید، خطوط عمودی حاشیه بیرونی را حذف کنید"
        boldFirstRow: "متن ردیف اول را پررنگ کنید"
        boldFirstColumn: "متن ستون اول را پررنگ کنید"
        firstHeader: "ردیف اول را به عنوان سرتیتر در نظر بگیرید و خط جداکننده اضافه کنید"
        textAlign: "تراز متن ستون را تنظیم کنید: چپ، وسط، راست"
        multilineHandling: "مدیریت متن چندخطی: حفظ شکست خط، فرار به \\n، استفاده از تگ‌های &lt;br&gt;"

        includeLineNumbers: "ستون شماره خط در سمت چپ جدول اضافه کنید"
      magic:
        builtin: "فرمت‌های قالب رایج از پیش تعریف‌شده را انتخاب کنید"
        rowsTpl: "<table> <tr> <th>نحو جادویی</th> <th>توضیحات</th> <th>پشتیبانی از متدهای JS</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>فیلد اول، دوم ... <b>سرتیتر</b>، یعنی {hA} {hB} ...</td> <td>متدهای رشته</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>فیلد اول، دوم ... ردیف فعلی، یعنی {$A} {$B} ...</td> <td>متدهای رشته</td> </tr> <tr> <td>{F,} {F;}</td> <td>ردیف فعلی را با رشته بعد از <b>F</b> تقسیم کنید</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td><b>شماره</b> خط <b>ردیف</b> فعلی از 1 یا 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>شماره</b> خط <b>پایانی</b> <b>ردیف‌ها</b> </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>کد JavaScript را <b>اجرا</b> کنید، مثال: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> از بک‌اسلش <b>\\</b> برای خروجی آکولادها {...} استفاده کنید </td> <td></td> </tr></table>"
        headerTpl: "قالب خروجی سفارشی برای بخش سرتیتر"
        footerTpl: "قالب خروجی سفارشی برای بخش پاورقی"
      textile:
        escape: "کاراکترهای نحو Textile (|, ., -, ^) را برای جلوگیری از تداخل فرمت فرار دهید"
        rowHeader: "ردیف اول را به عنوان ردیف سرتیتر تنظیم کنید"
        thead: "نشانگرهای نحو Textile برای سر و بدنه جدول اضافه کنید"
      xml:
        escape: "کاراکترهای ویژه XML (&lt;, &gt;, &amp;, \", ') را برای اطمینان از XML معتبر فرار دهید"
        minify: "خروجی XML فشرده تولید کنید، فضاهای خالی اضافی را حذف کنید"
        rootElement: "نام تگ عنصر ریشه XML را تنظیم کنید"
        rowElement: "نام تگ عنصر XML را برای هر ردیف داده تنظیم کنید"
        declaration: "سرتیتر اعلان XML اضافه کنید (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "داده‌ها را به عنوان ویژگی‌های XML به جای عناصر فرزند خروجی دهید"
        cdata: "محتوای متن را با CDATA بپوشانید تا کاراکترهای ویژه محافظت شوند"
        encoding: "فرمت کدگذاری کاراکتر برای سند XML تنظیم کنید"
        indentation: "کاراکتر تورفتگی XML را انتخاب کنید: فضاها یا تب‌ها"
      yaml:
        indentSize: "تعداد فضاها برای تورفتگی سلسله‌مراتب YAML تنظیم کنید (معمولاً 2 یا 4)"
        arrayStyle: "فرمت آرایه: بلوک (یک آیتم در هر خط) یا جریان (فرمت درون‌خطی)"
        quotationStyle: "استایل نقل‌قول رشته: بدون نقل‌قول، نقل‌قول تکی، نقل‌قول دوتایی"
      pdf:
        theme: "انتخاب سبک بصری جدول PDF برای اسناد حرفه‌ای"
        headerColor: "انتخاب رنگ پس‌زمینه سرتیتر جدول PDF"
        showHead: "کنترل نمایش سرتیتر در صفحات PDF"
        docTitle: "عنوان اختیاری برای سند PDF"
        docDescription: "متن توضیحی اختیاری برای سند PDF"
      csv:
        bom: "علامت ترتیب بایت UTF-8 اضافه کنید تا Excel و نرم‌افزارهای دیگر کدگذاری را تشخیص دهند"
      excel:
        autoWidth: "عرض ستون را بر اساس محتوا به طور خودکار تنظیم کنید"
        protectSheet: "محافظت کاربرگ را با رمز عبور فعال کنید: tableconvert.com"
      sql:
        primaryKey: "نام فیلد کلید اصلی را برای دستور CREATE TABLE مشخص کنید"
        dialect: "نوع پایگاه داده را انتخاب کنید که بر نحو نقل‌قول و نوع داده تأثیر می‌گذارد"
      ascii:
        forceSep: "خطوط جداکننده بین هر ردیف داده را اجبار کنید"
        style: "استایل ترسیم حاشیه جدول ASCII را انتخاب کنید"
        comment: "نشانگرهای نظر اضافه کنید تا کل جدول را بپوشانند"
      mediawiki:
        minify: "کد خروجی را فشرده کنید، فضاهای خالی اضافی را حذف کنید"
        header: "ردیف اول را به عنوان استایل سرتیتر علامت‌گذاری کنید"
        sort: "قابلیت مرتب‌سازی کلیک جدول را فعال کنید"
      asciidoc:
        minify: "خروجی فرمت AsciiDoc را فشرده کنید"
        firstHeader: "ردیف اول را به عنوان ردیف سرتیتر تنظیم کنید"
        lastFooter: "ردیف آخر را به عنوان ردیف پاورقی تنظیم کنید"
        title: "متن عنوان به جدول اضافه کنید"
      tracwiki:
        rowHeader: "ردیف اول را به عنوان سرتیتر تنظیم کنید"
        colHeader: "ستون اول را به عنوان سرتیتر تنظیم کنید"
      bbcode:
        minify: "فرمت خروجی BBCode را فشرده کنید"
      restructuredtext:
        style: "استایل حاشیه جدول reStructuredText را انتخاب کنید"
        forceSep: "خطوط جداکننده را اجبار کنید"
    label:
      ascii:
        forceSep: "جداکننده‌های ردیف"
        style: "استایل حاشیه"
        comment: "پوشش نظر"
      restructuredtext:
        style: "استایل حاشیه"
        forceSep: "اجبار جداکننده‌ها"
      bbcode:
        minify: "فشرده‌سازی خروجی"
      csv:
        doubleQuote: "پوشش نقل‌قول دوتایی"
        delimiter: "جداکننده فیلد"
        bom: "UTF-8 BOM"
        valueDelimiter: "جداکننده مقدار"
        rowDelimiter: "جداکننده ردیف"
        prefix: "پیشوند ردیف"
        suffix: "پسوند ردیف"
      excel:
        autoWidth: "عرض خودکار"
        textFormat: "فرمت متن"
        protectSheet: "محافظت کاربرگ"
        boldFirstRow: "ردیف اول پررنگ"
        boldFirstColumn: "ستون اول پررنگ"
        sheetName: "نام کاربرگ"
      html:
        escape: "فرار کاراکترهای HTML"
        div: "جدول DIV"
        minify: "فشرده‌سازی کد"
        thead: "ساختار سر جدول"
        tableCaption: "عنوان جدول"
        tableClass: "کلاس جدول"
        tableId: "شناسه جدول"
        rowHeader: "سرتیتر ردیف"
        colHeader: "سرتیتر ستون"
      jira:
        escape: "فرار کاراکترها"
        rowHeader: "سرتیتر ردیف"
        colHeader: "سرتیتر ستون"
      json:
        parsingJSON: "تجزیه JSON"
        minify: "فشرده‌سازی خروجی"
        format: "فرمت داده"
        rootName: "نام شیء ریشه"
        indentSize: "اندازه تورفتگی"
      jsonlines:
        parsingJSON: "تجزیه JSON"
        format: "فرمت داده"
      latex:
        escape: "فرار کاراکترهای جدول LaTeX"
        ht: "موقعیت شناور"
        mwe: "سند کامل"
        tableAlign: "تراز جدول"
        tableBorder: "استایل حاشیه"
        label: "برچسب مرجع"
        caption: "عنوان جدول"
        location: "موقعیت عنوان"
        tableType: "نوع جدول"
        boldFirstRow: "ردیف اول پررنگ"
        boldFirstColumn: "ستون اول پررنگ"
        textAlign: "تراز متن"
        borders: "تنظیمات حاشیه"
      markdown:
        escape: "فرار کاراکترها"
        pretty: "جدول Markdown زیبا"
        simple: "فرمت Markdown ساده"
        boldFirstRow: "ردیف اول پررنگ"
        boldFirstColumn: "ستون اول پررنگ"
        firstHeader: "سرتیتر اول"
        textAlign: "تراز متن"
        multilineHandling: "مدیریت چندخطی"

        includeLineNumbers: "افزودن شماره خط"
        align: "تراز"
      mediawiki:
        minify: "فشرده‌سازی کد"
        header: "نشانه‌گذاری سرتیتر"
        sort: "قابل مرتب‌سازی"
      asciidoc:
        minify: "فشرده‌سازی فرمت"
        firstHeader: "سرتیتر اول"
        lastFooter: "پاورقی آخر"
        title: "عنوان جدول"
      tracwiki:
        rowHeader: "سرتیتر ردیف"
        colHeader: "سرتیتر ستون"
      sql:
        drop: "حذف جدول (در صورت وجود)"
        create: "ایجاد جدول"
        oneInsert: "درج دسته‌ای"
        table: "نام جدول"
        dialect: "نوع پایگاه داده"
        primaryKey: "کلید اصلی"
      magic:
        builtin: "قالب داخلی"
        rowsTpl: "قالب ردیف، نحو ->"
        headerTpl: "قالب سرتیتر"
        footerTpl: "قالب پاورقی"
      textile:
        escape: "فرار کاراکترها"
        rowHeader: "سرتیتر ردیف"
        thead: "نحو سر جدول"
      xml:
        escape: "فرار کاراکترهای XML"
        minify: "فشرده‌سازی خروجی"
        rootElement: "عنصر ریشه"
        rowElement: "عنصر ردیف"
        declaration: "اعلان XML"
        attributes: "حالت ویژگی"
        cdata: "پوشش CDATA"
        encoding: "کدگذاری"
        indentSize: "اندازه تورفتگی"
      yaml:
        indentSize: "اندازه تورفتگی"
        arrayStyle: "استایل آرایه"
        quotationStyle: "استایل نقل‌قول"
      pdf:
        theme: "تم جدول PDF"
        headerColor: "رنگ سرتیتر PDF"
        showHead: "نمایش سرتیتر PDF"
        docTitle: "عنوان سند PDF"
        docDescription: "توضیحات سند PDF"
sidebar:
  all: "همه ابزارهای تبدیل"
  dataSource:
    title: "منبع داده"
    description:
      converter: "وارد کردن %s برای تبدیل به %s. پشتیبانی از آپلود فایل، ویرایش آنلاین و استخراج داده وب."
      generator: "ایجاد داده جدول با پشتیبانی از روش‌های ورودی متعدد شامل ورود دستی، وارد کردن فایل و تولید قالب."
  tableEditor:
    title: "ویرایشگر جدول آنلاین"
    description:
      converter: "پردازش %s آنلاین با استفاده از ویرایشگر جدول ما. تجربه عملیاتی شبیه Excel با پشتیبانی از حذف ردیف‌های خالی، حذف تکراری، مرتب‌سازی و جستجو و جایگزینی."
      generator: "ویرایشگر جدول آنلاین قدرتمند که تجربه عملیاتی شبیه Excel ارائه می‌دهد. پشتیبانی از حذف ردیف‌های خالی، حذف تکراری، مرتب‌سازی و جستجو و جایگزینی."
  tableGenerator:
    title: "تولیدکننده جدول"
    description:
      converter: "تولید سریع %s با پیش‌نمایش زمان واقعی تولیدکننده جدول. گزینه‌های صادرات غنی، کپی و دانلود یک کلیکه."
      generator: "صادرات داده %s در فرمت‌های متعدد برای پاسخ به سناریوهای استفاده مختلف. پشتیبانی از گزینه‌های سفارشی و پیش‌نمایش زمان واقعی."
footer:
  changelog: "تاریخچه تغییرات"
  sponsor: "حامیان مالی"
  contact: "تماس با ما"
  privacyPolicy: "سیاست حریم خصوصی"
  about: "درباره"
  resources: "منابع"
  popularConverters: "مبدل‌های محبوب"
  popularGenerators: "تولیدکننده‌های محبوب"
  dataSecurity: "داده‌های شما امن هستند - همه تبدیل‌ها در مرورگر شما اجرا می‌شوند."
converters:
  Markdown:
    alias: "جدول Markdown"
    what: "Markdown یک زبان نشانه‌گذاری سبک است که به طور گسترده برای مستندات فنی، ایجاد محتوای وبلاگ و توسعه وب استفاده می‌شود. نحو جدول آن مختصر و بصری است و از تراز متن، جاسازی لینک و قالب‌بندی پشتیبانی می‌کند. این ابزار ترجیحی برنامه‌نویسان و نویسندگان فنی است که کاملاً با GitHub، GitLab و سایر پلتفرم‌های میزبانی کد سازگار است."
    step1: "داده جدول Markdown را در ناحیه منبع داده بچسبانید یا مستقیماً فایل‌های .md را برای آپلود بکشید و رها کنید. ابزار به طور خودکار ساختار جدول و قالب‌بندی را تجزیه می‌کند و از محتوای تودرتوی پیچیده و مدیریت کاراکترهای ویژه پشتیبانی می‌کند."
    step3: "کد جدول Markdown استاندارد را در زمان واقعی تولید کنید که از روش‌های تراز متعدد، پررنگ کردن متن، افزودن شماره خط و سایر تنظیمات فرمت پیشرفته پشتیبانی می‌کند. کد تولید شده کاملاً با GitHub و ویرایشگرهای اصلی Markdown سازگار است و با یک کلیک آماده استفاده است."
    from_alias: "فایل جدول Markdown"
    to_alias: "فرمت جدول Markdown"
  Magic:
    alias: "قالب سفارشی"
    what: "قالب جادویی یک تولیدکننده داده پیشرفته منحصر به فرد این ابزار است که به کاربران اجازه می‌دهد از طریق نحو قالب سفارشی، خروجی داده با فرمت دلخواه ایجاد کنند. از جایگزینی متغیر، قضاوت شرطی و پردازش حلقه پشتیبانی می‌کند. این راه‌حل نهایی برای مدیریت نیازهای تبدیل داده پیچیده و فرمت‌های خروجی شخصی‌سازی شده است که به ویژه برای توسعه‌دهندگان و مهندسان داده مناسب است."
    step1: "قالب‌های رایج داخلی را انتخاب کنید یا نحو قالب سفارشی ایجاد کنید. از متغیرها و توابع غنی پشتیبانی می‌کند که می‌تواند ساختارهای داده پیچیده و منطق تجاری را مدیریت کند."
    step3: "خروجی داده‌ای تولید کنید که کاملاً با الزامات فرمت سفارشی مطابقت دارد. از منطق تبدیل داده پیچیده و پردازش شرطی پشتیبانی می‌کند که کارایی پردازش داده و کیفیت خروجی را به طور قابل توجهی بهبود می‌بخشد. ابزاری قدرتمند برای پردازش داده دسته‌ای."
    from_alias: "داده جدول"
    to_alias: "خروجی فرمت سفارشی"
  CSV:
    alias: "CSV"
    what: "CSV (مقادیر جدا شده با کاما) پرکاربردترین فرمت تبادل داده است که به طور کامل توسط Excel، Google Sheets، سیستم‌های پایگاه داده و ابزارهای تحلیل داده مختلف پشتیبانی می‌شود. ساختار ساده و سازگاری قوی آن، آن را به فرمت استاندارد برای مهاجرت داده، وارد/صادر کردن دسته‌ای و تبادل داده بین پلتفرمی تبدیل کرده است که به طور گسترده در تحلیل تجاری، علم داده و یکپارچه‌سازی سیستم استفاده می‌شود."
    step1: "فایل‌های CSV را آپلود کنید یا مستقیماً داده CSV بچسبانید. ابزار به طور هوشمند جداکننده‌های مختلف (کاما، تب، نقطه‌ویرگول، پایپ و غیره) را تشخیص می‌دهد، انواع داده و فرمت‌های کدگذاری را به طور خودکار شناسایی می‌کند و از تجزیه سریع فایل‌های بزرگ و ساختارهای داده پیچیده پشتیبانی می‌کند."
    step3: "فایل‌های فرمت CSV استاندارد با پشتیبانی از جداکننده‌های سفارشی، استایل‌های نقل‌قول، فرمت‌های کدگذاری و تنظیمات علامت BOM تولید کنید. سازگاری کامل با سیستم‌های هدف را تضمین می‌کند و گزینه‌های دانلود و فشرده‌سازی برای پاسخ به نیازهای پردازش داده سطح سازمانی ارائه می‌دهد."
    from_alias: "فایل داده CSV"
    to_alias: "فرمت استاندارد CSV"
  JSON:
    alias: "آرایه JSON"
    what: "JSON (نشانه‌گذاری شیء JavaScript) فرمت داده جدول استاندارد برای برنامه‌های وب مدرن، API های REST و معماری میکروسرویس است. ساختار واضح و تجزیه کارآمد آن باعث شده است که به طور گسترده در تعامل داده front-end و back-end، ذخیره‌سازی فایل پیکربندی و پایگاه‌های داده NoSQL استفاده شود. از اشیاء تودرتو، ساختارهای آرایه و انواع داده متعدد پشتیبانی می‌کند که آن را به داده جدول ضروری برای توسعه نرم‌افزار مدرن تبدیل می‌کند."
    step1: "فایل‌های JSON را آپلود کنید یا آرایه‌های JSON بچسبانید. از تشخیص و تجزیه خودکار آرایه‌های شیء، ساختارهای تودرتو و انواع داده پیچیده پشتیبانی می‌کند. ابزار به طور هوشمند نحو JSON را اعتبارسنجی می‌کند و اعلان‌های خطا ارائه می‌دهد."
    step3: "خروجی‌های فرمت JSON متعدد تولید کنید: آرایه‌های شیء استاندارد، آرایه‌های 2D، آرایه‌های ستون و فرمت‌های جفت کلید-مقدار. از خروجی زیباسازی شده، حالت فشرده‌سازی، نام‌های شیء ریشه سفارشی و تنظیمات تورفتگی پشتیبانی می‌کند که کاملاً با رابط‌های API مختلف و نیازهای ذخیره‌سازی داده سازگار است."
    from_alias: "فایل آرایه JSON"
    to_alias: "فرمت استاندارد JSON"
  JSONLines:
    alias: "فرمت JSONLines"
    what: "JSON Lines (همچنین به عنوان NDJSON شناخته می‌شود) فرمت مهمی برای پردازش کلان داده و انتقال داده جریانی است که هر خط حاوی یک شیء JSON مستقل است. به طور گسترده در تحلیل لاگ، پردازش جریان داده، یادگیری ماشین و سیستم‌های توزیع شده استفاده می‌شود. از پردازش تدریجی و محاسبات موازی پشتیبانی می‌کند که آن را به انتخاب ایده‌آل برای مدیریت داده‌های ساختاریافته مقیاس بزرگ تبدیل می‌کند."
    step1: "فایل‌های JSONLines را آپلود کنید یا داده بچسبانید. ابزار اشیاء JSON را خط به خط تجزیه می‌کند و از پردازش جریانی فایل‌های بزرگ و قابلیت رد کردن خط‌های خطا پشتیبانی می‌کند."
    step3: "فرمت JSONLines استاندارد تولید کنید که هر خط یک شیء JSON کامل خروجی می‌دهد. برای سناریوهای پردازش جریانی، وارد کردن دسته‌ای و تحلیل کلان داده مناسب است و از اعتبارسنجی داده و بهینه‌سازی فرمت پشتیبانی می‌کند."
    from_alias: "داده JSONLines"
    to_alias: "فرمت جریانی JSONLines"
  XML:
    alias: "XML"
    what: "XML (زبان نشانه‌گذاری قابل گسترش) فرمت استاندارد برای تبادل داده سطح سازمانی و مدیریت پیکربندی است که دارای مشخصات نحوی سخت‌گیرانه و مکانیزم‌های اعتبارسنجی قدرتمند است. به طور گسترده در سرویس‌های وب، فایل‌های پیکربندی، ذخیره‌سازی اسناد و یکپارچه‌سازی سیستم استفاده می‌شود. از فضاهای نام، اعتبارسنجی طرحواره و تبدیل XSLT پشتیبانی می‌کند که آن را به داده جدول مهم برای برنامه‌های سازمانی تبدیل می‌کند."
    step1: "فایل‌های XML را آپلود کنید یا داده XML بچسبانید. ابزار به طور خودکار ساختار XML را تجزیه می‌کند و آن را به فرمت جدول تبدیل می‌کند و از فضای نام، مدیریت ویژگی و ساختارهای تودرتوی پیچیده پشتیبانی می‌کند."
    step3: "خروجی XML تولید کنید که با استانداردهای XML مطابقت دارد. از عناصر ریشه سفارشی، نام‌های عنصر ردیف، حالت‌های ویژگی، پوشش CDATA و تنظیمات کدگذاری کاراکتر پشتیبانی می‌کند. یکپارچگی داده و سازگاری را تضمین می‌کند و الزامات برنامه‌های سطح سازمانی را برآورده می‌کند."
    from_alias: "فایل داده XML"
    to_alias: "فرمت استاندارد XML"
  YAML:
    alias: "پیکربندی YAML"
    what: "YAML یک استاندارد سریال‌سازی داده دوستدار انسان است که به خاطر ساختار سلسله‌مراتبی واضح و نحو مختصر خود مشهور است. به طور گسترده در فایل‌های پیکربندی، زنجیره ابزارهای DevOps، Docker Compose و استقرار Kubernetes استفاده می‌شود. خوانایی قوی و نحو مختصر آن، آن را به فرمت پیکربندی مهم برای برنامه‌های cloud-native مدرن و عملیات خودکار تبدیل می‌کند."
    step1: "فایل‌های YAML را آپلود کنید یا داده YAML بچسبانید. ابزار به طور هوشمند ساختار YAML را تجزیه می‌کند و صحت نحو را اعتبارسنجی می‌کند و از فرمت‌های چند سندی و انواع داده پیچیده پشتیبانی می‌کند."
    step3: "خروجی فرمت YAML استاندارد با پشتیبانی از استایل‌های آرایه بلوک و جریان، تنظیمات نقل‌قول متعدد، تورفتگی سفارشی و حفظ نظرات تولید کنید. تضمین می‌کند که فایل‌های YAML خروجی کاملاً با تجزیه‌کننده‌ها و سیستم‌های پیکربندی مختلف سازگار باشند."
    from_alias: "فایل پیکربندی YAML"
    to_alias: "فرمت استاندارد YAML"
  MySQL:
      alias: "نتایج پرس‌وجوی MySQL"
      what: "MySQL محبوب‌ترین سیستم مدیریت پایگاه داده رابطه‌ای متن‌باز جهان است که به خاطر عملکرد بالا، قابلیت اعتماد و سهولت استفاده مشهور است. به طور گسترده در برنامه‌های وب، سیستم‌های سازمانی و پلتفرم‌های تحلیل داده استفاده می‌شود. نتایج پرس‌وجوی MySQL معمولاً حاوی داده‌های جدول ساختاریافته هستند که به عنوان منبع داده مهم در مدیریت پایگاه داده و کار تحلیل داده عمل می‌کنند."
      step1: "نتایج خروجی پرس‌وجوی MySQL را در ناحیه منبع داده بچسبانید. ابزار به طور خودکار فرمت خروجی خط فرمان MySQL را تشخیص و تجزیه می‌کند و از استایل‌های نتیجه پرس‌وجوی مختلف و کدگذاری‌های کاراکتر پشتیبانی می‌کند و به طور هوشمند سرتیترها و ردیف‌های داده را مدیریت می‌کند."
      step3: "نتایج پرس‌وجوی MySQL را سریعاً به فرمت‌های داده جدول متعدد تبدیل کنید که تحلیل داده، تولید گزارش، مهاجرت داده بین سیستمی و اعتبارسنجی داده را تسهیل می‌کند. ابزاری عملی برای مدیران پایگاه داده و تحلیل‌گران داده."
      from_alias: "خروجی پرس‌وجوی MySQL"
      to_alias: "داده جدول MySQL"
  SQL:
    alias: "SQL درج"
    what: "SQL (زبان پرس‌وجوی ساختاریافته) زبان عملیات استاندارد برای پایگاه‌های داده رابطه‌ای است که برای عملیات پرس‌وجو، درج، به‌روزرسانی و حذف داده استفاده می‌شود. به عنوان فناوری هسته مدیریت پایگاه داده، SQL به طور گسترده در تحلیل داده، هوش تجاری، پردازش ETL و ساخت انبار داده استفاده می‌شود. این ابزار مهارت ضروری برای متخصصان داده است."
    step1: "دستورات SQL INSERT را بچسبانید یا فایل‌های .sql آپلود کنید. ابزار به طور هوشمند نحو SQL را تجزیه می‌کند و داده جدول را استخراج می‌کند و از گویش‌های SQL متعدد و پردازش دستورات پرس‌وجوی پیچیده پشتیبانی می‌کند."
    step3: "دستورات SQL INSERT استاندارد و دستورات ایجاد جدول تولید کنید. از گویش‌های پایگاه داده متعدد (MySQL، PostgreSQL، SQLite، SQL Server، Oracle) پشتیبانی می‌کند، به طور خودکار نگاشت نوع داده، فرار کاراکتر و محدودیت‌های کلید اصلی را مدیریت می‌کند. تضمین می‌کند که کد SQL تولید شده قابل اجرای مستقیم باشد."
    from_alias: "فایل داده SQL"
    to_alias: "دستور استاندارد SQL"
  Qlik:
      alias: "جدول Qlik"
      what: "Qlik یک فروشنده نرم‌افزار متخصص در تجسم داده، داشبوردهای اجرایی و محصولات هوش تجاری خودخدمت است، همراه با Tableau و Microsoft."
      step1: ""
      step3: "در نهایت، [تولیدکننده جدول](#TableGenerator) نتایج تبدیل را نشان می‌دهد. در Qlik Sense، Qlik AutoML، QlikView یا سایر نرم‌افزارهای فعال‌شده Qlik خود استفاده کنید."
      from_alias: "جدول Qlik"
      to_alias: "جدول Qlik"
  DAX:
      alias: "جدول DAX"
      what: "DAX (عبارات تحلیل داده) یک زبان برنامه‌نویسی است که در سراسر Microsoft Power BI برای ایجاد ستون‌های محاسبه‌شده، معیارها و جداول سفارشی استفاده می‌شود."
      step1: ""
      step3: "در نهایت، [تولیدکننده جدول](#TableGenerator) نتایج تبدیل را نشان می‌دهد. همان‌طور که انتظار می‌رود، در چندین محصول Microsoft شامل Microsoft Power BI، Microsoft Analysis Services و Microsoft Power Pivot برای Excel استفاده می‌شود."
      from_alias: "جدول DAX"
      to_alias: "جدول DAX"
  Firebase:
    alias: "فهرست Firebase"
    what: "Firebase یک پلتفرم توسعه برنامه BaaS است که سرویس‌های backend میزبانی‌شده مانند پایگاه داده بلادرنگ، ذخیره‌سازی ابری، احراز هویت، گزارش خرابی و غیره ارائه می‌دهد."
    step1: ""
    step3: "در نهایت، [تولیدکننده جدول](#TableGenerator) نتایج تبدیل را نشان می‌دهد. سپس می‌توانید از متد push در API Firebase برای افزودن به فهرست داده‌ها در پایگاه داده Firebase استفاده کنید."
    from_alias: "فهرست Firebase"
    to_alias: "فهرست Firebase"
  HTML:
    alias: "جدول HTML"
    what: "جداول HTML روش استاندارد نمایش داده‌های ساختاریافته در صفحات وب هستند که با تگ‌های table، tr، td و سایر تگ‌ها ساخته می‌شوند. از سفارشی‌سازی استایل غنی، چیدمان واکنش‌گرا و قابلیت تعاملی پشتیبانی می‌کند. به طور گسترده در توسعه وب‌سایت، نمایش داده و تولید گزارش استفاده می‌شود و به عنوان جزء مهم توسعه front-end و طراحی وب عمل می‌کند."
    step1: "کد HTML حاوی جداول را بچسبانید یا فایل‌های HTML آپلود کنید. ابزار به طور خودکار داده‌های جدول را از صفحات تشخیص و استخراج می‌کند و از ساختارهای HTML پیچیده، استایل‌های CSS و پردازش جداول تودرتو پشتیبانی می‌کند."
    step3: "کد جدول HTML معنایی تولید کنید با پشتیبانی از ساختار thead/tbody، تنظیمات کلاس CSS، عنوان‌های جدول، سرتیترهای ردیف/ستون و پیکربندی ویژگی واکنش‌گرا. تضمین می‌کند که کد جدول تولید شده با استانداردهای وب مطابقت دارد و دسترسی‌پذیری خوب و دوستدار SEO دارد."
    from_alias: "جدول وب HTML"
    to_alias: "جدول استاندارد HTML"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel محبوب‌ترین نرم‌افزار صفحه‌گسترده جهان است که به طور گسترده در تحلیل تجاری، مدیریت مالی، پردازش داده و ایجاد گزارش استفاده می‌شود. قابلیت‌های قدرتمند پردازش داده، کتابخانه توابع غنی و ویژگی‌های تجسم انعطاف‌پذیر آن را به ابزار استاندارد برای اتوماسیون اداری و تحلیل داده تبدیل کرده است که کاربردهای گسترده‌ای در تقریباً همه صنایع و زمینه‌ها دارد."
    step1: "فایل‌های Excel آپلود کنید (از فرمت‌های .xlsx، .xls پشتیبانی می‌کند) یا داده‌های جدول را مستقیماً از Excel کپی کرده و بچسبانید. ابزار از پردازش چند کاربرگی، تشخیص فرمت پیچیده و تجزیه سریع فایل‌های بزرگ پشتیبانی می‌کند و به طور خودکار سلول‌های ادغام‌شده و انواع داده را مدیریت می‌کند."
    step3: "داده‌های جدول سازگار با Excel تولید کنید که می‌تواند مستقیماً در Excel چسبانده شود یا به عنوان فایل‌های .xlsx استاندارد دانلود شود. از نام‌گذاری کاربرگ، قالب‌بندی سلول، عرض ستون خودکار، استایل سرتیتر و تنظیمات اعتبارسنجی داده پشتیبانی می‌کند. تضمین می‌کند که فایل‌های Excel خروجی ظاهر حرفه‌ای و قابلیت کامل داشته باشند."
    from_alias: "صفحه‌گسترده Excel"
    to_alias: "فرمت استاندارد Excel"
  LaTeX:
    alias: "جدول LaTeX"
    what: "LaTeX یک سیستم حروف‌چینی سند حرفه‌ای است که به ویژه برای ایجاد مقالات آکادمیک، اسناد فنی و انتشارات علمی مناسب است. قابلیت جدول آن قدرتمند است و از فرمول‌های ریاضی پیچیده، کنترل چیدمان دقیق و خروجی PDF با کیفیت بالا پشتیبانی می‌کند. این ابزار استاندارد در دانشگاه و انتشارات علمی است که به طور گسترده در مقالات مجله، پایان‌نامه‌ها و حروف‌چینی راهنمای فنی استفاده می‌شود."
    step1: "کد جدول LaTeX را بچسبانید یا فایل‌های .tex آپلود کنید. ابزار نحو جدول LaTeX را تجزیه می‌کند و محتوای داده را استخراج می‌کند و از محیط‌های جدول متعدد (tabular، longtable، array و غیره) و دستورات فرمت پیچیده پشتیبانی می‌کند."
    step3: "کد جدول LaTeX حرفه‌ای تولید کنید با پشتیبانی از انتخاب محیط جدول متعدد، پیکربندی استایل حاشیه، تنظیمات موقعیت عنوان، مشخصات کلاس سند و مدیریت بسته. می‌تواند اسناد LaTeX کامل قابل کامپایل تولید کند و تضمین می‌کند که جداول خروجی با استانداردهای انتشار آکادمیک مطابقت دارند."
    from_alias: "جدول سند LaTeX"
    to_alias: "فرمت حرفه‌ای LaTeX"
  ASCII:
    alias: "جدول ASCII"
    what: "جداول ASCII از کاراکترهای متن ساده برای ترسیم حاشیه‌ها و ساختارهای جدول استفاده می‌کنند و بهترین سازگاری و قابلیت حمل را ارائه می‌دهند. با همه ویرایشگرهای متن، محیط‌های ترمینال و سیستم‌عامل‌ها سازگار است. به طور گسترده در مستندات کد، راهنماهای فنی، فایل‌های README و خروجی ابزارهای خط فرمان استفاده می‌شود. فرمت نمایش داده ترجیحی برای برنامه‌نویسان و مدیران سیستم."
    step1: "فایل‌های متنی حاوی جداول ASCII آپلود کنید یا مستقیماً داده‌های جدول بچسبانید. ابزار به طور هوشمند ساختارهای جدول ASCII را تشخیص و تجزیه می‌کند و از استایل‌های حاشیه متعدد و فرمت‌های تراز پشتیبانی می‌کند."
    step3: "جداول ASCII متن ساده زیبا تولید کنید با پشتیبانی از استایل‌های حاشیه متعدد (خط تکی، خط دوتایی، گوشه‌های گرد و غیره)، روش‌های تراز متن و عرض ستون خودکار. جداول تولید شده در ویرایشگرهای کد، اسناد و خطوط فرمان به طور کامل نمایش داده می‌شوند."
    from_alias: "جدول متن ASCII"
    to_alias: "فرمت استاندارد ASCII"
  MediaWiki:
    alias: "جدول MediaWiki"
    what: "MediaWiki پلتفرم نرم‌افزار متن‌باز است که توسط سایت‌های ویکی مشهور مانند ویکی‌پدیا استفاده می‌شود. نحو جدول آن مختصر اما قدرتمند است و از سفارشی‌سازی استایل جدول، قابلیت مرتب‌سازی و جاسازی لینک پشتیبانی می‌کند. به طور گسترده در مدیریت دانش، ویرایش مشارکتی و سیستم‌های مدیریت محتوا استفاده می‌شود و به عنوان فناوری هسته برای ساخت دایره‌المعارف‌های ویکی و پایگاه‌های دانش عمل می‌کند."
    step1: "کد جدول MediaWiki را بچسبانید یا فایل‌های منبع ویکی آپلود کنید. ابزار نحو نشانه‌گذاری ویکی را تجزیه می‌کند و داده‌های جدول را استخراج می‌کند و از نحو ویکی پیچیده و پردازش قالب پشتیبانی می‌کند."
    step3: "کد جدول MediaWiki استاندارد تولید کنید با پشتیبانی از تنظیمات استایل سرتیتر، تراز سلول، فعال‌سازی قابلیت مرتب‌سازی و گزینه‌های فشرده‌سازی کد. کد تولید شده می‌تواند مستقیماً برای ویرایش صفحه ویکی استفاده شود و نمایش کامل در پلتفرم‌های MediaWiki را تضمین می‌کند."
    from_alias: "کد منبع MediaWiki"
    to_alias: "نحو جدول MediaWiki"
  TracWiki:
    alias: "جدول TracWiki"
    what: "Trac یک سیستم مدیریت پروژه و ردیابی باگ مبتنی بر وب است که از نحو ویکی ساده‌شده برای ایجاد محتوای جدول استفاده می‌کند."
    step1: "فایل‌های TracWiki آپلود کنید یا داده‌های جدول بچسبانید."
    step3: "کد جدول سازگار با TracWiki تولید کنید با پشتیبانی از تنظیمات سرتیتر ردیف/ستون که مدیریت اسناد پروژه را تسهیل می‌کند."
    from_alias: "جدول TracWiki"
    to_alias: "فرمت TracWiki"
  AsciiDoc:
    alias: "جدول AsciiDoc"
    what: "AsciiDoc یک زبان نشانه‌گذاری سبک است که می‌تواند به HTML، PDF، صفحات راهنما و فرمت‌های دیگر تبدیل شود و به طور گسترده برای نوشتن مستندات فنی استفاده می‌شود."
    step1: "فایل‌های AsciiDoc آپلود کنید یا داده بچسبانید."
    step3: "نحو جدول AsciiDoc تولید کنید با پشتیبانی از تنظیمات سرتیتر، پاورقی و عنوان که مستقیماً در ویرایشگرهای AsciiDoc قابل استفاده است."
    from_alias: "جدول AsciiDoc"
    to_alias: "فرمت AsciiDoc"
  reStructuredText:
    alias: "جدول reStructuredText"
    what: "reStructuredText فرمت مستندات استاندارد برای جامعه Python است که از نحو جدول غنی پشتیبانی می‌کند و معمولاً برای تولید مستندات Sphinx استفاده می‌شود."
    step1: "فایل‌های .rst آپلود کنید یا داده‌های reStructuredText بچسبانید."
    step3: "جداول reStructuredText استاندارد تولید کنید با پشتیبانی از استایل‌های حاشیه متعدد که مستقیماً در پروژه‌های مستندات Sphinx قابل استفاده است."
    from_alias: "جدول reStructuredText"
    to_alias: "فرمت reStructuredText"
  PHP:
    alias: "آرایه PHP"
    what: "PHP یک زبان اسکریپت‌نویسی سمت سرور محبوب است که آرایه‌ها ساختار داده اصلی آن هستند و به طور گسترده در توسعه وب و پردازش داده استفاده می‌شود."
    step1: "فایل‌های حاوی آرایه‌های PHP آپلود کنید یا مستقیماً داده بچسبانید."
    step3: "کد آرایه PHP استاندارد تولید کنید که می‌تواند مستقیماً در پروژه‌های PHP استفاده شود و از فرمت‌های آرایه انجمنی و شاخص‌دار پشتیبانی می‌کند."
    from_alias: "آرایه PHP"
    to_alias: "کد PHP"
  Ruby:
    alias: "آرایه Ruby"
    what: "Ruby یک زبان برنامه‌نویسی شیءگرا پویا با نحو مختصر و ظریف است که آرایه‌ها ساختار داده مهمی در آن هستند."
    step1: "فایل‌های Ruby آپلود کنید یا داده‌های آرایه بچسبانید."
    step3: "کد آرایه Ruby تولید کنید که با مشخصات نحو Ruby مطابقت دارد و مستقیماً در پروژه‌های Ruby قابل استفاده است."
    from_alias: "آرایه Ruby"
    to_alias: "کد Ruby"
  ASP:
    alias: "آرایه ASP"
    what: "ASP (صفحات سرور فعال) محیط اسکریپت‌نویسی سمت سرور مایکروسافت است که از زبان‌های برنامه‌نویسی متعدد برای توسعه صفحات وب پویا پشتیبانی می‌کند."
    step1: "فایل‌های ASP آپلود کنید یا داده‌های آرایه بچسبانید."
    step3: "کد آرایه سازگار با ASP تولید کنید با پشتیبانی از نحو VBScript و JScript که در پروژه‌های ASP.NET قابل استفاده است."
    from_alias: "آرایه ASP"
    to_alias: "کد ASP"
  ActionScript:
    alias: "آرایه ActionScript"
    what: "ActionScript یک زبان برنامه‌نویسی شیءگرا است که عمدتاً برای توسعه برنامه‌های Adobe Flash و AIR استفاده می‌شود."
    step1: "فایل‌های .as آپلود کنید یا داده‌های ActionScript بچسبانید."
    step3: "کد آرایه ActionScript تولید کنید که با استانداردهای نحو AS3 مطابقت دارد و برای توسعه پروژه‌های Flash و Flex قابل استفاده است."
    from_alias: "آرایه ActionScript"
    to_alias: "کد ActionScript"
  BBCode:
    alias: "جدول BBCode"
    what: "BBCode یک زبان نشانه‌گذاری سبک است که معمولاً در انجمن‌ها و جوامع آنلاین استفاده می‌شود و قابلیت قالب‌بندی ساده شامل پشتیبانی از جدول ارائه می‌دهد."
    step1: "فایل‌های حاوی BBCode آپلود کنید یا داده بچسبانید."
    step3: "کد جدول BBCode مناسب برای ارسال در انجمن و ایجاد محتوای جامعه تولید کنید با پشتیبانی از فرمت خروجی فشرده."
    from_alias: "جدول BBCode"
    to_alias: "فرمت BBCode"
  PDF:
    alias: "جدول PDF"
    what: "PDF (فرمت سند قابل حمل) یک استاندارد سند بین پلتفرمی با چیدمان ثابت، نمایش یکسان و ویژگی‌های چاپ با کیفیت بالا است. به طور گسترده در اسناد رسمی، گزارش‌ها، فاکتورها، قراردادها و مقالات آکادمیک استفاده می‌شود. فرمت ترجیحی برای ارتباطات تجاری و بایگانی اسناد که اثرات بصری کاملاً یکسان در دستگاه‌ها و سیستم‌عامل‌های مختلف را تضمین می‌کند."
    step1: "داده‌های جدول را در هر فرمتی وارد کنید. ابزار به طور خودکار ساختار داده را تحلیل می‌کند و طراحی چیدمان هوشمند انجام می‌دهد و از صفحه‌بندی خودکار جداول بزرگ و پردازش انواع داده پیچیده پشتیبانی می‌کند."
    step3: "فایل‌های جدول PDF با کیفیت بالا تولید کنید با پشتیبانی از استایل‌های تم حرفه‌ای متعدد (تجاری، آکادمیک، مینیمالیست و غیره)، فونت‌های چندزبانه، صفحه‌بندی خودکار، افزودن واترمارک و بهینه‌سازی چاپ. تضمین می‌کند که اسناد PDF خروجی ظاهر حرفه‌ای داشته باشند و مستقیماً برای ارائه‌های تجاری و انتشار رسمی قابل استفاده باشند."
    from_alias: "داده جدول"
    to_alias: "سند حرفه‌ای PDF"
  JPEG:
    alias: "تصویر JPEG"
    what: "JPEG پرکاربردترین فرمت تصویر دیجیتال با اثرات فشرده‌سازی عالی و سازگاری گسترده است. اندازه فایل کوچک و سرعت بارگذاری سریع آن را برای نمایش وب، اشتراک‌گذاری در رسانه‌های اجتماعی، تصاویر اسناد و ارائه‌های آنلاین مناسب می‌کند. فرمت تصویر استاندارد برای رسانه‌های دیجیتال و ارتباطات شبکه که تقریباً توسط همه دستگاه‌ها و نرم‌افزارها به طور کامل پشتیبانی می‌شود."
    step1: "داده‌های جدول را در هر فرمتی وارد کنید. ابزار طراحی چیدمان هوشمند و بهینه‌سازی بصری انجام می‌دهد و به طور خودکار اندازه و وضوح بهینه را محاسبه می‌کند."
    step3: "تصاویر جدول JPEG با وضوح بالا تولید کنید با پشتیبانی از طرح‌های رنگی تم متعدد (روشن، تیره، دوستدار چشم و غیره)، چیدمان تطبیقی، بهینه‌سازی وضوح متن و سفارشی‌سازی اندازه. برای اشتراک‌گذاری آنلاین، درج در اسناد و استفاده در ارائه‌ها مناسب است و اثرات بصری عالی در دستگاه‌های نمایش مختلف را تضمین می‌کند."
    from_alias: "داده جدول"
    to_alias: "تصویر با وضوح بالا JPEG"
  Jira:
    alias: "جدول Jira"
    what: "JIRA نرم‌افزار حرفه‌ای مدیریت پروژه و ردیابی باگ توسعه‌یافته توسط Atlassian است که به طور گسترده در توسعه چابک، تست نرم‌افزار و همکاری پروژه استفاده می‌شود. قابلیت جدول آن از گزینه‌های قالب‌بندی غنی و نمایش داده پشتیبانی می‌کند و به عنوان ابزار مهم برای تیم‌های توسعه نرم‌افزار، مدیران پروژه و پرسنل تضمین کیفیت در مدیریت الزامات، ردیابی باگ و گزارش پیشرفت عمل می‌کند."
    step1: "فایل‌های حاوی داده‌های جدول آپلود کنید یا مستقیماً محتوای داده بچسبانید. ابزار به طور خودکار داده‌های جدول و فرار کاراکترهای ویژه را پردازش می‌کند."
    step3: "کد جدول سازگار با پلتفرم JIRA تولید کنید با پشتیبانی از تنظیمات استایل سرتیتر، تراز سلول، پردازش فرار کاراکتر و بهینه‌سازی فرمت. کد تولید شده می‌تواند مستقیماً در توضیحات مسائل JIRA، نظرات یا صفحات ویکی چسبانده شود و نمایش و رندر صحیح در سیستم‌های JIRA را تضمین می‌کند."
    from_alias: "داده پروژه"
    to_alias: "نحو جدول Jira"
  Textile:
    alias: "جدول Textile"
    what: "Textile یک زبان نشانه‌گذاری سبک مختصر با نحو ساده و آسان برای یادگیری است که به طور گسترده در سیستم‌های مدیریت محتوا، پلتفرم‌های وبلاگ و سیستم‌های انجمن استفاده می‌شود. نحو جدول آن واضح و بصری است و از قالب‌بندی سریع و تنظیمات استایل پشتیبانی می‌کند. ابزار ایده‌آل برای سازندگان محتوا و مدیران وب‌سایت برای نوشتن سریع اسناد و انتشار محتوا."
    step1: "فایل‌های فرمت Textile آپلود کنید یا داده‌های جدول بچسبانید. ابزار نحو نشانه‌گذاری Textile را تجزیه می‌کند و محتوای جدول را استخراج می‌کند."
    step3: "نحو جدول Textile استاندارد تولید کنید با پشتیبانی از نشانه‌گذاری سرتیتر، تراز سلول، فرار کاراکترهای ویژه و بهینه‌سازی فرمت. کد تولید شده می‌تواند مستقیماً در سیستم‌های CMS، پلتفرم‌های وبلاگ و سیستم‌های سندی که از Textile پشتیبانی می‌کنند استفاده شود و رندر و نمایش صحیح محتوا را تضمین می‌کند."
    from_alias: "سند Textile"
    to_alias: "نحو جدول Textile"
  PNG:
    alias: "تصویر PNG"
    what: "PNG (گرافیک شبکه قابل حمل) یک فرمت تصویر بدون اتلاف با فشرده‌سازی عالی و پشتیبانی از شفافیت است. به طور گسترده در طراحی وب، گرافیک دیجیتال و عکاسی حرفه‌ای استفاده می‌شود. کیفیت بالا و سازگاری گسترده آن را برای اسکرین‌شات‌ها، لوگوها، نمودارها و هر تصویری که نیاز به جزئیات واضح و پس‌زمینه شفاف دارد، ایده‌آل می‌کند."
    step1: "داده‌های جدول را در هر فرمتی وارد کنید. ابزار طراحی چیدمان هوشمند و بهینه‌سازی بصری انجام می‌دهد و به طور خودکار اندازه و وضوح بهینه برای خروجی PNG محاسبه می‌کند."
    step3: "تصاویر جدول PNG با کیفیت بالا تولید کنید با پشتیبانی از طرح‌های رنگی تم متعدد، پس‌زمینه‌های شفاف، چیدمان تطبیقی و بهینه‌سازی وضوح متن. برای استفاده وب، درج در اسناد و ارائه‌های حرفه‌ای با کیفیت بصری عالی کامل است."
    from_alias: "داده جدول"
    to_alias: "تصویر با کیفیت بالا PNG"
  TOML:
    alias: "پیکربندی TOML"
    what: "TOML (زبان آشکار و مینیمال تام) یک فرمت فایل پیکربندی است که خواندن و نوشتن آن آسان است. طراحی شده تا بدون ابهام و ساده باشد، به طور گسترده در پروژه‌های نرم‌افزاری مدرن برای مدیریت پیکربندی استفاده می‌شود. نحو واضح و تایپ قوی آن را به انتخاب عالی برای تنظیمات برنامه و فایل‌های پیکربندی پروژه تبدیل می‌کند."
    step1: "فایل‌های TOML آپلود کنید یا داده‌های پیکربندی بچسبانید. ابزار نحو TOML را تجزیه می‌کند و اطلاعات پیکربندی ساختاریافته را استخراج می‌کند."
    step3: "فرمت TOML استاندارد تولید کنید با پشتیبانی از ساختارهای تودرتو، انواع داده و نظرات. فایل‌های TOML تولید شده برای پیکربندی برنامه، ابزارهای ساخت و تنظیمات پروژه کامل هستند."
    from_alias: "پیکربندی TOML"
    to_alias: "فرمت TOML"
  INI:
    alias: "پیکربندی INI"
    what: "فایل‌های INI فایل‌های پیکربندی ساده‌ای هستند که توسط بسیاری از برنامه‌ها و سیستم‌عامل‌ها استفاده می‌شوند. ساختار ساده جفت کلید-مقدار آن‌ها خواندن و ویرایش دستی را آسان می‌کند. به طور گسترده در برنامه‌های ویندوز، سیستم‌های قدیمی و سناریوهای پیکربندی ساده که خوانایی انسانی مهم است، استفاده می‌شود."
    step1: "فایل‌های INI آپلود کنید یا داده‌های پیکربندی بچسبانید. ابزار نحو INI را تجزیه می‌کند و اطلاعات پیکربندی مبتنی بر بخش را استخراج می‌کند."
    step3: "فرمت INI استاندارد تولید کنید با پشتیبانی از بخش‌ها، نظرات و انواع داده مختلف. فایل‌های INI تولید شده با اکثر برنامه‌ها و سیستم‌های پیکربندی سازگار هستند."
    from_alias: "پیکربندی INI"
    to_alias: "فرمت INI"
  Avro:
    alias: "طرحواره Avro"
    what: "Apache Avro یک سیستم سریال‌سازی داده است که ساختارهای داده غنی، فرمت باینری فشرده و قابلیت‌های تکامل طرحواره ارائه می‌دهد. به طور گسترده در پردازش کلان داده، صف‌های پیام و سیستم‌های توزیع شده استفاده می‌شود. تعریف طرحواره آن از انواع داده پیچیده و سازگاری نسخه پشتیبانی می‌کند که آن را به ابزار مهم برای مهندسان داده و معماران سیستم تبدیل می‌کند."
    step1: "فایل‌های طرحواره Avro آپلود کنید یا داده بچسبانید. ابزار تعاریف طرحواره Avro را تجزیه می‌کند و اطلاعات ساختار جدول را استخراج می‌کند."
    step3: "تعاریف طرحواره Avro استاندارد تولید کنید با پشتیبانی از نگاشت نوع داده، محدودیت‌های فیلد و اعتبارسنجی طرحواره. طرحواره‌های تولید شده می‌توانند مستقیماً در اکوسیستم‌های Hadoop، سیستم‌های پیام Kafka و سایر پلتفرم‌های کلان داده استفاده شوند."
    from_alias: "طرحواره Avro"
    to_alias: "فرمت داده Avro"
  Protobuf:
    alias: "بافرهای پروتکل"
    what: "بافرهای پروتکل (protobuf) مکانیزم قابل گسترش، مستقل از زبان و پلتفرم گوگل برای سریال‌سازی داده‌های ساختاریافته است. به طور گسترده در میکروسرویس‌ها، توسعه API و ذخیره‌سازی داده استفاده می‌شود. فرمت باینری کارآمد و تایپ قوی آن را برای برنامه‌های با عملکرد بالا و ارتباط بین زبان‌ها ایده‌آل می‌کند."
    step1: "فایل‌های .proto آپلود کنید یا تعاریف بافر پروتکل بچسبانید. ابزار نحو protobuf را تجزیه می‌کند و اطلاعات ساختار پیام را استخراج می‌کند."
    step3: "تعاریف بافر پروتکل استاندارد تولید کنید با پشتیبانی از انواع پیام، گزینه‌های فیلد و تعاریف سرویس. فایل‌های .proto تولید شده می‌توانند برای زبان‌های برنامه‌نویسی متعدد کامپایل شوند."
    from_alias: "بافر پروتکل"
    to_alias: "طرحواره Protobuf"
  PandasDataFrame:
    alias: "DataFrame پاندا"
    what: "پاندا محبوب‌ترین کتابخانه تحلیل داده در Python است که DataFrame ساختار داده اصلی آن است. قابلیت‌های قدرتمند دستکاری، تمیزکاری و تحلیل داده ارائه می‌دهد که به طور گسترده در علم داده، یادگیری ماشین و هوش تجاری استفاده می‌شود. ابزار ضروری برای توسعه‌دهندگان Python و تحلیل‌گران داده."
    step1: "فایل‌های Python حاوی کد DataFrame آپلود کنید یا داده بچسبانید. ابزار نحو پاندا را تجزیه می‌کند و اطلاعات ساختار DataFrame را استخراج می‌کند."
    step3: "کد DataFrame پاندا استاندارد تولید کنید با پشتیبانی از مشخصات نوع داده، تنظیمات شاخص و عملیات داده. کد تولید شده می‌تواند مستقیماً در محیط Python برای تحلیل و پردازش داده اجرا شود."
    from_alias: "DataFrame پاندا"
    to_alias: "ساختار داده Python"
  RDF:
    alias: "سه‌گانه RDF"
    what: "RDF (چارچوب توصیف منابع) یک مدل استاندارد برای تبادل داده در وب است که برای نمایش اطلاعات منابع در شکل گراف طراحی شده است. به طور گسترده در وب معنایی، گراف‌های دانش و برنامه‌های داده پیوندی استفاده می‌شود. ساختار سه‌گانه آن نمایش فراداده غنی و روابط معنایی را امکان‌پذیر می‌کند."
    step1: "فایل‌های RDF آپلود کنید یا داده‌های سه‌گانه بچسبانید. ابزار نحو RDF را تجزیه می‌کند و روابط معنایی و اطلاعات منابع را استخراج می‌کند."
    step3: "فرمت RDF استاندارد تولید کنید با پشتیبانی از سریال‌سازی‌های مختلف (RDF/XML، Turtle، N-Triples). RDF تولید شده می‌تواند در برنامه‌های وب معنایی، پایگاه‌های دانش و سیستم‌های داده پیوندی استفاده شود."
    from_alias: "داده RDF"
    to_alias: "فرمت معنایی RDF"
  MATLAB:
    alias: "آرایه MATLAB"
    what: "MATLAB یک نرم‌افزار محاسبات عددی و تجسم با عملکرد بالا است که به طور گسترده در محاسبات مهندسی، تحلیل داده و توسعه الگوریتم استفاده می‌شود. عملیات آرایه و ماتریس آن قدرتمند است و از محاسبات ریاضی پیچیده و پردازش داده پشتیبانی می‌کند. ابزار ضروری برای مهندسان، محققان و دانشمندان داده."
    step1: "فایل‌های .m MATLAB آپلود کنید یا داده‌های آرایه بچسبانید. ابزار نحو MATLAB را تجزیه می‌کند و اطلاعات ساختار آرایه را استخراج می‌کند."
    step3: "کد آرایه MATLAB استاندارد تولید کنید با پشتیبانی از آرایه‌های چندبعدی، مشخصات نوع داده و نام‌گذاری متغیر. کد تولید شده می‌تواند مستقیماً در محیط MATLAB برای تحلیل داده و محاسبات علمی اجرا شود."
    from_alias: "آرایه MATLAB"
    to_alias: "فرمت کد MATLAB"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame ساختار داده اصلی در زبان برنامه‌نویسی R است که به طور گسترده در تحلیل آماری، استخراج داده و یادگیری ماشین استفاده می‌شود. R ابزار برتر برای محاسبات آماری و گرافیک است که در آن DataFrame قابلیت‌های قدرتمند دستکاری داده، تحلیل آماری و تجسم ارائه می‌دهد. برای دانشمندان داده، آمارشناسان و محققانی که با تحلیل داده‌های ساختاریافته کار می‌کنند، ضروری است."
    step1: "فایل‌های داده R آپلود کنید یا کد DataFrame بچسبانید. ابزار نحو R را تجزیه می‌کند و اطلاعات ساختار DataFrame شامل انواع ستون، نام‌های ردیف و محتوای داده را استخراج می‌کند."
    step3: "کد R DataFrame استاندارد تولید کنید با پشتیبانی از مشخصات نوع داده، سطوح فاکتور، نام‌های ردیف/ستون و ساختارهای داده خاص R. کد تولید شده می‌تواند مستقیماً در محیط R برای تحلیل آماری و پردازش داده اجرا شود."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "شروع تبدیل"
  start_generating: "شروع تولید"
  api_docs: "مستندات API"
related:
  section_title: 'مبدل‌های بیشتر {{ if and .from (ne .from "generator") }}{{ .from }} و {{ end }}{{ .to }}'
  section_description: 'مبدل‌های بیشتری را برای فرمت‌های {{ if and .from (ne .from "generator") }}{{ .from }} و {{ end }}{{ .to }} کاوش کنید. داده‌های خود را بین فرمت‌های متعدد با ابزارهای تبدیل آنلاین حرفه‌ای ما تغییر دهید.'
  title: "{{ .from }} به {{ .to }}"
howto:
  step2: "داده‌ها را با استفاده از ویرایشگر جدول آنلاین پیشرفته ما با ویژگی‌های حرفه‌ای ویرایش کنید. از حذف ردیف‌های خالی، حذف تکراری‌ها، تبدیل داده‌ها، مرتب‌سازی، جستجو و جایگزینی regex و پیش‌نمایش زمان واقعی پشتیبانی می‌کند. همه تغییرات به طور خودکار به فرمت %s با نتایج دقیق و قابل اعتماد تبدیل می‌شوند."
  section_title: "نحوه استفاده از {{ . }}"
  converter_description: "یاد بگیرید چگونه {{ .from }} را به {{ .to }} با راهنمای گام به گام ما تبدیل کنید. مبدل آنلاین حرفه‌ای با ویژگی‌های پیشرفته و پیش‌نمایش زمان واقعی."
  generator_description: "یاد بگیرید چگونه جداول {{ .to }} حرفه‌ای را با تولیدکننده آنلاین ما ایجاد کنید. ویرایش شبیه Excel، پیش‌نمایش زمان واقعی و قابلیت‌های صادرات فوری."
extension:
  section_title: "افزونه تشخیص و استخراج جدول"
  section_description: "جداول را از هر وب‌سایتی با یک کلیک استخراج کنید. فوراً به بیش از ۳۰ فرمت شامل Excel، CSV، JSON تبدیل کنید - نیازی به کپی-پیست نیست."
  features:
    extraction_title: "استخراج جدول با یک کلیک"
    extraction_description: "فوراً جداول را از هر صفحه وب بدون کپی-پیست استخراج کنید - استخراج داده حرفه‌ای ساده شده"
    formats_title: "پشتیبانی از بیش از ۳۰ فرمت"
    formats_description: "جداول استخراج شده را به Excel، CSV، JSON، Markdown، SQL و موارد دیگر با مبدل جدول پیشرفته ما تبدیل کنید"
    detection_title: "تشخیص هوشمند جدول"
    detection_description: "به طور خودکار جداول را در هر صفحه وب برای استخراج و تبدیل سریع داده تشخیص و برجسته می‌کند"
  hover_tip: "✨ موس را روی هر جدول ببرید تا آیکون استخراج را ببینید"
recommendations:
  section_title: "توصیه شده توسط دانشگاه‌ها و متخصصان"
  section_description: "TableConvert توسط متخصصان در دانشگاه‌ها، مؤسسات تحقیقاتی و تیم‌های توسعه برای تبدیل قابل اعتماد جدول و پردازش داده مورد اعتماد است."
  cards:
    university_title: "دانشگاه ویسکانسین-مدیسون"
    university_description: "TableConvert.com - ابزار حرفه‌ای رایگان آنلاین مبدل جدول و فرمت‌های داده"
    university_link: "مقاله را بخوانید"
    facebook_title: "جامعه متخصصان داده"
    facebook_description: "توسط تحلیلگران داده و متخصصان در گروه‌های توسعه‌دهندگان Facebook به اشتراک گذاشته و توصیه شده"
    facebook_link: "پست را ببینید"
    twitter_title: "جامعه توسعه‌دهندگان"
    twitter_description: "توسط @xiaoying_eth و سایر توسعه‌دهندگان در X (Twitter) برای تبدیل جدول توصیه شده"
    twitter_link: "توییت را ببینید"
faq:
  section_title: "سؤالات متداول"
  section_description: "سؤالات رایج درباره مبدل جدول رایگان آنلاین ما، فرمت‌های داده و فرآیند تبدیل."
  what: "فرمت %s چیست؟"
  howto_convert:
    question: "چگونه از {{ . }} به صورت رایگان استفاده کنم؟"
    answer: "فایل {{ .from }} خود را آپلود کنید، داده‌ها را بچسبانید یا از صفحات وب با استفاده از مبدل جدول رایگان آنلاین ما استخراج کنید. ابزار مبدل حرفه‌ای ما فوراً داده‌های شما را به فرمت {{ .to }} با پیش‌نمایش زمان واقعی و ویژگی‌های ویرایش پیشرفته تبدیل می‌کند. نتیجه تبدیل شده را فوراً دانلود یا کپی کنید."
  security:
    question: "آیا داده‌های من هنگام استفاده از این مبدل آنلاین امن هستند؟"
    answer: "قطعاً! همه تبدیل‌های جدول به صورت محلی در مرورگر شما انجام می‌شوند - داده‌های شما هرگز دستگاه شما را ترک نمی‌کنند. مبدل آنلاین ما همه چیز را در سمت کلاینت پردازش می‌کند و حریم خصوصی کامل و امنیت داده را تضمین می‌کند. هیچ فایلی در سرورهای ما ذخیره نمی‌شود."
  free:
    question: "آیا TableConvert واقعاً رایگان است؟"
    answer: "بله، TableConvert کاملاً رایگان است! همه ویژگی‌های مبدل، ویرایشگر جدول، ابزارهای تولیدکننده داده و گزینه‌های صادرات بدون هزینه، ثبت‌نام یا هزینه‌های پنهان در دسترس هستند. فایل‌های نامحدود را به صورت رایگان آنلاین تبدیل کنید."
  filesize:
    question: "مبدل آنلاین چه محدودیت‌های اندازه فایل دارد؟"
    answer: "مبدل جدول رایگان آنلاین ما از فایل‌های تا ۱۰ مگابایت پشتیبانی می‌کند. برای فایل‌های بزرگ‌تر، پردازش دسته‌ای یا نیازهای سازمانی، از افزونه مرورگر یا سرویس API حرفه‌ای ما با محدودیت‌های بالاتر استفاده کنید."
stats:
  conversions: "جداول تبدیل شده"
  tables: "جداول تولید شده"
  formats: "فرمت‌های فایل داده"
  rating: "امتیاز کاربر"
