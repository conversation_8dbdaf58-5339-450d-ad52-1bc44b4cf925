site:
  fullname: "Çevrimiçi Tablo Dönüştürücü"
  name: "TableConvert"
  subtitle: "Ücretsiz Çevrimiçi Tablo Dönüştürücü ve Oluşturucu"
  intro: "TableConvert, Excel, CSV, JSON, Markdown, LaTeX, SQL ve daha fazlası dahil 30'dan fazla format arasında dönüştürmeyi destekleyen ücretsiz çevrimiçi tablo dönüştürücü ve veri oluşturucu aracıdır."
  followTwitter: "X'te bizi takip edin"
title:
  converter: "%s'den %s'ye"
  generator: "%s Oluşturucu"
post:
  tags:
    converter: "Dönüştürücü"
    editor: "Editör"
    generator: "Oluşturucu"
    maker: "İnşaatçı"
  converter:
    title: "%s'yi %s'ye Çevrimiçi Dönüştür"
    short: "Ücretsiz ve güçlü %s'den %s'ye çevrimiçi araç"
    intro: "Kullanımı kolay çevrimiçi %s'den %s'ye dönüştürücü. Sezgisel dönüştürme aracımızla tablo verilerini zahmetsizce dönüştürün. Hızlı, güvenilir ve kullanıcı dostu."
  generator:
    title: "Çevrimiçi %s Editörü ve Oluşturucu"
    short: "Kapsamlı özelliklerle profesyonel %s çevrimiçi oluşturma aracı"
    intro: "Kullanımı kolay çevrimiçi %s oluşturucu ve tablo editörü. Sezgisel aracımız ve gerçek zamanlı önizleme ile profesyonel veri tablolarını zahmetsizce oluşturun."
navbar:
  search:
    placeholder: "Dönüştürücü ara..."
  sponsor: "Bize Kahve Ismarla"
  extension: "Uzantı"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Veri Kaynağı"
    placeholder: "%s verilerinizi yapıştırın veya %s dosyalarını buraya sürükleyin"
    example: "Örnek"
    upload: "Dosya Yükle"
    extract:
      enter: "Web Sayfasından Çıkar"
      intro: "Yapılandırılmış verileri otomatik olarak çıkarmak için tablo verisi içeren bir web sayfası URL'si girin"
      btn: "%s Çıkar"
    excel:
      sheet: "Çalışma Sayfası"
      none: "Hiçbiri"
  tableEditor:
    title: "Çevrimiçi Tablo Editörü"
    undo: "Geri Al"
    redo: "Yinele"
    transpose: "Transpoz"
    clear: "Temizle"
    deleteBlank: "Boşları Sil"
    deleteDuplicate: "Kopyaları Sil"
    uppercase: "BÜYÜK HARF"
    lowercase: "küçük harf"
    capitalize: "İlk Harfi Büyük"
    replace:
      replace: "Bul ve Değiştir (Regex desteklenir)"
      subst: "Şununla değiştir..."
      btn: "Tümünü Değiştir"
  tableGenerator:
    title: "Tablo Oluşturucu"
    sponsor: "Bize Kahve Ismarla"
    copy: "Panoya Kopyala"
    download: "Dosya İndir"
    tooltip:
      html:
        escape: "Görüntü hatalarını önlemek için HTML özel karakterlerini (&, <, >, \", ') kaçır"
        div: "Geleneksel TABLE etiketleri yerine DIV+CSS düzeni kullan, duyarlı tasarım için daha uygun"
        minify: "Sıkıştırılmış HTML kodu oluşturmak için boşlukları ve satır sonlarını kaldır"
        thead: "Standart tablo başı (&lt;thead&gt;) ve gövde (&lt;tbody&gt;) yapısı oluştur"
        tableCaption: "Tablonun üstüne açıklayıcı başlık ekle (&lt;caption&gt; öğesi)"
        tableClass: "Kolay stil özelleştirmesi için tabloya CSS sınıf adı ekle"
        tableId: "JavaScript manipülasyonu için tabloya benzersiz ID tanımlayıcısı ayarla"
      jira:
        escape: "Jira tablo sözdizimi ile çakışmaları önlemek için pipe karakterlerini (|) kaçır"
      json:
        parsingJSON: "Hücrelerdeki JSON dizelerini nesnelere akıllıca ayrıştır"
        minify: "Dosya boyutunu azaltmak için kompakt tek satır JSON formatı oluştur"
        format: "Çıktı JSON veri yapısını seç: nesne dizisi, 2D dizi, vb."
      latex:
        escape: "Doğru derlemeyi sağlamak için LaTeX özel karakterlerini (%, &, _, #, $, vb.) kaçır"
        ht: "Sayfada tablo konumunu kontrol etmek için yüzen konum parametresi [!ht] ekle"
        mwe: "Tam LaTeX belgesi oluştur"
        tableAlign: "Sayfada tablonun yatay hizalamasını ayarla"
        tableBorder: "Tablo kenarlık stilini yapılandır: kenarlık yok, kısmi kenarlık, tam kenarlık"
        label: "\\ref{} komutu çapraz referansı için tablo etiketi ayarla"
        caption: "Tablonun üstünde veya altında görüntülenecek tablo başlığını ayarla"
        location: "Tablo başlığı görüntüleme konumunu seç: üst veya alt"
        tableType: "Tablo ortam türünü seç: tabular, longtable, array, vb."
      markdown:
        escape: "Format çakışmalarını önlemek için Markdown özel karakterlerini (*, _, |, \\, vb.) kaçır"
        pretty: "Daha güzel tablo formatı oluşturmak için sütun genişliklerini otomatik hizala"
        simple: "Dış kenarlık dikey çizgilerini atlayarak basitleştirilmiş sözdizimi kullan"
        boldFirstRow: "İlk satır metnini kalın yap"
        boldFirstColumn: "İlk sütun metnini kalın yap"
        firstHeader: "İlk satırı başlık olarak işle ve ayırıcı çizgi ekle"
        textAlign: "Sütun metin hizalamasını ayarla: sol, orta, sağ"
        multilineHandling: "Çok satırlı metin işleme: satır sonlarını koru, \\n'ye kaçır, &lt;br&gt; etiketleri kullan"

        includeLineNumbers: "Tablonun sol tarafına satır numarası sütunu ekle"
      magic:
        builtin: "Önceden tanımlanmış ortak şablon formatlarını seç"
        rowsTpl: "<table> <tr> <th>Sihirli Sözdizimi</th> <th>Açıklama</th> <th>Desteklenen JS Yöntemleri</th> </tr> <tr> <td>{h1} {h2} ...</td> <td><b>B</b>aşlığın 1., 2. ... alanı, Diğer adıyla {hA} {hB} ...</td> <td>String yöntemleri</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>Mevcut satırın 1., 2. ... alanı, Diğer adıyla {$A} {$B} ...</td> <td>String yöntemleri</td> </tr> <tr> <td>{F,} {F;}</td> <td>Mevcut satırı <b>F</b>'den sonraki dizeyle böl</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>Mevcut <b>S</b>atırın satır <b>N</b>umarası 1 veya 100'den</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> Satırların <b>S</b>on satır <b>N</b>umarası </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>JavaScript kodu <b>ç</b>alıştır, örn: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Küme parantezleri {...} çıktısı için ters eğik çizgi <b>\\</b> kullan </td> <td></td> </tr></table>"
        headerTpl: "Başlık bölümü için özel çıktı şablonu"
        footerTpl: "Alt bilgi bölümü için özel çıktı şablonu"
      textile:
        escape: "Format çakışmalarını önlemek için Textile sözdizimi karakterlerini (|, ., -, ^) kaçır"
        rowHeader: "İlk satırı başlık satırı olarak ayarla"
        thead: "Tablo başı ve gövdesi için Textile sözdizimi işaretleyicileri ekle"
      xml:
        escape: "Geçerli XML sağlamak için XML özel karakterlerini (&lt;, &gt;, &amp;, \", ') kaçır"
        minify: "Ekstra boşlukları kaldırarak sıkıştırılmış XML çıktısı oluştur"
        rootElement: "XML kök öğe etiket adını ayarla"
        rowElement: "Her veri satırı için XML öğe etiket adını ayarla"
        declaration: "XML bildirim başlığı ekle (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Alt öğeler yerine XML öznitelikleri olarak veri çıktısı"
        cdata: "Özel karakterleri korumak için metin içeriğini CDATA ile sar"
        encoding: "XML belgesi için karakter kodlama formatını ayarla"
        indentation: "XML girinti karakterini seç: boşluklar veya sekmeler"
      yaml:
        indentSize: "YAML hiyerarşi girintisi için boşluk sayısını ayarla (genellikle 2 veya 4)"
        arrayStyle: "Dizi formatı: blok (satır başına bir öğe) veya akış (satır içi format)"
        quotationStyle: "Dize tırnak stili: tırnak yok, tek tırnak, çift tırnak"
      pdf:
        theme: "Profesyonel belgeler için PDF tablo görsel stilini seçin"
        headerColor: "PDF tablo başlığı arka plan rengini seçin"
        showHead: "PDF sayfalarında başlık görüntüsünü kontrol edin"
        docTitle: "PDF belgesi için isteğe bağlı başlık"
        docDescription: "PDF belgesi için isteğe bağlı açıklama metni"
      csv:
        bom: "Excel ve diğer yazılımların kodlamayı tanımasına yardımcı olmak için UTF-8 bayt sırası işareti ekle"
      excel:
        autoWidth: "İçeriğe göre sütun genişliğini otomatik ayarla"
        protectSheet: "Çalışma sayfası korumasını şifre ile etkinleştir: tableconvert.com"
      sql:
        primaryKey: "CREATE TABLE ifadesi için birincil anahtar alan adını belirt"
        dialect: "Tırnak ve veri türü sözdizimini etkileyen veritabanı türünü seç"
      ascii:
        forceSep: "Her veri satırı arasında ayırıcı çizgileri zorla"
        style: "ASCII tablo kenarlık çizim stilini seç"
        comment: "Tüm tabloyu sarmak için yorum işaretleyicileri ekle"
      mediawiki:
        minify: "Ekstra boşlukları kaldırarak çıktı kodunu sıkıştır"
        header: "İlk satırı başlık stili olarak işaretle"
        sort: "Tablo tıklama sıralama işlevselliğini etkinleştir"
      asciidoc:
        minify: "AsciiDoc format çıktısını sıkıştır"
        firstHeader: "İlk satırı başlık satırı olarak ayarla"
        lastFooter: "Son satırı alt bilgi satırı olarak ayarla"
        title: "Tabloya başlık metni ekle"
      tracwiki:
        rowHeader: "İlk satırı başlık olarak ayarla"
        colHeader: "İlk sütunu başlık olarak ayarla"
      bbcode:
        minify: "BBCode çıktı formatını sıkıştır"
      restructuredtext:
        style: "reStructuredText tablo kenarlık stilini seç"
        forceSep: "Ayırıcı çizgileri zorla"
    label:
      ascii:
        forceSep: "Satır Ayırıcıları"
        style: "Kenarlık Stili"
        comment: "Yorum Sarmalayıcı"
      restructuredtext:
        style: "Kenarlık Stili"
        forceSep: "Ayırıcıları Zorla"
      bbcode:
        minify: "Çıktıyı Sıkıştır"
      csv:
        doubleQuote: "Çift Tırnak Sarma"
        delimiter: "Alan Ayırıcısı"
        bom: "UTF-8 BOM"
        valueDelimiter: "Değer Ayırıcısı"
        rowDelimiter: "Satır Ayırıcısı"
        prefix: "Satır Öneki"
        suffix: "Satır Soneki"
      excel:
        autoWidth: "Otomatik Genişlik"
        textFormat: "Metin Formatı"
        protectSheet: "Sayfayı Koru"
        boldFirstRow: "İlk Satırı Kalın Yap"
        boldFirstColumn: "İlk Sütunu Kalın Yap"
        sheetName: "Sayfa Adı"
      html:
        escape: "HTML Karakterlerini Kaçır"
        div: "DIV Tablosu"
        minify: "Kodu Sıkıştır"
        thead: "Tablo Başı Yapısı"
        tableCaption: "Tablo Başlığı"
        tableClass: "Tablo Sınıfı"
        tableId: "Tablo ID'si"
        rowHeader: "Satır Başlığı"
        colHeader: "Sütun Başlığı"
      jira:
        escape: "Karakterleri Kaçır"
        rowHeader: "Satır Başlığı"
        colHeader: "Sütun Başlığı"
      json:
        parsingJSON: "JSON Ayrıştır"
        minify: "Çıktıyı Sıkıştır"
        format: "Veri Formatı"
        rootName: "Kök Nesne Adı"
        indentSize: "Girinti Boyutu"
      jsonlines:
        parsingJSON: "JSON Ayrıştır"
        format: "Veri Formatı"
      latex:
        escape: "LaTeX Tablo Karakterlerini Kaçır"
        ht: "Yüzen Konum"
        mwe: "Tam Belge"
        tableAlign: "Tablo Hizalama"
        tableBorder: "Kenarlık Stili"
        label: "Referans Etiketi"
        caption: "Tablo Başlığı"
        location: "Başlık Konumu"
        tableType: "Tablo Türü"
        boldFirstRow: "İlk Satırı Kalın Yap"
        boldFirstColumn: "İlk Sütunu Kalın Yap"
        textAlign: "Metin Hizalama"
        borders: "Kenarlık Ayarları"
      markdown:
        escape: "Karakterleri Kaçır"
        pretty: "Güzel Markdown Tablosu"
        simple: "Basit Markdown Formatı"
        boldFirstRow: "İlk Satırı Kalın Yap"
        boldFirstColumn: "İlk Sütunu Kalın Yap"
        firstHeader: "İlk Başlık"
        textAlign: "Metin Hizalama"
        multilineHandling: "Çok Satırlı İşleme"

        includeLineNumbers: "Satır Numaraları Ekle"
        align: "Hizalama"
      mediawiki:
        minify: "Kodu Sıkıştır"
        header: "Başlık İşaretlemesi"
        sort: "Sıralanabilir"
      asciidoc:
        minify: "Formatı Sıkıştır"
        firstHeader: "İlk Başlık"
        lastFooter: "Son Alt Bilgi"
        title: "Tablo Başlığı"
      tracwiki:
        rowHeader: "Satır Başlığı"
        colHeader: "Sütun Başlığı"
      sql:
        drop: "Tabloyu Sil (Varsa)"
        create: "Tablo Oluştur"
        oneInsert: "Toplu Ekleme"
        table: "Tablo Adı"
        dialect: "Veritabanı Türü"
        primaryKey: "Birincil Anahtar"
      magic:
        builtin: "Yerleşik Şablon"
        rowsTpl: "Satır Şablonu, Sözdizimi ->"
        headerTpl: "Başlık Şablonu"
        footerTpl: "Alt Bilgi Şablonu"
      textile:
        escape: "Karakterleri Kaçır"
        rowHeader: "Satır Başlığı"
        thead: "Tablo Başı Sözdizimi"
      xml:
        escape: "XML Karakterlerini Kaçır"
        minify: "Çıktıyı Sıkıştır"
        rootElement: "Kök Öğe"
        rowElement: "Satır Öğesi"
        declaration: "XML Bildirimi"
        attributes: "Öznitelik Modu"
        cdata: "CDATA Sarmalayıcı"
        encoding: "Kodlama"
        indentSize: "Girinti Boyutu"
      yaml:
        indentSize: "Girinti Boyutu"
        arrayStyle: "Dizi Stili"
        quotationStyle: "Alıntı Stili"
      pdf:
        theme: "PDF Tablo Teması"
        headerColor: "PDF Başlık Rengi"
        showHead: "PDF Başlık Görüntüsü"
        docTitle: "PDF Belge Başlığı"
        docDescription: "PDF Belge Açıklaması"
sidebar:
  all: "Tüm Dönüştürme Araçları"
  dataSource:
    title: "Veri Kaynağı"
    description:
      converter: "%s'yi %s'ye dönüştürmek için içe aktarın. Dosya yükleme, çevrimiçi düzenleme ve web veri çıkarma desteklenir."
      generator: "Manuel giriş, dosya içe aktarma ve şablon oluşturma dahil olmak üzere çoklu giriş yöntemleri desteği ile tablo verisi oluşturun."
  tableEditor:
    title: "Çevrimiçi Tablo Editörü"
    description:
      converter: "Tablo editörümüzü kullanarak %s'yi çevrimiçi işleyin. Boş satırları silme, tekrarları kaldırma, sıralama ve bul & değiştir desteği ile Excel benzeri işlem deneyimi."
      generator: "Excel benzeri işlem deneyimi sağlayan güçlü çevrimiçi tablo editörü. Boş satırları silme, tekrarları kaldırma, sıralama ve bul & değiştir desteklenir."
  tableGenerator:
    title: "Tablo Oluşturucu"
    description:
      converter: "Tablo oluşturucunun gerçek zamanlı önizlemesi ile %s'yi hızla oluşturun. Zengin dışa aktarma seçenekleri, tek tıkla kopyala & indir."
      generator: "Farklı kullanım senaryolarını karşılamak için %s verilerini çoklu formatlarda dışa aktarın. Özel seçenekler ve gerçek zamanlı önizleme desteklenir."
footer:
  changelog: "Değişiklik Günlüğü"
  sponsor: "Sponsorlar"
  contact: "Bize Ulaşın"
  privacyPolicy: "Gizlilik Politikası"
  about: "Hakkında"
  resources: "Kaynaklar"
  popularConverters: "Popüler Dönüştürücüler"
  popularGenerators: "Popüler Oluşturucular"
  dataSecurity: "Verileriniz güvende - tüm dönüştürmeler tarayıcınızda çalışır."
converters:
  Markdown:
    alias: "Markdown Tablosu"
    what: "Markdown, teknik dokümantasyon, blog içeriği oluşturma ve web geliştirme için yaygın olarak kullanılan hafif bir işaretleme dilidir. Tablo sözdizimi özlü ve sezgisel olup, metin hizalama, bağlantı gömme ve biçimlendirmeyi destekler. Programcılar ve teknik yazarlar için tercih edilen araçtır, GitHub, GitLab ve diğer kod barındırma platformları ile mükemmel uyumludur."
    step1: "Markdown tablo verilerini veri kaynağı alanına yapıştırın veya .md dosyalarını doğrudan sürükleyip bırakarak yükleyin. Araç otomatik olarak tablo yapısını ve biçimlendirmesini ayrıştırır, karmaşık iç içe içerik ve özel karakter işlemeyi destekler."
    step3: "Gerçek zamanlı olarak standart Markdown tablo kodu oluşturun, çoklu hizalama yöntemleri, metin kalınlaştırma, satır numarası ekleme ve diğer gelişmiş biçim ayarlarını destekler. Oluşturulan kod GitHub ve büyük Markdown editörleri ile tamamen uyumludur, tek tıkla kopyalama ile kullanıma hazırdır."
    from_alias: "Markdown Tablo Dosyası"
    to_alias: "Markdown Tablo Formatı"
  Magic:
    alias: "Özel Şablon"
    what: "Magic şablonu, bu aracın benzersiz gelişmiş veri üreticisidir ve kullanıcıların özel şablon sözdizimi aracılığıyla rastgele format veri çıktısı oluşturmasına olanak tanır. Değişken değiştirme, koşullu yargılama ve döngü işlemeyi destekler. Karmaşık veri dönüştürme ihtiyaçları ve kişiselleştirilmiş çıktı formatları için nihai çözümdür, özellikle geliştiriciler ve veri mühendisleri için uygundur."
    step1: "Yerleşik ortak şablonları seçin veya özel şablon sözdizimi oluşturun. Karmaşık veri yapıları ve iş mantığını işleyebilen zengin değişkenler ve fonksiyonları destekler."
    step3: "Özel format gereksinimlerini tamamen karşılayan veri çıktısı oluşturun. Karmaşık veri dönüştürme mantığı ve koşullu işlemeyi destekler, veri işleme verimliliği ve çıktı kalitesini büyük ölçüde artırır. Toplu veri işleme için güçlü bir araç."
    from_alias: "Tablo Verisi"
    to_alias: "Özel Format Çıktısı"
  CSV:
    alias: "CSV"
    what: "CSV (Virgülle Ayrılmış Değerler), Excel, Google Sheets, veritabanı sistemleri ve çeşitli veri analizi araçları tarafından mükemmel şekilde desteklenen en yaygın kullanılan veri değişim formatıdır. Basit yapısı ve güçlü uyumluluğu onu veri taşıma, toplu içe/dışa aktarma ve platformlar arası veri değişimi için standart format yapar, iş analizi, veri bilimi ve sistem entegrasyonunda yaygın olarak kullanılır."
    step1: "CSV dosyalarını yükleyin veya CSV verilerini doğrudan yapıştırın. Araç çeşitli ayırıcıları (virgül, sekme, noktalı virgül, pipe vb.) akıllıca tanır, veri türlerini ve kodlama formatlarını otomatik olarak algılar, büyük dosyaların ve karmaşık veri yapılarının hızlı ayrıştırılmasını destekler."
    step3: "Özel ayırıcılar, alıntı stilleri, kodlama formatları ve BOM işareti ayarları desteği ile standart CSV format dosyaları oluşturun. Hedef sistemlerle mükemmel uyumluluk sağlar, kurumsal düzeyde veri işleme ihtiyaçlarını karşılamak için indirme ve sıkıştırma seçenekleri sunar."
    from_alias: "CSV Veri Dosyası"
    to_alias: "CSV Standart Format"
  JSON:
    alias: "JSON Dizisi"
    what: "JSON (JavaScript Object Notation), modern web uygulamaları, REST API'ları ve mikroservis mimarileri için standart tablo veri formatıdır. Net yapısı ve verimli ayrıştırması onu ön uç ve arka uç veri etkileşimi, yapılandırma dosyası depolama ve NoSQL veritabanlarında yaygın olarak kullanılır hale getirir. İç içe nesneleri, dizi yapılarını ve çoklu veri türlerini destekler, modern yazılım geliştirme için vazgeçilmez tablo verisi yapar."
    step1: "JSON dosyalarını yükleyin veya JSON dizilerini yapıştırın. Nesne dizilerinin, iç içe yapıların ve karmaşık veri türlerinin otomatik tanınması ve ayrıştırılmasını destekler. Araç JSON sözdizimini akıllıca doğrular ve hata uyarıları sağlar."
    step3: "Çoklu JSON format çıktıları oluşturun: standart nesne dizileri, 2D diziler, sütun dizileri ve anahtar-değer çifti formatları. Güzelleştirilmiş çıktı, sıkıştırma modu, özel kök nesne adları ve girinti ayarlarını destekler, çeşitli API arayüzleri ve veri depolama ihtiyaçlarına mükemmel şekilde uyum sağlar."
    from_alias: "JSON Dizi Dosyası"
    to_alias: "JSON Standart Format"
  JSONLines:
    alias: "JSONLines Formatı"
    what: "JSON Lines (NDJSON olarak da bilinir), her satırda bağımsız bir JSON nesnesi bulunan büyük veri işleme ve akış veri iletimi için önemli bir formattır. Log analizi, veri akışı işleme, makine öğrenmesi ve dağıtık sistemlerde yaygın olarak kullanılır. Artımlı işleme ve paralel hesaplamayı destekler, büyük ölçekli yapılandırılmış verileri işlemek için ideal seçim yapar."
    step1: "JSONLines dosyalarını yükleyin veya veri yapıştırın. Araç JSON nesnelerini satır satır ayrıştırır, büyük dosya akış işleme ve hata satırı atlama işlevselliğini destekler."
    step3: "Her satırın tam bir JSON nesnesi çıktısı verdiği standart JSONLines formatı oluşturun. Akış işleme, toplu içe aktarma ve büyük veri analizi senaryoları için uygundur, veri doğrulama ve format optimizasyonunu destekler."
    from_alias: "JSONLines Verisi"
    to_alias: "JSONLines Akış Formatı"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language), katı sözdizimi spesifikasyonları ve güçlü doğrulama mekanizmaları ile kurumsal düzeyde veri değişimi ve yapılandırma yönetimi için standart formattır. Web servisleri, yapılandırma dosyaları, belge depolama ve sistem entegrasyonunda yaygın olarak kullanılır. Ad alanları, şema doğrulama ve XSLT dönüşümünü destekler, kurumsal uygulamalar için önemli tablo verisi yapar."
    step1: "XML dosyalarını yükleyin veya XML verilerini yapıştırın. Araç otomatik olarak XML yapısını ayrıştırır ve tablo formatına dönüştürür, ad alanı, öznitelik işleme ve karmaşık iç içe yapıları destekler."
    step3: "XML standartlarına uygun XML çıktısı oluşturun. Özel kök öğeler, satır öğe adları, öznitelik modları, CDATA sarma ve karakter kodlama ayarlarını destekler. Veri bütünlüğü ve uyumluluğu sağlar, kurumsal düzeyde uygulama gereksinimlerini karşılar."
    from_alias: "XML Veri Dosyası"
    to_alias: "XML Standart Format"
  YAML:
    alias: "YAML Yapılandırması"
    what: "YAML, net hiyerarşik yapısı ve özlü sözdizimi ile ünlü, insan dostu bir veri serileştirme standardıdır. Yapılandırma dosyaları, DevOps araç zincirleri, Docker Compose ve Kubernetes dağıtımında yaygın olarak kullanılır. Güçlü okunabilirliği ve özlü sözdizimi onu modern bulut-yerel uygulamalar ve otomatik operasyonlar için önemli bir yapılandırma formatı yapar."
    step1: "YAML dosyalarını yükleyin veya YAML verilerini yapıştırın. Araç YAML yapısını akıllıca ayrıştırır ve sözdizimi doğruluğunu doğrular, çoklu belge formatları ve karmaşık veri türlerini destekler."
    step3: "Blok ve akış dizi stilleri, çoklu alıntı ayarları, özel girinti ve yorum koruma desteği ile standart YAML format çıktısı oluşturun. Çıktı YAML dosyalarının çeşitli ayrıştırıcılar ve yapılandırma sistemleri ile tamamen uyumlu olmasını sağlar."
    from_alias: "YAML Yapılandırma Dosyası"
    to_alias: "YAML Standart Format"
  MySQL:
      alias: "MySQL Sorgu Sonuçları"
      what: "MySQL, yüksek performansı, güvenilirliği ve kullanım kolaylığı ile ünlü dünyanın en popüler açık kaynak ilişkisel veritabanı yönetim sistemidir. Web uygulamaları, kurumsal sistemler ve veri analizi platformlarında yaygın olarak kullanılır. MySQL sorgu sonuçları genellikle yapılandırılmış tablo verilerini içerir ve veritabanı yönetimi ile veri analizi çalışmalarında önemli bir veri kaynağı olarak hizmet eder."
      step1: "MySQL sorgu çıktı sonuçlarını veri kaynağı alanına yapıştırın. Araç otomatik olarak MySQL komut satırı çıktı formatını tanır ve ayrıştırır, çeşitli sorgu sonucu stillerini ve karakter kodlamalarını destekler, başlıkları ve veri satırlarını akıllıca işler."
      step3: "MySQL sorgu sonuçlarını hızla birden fazla tablo veri formatına dönüştürün, veri analizi, rapor oluşturma, sistemler arası veri taşıma ve veri doğrulamayı kolaylaştırır. Veritabanı yöneticileri ve veri analistleri için pratik bir araç."
      from_alias: "MySQL Sorgu Çıktısı"
      to_alias: "MySQL Tablo Verisi"
  SQL:
    alias: "SQL Ekleme"
    what: "SQL (Yapılandırılmış Sorgu Dili), veri sorgulama, ekleme, güncelleme ve silme işlemleri için kullanılan ilişkisel veritabanlarının standart işlem dilidir. Veritabanı yönetiminin temel teknolojisi olarak SQL, veri analizi, iş zekası, ETL işleme ve veri ambarı inşaatında yaygın olarak kullanılır. Veri profesyonelleri için vazgeçilmez bir beceri aracıdır."
    step1: "INSERT SQL ifadelerini yapıştırın veya .sql dosyalarını yükleyin. Araç SQL sözdizimini akıllıca ayrıştırır ve tablo verilerini çıkarır, birden fazla SQL lehçesini ve karmaşık sorgu ifadesi işlemesini destekler."
    step3: "Standart SQL INSERT ifadeleri ve tablo oluşturma ifadeleri oluşturun. Birden fazla veritabanı lehçesini (MySQL, PostgreSQL, SQLite, SQL Server, Oracle) destekler, veri türü eşlemesi, karakter kaçırma ve birincil anahtar kısıtlamalarını otomatik olarak işler. Oluşturulan SQL kodunun doğrudan çalıştırılabilmesini sağlar."
    from_alias: "SQL Veri Dosyası"
    to_alias: "SQL Standart İfade"
  Qlik:
      alias: "Qlik Tablosu"
      what: "Qlik, Tableau ve Microsoft ile birlikte veri görselleştirme, yönetici panoları ve self-servis iş zekası ürünlerinde uzmanlaşmış bir yazılım satıcısıdır."
      step1: ""
      step3: "Son olarak, [Tablo Oluşturucu](#TableGenerator) dönüştürme sonuçlarını gösterir. Qlik Sense, Qlik AutoML, QlikView veya diğer Qlik özellikli yazılımlarınızda kullanın."
      from_alias: "Qlik Tablosu"
      to_alias: "Qlik Tablosu"
  DAX:
      alias: "DAX Tablosu"
      what: "DAX (Data Analysis Expressions), Microsoft Power BI boyunca hesaplanan sütunlar, ölçüler ve özel tablolar oluşturmak için kullanılan bir programlama dilidir."
      step1: ""
      step3: "Son olarak, [Tablo Oluşturucu](#TableGenerator) dönüştürme sonuçlarını gösterir. Beklendiği gibi, Microsoft Power BI, Microsoft Analysis Services ve Excel için Microsoft Power Pivot dahil olmak üzere çeşitli Microsoft ürünlerinde kullanılır."
      from_alias: "DAX Tablosu"
      to_alias: "DAX Tablosu"
  Firebase:
    alias: "Firebase Listesi"
    what: "Firebase, gerçek zamanlı veritabanı, bulut depolama, kimlik doğrulama, çökme raporlama gibi barındırılan backend hizmetleri sağlayan bir BaaS uygulama geliştirme platformudur."
    step1: ""
    step3: "Son olarak, [Tablo Oluşturucu](#TableGenerator) dönüştürme sonuçlarını gösterir. Daha sonra Firebase veritabanındaki veri listesine eklemek için Firebase API'sindeki push yöntemini kullanabilirsiniz."
    from_alias: "Firebase Listesi"
    to_alias: "Firebase Listesi"
  HTML:
    alias: "HTML Tablosu"
    what: "HTML tabloları, table, tr, td ve diğer etiketlerle oluşturulan web sayfalarında yapılandırılmış verileri görüntülemenin standart yoludur. Zengin stil özelleştirmesi, duyarlı düzen ve etkileşimli işlevselliği destekler. Web sitesi geliştirme, veri görüntüleme ve rapor oluşturmada yaygın olarak kullanılır, ön uç geliştirme ve web tasarımının önemli bir bileşeni olarak hizmet eder."
    step1: "Tablo içeren HTML kodunu yapıştırın veya HTML dosyalarını yükleyin. Araç otomatik olarak sayfalardan tablo verilerini tanır ve çıkarır, karmaşık HTML yapıları, CSS stilleri ve iç içe tablo işlemesini destekler."
    step3: "thead/tbody yapısı, CSS sınıf ayarları, tablo başlıkları, satır/sütun başlıkları ve duyarlı öznitelik yapılandırması desteği ile anlamsal HTML tablo kodu oluşturun. Oluşturulan tablo kodunun iyi erişilebilirlik ve SEO dostu özelliklerle web standartlarını karşılamasını sağlar."
    from_alias: "HTML Web Tablosu"
    to_alias: "HTML Standart Tablo"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel, iş analizi, finansal yönetim, veri işleme ve rapor oluşturmada yaygın olarak kullanılan dünyanın en popüler elektronik tablo yazılımıdır. Güçlü veri işleme yetenekleri, zengin fonksiyon kütüphanesi ve esnek görselleştirme özellikleri onu ofis otomasyonu ve veri analizi için standart araç yapar, neredeyse tüm endüstri ve alanlarda geniş uygulamalara sahiptir."
    step1: "Excel dosyalarını yükleyin (.xlsx, .xls formatlarını destekler) veya tablo verilerini doğrudan Excel'den kopyalayıp yapıştırın. Araç çoklu çalışma sayfası işleme, karmaşık format tanıma ve büyük dosyaların hızlı ayrıştırılmasını destekler, birleştirilmiş hücreleri ve veri türlerini otomatik olarak işler."
    step3: "Doğrudan Excel'e yapıştırılabilen veya standart .xlsx dosyaları olarak indirilebilen Excel uyumlu tablo verileri oluşturun. Çalışma sayfası adlandırma, hücre biçimlendirme, otomatik sütun genişliği, başlık stili ve veri doğrulama ayarlarını destekler. Çıktı Excel dosyalarının profesyonel görünüm ve tam işlevselliğe sahip olmasını sağlar."
    from_alias: "Excel Elektronik Tablo"
    to_alias: "Excel Standart Format"
  LaTeX:
    alias: "LaTeX Tablosu"
    what: "LaTeX, özellikle akademik makaleler, teknik belgeler ve bilimsel yayınlar oluşturmak için uygun profesyonel bir belge dizgi sistemidir. Tablo işlevselliği güçlüdür, karmaşık matematiksel formülleri, hassas düzen kontrolünü ve yüksek kaliteli PDF çıktısını destekler. Akademi ve bilimsel yayıncılıkta standart araçtır, dergi makaleleri, tezler ve teknik kılavuz dizgisinde yaygın olarak kullanılır."
    step1: "LaTeX tablo kodunu yapıştırın veya .tex dosyalarını yükleyin. Araç LaTeX tablo sözdizimini ayrıştırır ve veri içeriğini çıkarır, çoklu tablo ortamlarını (tabular, longtable, array vb.) ve karmaşık format komutlarını destekler."
    step3: "Çoklu tablo ortamı seçimi, kenarlık stili yapılandırması, başlık konum ayarları, belge sınıfı spesifikasyonu ve paket yönetimi desteği ile profesyonel LaTeX tablo kodu oluşturun. Tam derlenebilir LaTeX belgeleri oluşturabilir, çıktı tablolarının akademik yayın standartlarını karşılamasını sağlar."
    from_alias: "LaTeX Belge Tablosu"
    to_alias: "LaTeX Profesyonel Format"
  ASCII:
    alias: "ASCII Tablosu"
    what: "ASCII tabloları, tablo kenarlıklarını ve yapılarını çizmek için düz metin karakterleri kullanır, en iyi uyumluluk ve taşınabilirlik sağlar. Tüm metin editörleri, terminal ortamları ve işletim sistemleri ile uyumludur. Kod dokümantasyonu, teknik kılavuzlar, README dosyaları ve komut satırı araç çıktılarında yaygın olarak kullanılır. Programcılar ve sistem yöneticileri için tercih edilen veri görüntüleme formatıdır."
    step1: "ASCII tabloları içeren metin dosyalarını yükleyin veya tablo verilerini doğrudan yapıştırın. Araç ASCII tablo yapılarını akıllıca tanır ve ayrıştırır, çoklu kenarlık stilleri ve hizalama formatlarını destekler."
    step3: "Çoklu kenarlık stilleri (tek çizgi, çift çizgi, yuvarlatılmış köşeler vb.), metin hizalama yöntemleri ve otomatik sütun genişliği desteği ile güzel düz metin ASCII tabloları oluşturun. Oluşturulan tablolar kod editörlerinde, belgelerde ve komut satırlarında mükemmel şekilde görüntülenir."
    from_alias: "ASCII Metin Tablosu"
    to_alias: "ASCII Standart Format"
  MediaWiki:
    alias: "MediaWiki Tablosu"
    what: "MediaWiki, Wikipedia gibi ünlü wiki sitelerinde kullanılan açık kaynak yazılım platformudur. Tablo sözdizimi özlü ancak güçlüdür, tablo stili özelleştirmesi, sıralama işlevselliği ve bağlantı gömmeyi destekler. Bilgi yönetimi, işbirlikçi düzenleme ve içerik yönetim sistemlerinde yaygın olarak kullanılır, wiki ansiklopedileri ve bilgi tabanları oluşturmak için temel teknoloji olarak hizmet eder."
    step1: "MediaWiki tablo kodunu yapıştırın veya wiki kaynak dosyalarını yükleyin. Araç wiki işaretleme sözdizimini ayrıştırır ve tablo verilerini çıkarır, karmaşık wiki sözdizimi ve şablon işlemeyi destekler."
    step3: "Başlık stili ayarları, hücre hizalama, sıralama işlevselliği etkinleştirme ve kod sıkıştırma seçenekleri desteği ile standart MediaWiki tablo kodu oluşturun. Oluşturulan kod doğrudan wiki sayfa düzenlemesi için kullanılabilir, MediaWiki platformlarında mükemmel görüntüleme sağlar."
    from_alias: "MediaWiki Kaynak Kodu"
    to_alias: "MediaWiki Tablo Sözdizimi"
  TracWiki:
    alias: "TracWiki Tablosu"
    what: "Trac, tablo içeriği oluşturmak için basitleştirilmiş wiki sözdizimi kullanan web tabanlı bir proje yönetimi ve hata izleme sistemidir."
    step1: "TracWiki dosyalarını yükleyin veya tablo verilerini yapıştırın."
    step3: "Satır/sütun başlık ayarları desteği ile TracWiki uyumlu tablo kodu oluşturun, proje belge yönetimini kolaylaştırır."
    from_alias: "TracWiki Tablosu"
    to_alias: "TracWiki Formatı"
  AsciiDoc:
    alias: "AsciiDoc Tablosu"
    what: "AsciiDoc, HTML, PDF, kılavuz sayfaları ve diğer formatlara dönüştürülebilen hafif bir işaretleme dilidir, teknik dokümantasyon yazımında yaygın olarak kullanılır."
    step1: "AsciiDoc dosyalarını yükleyin veya veri yapıştırın."
    step3: "Başlık, alt bilgi ve başlık ayarları desteği ile AsciiDoc tablo sözdizimi oluşturun, AsciiDoc editörlerinde doğrudan kullanılabilir."
    from_alias: "AsciiDoc Tablosu"
    to_alias: "AsciiDoc Formatı"
  reStructuredText:
    alias: "reStructuredText Tablosu"
    what: "reStructuredText, Python topluluğu için standart dokümantasyon formatıdır, zengin tablo sözdizimini destekler, Sphinx dokümantasyon oluşturma için yaygın olarak kullanılır."
    step1: ".rst dosyalarını yükleyin veya reStructuredText verilerini yapıştırın."
    step3: "Çoklu kenarlık stilleri desteği ile standart reStructuredText tabloları oluşturun, Sphinx dokümantasyon projelerinde doğrudan kullanılabilir."
    from_alias: "reStructuredText Tablosu"
    to_alias: "reStructuredText Formatı"
  PHP:
    alias: "PHP Dizisi"
    what: "PHP, dizilerin temel veri yapısı olduğu popüler bir sunucu tarafı betik dilidir, web geliştirme ve veri işlemede yaygın olarak kullanılır."
    step1: "PHP dizileri içeren dosyaları yükleyin veya verileri doğrudan yapıştırın."
    step3: "PHP projelerinde doğrudan kullanılabilen standart PHP dizi kodu oluşturun, ilişkisel ve indeksli dizi formatlarını destekler."
    from_alias: "PHP Dizisi"
    to_alias: "PHP Kodu"
  Ruby:
    alias: "Ruby Dizisi"
    what: "Ruby, özlü ve zarif sözdizimi olan dinamik nesne yönelimli bir programlama dilidir, diziler önemli bir veri yapısıdır."
    step1: "Ruby dosyalarını yükleyin veya dizi verilerini yapıştırın."
    step3: "Ruby sözdizimi spesifikasyonlarına uygun Ruby dizi kodu oluşturun, Ruby projelerinde doğrudan kullanılabilir."
    from_alias: "Ruby Dizisi"
    to_alias: "Ruby Kodu"
  ASP:
    alias: "ASP Dizisi"
    what: "ASP (Active Server Pages), dinamik web sayfaları geliştirmek için çoklu programlama dillerini destekleyen Microsoft'un sunucu tarafı betik ortamıdır."
    step1: "ASP dosyalarını yükleyin veya dizi verilerini yapıştırın."
    step3: "VBScript ve JScript sözdizimi desteği ile ASP uyumlu dizi kodu oluşturun, ASP.NET projelerinde kullanılabilir."
    from_alias: "ASP Dizisi"
    to_alias: "ASP Kodu"
  ActionScript:
    alias: "ActionScript Dizisi"
    what: "ActionScript, öncelikle Adobe Flash ve AIR uygulama geliştirme için kullanılan nesne yönelimli bir programlama dilidir."
    step1: ".as dosyalarını yükleyin veya ActionScript verilerini yapıştırın."
    step3: "AS3 sözdizimi standartlarına uygun ActionScript dizi kodu oluşturun, Flash ve Flex proje geliştirme için kullanılabilir."
    from_alias: "ActionScript Dizisi"
    to_alias: "ActionScript Kodu"
  BBCode:
    alias: "BBCode Tablosu"
    what: "BBCode, forumlarda ve çevrimiçi topluluklarda yaygın olarak kullanılan hafif bir işaretleme dilidir, tablo desteği dahil olmak üzere basit biçimlendirme işlevselliği sağlar."
    step1: "BBCode içeren dosyaları yükleyin veya veri yapıştırın."
    step3: "Forum gönderisi ve topluluk içeriği oluşturma için uygun BBCode tablo kodu oluşturun, sıkıştırılmış çıktı formatı desteği ile."
    from_alias: "BBCode Tablosu"
    to_alias: "BBCode Formatı"
  PDF:
    alias: "PDF Tablosu"
    what: "PDF (Portable Document Format), sabit düzen, tutarlı görüntü ve yüksek kaliteli yazdırma özellikleri olan platformlar arası belge standardıdır. Resmi belgeler, raporlar, faturalar, sözleşmeler ve akademik makalelerde yaygın olarak kullanılır. İş iletişimi ve belge arşivleme için tercih edilen format, farklı cihazlar ve işletim sistemlerinde tamamen tutarlı görsel efektler sağlar."
    step1: "Herhangi bir formatta tablo verilerini içe aktarın. Araç otomatik olarak veri yapısını analiz eder ve akıllı düzen tasarımı gerçekleştirir, büyük tablo otomatik sayfalama ve karmaşık veri türü işlemeyi destekler."
    step3: "Çoklu profesyonel tema stilleri (iş, akademik, minimalist vb.), çok dilli fontlar, otomatik sayfalama, filigran ekleme ve yazdırma optimizasyonu desteği ile yüksek kaliteli PDF tablo dosyaları oluşturun. Çıktı PDF belgelerinin profesyonel görünüme sahip olmasını sağlar, iş sunumları ve resmi yayın için doğrudan kullanılabilir."
    from_alias: "Tablo Verisi"
    to_alias: "PDF Profesyonel Belge"
  JPEG:
    alias: "JPEG Resmi"
    what: "JPEG, mükemmel sıkıştırma efektleri ve geniş uyumluluk ile en yaygın kullanılan dijital resim formatıdır. Küçük dosya boyutu ve hızlı yükleme hızı onu web görüntüleme, sosyal medya paylaşımı, belge illüstrasyonları ve çevrimiçi sunumlar için uygun hale getirir. Dijital medya ve ağ iletişimi için standart resim formatı, neredeyse tüm cihazlar ve yazılımlar tarafından mükemmel şekilde desteklenir."
    step1: "Herhangi bir formatta tablo verilerini içe aktarın. Araç akıllı düzen tasarımı ve görsel optimizasyon gerçekleştirir, optimal boyut ve çözünürlüğü otomatik olarak hesaplar."
    step3: "Çoklu tema renk şemaları (açık, koyu, göz dostu vb.), uyarlanabilir düzen, metin netliği optimizasyonu ve boyut özelleştirme desteği ile yüksek çözünürlüklü JPEG tablo resimleri oluşturun. Çevrimiçi paylaşım, belge ekleme ve sunum kullanımı için uygun, çeşitli görüntü cihazlarında mükemmel görsel efektler sağlar."
    from_alias: "Tablo Verisi"
    to_alias: "JPEG Yüksek Çözünürlük Resmi"
  Jira:
    alias: "Jira Tablosu"
    what: "JIRA, Atlassian tarafından geliştirilen profesyonel proje yönetimi ve hata izleme yazılımıdır, çevik geliştirme, yazılım testi ve proje işbirliğinde yaygın olarak kullanılır. Tablo işlevselliği zengin biçimlendirme seçenekleri ve veri görüntülemeyi destekler, yazılım geliştirme ekipleri, proje yöneticileri ve kalite güvence personeli için gereksinim yönetimi, hata izleme ve ilerleme raporlamasında önemli bir araç olarak hizmet eder."
    step1: "Tablo verisi içeren dosyaları yükleyin veya veri içeriğini doğrudan yapıştırın. Araç otomatik olarak tablo verilerini ve özel karakter kaçırma işlemlerini gerçekleştirir."
    step3: "Başlık stili ayarları, hücre hizalama, karakter kaçırma işleme ve format optimizasyonu desteği ile JIRA platformu uyumlu tablo kodu oluşturun. Oluşturulan kod doğrudan JIRA sorun açıklamaları, yorumlar veya wiki sayfalarına yapıştırılabilir, JIRA sistemlerinde doğru görüntüleme ve işleme sağlar."
    from_alias: "Proje Verisi"
    to_alias: "Jira Tablo Sözdizimi"
  Textile:
    alias: "Textile Tablosu"
    what: "Textile, basit ve öğrenmesi kolay sözdizimi olan özlü hafif bir işaretleme dilidir, içerik yönetim sistemleri, blog platformları ve forum sistemlerinde yaygın olarak kullanılır. Tablo sözdizimi net ve sezgiseldir, hızlı biçimlendirme ve stil ayarlarını destekler. İçerik oluşturucular ve web sitesi yöneticileri için hızlı belge yazımı ve içerik yayınlama için ideal bir araçtır."
    step1: "Textile format dosyalarını yükleyin veya tablo verilerini yapıştırın. Araç Textile işaretleme sözdizimini ayrıştırır ve tablo içeriğini çıkarır."
    step3: "Başlık işaretlemesi, hücre hizalama, özel karakter kaçırma ve format optimizasyonu desteği ile standart Textile tablo sözdizimi oluşturun. Oluşturulan kod Textile destekleyen CMS sistemleri, blog platformları ve belge sistemlerinde doğrudan kullanılabilir, doğru içerik işleme ve görüntüleme sağlar."
    from_alias: "Textile Belgesi"
    to_alias: "Textile Tablo Sözdizimi"
  PNG:
    alias: "PNG Resmi"
    what: "PNG (Portable Network Graphics), mükemmel sıkıştırma ve şeffaflık desteği olan kayıpsız bir resim formatıdır. Web tasarımı, dijital grafikler ve profesyonel fotoğrafçılıkta yaygın olarak kullanılır. Yüksek kalitesi ve geniş uyumluluğu onu ekran görüntüleri, logolar, diyagramlar ve net detaylar ile şeffaf arka planlar gerektiren herhangi bir resim için ideal hale getirir."
    step1: "Herhangi bir formatta tablo verilerini içe aktarın. Araç akıllı düzen tasarımı ve görsel optimizasyon gerçekleştirir, PNG çıktısı için optimal boyut ve çözünürlüğü otomatik olarak hesaplar."
    step3: "Çoklu tema renk şemaları, şeffaf arka planlar, uyarlanabilir düzen ve metin netliği optimizasyonu desteği ile yüksek kaliteli PNG tablo resimleri oluşturun. Mükemmel görsel kalite ile web kullanımı, belge ekleme ve profesyonel sunumlar için mükemmeldir."
    from_alias: "Tablo Verisi"
    to_alias: "PNG Yüksek Kalite Resmi"
  TOML:
    alias: "TOML Yapılandırması"
    what: "TOML (Tom's Obvious, Minimal Language), okumak ve yazmak kolay bir yapılandırma dosyası formatıdır. Belirsizlik olmayan ve basit olacak şekilde tasarlanmıştır, yapılandırma yönetimi için modern yazılım projelerinde yaygın olarak kullanılır. Net sözdizimi ve güçlü tipleme onu uygulama ayarları ve proje yapılandırma dosyaları için mükemmel bir seçim yapar."
    step1: "TOML dosyalarını yükleyin veya yapılandırma verilerini yapıştırın. Araç TOML sözdizimini ayrıştırır ve yapılandırılmış yapılandırma bilgilerini çıkarır."
    step3: "İç içe yapılar, veri türleri ve yorumlar desteği ile standart TOML formatı oluşturun. Oluşturulan TOML dosyaları uygulama yapılandırması, derleme araçları ve proje ayarları için mükemmeldir."
    from_alias: "TOML Yapılandırması"
    to_alias: "TOML Formatı"
  INI:
    alias: "INI Yapılandırması"
    what: "INI dosyaları, birçok uygulama ve işletim sistemi tarafından kullanılan basit yapılandırma dosyalarıdır. Doğrudan anahtar-değer çifti yapıları onları manuel olarak okumayı ve düzenlemeyi kolaylaştırır. Windows uygulamaları, eski sistemler ve insan okunabilirliğinin önemli olduğu basit yapılandırma senaryolarında yaygın olarak kullanılır."
    step1: "INI dosyalarını yükleyin veya yapılandırma verilerini yapıştırın. Araç INI sözdizimini ayrıştırır ve bölüm tabanlı yapılandırma bilgilerini çıkarır."
    step3: "Bölümler, yorumlar ve çeşitli veri türleri desteği ile standart INI formatı oluşturun. Oluşturulan INI dosyaları çoğu uygulama ve yapılandırma sistemi ile uyumludur."
    from_alias: "INI Yapılandırması"
    to_alias: "INI Formatı"
  Avro:
    alias: "Avro Şeması"
    what: "Apache Avro, zengin veri yapıları, kompakt ikili format ve şema evrim yetenekleri sağlayan bir veri serileştirme sistemidir. Büyük veri işleme, mesaj kuyrukları ve dağıtık sistemlerde yaygın olarak kullanılır. Şema tanımı karmaşık veri türlerini ve sürüm uyumluluğunu destekler, veri mühendisleri ve sistem mimarları için önemli bir araç yapar."
    step1: "Avro şema dosyalarını yükleyin veya veri yapıştırın. Araç Avro şema tanımlarını ayrıştırır ve tablo yapısı bilgilerini çıkarır."
    step3: "Veri türü eşlemesi, alan kısıtlamaları ve şema doğrulama desteği ile standart Avro şema tanımları oluşturun. Oluşturulan şemalar Hadoop ekosistemleri, Kafka mesaj sistemleri ve diğer büyük veri platformlarında doğrudan kullanılabilir."
    from_alias: "Avro Şeması"
    to_alias: "Avro Veri Formatı"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf), Google'ın yapılandırılmış verileri serileştirmek için dil-nötr, platform-nötr, genişletilebilir mekanizmasıdır. Mikroservisler, API geliştirme ve veri depolamada yaygın olarak kullanılır. Verimli ikili formatı ve güçlü tipleme onu yüksek performanslı uygulamalar ve diller arası iletişim için ideal hale getirir."
    step1: ".proto dosyalarını yükleyin veya Protocol Buffer tanımlarını yapıştırın. Araç protobuf sözdizimini ayrıştırır ve mesaj yapısı bilgilerini çıkarır."
    step3: "Mesaj türleri, alan seçenekleri ve servis tanımları desteği ile standart Protocol Buffer tanımları oluşturun. Oluşturulan .proto dosyaları çoklu programlama dilleri için derlenebilir."
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf Şeması"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas, Python'da en popüler veri analizi kütüphanesidir ve DataFrame onun temel veri yapısıdır. Güçlü veri manipülasyonu, temizleme ve analiz yetenekleri sağlar, veri bilimi, makine öğrenmesi ve iş zekasında yaygın olarak kullanılır. Python geliştiricileri ve veri analistleri için vazgeçilmez bir araçtır."
    step1: "DataFrame kodu içeren Python dosyalarını yükleyin veya veri yapıştırın. Araç Pandas sözdizimini ayrıştırır ve DataFrame yapısı bilgilerini çıkarır."
    step3: "Veri türü spesifikasyonları, indeks ayarları ve veri işlemleri desteği ile standart Pandas DataFrame kodu oluşturun. Oluşturulan kod veri analizi ve işleme için Python ortamında doğrudan çalıştırılabilir."
    from_alias: "Pandas DataFrame"
    to_alias: "Python Veri Yapısı"
  RDF:
    alias: "RDF Üçlüsü"
    what: "RDF (Resource Description Framework), Web'de veri değişimi için standart bir modeldir, kaynaklar hakkındaki bilgileri grafik formunda temsil etmek üzere tasarlanmıştır. Semantik web, bilgi grafikleri ve bağlantılı veri uygulamalarında yaygın olarak kullanılır. Üçlü yapısı zengin metadata temsili ve semantik ilişkileri mümkün kılar."
    step1: "RDF dosyalarını yükleyin veya üçlü verilerini yapıştırın. Araç RDF sözdizimini ayrıştırır ve semantik ilişkiler ile kaynak bilgilerini çıkarır."
    step3: "Çeşitli serileştirmeler (RDF/XML, Turtle, N-Triples) desteği ile standart RDF formatı oluşturun. Oluşturulan RDF semantik web uygulamaları, bilgi tabanları ve bağlantılı veri sistemlerinde kullanılabilir."
    from_alias: "RDF Verisi"
    to_alias: "RDF Semantik Format"
  MATLAB:
    alias: "MATLAB Dizisi"
    what: "MATLAB, mühendislik hesaplama, veri analizi ve algoritma geliştirmede yaygın olarak kullanılan yüksek performanslı sayısal hesaplama ve görselleştirme yazılımıdır. Dizi ve matris işlemleri güçlüdür, karmaşık matematiksel hesaplamaları ve veri işlemeyi destekler. Mühendisler, araştırmacılar ve veri bilimciler için vazgeçilmez bir araçtır."
    step1: "MATLAB .m dosyalarını yükleyin veya dizi verilerini yapıştırın. Araç MATLAB sözdizimini ayrıştırır ve dizi yapısı bilgilerini çıkarır."
    step3: "Çok boyutlu diziler, veri türü spesifikasyonları ve değişken adlandırma desteği ile standart MATLAB dizi kodu oluşturun. Oluşturulan kod veri analizi ve bilimsel hesaplama için MATLAB ortamında doğrudan çalıştırılabilir."
    from_alias: "MATLAB Dizisi"
    to_alias: "MATLAB Kod Formatı"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame, R programlama dilinin temel veri yapısıdır ve istatistiksel analiz, veri madenciliği ve makine öğrenmesinde yaygın olarak kullanılır. R, istatistiksel hesaplama ve grafikler için önde gelen araçtır ve DataFrame güçlü veri manipülasyonu, istatistiksel analiz ve görselleştirme yetenekleri sağlar. Yapılandırılmış veri analizi ile çalışan veri bilimciler, istatistikçiler ve araştırmacılar için vazgeçilmezdir."
    step1: "R veri dosyalarını yükleyin veya DataFrame kodunu yapıştırın. Araç R sözdizimini ayrıştırır ve sütun türleri, satır adları ve veri içeriği dahil olmak üzere DataFrame yapısı bilgilerini çıkarır."
    step3: "Veri türü spesifikasyonları, faktör seviyeleri, satır/sütun adları ve R'ye özgü veri yapıları desteği ile standart R DataFrame kodu oluşturun. Oluşturulan kod istatistiksel analiz ve veri işleme için R ortamında doğrudan çalıştırılabilir."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Dönüştürmeye Başla"
  start_generating: "Oluşturmaya başla"
  api_docs: "API Belgeleri"
related:
  section_title: 'Daha Fazla {{ if and .from (ne .from "generator") }}{{ .from }} ve {{ end }}{{ .to }} Dönüştürücü'
  section_description: '{{ if and .from (ne .from "generator") }}{{ .from }} ve {{ end }}{{ .to }} formatları için daha fazla dönüştürücü keşfedin. Profesyonel çevrimiçi dönüştürme araçlarımızla verilerinizi birden fazla format arasında dönüştürün.'
  title: "{{ .from }}'den {{ .to }}'ye"
howto:
  step2: "Profesyonel özelliklerle gelişmiş çevrimiçi tablo editörümüzü kullanarak verileri düzenleyin. Boş satırları silme, yinelemeleri kaldırma, veri aktarımı, sıralama, regex bulma ve değiştirme ve gerçek zamanlı önizlemeyi destekler. Tüm değişiklikler otomatik olarak %s formatına kesin, güvenilir sonuçlarla dönüştürülür."
  section_title: "{{ . }} nasıl kullanılır"
  converter_description: "Adım adım kılavuzumuzla {{ .from }}'yi {{ .to }}'ye dönüştürmeyi öğrenin. Gelişmiş özellikler ve gerçek zamanlı önizleme ile profesyonel çevrimiçi dönüştürücü."
  generator_description: "Çevrimiçi oluşturucumuzla profesyonel {{ .to }} tabloları oluşturmayı öğrenin. Excel benzeri düzenleme, gerçek zamanlı önizleme ve anında dışa aktarma yetenekleri."
extension:
  section_title: "Tablo Algılama ve Çıkarma Uzantısı"
  section_description: "Herhangi bir web sitesinden tek tıkla tablo çıkarın. Excel, CSV, JSON dahil 30'dan fazla formata anında dönüştürün - kopyala-yapıştır gerekmez."
  features:
    extraction_title: "Tek Tıkla Tablo Çıkarma"
    extraction_description: "Kopyala-yapıştır olmadan herhangi bir web sayfasından anında tablo çıkarın - profesyonel veri çıkarma basitleştirildi"
    formats_title: "30+ Format Dönüştürücü Desteği"
    formats_description: "Gelişmiş tablo dönüştürücümüzle çıkarılan tabloları Excel, CSV, JSON, Markdown, SQL ve daha fazlasına dönüştürün"
    detection_title: "Akıllı Tablo Algılama"
    detection_description: "Hızlı veri çıkarma ve dönüştürme için herhangi bir web sayfasındaki tabloları otomatik olarak algılar ve vurgular"
  hover_tip: "✨ Çıkarma simgesini görmek için herhangi bir tablonun üzerine gelin"
recommendations:
  section_title: "Üniversiteler ve Profesyoneller Tarafından Öneriliyor"
  section_description: "TableConvert, güvenilir tablo dönüştürme ve veri işleme için üniversiteler, araştırma kurumları ve geliştirme ekiplerindeki profesyoneller tarafından güveniliyor."
  cards:
    university_title: "Wisconsin-Madison Üniversitesi"
    university_description: "TableConvert.com - Profesyonel ücretsiz çevrimiçi tablo dönüştürücü ve veri formatları aracı"
    university_link: "Makaleyi Oku"
    facebook_title: "Veri Profesyonelleri Topluluğu"
    facebook_description: "Facebook geliştirici gruplarında veri analistleri ve profesyoneller tarafından paylaşıldı ve önerildi"
    facebook_link: "Gönderiyi Görüntüle"
    twitter_title: "Geliştirici Topluluğu"
    twitter_description: "Tablo dönüştürme için X (Twitter)'da @xiaoying_eth ve diğer geliştiriciler tarafından önerildi"
    twitter_link: "Tweet'i Görüntüle"
faq:
  section_title: "Sıkça Sorulan Sorular"
  section_description: "Ücretsiz çevrimiçi tablo dönüştürücümüz, veri formatları ve dönüştürme süreci hakkında yaygın sorular."
  what: "%s formatı nedir?"
  howto_convert:
    question: "{{ . }} ücretsiz nasıl kullanılır?"
    answer: "Ücretsiz çevrimiçi tablo dönüştürücümüzü kullanarak {{ .from }} dosyanızı yükleyin, verileri yapıştırın veya web sayfalarından çıkarın. Profesyonel dönüştürücü aracımız, gerçek zamanlı önizleme ve gelişmiş düzenleme özellikleriyle verilerinizi anında {{ .to }} formatına dönüştürür. Dönüştürülen sonucu hemen indirin veya kopyalayın."
  security:
    question: "Bu çevrimiçi dönüştürücüyü kullanırken verilerim güvende mi?"
    answer: "Kesinlikle! Tüm tablo dönüştürmeleri tarayıcınızda yerel olarak gerçekleşir - verileriniz cihazınızı asla terk etmez. Çevrimiçi dönüştürücümüz her şeyi istemci tarafında işler, tam gizlilik ve veri güvenliği sağlar. Sunucularımızda hiçbir dosya saklanmaz."
  free:
    question: "TableConvert gerçekten ücretsiz mi?"
    answer: "Evet, TableConvert tamamen ücretsiz! Tüm dönüştürücü özellikleri, tablo editörü, veri oluşturucu araçları ve dışa aktarma seçenekleri maliyet, kayıt veya gizli ücret olmadan kullanılabilir. Sınırsız dosyayı çevrimiçi ücretsiz dönüştürün."
  filesize:
    question: "Çevrimiçi dönüştürücünün dosya boyutu sınırları nelerdir?"
    answer: "Ücretsiz çevrimiçi tablo dönüştürücümüz 10MB'a kadar dosyaları destekler. Daha büyük dosyalar, toplu işleme veya kurumsal ihtiyaçlar için daha yüksek sınırlara sahip tarayıcı uzantımızı veya profesyonel API hizmetimizi kullanın."
stats:
  conversions: "Dönüştürülen Tablolar"
  tables: "Oluşturulan Tablolar"
  formats: "Veri Dosyası Formatları"
  rating: "Kullanıcı Değerlendirmesi"
