site:
  fullname: "Online Tabellen Konverter"
  name: "TableConvert"
  subtitle: "Kostenloser Online-Tabellenkonverter und -generator"
  intro: "TableConvert ist ein kostenloses Online-Tool zur Tabellenkonvertierung und Datengenerierung, das die Konvertierung zwischen über 30 Formaten unterstützt, einschließlich Excel, CSV, JSON, Markdown, LaTeX, SQL und mehr."
  followTwitter: "Folgen Sie uns auf X"
title:
  converter: "%s zu %s"
  generator: "%s Generator"
post:
  tags:
    converter: "Konverter"
    editor: "Editor"
    generator: "Generator"
    maker: "Builder"
  converter:
    title: "%s zu %s Online konvertieren"
    short: "Ein kostenloses und leistungsstarkes %s zu %s Online-Tool"
    intro: "Benutzerfreundlicher Online-%s-zu-%s-Konverter. Transformieren Sie Tabellendaten mühelos mit unserem intuitiven Konvertierungstool. Schnell, zuverlässig und benutzerfreundlich."
  generator:
    title: "Online-%s-Editor und -Generator"
    short: "Professionelles %s Online-Generierungstool mit umfassenden Funktionen"
    intro: "Benutzerfreundlicher Online-%s-Generator und Tabelleneditor. Erstellen Sie professionelle Datentabellen mühelos mit unserem intuitiven Tool und Echtzeit-Vorschau."
navbar:
  search:
    placeholder: "Konverter suchen..."
  sponsor: "Spendieren Sie uns einen Kaffee"
  extension: "Erweiterung"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Datenquelle"
    placeholder: "Fügen Sie Ihre %s-Daten ein oder ziehen Sie %s-Dateien hierher"
    example: "Beispiel"
    upload: "Datei hochladen"
    extract:
      enter: "Aus Webseite extrahieren"
      intro: "Geben Sie eine Webseiten-URL mit Tabellendaten ein, um strukturierte Daten automatisch zu extrahieren"
      btn: "%s extrahieren"
    excel:
      sheet: "Arbeitsblatt"
      none: "Keine"
  tableEditor:
    title: "Online-Tabelleneditor"
    undo: "Rückgängig"
    redo: "Wiederholen"
    transpose: "Transponieren"
    clear: "Löschen"
    deleteBlank: "Leere löschen"
    deleteDuplicate: "Duplikate entfernen"
    uppercase: "GROSSBUCHSTABEN"
    lowercase: "kleinbuchstaben"
    capitalize: "Großschreibung"
    replace:
      replace: "Suchen & Ersetzen (Regex unterstützt)"
      subst: "Ersetzen durch..."
      btn: "Alle ersetzen"
  tableGenerator:
    title: "Tabellengenerator"
    sponsor: "Spendieren Sie uns einen Kaffee"
    copy: "In Zwischenablage kopieren"
    download: "Datei herunterladen"
    tooltip:
      html:
        escape: "HTML-Sonderzeichen (&, <, >, \", ') maskieren, um Anzeigefehler zu vermeiden"
        div: "DIV+CSS-Layout anstelle traditioneller TABLE-Tags verwenden, besser für responsives Design geeignet"
        minify: "Leerzeichen und Zeilenumbrüche entfernen, um komprimierten HTML-Code zu generieren"
        thead: "Standard-Tabellenkopf (&lt;thead&gt;) und -körper (&lt;tbody&gt;) Struktur generieren"
        tableCaption: "Beschreibenden Titel über der Tabelle hinzufügen (&lt;caption&gt; Element)"
        tableClass: "CSS-Klassenname zur Tabelle hinzufügen für einfache Stilanpassung"
        tableId: "Eindeutige ID-Kennung für die Tabelle für JavaScript-Manipulation setzen"
      jira:
        escape: "Pipe-Zeichen (|) maskieren, um Konflikte mit Jira-Tabellensyntax zu vermeiden"
      json:
        parsingJSON: "JSON-Strings in Zellen intelligent in Objekte parsen"
        minify: "Kompaktes einzeiliges JSON-Format generieren, um Dateigröße zu reduzieren"
        format: "JSON-Ausgabedatenstruktur auswählen: Objekt-Array, 2D-Array, etc."
      latex:
        escape: "LaTeX-Sonderzeichen (%, &, _, #, $, etc.) maskieren, um ordnungsgemäße Kompilierung sicherzustellen"
        ht: "Floating-Position-Parameter [!ht] hinzufügen, um Tabellenposition auf der Seite zu kontrollieren"
        mwe: "Vollständiges LaTeX-Dokument generieren"
        tableAlign: "Horizontale Ausrichtung der Tabelle auf der Seite setzen"
        tableBorder: "Tabellenrandstil konfigurieren: kein Rand, teilweiser Rand, vollständiger Rand"
        label: "Tabellenlabel für \\ref{} Befehl-Querverweise setzen"
        caption: "Tabellenüberschrift setzen, um über oder unter der Tabelle anzuzeigen"
        location: "Anzeigeposition der Tabellenüberschrift wählen: oben oder unten"
        tableType: "Tabellenumgebungstyp wählen: tabular, longtable, array, etc."
      markdown:
        escape: "Markdown-Sonderzeichen (*, _, |, \\, etc.) maskieren, um Formatkonflikte zu vermeiden"
        pretty: "Spaltenbreiten automatisch ausrichten, um schöneres Tabellenformat zu generieren"
        simple: "Vereinfachte Syntax verwenden, äußere Randvertikallinien weglassen"
        boldFirstRow: "Text der ersten Zeile fett machen"
        boldFirstColumn: "Text der ersten Spalte fett machen"
        firstHeader: "Erste Zeile als Kopfzeile behandeln und Trennlinie hinzufügen"
        textAlign: "Spaltentext-Ausrichtung setzen: links, zentriert, rechts"
        multilineHandling: "Mehrzeilige Textbehandlung: Zeilenumbrüche beibehalten, zu \\n maskieren, &lt;br&gt; Tags verwenden"

        includeLineNumbers: "Zeilennummernspalte auf der linken Seite der Tabelle hinzufügen"
      magic:
        builtin: "Vordefinierte gemeinsame Vorlagenformate auswählen"
        rowsTpl: "<table> <tr> <th>Magische Syntax</th> <th>Beschreibung</th> <th>Unterstützte JS-Methoden</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>1., 2. ... Feld der <b>Ü</b>berschrift, auch {hA} {hB} ...</td> <td>String-Methoden</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>1., 2. ... Feld der aktuellen Zeile, auch {$A} {$B} ...</td> <td>String-Methoden</td> </tr> <tr> <td>{F,} {F;}</td> <td>Aktuelle Zeile durch den String nach <b>F</b> teilen</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>Zeilen<b>n</b>ummer der aktuellen <b>Z</b>eile ab 1 oder 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>E</b>nd-Zeilen<b>n</b>ummer der <b>Z</b>eilen </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>JavaScript-Code aus<b>f</b>ühren, z.B.: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Backslash <b>\\</b> verwenden, um geschweifte Klammern {...} auszugeben </td> <td></td> </tr></table>"
        headerTpl: "Benutzerdefinierte Ausgabevorlage für Kopfbereich"
        footerTpl: "Benutzerdefinierte Ausgabevorlage für Fußbereich"
      textile:
        escape: "Textile-Syntaxzeichen (|, ., -, ^) maskieren, um Formatkonflikte zu vermeiden"
        rowHeader: "Erste Zeile als Kopfzeile setzen"
        thead: "Textile-Syntaxmarkierungen für Tabellenkopf und -körper hinzufügen"
      xml:
        escape: "XML-Sonderzeichen (&lt;, &gt;, &amp;, \", ') maskieren, um gültiges XML sicherzustellen"
        minify: "Komprimierte XML-Ausgabe generieren, zusätzliche Leerzeichen entfernen"
        rootElement: "XML-Wurzelelement-Tag-Name setzen"
        rowElement: "XML-Element-Tag-Name für jede Datenzeile setzen"
        declaration: "XML-Deklarations-Header hinzufügen (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Daten als XML-Attribute anstelle von Kindelementen ausgeben"
        cdata: "Textinhalt mit CDATA umhüllen, um Sonderzeichen zu schützen"
        encoding: "Zeichenkodierungsformat für XML-Dokument setzen"
        indentation: "XML-Einrückungszeichen wählen: Leerzeichen oder Tabs"
      yaml:
        indentSize: "Anzahl der Leerzeichen für YAML-Hierarchie-Einrückung setzen (normalerweise 2 oder 4)"
        arrayStyle: "Array-Format: Block (ein Element pro Zeile) oder Flow (Inline-Format)"
        quotationStyle: "String-Anführungszeichenstil: keine Anführungszeichen, einfache Anführungszeichen, doppelte Anführungszeichen"
      pdf:
        theme: "PDF-Tabellen-Visualstil für professionelle Dokumente wählen"
        headerColor: "Hintergrundfarbe für PDF-Tabellenkopf wählen"
        showHead: "Kopfzeilenanzeige auf PDF-Seiten steuern"
        docTitle: "Optionaler Titel für das PDF-Dokument"
        docDescription: "Optionaler Beschreibungstext für PDF-Dokument"
      csv:
        bom: "UTF-8-Byte-Order-Mark hinzufügen, um Excel und anderer Software bei der Kodierungserkennung zu helfen"
      excel:
        autoWidth: "Spaltenbreite automatisch basierend auf Inhalt anpassen"
        protectSheet: "Arbeitsblattschutz mit Passwort aktivieren: tableconvert.com"
      sql:
        primaryKey: "Primärschlüssel-Feldname für CREATE TABLE-Anweisung angeben"
        dialect: "Datenbanktyp auswählen, beeinflusst Anführungszeichen und Datentyp-Syntax"
      ascii:
        forceSep: "Trennlinien zwischen jeder Datenzeile erzwingen"
        style: "ASCII-Tabellenrand-Zeichenstil auswählen"
        comment: "Kommentarmarkierungen hinzufügen, um die gesamte Tabelle zu umhüllen"
      mediawiki:
        minify: "Ausgabecode komprimieren, zusätzliche Leerzeichen entfernen"
        header: "Erste Zeile als Kopfzeilenstil markieren"
        sort: "Tabellen-Klick-Sortierfunktionalität aktivieren"
      asciidoc:
        minify: "AsciiDoc-Format-Ausgabe komprimieren"
        firstHeader: "Erste Zeile als Kopfzeile setzen"
        lastFooter: "Letzte Zeile als Fußzeile setzen"
        title: "Titeltext zur Tabelle hinzufügen"
      tracwiki:
        rowHeader: "Erste Zeile als Kopfzeile setzen"
        colHeader: "Erste Spalte als Kopfzeile setzen"
      bbcode:
        minify: "BBCode-Ausgabeformat komprimieren"
      restructuredtext:
        style: "reStructuredText-Tabellenrandstil auswählen"
        forceSep: "Trennlinien erzwingen"
    label:
      ascii:
        forceSep: "Zeilentrenner"
        style: "Randstil"
        comment: "Kommentar-Wrapper"
      restructuredtext:
        style: "Randstil"
        forceSep: "Trenner erzwingen"
      bbcode:
        minify: "Ausgabe komprimieren"
      csv:
        doubleQuote: "Doppelte Anführungszeichen"
        delimiter: "Feldtrenner"
        bom: "UTF-8 BOM"
        valueDelimiter: "Werttrenner"
        rowDelimiter: "Zeilentrenner"
        prefix: "Zeilenpräfix"
        suffix: "Zeilensuffix"
      excel:
        autoWidth: "Automatische Breite"
        textFormat: "Textformat"
        protectSheet: "Blatt schützen"
        boldFirstRow: "Erste Zeile fett"
        boldFirstColumn: "Erste Spalte fett"
        sheetName: "Blattname"
      html:
        escape: "HTML-Zeichen maskieren"
        div: "DIV-Tabelle"
        minify: "Code komprimieren"
        thead: "Tabellenkopf-Struktur"
        tableCaption: "Tabellenüberschrift"
        tableClass: "Tabellenklasse"
        tableId: "Tabellen-ID"
        rowHeader: "Zeilenkopf"
        colHeader: "Spaltenkopf"
      jira:
        escape: "Zeichen maskieren"
        rowHeader: "Zeilenkopf"
        colHeader: "Spaltenkopf"
      json:
        parsingJSON: "JSON parsen"
        minify: "Ausgabe komprimieren"
        format: "Datenformat"
        rootName: "Root-Objektname"
        indentSize: "Einrückungsgröße"
      jsonlines:
        parsingJSON: "JSON parsen"
        format: "Datenformat"
      latex:
        escape: "LaTeX-Tabellenzeichen maskieren"
        ht: "Float-Position"
        mwe: "Vollständiges Dokument"
        tableAlign: "Tabellenausrichtung"
        tableBorder: "Randstil"
        label: "Referenzlabel"
        caption: "Tabellenüberschrift"
        location: "Überschriftposition"
        tableType: "Tabellentyp"
        boldFirstRow: "Erste Zeile fett"
        boldFirstColumn: "Erste Spalte fett"
        textAlign: "Textausrichtung"
        borders: "Randeinstellungen"
      markdown:
        escape: "Zeichen maskieren"
        pretty: "Schöne Markdown-Tabelle"
        simple: "Einfaches Markdown-Format"
        boldFirstRow: "Erste Zeile fett"
        boldFirstColumn: "Erste Spalte fett"
        firstHeader: "Erste Überschrift"
        textAlign: "Textausrichtung"
        multilineHandling: "Mehrzeilige Behandlung"

        includeLineNumbers: "Zeilennummern hinzufügen"
        align: "Ausrichtung"
      mediawiki:
        minify: "Code komprimieren"
        header: "Header-Markup"
        sort: "Sortierbar"
      asciidoc:
        minify: "Format komprimieren"
        firstHeader: "Erste Überschrift"
        lastFooter: "Letzte Fußzeile"
        title: "Tabellentitel"
      tracwiki:
        rowHeader: "Zeilenkopf"
        colHeader: "Spaltenkopf"
      sql:
        drop: "Tabelle löschen (falls vorhanden)"
        create: "Tabelle erstellen"
        oneInsert: "Batch-Einfügung"
        table: "Tabellenname"
        dialect: "Datenbanktyp"
        primaryKey: "Primärschlüssel"
      magic:
        builtin: "Eingebaute Vorlage"
        rowsTpl: "Zeilenvorlage, Syntax ->"
        headerTpl: "Header-Vorlage"
        footerTpl: "Footer-Vorlage"
      textile:
        escape: "Zeichen maskieren"
        rowHeader: "Zeilenkopf"
        thead: "Tabellenkopf-Syntax"
      xml:
        escape: "XML-Zeichen maskieren"
        minify: "Ausgabe komprimieren"
        rootElement: "Root-Element"
        rowElement: "Zeilen-Element"
        declaration: "XML-Deklaration"
        attributes: "Attributmodus"
        cdata: "CDATA-Wrapper"
        encoding: "Kodierung"
        indentSize: "Einrückungsgröße"
      yaml:
        indentSize: "Einrückungsgröße"
        arrayStyle: "Array-Stil"
        quotationStyle: "Anführungszeichenstil"
      pdf:
        theme: "PDF-Tabellen-Thema"
        headerColor: "PDF-Kopfzeilen-Farbe"
        showHead: "PDF-Kopfzeilen-Anzeige"
        docTitle: "PDF-Dokument-Titel"
        docDescription: "PDF-Dokument-Beschreibung"
sidebar:
  all: "Alle Konvertierungstools"
  dataSource:
    title: "Datenquelle"
    description:
      converter: "Importieren Sie %s zur Konvertierung in %s. Unterstützt Datei-Upload, Online-Bearbeitung und Web-Datenextraktion."
      generator: "Erstellen Sie Tabellendaten mit Unterstützung für mehrere Eingabemethoden einschließlich manueller Eingabe, Dateiimport und Vorlagenerstellung."
  tableEditor:
    title: "Online-Tabelleneditor"
    description:
      converter: "Verarbeiten Sie %s online mit unserem Tabelleneditor. Excel-ähnliche Bedienungserfahrung mit Unterstützung für das Löschen leerer Zeilen, Deduplizierung, Sortierung und Suchen & Ersetzen."
      generator: "Leistungsstarker Online-Tabelleneditor mit Excel-ähnlicher Bedienungserfahrung. Unterstützt das Löschen leerer Zeilen, Deduplizierung, Sortierung und Suchen & Ersetzen."
  tableGenerator:
    title: "Tabellengenerator"
    description:
      converter: "Generieren Sie schnell %s mit Echtzeit-Vorschau des Tabellengenerators. Umfangreiche Exportoptionen, Ein-Klick-Kopieren & Download."
      generator: "Exportieren Sie %s-Daten in mehreren Formaten für verschiedene Anwendungsszenarien. Unterstützt benutzerdefinierte Optionen und Echtzeit-Vorschau."
footer:
  changelog: "Änderungsprotokoll"
  sponsor: "Sponsoren"
  contact: "Kontakt"
  privacyPolicy: "Datenschutzrichtlinie"
  about: "Über uns"
  resources: "Ressourcen"
  popularConverters: "Beliebte Konverter"
  popularGenerators: "Beliebte Generatoren"
  dataSecurity: "Ihre Daten sind sicher - alle Konvertierungen laufen in Ihrem Browser."
converters:
  Markdown:
    alias: "Markdown-Tabelle"
    what: "Markdown ist eine leichtgewichtige Markup-Sprache, die weit verbreitet für technische Dokumentation, Blog-Content-Erstellung und Webentwicklung verwendet wird. Ihre Tabellensyntax ist prägnant und intuitiv und unterstützt Textausrichtung, Link-Einbettung und Formatierung. Es ist das bevorzugte Tool für Programmierer und technische Autoren, perfekt kompatibel mit GitHub, GitLab und anderen Code-Hosting-Plattformen."
    step1: "Fügen Sie Markdown-Tabellendaten in den Datenquellenbereich ein oder ziehen Sie .md-Dateien direkt zum Upload. Das Tool parst automatisch Tabellenstruktur und Formatierung und unterstützt komplexe verschachtelte Inhalte und Sonderzeichenbehandlung."
    step3: "Generieren Sie Standard-Markdown-Tabellencode in Echtzeit mit Unterstützung für mehrere Ausrichtungsmethoden, Textfettung, Zeilennummernhinzufügung und andere erweiterte Formateinstellungen. Der generierte Code ist vollständig kompatibel mit GitHub und großen Markdown-Editoren, bereit zur Verwendung mit Ein-Klick-Kopieren."
    from_alias: "Markdown-Tabellendatei"
    to_alias: "Markdown-Tabellenformat"
  Magic:
    alias: "Benutzerdefinierte Vorlage"
    what: "Magic-Vorlage ist ein einzigartiger fortgeschrittener Datengenerator dieses Tools, der es Benutzern ermöglicht, beliebige Formatdatenausgabe durch benutzerdefinierte Vorlagensyntax zu erstellen. Unterstützt Variablenersetzung, bedingte Beurteilung und Schleifenverarbeitung. Es ist die ultimative Lösung für die Behandlung komplexer Datenkonvertierungsanforderungen und personalisierter Ausgabeformate, besonders geeignet für Entwickler und Dateningenieure."
    step1: "Wählen Sie eingebaute gemeinsame Vorlagen oder erstellen Sie benutzerdefinierte Vorlagensyntax. Unterstützt reiche Variablen und Funktionen, die komplexe Datenstrukturen und Geschäftslogik handhaben können."
    step3: "Generieren Sie Datenausgabe, die vollständig den benutzerdefinierten Formatanforderungen entspricht. Unterstützt komplexe Datenkonvertierungslogik und bedingte Verarbeitung, verbessert erheblich die Datenverarbeitungseffizienz und Ausgabequalität. Ein mächtiges Tool für Batch-Datenverarbeitung."
    from_alias: "Tabellendaten"
    to_alias: "Benutzerdefinierte Formatausgabe"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) ist das am weitesten verbreitete Datenaustauschformat, perfekt unterstützt von Excel, Google Sheets, Datenbanksystemen und verschiedenen Datenanalyse-Tools. Seine einfache Struktur und starke Kompatibilität machen es zum Standardformat für Datenmigration, Batch-Import/Export und plattformübergreifenden Datenaustausch, weit verbreitet in Geschäftsanalyse, Datenwissenschaft und Systemintegration."
    step1: "Laden Sie CSV-Dateien hoch oder fügen Sie CSV-Daten direkt ein. Das Tool erkennt intelligent verschiedene Trennzeichen (Komma, Tab, Semikolon, Pipe, etc.), erkennt automatisch Datentypen und Kodierungsformate und unterstützt schnelles Parsen großer Dateien und komplexer Datenstrukturen."
    step3: "Generieren Sie Standard-CSV-Formatdateien mit Unterstützung für benutzerdefinierte Trennzeichen, Anführungszeichenstile, Kodierungsformate und BOM-Markierungseinstellungen. Gewährleistet perfekte Kompatibilität mit Zielsystemen und bietet Download- und Komprimierungsoptionen für Datenverarbeitungsanforderungen auf Unternehmensebene."
    from_alias: "CSV-Datendatei"
    to_alias: "CSV-Standardformat"
  JSON:
    alias: "JSON Array"
    what: "JSON (JavaScript Object Notation) ist das Standard-Tabellendatenformat für moderne Webanwendungen, REST-APIs und Microservice-Architekturen. Seine klare Struktur und effiziente Analyse machen es weit verbreitet in Front-End- und Back-End-Dateninteraktion, Konfigurationsdateispeicherung und NoSQL-Datenbanken. Unterstützt verschachtelte Objekte, Array-Strukturen und mehrere Datentypen, was es zu unverzichtbaren Tabellendaten für moderne Softwareentwicklung macht."
    step1: "Laden Sie JSON-Dateien hoch oder fügen Sie JSON-Arrays ein. Unterstützt automatische Erkennung und Analyse von Objekt-Arrays, verschachtelten Strukturen und komplexen Datentypen. Das Tool validiert intelligent JSON-Syntax und bietet Fehlerhinweise."
    step3: "Generieren Sie mehrere JSON-Format-Ausgaben: Standard-Objekt-Arrays, 2D-Arrays, Spalten-Arrays und Schlüssel-Wert-Paar-Formate. Unterstützt verschönerte Ausgabe, Komprimierungsmodus, benutzerdefinierte Root-Objektnamen und Einrückungseinstellungen, perfekt angepasst an verschiedene API-Schnittstellen und Datenspeicheranforderungen."
    from_alias: "JSON Array-Datei"
    to_alias: "JSON-Standardformat"
  JSONLines:
    alias: "JSONLines-Format"
    what: "JSON Lines (auch bekannt als NDJSON) ist ein wichtiges Format für Big-Data-Verarbeitung und Streaming-Datenübertragung, wobei jede Zeile ein unabhängiges JSON-Objekt enthält. Weit verbreitet in Log-Analyse, Datenstrom-Verarbeitung, maschinellem Lernen und verteilten Systemen. Unterstützt inkrementelle Verarbeitung und paralleles Computing, was es zur idealen Wahl für die Behandlung großskaliger strukturierter Daten macht."
    step1: "Laden Sie JSONLines-Dateien hoch oder fügen Sie Daten ein. Das Tool parst JSON-Objekte zeilenweise und unterstützt große Datei-Streaming-Verarbeitung und Fehlerzeilen-Überspringfunktionalität."
    step3: "Generieren Sie Standard-JSONLines-Format mit jeder Zeile, die ein vollständiges JSON-Objekt ausgibt. Geeignet für Streaming-Verarbeitung, Batch-Import und Big-Data-Analyse-Szenarien, unterstützt Datenvalidierung und Formatoptimierung."
    from_alias: "JSONLines-Daten"
    to_alias: "JSONLines-Streaming-Format"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) ist das Standardformat für Datenaustausch und Konfigurationsmanagement auf Unternehmensebene, mit strengen Syntaxspezifikationen und leistungsstarken Validierungsmechanismen. Weit verbreitet in Webdiensten, Konfigurationsdateien, Dokumentenspeicherung und Systemintegration. Unterstützt Namespaces, Schema-Validierung und XSLT-Transformation, was es zu wichtigen Tabellendaten für Unternehmensanwendungen macht."
    step1: "Laden Sie XML-Dateien hoch oder fügen Sie XML-Daten ein. Das Tool parst automatisch XML-Struktur und konvertiert sie in Tabellenformat, unterstützt Namespace, Attributbehandlung und komplexe verschachtelte Strukturen."
    step3: "Generieren Sie XML-Ausgabe, die XML-Standards entspricht. Unterstützt benutzerdefinierte Root-Elemente, Zeilenelement-Namen, Attributmodi, CDATA-Wrapping und Zeichenkodierungseinstellungen. Gewährleistet Datenintegrität und Kompatibilität, erfüllt Anwendungsanforderungen auf Unternehmensebene."
    from_alias: "XML-Datendatei"
    to_alias: "XML-Standardformat"
  YAML:
    alias: "YAML-Konfiguration"
    what: "YAML ist ein menschenfreundlicher Datenserialisierungsstandard, bekannt für seine klare hierarchische Struktur und prägnante Syntax. Weit verbreitet in Konfigurationsdateien, DevOps-Tool-Ketten, Docker Compose und Kubernetes-Deployment. Seine starke Lesbarkeit und prägnante Syntax machen es zu einem wichtigen Konfigurationsformat für moderne Cloud-native Anwendungen und automatisierte Operationen."
    step1: "Laden Sie YAML-Dateien hoch oder fügen Sie YAML-Daten ein. Das Tool parst intelligent YAML-Struktur und validiert Syntaxkorrektheit, unterstützt Multi-Dokument-Formate und komplexe Datentypen."
    step3: "Generieren Sie Standard-YAML-Format-Ausgabe mit Unterstützung für Block- und Flow-Array-Stile, mehrere Anführungszeicheneinstellungen, benutzerdefinierte Einrückung und Kommentarerhaltung. Gewährleistet, dass Ausgabe-YAML-Dateien vollständig kompatibel mit verschiedenen Parsern und Konfigurationssystemen sind."
    from_alias: "YAML-Konfigurationsdatei"
    to_alias: "YAML-Standardformat"
  MySQL:
      alias: "MySQL-Abfrageergebnisse"
      what: "MySQL ist das weltweit beliebteste Open-Source-Relationale-Datenbank-Managementsystem, bekannt für seine hohe Leistung, Zuverlässigkeit und Benutzerfreundlichkeit. Weit verbreitet in Webanwendungen, Unternehmenssystemen und Datenanalyseplattformen. MySQL-Abfrageergebnisse enthalten typischerweise strukturierte Tabellendaten und dienen als wichtige Datenquelle in der Datenbankverwaltung und Datenanalyse."
      step1: "Fügen Sie MySQL-Abfrageausgabeergebnisse in den Datenquellenbereich ein. Das Tool erkennt und parst automatisch das MySQL-Kommandozeilen-Ausgabeformat, unterstützt verschiedene Abfrageergebnisstile und Zeichenkodierungen und behandelt intelligent Kopfzeilen und Datenzeilen."
      step3: "Konvertieren Sie MySQL-Abfrageergebnisse schnell in mehrere Tabellendatenformate, um Datenanalyse, Berichtserstellung, systemübergreifende Datenmigration und Datenvalidierung zu erleichtern. Ein praktisches Tool für Datenbankadministratoren und Datenanalysten."
      from_alias: "MySQL-Abfrageausgabe"
      to_alias: "MySQL-Tabellendaten"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) ist die Standard-Operationssprache für relationale Datenbanken, verwendet für Datenabfrage, Einfügung, Aktualisierung und Löschoperationen. Als Kerntechnologie der Datenbankverwaltung wird SQL weit verbreitet in Datenanalyse, Business Intelligence, ETL-Verarbeitung und Data Warehouse-Konstruktion verwendet. Es ist ein unverzichtbares Skill-Tool für Datenprofis."
    step1: "Fügen Sie INSERT SQL-Anweisungen ein oder laden Sie .sql-Dateien hoch. Das Tool parst intelligent SQL-Syntax und extrahiert Tabellendaten, unterstützt mehrere SQL-Dialekte und komplexe Abfrageanweisungsverarbeitung."
    step3: "Generieren Sie Standard-SQL INSERT-Anweisungen und Tabellenerstellungsanweisungen. Unterstützt mehrere Datenbankdialekte (MySQL, PostgreSQL, SQLite, SQL Server, Oracle), behandelt automatisch Datentypzuordnung, Zeichenmaskierung und Primärschlüssel-Einschränkungen. Stellt sicher, dass generierter SQL-Code direkt ausgeführt werden kann."
    from_alias: "SQL-Datendatei"
    to_alias: "SQL-Standardanweisung"
  Qlik:
      alias: "Qlik-Tabelle"
      what: "Qlik ist ein Softwareanbieter, der sich auf Datenvisualisierung, Executive Dashboards und Self-Service Business Intelligence-Produkte spezialisiert hat, zusammen mit Tableau und Microsoft."
      step1: ""
      step3: "Schließlich zeigt der [Tabellengenerator](#TableGenerator) die Konvertierungsergebnisse. Verwenden Sie in Ihrer Qlik Sense, Qlik AutoML, QlikView oder anderen Qlik-fähigen Software."
      from_alias: "Qlik-Tabelle"
      to_alias: "Qlik-Tabelle"
  DAX:
      alias: "DAX-Tabelle"
      what: "DAX (Data Analysis Expressions) ist eine Programmiersprache, die in Microsoft Power BI für die Erstellung berechneter Spalten, Kennzahlen und benutzerdefinierter Tabellen verwendet wird."
      step1: ""
      step3: "Schließlich zeigt der [Tabellengenerator](#TableGenerator) die Konvertierungsergebnisse. Wie erwartet wird es in mehreren Microsoft-Produkten verwendet, einschließlich Microsoft Power BI, Microsoft Analysis Services und Microsoft Power Pivot für Excel."
      from_alias: "DAX-Tabelle"
      to_alias: "DAX-Tabelle"
  Firebase:
    alias: "Firebase-Liste"
    what: "Firebase ist eine BaaS-Anwendungsentwicklungsplattform, die gehostete Backend-Services wie Echtzeitdatenbank, Cloud-Speicher, Authentifizierung, Crash-Reporting usw. bereitstellt."
    step1: ""
    step3: "Schließlich zeigt der [Tabellengenerator](#TableGenerator) die Konvertierungsergebnisse. Sie können dann die Push-Methode in der Firebase-API verwenden, um zu einer Datenliste in der Firebase-Datenbank hinzuzufügen."
    from_alias: "Firebase-Liste"
    to_alias: "Firebase-Liste"
  HTML:
    alias: "HTML-Tabelle"
    what: "HTML-Tabellen sind die Standardmethode zur Anzeige strukturierter Daten in Webseiten, erstellt mit table-, tr-, td- und anderen Tags. Unterstützt umfangreiche Stilanpassung, responsives Layout und interaktive Funktionalität. Weit verbreitet in Website-Entwicklung, Datenanzeige und Berichtserstellung, dient als wichtige Komponente der Frontend-Entwicklung und des Webdesigns."
    step1: "Fügen Sie HTML-Code mit Tabellen ein oder laden Sie HTML-Dateien hoch. Das Tool erkennt und extrahiert automatisch Tabellendaten von Seiten, unterstützt komplexe HTML-Strukturen, CSS-Stile und verschachtelte Tabellenverarbeitung."
    step3: "Generieren Sie semantischen HTML-Tabellencode mit Unterstützung für thead/tbody-Struktur, CSS-Klasseneinstellungen, Tabellenüberschriften, Zeilen-/Spaltenköpfe und responsive Attributkonfiguration. Stellt sicher, dass generierter Tabellencode Webstandards erfüllt mit guter Zugänglichkeit und SEO-Freundlichkeit."
    from_alias: "HTML-Webtabelle"
    to_alias: "HTML-Standardtabelle"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel ist die weltweit beliebteste Tabellenkalkulationssoftware, weit verbreitet in Geschäftsanalyse, Finanzmanagement, Datenverarbeitung und Berichtserstellung. Seine leistungsstarken Datenverarbeitungsfähigkeiten, reiche Funktionsbibliothek und flexible Visualisierungsfeatures machen es zum Standardtool für Büroautomatisierung und Datenanalyse, mit umfangreichen Anwendungen in fast allen Branchen und Bereichen."
    step1: "Laden Sie Excel-Dateien hoch (unterstützt .xlsx, .xls-Formate) oder kopieren Sie Tabellendaten direkt aus Excel und fügen Sie sie ein. Das Tool unterstützt Multi-Arbeitsblatt-Verarbeitung, komplexe Formaterkennung und schnelle Analyse großer Dateien, behandelt automatisch zusammengeführte Zellen und Datentypen."
    step3: "Generieren Sie Excel-kompatible Tabellendaten, die direkt in Excel eingefügt oder als Standard-.xlsx-Dateien heruntergeladen werden können. Unterstützt Arbeitsblattbenennung, Zellenformatierung, automatische Spaltenbreite, Kopfzeilenstyling und Datenvalidierungseinstellungen. Stellt sicher, dass Ausgabe-Excel-Dateien professionelles Aussehen und vollständige Funktionalität haben."
    from_alias: "Excel-Tabellenkalkulation"
    to_alias: "Excel-Standardformat"
  LaTeX:
    alias: "LaTeX-Tabelle"
    what: "LaTeX ist ein professionelles Dokumentensatzsystem, besonders geeignet für die Erstellung akademischer Arbeiten, technischer Dokumente und wissenschaftlicher Publikationen. Seine Tabellenfunktionalität ist leistungsstark, unterstützt komplexe mathematische Formeln, präzise Layout-Kontrolle und hochwertige PDF-Ausgabe. Es ist das Standardtool in der Wissenschaft und im wissenschaftlichen Publizieren, weit verbreitet in Zeitschriftenartikeln, Dissertationen und technischen Handbuch-Satz."
    step1: "Fügen Sie LaTeX-Tabellencode ein oder laden Sie .tex-Dateien hoch. Das Tool parst LaTeX-Tabellensyntax und extrahiert Dateninhalte, unterstützt mehrere Tabellenumgebungen (tabular, longtable, array, etc.) und komplexe Formatbefehle."
    step3: "Generieren Sie professionellen LaTeX-Tabellencode mit Unterstützung für mehrere Tabellenumgebungsauswahl, Randstilkonfiguration, Überschriftspositionseinstellungen, Dokumentklassenspezifikation und Paketmanagement. Kann vollständige kompilierbare LaTeX-Dokumente generieren, stellt sicher, dass Ausgabetabellen akademische Publikationsstandards erfüllen."
    from_alias: "LaTeX-Dokumenttabelle"
    to_alias: "LaTeX-Professionelles Format"
  ASCII:
    alias: "ASCII-Tabelle"
    what: "ASCII-Tabellen verwenden einfache Textzeichen, um Tabellenränder und -strukturen zu zeichnen, bieten beste Kompatibilität und Portabilität. Kompatibel mit allen Texteditoren, Terminalumgebungen und Betriebssystemen. Weit verbreitet in Code-Dokumentation, technischen Handbüchern, README-Dateien und Kommandozeilen-Tool-Ausgabe. Das bevorzugte Datenanzeigenformat für Programmierer und Systemadministratoren."
    step1: "Laden Sie Textdateien mit ASCII-Tabellen hoch oder fügen Sie Tabellendaten direkt ein. Das Tool erkennt und parst intelligent ASCII-Tabellenstrukturen, unterstützt mehrere Randstile und Ausrichtungsformate."
    step3: "Generieren Sie schöne Klartext-ASCII-Tabellen mit Unterstützung für mehrere Randstile (einfache Linie, doppelte Linie, abgerundete Ecken, etc.), Textausrichtungsmethoden und automatische Spaltenbreite. Generierte Tabellen werden perfekt in Code-Editoren, Dokumenten und Kommandozeilen angezeigt."
    from_alias: "ASCII-Texttabelle"
    to_alias: "ASCII-Standardformat"
  MediaWiki:
    alias: "MediaWiki-Tabelle"
    what: "MediaWiki ist die Open-Source-Software-Plattform, die von berühmten Wiki-Sites wie Wikipedia verwendet wird. Ihre Tabellensyntax ist prägnant aber mächtig, unterstützt Tabellenstil-Anpassung, Sortierfunktionalität und Link-Einbettung. Weit verbreitet in Wissensmanagement, kollaborativer Bearbeitung und Content-Management-Systemen, dient als Kerntechnologie für den Aufbau von Wiki-Enzyklopädien und Wissensbasen."
    step1: "Fügen Sie MediaWiki-Tabellencode ein oder laden Sie Wiki-Quelldateien hoch. Das Tool parst Wiki-Markup-Syntax und extrahiert Tabellendaten, unterstützt komplexe Wiki-Syntax und Template-Verarbeitung."
    step3: "Generieren Sie Standard-MediaWiki-Tabellencode mit Unterstützung für Kopfzeilenstil-Einstellungen, Zellenausrichtung, Sortierfunktionalitäts-Aktivierung und Code-Komprimierungsoptionen. Generierter Code kann direkt für Wiki-Seitenbearbeitung verwendet werden, gewährleistet perfekte Anzeige auf MediaWiki-Plattformen."
    from_alias: "MediaWiki-Quellcode"
    to_alias: "MediaWiki-Tabellensyntax"
  TracWiki:
    alias: "TracWiki-Tabelle"
    what: "Trac ist ein webbasiertes Projektmanagement- und Bug-Tracking-System, das vereinfachte Wiki-Syntax zur Erstellung von Tabelleninhalten verwendet."
    step1: "Laden Sie TracWiki-Dateien hoch oder fügen Sie Tabellendaten ein."
    step3: "Generieren Sie TracWiki-kompatiblen Tabellencode mit Unterstützung für Zeilen-/Spalten-Kopfzeilen-Einstellungen, erleichtert Projektdokumentmanagement."
    from_alias: "TracWiki-Tabelle"
    to_alias: "TracWiki-Format"
  AsciiDoc:
    alias: "AsciiDoc-Tabelle"
    what: "AsciiDoc ist eine leichtgewichtige Markup-Sprache, die in HTML, PDF, Handbuchseiten und andere Formate konvertiert werden kann, weit verbreitet für technische Dokumentationserstellung."
    step1: "Laden Sie AsciiDoc-Dateien hoch oder fügen Sie Daten ein."
    step3: "Generieren Sie AsciiDoc-Tabellensyntax mit Unterstützung für Kopfzeilen-, Fußzeilen- und Titeleinstellungen, direkt verwendbar in AsciiDoc-Editoren."
    from_alias: "AsciiDoc-Tabelle"
    to_alias: "AsciiDoc-Format"
  reStructuredText:
    alias: "reStructuredText-Tabelle"
    what: "reStructuredText ist das Standard-Dokumentationsformat für die Python-Community, unterstützt reiche Tabellensyntax, häufig verwendet für Sphinx-Dokumentationsgenerierung."
    step1: "Laden Sie .rst-Dateien hoch oder fügen Sie reStructuredText-Daten ein."
    step3: "Generieren Sie Standard-reStructuredText-Tabellen mit Unterstützung für mehrere Randstile, direkt verwendbar in Sphinx-Dokumentationsprojekten."
    from_alias: "reStructuredText-Tabelle"
    to_alias: "reStructuredText-Format"
  PHP:
    alias: "PHP-Array"
    what: "PHP ist eine beliebte serverseitige Skriptsprache, wobei Arrays ihre Kerndatenstruktur sind, weit verbreitet in Webentwicklung und Datenverarbeitung."
    step1: "Laden Sie Dateien mit PHP-Arrays hoch oder fügen Sie Daten direkt ein."
    step3: "Generieren Sie Standard-PHP-Array-Code, der direkt in PHP-Projekten verwendet werden kann, mit Unterstützung für assoziative und indizierte Array-Formate."
    from_alias: "PHP-Array"
    to_alias: "PHP-Code"
  Ruby:
    alias: "Ruby-Array"
    what: "Ruby ist eine dynamische objektorientierte Programmiersprache mit prägnanter und eleganter Syntax, wobei Arrays eine wichtige Datenstruktur sind."
    step1: "Laden Sie Ruby-Dateien hoch oder fügen Sie Array-Daten ein."
    step3: "Generieren Sie Ruby-Array-Code, der den Ruby-Syntaxspezifikationen entspricht, direkt verwendbar in Ruby-Projekten."
    from_alias: "Ruby-Array"
    to_alias: "Ruby-Code"
  ASP:
    alias: "ASP-Array"
    what: "ASP (Active Server Pages) ist Microsofts serverseitige Skriptumgebung, die mehrere Programmiersprachen für die Entwicklung dynamischer Webseiten unterstützt."
    step1: "Laden Sie ASP-Dateien hoch oder fügen Sie Array-Daten ein."
    step3: "Generieren Sie ASP-kompatiblen Array-Code mit Unterstützung für VBScript- und JScript-Syntax, verwendbar in ASP.NET-Projekten."
    from_alias: "ASP-Array"
    to_alias: "ASP-Code"
  ActionScript:
    alias: "ActionScript-Array"
    what: "ActionScript ist eine objektorientierte Programmiersprache, die hauptsächlich für Adobe Flash- und AIR-Anwendungsentwicklung verwendet wird."
    step1: "Laden Sie .as-Dateien hoch oder fügen Sie ActionScript-Daten ein."
    step3: "Generieren Sie ActionScript-Array-Code, der AS3-Syntaxstandards entspricht, verwendbar für Flash- und Flex-Projektentwicklung."
    from_alias: "ActionScript-Array"
    to_alias: "ActionScript-Code"
  BBCode:
    alias: "BBCode-Tabelle"
    what: "BBCode ist eine leichtgewichtige Markup-Sprache, die häufig in Foren und Online-Communities verwendet wird und einfache Formatierungsfunktionen einschließlich Tabellenunterstützung bietet."
    step1: "Laden Sie Dateien mit BBCode hoch oder fügen Sie Daten ein."
    step3: "Generieren Sie BBCode-Tabellencode, geeignet für Forum-Posts und Community-Content-Erstellung, mit Unterstützung für komprimiertes Ausgabeformat."
    from_alias: "BBCode-Tabelle"
    to_alias: "BBCode-Format"
  PDF:
    alias: "PDF-Tabelle"
    what: "PDF (Portable Document Format) ist ein plattformübergreifender Dokumentenstandard mit festem Layout, konsistenter Anzeige und hochwertigen Druckeigenschaften. Weit verbreitet in formellen Dokumenten, Berichten, Rechnungen, Verträgen und wissenschaftlichen Arbeiten. Das bevorzugte Format für Geschäftskommunikation und Dokumentenarchivierung, das vollständig konsistente visuelle Effekte auf verschiedenen Geräten und Betriebssystemen gewährleistet."
    step1: "Importieren Sie Tabellendaten in jedem Format. Das Tool analysiert automatisch die Datenstruktur und führt intelligentes Layout-Design durch, unterstützt große Tabellen-Auto-Paginierung und komplexe Datentypverarbeitung."
    step3: "Generieren Sie hochwertige PDF-Tabellendateien mit Unterstützung für mehrere professionelle Theme-Stile (Business, Akademisch, Minimalistisch, etc.), mehrsprachige Schriftarten, Auto-Paginierung, Wasserzeichen-Hinzufügung und Druckoptimierung. Stellt sicher, dass Ausgabe-PDF-Dokumente professionelles Aussehen haben, direkt verwendbar für Geschäftspräsentationen und formelle Veröffentlichung."
    from_alias: "Tabellendaten"
    to_alias: "PDF-Professionelles Dokument"
  JPEG:
    alias: "JPEG-Bild"
    what: "JPEG ist das am weitesten verbreitete digitale Bildformat mit exzellenten Kompressionseffekten und breiter Kompatibilität. Seine kleine Dateigröße und schnelle Ladegeschwindigkeit machen es geeignet für Web-Anzeige, Social Media-Sharing, Dokumentenillustrationen und Online-Präsentationen. Das Standard-Bildformat für digitale Medien und Netzwerkkommunikation, perfekt unterstützt von fast allen Geräten und Software."
    step1: "Importieren Sie Tabellendaten in jedem Format. Das Tool führt intelligentes Layout-Design und visuelle Optimierung durch, berechnet automatisch optimale Größe und Auflösung."
    step3: "Generieren Sie hochauflösende JPEG-Tabellenbilder mit Unterstützung für mehrere Theme-Farbschemata (hell, dunkel, augenfreundlich, etc.), adaptives Layout, Textklarheitsoptimierung und Größenanpassung. Geeignet für Online-Sharing, Dokumenteneinfügung und Präsentationsnutzung, gewährleistet exzellente visuelle Effekte auf verschiedenen Anzeigegeräten."
    from_alias: "Tabellendaten"
    to_alias: "JPEG-Hochauflösendes Bild"
  Jira:
    alias: "Jira-Tabelle"
    what: "JIRA ist professionelle Projektmanagement- und Bug-Tracking-Software, entwickelt von Atlassian, weit verbreitet in agiler Entwicklung, Softwaretests und Projektzusammenarbeit. Seine Tabellenfunktionalität unterstützt reiche Formatierungsoptionen und Datenanzeige, dient als wichtiges Tool für Softwareentwicklungsteams, Projektmanager und Qualitätssicherungspersonal in Anforderungsmanagement, Bug-Tracking und Fortschrittsberichterstattung."
    step1: "Laden Sie Dateien mit Tabellendaten hoch oder fügen Sie Dateninhalte direkt ein. Das Tool verarbeitet automatisch Tabellendaten und Sonderzeichen-Escaping."
    step3: "Generieren Sie JIRA-plattformkompatiblen Tabellencode mit Unterstützung für Header-Stil-Einstellungen, Zellenausrichtung, Zeichen-Escape-Verarbeitung und Formatoptimierung. Generierter Code kann direkt in JIRA-Issue-Beschreibungen, Kommentare oder Wiki-Seiten eingefügt werden, gewährleistet korrekte Anzeige und Rendering in JIRA-Systemen."
    from_alias: "Projektdaten"
    to_alias: "Jira-Tabellensyntax"
  Textile:
    alias: "Textile-Tabelle"
    what: "Textile ist eine prägnante leichtgewichtige Markup-Sprache mit einfacher und leicht zu erlernender Syntax, weit verbreitet in Content-Management-Systemen, Blog-Plattformen und Forum-Systemen. Seine Tabellensyntax ist klar und intuitiv, unterstützt schnelle Formatierung und Stil-Einstellungen. Ein ideales Tool für Content-Ersteller und Website-Administratoren für schnelles Dokumentenschreiben und Content-Veröffentlichung."
    step1: "Laden Sie Textile-Format-Dateien hoch oder fügen Sie Tabellendaten ein. Das Tool parst Textile-Markup-Syntax und extrahiert Tabelleninhalte."
    step3: "Generieren Sie Standard-Textile-Tabellensyntax mit Unterstützung für Header-Markup, Zellenausrichtung, Sonderzeichen-Escaping und Formatoptimierung. Generierter Code kann direkt in CMS-Systemen, Blog-Plattformen und Dokumentensystemen verwendet werden, die Textile unterstützen, gewährleistet korrektes Content-Rendering und -Anzeige."
    from_alias: "Textile-Dokument"
    to_alias: "Textile-Tabellensyntax"
  PNG:
    alias: "PNG-Bild"
    what: "PNG (Portable Network Graphics) ist ein verlustfreies Bildformat mit exzellenter Kompression und Transparenzunterstützung. Weit verbreitet in Webdesign, digitaler Grafik und professioneller Fotografie. Seine hohe Qualität und breite Kompatibilität machen es ideal für Screenshots, Logos, Diagramme und alle Bilder, die scharfe Details und transparente Hintergründe erfordern."
    step1: "Importieren Sie Tabellendaten in jedem Format. Das Tool führt intelligentes Layout-Design und visuelle Optimierung durch, berechnet automatisch optimale Größe und Auflösung für PNG-Ausgabe."
    step3: "Generieren Sie hochwertige PNG-Tabellenbilder mit Unterstützung für mehrere Theme-Farbschemata, transparente Hintergründe, adaptives Layout und Textklarheitsoptimierung. Perfekt für Web-Nutzung, Dokumenteneinfügung und professionelle Präsentationen mit exzellenter visueller Qualität."
    from_alias: "Tabellendaten"
    to_alias: "PNG-Hochwertiges Bild"
  TOML:
    alias: "TOML-Konfiguration"
    what: "TOML (Tom's Obvious, Minimal Language) ist ein Konfigurationsdateiformat, das einfach zu lesen und zu schreiben ist. Entwickelt, um eindeutig und einfach zu sein, wird es weit verbreitet in modernen Softwareprojekten für Konfigurationsmanagement verwendet. Seine klare Syntax und starke Typisierung machen es zu einer exzellenten Wahl für Anwendungseinstellungen und Projektkonfigurationsdateien."
    step1: "Laden Sie TOML-Dateien hoch oder fügen Sie Konfigurationsdaten ein. Das Tool parst TOML-Syntax und extrahiert strukturierte Konfigurationsinformationen."
    step3: "Generieren Sie Standard-TOML-Format mit Unterstützung für verschachtelte Strukturen, Datentypen und Kommentare. Generierte TOML-Dateien sind perfekt für Anwendungskonfiguration, Build-Tools und Projekteinstellungen."
    from_alias: "TOML-Konfiguration"
    to_alias: "TOML-Format"
  INI:
    alias: "INI-Konfiguration"
    what: "INI-Dateien sind einfache Konfigurationsdateien, die von vielen Anwendungen und Betriebssystemen verwendet werden. Ihre unkomplizierte Schlüssel-Wert-Paar-Struktur macht sie einfach zu lesen und manuell zu bearbeiten. Weit verbreitet in Windows-Anwendungen, Legacy-Systemen und einfachen Konfigurationsszenarien, wo menschliche Lesbarkeit wichtig ist."
    step1: "Laden Sie INI-Dateien hoch oder fügen Sie Konfigurationsdaten ein. Das Tool parst INI-Syntax und extrahiert abschnittsbasierte Konfigurationsinformationen."
    step3: "Generieren Sie Standard-INI-Format mit Unterstützung für Abschnitte, Kommentare und verschiedene Datentypen. Generierte INI-Dateien sind kompatibel mit den meisten Anwendungen und Konfigurationssystemen."
    from_alias: "INI-Konfiguration"
    to_alias: "INI-Format"
  Avro:
    alias: "Avro-Schema"
    what: "Apache Avro ist ein Datenserialisierungssystem, das reiche Datenstrukturen, kompaktes Binärformat und Schema-Evolutionsfähigkeiten bietet. Weit verbreitet in Big Data-Verarbeitung, Message Queues und verteilten Systemen. Seine Schema-Definition unterstützt komplexe Datentypen und Versionskompatibilität, was es zu einem wichtigen Tool für Dateningenieure und Systemarchitekten macht."
    step1: "Laden Sie Avro-Schema-Dateien hoch oder fügen Sie Daten ein. Das Tool parst Avro-Schema-Definitionen und extrahiert Tabellenstrukturinformationen."
    step3: "Generieren Sie Standard-Avro-Schema-Definitionen mit Unterstützung für Datentyp-Mapping, Feldbeschränkungen und Schema-Validierung. Generierte Schemas können direkt in Hadoop-Ökosystemen, Kafka-Message-Systemen und anderen Big Data-Plattformen verwendet werden."
    from_alias: "Avro-Schema"
    to_alias: "Avro-Datenformat"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) ist Googles sprachneutraler, plattformneutraler, erweiterbarer Mechanismus zur Serialisierung strukturierter Daten. Weit verbreitet in Microservices, API-Entwicklung und Datenspeicherung. Sein effizientes Binärformat und starke Typisierung machen es ideal für Hochleistungsanwendungen und sprachübergreifende Kommunikation."
    step1: "Laden Sie .proto-Dateien hoch oder fügen Sie Protocol Buffer-Definitionen ein. Das Tool parst protobuf-Syntax und extrahiert Message-Strukturinformationen."
    step3: "Generieren Sie Standard-Protocol Buffer-Definitionen mit Unterstützung für Message-Typen, Feldoptionen und Service-Definitionen. Generierte .proto-Dateien können für mehrere Programmiersprachen kompiliert werden."
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf-Schema"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas ist die beliebteste Datenanalysebibliothek in Python, wobei DataFrame ihre Kerndatenstruktur ist. Sie bietet leistungsstarke Datenmanipulation, -bereinigung und -analysefähigkeiten, weit verbreitet in Data Science, Machine Learning und Business Intelligence. Ein unverzichtbares Tool für Python-Entwickler und Datenanalysten."
    step1: "Laden Sie Python-Dateien mit DataFrame-Code hoch oder fügen Sie Daten ein. Das Tool parst Pandas-Syntax und extrahiert DataFrame-Strukturinformationen."
    step3: "Generieren Sie Standard-Pandas DataFrame-Code mit Unterstützung für Datentypspezifikationen, Index-Einstellungen und Datenoperationen. Generierter Code kann direkt in Python-Umgebung für Datenanalyse und -verarbeitung ausgeführt werden."
    from_alias: "Pandas DataFrame"
    to_alias: "Python-Datenstruktur"
  RDF:
    alias: "RDF-Tripel"
    what: "RDF (Resource Description Framework) ist ein Standardmodell für Datenaustausch im Web, entwickelt zur Darstellung von Informationen über Ressourcen in Graphenform. Weit verbreitet in Semantic Web, Wissensgraphen und Linked Data-Anwendungen. Seine Tripel-Struktur ermöglicht reiche Metadaten-Darstellung und semantische Beziehungen."
    step1: "Laden Sie RDF-Dateien hoch oder fügen Sie Tripel-Daten ein. Das Tool parst RDF-Syntax und extrahiert semantische Beziehungen und Ressourceninformationen."
    step3: "Generieren Sie Standard-RDF-Format mit Unterstützung für verschiedene Serialisierungen (RDF/XML, Turtle, N-Triples). Generiertes RDF kann in Semantic Web-Anwendungen, Wissensbasen und Linked Data-Systemen verwendet werden."
    from_alias: "RDF-Daten"
    to_alias: "RDF-Semantisches Format"
  MATLAB:
    alias: "MATLAB-Array"
    what: "MATLAB ist eine hochleistungsfähige numerische Computing- und Visualisierungssoftware, weit verbreitet in Ingenieurswesen, Datenanalyse und Algorithmusentwicklung. Seine Array- und Matrixoperationen sind leistungsstark, unterstützen komplexe mathematische Berechnungen und Datenverarbeitung. Ein unverzichtbares Tool für Ingenieure, Forscher und Datenwissenschaftler."
    step1: "Laden Sie MATLAB .m-Dateien hoch oder fügen Sie Array-Daten ein. Das Tool parst MATLAB-Syntax und extrahiert Array-Strukturinformationen."
    step3: "Generieren Sie Standard-MATLAB-Array-Code mit Unterstützung für mehrdimensionale Arrays, Datentypspezifikationen und Variablenbenennung. Generierter Code kann direkt in MATLAB-Umgebung für Datenanalyse und wissenschaftliches Computing ausgeführt werden."
    from_alias: "MATLAB-Array"
    to_alias: "MATLAB-Code-Format"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame ist die zentrale Datenstruktur der Programmiersprache R, weit verbreitet in statistischer Analyse, Data Mining und maschinellem Lernen. R ist das führende Tool für statistische Berechnungen und Grafiken, wobei DataFrame leistungsstarke Datenmanipulation, statistische Analyse und Visualisierungsfähigkeiten bietet. Unverzichtbar für Datenwissenschaftler, Statistiker und Forscher, die mit strukturierter Datenanalyse arbeiten."
    step1: "Laden Sie R-Datendateien hoch oder fügen Sie DataFrame-Code ein. Das Tool parst R-Syntax und extrahiert DataFrame-Strukturinformationen einschließlich Spaltentypen, Zeilennamen und Dateninhalten."
    step3: "Generieren Sie Standard-R DataFrame-Code mit Unterstützung für Datentypspezifikationen, Faktor-Level, Zeilen-/Spaltennamen und R-spezifische Datenstrukturen. Generierter Code kann direkt in R-Umgebung für statistische Analyse und Datenverarbeitung ausgeführt werden."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Konvertierung starten"
  start_generating: "Generierung starten"
  api_docs: "API-Dokumentation"
related:
  section_title: 'Weitere {{ if and .from (ne .from "generator") }}{{ .from }}- und {{ end }}{{ .to }}-Konverter'
  section_description: 'Entdecken Sie weitere Konverter für {{ if and .from (ne .from "generator") }}{{ .from }}- und {{ end }}{{ .to }}-Formate. Transformieren Sie Ihre Daten zwischen mehreren Formaten mit unseren professionellen Online-Konvertierungstools.'
  title: "{{ .from }} zu {{ .to }}"
howto:
  step2: "Bearbeiten Sie Daten mit unserem erweiterten Online-Tabelleneditor mit professionellen Funktionen. Unterstützt das Löschen leerer Zeilen, Entfernen von Duplikaten, Datentransposition, Sortierung, Regex-Suchen und -Ersetzen sowie Echtzeit-Vorschau. Alle Änderungen werden automatisch in das %s-Format mit präzisen, zuverlässigen Ergebnissen konvertiert."
  section_title: "So verwenden Sie den {{ . }}"
  converter_description: "Lernen Sie, {{ .from }} zu {{ .to }} mit unserer Schritt-für-Schritt-Anleitung zu konvertieren. Professioneller Online-Konverter mit erweiterten Funktionen und Echtzeit-Vorschau."
  generator_description: "Lernen Sie, professionelle {{ .to }}-Tabellen mit unserem Online-Generator zu erstellen. Excel-ähnliche Bearbeitung, Echtzeit-Vorschau und sofortige Export-Funktionen."
extension:
  section_title: "Tabellenerkennung & Extraktions-Erweiterung"
  section_description: "Extrahieren Sie Tabellen von jeder Website mit einem Klick. Konvertieren Sie sofort in über 30 Formate einschließlich Excel, CSV, JSON - kein Kopieren und Einfügen erforderlich."
  features:
    extraction_title: "Ein-Klick-Tabellenextraktion"
    extraction_description: "Extrahieren Sie sofort Tabellen von jeder Webseite ohne Kopieren und Einfügen - professionelle Datenextraktion leicht gemacht"
    formats_title: "Unterstützung für über 30 Formate"
    formats_description: "Konvertieren Sie extrahierte Tabellen zu Excel, CSV, JSON, Markdown, SQL und mehr mit unserem erweiterten Tabellenkonverter"
    detection_title: "Intelligente Tabellenerkennung"
    detection_description: "Erkennt und markiert automatisch Tabellen auf jeder Webseite für schnelle Datenextraktion und -konvertierung"
  hover_tip: "✨ Bewegen Sie die Maus über eine Tabelle, um das Extraktionssymbol zu sehen"
recommendations:
  section_title: "Empfohlen von Universitäten & Fachleuten"
  section_description: "TableConvert wird von Fachleuten in Universitäten, Forschungseinrichtungen und Entwicklungsteams für zuverlässige Tabellenkonvertierung und Datenverarbeitung vertraut."
  cards:
    university_title: "Universität Wisconsin-Madison"
    university_description: "TableConvert.com - Professionelles kostenloses Online-Tabellenkonvertierungs- und Datenformat-Tool"
    university_link: "Artikel lesen"
    facebook_title: "Daten-Fachleute-Community"
    facebook_description: "Geteilt und empfohlen von Datenanalysten und Fachleuten in Facebook-Entwicklergruppen"
    facebook_link: "Beitrag ansehen"
    twitter_title: "Entwickler-Community"
    twitter_description: "Empfohlen von @xiaoying_eth und anderen Entwicklern auf X (Twitter) für Tabellenkonvertierung"
    twitter_link: "Tweet ansehen"
faq:
  section_title: "Häufig gestellte Fragen"
  section_description: "Häufige Fragen zu unserem kostenlosen Online-Tabellenkonverter, Datenformaten und Konvertierungsprozess."
  what: "Was ist das %s-Format?"
  howto_convert:
    question: "Wie verwende ich den {{ . }} kostenlos?"
    answer: "Laden Sie Ihre {{ .from }}-Datei hoch, fügen Sie Daten ein oder extrahieren Sie von Webseiten mit unserem kostenlosen Online-Tabellenkonverter. Unser professionelles Konverter-Tool transformiert Ihre Daten sofort in das {{ .to }}-Format mit Echtzeit-Vorschau und erweiterten Bearbeitungsfunktionen. Laden Sie das konvertierte Ergebnis sofort herunter oder kopieren Sie es."
  security:
    question: "Sind meine Daten sicher, wenn ich diesen Online-Konverter verwende?"
    answer: "Absolut! Alle Tabellenkonvertierungen finden lokal in Ihrem Browser statt - Ihre Daten verlassen niemals Ihr Gerät. Unser Online-Konverter verarbeitet alles clientseitig und gewährleistet vollständige Privatsphäre und Datensicherheit. Keine Dateien werden auf unseren Servern gespeichert."
  free:
    question: "Ist TableConvert wirklich kostenlos zu verwenden?"
    answer: "Ja, TableConvert ist völlig kostenlos! Alle Konverter-Funktionen, Tabelleneditor, Datengenerator-Tools und Export-Optionen sind ohne Kosten, Registrierung oder versteckte Gebühren verfügbar. Konvertieren Sie unbegrenzt Dateien online kostenlos."
  filesize:
    question: "Welche Dateigrößenbeschränkungen hat der Online-Konverter?"
    answer: "Unser kostenloser Online-Tabellenkonverter unterstützt Dateien bis zu 10MB. Für größere Dateien, Batch-Verarbeitung oder Unternehmensanforderungen verwenden Sie unsere Browser-Erweiterung oder den professionellen API-Service mit höheren Limits."
stats:
  conversions: "Konvertierte Tabellen"
  tables: "Generierte Tabellen"
  formats: "Datendateiformate"
  rating: "Benutzerbewertung"
