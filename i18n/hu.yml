site:
  fullname: "Table Convert"
  name: "TableConvert"
  subtitle: "Ingyenes Online Táblázat Konverter és Generátor"
  intro: "A TableConvert egy ingyenes online táblázat konverter és adatgener<PERSON><PERSON> es<PERSON>, amely támogatja a konverziót 30+ form<PERSON><PERSON>, beleértve az Excel, CSV, JSON, Markdown, LaTeX, SQL és még sok más formátumot."
  followTwitter: "Kövess minket X-en"
title:
  converter: "%s %s-re"
  generator: "%s Generátor"
post:
  tags:
    converter: "Konverter"
    editor: "Szerkesztő"
    generator: "Generátor"
    maker: "Készítő"
  converter:
    title: "%s konvertálása %s-re Online"
    short: "Egy ingyenes és hatékony %s %s-re online eszköz"
    intro: "Könnyen használható online %s %s-re konverter. Alakítsd át a táblázat adatokat könnyedén az intuitív konverziós eszközünkkel. Gyors, megbízható és felhasználóbarát."
  generator:
    title: "Online %s Szerkesztő és Generátor"
    short: "Professzionális %s online generáló eszköz átfogó funkciókkal"
    intro: "Könnyen használható online %s generátor és táblázat szerkesztő. Hozz létre professzionális adattáblázatokat könnyedén az intuitív eszközünkkel és valós idejű előnézettel."
navbar:
  search:
    placeholder: "Konverter keresése ..."
  sponsor: "Vegyél nekem egy kávét"
  extension: "Bővítmény"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Adatforrás"
    placeholder: "Illeszd be a %s adataidat vagy húzd ide a %s fájlokat"
    example: "Példa"
    upload: "Fájl feltöltése"
    extract:
      enter: "Kinyerés weboldalról"
      intro: "Adj meg egy weboldal URL-t, amely táblázat adatokat tartalmaz a strukturált adatok automatikus kinyeréséhez"
      btn: "%s kinyerése"
    excel:
      sheet: "Munkalap"
      none: "Nincs"
  tableEditor:
    title: "Online Táblázat Szerkesztő"
    undo: "Visszavonás"
    redo: "Újra"
    transpose: "Transzponálás"
    clear: "Törlés"
    deleteBlank: "Üres törlése"
    deleteDuplicate: "Duplikátumok eltávolítása"
    uppercase: "NAGYBETŰS"
    lowercase: "kisbetűs"
    capitalize: "Első betű nagy"
    replace:
      replace: "Keresés és csere (Regex támogatott)"
      subst: "Csere erre..."
      btn: "Összes cseréje"
  tableGenerator:
    title: "Táblázat Generátor"
    sponsor: "Vegyél nekem egy kávét"
    copy: "Másolás vágólapra"
    download: "Fájl letöltése"
    tooltip:
      html:
        escape: "Escape HTML speciális karakterek (&, <, >, \", ') a megjelenítési hibák megelőzésére"
        div: "Használjon DIV+CSS elrendezést a hagyományos TABLE címkék helyett, jobban alkalmas a reszponzív tervezéshez"
        minify: "Távolítsa el a szóközöket és sortöréseket a tömörített HTML kód generálásához"
        thead: "Generáljon szabványos táblázat fejléc (&lt;thead&gt;) és törzs (&lt;tbody&gt;) struktúrát"
        tableCaption: "Adjon leíró címet a táblázat fölé (&lt;caption&gt; elem)"
        tableClass: "Adjon CSS osztálynevet a táblázathoz a könnyű stílus testreszabáshoz"
        tableId: "Állítson be egyedi ID azonosítót a táblázathoz JavaScript manipulációhoz"
      jira:
        escape: "Escape pipe karakterek (|) a Jira táblázat szintaxissal való konfliktusok elkerülése érdekében"
      json:
        parsingJSON: "Intelligensen elemezze a JSON karakterláncokat a cellákban objektumokká"
        minify: "Generáljon kompakt egysoros JSON formátumot a fájlméret csökkentéséhez"
        format: "Válassza ki a kimeneti JSON adatstruktúrát: objektum tömb, 2D tömb, stb."
      latex:
        escape: "Escape LaTeX speciális karakterek (%, &, _, #, $, stb.) a megfelelő fordítás biztosításához"
        ht: "Adjon hozzá lebegő pozíció paramétert [!ht] a táblázat pozíciójának vezérléséhez az oldalon"
        mwe: "Generáljon teljes LaTeX dokumentumot"
        tableAlign: "Állítsa be a táblázat vízszintes igazítását az oldalon"
        tableBorder: "Konfigurálja a táblázat szegély stílusát: nincs szegély, részleges szegély, teljes szegély"
        label: "Állítson be táblázat címkét a \\ref{} parancs kereszthivatkozásaihoz"
        caption: "Állítson be táblázat feliratot a táblázat felett vagy alatt való megjelenítéshez"
        location: "Válassza ki a táblázat felirat megjelenítési pozícióját: fent vagy lent"
        tableType: "Válassza ki a táblázat környezet típusát: tabular, longtable, array, stb."
      markdown:
        escape: "Escape Markdown speciális karakterek (*, _, |, \\, stb.) a formátum konfliktusok elkerülése érdekében"
        pretty: "Oszlopszélességek automatikus igazítása szebb táblázat formátum generálásához"
        simple: "Egyszerűsített szintaxis használata, külső szegély függőleges vonalak elhagyásával"
        boldFirstRow: "Az első sor szövegének félkövérré tétele"
        boldFirstColumn: "Az első oszlop szövegének félkövérré tétele"
        firstHeader: "Az első sor fejlécként való kezelése és elválasztó vonal hozzáadása"
        textAlign: "Oszlop szöveg igazítás beállítása: bal, közép, jobb"
        multilineHandling: "Többsoros szöveg kezelés: sortörések megőrzése, escape \\n-re, &lt;br&gt; címkék használata"

        includeLineNumbers: "Adjon sorszám oszlopot a táblázat bal oldalára"
      magic:
        builtin: "Válasszon előre meghatározott közös sablon formátumokat"
        rowsTpl: "<table> <tr> <th>Mágikus szintaxis</th> <th>Leírás</th> <th>Támogatott JS metódusok</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>1., 2. ... <b>f</b>ejléc mező, azaz {hA} {hB} ...</td> <td>Karakterlánc metódusok</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>1., 2. ... aktuális sor mező, azaz {$A} {$B} ...</td> <td>Karakterlánc metódusok</td> </tr> <tr> <td>{F,} {F;}</td> <td>Az aktuális sor felosztása az <b>F</b> utáni karakterlánccal</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>Az aktuális <b>s</b>or <b>s</b>záma 1-től vagy 100-tól</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>S</b>orok <b>v</b>égső száma </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>JavaScript kód <b>v</b>égrehajtása, pl.: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Használjon fordított perjelet <b>\\</b> a kapcsos zárójelek {...} kiírásához </td> <td></td> </tr></table>"
        headerTpl: "Egyéni kimeneti sablon a fejléc szakaszhoz"
        footerTpl: "Egyéni kimeneti sablon a lábléc szakaszhoz"
      textile:
        escape: "Escape Textile szintaxis karakterek (|, ., -, ^) a formátum konfliktusok elkerülése érdekében"
        rowHeader: "Állítsa be az első sort fejléc sorként"
        thead: "Add Textile syntax markers for table head and body"
      xml:
        escape: "Escape XML special characters (&lt;, &gt;, &amp;, \", ') to ensure valid XML"
        minify: "Generáljon tömörített XML kimenetet, eltávolítva a felesleges szóközöket"
        rootElement: "Set XML root element tag name"
        rowElement: "Set XML element tag name for each row of data"
        declaration: "XML deklarációs fejléc hozzáadása (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Adatok kimenete XML attribútumokként gyermek elemek helyett"
        cdata: "Szöveges tartalom CDATA-val való becsomagolása speciális karakterek védelme érdekében"
        encoding: "Karakterkódolási formátum beállítása XML dokumentumhoz"
        indentation: "XML behúzási karakter választása: szóközök vagy tabulátorok"
      yaml:
        indentSize: "Szóközök számának beállítása YAML hierarchia behúzáshoz (általában 2 vagy 4)"
        arrayStyle: "Tömb formátum: blokk (egy elem soronként) vagy folyam (inline formátum)"
        quotationStyle: "Karakterlánc idézőjel stílus: nincs idézőjel, egyszeres idézőjel, dupla idézőjel"
      csv:
        bom: "UTF-8 byte order mark hozzáadása az Excel és más szoftverek kódolás felismerésének segítéséhez"
      excel:
        autoWidth: "Oszlopszélesség automatikus beállítása tartalom alapján"
        protectSheet: "Munkalap védelem engedélyezése jelszóval: tableconvert.com"
      sql:
        primaryKey: "Elsődleges kulcs mező nevének megadása CREATE TABLE utasításhoz"
        dialect: "Adatbázis típus kiválasztása, befolyásolja az idézőjel és adattípus szintaxist"
      ascii:
        forceSep: "Kényszerített elválasztó vonalak minden adatsor között"
        style: "ASCII táblázat szegély rajzolási stílus kiválasztása"
        comment: "Megjegyzés jelölők hozzáadása a teljes táblázat körbezárásához"
      mediawiki:
        minify: "Kimeneti kód tömörítése, felesleges szóközök eltávolítása"
        header: "Első sor megjelölése fejléc stílusként"
        sort: "Táblázat kattintásos rendezési funkció engedélyezése"
      asciidoc:
        minify: "AsciiDoc formátum kimenet tömörítése"
        firstHeader: "Első sor beállítása fejléc sorként"
        lastFooter: "Utolsó sor beállítása lábléc sorként"
        title: "Cím szöveg hozzáadása a táblázathoz"
      tracwiki:
        rowHeader: "Első sor beállítása fejlécként"
        colHeader: "Első oszlop beállítása fejlécként"
      bbcode:
        minify: "BBCode kimeneti formátum tömörítése"
      restructuredtext:
        style: "reStructuredText táblázat szegély stílus kiválasztása"
        forceSep: "Kényszerített elválasztó vonalak"
      pdf:
        theme: "PDF táblázat vizuális stílus kiválasztása professzionális dokumentumokhoz"
        headerColor: "Fejléc háttérszín kiválasztása PDF táblázatokhoz"
        showHead: "Fejléc megjelenítés vezérlése PDF oldalakon"
        docTitle: "Opcionális cím a PDF dokumentumhoz"
        docDescription: "Opcionális leírás szöveg a PDF dokumentumhoz"
    label:
      ascii:
        forceSep: "Sorelválasztók"
        style: "Szegély stílus"
        comment: "Megjegyzés burkoló"
      restructuredtext:
        style: "Szegély stílus"
        forceSep: "Kényszerített elválasztók"
      bbcode:
        minify: "Kimenet minimalizálása"
      csv:
        doubleQuote: "Dupla idézőjel burkolás"
        delimiter: "Mező elválasztó"
        bom: "UTF-8 BOM"
        valueDelimiter: "Érték elválasztó"
        rowDelimiter: "Sor elválasztó"
        prefix: "Sor előtag"
        suffix: "Sor utótag"
      excel:
        autoWidth: "Automatikus szélesség"
        textFormat: "Szöveg formátum"
        protectSheet: "Munkalap védelme"
        boldFirstRow: "Első sor félkövér"
        boldFirstColumn: "Első oszlop félkövér"
        sheetName: "Munkalap neve"
      html:
        escape: "HTML karakterek escape-elése"
        div: "DIV táblázat"
        minify: "Kód minifikálása"
        thead: "Táblázat fejléc struktúra"
        tableCaption: "Táblázat felirat"
        tableClass: "Táblázat osztály"
        tableId: "Táblázat azonosító"
        rowHeader: "Sor fejléc"
        colHeader: "Oszlop fejléc"
      jira:
        escape: "Karakterek escape-elése"
        rowHeader: "Sor fejléc"
        colHeader: "Oszlop fejléc"
      json:
        parsingJSON: "JSON elemzése"
        minify: "Kimenet minimalizálása"
        format: "Adat formátum"
        rootName: "Gyökér objektum neve"
        indentSize: "Behúzás mérete"
      jsonlines:
        parsingJSON: "JSON elemzése"
        format: "Adat formátum"
      latex:
        escape: "LaTeX táblázat karakterek escape-elése"
        ht: "Lebegő pozíció"
        mwe: "Teljes dokumentum"
        tableAlign: "Táblázat igazítás"
        tableBorder: "Szegély stílus"
        label: "Hivatkozási címke"
        caption: "Táblázat felirat"
        location: "Felirat pozíció"
        tableType: "Táblázat típus"
        boldFirstRow: "Első sor félkövér"
        boldFirstColumn: "Első oszlop félkövér"
        textAlign: "Szöveg igazítás"
        borders: "Szegély beállítások"
      markdown:
        escape: "Karakterek escape-elése"
        pretty: "Szép Markdown táblázat"
        simple: "Egyszerű Markdown formátum"
        boldFirstRow: "Első sor félkövér"
        boldFirstColumn: "Első oszlop félkövér"
        firstHeader: "Első fejléc"
        textAlign: "Szöveg igazítás"
        multilineHandling: "Többsoros kezelés"

        includeLineNumbers: "Sorszámok hozzáadása"
        align: "Igazítás"
      mediawiki:
        minify: "Kód minimalizálása"
        header: "Fejléc jelölés"
        sort: "Rendezhető"
      asciidoc:
        minify: "Formátum minifikálása"
        firstHeader: "Első fejléc"
        lastFooter: "Utolsó lábléc"
        title: "Táblázat címe"
      tracwiki:
        rowHeader: "Sor fejléc"
        colHeader: "Oszlop fejléc"
      sql:
        drop: "Táblázat eldobása (ha létezik)"
        create: "Táblázat létrehozása"
        oneInsert: "Kötegelt beszúrás"
        table: "Táblázat neve"
        dialect: "Adatbázis típus"
        primaryKey: "Elsődleges kulcs"
      magic:
        builtin: "Beépített sablon"
        rowsTpl: "Sor sablon, szintaxis ->"
        headerTpl: "Fejléc sablon"
        footerTpl: "Lábléc sablon"
      textile:
        escape: "Karakterek escape-elése"
        rowHeader: "Sor fejléc"
        thead: "Táblázat fejléc szintaxis"
      xml:
        escape: "XML karakterek escape-elése"
        minify: "Kimenet minimalizálása"
        rootElement: "Gyökér elem"
        rowElement: "Sor elem"
        declaration: "XML deklaráció"
        attributes: "Attribútum mód"
        cdata: "CDATA burkoló"
        encoding: "Kódolás"
        indentSize: "Behúzás mérete"
      yaml:
        indentSize: "Behúzás mérete"
        arrayStyle: "Tömb stílus"
        quotationStyle: "Idézőjel stílus"
      pdf:
        theme: "PDF táblázat téma"
        headerColor: "PDF fejléc szín"
        showHead: "PDF fejléc megjelenítés"
        docTitle: "PDF dokumentum cím"
        docDescription: "PDF dokumentum leírás"

sidebar:
  all: "Összes Konverziós Eszköz"
  dataSource:
    title: "Adatforrás"
    description:
      converter: "Importáljon %s konverzióhoz %s formátumba. Támogatja a fájlfeltöltést, online szerkesztést és webadat-kinyerést."
      generator: "Hozzon létre táblázatadatokat több beviteli módszer támogatásával, beleértve a kézi bevitelt, fájlimportot és sablongenerálást."
  tableEditor:
    title: "Online Táblázatszerkesztő"
    description:
      converter: "Dolgozza fel a %s-t online táblázatszerkesztőnkkel. Excel-szerű működési élmény üres sorok törlésének, deduplikálásnak, rendezésnek és keresés és csere funkciónak a támogatásával."
      generator: "Hatékony online táblázatszerkesztő, amely Excel-szerű működési élményt nyújt. Támogatja az üres sorok törlését, deduplikálást, rendezést és keresés és csere funkciót."
  tableGenerator:
    title: "Táblázatgenerátor"
    description:
      converter: "Gyorsan generáljon %s-t a táblázatgenerátor valós idejű előnézetével. Gazdag exportálási lehetőségek, egy kattintásos másolás és letöltés."
      generator: "Exportáljon %s adatokat több formátumban különböző használati forgatókönyvek kielégítésére. Támogatja az egyéni beállításokat és valós idejű előnézetet."
footer:
  changelog: "Változásnapló"
  sponsor: "Szponzorok"
  contact: "Kapcsolat"
  privacyPolicy: "Adatvédelmi Irányelvek"
  about: "Rólunk"
  resources: "Források"
  popularConverters: "Népszerű Konverterek"
  popularGenerators: "Népszerű Generátorok"
  dataSecurity: "Az adatai biztonságban vannak - minden konverzió a böngészőjében fut."
converters:
  Markdown:
    alias: "Markdown Táblázat"
    what: "A Markdown egy könnyű jelölőnyelv, amelyet széles körben használnak technikai dokumentációhoz, blog tartalom létrehozásához és webfejlesztéshez. A táblázat szintaxisa tömör és intuitív, támogatja a szöveg igazítását, link beágyazást és formázást. Ez a programozók és technikai írók előnyben részesített eszköze, tökéletesen kompatibilis a GitHub, GitLab és más kódtároló platformokkal."
    step1: "Illessze be a Markdown táblázat adatokat az adatforrás területre, vagy közvetlenül húzza át és ejtse le a .md fájlokat feltöltéshez. Az eszköz automatikusan elemzi a táblázat szerkezetét és formázását, támogatja a komplex beágyazott tartalmat és speciális karakterek kezelését."
    step3: "Valós időben generáljon szabványos Markdown táblázat kódot, támogatva több igazítási módszert, szöveg félkövérítést, sorszám hozzáadást és egyéb fejlett formátum beállításokat. A generált kód teljesen kompatibilis a GitHub-bal és a főbb Markdown szerkesztőkkel, egy kattintással másolható használatra kész."
    from_alias: "Markdown tábla fájl"
    to_alias: "Markdown tábla formátum"
  Magic:
    alias: "Egyéni Sablon"
    what: "A Magic sablon ennek az eszköznek egy egyedülálló fejlett adatgenerátora, amely lehetővé teszi a felhasználók számára, hogy tetszőleges formátumú adatkimenetet hozzanak létre egyéni sablon szintaxis segítségével. Támogatja a változó helyettesítést, feltételes értékelést és ciklus feldolgozást. Ez a végső megoldás komplex adatkonverziós igények és személyre szabott kimeneti formátumok kezelésére, különösen alkalmas fejlesztők és adatmérnökök számára."
    step1: "Válasszon beépített közös sablonokat vagy hozzon létre egyéni sablon szintaxist. Gazdag változókat és funkciókat támogat, amelyek képesek kezelni komplex adatstruktúrákat és üzleti logikát."
    step3: "Generáljon olyan adatkimenetet, amely teljes mértékben megfelel az egyéni formátum követelményeinek. Támogatja a komplex adatkonverziós logikát és feltételes feldolgozást, jelentősen javítva az adatfeldolgozás hatékonyságát és a kimenet minőségét. Hatékony eszköz kötegelt adatfeldolgozáshoz."
    from_alias: "Táblázat adat"
    to_alias: "Egyéni formátum kimenet"
  CSV:
    alias: "CSV"
    what: "A CSV (Comma-Separated Values) a legszélesebb körben használt adatcsere formátum, tökéletesen támogatott az Excel, Google Sheets, adatbázis rendszerek és különféle adatelemző eszközök által. Egyszerű szerkezete és erős kompatibilitása teszi az adatmigráció, kötegelt import/export és platformok közötti adatcsere szabványos formátumává, széles körben használt üzleti elemzésben, adattudományban és rendszerintegrációban."
    step1: "Töltsön fel CSV fájlokat vagy közvetlenül illessze be a CSV adatokat. Az eszköz intelligensen felismeri a különféle elválasztókat (vessző, tabulátor, pontosvessző, pipe stb.), automatikusan észleli az adattípusokat és kódolási formátumokat, támogatja a nagy fájlok gyors elemzését és komplex adatstruktúrákat."
    step3: "Generáljon szabványos CSV formátumú fájlokat egyéni elválasztók, idézőjel stílusok, kódolási formátumok és BOM jelölés beállítások támogatásával. Biztosítja a tökéletes kompatibilitást a célrendszerekkel, letöltési és tömörítési lehetőségeket biztosít a vállalati szintű adatfeldolgozási igények kielégítésére."
    from_alias: "CSV adatfájl"
    to_alias: "Szabványos CSV formátum"
  JSON:
    alias: "JSON Tömb"
    what: "A JSON (JavaScript Object Notation) a modern webalkalmazások, REST API-k és mikroszolgáltatás architektúrák szabványos táblázat adatformátuma. Világos szerkezete és hatékony elemzése széles körben használttá teszi a frontend és backend adatinterakcióban, konfigurációs fájlok tárolásában és NoSQL adatbázisokban. Támogatja a beágyazott objektumokat, tömb struktúrákat és több adattípust, nélkülözhetetlenné téve a modern szoftverfejlesztés táblázat adatai számára."
    step1: "Töltsön fel JSON fájlokat vagy illessze be JSON tömböket. Támogatja az objektum tömbök, beágyazott struktúrák és komplex adattípusok automatikus felismerését és elemzését. Az eszköz intelligensen validálja a JSON szintaxist és hibaüzeneteket biztosít."
    step3: "Generáljon több JSON formátumú kimenetet: szabványos objektum tömbök, 2D tömbök, oszlop tömbök és kulcs-érték pár formátumok. Támogatja a szépített kimenetet, tömörítési módot, egyéni gyökér objektum neveket és behúzás beállításokat, tökéletesen alkalmazkodva különféle API interfészekhez és adattárolási igényekhez."
    from_alias: "JSON tömb fájl"
    to_alias: "Szabványos JSON formátum"
  JSONLines:
    alias: "JSONLines Formátum"
    what: "A JSON Lines (más néven NDJSON) egy fontos formátum a nagy adatok feldolgozásához és streaming adatátvitelhez, ahol minden sor egy független JSON objektumot tartalmaz. Széles körben használt log elemzésben, adatfolyam feldolgozásban, gépi tanulásban és elosztott rendszerekben. Támogatja az inkrementális feldolgozást és párhuzamos számítást, így ideális választás a nagy léptékű strukturált adatok kezeléséhez."
    step1: "Töltsön fel JSONLines fájlokat vagy illessze be az adatokat. Az eszköz soronként elemzi a JSON objektumokat, támogatja a nagy fájlok streaming feldolgozását és a hibás sorok átugrását."
    step3: "Generáljon szabványos JSONLines formátumot, ahol minden sor egy teljes JSON objektumot ad ki. Alkalmas streaming feldolgozásra, kötegelt importra és nagy adatok elemzési forgatókönyveire, támogatja az adatvalidációt és formátum optimalizálást."
    from_alias: "JSONLines adat"
    to_alias: "JSONLines streaming formátum"
  XML:
    alias: "XML"
    what: "Az XML (eXtensible Markup Language) a vállalati szintű adatcsere és konfigurációkezelés szabványos formátuma, szigorú szintaxis specifikációkkal és erőteljes validációs mechanizmusokkal. Széles körben használt webszolgáltatásokban, konfigurációs fájlokban, dokumentum tárolásban és rendszerintegrációban. Támogatja a névtereket, séma validációt és XSLT transzformációt, fontos táblázat adattá téve a vállalati alkalmazások számára."
    step1: "Töltsön fel XML fájlokat vagy illessze be XML adatokat. Az eszköz automatikusan elemzi az XML struktúrát és táblázat formátumra konvertálja, támogatja a névtér, attribútum kezelést és komplex beágyazott struktúrákat."
    step3: "Generáljon XML kimenetet, amely megfelel az XML szabványoknak. Támogatja az egyéni gyökér elemeket, sor elem neveket, attribútum módokat, CDATA csomagolást és karakter kódolási beállításokat. Biztosítja az adatok integritását és kompatibilitását, megfelelve a vállalati szintű alkalmazási követelményeknek."
    from_alias: "XML adatfájl"
    to_alias: "Szabványos XML formátum"
  YAML:
    alias: "YAML Konfiguráció"
    what: "A YAML egy ember-barát adatszerializációs szabvány, híres világos hierarchikus struktúrájáról és tömör szintaxisáról. Széles körben használt konfigurációs fájlokban, DevOps eszközláncokban, Docker Compose-ban és Kubernetes telepítésekben. Erős olvashatósága és tömör szintaxisa fontos konfigurációs formátummá teszi a modern felhő-natív alkalmazások és automatizált műveletek számára."
    step1: "Töltsön fel YAML fájlokat vagy illessze be YAML adatokat. Az eszköz intelligensen elemzi a YAML struktúrát és validálja a szintaxis helyességét, támogatja a multi-dokumentum formátumokat és komplex adattípusokat."
    step3: "Generáljon szabványos YAML formátum kimenetet blokk és folyam tömb stílusok támogatásával, több idézőjel beállítással, egyéni behúzással és megjegyzés megőrzéssel. Biztosítja, hogy a kimeneti YAML fájlok teljesen kompatibilisek legyenek különféle parserekkel és konfigurációs rendszerekkel."
    from_alias: "YAML konfigurációs fájl"
    to_alias: "Szabványos YAML formátum"
  MySQL:
      alias: "MySQL Lekérdezés Eredmények"
      what: "A MySQL a világ legnépszerűbb nyílt forráskódú relációs adatbázis-kezelő rendszere, híres magas teljesítményéről, megbízhatóságáról és könnyű használatáról. Széles körben használt webalkalmazásokban, vállalati rendszerekben és adatelemzési platformokon. A MySQL lekérdezés eredmények jellemzően strukturált táblázat adatokat tartalmaznak, fontos adatforrásként szolgálva az adatbázis-kezelésben és adatelemzési munkában."
      step1: "Illessze be a MySQL lekérdezés kimeneti eredményeket az adatforrás területre. Az eszköz automatikusan felismeri és elemzi a MySQL parancssori kimeneti formátumot, támogatja a különféle lekérdezés eredmény stílusokat és karakter kódolásokat, intelligensen kezeli a fejléceket és adatsorokat."
      step3: "Gyorsan konvertálja a MySQL lekérdezés eredményeket több táblázat adatformátumra, megkönnyítve az adatelemzést, jelentés generálást, rendszerek közötti adatmigrációt és adatvalidációt. Praktikus eszköz adatbázis adminisztrátorok és adatelemzők számára."
      from_alias: "MySQL lekérdezés kimenet"
      to_alias: "MySQL táblázat adat"
  SQL:
    alias: "Insert SQL"
    what: "Az SQL (Structured Query Language) a relációs adatbázisok szabványos műveleti nyelve, adatlekérdezéshez, beszúráshoz, frissítéshez és törlési műveletekhez használt. Az adatbázis-kezelés alapvető technológiájaként az SQL széles körben használt adatelemzésben, üzleti intelligenciában, ETL feldolgozásban és adattárház építésben. Nélkülözhetetlen készségeszköz az adatszakemberek számára."
    step1: "Illessze be INSERT SQL utasításokat vagy töltsön fel .sql fájlokat. Az eszköz intelligensen elemzi az SQL szintaxist és kivonja a táblázat adatokat, támogatja a több SQL dialektust és komplex lekérdezési utasítás feldolgozást."
    step3: "Generáljon szabványos SQL INSERT utasításokat és táblázat létrehozási utasításokat. Támogatja a több adatbázis dialektust (MySQL, PostgreSQL, SQLite, SQL Server, Oracle), automatikusan kezeli az adattípus leképezést, karakter escapelést és elsődleges kulcs megszorításokat. Biztosítja, hogy a generált SQL kód közvetlenül végrehajtható legyen."
    from_alias: "Insert SQL"
    to_alias: "SQL utasítás"
  Qlik:
      alias: "Qlik Táblázat"
      what: "A Qlik egy szoftverszállító, amely adatvizualizációra, vezetői irányítópultokra és önkiszolgáló üzleti intelligencia termékekre specializálódott, a Tableau és a Microsoft mellett."
      step1: ""
      step3: "Végül a [Táblázat Generátor](#TableGenerator) megjeleníti a konverziós eredményeket. Használja a Qlik Sense, Qlik AutoML, QlikView vagy más Qlik-kompatibilis szoftverében."
      from_alias: "Qlik táblázat"
      to_alias: "Qlik táblázat"
  DAX:
      alias: "DAX Táblázat"
      what: "A DAX (Data Analysis Expressions) egy programozási nyelv, amelyet a Microsoft Power BI-ban használnak számított oszlopok, mértékek és egyéni táblázatok létrehozására."
      step1: ""
      step3: "Végül a [Táblázat Generátor](#TableGenerator) megjeleníti a konverziós eredményeket. Ahogy várható, több Microsoft termékben is használatos, beleértve a Microsoft Power BI-t, a Microsoft Analysis Services-t és a Microsoft Power Pivot for Excel-t."
      from_alias: "DAX táblázat"
      to_alias: "DAX táblázat"
  Firebase:
    alias: "Firebase Lista"
    what: "A Firebase egy BaaS alkalmazásfejlesztési platform, amely hosztolt backend szolgáltatásokat nyújt, mint például valós idejű adatbázis, felhő tárolás, hitelesítés, összeomlás jelentés stb."
    step1: ""
    step3: "Végül a [Táblázat Generátor](#TableGenerator) megjeleníti a konverziós eredményeket. Ezután használhatja a push metódust a Firebase API-ban, hogy hozzáadjon a Firebase adatbázisban lévő adatok listájához."
    from_alias: "Firebase lista"
    to_alias: "Firebase lista"
  HTML:
    alias: "HTML Táblázat"
    what: "A HTML táblázatok a strukturált adatok megjelenítésének szabványos módja a weboldalakban, table, tr, td és más tagekkel építve. Támogatja a gazdag stílus testreszabást, reszponzív elrendezést és interaktív funkcionalitást. Széles körben használt weboldal fejlesztésben, adatok megjelenítésében és jelentés generálásban, a frontend fejlesztés és webdesign fontos komponenseként szolgál."
    step1: "Illessze be a táblázatokat tartalmazó HTML kódot vagy töltsön fel HTML fájlokat. Az eszköz automatikusan felismeri és kivonja a táblázat adatokat az oldalakról, támogatja a komplex HTML struktúrákat, CSS stílusokat és beágyazott táblázat feldolgozást."
    step3: "Generáljon szemantikus HTML táblázat kódot thead/tbody struktúra, CSS osztály beállítások, táblázat feliratok, sor/oszlop fejlécek és reszponzív attribútum konfiguráció támogatásával. Biztosítja, hogy a generált táblázat kód megfelel a webes szabványoknak jó hozzáférhetőséggel és SEO barátságossággal."
    from_alias: "HTML táblázat"
    to_alias: "HTML táblázat"
  Excel:
    alias: "Excel"
    what: "A Microsoft Excel a világ legnépszerűbb táblázatkezelő szoftvere, széles körben használt üzleti elemzésben, pénzügyi menedzsmentben, adatfeldolgozásban és jelentés készítésben. Hatékony adatfeldolgozási képességei, gazdag függvénykönyvtára és rugalmas vizualizációs funkciói teszik a irodai automatizálás és adatelemzés szabványos eszközévé, széleskörű alkalmazásokkal szinte minden iparágban és területen."
    step1: "Töltsön fel Excel fájlokat (.xlsx, .xls formátumokat támogat) vagy másolja át a táblázat adatokat közvetlenül az Excel-ből és illessze be. Az eszköz támogatja a több munkalap feldolgozást, komplex formátum felismerést és nagy fájlok gyors elemzését, automatikusan kezeli az egyesített cellákat és adattípusokat."
    step3: "Generáljon Excel-kompatibilis táblázat adatokat, amelyek közvetlenül beilleszthetők az Excel-be vagy letölthetők szabványos .xlsx fájlokként. Támogatja a munkalap elnevezést, cella formázást, automatikus oszlopszélességet, fejléc stílust és adatvalidációs beállításokat. Biztosítja, hogy a kimeneti Excel fájlok professzionális megjelenéssel és teljes funkcionalitással rendelkezzenek."
    from_alias: "Excel táblázat"
    to_alias: "Excel"
  LaTeX:
    alias: "LaTeX Táblázat"
    what: "A LaTeX egy professzionális dokumentum szedőrendszer, különösen alkalmas tudományos dolgozatok, technikai dokumentumok és tudományos publikációk készítésére. A táblázat funkcionalitása erőteljes, támogatja a komplex matematikai képleteket, precíz elrendezés vezérlést és magas minőségű PDF kimenetet. Ez a szabványos eszköz az akadémiában és tudományos kiadásban, széles körben használt folyóirat cikkekben, disszertációkban és technikai kézikönyv szedésben."
    step1: "Illessze be a LaTeX táblázat kódot vagy töltsön fel .tex fájlokat. Az eszköz elemzi a LaTeX táblázat szintaxist és kivonja az adattartalmat, támogatja a több táblázat környezetet (tabular, longtable, array stb.) és komplex formátum parancsokat."
    step3: "Generáljon professzionális LaTeX táblázat kódot több táblázat környezet kiválasztás, szegély stílus konfiguráció, felirat pozíció beállítások, dokumentum osztály specifikáció és csomag kezelés támogatásával. Képes teljes fordítható LaTeX dokumentumokat generálni, biztosítva hogy a kimeneti táblázatok megfeleljenek az akadémiai kiadási szabványoknak."
    from_alias: "LaTeX táblázat"
    to_alias: "LaTeX táblázat"
  ASCII:
    alias: "ASCII Szöveges Táblázat"
    what: "Az ASCII táblázatok egyszerű szöveges karaktereket használnak a táblázat szegélyek és struktúrák rajzolásához, biztosítva a legjobb kompatibilitást és hordozhatóságot. Kompatibilis minden szövegszerkesztővel, terminál környezettel és operációs rendszerrel. Széles körben használt kód dokumentációban, technikai kézikönyvekben, README fájlokban és parancssori eszköz kimenetekben. A programozók és rendszeradminisztrátorok előnyben részesített adatmegjelenítési formátuma."
    step1: "Töltsön fel ASCII táblázatokat tartalmazó szöveges fájlokat vagy közvetlenül illessze be a táblázat adatokat. Az eszköz intelligensen felismeri és elemzi az ASCII táblázat struktúrákat, támogatja a több szegély stílust és igazítási formátumokat."
    step3: "Generáljon gyönyörű egyszerű szöveges ASCII táblázatokat több szegély stílus (egyszeres vonal, dupla vonal, lekerekített sarkok stb.), szöveg igazítási módszerek és automatikus oszlopszélesség támogatásával. A generált táblázatok tökéletesen megjelennek kódszerkesztőkben, dokumentumokban és parancssorokban."
    from_alias: "ASCII szöveges táblázat"
    to_alias: "ASCII szöveges táblázat"
  MediaWiki:
    alias: "MediaWiki Táblázat"
    what: "A MediaWiki a nyílt forráskódú szoftver platform, amelyet híres wiki oldalak használnak, mint a Wikipedia. A táblázat szintaxisa tömör, mégis erőteljes, támogatja a táblázat stílus testreszabást, rendezési funkcionalitást és link beágyazást. Széles körben használt tudásmenedzsmentben, együttműködő szerkesztésben és tartalomkezelő rendszerekben, a wiki enciklopédiák és tudásbázisok építésének alapvető technológiájaként szolgál."
    step1: "Illessze be a MediaWiki táblázat kódot vagy töltsön fel wiki forrás fájlokat. Az eszköz elemzi a wiki markup szintaxist és kivonja a táblázat adatokat, támogatja a komplex wiki szintaxist és sablon feldolgozást."
    step3: "Generáljon szabványos MediaWiki táblázat kódot fejléc stílus beállítások, cella igazítás, rendezési funkcionalitás engedélyezés és kód tömörítési opciók támogatásával. A generált kód közvetlenül használható wiki oldal szerkesztéshez, biztosítva a tökéletes megjelenést MediaWiki platformokon."
    from_alias: "MediaWiki táblázat"
    to_alias: "MediaWiki táblázat"
  TracWiki:
    alias: "TracWiki Táblázat"
    what: "A Trac egy webalapú projektmenedzsment és hibakövető rendszer, amely egyszerűsített wiki szintaxist használ táblázat tartalom létrehozásához."
    step1: "Töltsön fel TracWiki fájlokat vagy illessze be a táblázat adatokat."
    step3: "Generáljon TracWiki-kompatibilis táblázat kódot sor/oszlop fejléc beállítások támogatásával, megkönnyítve a projekt dokumentum kezelést."
    from_alias: "TracWiki táblázat"
    to_alias: "TracWiki táblázat"
  AsciiDoc:
    alias: "AsciiDoc Táblázat"
    what: "Az AsciiDoc egy könnyű jelölőnyelv, amely HTML-re, PDF-re, kézikönyv oldalakra és más formátumokra konvertálható, széles körben használt technikai dokumentáció íráshoz."
    step1: "Töltsön fel AsciiDoc fájlokat vagy illessze be az adatokat."
    step3: "Generáljon AsciiDoc táblázat szintaxist fejléc, lábléc és cím beállítások támogatásával, közvetlenül használható AsciiDoc szerkesztőkben."
    from_alias: "AsciiDoc táblázat"
    to_alias: "AsciiDoc táblázat"
  reStructuredText:
    alias: "reStructuredText Táblázat"
    what: "A reStructuredText a Python közösség szabványos dokumentációs formátuma, gazdag táblázat szintaxist támogat, általában Sphinx dokumentáció generáláshoz használt."
    step1: "Töltsön fel .rst fájlokat vagy illessze be a reStructuredText adatokat."
    step3: "Generáljon szabványos reStructuredText táblázatokat több szegély stílus támogatásával, közvetlenül használható Sphinx dokumentációs projektekben."
    from_alias: "reStructuredText táblázat"
    to_alias: "reStructuredText táblázat"
  PHP:
    alias: "PHP Tömb"
    what: "A PHP egy népszerű szerver-oldali szkriptnyelv, a tömbök a központi adatstruktúrája, széles körben használt webfejlesztésben és adatfeldolgozásban."
    step1: "Töltsön fel PHP tömböket tartalmazó fájlokat vagy közvetlenül illessze be az adatokat."
    step3: "Generáljon szabványos PHP tömb kódot, amely közvetlenül használható PHP projektekben, támogatja az asszociatív és indexelt tömb formátumokat."
    from_alias: "PHP tömb"
    to_alias: "PHP kód"
  Ruby:
    alias: "Ruby Tömb"
    what: "A Ruby egy dinamikus objektumorientált programozási nyelv tömör és elegáns szintaxissal, a tömbök fontos adatstruktúra."
    step1: "Töltsön fel Ruby fájlokat vagy illessze be a tömb adatokat."
    step3: "Generáljon Ruby tömb kódot, amely megfelel a Ruby szintaxis specifikációknak, közvetlenül használható Ruby projektekben."
    from_alias: "Ruby tömb"
    to_alias: "Ruby kód"
  ASP:
    alias: "ASP Tömb"
    what: "Az ASP (Active Server Pages) a Microsoft szerver-oldali szkriptkörnyezete, több programozási nyelvet támogat dinamikus weboldalak fejlesztéséhez."
    step1: "Töltsön fel ASP fájlokat vagy illessze be a tömb adatokat."
    step3: "Generáljon ASP-kompatibilis tömb kódot VBScript és JScript szintaxis támogatással, használható ASP.NET projektekben."
    from_alias: "ASP tömb"
    to_alias: "ASP kód"
  ActionScript:
    alias: "ActionScript Tömb"
    what: "Az ActionScript egy objektumorientált programozási nyelv, elsősorban Adobe Flash és AIR alkalmazásfejlesztéshez használt."
    step1: "Töltsön fel .as fájlokat vagy illessze be az ActionScript adatokat."
    step3: "Generáljon ActionScript tömb kódot, amely megfelel az AS3 szintaxis szabványoknak, használható Flash és Flex projekt fejlesztéshez."
    from_alias: "ActionScript tömb"
    to_alias: "ActionScript kód"
  BBCode:
    alias: "BBCode Táblázat"
    what: "A BBCode egy könnyű jelölőnyelv, amelyet általában fórumokon és online közösségekben használnak, egyszerű formázási funkcionalitást biztosít, beleértve a táblázat támogatást."
    step1: "Töltsön fel BBCode-ot tartalmazó fájlokat vagy illessze be az adatokat."
    step3: "Generáljon BBCode táblázat kódot, amely alkalmas fórum posztolásra és közösségi tartalom létrehozásra, tömörített kimeneti formátum támogatással."
    from_alias: "BBCode táblázat"
    to_alias: "BBCode táblázat"
  PDF:
    alias: "PDF Táblázat"
    what: "A PDF (Portable Document Format) egy platformfüggetlen dokumentum szabvány rögzített elrendezéssel, konzisztens megjelenítéssel és magas minőségű nyomtatási jellemzőkkel. Széles körben használt formális dokumentumokban, jelentésekben, számlákban, szerződésekben és tudományos dolgozatokban. Az előnyben részesített formátum üzleti kommunikációhoz és dokumentum archiváláshoz, biztosítva a teljesen konzisztens vizuális hatásokat különböző eszközökön és operációs rendszereken."
    step1: "Importáljon táblázat adatokat bármilyen formátumban. Az eszköz automatikusan elemzi az adatstruktúrát és intelligens elrendezés tervezést hajt végre, támogatja a nagy táblázatok automatikus oldalszámozását és komplex adattípus feldolgozást."
    step3: "Generáljon magas minőségű PDF táblázat fájlokat több professzionális téma stílus (üzleti, akadémiai, minimalista stb.), többnyelvű betűtípusok, automatikus oldalszámozás, vízjel hozzáadás és nyomtatás optimalizálás támogatásával. Biztosítja, hogy a kimeneti PDF dokumentumok professzionális megjelenéssel rendelkezzenek, közvetlenül használhatók üzleti prezentációkhoz és formális publikáláshoz."
    from_alias: "Táblázat adat"
    to_alias: "PDF táblázat"
  JPEG:
    alias: "JPEG Kép"
    what: "A JPEG a legszélesebb körben használt digitális képformátum kiváló tömörítési hatásokkal és széles kompatibilitással. Kis fájlmérete és gyors betöltési sebessége alkalmassá teszi webes megjelenítésre, közösségi média megosztásra, dokumentum illusztrációkra és online prezentációkra. A szabványos képformátum digitális médiához és hálózati kommunikációhoz, tökéletesen támogatott szinte minden eszköz és szoftver által."
    step1: "Importáljon táblázat adatokat bármilyen formátumban. Az eszköz intelligens elrendezés tervezést és vizuális optimalizálást hajt végre, automatikusan kiszámítja az optimális méretet és felbontást."
    step3: "Generáljon nagy felbontású JPEG táblázat képeket több téma színséma (világos, sötét, szembarát stb.), adaptív elrendezés, szöveg tisztaság optimalizálás és méret testreszabás támogatásával. Alkalmas online megosztásra, dokumentum beszúrásra és prezentáció használatra, biztosítva kiváló vizuális hatásokat különböző megjelenítő eszközökön."
    from_alias: "Táblázat adat"
    to_alias: "JPEG kép"
  Jira:
    alias: "Jira Táblázat"
    what: "A JIRA professzionális projektmenedzsment és hibakövető szoftver, amelyet az Atlassian fejlesztett ki, széles körben használt agilis fejlesztésben, szoftver tesztelésben és projekt együttműködésben. A táblázat funkcionalitása gazdag formázási lehetőségeket és adatmegjelenítést támogat, fontos eszközként szolgál szoftverfejlesztő csapatok, projektmenedzserek és minőségbiztosítási személyzet számára követelmény kezelésben, hibakövető és előrehaladás jelentésben."
    step1: "Töltsön fel táblázat adatokat tartalmazó fájlokat vagy közvetlenül illessze be az adattartalmat. Az eszköz automatikusan feldolgozza a táblázat adatokat és speciális karakter escapelést."
    step3: "Generáljon JIRA platform-kompatibilis táblázat kódot fejléc stílus beállítások, cella igazítás, karakter escape feldolgozás és formátum optimalizálás támogatásával. A generált kód közvetlenül beilleszthető JIRA probléma leírásokba, megjegyzésekbe vagy wiki oldalakba, biztosítva a helyes megjelenítést és renderelést JIRA rendszerekben."
    from_alias: "Jira táblázat"
    to_alias: "Jira táblázat"
  Textile:
    alias: "Textile Táblázat"
    what: "A Textile egy tömör könnyű jelölőnyelv egyszerű és könnyen tanulható szintaxissal, széles körben használt tartalomkezelő rendszerekben, blog platformokon és fórum rendszerekben. A táblázat szintaxisa világos és intuitív, támogatja a gyors formázást és stílus beállításokat. Ideális eszköz tartalomkészítők és weboldal adminisztrátorok számára gyors dokumentum íráshoz és tartalom publikáláshoz."
    step1: "Töltsön fel Textile formátumú fájlokat vagy illessze be a táblázat adatokat. Az eszköz elemzi a Textile markup szintaxist és kivonja a táblázat tartalmat."
    step3: "Generáljon szabványos Textile táblázat szintaxist fejléc markup, cella igazítás, speciális karakter escapelés és formátum optimalizálás támogatásával. A generált kód közvetlenül használható CMS rendszerekben, blog platformokon és dokumentum rendszerekben, amelyek támogatják a Textile-t, biztosítva a helyes tartalom renderelést és megjelenítést."
    from_alias: "Textile táblázat"
    to_alias: "Textile táblázat"
  PNG:
    alias: "PNG Kép"
    what: "A PNG (Portable Network Graphics) egy veszteségmentes képformátum kiváló tömörítéssel és átlátszóság támogatással. Széles körben használt webdesignban, digitális grafikában és professzionális fotózásban. Magas minősége és széles kompatibilitása ideálissá teszi képernyőképekhez, logókhoz, diagramokhoz és bármilyen képhez, amely éles részleteket és átlátszó hátteret igényel."
    step1: "Importáljon táblázat adatokat bármilyen formátumban. Az eszköz intelligens elrendezés tervezést és vizuális optimalizálást hajt végre, automatikusan kiszámítja az optimális méretet és felbontást PNG kimenethez."
    step3: "Generáljon magas minőségű PNG táblázat képeket több téma színséma, átlátszó hátterek, adaptív elrendezés és szöveg tisztaság optimalizálás támogatásával. Tökéletes webes használatra, dokumentum beszúrásra és professzionális prezentációkra kiváló vizuális minőséggel."
    from_alias: ""
    to_alias: "PNG kép"
  TOML:
    alias: "TOML"
    what: "A TOML (Tom's Obvious, Minimal Language) egy konfigurációs fájl formátum, amely könnyen olvasható és írható. Egyértelműnek és egyszerűnek tervezve, széles körben használt modern szoftver projektekben konfigurációkezeléshez. Világos szintaxisa és erős típuskezelése kiváló választássá teszi alkalmazás beállításokhoz és projekt konfigurációs fájlokhoz."
    step1: "Töltsön fel TOML fájlokat vagy illessze be a konfigurációs adatokat. Az eszköz elemzi a TOML szintaxist és kivonja a strukturált konfigurációs információkat."
    step3: "Generáljon szabványos TOML formátumot beágyazott struktúrák, adattípusok és megjegyzések támogatásával. A generált TOML fájlok tökéletesek alkalmazás konfigurációhoz, build eszközökhöz és projekt beállításokhoz."
    from_alias: "TOML"
    to_alias: "TOML formátum"
  INI:
    alias: "INI"
    what: "Az INI fájlok egyszerű konfigurációs fájlok, amelyeket sok alkalmazás és operációs rendszer használ. Egyszerű kulcs-érték pár struktúrájuk könnyen olvashatóvá és manuálisan szerkeszthetővé teszi őket. Széles körben használt Windows alkalmazásokban, örökölt rendszerekben és egyszerű konfigurációs forgatókönyvekben, ahol az emberi olvashatóság fontos."
    step1: "Töltsön fel INI fájlokat vagy illessze be a konfigurációs adatokat. Az eszköz elemzi az INI szintaxist és kivonja a szekció-alapú konfigurációs információkat."
    step3: "Generáljon szabványos INI formátumot szekciók, megjegyzések és különféle adattípusok támogatásával. A generált INI fájlok kompatibilisek a legtöbb alkalmazással és konfigurációs rendszerrel."
    from_alias: "INI"
    to_alias: "INI formátum"
  Avro:
    alias: "Avro Séma"
    what: "Az Apache Avro egy adatszerializációs rendszer, amely gazdag adatstruktúrákat, kompakt bináris formátumot és séma evolúciós képességeket biztosít. Széles körben használt nagy adatok feldolgozásban, üzenet sorok és elosztott rendszerekben. A séma definíciója támogatja a komplex adattípusokat és verzió kompatibilitást, fontos eszközzé téve adatmérnökök és rendszerarchitektusok számára."
    step1: "Töltsön fel Avro séma fájlokat vagy illessze be az adatokat. Az eszköz elemzi az Avro séma definíciókat és kivonja a táblázat struktúra információkat."
    step3: "Generáljon szabványos Avro séma definíciókat adattípus leképezés, mező megszorítások és séma validáció támogatásával. A generált sémák közvetlenül használhatók Hadoop ökoszisztémákban, Kafka üzenet rendszerekben és más nagy adat platformokon."
    from_alias: "Avro séma"
    to_alias: "Avro séma"
  Protobuf:
    alias: "Protocol Buffers"
    what: "A Protocol Buffers (protobuf) a Google nyelvfüggetlen, platformfüggetlen, bővíthető mechanizmusa strukturált adatok szerializálásához. Széles körben használt mikroszolgáltatásokban, API fejlesztésben és adattárolásban. Hatékony bináris formátuma és erős típuskezelése ideálissá teszi nagy teljesítményű alkalmazásokhoz és nyelvek közötti kommunikációhoz."
    step1: "Töltsön fel .proto fájlokat vagy illessze be a Protocol Buffer definíciókat. Az eszköz elemzi a protobuf szintaxist és kivonja az üzenet struktúra információkat."
    step3: "Generáljon szabványos Protocol Buffer definíciókat üzenet típusok, mező opciók és szolgáltatás definíciók támogatásával. A generált .proto fájlok fordíthatók több programozási nyelvhez."
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf séma"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "A Pandas a legnépszerűbb adatelemző könyvtár Pythonban, a DataFrame a központi adatstruktúrája. Hatékony adatmanipulációs, tisztítási és elemzési képességeket biztosít, széles körben használt adattudományban, gépi tanulásban és üzleti intelligenciában. Nélkülözhetetlen eszköz Python fejlesztők és adatelemzők számára."
    step1: "Töltsön fel DataFrame kódot tartalmazó Python fájlokat vagy illessze be az adatokat. Az eszköz elemzi a Pandas szintaxist és kivonja a DataFrame struktúra információkat."
    step3: "Generáljon szabványos Pandas DataFrame kódot adattípus specifikációk, index beállítások és adatműveletek támogatásával. A generált kód közvetlenül végrehajtható Python környezetben adatelemzéshez és feldolgozáshoz."
    from_alias: "Pandas DataFrame"
    to_alias: "Pandas DataFrame"
  RDF:
    alias: "RDF Hármas"
    what: "Az RDF (Resource Description Framework) egy szabványos modell adatcseréhez a weben, amelyet úgy terveztek, hogy gráf formában reprezentálja az erőforrásokról szóló információkat. Széles körben használt szemantikus webben, tudásgráfokban és kapcsolt adat alkalmazásokban. Hármas struktúrája gazdag metaadat reprezentációt és szemantikus kapcsolatokat tesz lehetővé."
    step1: "Töltsön fel RDF fájlokat vagy illessze be a hármas adatokat. Az eszköz elemzi az RDF szintaxist és kivonja a szemantikus kapcsolatokat és erőforrás információkat."
    step3: "Generáljon szabványos RDF formátumot különféle szerializációk (RDF/XML, Turtle, N-Triples) támogatásával. A generált RDF használható szemantikus web alkalmazásokban, tudásbázisokban és kapcsolt adat rendszerekben."
    from_alias: "RDF"
    to_alias: "RDF hármas"
  MATLAB:
    alias: "MATLAB Tömb"
    what: "A MATLAB egy nagy teljesítményű numerikus számítási és vizualizációs szoftver, széles körben használt mérnöki számításokban, adatelemzésben és algoritmus fejlesztésben. A tömb és mátrix műveletei erőteljesek, támogatják a komplex matematikai számításokat és adatfeldolgozást. Nélkülözhetetlen eszköz mérnökök, kutatók és adattudósok számára."
    step1: "Töltsön fel MATLAB .m fájlokat vagy illessze be a tömb adatokat. Az eszköz elemzi a MATLAB szintaxist és kivonja a tömb struktúra információkat."
    step3: "Generáljon szabványos MATLAB tömb kódot többdimenziós tömbök, adattípus specifikációk és változó elnevezés támogatásával. A generált kód közvetlenül végrehajtható MATLAB környezetben adatelemzéshez és tudományos számításokhoz."
    from_alias: "MATLAB tömb"
    to_alias: "MATLAB tömb"
  RDataFrame:
    alias: "R DataFrame"
    what: "Az R DataFrame az R programozási nyelv központi adatstruktúrája, széles körben használt statisztikai elemzésben, adatbányászatban és gépi tanulásban. Az R a vezető eszköz statisztikai számításokhoz és grafikákhoz, a DataFrame hatékony adatmanipulációs, statisztikai elemzési és vizualizációs képességeket biztosít. Nélkülözhetetlen adattudósok, statisztikusok és kutatók számára, akik strukturált adatelemzéssel foglalkoznak."
    step1: "Töltsön fel R adat fájlokat vagy illessze be a DataFrame kódot. Az eszköz elemzi az R szintaxist és kivonja a DataFrame struktúra információkat, beleértve az oszlop típusokat, sor neveket és adat tartalmat."
    step3: "Generáljon szabványos R DataFrame kódot adattípus specifikációk, faktor szintek, sor/oszlop nevek és R-specifikus adatstruktúrák támogatásával. A generált kód közvetlenül végrehajtható R környezetben statisztikai elemzéshez és adatfeldolgozáshoz."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Konvertálás kezdése"
  start_generating: "Generálás kezdése"
  api_docs: "API dokumentáció"
related:
  section_title: 'További {{ if and .from (ne .from "generator") }}{{ .from }} és {{ end }}{{ .to }} konverterek'
  section_description: 'Fedezz fel további konvertereket {{ if and .from (ne .from "generator") }}{{ .from }} és {{ end }}{{ .to }} formátumokhoz. Alakítsd át az adataidat több formátum között a professzionális online konverziós eszközeinkkel.'
  title: "{{ .from }} {{ .to }}-re"
howto:
  step2: "Szerkeszd az adatokat a fejlett online táblázat szerkesztőnkkel, amely professzionális funkciókkal rendelkezik. Támogatja az üres sorok törlését, duplikátumok eltávolítását, adatok transzponálását, rendezést, regex keresést és cserét, valamint valós idejű előnézetet. Minden változás automatikusan %s formátumra konvertálódik pontos, megbízható eredményekkel."
  section_title: "Hogyan használd a {{ . }}-t"
  converter_description: "Tanuld meg, hogyan konvertálj {{ .from }}-t {{ .to }}-re a lépésről lépésre útmutatónkkal. Professzionális online konverter fejlett funkciókkal és valós idejű előnézettel."
  generator_description: "Tanuld meg, hogyan hozz létre professzionális {{ .to }} táblázatokat az online generátorunkkal. Excel-szerű szerkesztés, valós idejű előnézet és azonnali export lehetőségek."
extension:
  section_title: "Táblázat Észlelés és Kinyerés Bővítmény"
  section_description: "Táblázatok kinyerése bármely weboldalról egy kattintással. Konvertálás 30+ formátumra, beleértve az Excel, CSV, JSON-t azonnal - nincs szükség másolásra és beillesztésre."
  features:
    extraction_title: "Egy kattintásos táblázat kinyerés"
    extraction_description: "Azonnal kinyerheted a táblázatokat bármely weboldalról másolás és beillesztés nélkül - a professzionális adatkinyerés egyszerűvé téve"
    formats_title: "30+ formátum konverter támogatás"
    formats_description: "Konvertáld a kinyert táblázatokat Excel, CSV, JSON, Markdown, SQL és további formátumokra a fejlett táblázat konverterünkkel"
    detection_title: "Intelligens táblázat észlelés"
    detection_description: "Automatikusan észleli és kiemeli a táblázatokat bármely weboldalon a gyors adatkinyerés és konvertálás érdekében"
  hover_tip: "✨ Vidd az egeret bármely táblázat fölé a kinyerési ikon megtekintéséhez"
recommendations:
  section_title: "Egyetemek és szakemberek által ajánlott"
  section_description: "A TableConvert megbízható eszköz a szakemberek számára egyetemeken, kutatóintézetekben és fejlesztői csapatokban a megbízható táblázat konvertáláshoz és adatfeldolgozáshoz."
  cards:
    university_title: "University of Wisconsin-Madison"
    university_description: "TableConvert.com - Professzionális ingyenes online táblázat konverter és adatformátum eszköz"
    university_link: "Cikk elolvasása"
    facebook_title: "Adat szakemberek közössége"
    facebook_description: "Megosztva és ajánlva adatelemzők és szakemberek által Facebook fejlesztői csoportokban"
    facebook_link: "Bejegyzés megtekintése"
    twitter_title: "Fejlesztői közösség"
    twitter_description: "Ajánlva @xiaoying_eth és más fejlesztők által X-en (Twitter) táblázat konvertáláshoz"
    twitter_link: "Tweet megtekintése"
faq:
  section_title: "Gyakran ismételt kérdések"
  section_description: "Gyakori kérdések az ingyenes online táblázat konverterünkről, adatformátumokról és a konverziós folyamatról."
  what: "Mi az a %s formátum?"
  howto_convert:
    question: "Hogyan használd a {{ . }}-t ingyen?"
    answer: "Töltsd fel a {{ .from }} fájlodat, illeszd be az adatokat, vagy nyerd ki weboldalakról az ingyenes online táblázat konverterünkkel. A professzionális konverter eszközünk azonnal átalakítja az adataidat {{ .to }} formátumra valós idejű előnézettel és fejlett szerkesztési funkciókkal. Töltsd le vagy másold a konvertált eredményt azonnal."
  security:
    question: "Biztonságosak az adataim az online konverter használatakor?"
    answer: "Természetesen! Minden táblázat konverzió helyileg történik a böngésződben - az adataid soha nem hagyják el az eszközödet. Az online konverterünk mindent kliens oldalon dolgoz fel, biztosítva a teljes adatvédelmet és adatbiztonságot. Nincsenek fájlok tárolva a szervereinkén."
  free:
    question: "Valóban ingyenes a TableConvert használata?"
    answer: "Igen, a TableConvert teljesen ingyenes! Minden konverter funkció, táblázat szerkesztő, adatgenerátor eszközök és export opciók elérhetők költség, regisztráció vagy rejtett díjak nélkül. Konvertálj korlátlan fájlokat online ingyen."
  filesize:
    question: "Milyen fájlméret korlátai vannak az online konverternek?"
    answer: "Az ingyenes online táblázat konverterünk 10MB-ig támogatja a fájlokat. Nagyobb fájlokhoz, kötegelt feldolgozáshoz vagy vállalati igényekhez használd a böngésző bővítményünket vagy professzionális API szolgáltatásunkat magasabb korlátokkal."
stats:
  conversions: "Konvertált táblázatok"
  tables: "Generált táblázatok"
  formats: "Adat fájl formátumok"
  rating: "Felhasználói értékelés"
