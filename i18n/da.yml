site:
  fullname: "Table Convert"
  name: "TableConvert"
  subtitle: "Gratis Online Tabel Konverter og Generator"
  intro: "TableConvert er et gratis online tabel konverter og data generator værktøj, der understøtter konvertering mellem 30+ formater inklusive Excel, CSV, JSON, Markdown, LaTeX, SQL og mere."
  followTwitter: "Følg os på X"
title:
  converter: "%s til %s"
  generator: "%s Generator"
post:
  tags:
    converter: "Konverter"
    editor: "Editor"
    generator: "Generator"
    maker: "Builder"
  converter:
    title: "Konverter %s til %s Online"
    short: "Et gratis og kraftfuldt %s til %s online værktøj"
    intro: "Brugervenlig online %s til %s konverter. Transformer tabeldata ubesværet med vores intuitive konverteringsværktøj. Hurtig, pålidelig og brugervenlig."
  generator:
    title: "Online %s Editor og Generator"
    short: "Professionelt %s online generationsværktøj med omfattende funktioner"
    intro: "Brugervenlig online %s generator og tabeleditor. Opret professionelle datatabeller ubesværet med vores intuitive værktøj og realtids forhåndsvisning."
navbar:
  search:
    placeholder: "Søg konverter ..."
  sponsor: "Køb mig en kaffe"
  extension: "Udvidelse"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Datakilde"
    placeholder: "Indsæt dine %s data eller træk %s filer hertil"
    example: "Eksempel"
    upload: "Upload Fil"
    extract:
      enter: "Udtræk fra Webside"
      intro: "Indtast en webside URL, der indeholder tabeldata for automatisk at udtrække strukturerede data"
      btn: "Udtræk %s"
    excel:
      sheet: "Regneark"
      none: "Ingen"
  tableEditor:
    title: "Online Tabeleditor"
    undo: "Fortryd"
    redo: "Gentag"
    transpose: "Transponér"
    clear: "Ryd"
    deleteBlank: "Slet Tomme"
    deleteDuplicate: "Fjern Dubletter"
    uppercase: "STORE BOGSTAVER"
    lowercase: "små bogstaver"
    capitalize: "Stort Begyndelsesbogstav"
    replace:
      replace: "Find og Erstat (Regex understøttet)"
      subst: "Erstat med..."
      btn: "Erstat Alle"
  tableGenerator:
    title: "Tabelgenerator"
    sponsor: "Køb mig en kaffe"
    copy: "Kopiér til Udklipsholder"
    download: "Download Fil"
    tooltip:
      html:
        escape: "Escape HTML specialtegn (&, <, >, \", ') for at forhindre visningsfejl"
        div: "Brug DIV+CSS layout i stedet for traditionelle TABLE tags, bedre egnet til responsivt design"
        minify: "Fjern mellemrum og linjeskift for at generere komprimeret HTML kode"
        thead: "Generer standard tabelhoved (&lt;thead&gt;) og krop (&lt;tbody&gt;) struktur"
        tableCaption: "Tilføj beskrivende titel over tabellen (&lt;caption&gt; element)"
        tableClass: "Tilføj CSS klassenavn til tabellen for nem stilanpassning"
        tableId: "Indstil unikt ID identifikator for tabellen til JavaScript manipulation"
      jira:
        escape: "Escape pipe tegn (|) for at undgå konflikter med Jira tabel syntaks"
      json:
        parsingJSON: "Intelligent parsing af JSON strenge i celler til objekter"
        minify: "Generer kompakt enkelt-linje JSON format for at reducere filstørrelse"
        format: "Vælg output JSON datastruktur: objekt array, 2D array, osv."
      latex:
        escape: "Escape LaTeX specialtegn (%, &, _, #, $, osv.) for at sikre korrekt kompilering"
        ht: "Tilføj flydende positionsparameter [!ht] for at kontrollere tabelposition på siden"
        mwe: "Generer komplet LaTeX dokument"
        tableAlign: "Indstil horisontal justering af tabel på siden"
        tableBorder: "Konfigurer tabelkant stil: ingen kant, delvis kant, fuld kant"
        label: "Indstil tabellabel til \\ref{} kommando krydsreference"
        caption: "Indstil tabeloverskrift til visning over eller under tabellen"
        location: "Vælg tabeloverskrift visningsposition: over eller under"
        tableType: "Vælg tabelmiljø type: tabular, longtable, array, osv."
      markdown:
        escape: "Escape Markdown specialtegn (*, _, |, \\, osv.) for at undgå format konflikter"
        pretty: "Auto-juster kolonnebredder for at generere smukkere tabelformat"
        simple: "Brug forenklet syntaks, udelad ydre kant lodrette linjer"
        boldFirstRow: "Gør første række tekst fed"
        boldFirstColumn: "Gør første kolonne tekst fed"
        firstHeader: "Behandl første række som overskrift og tilføj separatorlinje"
        textAlign: "Indstil kolonne tekstjustering: venstre, center, højre"
        multilineHandling: "Flerlinje tekst håndtering: bevar linjeskift, escape til \\n, brug &lt;br&gt; tags"

        includeLineNumbers: "Tilføj linjenummer kolonne på venstre side af tabellen"
      magic:
        builtin: "Vælg foruddefinerede almindelige skabelonformater"
        rowsTpl: "<table> <tr> <th>Magic Syntaks</th> <th>Beskrivelse</th> <th>Understøt JS Metoder</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>1., 2. ... felt af <b>o</b>verskrift, Aka {hA} {hB} ...</td> <td>String metoder</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>1., 2. ... felt af nuværende række, Aka {$A} {$B} ...</td> <td>String metoder</td> </tr> <tr> <td>{F,} {F;}</td> <td>Opdel den nuværende række med strengen efter <b>F</b></td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>Linje <b>N</b>ummer af nuværende <b>R</b>ække fra 1 eller 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>S</b>lut linje <b>N</b>ummer af <b>R</b>ækker </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>U<b>d</b>før JavaScript kode, f.eks.: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Brug backslash <b>\\</b> til at outputte krøllede parenteser {...} </td> <td></td> </tr></table>"
        headerTpl: "Brugerdefineret output skabelon til overskrift sektion"
        footerTpl: "Brugerdefineret output skabelon til bundtekst sektion"
      textile:
        escape: "Escape Textile syntaks tegn (|, ., -, ^) for at undgå format konflikter"
        rowHeader: "Indstil første række som overskrift række"
        thead: "Tilføj Textile syntaks markører for tabelhoved og krop"
      xml:
        escape: "Escape XML specialtegn (&lt;, &gt;, &amp;, \", ') for at sikre gyldig XML"
        minify: "Generer komprimeret XML output, fjern ekstra mellemrum"
        rootElement: "Indstil XML rod element tag navn"
        rowElement: "Indstil XML element tag navn for hver række data"
        declaration: "Tilføj XML deklaration header (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Output data som XML attributter i stedet for børn elementer"
        cdata: "Indpak tekstindhold med CDATA for at beskytte specialtegn"
        encoding: "Indstil tegn kodning format for XML dokument"
        indentation: "Vælg XML indrykning tegn: mellemrum eller tabs"
      yaml:
        indentSize: "Indstil antal mellemrum til YAML hierarki indrykning (normalt 2 eller 4)"
        arrayStyle: "Array format: blok (et element per linje) eller flow (inline format)"
        quotationStyle: "Streng citationstegn stil: ingen citationstegn, enkelte citationstegn, dobbelte citationstegn"
      csv:
        bom: "Tilføj UTF-8 byte order mark for at hjælpe Excel og anden software med at genkende kodning"
      excel:
        autoWidth: "Juster automatisk kolonnebredde baseret på indhold"
        protectSheet: "Aktivér regneark beskyttelse med adgangskode: tableconvert.com"
      sql:
        primaryKey: "Specificer primær nøgle feltnavn til CREATE TABLE statement"
        dialect: "Vælg database type, påvirker citationstegn og datatype syntaks"
      ascii:
        forceSep: "Tving separator linjer mellem hver række data"
        style: "Vælg ASCII tabel kant tegning stil"
        comment: "Tilføj kommentar markører til at indpakke hele tabellen"
      mediawiki:
        minify: "Komprimer output kode, fjern ekstra mellemrum"
        header: "Markér første række som overskrift stil"
        sort: "Aktivér tabel klik sortering funktionalitet"
      asciidoc:
        minify: "Komprimer AsciiDoc format output"
        firstHeader: "Indstil første række som overskrift række"
        lastFooter: "Indstil sidste række som bundtekst række"
        title: "Tilføj titel tekst til tabellen"
      tracwiki:
        rowHeader: "Indstil første række som overskrift"
        colHeader: "Indstil første kolonne som overskrift"
      bbcode:
        minify: "Komprimer BBCode output format"
      restructuredtext:
        style: "Vælg reStructuredText tabel kant stil"
        forceSep: "Tving separator linjer"
      pdf:
        theme: "Vælg PDF tabel visuel stil til professionelle dokumenter"
        headerColor: "Vælg overskrift baggrundsfarve til PDF tabeller"
        showHead: "Kontroller overskrift visning på tværs af PDF sider"
        docTitle: "Valgfri titel til PDF dokumentet"
        docDescription: "Valgfri beskrivelse tekst til PDF dokument"
    label:
      ascii:
        forceSep: "Række Separatorer"
        style: "Kant Stil"
        comment: "Kommentar Wrapper"
      restructuredtext:
        style: "Kant Stil"
        forceSep: "Tving Separatorer"
      bbcode:
        minify: "Minificer Output"
      csv:
        doubleQuote: "Dobbelt Citationstegn Wrap"
        delimiter: "Felt Delimiter"
        bom: "UTF-8 BOM"
        valueDelimiter: "Værdi Delimiter"
        rowDelimiter: "Række Delimiter"
        prefix: "Række Præfiks"
        suffix: "Række Suffiks"
      excel:
        autoWidth: "Auto Bredde"
        textFormat: "Tekst Format"
        protectSheet: "Beskyt Ark"
        boldFirstRow: "Fed Første Række"
        boldFirstColumn: "Fed Første Kolonne"
        sheetName: "Ark Navn"
      html:
        escape: "Escape HTML Tegn"
        div: "DIV Tabel"
        minify: "Minificer Kode"
        thead: "Tabelhoved Struktur"
        tableCaption: "Tabel Overskrift"
        tableClass: "Tabel Klasse"
        tableId: "Tabel ID"
        rowHeader: "Række Overskrift"
        colHeader: "Kolonne Overskrift"
      jira:
        escape: "Escape Tegn"
        rowHeader: "Række Overskrift"
        colHeader: "Kolonne Overskrift"
      json:
        parsingJSON: "Parse JSON"
        minify: "Minificer Output"
        format: "Data Format"
        rootName: "Rod Objekt Navn"
        indentSize: "Indrykning Størrelse"
      jsonlines:
        parsingJSON: "Parse JSON"
        format: "Data Format"
      latex:
        escape: "Escape LaTeX Tabel Tegn"
        ht: "Flyde Position"
        mwe: "Komplet Dokument"
        tableAlign: "Tabel Justering"
        tableBorder: "Kant Stil"
        label: "Reference Label"
        caption: "Tabel Overskrift"
        location: "Overskrift Position"
        tableType: "Tabel Type"
        boldFirstRow: "Fed Første Række"
        boldFirstColumn: "Fed Første Kolonne"
        textAlign: "Tekst Justering"
        borders: "Kant Indstillinger"
      markdown:
        escape: "Escape Tegn"
        pretty: "Smuk Markdown Tabel"
        simple: "Simpel Markdown Format"
        boldFirstRow: "Fed Første Række"
        boldFirstColumn: "Fed Første Kolonne"
        firstHeader: "Første Overskrift"
        textAlign: "Tekst Justering"
        multilineHandling: "Flerlinje Håndtering"

        includeLineNumbers: "Tilføj Linjenumre"
        align: "Justering"
      mediawiki:
        minify: "Minificer Kode"
        header: "Overskrift Markup"
        sort: "Sorterbar"
      asciidoc:
        minify: "Minificer Format"
        firstHeader: "Første Overskrift"
        lastFooter: "Sidste Bundtekst"
        title: "Tabel Titel"
      tracwiki:
        rowHeader: "Række Overskrift"
        colHeader: "Kolonne Overskrift"
      sql:
        drop: "Drop Tabel (Hvis Eksisterer)"
        create: "Opret Tabel"
        oneInsert: "Batch Indsæt"
        table: "Tabel Navn"
        dialect: "Database Type"
        primaryKey: "Primær Nøgle"
      magic:
        builtin: "Indbygget Skabelon"
        rowsTpl: "Række Skabelon, Syntaks ->"
        headerTpl: "Overskrift Skabelon"
        footerTpl: "Bundtekst Skabelon"
      textile:
        escape: "Escape Tegn"
        rowHeader: "Række Overskrift"
        thead: "Tabelhoved Syntaks"
      xml:
        escape: "Escape XML Tegn"
        minify: "Minificer Output"
        rootElement: "Rod Element"
        rowElement: "Række Element"
        declaration: "XML Deklaration"
        attributes: "Attribut Tilstand"
        cdata: "CDATA Wrapper"
        encoding: "Kodning"
        indentSize: "Indrykning Størrelse"
      yaml:
        indentSize: "Indrykning Størrelse"
        arrayStyle: "Array Stil"
        quotationStyle: "Citationstegn Stil"
      pdf:
        theme: "PDF Tabel Tema"
        headerColor: "PDF Overskrift Farve"
        showHead: "PDF Overskrift Visning"
        docTitle: "PDF Dokument Titel"
        docDescription: "PDF Dokument Beskrivelse"

sidebar:
  all: "Alle Konverteringsværktøjer"
  dataSource:
    title: "Datakilde"
    description:
      converter: "Importer %s til konvertering til %s. Understøtter fil upload, online redigering og web data udtrækning."
      generator: "Opret tabeldata med understøttelse af flere input metoder inklusive manuel input, fil import og skabelon generering."
  tableEditor:
    title: "Online Tabeleditor"
    description:
      converter: "Behandl %s online ved hjælp af vores tabeleditor. Excel-lignende betjeningsoplevelse med understøttelse af sletning af tomme rækker, deduplikering, sortering og find og erstat."
      generator: "Kraftfuld online tabeleditor, der giver Excel-lignende betjeningsoplevelse. Understøtter sletning af tomme rækker, deduplikering, sortering og find og erstat."
  tableGenerator:
    title: "Tabelgenerator"
    description:
      converter: "Generer hurtigt %s med realtids forhåndsvisning af tabelgenerator. Rige eksport muligheder, et-klik kopiering og download."
      generator: "Eksporter %s data i flere formater for at imødekomme forskellige anvendelsesscenarier. Understøtter brugerdefinerede muligheder og realtids forhåndsvisning."
footer:
  changelog: "Ændringslog"
  sponsor: "Sponsorer"
  contact: "Kontakt Os"
  privacyPolicy: "Privatlivspolitik"
  about: "Om"
  resources: "Ressourcer"
  popularConverters: "Populære Konvertere"
  popularGenerators: "Populære Generatorer"
  dataSecurity: "Dine data er sikre - alle konverteringer kører i din browser."
converters:
  Markdown:
    alias: "Markdown Tabel"
    what: "Markdown er et letvægts markup sprog, der er meget brugt til teknisk dokumentation, blog indhold oprettelse og webudvikling. Dets tabel syntaks er kortfattet og intuitiv, understøtter tekst justering, link indlejring og formatering. Det er det foretrukne værktøj for programmører og tekniske skribenter, perfekt kompatibelt med GitHub, GitLab og andre kode hosting platforme."
    step1: "Indsæt Markdown tabeldata i datakilde området, eller træk og slip .md filer direkte til upload. Værktøjet parser automatisk tabel struktur og formatering, understøtter komplekst indlejret indhold og specialtegn håndtering."
    step3: "Generer standard Markdown tabel kode i realtid, understøtter flere justeringsmetoder, tekst fed skrift, linjenummer tilføjelse og andre avancerede format indstillinger. Den genererede kode er fuldt kompatibel med GitHub og store Markdown editorer, klar til brug med et-klik kopiering."
    from_alias: "Markdown Tabel Fil"
    to_alias: "Markdown Tabel Format"
  Magic:
    alias: "Brugerdefineret Skabelon"
    what: "Magic skabelon er en unik avanceret data generator af dette værktøj, der tillader brugere at oprette vilkårlig format data output gennem brugerdefineret skabelon syntaks. Understøtter variabel erstatning, betinget vurdering og loop behandling. Det er den ultimative løsning til håndtering af komplekse data konvertering behov og personaliserede output formater, især egnet til udviklere og data ingeniører."
    step1: "Vælg indbyggede almindelige skabeloner eller opret brugerdefineret skabelon syntaks. Understøtter rige variabler og funktioner, der kan håndtere komplekse data strukturer og forretningslogik."
    step3: "Generer data output, der fuldt opfylder brugerdefinerede format krav. Understøtter kompleks data konvertering logik og betinget behandling, forbedrer betydeligt data behandling effektivitet og output kvalitet. Et kraftfuldt værktøj til batch data behandling."
    from_alias: "Tabel Data"
    to_alias: "Brugerdefineret Format Output"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) er det mest udbredte data udvekslingsformat, perfekt understøttet af Excel, Google Sheets, database systemer og forskellige data analyse værktøjer. Dets simple struktur og stærke kompatibilitet gør det til standard formatet for data migration, batch import/eksport og kryds-platform data udveksling, meget brugt i forretningsanalyse, data videnskab og system integration."
    step1: "Upload CSV filer eller indsæt CSV data direkte. Værktøjet genkender intelligent forskellige delimitere (komma, tab, semikolon, pipe, osv.), detekterer automatisk data typer og kodning formater, understøtter hurtig parsing af store filer og komplekse data strukturer."
    step3: "Generer standard CSV format filer med understøttelse af brugerdefinerede delimitere, citationstegn stilarter, kodning formater og BOM mærke indstillinger. Sikrer perfekt kompatibilitet med målsystemer, giver download og komprimering muligheder for at imødekomme virksomhedsniveau data behandling behov."
    from_alias: "CSV Data Fil"
    to_alias: "CSV Standard Format"
  JSON:
    alias: "JSON Array"
    what: "JSON (JavaScript Object Notation) er standard tabel data formatet for moderne web applikationer, REST API'er og mikroservice arkitekturer. Dets klare struktur og effektive parsing gør det meget brugt i front-end og back-end data interaktion, konfigurationsfil lagring og NoSQL databaser. Understøtter indlejrede objekter, array strukturer og flere data typer, gør det uundværligt tabel data for moderne software udvikling."
    step1: "Upload JSON filer eller indsæt JSON arrays. Understøtter automatisk genkendelse og parsing af objekt arrays, indlejrede strukturer og komplekse data typer. Værktøjet validerer intelligent JSON syntaks og giver fejl prompts."
    step3: "Generer flere JSON format outputs: standard objekt arrays, 2D arrays, kolonne arrays og nøgle-værdi par formater. Understøtter forskønnet output, komprimering tilstand, brugerdefinerede rod objekt navne og indrykning indstillinger, tilpasser sig perfekt til forskellige API grænseflader og data lagring behov."
    from_alias: "JSON Array Fil"
    to_alias: "JSON Standard Format"
  JSONLines:
    alias: "JSONLines Format"
    what: "JSON Lines (også kendt som NDJSON) er et vigtigt format for big data behandling og streaming data transmission, med hver linje indeholdende et uafhængigt JSON objekt. Meget brugt i log analyse, data stream behandling, maskinlæring og distribuerede systemer. Understøtter trinvis behandling og parallel computing, gør det til det ideelle valg for håndtering af store strukturerede data."
    step1: "Upload JSONLines filer eller indsæt data. Værktøjet parser JSON objekter linje for linje, understøtter stor fil streaming behandling og fejl linje spring funktionalitet."
    step3: "Generer standard JSONLines format med hver linje outputter et komplet JSON objekt. Egnet til streaming behandling, batch import og big data analyse scenarier, understøtter data validering og format optimering."
    from_alias: "JSONLines Data"
    to_alias: "JSONLines Streaming Format"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) er standard formatet for virksomhedsniveau data udveksling og konfiguration styring, med strenge syntaks specifikationer og kraftfulde validering mekanismer. Meget brugt i web tjenester, konfigurationsfiler, dokument lagring og system integration. Understøtter navnerum, skema validering og XSLT transformation, gør det vigtigt tabel data for virksomheds applikationer."
    step1: "Upload XML filer eller indsæt XML data. Værktøjet parser automatisk XML struktur og konverterer det til tabel format, understøtter navnerum, attribut håndtering og komplekse indlejrede strukturer."
    step3: "Generer XML output, der overholder XML standarder. Understøtter brugerdefinerede rod elementer, række element navne, attribut tilstande, CDATA indpakning og tegn kodning indstillinger. Sikrer data integritet og kompatibilitet, opfylder virksomhedsniveau applikation krav."
    from_alias: "XML Data Fil"
    to_alias: "XML Standard Format"
  YAML:
    alias: "YAML Konfiguration"
    what: "YAML er en menneskevenlig data serialisering standard, berømt for sin klare hierarkiske struktur og kortfattede syntaks. Meget brugt i konfigurationsfiler, DevOps værktøjskæder, Docker Compose og Kubernetes deployment. Dens stærke læsbarhed og kortfattede syntaks gør det til et vigtigt konfigurationsformat for moderne cloud-native applikationer og automatiserede operationer."
    step1: "Upload YAML filer eller indsæt YAML data. Værktøjet parser intelligent YAML struktur og validerer syntaks korrekthed, understøtter multi-dokument formater og komplekse data typer."
    step3: "Generer standard YAML format output med understøttelse af blok og flow array stilarter, flere citationstegn indstillinger, brugerdefineret indrykning og kommentar bevarelse. Sikrer output YAML filer er fuldt kompatible med forskellige parsere og konfiguration systemer."
    from_alias: "YAML Konfigurationsfil"
    to_alias: "YAML Standard Format"
  MySQL:
      alias: "MySQL Forespørgselsresultater"
      what: "MySQL er verdens mest populære open-source relationelle database management system, berømt for sin høje ydeevne, pålidelighed og brugervenlighed. Meget brugt i web applikationer, virksomhedssystemer og data analyse platforme. MySQL forespørgselsresultater indeholder typisk strukturerede tabeldata, der tjener som en vigtig datakilde i database management og data analyse arbejde."
      step1: "Indsæt MySQL forespørgsel output resultater i datakilde området. Værktøjet genkender automatisk og parser MySQL kommandolinje output format, understøtter forskellige forespørgselsresultat stilarter og tegn kodninger, håndterer intelligent overskrifter og data rækker."
      step3: "Konverter hurtigt MySQL forespørgselsresultater til flere tabeldata formater, faciliterer data analyse, rapport generering, kryds-system data migration og data validering. Et praktisk værktøj for database administratorer og data analytikere."
      from_alias: "MySQL Forespørgsel Output"
      to_alias: "MySQL Tabel Data"
  SQL:
    alias: "Indsæt SQL"
    what: "SQL (Structured Query Language) er standard operations sproget for relationelle databaser, brugt til data forespørgsel, indsæt, opdater og slet operationer. Som kerneteknikken i database management, er SQL meget brugt i data analyse, business intelligence, ETL behandling og data warehouse konstruktion. Det er et essentielt færdigheds værktøj for data professionelle."
    step1: "Indsæt INSERT SQL statements eller upload .sql filer. Værktøjet parser intelligent SQL syntaks og udtrækker tabeldata, understøtter flere SQL dialekter og kompleks forespørgsel statement behandling."
    step3: "Generer standard SQL INSERT statements og tabel oprettelse statements. Understøtter flere database dialekter (MySQL, PostgreSQL, SQLite, SQL Server, Oracle), håndterer automatisk data type mapping, tegn escaping og primær nøgle begrænsninger. Sikrer genereret SQL kode kan udføres direkte."
    from_alias: "Indsæt SQL"
    to_alias: "SQL Statement"
  Qlik:
      alias: "Qlik Tabel"
      what: "Qlik er en software leverandør, der specialiserer sig i data visualisering, executive dashboards og self-service business intelligence produkter, sammen med Tableau og Microsoft."
      step1: ""
      step3: "Endelig viser [Tabel Generator](#TableGenerator) konverteringsresultaterne. Brug i din Qlik Sense, Qlik AutoML, QlikView eller anden Qlik-aktiveret software."
      from_alias: "Qlik Tabel"
      to_alias: "Qlik Tabel"
  DAX:
      alias: "DAX Tabel"
      what: "DAX (Data Analysis Expressions) er et programmeringssprog, der bruges gennem Microsoft Power BI til at oprette beregnede kolonner, målinger og brugerdefinerede tabeller."
      step1: ""
      step3: "Endelig viser [Tabel Generator](#TableGenerator) konverteringsresultaterne. Som forventet bruges det i flere Microsoft produkter inklusive Microsoft Power BI, Microsoft Analysis Services og Microsoft Power Pivot til Excel."
      from_alias: "DAX Tabel"
      to_alias: "DAX Tabel"
  Firebase:
    alias: "Firebase Liste"
    what: "Firebase er en BaaS applikationsudviklingsplatform, der giver hostede backend tjenester såsom realtids database, cloud storage, autentificering, crash rapportering, osv."
    step1: ""
    step3: "Endelig viser [Tabel Generator](#TableGenerator) konverteringsresultaterne. Du kan derefter bruge push metoden i Firebase API'en til at tilføje til en liste af data i Firebase databasen."
    from_alias: "Firebase Liste"
    to_alias: "Firebase Liste"
  HTML:
    alias: "HTML Tabel"
    what: "HTML tabeller er standard måden at vise strukturerede data på websider, bygget med table, tr, td og andre tags. Understøtter rig stil tilpasning, responsivt layout og interaktiv funktionalitet. Meget brugt i website udvikling, data visning og rapport generering, tjener som en vigtig komponent i front-end udvikling og web design."
    step1: "Indsæt HTML kode indeholdende tabeller eller upload HTML filer. Værktøjet genkender automatisk og udtrækker tabeldata fra sider, understøtter komplekse HTML strukturer, CSS stilarter og indlejret tabel behandling."
    step3: "Generer semantisk HTML tabel kode med understøttelse af thead/tbody struktur, CSS klasse indstillinger, tabel overskrifter, række/kolonne overskrifter og responsiv attribut konfiguration. Sikrer genereret tabel kode opfylder web standarder med god tilgængelighed og SEO venlighed."
    from_alias: "HTML Tabel"
    to_alias: "HTML Tabel"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel er verdens mest populære regneark software, meget brugt i forretningsanalyse, finansiel styring, databehandling og rapport oprettelse. Dens kraftfulde databehandling kapaciteter, rige funktionsbibliotek og fleksible visualisering funktioner gør det til standard værktøjet for kontor automatisering og data analyse, med omfattende applikationer på tværs af næsten alle industrier og områder."
    step1: "Upload Excel filer (understøtter .xlsx, .xls formater) eller kopiér tabeldata direkte fra Excel og indsæt. Værktøjet understøtter multi-regneark behandling, kompleks format genkendelse og hurtig parsing af store filer, håndterer automatisk sammenslåede celler og data typer."
    step3: "Generer Excel-kompatible tabeldata, der kan indsættes direkte i Excel eller downloades som standard .xlsx filer. Understøtter regneark navngivning, celle formatering, auto kolonne bredde, overskrift styling og data validering indstillinger. Sikrer output Excel filer har professionelt udseende og komplet funktionalitet."
    from_alias: "Excel Regneark"
    to_alias: "Excel"
  LaTeX:
    alias: "LaTeX Tabel"
    what: "LaTeX er et professionelt dokument typesetting system, især egnet til at oprette akademiske papirer, tekniske dokumenter og videnskabelige publikationer. Dens tabel funktionalitet er kraftfuld, understøtter komplekse matematiske formler, præcis layout kontrol og høj-kvalitets PDF output. Det er standard værktøjet i akademia og videnskabelig udgivelse, meget brugt i journal papirer, afhandlinger og teknisk manual typesetting."
    step1: "Indsæt LaTeX tabel kode eller upload .tex filer. Værktøjet parser LaTeX tabel syntaks og udtrækker data indhold, understøtter flere tabel miljøer (tabular, longtable, array, osv.) og komplekse format kommandoer."
    step3: "Generer professionel LaTeX tabel kode med understøttelse af flere tabel miljø valg, kant stil konfiguration, overskrift position indstillinger, dokument klasse specifikation og pakke styring. Kan generere komplette kompilerbare LaTeX dokumenter, sikrer output tabeller opfylder akademiske udgivelses standarder."
    from_alias: "LaTeX Tabel"
    to_alias: "LaTeX Tabel"
  ASCII:
    alias: "ASCII Tekst Tabel"
    what: "ASCII tabeller bruger almindelige tekst tegn til at tegne tabel kanter og strukturer, giver den bedste kompatibilitet og portabilitet. Kompatibel med alle tekst editorer, terminal miljøer og operativsystemer. Meget brugt i kode dokumentation, tekniske manualer, README filer og kommandolinje værktøj output. Det foretrukne data visnings format for programmører og system administratorer."
    step1: "Upload tekst filer indeholdende ASCII tabeller eller indsæt tabeldata direkte. Værktøjet genkender intelligent og parser ASCII tabel strukturer, understøtter flere kant stilarter og justering formater."
    step3: "Generer smukke almindelig tekst ASCII tabeller med understøttelse af flere kant stilarter (enkelt linje, dobbelt linje, afrundede hjørner, osv.), tekst justering metoder og auto kolonne bredde. Genererede tabeller vises perfekt i kode editorer, dokumenter og kommandolinjer."
    from_alias: "ASCII Tekst Tabel"
    to_alias: "ASCII Tekst Tabel"
  MediaWiki:
    alias: "MediaWiki Tabel"
    what: "MediaWiki er open-source software platformen brugt af berømte wiki sites som Wikipedia. Dens tabel syntaks er kortfattet men kraftfuld, understøtter tabel stil tilpasning, sortering funktionalitet og link indlejring. Meget brugt i videnstyring, kollaborativ redigering og indholdstyring systemer, tjener som kerneteknik til at bygge wiki encyklopædier og vidensbaser."
    step1: "Indsæt MediaWiki tabel kode eller upload wiki kilde filer. Værktøjet parser wiki markup syntaks og udtrækker tabeldata, understøtter kompleks wiki syntaks og skabelon behandling."
    step3: "Generer standard MediaWiki tabel kode med understøttelse af overskrift stil indstillinger, celle justering, sortering funktionalitet aktivering og kode komprimering muligheder. Genereret kode kan bruges direkte til wiki side redigering, sikrer perfekt visning på MediaWiki platforme."
    from_alias: "MediaWiki Tabel"
    to_alias: "MediaWiki Tabel"
  TracWiki:
    alias: "TracWiki Tabel"
    what: "Trac er et web-baseret projektledelse og bug tracking system, der bruger forenklet wiki syntaks til at oprette tabel indhold."
    step1: "Upload TracWiki filer eller indsæt tabeldata."
    step3: "Generer TracWiki-kompatibel tabel kode med understøttelse af række/kolonne overskrift indstillinger, faciliterer projekt dokument styring."
    from_alias: "TracWiki Tabel"
    to_alias: "TracWiki Tabel"
  AsciiDoc:
    alias: "AsciiDoc Tabel"
    what: "AsciiDoc er et letvægts markup sprog, der kan konverteres til HTML, PDF, manual sider og andre formater, meget brugt til teknisk dokumentation skrivning."
    step1: "Upload AsciiDoc filer eller indsæt data."
    step3: "Generer AsciiDoc tabel syntaks med understøttelse af overskrift, bundtekst og titel indstillinger, direkte brugbar i AsciiDoc editorer."
    from_alias: "AsciiDoc Tabel"
    to_alias: "AsciiDoc Tabel"
  reStructuredText:
    alias: "reStructuredText Tabel"
    what: "reStructuredText er standard dokumentations formatet for Python fællesskabet, understøtter rig tabel syntaks, almindeligt brugt til Sphinx dokumentation generering."
    step1: "Upload .rst filer eller indsæt reStructuredText data."
    step3: "Generer standard reStructuredText tabeller med understøttelse af flere kant stilarter, direkte brugbar i Sphinx dokumentation projekter."
    from_alias: "reStructuredText Tabel"
    to_alias: "reStructuredText Tabel"
  PHP:
    alias: "PHP Array"
    what: "PHP er et populært server-side scripting sprog, med arrays som dets kerne datastruktur, meget brugt i webudvikling og databehandling."
    step1: "Upload filer indeholdende PHP arrays eller indsæt data direkte."
    step3: "Generer standard PHP array kode, der kan bruges direkte i PHP projekter, understøtter associative og indekserede array formater."
    from_alias: "PHP Array"
    to_alias: "PHP Kode"
  Ruby:
    alias: "Ruby Array"
    what: "Ruby er et dynamisk objektorienteret programmeringssprog med kortfattet og elegant syntaks, med arrays som en vigtig datastruktur."
    step1: "Upload Ruby filer eller indsæt array data."
    step3: "Generer Ruby array kode, der overholder Ruby syntaks specifikationer, direkte brugbar i Ruby projekter."
    from_alias: "Ruby Array"
    to_alias: "Ruby Kode"
  ASP:
    alias: "ASP Array"
    what: "ASP (Active Server Pages) er Microsofts server-side scripting miljø, understøtter flere programmeringssprog til udvikling af dynamiske websider."
    step1: "Upload ASP filer eller indsæt array data."
    step3: "Generer ASP-kompatibel array kode med understøttelse af VBScript og JScript syntaks, brugbar i ASP.NET projekter."
    from_alias: "ASP Array"
    to_alias: "ASP Kode"
  ActionScript:
    alias: "ActionScript Array"
    what: "ActionScript er et objektorienteret programmeringssprog primært brugt til Adobe Flash og AIR applikationsudvikling."
    step1: "Upload .as filer eller indsæt ActionScript data."
    step3: "Generer ActionScript array kode, der overholder AS3 syntaks standarder, brugbar til Flash og Flex projektudvikling."
    from_alias: "ActionScript Array"
    to_alias: "ActionScript Kode"
  BBCode:
    alias: "BBCode Tabel"
    what: "BBCode er et letvægts markup sprog almindeligt brugt i fora og online fællesskaber, giver simpel formatering funktionalitet inklusive tabel understøttelse."
    step1: "Upload filer indeholdende BBCode eller indsæt data."
    step3: "Generer BBCode tabel kode egnet til forum posting og fællesskabs indhold oprettelse, med understøttelse af komprimeret output format."
    from_alias: "BBCode Tabel"
    to_alias: "BBCode Tabel"
  PDF:
    alias: "PDF Tabel"
    what: "PDF (Portable Document Format) er en kryds-platform dokument standard med fast layout, konsistent visning og høj-kvalitets print karakteristika. Meget brugt i formelle dokumenter, rapporter, fakturaer, kontrakter og akademiske papirer. Det foretrukne format for forretningskommunikation og dokument arkivering, sikrer fuldstændig konsistente visuelle effekter på tværs af forskellige enheder og operativsystemer."
    step1: "Importer tabeldata i ethvert format. Værktøjet analyserer automatisk datastruktur og udfører intelligent layout design, understøtter stor tabel auto-paginering og kompleks datatype behandling."
    step3: "Generer høj-kvalitets PDF tabel filer med understøttelse af flere professionelle tema stilarter (forretning, akademisk, minimalistisk, osv.), flersprogede skrifttyper, auto-paginering, vandmærke tilføjelse og print optimering. Sikrer output PDF dokumenter har professionelt udseende, direkte brugbar til forretningspræsentationer og formel publikation."
    from_alias: "Tabel Data"
    to_alias: "PDF Tabel"
  JPEG:
    alias: "JPEG Billede"
    what: "JPEG er det mest udbredte digitale billedformat med fremragende komprimering effekter og bred kompatibilitet. Dens lille filstørrelse og hurtige indlæsningshastighed gør det egnet til web visning, sociale medier deling, dokument illustrationer og online præsentationer. Standard billedformatet for digitale medier og netværkskommunikation, perfekt understøttet af næsten alle enheder og software."
    step1: "Importer tabeldata i ethvert format. Værktøjet udfører intelligent layout design og visuel optimering, beregner automatisk optimal størrelse og opløsning."
    step3: "Generer høj-definition JPEG tabel billeder med understøttelse af flere tema farveskemaer (lys, mørk, øjenvenlig, osv.), adaptivt layout, tekst klarhed optimering og størrelse tilpasning. Egnet til online deling, dokument indsættelse og præsentationsbrug, sikrer fremragende visuelle effekter på forskellige visningsenheder."
    from_alias: "Tabel Data"
    to_alias: "JPEG Billede"
  Jira:
    alias: "Jira Tabel"
    what: "JIRA er professionel projektledelse og bug tracking software udviklet af Atlassian, meget brugt i agil udvikling, software test og projekt samarbejde. Dens tabel funktionalitet understøtter rige formatering muligheder og data visning, tjener som et vigtigt værktøj for software udviklingsteams, projektledere og kvalitetssikring personale i krav styring, bug tracking og fremskridt rapportering."
    step1: "Upload filer indeholdende tabeldata eller indsæt data indhold direkte. Værktøjet behandler automatisk tabeldata og specialtegn escaping."
    step3: "Generer JIRA platform-kompatibel tabel kode med understøttelse af overskrift stil indstillinger, celle justering, tegn escape behandling og format optimering. Genereret kode kan indsættes direkte i JIRA issue beskrivelser, kommentarer eller wiki sider, sikrer korrekt visning og rendering i JIRA systemer."
    from_alias: "Jira Tabel"
    to_alias: "Jira Tabel"
  Textile:
    alias: "Textile Tabel"
    what: "Textile er et kortfattet letvægts markup sprog med simpel og let-at-lære syntaks, meget brugt i indholdstyring systemer, blog platforme og forum systemer. Dens tabel syntaks er klar og intuitiv, understøtter hurtig formatering og stil indstillinger. Et ideelt værktøj for indhold skabere og website administratorer til hurtig dokument skrivning og indhold udgivelse."
    step1: "Upload Textile format filer eller indsæt tabeldata. Værktøjet parser Textile markup syntaks og udtrækker tabel indhold."
    step3: "Generer standard Textile tabel syntaks med understøttelse af overskrift markup, celle justering, specialtegn escaping og format optimering. Genereret kode kan bruges direkte i CMS systemer, blog platforme og dokument systemer, der understøtter Textile, sikrer korrekt indhold rendering og visning."
    from_alias: "Textile Tabel"
    to_alias: "Textile Tabel"
  PNG:
    alias: "PNG Billede"
    what: "PNG (Portable Network Graphics) er et tabsfrit billedformat med fremragende komprimering og gennemsigtigheds understøttelse. Meget brugt i web design, digital grafik og professionel fotografering. Dens høje kvalitet og brede kompatibilitet gør det ideelt til skærmbilleder, logoer, diagrammer og alle billeder, der kræver skarpe detaljer og gennemsigtige baggrunde."
    step1: "Importer tabeldata i ethvert format. Værktøjet udfører intelligent layout design og visuel optimering, beregner automatisk optimal størrelse og opløsning til PNG output."
    step3: "Generer høj-kvalitets PNG tabel billeder med understøttelse af flere tema farveskemaer, gennemsigtige baggrunde, adaptivt layout og tekst klarhed optimering. Perfekt til web brug, dokument indsættelse og professionelle præsentationer med fremragende visuel kvalitet."
    from_alias: ""
    to_alias: "PNG Billede"
  TOML:
    alias: "TOML"
    what: "TOML (Tom's Obvious, Minimal Language) er et konfigurationsfil format, der er let at læse og skrive. Designet til at være utvetydig og simpel, er det meget brugt i moderne software projekter til konfigurationsstyring. Dens klare syntaks og stærke typning gør det til et fremragende valg for applikationsindstillinger og projekt konfigurationsfiler."
    step1: "Upload TOML filer eller indsæt konfigurationsdata. Værktøjet parser TOML syntaks og udtrækker strukturerede konfigurationsinformationer."
    step3: "Generer standard TOML format med understøttelse af indlejrede strukturer, datatyper og kommentarer. Genererede TOML filer er perfekte til applikationskonfiguration, byggeværktøjer og projektindstillinger."
    from_alias: "TOML"
    to_alias: "TOML Format"
  INI:
    alias: "INI"
    what: "INI filer er simple konfigurationsfiler brugt af mange applikationer og operativsystemer. Deres ligetil nøgle-værdi par struktur gør dem lette at læse og redigere manuelt. Meget brugt i Windows applikationer, legacy systemer og simple konfigurationsscenarier, hvor menneskelig læsbarhed er vigtig."
    step1: "Upload INI filer eller indsæt konfigurationsdata. Værktøjet parser INI syntaks og udtrækker sektion-baserede konfigurationsinformationer."
    step3: "Generer standard INI format med understøttelse af sektioner, kommentarer og forskellige datatyper. Genererede INI filer er kompatible med de fleste applikationer og konfigurationssystemer."
    from_alias: "INI"
    to_alias: "INI Format"
  Avro:
    alias: "Avro Schema"
    what: "Apache Avro er et data serialisering system, der giver rige datastrukturer, kompakt binært format og skema evolution kapaciteter. Meget brugt i big data behandling, besked køer og distribuerede systemer. Dens skema definition understøtter komplekse datatyper og version kompatibilitet, gør det til et vigtigt værktøj for data ingeniører og system arkitekter."
    step1: "Upload Avro skema filer eller indsæt data. Værktøjet parser Avro skema definitioner og udtrækker tabel struktur informationer."
    step3: "Generer standard Avro skema definitioner med understøttelse af datatype mapping, felt begrænsninger og skema validering. Genererede skemaer kan bruges direkte i Hadoop økosystemer, Kafka besked systemer og andre big data platforme."
    from_alias: "Avro Schema"
    to_alias: "Avro Schema"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) er Googles sprog-neutrale, platform-neutrale, udvidelige mekanisme til serialisering af strukturerede data. Meget brugt i microservices, API udvikling og data lagring. Dens effektive binære format og stærke typning gør det ideelt til høj-ydeevne applikationer og kryds-sprog kommunikation."
    step1: "Upload .proto filer eller indsæt Protocol Buffer definitioner. Værktøjet parser protobuf syntaks og udtrækker besked struktur informationer."
    step3: "Generer standard Protocol Buffer definitioner med understøttelse af besked typer, felt muligheder og service definitioner. Genererede .proto filer kan kompileres til flere programmeringssprog."
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf Schema"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas er det mest populære data analyse bibliotek i Python, med DataFrame som dets kerne datastruktur. Det giver kraftfulde data manipulation, rensning og analyse kapaciteter, meget brugt i data videnskab, maskinlæring og business intelligence. Et uundværligt værktøj for Python udviklere og data analytikere."
    step1: "Upload Python filer indeholdende DataFrame kode eller indsæt data. Værktøjet parser Pandas syntaks og udtrækker DataFrame struktur informationer."
    step3: "Generer standard Pandas DataFrame kode med understøttelse af datatype specifikationer, indeks indstillinger og data operationer. Genereret kode kan udføres direkte i Python miljø til data analyse og behandling."
    from_alias: "Pandas DataFrame"
    to_alias: "Pandas DataFrame"
  RDF:
    alias: "RDF Triple"
    what: "RDF (Resource Description Framework) er en standard model for data udveksling på nettet, designet til at repræsentere information om ressourcer i en graf form. Meget brugt i semantisk web, videns grafer og linkede data applikationer. Dens triple struktur muliggør rig metadata repræsentation og semantiske relationer."
    step1: "Upload RDF filer eller indsæt triple data. Værktøjet parser RDF syntaks og udtrækker semantiske relationer og ressource informationer."
    step3: "Generer standard RDF format med understøttelse af forskellige serialiseringer (RDF/XML, Turtle, N-Triples). Genereret RDF kan bruges i semantiske web applikationer, vidensbaser og linkede data systemer."
    from_alias: "RDF"
    to_alias: "RDF Triple"
  MATLAB:
    alias: "MATLAB Array"
    what: "MATLAB er en høj-ydeevne numerisk computing og visualisering software meget brugt i ingeniør computing, data analyse og algoritme udvikling. Dens array og matrix operationer er kraftfulde, understøtter komplekse matematiske beregninger og databehandling. Et essentielt værktøj for ingeniører, forskere og data videnskabsfolk."
    step1: "Upload MATLAB .m filer eller indsæt array data. Værktøjet parser MATLAB syntaks og udtrækker array struktur informationer."
    step3: "Generer standard MATLAB array kode med understøttelse af multi-dimensionelle arrays, datatype specifikationer og variabel navngivning. Genereret kode kan udføres direkte i MATLAB miljø til data analyse og videnskabelig computing."
    from_alias: "MATLAB Array"
    to_alias: "MATLAB Array"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame er kerne datastrukturen i R programmeringssproget, meget brugt i statistisk analyse, data mining og maskinlæring. R er det førende værktøj til statistisk computing og grafik, med DataFrame der giver kraftfulde data manipulation, statistisk analyse og visualisering kapaciteter. Essentielt for data videnskabsfolk, statistikere og forskere, der arbejder med struktureret data analyse."
    step1: "Upload R data filer eller indsæt DataFrame kode. Værktøjet parser R syntaks og udtrækker DataFrame struktur informationer inklusive kolonne typer, række navne og data indhold."
    step3: "Generer standard R DataFrame kode med understøttelse af datatype specifikationer, faktor niveauer, række/kolonne navne og R-specifikke datastrukturer. Genereret kode kan udføres direkte i R miljø til statistisk analyse og databehandling."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Start Konvertering"
  start_generating: "Start Generering"
  api_docs: "API Dokumentation"
related:
  section_title: 'Flere {{ if and .from (ne .from "generator") }}{{ .from }} og {{ end }}{{ .to }} Konvertere'
  section_description: 'Udforsk flere konvertere til {{ if and .from (ne .from "generator") }}{{ .from }} og {{ end }}{{ .to }} formater. Transformer dine data mellem flere formater med vores professionelle online konverteringsværktøjer.'
  title: "{{ .from }} til {{ .to }}"
howto:
  step2: "Rediger data ved hjælp af vores avancerede online tabeleditor med professionelle funktioner. Understøtter sletning af tomme rækker, fjernelse af dubletter, data transposition, sortering, regex find og erstat, og realtids forhåndsvisning. Alle ændringer konverteres automatisk til %s format med præcise, pålidelige resultater."
  section_title: "Sådan bruges {{ . }}"
  converter_description: "Lær at konvertere {{ .from }} til {{ .to }} med vores trin-for-trin guide. Professionel online konverter med avancerede funktioner og realtids forhåndsvisning."
  generator_description: "Lær at oprette professionelle {{ .to }} tabeller med vores online generator. Excel-lignende redigering, realtids forhåndsvisning og øjeblikkelige eksport muligheder."
extension:
  section_title: "Tabel Detektion og Udtrækning Udvidelse"
  section_description: "Udtræk tabeller fra enhver hjemmeside med ét klik. Konverter til 30+ formater inklusive Excel, CSV, JSON øjeblikkeligt - ingen kopiering og indsættelse påkrævet."
  features:
    extraction_title: "Et-Klik Tabel Udtrækning"
    extraction_description: "Udtræk øjeblikkeligt tabeller fra enhver webside uden kopiering og indsættelse - professionel data udtrækning gjort simpelt"
    formats_title: "30+ Format Konverter Understøttelse"
    formats_description: "Konverter udtrukne tabeller til Excel, CSV, JSON, Markdown, SQL og mere med vores avancerede tabelkonverter"
    detection_title: "Smart Tabel Detektion"
    detection_description: "Detekterer automatisk og fremhæver tabeller på enhver webside til hurtig data udtrækning og konvertering"
  hover_tip: "✨ Hold musen over enhver tabel for at se udtrækning ikonet"
recommendations:
  section_title: "Anbefalet af Universiteter og Professionelle"
  section_description: "TableConvert er betroet af professionelle på tværs af universiteter, forskningsinstitutioner og udviklingsteams til pålidelig tabelkonvertering og databehandling."
  cards:
    university_title: "University of Wisconsin-Madison"
    university_description: "TableConvert.com - Professionel gratis online tabelkonverter og dataformat værktøj"
    university_link: "Læs Artikel"
    facebook_title: "Data Professionelle Fællesskab"
    facebook_description: "Delt og anbefalet af data analytikere og professionelle i Facebook udvikler grupper"
    facebook_link: "Se Indlæg"
    twitter_title: "Udvikler Fællesskab"
    twitter_description: "Anbefalet af @xiaoying_eth og andre udviklere på X (Twitter) til tabelkonvertering"
    twitter_link: "Se Tweet"
faq:
  section_title: "Ofte Stillede Spørgsmål"
  section_description: "Almindelige spørgsmål om vores gratis online tabelkonverter, dataformater og konverteringsproces."
  what: "Hvad er %s format?"
  howto_convert:
    question: "Hvordan bruges {{ . }} gratis?"
    answer: "Upload din {{ .from }} fil, indsæt data eller udtræk fra websider ved hjælp af vores gratis online tabelkonverter. Vores professionelle konverter værktøj transformer øjeblikkeligt dine data til {{ .to }} format med realtids forhåndsvisning og avancerede redigerings funktioner. Download eller kopiér det konverterede resultat øjeblikkeligt."
  security:
    question: "Er mine data sikre når jeg bruger denne online konverter?"
    answer: "Absolut! Alle tabelkonverteringer sker lokalt i din browser - dine data forlader aldrig din enhed. Vores online konverter behandler alt på klient-siden, sikrer komplet privatliv og datasikkerhed. Ingen filer gemmes på vores servere."
  free:
    question: "Er TableConvert virkelig gratis at bruge?"
    answer: "Ja, TableConvert er fuldstændig gratis! Alle konverter funktioner, tabeleditor, data generator værktøjer og eksport muligheder er tilgængelige uden omkostninger, registrering eller skjulte gebyrer. Konverter ubegrænsede filer online gratis."
  filesize:
    question: "Hvilke filstørrelse begrænsninger har online konverteren?"
    answer: "Vores gratis online tabelkonverter understøtter filer op til 10MB. For større filer, batch behandling eller virksomhedsbehov, brug vores browser udvidelse eller professionelle API service med højere grænser."
stats:
  conversions: "Tabeller Konverteret"
  tables: "Tabeller Genereret"
  formats: "Data Fil Formater"
  rating: "Bruger Vurdering"
