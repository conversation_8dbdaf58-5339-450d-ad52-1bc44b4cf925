site:
  fullname: "Table Convert"
  name: "TableConvert"
  subtitle: "Konverter dan Generator Tabel Online Gratis"
  intro: "TableConvert adalah alat konverter tabel online gratis dan generator data yang mendukung konversi antara 30+ format termasuk Excel, CSV, JSON, Markdown, LaTeX, SQL dan lainnya."
  followTwitter: "Ikuti kami di X"
title:
  converter: "%s ke %s"
  generator: "Generator %s"
post:
  tags:
    converter: "Konverter"
    editor: "Editor"
    generator: "Generator"
    maker: "Pembuat"
  converter:
    title: "Konversi %s ke %s Online"
    short: "Alat online %s ke %s yang gratis dan canggih"
    intro: "Konverter online %s ke %s yang mudah digunakan. Transformasikan data tabel dengan mudah menggunakan alat konversi intuitif kami. Cepat, andal, dan ramah pengguna."
  generator:
    title: "Editor dan Generator %s Online"
    short: "Alat generasi online %s profesional dengan fitur lengkap"
    intro: "Generator %s online dan editor tabel yang mudah digunakan. Buat tabel data profesional dengan mudah menggunakan alat intuitif kami dan pratinjau real-time."
navbar:
  search:
    placeholder: "Cari konverter ..."
  sponsor: "Belikan Saya Kopi"
  extension: "Ekstensi"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Sumber Data"
    placeholder: "Tempel data %s Anda atau seret file %s ke sini"
    example: "Contoh"
    upload: "Unggah File"
    extract:
      enter: "Ekstrak dari Halaman Web"
      intro: "Masukkan URL halaman web yang berisi data tabel untuk mengekstrak data terstruktur secara otomatis"
      btn: "Ekstrak %s"
    excel:
      sheet: "Lembar Kerja"
      none: "Tidak Ada"
  tableEditor:
    title: "Editor Tabel Online"
    undo: "Urungkan"
    redo: "Ulangi"
    transpose: "Transpos"
    clear: "Hapus"
    deleteBlank: "Hapus Kosong"
    deleteDuplicate: "Hapus Duplikat"
    uppercase: "HURUF BESAR"
    lowercase: "huruf kecil"
    capitalize: "Huruf Kapital"
    replace:
      replace: "Cari & Ganti (Regex didukung)"
      subst: "Ganti dengan..."
      btn: "Ganti Semua"
  tableGenerator:
    title: "Generator Tabel"
    sponsor: "Belikan Saya Kopi"
    copy: "Salin ke Clipboard"
    download: "Unduh File"
    tooltip:
      html:
        escape: "Escape karakter khusus HTML (&, <, >, \", ') untuk mencegah kesalahan tampilan"
        div: "Gunakan layout DIV+CSS alih-alih tag TABLE tradisional, lebih cocok untuk desain responsif"
        minify: "Hapus spasi dan baris baru untuk menghasilkan kode HTML yang dikompresi"
        thead: "Hasilkan struktur kepala tabel standar (&lt;thead&gt;) dan badan (&lt;tbody&gt;)"
        tableCaption: "Tambahkan judul deskriptif di atas tabel (elemen &lt;caption&gt;)"
        tableClass: "Tambahkan nama kelas CSS ke tabel untuk kustomisasi gaya yang mudah"
        tableId: "Tetapkan pengenal ID unik untuk tabel untuk manipulasi JavaScript"
      jira:
        escape: "Escape karakter pipa (|) untuk menghindari konflik dengan sintaks tabel Jira"
      json:
        parsingJSON: "Parse string JSON dalam sel menjadi objek secara cerdas"
        minify: "Hasilkan format JSON satu baris yang kompak untuk mengurangi ukuran file"
        format: "Pilih struktur data JSON keluaran: array objek, array 2D, dll."
      latex:
        escape: "Escape karakter khusus LaTeX (%, &, _, #, $, dll.) untuk memastikan kompilasi yang tepat"
        ht: "Tambahkan parameter posisi mengambang [!ht] untuk mengontrol posisi tabel di halaman"
        mwe: "Hasilkan dokumen LaTeX lengkap"
        tableAlign: "Atur perataan horizontal tabel di halaman"
        tableBorder: "Konfigurasi gaya border tabel: tanpa border, border sebagian, border penuh"
        label: "Atur label tabel untuk referensi silang perintah \\ref{}"
        caption: "Atur keterangan tabel untuk ditampilkan di atas atau di bawah tabel"
        location: "Pilih posisi tampilan keterangan tabel: di atas atau di bawah"
        tableType: "Pilih jenis lingkungan tabel: tabular, longtable, array, dll."
      markdown:
        escape: "Escape karakter khusus Markdown (*, _, |, \\, dll.) untuk menghindari konflik format"
        pretty: "Sejajarkan lebar kolom secara otomatis untuk menghasilkan format tabel yang lebih indah"
        simple: "Gunakan sintaks yang disederhanakan, menghilangkan garis vertikal border luar"
        boldFirstRow: "Buat teks baris pertama menjadi tebal"
        boldFirstColumn: "Buat teks kolom pertama menjadi tebal"
        firstHeader: "Perlakukan baris pertama sebagai header dan tambahkan garis pemisah"
        textAlign: "Atur perataan teks kolom: kiri, tengah, kanan"
        multilineHandling: "Penanganan teks multibaris: pertahankan baris baru, escape ke \\n, gunakan tag &lt;br&gt;"

        includeLineNumbers: "Tambahkan kolom nomor baris di sisi kiri tabel"
      magic:
        builtin: "Pilih format template umum yang telah ditentukan"
        rowsTpl: "<table> <tr> <th>Sintaks Magic</th> <th>Deskripsi</th> <th>Dukungan Metode JS</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>Field ke-1, ke-2 ... dari <b>h</b>eading, Alias {hA} {hB} ...</td> <td>Metode string</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>Field ke-1, ke-2 ... dari baris saat ini, Alias {$A} {$B} ...</td> <td>Metode string</td> </tr> <tr> <td>{F,} {F;}</td> <td>Pisahkan baris saat ini dengan string setelah <b>F</b></td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td><b>N</b>omor baris <b>R</b>ow saat ini dari 1 atau 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>N</b>omor baris <b>A</b>khir dari <b>R</b>ows </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>E<b>x</b>ekusi kode JavaScript, contoh: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Gunakan backslash <b>\\</b> untuk menampilkan kurung kurawal {...} </td> <td></td> </tr></table>"
        headerTpl: "Template keluaran kustom untuk bagian header"
        footerTpl: "Template keluaran kustom untuk bagian footer"
      textile:
        escape: "Escape karakter sintaks Textile (|, ., -, ^) untuk menghindari konflik format"
        rowHeader: "Atur baris pertama sebagai baris header"
        thead: "Tambahkan penanda sintaks Textile untuk kepala dan badan tabel"
      xml:
        escape: "Escape karakter khusus XML (&lt;, &gt;, &amp;, \", ') untuk memastikan XML yang valid"
        minify: "Hasilkan keluaran XML yang dikompresi, menghapus spasi ekstra"
        rootElement: "Atur nama tag elemen root XML"
        rowElement: "Atur nama tag elemen XML untuk setiap baris data"
        declaration: "Tambahkan header deklarasi XML (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Keluarkan data sebagai atribut XML alih-alih elemen anak"
        cdata: "Bungkus konten teks dengan CDATA untuk melindungi karakter khusus"
        encoding: "Atur format encoding karakter untuk dokumen XML"
        indentation: "Pilih karakter indentasi XML: spasi atau tab"
      yaml:
        indentSize: "Atur jumlah spasi untuk indentasi hierarki YAML (biasanya 2 atau 4)"
        arrayStyle: "Format array: blok (satu item per baris) atau aliran (format inline)"
        quotationStyle: "Gaya kutipan string: tanpa kutipan, kutipan tunggal, kutipan ganda"
      csv:
        bom: "Tambahkan tanda urutan byte UTF-8 untuk membantu Excel dan perangkat lunak lain mengenali encoding"
      excel:
        autoWidth: "Sesuaikan lebar kolom secara otomatis berdasarkan konten"
        protectSheet: "Aktifkan perlindungan lembar kerja dengan kata sandi: tableconvert.com"
      sql:
        primaryKey: "Tentukan nama field kunci utama untuk pernyataan CREATE TABLE"
        dialect: "Pilih jenis database, mempengaruhi sintaks kutipan dan tipe data"
      ascii:
        forceSep: "Paksa garis pemisah antara setiap baris data"
        style: "Pilih gaya gambar border tabel ASCII"
        comment: "Tambahkan penanda komentar untuk membungkus seluruh tabel"
      mediawiki:
        minify: "Kompresi kode keluaran, menghapus spasi ekstra"
        header: "Tandai baris pertama sebagai gaya header"
        sort: "Aktifkan fungsi pengurutan klik tabel"
      asciidoc:
        minify: "Kompresi keluaran format AsciiDoc"
        firstHeader: "Atur baris pertama sebagai baris header"
        lastFooter: "Atur baris terakhir sebagai baris footer"
        title: "Tambahkan teks judul ke tabel"
      tracwiki:
        rowHeader: "Atur baris pertama sebagai header"
        colHeader: "Atur kolom pertama sebagai header"
      bbcode:
        minify: "Kompresi format keluaran BBCode"
      restructuredtext:
        style: "Pilih gaya border tabel reStructuredText"
        forceSep: "Paksa garis pemisah"
      pdf:
        theme: "Pilih gaya visual tabel PDF untuk dokumen profesional"
        headerColor: "Pilih warna latar belakang header untuk tabel PDF"
        showHead: "Kontrol tampilan header di seluruh halaman PDF"
        docTitle: "Judul opsional untuk dokumen PDF"
        docDescription: "Teks deskripsi opsional untuk dokumen PDF"
    label:
      ascii:
        forceSep: "Pemisah Baris"
        style: "Gaya Border"
        comment: "Pembungkus Komentar"
      restructuredtext:
        style: "Gaya Border"
        forceSep: "Paksa Pemisah"
      bbcode:
        minify: "Minifikasi Keluaran"
      csv:
        doubleQuote: "Bungkus Kutipan Ganda"
        delimiter: "Pembatas Field"
        bom: "UTF-8 BOM"
        valueDelimiter: "Pembatas Nilai"
        rowDelimiter: "Pembatas Baris"
        prefix: "Awalan Baris"
        suffix: "Akhiran Baris"
      excel:
        autoWidth: "Lebar Otomatis"
        textFormat: "Format Teks"
        protectSheet: "Lindungi Sheet"
        boldFirstRow: "Tebalkan Baris Pertama"
        boldFirstColumn: "Tebalkan Kolom Pertama"
        sheetName: "Nama Sheet"
      html:
        escape: "Escape Karakter HTML"
        div: "Tabel DIV"
        minify: "Minifikasi Kode"
        thead: "Struktur Kepala Tabel"
        tableCaption: "Keterangan Tabel"
        tableClass: "Kelas Tabel"
        tableId: "ID Tabel"
        rowHeader: "Header Baris"
        colHeader: "Header Kolom"
      jira:
        escape: "Escape Karakter"
        rowHeader: "Header Baris"
        colHeader: "Header Kolom"
      json:
        parsingJSON: "Parse JSON"
        minify: "Minifikasi Keluaran"
        format: "Format Data"
        rootName: "Nama Objek Root"
        indentSize: "Ukuran Indentasi"
      jsonlines:
        parsingJSON: "Parse JSON"
        format: "Format Data"
      latex:
        escape: "Escape Karakter Tabel LaTeX"
        ht: "Posisi Float"
        mwe: "Dokumen Lengkap"
        tableAlign: "Perataan Tabel"
        tableBorder: "Gaya Border"
        label: "Label Referensi"
        caption: "Keterangan Tabel"
        location: "Posisi Keterangan"
        tableType: "Jenis Tabel"
        boldFirstRow: "Tebalkan Baris Pertama"
        boldFirstColumn: "Tebalkan Kolom Pertama"
        textAlign: "Perataan Teks"
        borders: "Pengaturan Border"
      markdown:
        escape: "Escape Karakter"
        pretty: "Tabel Markdown Cantik"
        simple: "Format Markdown Sederhana"
        boldFirstRow: "Tebalkan Baris Pertama"
        boldFirstColumn: "Tebalkan Kolom Pertama"
        firstHeader: "Header Pertama"
        textAlign: "Perataan Teks"
        multilineHandling: "Penanganan Multibaris"

        includeLineNumbers: "Tambahkan Nomor Baris"
        align: "Perataan"
      mediawiki:
        minify: "Minifikasi Kode"
        header: "Markup Header"
        sort: "Dapat Diurutkan"
      asciidoc:
        minify: "Minifikasi Format"
        firstHeader: "Header Pertama"
        lastFooter: "Footer Terakhir"
        title: "Judul Tabel"
      tracwiki:
        rowHeader: "Header Baris"
        colHeader: "Header Kolom"
      sql:
        drop: "Drop Tabel (Jika Ada)"
        create: "Buat Tabel"
        oneInsert: "Insert Batch"
        table: "Nama Tabel"
        dialect: "Jenis Database"
        primaryKey: "Kunci Utama"
      magic:
        builtin: "Template Bawaan"
        rowsTpl: "Template Baris, Sintaks ->"
        headerTpl: "Template Header"
        footerTpl: "Template Footer"
      textile:
        escape: "Escape Karakter"
        rowHeader: "Header Baris"
        thead: "Sintaks Kepala Tabel"
      xml:
        escape: "Escape Karakter XML"
        minify: "Minifikasi Keluaran"
        rootElement: "Elemen Root"
        rowElement: "Elemen Baris"
        declaration: "Deklarasi XML"
        attributes: "Mode Atribut"
        cdata: "Pembungkus CDATA"
        encoding: "Encoding"
        indentSize: "Ukuran Indentasi"
      yaml:
        indentSize: "Ukuran Indentasi"
        arrayStyle: "Gaya Array"
        quotationStyle: "Gaya Kutipan"
      pdf:
        theme: "Tema Tabel PDF"
        headerColor: "Warna Header PDF"
        showHead: "Tampilan Header PDF"
        docTitle: "Judul Dokumen PDF"
        docDescription: "Deskripsi Dokumen PDF"

sidebar:
  all: "Semua Alat Konversi"
  dataSource:
    title: "Sumber Data"
    description:
      converter: "Impor %s untuk konversi ke %s. Mendukung unggah file, pengeditan online, dan ekstraksi data web."
      generator: "Buat data tabel dengan dukungan untuk berbagai metode input termasuk input manual, impor file, dan generasi template."
  tableEditor:
    title: "Editor Tabel Online"
    description:
      converter: "Proses %s online menggunakan editor tabel kami. Pengalaman operasi seperti Excel dengan dukungan untuk menghapus baris kosong, deduplikasi, pengurutan, dan cari & ganti."
      generator: "Editor tabel online yang canggih menyediakan pengalaman operasi seperti Excel. Mendukung penghapusan baris kosong, deduplikasi, pengurutan, dan cari & ganti."
  tableGenerator:
    title: "Generator Tabel"
    description:
      converter: "Hasilkan %s dengan cepat dengan pratinjau real-time dari generator tabel. Opsi ekspor yang kaya, salin & unduh satu klik."
      generator: "Ekspor data %s dalam berbagai format untuk memenuhi skenario penggunaan yang berbeda. Mendukung opsi kustom dan pratinjau real-time."
footer:
  changelog: "Log Perubahan"
  sponsor: "Sponsor"
  contact: "Hubungi Kami"
  privacyPolicy: "Kebijakan Privasi"
  about: "Tentang"
  resources: "Sumber Daya"
  popularConverters: "Konverter Populer"
  popularGenerators: "Generator Populer"
  dataSecurity: "Data Anda aman - semua konversi berjalan di browser Anda."
converters:
  Markdown:
    alias: "Tabel Markdown"
    what: "Markdown adalah bahasa markup ringan yang banyak digunakan untuk dokumentasi teknis, pembuatan konten blog, dan pengembangan web. Sintaks tabelnya ringkas dan intuitif, mendukung perataan teks, penyematan tautan, dan pemformatan. Ini adalah alat pilihan untuk programmer dan penulis teknis, kompatibel sempurna dengan GitHub, GitLab, dan platform hosting kode lainnya."
    step1: "Tempel data tabel Markdown ke area sumber data, atau langsung seret dan lepas file .md untuk diunggah. Alat ini secara otomatis mengurai struktur dan pemformatan tabel, mendukung konten bersarang yang kompleks dan penanganan karakter khusus."
    step3: "Hasilkan kode tabel Markdown standar secara real-time, mendukung berbagai metode perataan, penebalan teks, penambahan nomor baris, dan pengaturan format lanjutan lainnya. Kode yang dihasilkan sepenuhnya kompatibel dengan GitHub dan editor Markdown utama, siap digunakan dengan satu klik salin."
    from_alias: "File Tabel Markdown"
    to_alias: "Format Tabel Markdown"
  Magic:
    alias: "Template Kustom"
    what: "Template magic adalah generator data lanjutan unik dari alat ini, memungkinkan pengguna membuat keluaran data format arbitrer melalui sintaks template kustom. Mendukung penggantian variabel, penilaian kondisional, dan pemrosesan loop. Ini adalah solusi utama untuk menangani kebutuhan konversi data yang kompleks dan format keluaran yang dipersonalisasi, terutama cocok untuk pengembang dan insinyur data."
    step1: "Pilih template umum bawaan atau buat sintaks template kustom. Mendukung variabel dan fungsi yang kaya yang dapat menangani struktur data yang kompleks dan logika bisnis."
    step3: "Hasilkan keluaran data yang sepenuhnya memenuhi persyaratan format kustom. Mendukung logika konversi data yang kompleks dan pemrosesan kondisional, sangat meningkatkan efisiensi pemrosesan data dan kualitas keluaran. Alat yang kuat untuk pemrosesan data batch."
    from_alias: "Data Tabel"
    to_alias: "Keluaran Format Kustom"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) adalah format pertukaran data yang paling banyak digunakan, didukung sempurna oleh Excel, Google Sheets, sistem database, dan berbagai alat analisis data. Struktur sederhana dan kompatibilitas yang kuat menjadikannya format standar untuk migrasi data, impor/ekspor batch, dan pertukaran data lintas platform, banyak digunakan dalam analisis bisnis, ilmu data, dan integrasi sistem."
    step1: "Unggah file CSV atau langsung tempel data CSV. Alat ini secara cerdas mengenali berbagai pembatas (koma, tab, titik koma, pipa, dll.), secara otomatis mendeteksi jenis data dan format encoding, mendukung parsing cepat file besar dan struktur data yang kompleks."
    step3: "Hasilkan file format CSV standar dengan dukungan untuk pembatas kustom, gaya kutipan, format encoding, dan pengaturan tanda BOM. Memastikan kompatibilitas sempurna dengan sistem target, menyediakan opsi unduh dan kompresi untuk memenuhi kebutuhan pemrosesan data tingkat perusahaan."
    from_alias: "File Data CSV"
    to_alias: "Format Standar CSV"
  JSON:
    alias: "Array JSON"
    what: "JSON (JavaScript Object Notation) adalah format data tabel standar untuk aplikasi web modern, REST API, dan arsitektur microservice. Struktur yang jelas dan parsing yang efisien membuatnya banyak digunakan dalam interaksi data front-end dan back-end, penyimpanan file konfigurasi, dan database NoSQL. Mendukung objek bersarang, struktur array, dan berbagai jenis data, menjadikannya data tabel yang sangat diperlukan untuk pengembangan perangkat lunak modern."
    step1: "Unggah file JSON atau tempel array JSON. Mendukung pengenalan dan parsing otomatis array objek, struktur bersarang, dan jenis data yang kompleks. Alat ini secara cerdas memvalidasi sintaks JSON dan memberikan prompt kesalahan."
    step3: "Hasilkan berbagai keluaran format JSON: array objek standar, array 2D, array kolom, dan format pasangan kunci-nilai. Mendukung keluaran yang dipercantik, mode kompresi, nama objek root kustom, dan pengaturan indentasi, beradaptasi sempurna dengan berbagai antarmuka API dan kebutuhan penyimpanan data."
    from_alias: "File Array JSON"
    to_alias: "Format Standar JSON"
  JSONLines:
    alias: "Format JSONLines"
    what: "JSON Lines (juga dikenal sebagai NDJSON) adalah format penting untuk pemrosesan big data dan transmisi data streaming, dengan setiap baris berisi objek JSON independen. Banyak digunakan dalam analisis log, pemrosesan aliran data, pembelajaran mesin, dan sistem terdistribusi. Mendukung pemrosesan inkremental dan komputasi paralel, menjadikannya pilihan ideal untuk menangani data terstruktur skala besar."
    step1: "Unggah file JSONLines atau tempel data. Alat ini mengurai objek JSON baris demi baris, mendukung pemrosesan streaming file besar dan fungsionalitas melewati baris kesalahan."
    step3: "Hasilkan format JSONLines standar dengan setiap baris mengeluarkan objek JSON lengkap. Cocok untuk pemrosesan streaming, impor batch, dan skenario analisis big data, mendukung validasi data dan optimisasi format."
    from_alias: "Data JSONLines"
    to_alias: "Format Streaming JSONLines"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) adalah format standar untuk pertukaran data tingkat perusahaan dan manajemen konfigurasi, dengan spesifikasi sintaks yang ketat dan mekanisme validasi yang kuat. Banyak digunakan dalam layanan web, file konfigurasi, penyimpanan dokumen, dan integrasi sistem. Mendukung namespace, validasi skema, dan transformasi XSLT, menjadikannya data tabel penting untuk aplikasi perusahaan."
    step1: "Unggah file XML atau tempel data XML. Alat ini secara otomatis mengurai struktur XML dan mengonversinya ke format tabel, mendukung namespace, penanganan atribut, dan struktur bersarang yang kompleks."
    step3: "Hasilkan keluaran XML yang mematuhi standar XML. Mendukung elemen root kustom, nama elemen baris, mode atribut, pembungkusan CDATA, dan pengaturan encoding karakter. Memastikan integritas dan kompatibilitas data, memenuhi persyaratan aplikasi tingkat perusahaan."
    from_alias: "File Data XML"
    to_alias: "Format Standar XML"
  YAML:
    alias: "Konfigurasi YAML"
    what: "YAML adalah standar serialisasi data yang ramah manusia, terkenal karena struktur hierarkis yang jelas dan sintaks yang ringkas. Banyak digunakan dalam file konfigurasi, rantai alat DevOps, Docker Compose, dan deployment Kubernetes. Keterbacaan yang kuat dan sintaks yang ringkas menjadikannya format konfigurasi penting untuk aplikasi cloud-native modern dan operasi otomatis."
    step1: "Unggah file YAML atau tempel data YAML. Alat ini secara cerdas mengurai struktur YAML dan memvalidasi kebenaran sintaks, mendukung format multi-dokumen dan jenis data yang kompleks."
    step3: "Hasilkan keluaran format YAML standar dengan dukungan untuk gaya array blok dan aliran, pengaturan kutipan ganda, indentasi kustom, dan pelestarian komentar. Memastikan file YAML keluaran sepenuhnya kompatibel dengan berbagai parser dan sistem konfigurasi."
    from_alias: "File Konfigurasi YAML"
    to_alias: "Format Standar YAML"
  MySQL:
      alias: "Hasil Query MySQL"
      what: "MySQL adalah sistem manajemen database relasional open-source paling populer di dunia, terkenal karena kinerja tinggi, keandalan, dan kemudahan penggunaan. Banyak digunakan dalam aplikasi web, sistem perusahaan, dan platform analisis data. Hasil query MySQL biasanya berisi data tabel terstruktur, berfungsi sebagai sumber data penting dalam manajemen database dan pekerjaan analisis data."
      step1: "Tempel hasil keluaran query MySQL ke area sumber data. Alat ini secara otomatis mengenali dan mengurai format keluaran command-line MySQL, mendukung berbagai gaya hasil query dan encoding karakter, secara cerdas menangani header dan baris data."
      step3: "Konversi hasil query MySQL dengan cepat ke berbagai format data tabel, memfasilitasi analisis data, generasi laporan, migrasi data lintas sistem, dan validasi data. Alat praktis untuk administrator database dan analis data."
      from_alias: "Keluaran Query MySQL"
      to_alias: "Data Tabel MySQL"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) adalah bahasa operasi standar untuk database relasional, digunakan untuk operasi query, insert, update, dan delete data. Sebagai teknologi inti manajemen database, SQL banyak digunakan dalam analisis data, business intelligence, pemrosesan ETL, dan konstruksi data warehouse. Ini adalah alat keterampilan penting untuk profesional data."
    step1: "Tempel pernyataan INSERT SQL atau unggah file .sql. Alat ini secara cerdas mengurai sintaks SQL dan mengekstrak data tabel, mendukung berbagai dialek SQL dan pemrosesan pernyataan query yang kompleks."
    step3: "Hasilkan pernyataan INSERT SQL standar dan pernyataan pembuatan tabel. Mendukung berbagai dialek database (MySQL, PostgreSQL, SQLite, SQL Server, Oracle), secara otomatis menangani pemetaan jenis data, escaping karakter, dan batasan kunci utama. Memastikan kode SQL yang dihasilkan dapat dieksekusi langsung."
    from_alias: "Insert SQL"
    to_alias: "Pernyataan SQL"
  Qlik:
      alias: "Tabel Qlik"
      what: "Qlik adalah vendor perangkat lunak yang mengkhususkan diri dalam visualisasi data, dashboard eksekutif, dan produk business intelligence layanan mandiri, bersama dengan Tableau dan Microsoft."
      step1: ""
      step3: "Akhirnya, [Generator Tabel](#TableGenerator) menampilkan hasil konversi. Gunakan di Qlik Sense, Qlik AutoML, QlikView, atau perangkat lunak berkemampuan Qlik lainnya."
      from_alias: "Tabel Qlik"
      to_alias: "Tabel Qlik"
  DAX:
      alias: "Tabel DAX"
      what: "DAX (Data Analysis Expressions) adalah bahasa pemrograman yang digunakan di seluruh Microsoft Power BI untuk membuat kolom terhitung, ukuran, dan tabel kustom."
      step1: ""
      step3: "Akhirnya, [Generator Tabel](#TableGenerator) menampilkan hasil konversi. Seperti yang diharapkan, ini digunakan dalam beberapa produk Microsoft termasuk Microsoft Power BI, Microsoft Analysis Services, dan Microsoft Power Pivot untuk Excel."
      from_alias: "Tabel DAX"
      to_alias: "Tabel DAX"
  Firebase:
    alias: "Daftar Firebase"
    what: "Firebase adalah platform pengembangan aplikasi BaaS yang menyediakan layanan backend yang dihosting seperti database real-time, penyimpanan cloud, autentikasi, pelaporan crash, dll."
    step1: ""
    step3: "Akhirnya, [Generator Tabel](#TableGenerator) menampilkan hasil konversi. Anda kemudian dapat menggunakan metode push dalam API Firebase untuk menambahkan ke daftar data dalam database Firebase."
    from_alias: "Daftar Firebase"
    to_alias: "Daftar Firebase"
  HTML:
    alias: "Tabel HTML"
    what: "Tabel HTML adalah cara standar untuk menampilkan data terstruktur di halaman web, dibangun dengan tag table, tr, td dan lainnya. Mendukung kustomisasi gaya yang kaya, layout responsif, dan fungsionalitas interaktif. Banyak digunakan dalam pengembangan website, tampilan data, dan generasi laporan, berfungsi sebagai komponen penting dari pengembangan front-end dan desain web."
    step1: "Tempel kode HTML yang berisi tabel atau unggah file HTML. Alat ini secara otomatis mengenali dan mengekstrak data tabel dari halaman, mendukung struktur HTML yang kompleks, gaya CSS, dan pemrosesan tabel bersarang."
    step3: "Hasilkan kode tabel HTML semantik dengan dukungan untuk struktur thead/tbody, pengaturan kelas CSS, keterangan tabel, header baris/kolom, dan konfigurasi atribut responsif. Memastikan kode tabel yang dihasilkan memenuhi standar web dengan aksesibilitas yang baik dan ramah SEO."
    from_alias: "Tabel HTML"
    to_alias: "Tabel HTML"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel adalah perangkat lunak spreadsheet paling populer di dunia, banyak digunakan dalam analisis bisnis, manajemen keuangan, pemrosesan data, dan pembuatan laporan. Kemampuan pemrosesan data yang kuat, perpustakaan fungsi yang kaya, dan fitur visualisasi yang fleksibel menjadikannya alat standar untuk otomatisasi kantor dan analisis data, dengan aplikasi luas di hampir semua industri dan bidang."
    step1: "Unggah file Excel (mendukung format .xlsx, .xls) atau salin data tabel langsung dari Excel dan tempel. Alat ini mendukung pemrosesan multi-worksheet, pengenalan format yang kompleks, dan parsing cepat file besar, secara otomatis menangani sel yang digabung dan jenis data."
    step3: "Hasilkan data tabel yang kompatibel dengan Excel yang dapat langsung ditempel ke Excel atau diunduh sebagai file .xlsx standar. Mendukung penamaan worksheet, pemformatan sel, lebar kolom otomatis, styling header, dan pengaturan validasi data. Memastikan file Excel keluaran memiliki tampilan profesional dan fungsionalitas lengkap."
    from_alias: "Spreadsheet Excel"
    to_alias: "Excel"
  LaTeX:
    alias: "Tabel LaTeX"
    what: "LaTeX adalah sistem typesetting dokumen profesional, terutama cocok untuk membuat makalah akademik, dokumen teknis, dan publikasi ilmiah. Fungsionalitas tabelnya kuat, mendukung rumus matematika yang kompleks, kontrol layout yang presisi, dan keluaran PDF berkualitas tinggi. Ini adalah alat standar dalam akademik dan penerbitan ilmiah, banyak digunakan dalam makalah jurnal, disertasi, dan typesetting manual teknis."
    step1: "Tempel kode tabel LaTeX atau unggah file .tex. Alat ini mengurai sintaks tabel LaTeX dan mengekstrak konten data, mendukung berbagai lingkungan tabel (tabular, longtable, array, dll.) dan perintah format yang kompleks."
    step3: "Hasilkan kode tabel LaTeX profesional dengan dukungan untuk pemilihan lingkungan tabel ganda, konfigurasi gaya border, pengaturan posisi keterangan, spesifikasi kelas dokumen, dan manajemen paket. Dapat menghasilkan dokumen LaTeX yang dapat dikompilasi lengkap, memastikan tabel keluaran memenuhi standar penerbitan akademik."
    from_alias: "Tabel LaTeX"
    to_alias: "Tabel LaTeX"
  ASCII:
    alias: "Tabel Teks ASCII"
    what: "Tabel ASCII menggunakan karakter teks biasa untuk menggambar border dan struktur tabel, memberikan kompatibilitas dan portabilitas terbaik. Kompatibel dengan semua editor teks, lingkungan terminal, dan sistem operasi. Banyak digunakan dalam dokumentasi kode, manual teknis, file README, dan keluaran alat command-line. Format tampilan data pilihan untuk programmer dan administrator sistem."
    step1: "Unggah file teks yang berisi tabel ASCII atau langsung tempel data tabel. Alat ini secara cerdas mengenali dan mengurai struktur tabel ASCII, mendukung berbagai gaya border dan format perataan."
    step3: "Hasilkan tabel ASCII teks biasa yang indah dengan dukungan untuk berbagai gaya border (garis tunggal, garis ganda, sudut bulat, dll.), metode perataan teks, dan lebar kolom otomatis. Tabel yang dihasilkan ditampilkan dengan sempurna di editor kode, dokumen, dan command line."
    from_alias: "Tabel Teks ASCII"
    to_alias: "Tabel Teks ASCII"
  MediaWiki:
    alias: "Tabel MediaWiki"
    what: "MediaWiki adalah platform perangkat lunak open-source yang digunakan oleh situs wiki terkenal seperti Wikipedia. Sintaks tabelnya ringkas namun kuat, mendukung kustomisasi gaya tabel, fungsionalitas pengurutan, dan penyematan tautan. Banyak digunakan dalam manajemen pengetahuan, pengeditan kolaboratif, dan sistem manajemen konten, berfungsi sebagai teknologi inti untuk membangun ensiklopedia wiki dan basis pengetahuan."
    step1: "Tempel kode tabel MediaWiki atau unggah file sumber wiki. Alat ini mengurai sintaks markup wiki dan mengekstrak data tabel, mendukung sintaks wiki yang kompleks dan pemrosesan template."
    step3: "Hasilkan kode tabel MediaWiki standar dengan dukungan untuk pengaturan gaya header, perataan sel, pengaktifan fungsionalitas pengurutan, dan opsi kompresi kode. Kode yang dihasilkan dapat langsung digunakan untuk pengeditan halaman wiki, memastikan tampilan sempurna di platform MediaWiki."
    from_alias: "Tabel MediaWiki"
    to_alias: "Tabel MediaWiki"
  TracWiki:
    alias: "Tabel TracWiki"
    what: "Trac adalah sistem manajemen proyek dan pelacakan bug berbasis web yang menggunakan sintaks wiki yang disederhanakan untuk membuat konten tabel."
    step1: "Unggah file TracWiki atau tempel data tabel."
    step3: "Hasilkan kode tabel yang kompatibel dengan TracWiki dengan dukungan untuk pengaturan header baris/kolom, memfasilitasi manajemen dokumen proyek."
    from_alias: "Tabel TracWiki"
    to_alias: "Tabel TracWiki"
  AsciiDoc:
    alias: "Tabel AsciiDoc"
    what: "AsciiDoc adalah bahasa markup ringan yang dapat dikonversi ke HTML, PDF, halaman manual, dan format lainnya, banyak digunakan untuk penulisan dokumentasi teknis."
    step1: "Unggah file AsciiDoc atau tempel data."
    step3: "Hasilkan sintaks tabel AsciiDoc dengan dukungan untuk pengaturan header, footer, dan judul, langsung dapat digunakan di editor AsciiDoc."
    from_alias: "Tabel AsciiDoc"
    to_alias: "Tabel AsciiDoc"
  reStructuredText:
    alias: "Tabel reStructuredText"
    what: "reStructuredText adalah format dokumentasi standar untuk komunitas Python, mendukung sintaks tabel yang kaya, umumnya digunakan untuk generasi dokumentasi Sphinx."
    step1: "Unggah file .rst atau tempel data reStructuredText."
    step3: "Hasilkan tabel reStructuredText standar dengan dukungan untuk berbagai gaya border, langsung dapat digunakan dalam proyek dokumentasi Sphinx."
    from_alias: "Tabel reStructuredText"
    to_alias: "Tabel reStructuredText"
  PHP:
    alias: "Array PHP"
    what: "PHP adalah bahasa scripting server-side yang populer, dengan array sebagai struktur data intinya, banyak digunakan dalam pengembangan web dan pemrosesan data."
    step1: "Unggah file yang berisi array PHP atau langsung tempel data."
    step3: "Hasilkan kode array PHP standar yang dapat langsung digunakan dalam proyek PHP, mendukung format array asosiatif dan terindeks."
    from_alias: "Array PHP"
    to_alias: "Kode PHP"
  Ruby:
    alias: "Array Ruby"
    what: "Ruby adalah bahasa pemrograman berorientasi objek dinamis dengan sintaks yang ringkas dan elegan, dengan array sebagai struktur data penting."
    step1: "Unggah file Ruby atau tempel data array."
    step3: "Hasilkan kode array Ruby yang mematuhi spesifikasi sintaks Ruby, langsung dapat digunakan dalam proyek Ruby."
    from_alias: "Array Ruby"
    to_alias: "Kode Ruby"
  ASP:
    alias: "Array ASP"
    what: "ASP (Active Server Pages) adalah lingkungan scripting server-side Microsoft, mendukung berbagai bahasa pemrograman untuk mengembangkan halaman web dinamis."
    step1: "Unggah file ASP atau tempel data array."
    step3: "Hasilkan kode array yang kompatibel dengan ASP dengan dukungan untuk sintaks VBScript dan JScript, dapat digunakan dalam proyek ASP.NET."
    from_alias: "Array ASP"
    to_alias: "Kode ASP"
  ActionScript:
    alias: "Array ActionScript"
    what: "ActionScript adalah bahasa pemrograman berorientasi objek yang terutama digunakan untuk pengembangan aplikasi Adobe Flash dan AIR."
    step1: "Unggah file .as atau tempel data ActionScript."
    step3: "Hasilkan kode array ActionScript yang mematuhi standar sintaks AS3, dapat digunakan untuk pengembangan proyek Flash dan Flex."
    from_alias: "Array ActionScript"
    to_alias: "Kode ActionScript"
  BBCode:
    alias: "Tabel BBCode"
    what: "BBCode adalah bahasa markup ringan yang umumnya digunakan dalam forum dan komunitas online, menyediakan fungsionalitas pemformatan sederhana termasuk dukungan tabel."
    step1: "Unggah file yang berisi BBCode atau tempel data."
    step3: "Hasilkan kode tabel BBCode yang cocok untuk posting forum dan pembuatan konten komunitas, dengan dukungan untuk format keluaran yang dikompresi."
    from_alias: "Tabel BBCode"
    to_alias: "Tabel BBCode"
  PDF:
    alias: "Tabel PDF"
    what: "PDF (Portable Document Format) adalah standar dokumen lintas platform dengan layout tetap, tampilan konsisten, dan karakteristik pencetakan berkualitas tinggi. Banyak digunakan dalam dokumen formal, laporan, faktur, kontrak, dan makalah akademik. Format pilihan untuk komunikasi bisnis dan pengarsipan dokumen, memastikan efek visual yang sepenuhnya konsisten di berbagai perangkat dan sistem operasi."
    step1: "Impor data tabel dalam format apa pun. Alat ini secara otomatis menganalisis struktur data dan melakukan desain layout cerdas, mendukung paginasi otomatis tabel besar dan pemrosesan jenis data yang kompleks."
    step3: "Hasilkan file tabel PDF berkualitas tinggi dengan dukungan untuk berbagai gaya tema profesional (bisnis, akademik, minimalis, dll.), font multibahasa, paginasi otomatis, penambahan watermark, dan optimisasi cetak. Memastikan dokumen PDF keluaran memiliki tampilan profesional, langsung dapat digunakan untuk presentasi bisnis dan publikasi formal."
    from_alias: "Data Tabel"
    to_alias: "Tabel PDF"
  JPEG:
    alias: "Gambar JPEG"
    what: "JPEG adalah format gambar digital yang paling banyak digunakan dengan efek kompresi yang sangat baik dan kompatibilitas yang luas. Ukuran file yang kecil dan kecepatan loading yang cepat membuatnya cocok untuk tampilan web, berbagi media sosial, ilustrasi dokumen, dan presentasi online. Format gambar standar untuk media digital dan komunikasi jaringan, didukung sempurna oleh hampir semua perangkat dan perangkat lunak."
    step1: "Impor data tabel dalam format apa pun. Alat ini melakukan desain layout cerdas dan optimisasi visual, secara otomatis menghitung ukuran dan resolusi optimal."
    step3: "Hasilkan gambar tabel JPEG definisi tinggi dengan dukungan untuk berbagai skema warna tema (terang, gelap, ramah mata, dll.), layout adaptif, optimisasi kejelasan teks, dan kustomisasi ukuran. Cocok untuk berbagi online, penyisipan dokumen, dan penggunaan presentasi, memastikan efek visual yang sangat baik di berbagai perangkat tampilan."
    from_alias: "Data Tabel"
    to_alias: "Gambar JPEG"
  Jira:
    alias: "Tabel Jira"
    what: "JIRA adalah perangkat lunak manajemen proyek dan pelacakan bug profesional yang dikembangkan oleh Atlassian, banyak digunakan dalam pengembangan agile, pengujian perangkat lunak, dan kolaborasi proyek. Fungsionalitas tabelnya mendukung opsi pemformatan yang kaya dan tampilan data, berfungsi sebagai alat penting untuk tim pengembangan perangkat lunak, manajer proyek, dan personel jaminan kualitas dalam manajemen persyaratan, pelacakan bug, dan pelaporan kemajuan."
    step1: "Unggah file yang berisi data tabel atau langsung tempel konten data. Alat ini secara otomatis memproses data tabel dan escaping karakter khusus."
    step3: "Hasilkan kode tabel yang kompatibel dengan platform JIRA dengan dukungan untuk pengaturan gaya header, perataan sel, pemrosesan escape karakter, dan optimisasi format. Kode yang dihasilkan dapat langsung ditempel ke deskripsi masalah JIRA, komentar, atau halaman wiki, memastikan tampilan dan rendering yang benar dalam sistem JIRA."
    from_alias: "Tabel Jira"
    to_alias: "Tabel Jira"
  Textile:
    alias: "Tabel Textile"
    what: "Textile adalah bahasa markup ringan yang ringkas dengan sintaks yang sederhana dan mudah dipelajari, banyak digunakan dalam sistem manajemen konten, platform blog, dan sistem forum. Sintaks tabelnya jelas dan intuitif, mendukung pemformatan cepat dan pengaturan gaya. Alat ideal untuk pembuat konten dan administrator website untuk penulisan dokumen cepat dan penerbitan konten."
    step1: "Unggah file format Textile atau tempel data tabel. Alat ini mengurai sintaks markup Textile dan mengekstrak konten tabel."
    step3: "Hasilkan sintaks tabel Textile standar dengan dukungan untuk markup header, perataan sel, escaping karakter khusus, dan optimisasi format. Kode yang dihasilkan dapat langsung digunakan dalam sistem CMS, platform blog, dan sistem dokumen yang mendukung Textile, memastikan rendering dan tampilan konten yang benar."
    from_alias: "Tabel Textile"
    to_alias: "Tabel Textile"
  PNG:
    alias: "Gambar PNG"
    what: "PNG (Portable Network Graphics) adalah format gambar lossless dengan kompresi yang sangat baik dan dukungan transparansi. Banyak digunakan dalam desain web, grafik digital, dan fotografi profesional. Kualitas tinggi dan kompatibilitas yang luas membuatnya ideal untuk screenshot, logo, diagram, dan gambar apa pun yang memerlukan detail tajam dan latar belakang transparan."
    step1: "Impor data tabel dalam format apa pun. Alat ini melakukan desain layout cerdas dan optimisasi visual, secara otomatis menghitung ukuran dan resolusi optimal untuk keluaran PNG."
    step3: "Hasilkan gambar tabel PNG berkualitas tinggi dengan dukungan untuk berbagai skema warna tema, latar belakang transparan, layout adaptif, dan optimisasi kejelasan teks. Sempurna untuk penggunaan web, penyisipan dokumen, dan presentasi profesional dengan kualitas visual yang sangat baik."
    from_alias: ""
    to_alias: "Gambar PNG"
  TOML:
    alias: "TOML"
    what: "TOML (Tom's Obvious, Minimal Language) adalah format file konfigurasi yang mudah dibaca dan ditulis. Dirancang untuk tidak ambigu dan sederhana, banyak digunakan dalam proyek perangkat lunak modern untuk manajemen konfigurasi. Sintaks yang jelas dan typing yang kuat menjadikannya pilihan yang sangat baik untuk pengaturan aplikasi dan file konfigurasi proyek."
    step1: "Unggah file TOML atau tempel data konfigurasi. Alat ini mengurai sintaks TOML dan mengekstrak informasi konfigurasi terstruktur."
    step3: "Hasilkan format TOML standar dengan dukungan untuk struktur bersarang, jenis data, dan komentar. File TOML yang dihasilkan sempurna untuk konfigurasi aplikasi, alat build, dan pengaturan proyek."
    from_alias: "TOML"
    to_alias: "Format TOML"
  INI:
    alias: "INI"
    what: "File INI adalah file konfigurasi sederhana yang digunakan oleh banyak aplikasi dan sistem operasi. Struktur pasangan kunci-nilai yang langsung membuatnya mudah dibaca dan diedit secara manual. Banyak digunakan dalam aplikasi Windows, sistem legacy, dan skenario konfigurasi sederhana di mana keterbacaan manusia penting."
    step1: "Unggah file INI atau tempel data konfigurasi. Alat ini mengurai sintaks INI dan mengekstrak informasi konfigurasi berbasis bagian."
    step3: "Hasilkan format INI standar dengan dukungan untuk bagian, komentar, dan berbagai jenis data. File INI yang dihasilkan kompatibel dengan sebagian besar aplikasi dan sistem konfigurasi."
    from_alias: "INI"
    to_alias: "Format INI"
  Avro:
    alias: "Skema Avro"
    what: "Apache Avro adalah sistem serialisasi data yang menyediakan struktur data yang kaya, format biner yang kompak, dan kemampuan evolusi skema. Banyak digunakan dalam pemrosesan big data, antrian pesan, dan sistem terdistribusi. Definisi skemanya mendukung jenis data yang kompleks dan kompatibilitas versi, menjadikannya alat penting untuk insinyur data dan arsitek sistem."
    step1: "Unggah file skema Avro atau tempel data. Alat ini mengurai definisi skema Avro dan mengekstrak informasi struktur tabel."
    step3: "Hasilkan definisi skema Avro standar dengan dukungan untuk pemetaan jenis data, batasan field, dan validasi skema. Skema yang dihasilkan dapat langsung digunakan dalam ekosistem Hadoop, sistem pesan Kafka, dan platform big data lainnya."
    from_alias: "Skema Avro"
    to_alias: "Skema Avro"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) adalah mekanisme Google yang netral bahasa, netral platform, dan dapat diperluas untuk serialisasi data terstruktur. Banyak digunakan dalam microservices, pengembangan API, dan penyimpanan data. Format biner yang efisien dan typing yang kuat membuatnya ideal untuk aplikasi kinerja tinggi dan komunikasi lintas bahasa."
    step1: "Unggah file .proto atau tempel definisi Protocol Buffer. Alat ini mengurai sintaks protobuf dan mengekstrak informasi struktur pesan."
    step3: "Hasilkan definisi Protocol Buffer standar dengan dukungan untuk jenis pesan, opsi field, dan definisi layanan. File .proto yang dihasilkan dapat dikompilasi untuk berbagai bahasa pemrograman."
    from_alias: "Protocol Buffer"
    to_alias: "Skema Protobuf"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas adalah perpustakaan analisis data paling populer di Python, dengan DataFrame sebagai struktur data intinya. Ini menyediakan kemampuan manipulasi, pembersihan, dan analisis data yang kuat, banyak digunakan dalam ilmu data, pembelajaran mesin, dan business intelligence. Alat yang sangat diperlukan untuk pengembang Python dan analis data."
    step1: "Unggah file Python yang berisi kode DataFrame atau tempel data. Alat ini mengurai sintaks Pandas dan mengekstrak informasi struktur DataFrame."
    step3: "Hasilkan kode Pandas DataFrame standar dengan dukungan untuk spesifikasi jenis data, pengaturan indeks, dan operasi data. Kode yang dihasilkan dapat langsung dieksekusi di lingkungan Python untuk analisis dan pemrosesan data."
    from_alias: "Pandas DataFrame"
    to_alias: "Pandas DataFrame"
  RDF:
    alias: "RDF Triple"
    what: "RDF (Resource Description Framework) adalah model standar untuk pertukaran data di Web, dirancang untuk merepresentasikan informasi tentang sumber daya dalam bentuk grafik. Banyak digunakan dalam web semantik, grafik pengetahuan, dan aplikasi data terhubung. Struktur triple-nya memungkinkan representasi metadata yang kaya dan hubungan semantik."
    step1: "Unggah file RDF atau tempel data triple. Alat ini mengurai sintaks RDF dan mengekstrak hubungan semantik dan informasi sumber daya."
    step3: "Hasilkan format RDF standar dengan dukungan untuk berbagai serialisasi (RDF/XML, Turtle, N-Triples). RDF yang dihasilkan dapat digunakan dalam aplikasi web semantik, basis pengetahuan, dan sistem data terhubung."
    from_alias: "RDF"
    to_alias: "RDF Triple"
  MATLAB:
    alias: "Array MATLAB"
    what: "MATLAB adalah perangkat lunak komputasi numerik dan visualisasi berkinerja tinggi yang banyak digunakan dalam komputasi teknik, analisis data, dan pengembangan algoritma. Operasi array dan matriksnya kuat, mendukung perhitungan matematika yang kompleks dan pemrosesan data. Alat penting untuk insinyur, peneliti, dan ilmuwan data."
    step1: "Unggah file MATLAB .m atau tempel data array. Alat ini mengurai sintaks MATLAB dan mengekstrak informasi struktur array."
    step3: "Hasilkan kode array MATLAB standar dengan dukungan untuk array multi-dimensi, spesifikasi jenis data, dan penamaan variabel. Kode yang dihasilkan dapat langsung dieksekusi di lingkungan MATLAB untuk analisis data dan komputasi ilmiah."
    from_alias: "Array MATLAB"
    to_alias: "Array MATLAB"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame adalah struktur data inti dalam bahasa pemrograman R, banyak digunakan dalam analisis statistik, data mining, dan pembelajaran mesin. R adalah alat utama untuk komputasi statistik dan grafik, dengan DataFrame menyediakan kemampuan manipulasi data, analisis statistik, dan visualisasi yang kuat. Penting untuk ilmuwan data, statistikawan, dan peneliti yang bekerja dengan analisis data terstruktur."
    step1: "Unggah file data R atau tempel kode DataFrame. Alat ini mengurai sintaks R dan mengekstrak informasi struktur DataFrame termasuk jenis kolom, nama baris, dan konten data."
    step3: "Hasilkan kode R DataFrame standar dengan dukungan untuk spesifikasi jenis data, level faktor, nama baris/kolom, dan struktur data khusus R. Kode yang dihasilkan dapat langsung dieksekusi di lingkungan R untuk analisis statistik dan pemrosesan data."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Mulai Konversi"
  start_generating: "Mulai Generasi"
  api_docs: "Dokumentasi API"
related:
  section_title: 'Lebih Banyak Konverter {{ if and .from (ne .from "generator") }}{{ .from }} dan {{ end }}{{ .to }}'
  section_description: 'Jelajahi lebih banyak konverter untuk format {{ if and .from (ne .from "generator") }}{{ .from }} dan {{ end }}{{ .to }}. Transformasikan data Anda antara berbagai format dengan alat konversi online profesional kami.'
  title: "{{ .from }} ke {{ .to }}"
howto:
  step2: "Edit data menggunakan editor tabel online canggih kami dengan fitur profesional. Mendukung penghapusan baris kosong, penghapusan duplikat, transposisi data, pengurutan, cari & ganti regex, dan pratinjau real-time. Semua perubahan secara otomatis dikonversi ke format %s dengan hasil yang presisi dan andal."
  section_title: "Cara menggunakan {{ . }}"
  converter_description: "Pelajari cara mengonversi {{ .from }} ke {{ .to }} dengan panduan langkah demi langkah kami. Konverter online profesional dengan fitur canggih dan pratinjau real-time."
  generator_description: "Pelajari cara membuat tabel {{ .to }} profesional dengan generator online kami. Pengeditan seperti Excel, pratinjau real-time, dan kemampuan ekspor instan."
extension:
  section_title: "Ekstensi Deteksi & Ekstraksi Tabel"
  section_description: "Ekstrak tabel dari website mana pun dengan satu klik. Konversi ke 30+ format termasuk Excel, CSV, JSON secara instan - tidak perlu copy-paste."
  features:
    extraction_title: "Ekstraksi Tabel Satu Klik"
    extraction_description: "Ekstrak tabel secara instan dari halaman web mana pun tanpa copy-paste - ekstraksi data profesional menjadi sederhana"
    formats_title: "Dukungan Konverter 30+ Format"
    formats_description: "Konversi tabel yang diekstrak ke Excel, CSV, JSON, Markdown, SQL, dan lainnya dengan konverter tabel canggih kami"
    detection_title: "Deteksi Tabel Cerdas"
    detection_description: "Secara otomatis mendeteksi dan menyorot tabel di halaman web mana pun untuk ekstraksi dan konversi data yang cepat"
  hover_tip: "✨ Arahkan kursor ke tabel mana pun untuk melihat ikon ekstraksi"
recommendations:
  section_title: "Direkomendasikan oleh Universitas & Profesional"
  section_description: "TableConvert dipercaya oleh profesional di universitas, institusi penelitian, dan tim pengembangan untuk konversi tabel dan pemrosesan data yang andal."
  cards:
    university_title: "University of Wisconsin-Madison"
    university_description: "TableConvert.com - Alat konverter tabel online gratis dan format data profesional"
    university_link: "Baca Artikel"
    facebook_title: "Komunitas Profesional Data"
    facebook_description: "Dibagikan dan direkomendasikan oleh analis data dan profesional dalam grup pengembang Facebook"
    facebook_link: "Lihat Postingan"
    twitter_title: "Komunitas Pengembang"
    twitter_description: "Direkomendasikan oleh @xiaoying_eth dan pengembang lain di X (Twitter) untuk konversi tabel"
    twitter_link: "Lihat Tweet"
faq:
  section_title: "Pertanyaan yang Sering Diajukan"
  section_description: "Pertanyaan umum tentang konverter tabel online gratis kami, format data, dan proses konversi."
  what: "Apa itu format %s?"
  howto_convert:
    question: "Bagaimana cara menggunakan {{ . }} secara gratis?"
    answer: "Unggah file {{ .from }} Anda, tempel data, atau ekstrak dari halaman web menggunakan konverter tabel online gratis kami. Alat konverter profesional kami secara instan mengubah data Anda ke format {{ .to }} dengan pratinjau real-time dan fitur pengeditan canggih. Unduh atau salin hasil konversi segera."
  security:
    question: "Apakah data saya aman saat menggunakan konverter online ini?"
    answer: "Tentu saja! Semua konversi tabel terjadi secara lokal di browser Anda - data Anda tidak pernah meninggalkan perangkat Anda. Konverter online kami memproses semuanya di sisi klien, memastikan privasi dan keamanan data yang lengkap. Tidak ada file yang disimpan di server kami."
  free:
    question: "Apakah TableConvert benar-benar gratis untuk digunakan?"
    answer: "Ya, TableConvert sepenuhnya gratis! Semua fitur konverter, editor tabel, alat generator data, dan opsi ekspor tersedia tanpa biaya, registrasi, atau biaya tersembunyi. Konversi file tak terbatas online secara gratis."
  filesize:
    question: "Apa batasan ukuran file yang dimiliki konverter online?"
    answer: "Konverter tabel online gratis kami mendukung file hingga 10MB. Untuk file yang lebih besar, pemrosesan batch, atau kebutuhan perusahaan, gunakan ekstensi browser atau layanan API profesional kami dengan batas yang lebih tinggi."
stats:
  conversions: "Tabel Dikonversi"
  tables: "Tabel Dihasilkan"
  formats: "Format File Data"
  rating: "Rating Pengguna"
