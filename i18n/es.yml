site:
  fullname: "Convertidor de Tablas Online"
  name: "TableConvert"
  subtitle: "Convertidor y Generador de Tablas Online Gratuito"
  intro: "TableConvert es una herramienta gratuita online de conversión de tablas y generación de datos que soporta conversión entre más de 30 formatos incluyendo Excel, CSV, JSON, Markdown, LaTeX, SQL y más."
  followTwitter: "Síguenos en X"
title:
  converter: "%s a %s"
  generator: "Generador %s"
post:
  tags:
    converter: "Convertidor"
    editor: "Editor"
    generator: "Generador"
    maker: "Constructor"
  converter:
    title: "Convertir %s a %s Online"
    short: "Una herramienta online gratuita y poderosa de %s a %s"
    intro: "Convertidor online de %s a %s fácil de usar. Transforma datos de tablas sin esfuerzo con nuestra herramienta de conversión intuitiva. Rápido, confiable y fácil de usar."
  generator:
    title: "Editor y Generador %s Online"
    short: "Herramienta profesional de generación online %s con características completas"
    intro: "Generador %s online y editor de tablas fácil de usar. Crea tablas de datos profesionales sin esfuerzo con nuestra herramienta intuitiva y vista previa en tiempo real."
navbar:
  search:
    placeholder: "Buscar convertidor..."
  sponsor: "Cómpranos un Café"
  extension: "Extensión"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Fuente de Datos"
    placeholder: "Pega tus datos %s o arrastra archivos %s aquí"
    example: "Ejemplo"
    upload: "Subir Archivo"
    extract:
      enter: "Extraer de Página Web"
      intro: "Ingresa una URL de página web que contenga datos de tabla para extraer automáticamente datos estructurados"
      btn: "Extraer %s"
    excel:
      sheet: "Hoja de Trabajo"
      none: "Ninguno"
  tableEditor:
    title: "Editor de Tablas Online"
    undo: "Deshacer"
    redo: "Rehacer"
    transpose: "Transponer"
    clear: "Limpiar"
    deleteBlank: "Eliminar Vacíos"
    deleteDuplicate: "Eliminar Duplicados"
    uppercase: "MAYÚSCULAS"
    lowercase: "minúsculas"
    capitalize: "Capitalizar"
    replace:
      replace: "Buscar y Reemplazar (Regex soportado)"
      subst: "Reemplazar con..."
      btn: "Reemplazar Todo"
  tableGenerator:
    title: "Generador de Tablas"
    sponsor: "Cómpranos un Café"
    copy: "Copiar al Portapapeles"
    download: "Descargar Archivo"
    tooltip:
      html:
        escape: "Escapar caracteres especiales HTML (&, <, >, \", ') para prevenir errores de visualización"
        div: "Usar diseño DIV+CSS en lugar de etiquetas TABLE tradicionales, mejor adaptado para diseño responsivo"
        minify: "Eliminar espacios en blanco y saltos de línea para generar código HTML comprimido"
        thead: "Generar estructura estándar de cabeza de tabla (&lt;thead&gt;) y cuerpo (&lt;tbody&gt;)"
        tableCaption: "Agregar título descriptivo arriba de la tabla (elemento &lt;caption&gt;)"
        tableClass: "Agregar nombre de clase CSS a la tabla para fácil personalización de estilo"
        tableId: "Establecer identificador ID único para la tabla para manipulación JavaScript"
      jira:
        escape: "Escapar caracteres de tubería (|) para evitar conflictos con sintaxis de tabla Jira"
      json:
        parsingJSON: "Analizar inteligentemente cadenas JSON en celdas en objetos"
        minify: "Generar formato JSON compacto de una línea para reducir tamaño de archivo"
        format: "Seleccionar estructura de datos JSON de salida: array de objetos, array 2D, etc."
      latex:
        escape: "Escapar caracteres especiales LaTeX (%, &, _, #, $, etc.) para asegurar compilación adecuada"
        ht: "Agregar parámetro de posición flotante [!ht] para controlar posición de tabla en página"
        mwe: "Generar documento LaTeX completo"
        tableAlign: "Establecer alineación horizontal de tabla en la página"
        tableBorder: "Configurar estilo de borde de tabla: sin borde, borde parcial, borde completo"
        label: "Establecer etiqueta de tabla para referencias cruzadas del comando \\ref{}"
        caption: "Establecer título de tabla para mostrar arriba o abajo de la tabla"
        location: "Elegir posición de visualización del título de tabla: arriba o abajo"
        tableType: "Elegir tipo de entorno de tabla: tabular, longtable, array, etc."
      markdown:
        escape: "Escapar caracteres especiales Markdown (*, _, |, \\, etc.) para evitar conflictos de formato"
        pretty: "Auto-alinear anchos de columna para generar formato de tabla más hermoso"
        simple: "Usar sintaxis simplificada, omitiendo líneas verticales de borde exterior"
        boldFirstRow: "Hacer el texto de la primera fila en negrita"
        boldFirstColumn: "Hacer el texto de la primera columna en negrita"
        firstHeader: "Tratar primera fila como encabezado y agregar línea separadora"
        textAlign: "Establecer alineación de texto de columna: izquierda, centro, derecha"
        multilineHandling: "Manejo de texto multilínea: preservar saltos de línea, escapar a \\n, usar etiquetas &lt;br&gt;"

        includeLineNumbers: "Agregar columna de números de línea en el lado izquierdo de la tabla"
      magic:
        builtin: "Seleccionar formatos de plantilla común predefinidos"
        rowsTpl: "<table> <tr> <th>Sintaxis Mágica</th> <th>Descripción</th> <th>Métodos JS Soportados</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>1er, 2do ... campo del <b>e</b>ncabezado, También {hA} {hB} ...</td> <td>Métodos de cadena</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>1er, 2do ... campo de la fila actual, También {$A} {$B} ...</td> <td>Métodos de cadena</td> </tr> <tr> <td>{F,} {F;}</td> <td>Dividir la fila actual por la cadena después de <b>F</b></td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td><b>N</b>úmero de línea de la <b>f</b>ila actual desde 1 o 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>N</b>úmero de línea <b>f</b>inal de las <b>f</b>ilas </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>E<b>j</b>ecutar código JavaScript, ej: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Usar barra invertida <b>\\</b> para mostrar llaves {...} </td> <td></td> </tr></table>"
        headerTpl: "Plantilla de salida personalizada para sección de encabezado"
        footerTpl: "Plantilla de salida personalizada para sección de pie"
      textile:
        escape: "Escapar caracteres de sintaxis Textile (|, ., -, ^) para evitar conflictos de formato"
        rowHeader: "Establecer primera fila como fila de encabezado"
        thead: "Agregar marcadores de sintaxis Textile para cabeza y cuerpo de tabla"
      xml:
        escape: "Escapar caracteres especiales XML (&lt;, &gt;, &amp;, \", ') para asegurar XML válido"
        minify: "Generar salida XML comprimida, eliminando espacios en blanco extra"
        rootElement: "Establecer nombre de etiqueta del elemento raíz XML"
        rowElement: "Establecer nombre de etiqueta del elemento XML para cada fila de datos"
        declaration: "Agregar encabezado de declaración XML (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Mostrar datos como atributos XML en lugar de elementos hijo"
        cdata: "Envolver contenido de texto con CDATA para proteger caracteres especiales"
        encoding: "Establecer formato de codificación de caracteres para documento XML"
        indentation: "Elegir carácter de sangría XML: espacios o tabs"
      yaml:
        indentSize: "Establecer número de espacios para sangría de jerarquía YAML (usualmente 2 o 4)"
        arrayStyle: "Formato de array: bloque (un elemento por línea) o flujo (formato en línea)"
        quotationStyle: "Estilo de comillas de cadena: sin comillas, comillas simples, comillas dobles"
      pdf:
        theme: "Elegir estilo visual de tabla PDF para documentos profesionales"
        headerColor: "Elegir color de fondo de encabezado de tabla PDF"
        showHead: "Controlar visualización de encabezado en páginas PDF"
        docTitle: "Título opcional para el documento PDF"
        docDescription: "Texto de descripción opcional para documento PDF"
      csv:
        bom: "Agregar marca de orden de bytes UTF-8 para ayudar a Excel y otro software a reconocer codificación"
      excel:
        autoWidth: "Ajustar automáticamente ancho de columna basado en contenido"
        protectSheet: "Habilitar protección de hoja de trabajo con contraseña: tableconvert.com"
      sql:
        primaryKey: "Especificar nombre de campo de clave primaria para declaración CREATE TABLE"
        dialect: "Seleccionar tipo de base de datos, afectando sintaxis de comillas y tipo de datos"
      ascii:
        forceSep: "Forzar líneas separadoras entre cada fila de datos"
        style: "Seleccionar estilo de dibujo de borde de tabla ASCII"
        comment: "Agregar marcadores de comentario para envolver toda la tabla"
      mediawiki:
        minify: "Comprimir código de salida, eliminando espacios en blanco extra"
        header: "Marcar primera fila como estilo de encabezado"
        sort: "Habilitar funcionalidad de ordenamiento por clic en tabla"
      asciidoc:
        minify: "Comprimir salida de formato AsciiDoc"
        firstHeader: "Establecer primera fila como fila de encabezado"
        lastFooter: "Establecer última fila como fila de pie"
        title: "Agregar texto de título a la tabla"
      tracwiki:
        rowHeader: "Establecer primera fila como encabezado"
        colHeader: "Establecer primera columna como encabezado"
      bbcode:
        minify: "Comprimir formato de salida BBCode"
      restructuredtext:
        style: "Seleccionar estilo de borde de tabla reStructuredText"
        forceSep: "Forzar líneas separadoras"
    label:
      ascii:
        forceSep: "Separadores de Fila"
        style: "Estilo de Borde"
        comment: "Envoltorio de Comentario"
      restructuredtext:
        style: "Estilo de Borde"
        forceSep: "Forzar Separadores"
      bbcode:
        minify: "Comprimir Salida"
      csv:
        doubleQuote: "Comillas Dobles"
        delimiter: "Delimitador de Campo"
        bom: "UTF-8 BOM"
        valueDelimiter: "Delimitador de Valor"
        rowDelimiter: "Delimitador de Fila"
        prefix: "Prefijo de Fila"
        suffix: "Sufijo de Fila"
      excel:
        autoWidth: "Ancho Automático"
        textFormat: "Formato de Texto"
        protectSheet: "Proteger Hoja"
        boldFirstRow: "Primera Fila en Negrita"
        boldFirstColumn: "Primera Columna en Negrita"
        sheetName: "Nombre de Hoja"
      html:
        escape: "Escapar Caracteres HTML"
        div: "Tabla DIV"
        minify: "Comprimir Código"
        thead: "Estructura de Cabecera"
        tableCaption: "Título de Tabla"
        tableClass: "Clase de Tabla"
        tableId: "ID de Tabla"
        rowHeader: "Cabecera de Fila"
        colHeader: "Cabecera de Columna"
      jira:
        escape: "Escapar Caracteres"
        rowHeader: "Cabecera de Fila"
        colHeader: "Cabecera de Columna"
      json:
        parsingJSON: "Analizar JSON"
        minify: "Comprimir Salida"
        format: "Formato de Datos"
        rootName: "Nombre de Objeto Raíz"
        indentSize: "Tamaño de Sangría"
      jsonlines:
        parsingJSON: "Analizar JSON"
        format: "Formato de Datos"
      latex:
        escape: "Escapar Caracteres LaTeX"
        ht: "Posición Flotante"
        mwe: "Documento Completo"
        tableAlign: "Alineación de Tabla"
        tableBorder: "Estilo de Borde"
        label: "Etiqueta de Referencia"
        caption: "Título de Tabla"
        location: "Posición del Título"
        tableType: "Tipo de Tabla"
        boldFirstRow: "Primera Fila en Negrita"
        boldFirstColumn: "Primera Columna en Negrita"
        textAlign: "Alineación de Texto"
        borders: "Configuración de Bordes"
      markdown:
        escape: "Escapar Caracteres"
        pretty: "Tabla Markdown Elegante"
        simple: "Formato Markdown Simple"
        boldFirstRow: "Primera Fila en Negrita"
        boldFirstColumn: "Primera Columna en Negrita"
        firstHeader: "Primera Cabecera"
        textAlign: "Alineación de Texto"
        multilineHandling: "Manejo Multilínea"

        includeLineNumbers: "Agregar Números de Línea"
        align: "Alineación"
      mediawiki:
        minify: "Comprimir Código"
        header: "Marcado de Cabecera"
        sort: "Ordenable"
      asciidoc:
        minify: "Comprimir Formato"
        firstHeader: "Primera Cabecera"
        lastFooter: "Último Pie"
        title: "Título de Tabla"
      tracwiki:
        rowHeader: "Cabecera de Fila"
        colHeader: "Cabecera de Columna"
      sql:
        drop: "Eliminar Tabla (Si Existe)"
        create: "Crear Tabla"
        oneInsert: "Inserción por Lotes"
        table: "Nombre de Tabla"
        dialect: "Tipo de Base de Datos"
        primaryKey: "Clave Primaria"
      magic:
        builtin: "Plantilla Incorporada"
        rowsTpl: "Plantilla de Fila, Sintaxis ->"
        headerTpl: "Plantilla de Cabecera"
        footerTpl: "Plantilla de Pie"
      textile:
        escape: "Escapar Caracteres"
        rowHeader: "Cabecera de Fila"
        thead: "Sintaxis de Cabecera"
      xml:
        escape: "Escapar Caracteres XML"
        minify: "Comprimir Salida"
        rootElement: "Elemento Raíz"
        rowElement: "Elemento de Fila"
        declaration: "Declaración XML"
        attributes: "Modo de Atributos"
        cdata: "Envoltorio CDATA"
        encoding: "Codificación"
        indentSize: "Tamaño de Sangría"
      yaml:
        indentSize: "Tamaño de Sangría"
        arrayStyle: "Estilo de Array"
        quotationStyle: "Estilo de Comillas"
      pdf:
        theme: "Tema de Tabla PDF"
        headerColor: "Color de Encabezado PDF"
        showHead: "Visualización de Encabezado PDF"
        docTitle: "Título de Documento PDF"
        docDescription: "Descripción de Documento PDF"
sidebar:
  all: "Todas las Herramientas de Conversión"
  dataSource:
    title: "Fuente de Datos"
    description:
      converter: "Importa %s para conversión a %s. Soporta subida de archivos, edición online, y extracción de datos web."
      generator: "Crea datos de tabla con soporte para múltiples métodos de entrada incluyendo entrada manual, importación de archivos, y generación de plantillas."
  tableEditor:
    title: "Editor de Tablas Online"
    description:
      converter: "Procesa %s online usando nuestro editor de tablas. Experiencia de operación similar a Excel con soporte para eliminar filas vacías, deduplicación, ordenamiento, y buscar y reemplazar."
      generator: "Poderoso editor de tablas online que proporciona experiencia de operación similar a Excel. Soporta eliminar filas vacías, deduplicación, ordenamiento, y buscar y reemplazar."
  tableGenerator:
    title: "Generador de Tablas"
    description:
      converter: "Genera rápidamente %s con vista previa en tiempo real del generador de tablas. Opciones de exportación ricas, copia y descarga con un clic."
      generator: "Exporta datos %s en múltiples formatos para satisfacer diferentes escenarios de uso. Soporta opciones personalizadas y vista previa en tiempo real."
footer:
  changelog: "Registro de Cambios"
  sponsor: "Patrocinadores"
  contact: "Contáctanos"
  privacyPolicy: "Política de Privacidad"
  about: "Acerca de"
  resources: "Recursos"
  popularConverters: "Convertidores Populares"
  popularGenerators: "Generadores Populares"
  dataSecurity: "Tus datos están seguros - todas las conversiones se ejecutan en tu navegador."
converters:
  Markdown:
    alias: "Tabla Markdown"
    what: "Markdown es un lenguaje de marcado ligero ampliamente usado para documentación técnica, creación de contenido de blog, y desarrollo web. Su sintaxis de tabla es concisa e intuitiva, soportando alineación de texto, incrustación de enlaces, y formateo. Es la herramienta preferida para programadores y escritores técnicos, perfectamente compatible con GitHub, GitLab, y otras plataformas de alojamiento de código."
    step1: "Pega datos de tabla Markdown en el área de fuente de datos, o arrastra y suelta directamente archivos .md para subir. La herramienta analiza automáticamente la estructura y formateo de la tabla, soportando contenido anidado complejo y manejo de caracteres especiales."
    step3: "Genera código de tabla Markdown estándar en tiempo real, soportando múltiples métodos de alineación, negrita de texto, adición de números de línea, y otras configuraciones de formato avanzadas. El código generado es completamente compatible con GitHub y los principales editores Markdown, listo para usar con copia de un clic."
    from_alias: "Archivo de Tabla Markdown"
    to_alias: "Formato de Tabla Markdown"
  Magic:
    alias: "Plantilla Personalizada"
    what: "La plantilla Magic es un generador de datos avanzado único de esta herramienta, permitiendo a los usuarios crear salida de datos de formato arbitrario a través de sintaxis de plantilla personalizada. Soporta reemplazo de variables, juicio condicional, y procesamiento de bucles. Es la solución definitiva para manejar necesidades complejas de conversión de datos y formatos de salida personalizados, especialmente adecuado para desarrolladores e ingenieros de datos."
    step1: "Selecciona plantillas comunes incorporadas o crea sintaxis de plantilla personalizada. Soporta variables y funciones ricas que pueden manejar estructuras de datos complejas y lógica de negocio."
    step3: "Genera salida de datos que cumple completamente con los requisitos de formato personalizado. Soporta lógica de conversión de datos compleja y procesamiento condicional, mejorando enormemente la eficiencia de procesamiento de datos y la calidad de salida. Una herramienta poderosa para procesamiento de datos por lotes."
    from_alias: "Datos de Tabla"
    to_alias: "Salida de Formato Personalizado"
  CSV:
    alias: "CSV"
    what: "CSV (Valores Separados por Comas) es el formato de intercambio de datos más ampliamente usado, perfectamente soportado por Excel, Google Sheets, sistemas de base de datos, y varias herramientas de análisis de datos. Su estructura simple y fuerte compatibilidad lo convierten en el formato estándar para migración de datos, importación/exportación por lotes, e intercambio de datos multiplataforma, ampliamente usado en análisis de negocio, ciencia de datos, e integración de sistemas."
    step1: "Sube archivos CSV o pega directamente datos CSV. La herramienta reconoce inteligentemente varios delimitadores (coma, tab, punto y coma, pipe, etc.), detecta automáticamente tipos de datos y formatos de codificación, soportando análisis rápido de archivos grandes y estructuras de datos complejas."
    step3: "Genera archivos de formato CSV estándar con soporte para delimitadores personalizados, estilos de comillas, formatos de codificación, y configuraciones de marca BOM. Asegura compatibilidad perfecta con sistemas objetivo, proporcionando opciones de descarga y compresión para satisfacer necesidades de procesamiento de datos a nivel empresarial."
    from_alias: "Archivo de Datos CSV"
    to_alias: "Formato CSV Estándar"
  JSON:
    alias: "Array JSON"
    what: "JSON (JavaScript Object Notation) es el formato de datos de tabla estándar para aplicaciones web modernas, APIs REST, y arquitecturas de microservicios. Su estructura clara y análisis eficiente lo hacen ampliamente usado en interacción de datos front-end y back-end, almacenamiento de archivos de configuración, y bases de datos NoSQL. Soporta objetos anidados, estructuras de array, y múltiples tipos de datos, convirtiéndolo en datos de tabla indispensables para el desarrollo de software moderno."
    step1: "Sube archivos JSON o pega arrays JSON. Soporta reconocimiento automático y análisis de arrays de objetos, estructuras anidadas, y tipos de datos complejos. La herramienta valida inteligentemente la sintaxis JSON y proporciona avisos de error."
    step3: "Genera múltiples salidas de formato JSON: arrays de objetos estándar, arrays 2D, arrays de columnas, y formatos de pares clave-valor. Soporta salida embellecida, modo de compresión, nombres de objetos raíz personalizados, y configuraciones de sangría, adaptándose perfectamente a varias interfaces API y necesidades de almacenamiento de datos."
    from_alias: "Archivo de Array JSON"
    to_alias: "Formato JSON Estándar"
  JSONLines:
    alias: "Formato JSONLines"
    what: "JSON Lines (también conocido como NDJSON) es un formato importante para procesamiento de big data y transmisión de datos en streaming, con cada línea conteniendo un objeto JSON independiente. Ampliamente usado en análisis de logs, procesamiento de flujos de datos, aprendizaje automático, y sistemas distribuidos. Soporta procesamiento incremental y computación paralela, convirtiéndolo en la elección ideal para manejar datos estructurados a gran escala."
    step1: "Sube archivos JSONLines o pega datos. La herramienta analiza objetos JSON línea por línea, soportando procesamiento de streaming de archivos grandes y funcionalidad de salto de líneas de error."
    step3: "Genera formato JSONLines estándar con cada línea generando un objeto JSON completo. Adecuado para procesamiento de streaming, importación por lotes, y escenarios de análisis de big data, soportando validación de datos y optimización de formato."
    from_alias: "Datos JSONLines"
    to_alias: "Formato de Streaming JSONLines"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) es el formato estándar para intercambio de datos y gestión de configuración a nivel empresarial, con especificaciones de sintaxis estrictas y mecanismos de validación poderosos. Ampliamente usado en servicios web, archivos de configuración, almacenamiento de documentos, e integración de sistemas. Soporta espacios de nombres, validación de esquemas, y transformación XSLT, convirtiéndolo en datos de tabla importantes para aplicaciones empresariales."
    step1: "Sube archivos XML o pega datos XML. La herramienta analiza automáticamente la estructura XML y la convierte a formato de tabla, soportando espacio de nombres, manejo de atributos, y estructuras anidadas complejas."
    step3: "Genera salida XML que cumple con estándares XML. Soporta elementos raíz personalizados, nombres de elementos de fila, modos de atributos, envoltorio CDATA, y configuraciones de codificación de caracteres. Asegura integridad y compatibilidad de datos, cumpliendo requisitos de aplicaciones a nivel empresarial."
    from_alias: "Archivo de Datos XML"
    to_alias: "Formato XML Estándar"
  YAML:
    alias: "Configuración YAML"
    what: "YAML es un estándar de serialización de datos amigable para humanos, reconocido por su estructura jerárquica clara y sintaxis concisa. Ampliamente usado en archivos de configuración, cadenas de herramientas DevOps, Docker Compose, y despliegue de Kubernetes. Su fuerte legibilidad y sintaxis concisa lo convierten en un formato de configuración importante para aplicaciones cloud-native modernas y operaciones automatizadas."
    step1: "Sube archivos YAML o pega datos YAML. La herramienta analiza inteligentemente la estructura YAML y valida la corrección de sintaxis, soportando formatos multi-documento y tipos de datos complejos."
    step3: "Genera salida de formato YAML estándar con soporte para estilos de array de bloque y flujo, múltiples configuraciones de comillas, sangría personalizada, y preservación de comentarios. Asegura que los archivos YAML de salida sean completamente compatibles con varios analizadores y sistemas de configuración."
    from_alias: "Archivo de Configuración YAML"
    to_alias: "Formato YAML Estándar"
  MySQL:
      alias: "Resultados de Consulta MySQL"
      what: "MySQL es el sistema de gestión de bases de datos relacionales de código abierto más popular del mundo, reconocido por su alto rendimiento, confiabilidad y facilidad de uso. Ampliamente usado en aplicaciones web, sistemas empresariales y plataformas de análisis de datos. Los resultados de consulta MySQL típicamente contienen datos de tabla estructurados, sirviendo como una fuente de datos importante en gestión de bases de datos y trabajo de análisis de datos."
      step1: "Pega resultados de salida de consulta MySQL en el área de fuente de datos. La herramienta reconoce automáticamente y analiza el formato de salida de línea de comandos MySQL, soportando varios estilos de resultados de consulta y codificaciones de caracteres, manejando inteligentemente encabezados y filas de datos."
      step3: "Convierte rápidamente resultados de consulta MySQL a múltiples formatos de datos de tabla, facilitando análisis de datos, generación de reportes, migración de datos entre sistemas, y validación de datos. Una herramienta práctica para administradores de bases de datos y analistas de datos."
      from_alias: "Salida de Consulta MySQL"
      to_alias: "Datos de Tabla MySQL"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) es el lenguaje de operación estándar para bases de datos relacionales, usado para operaciones de consulta, inserción, actualización y eliminación de datos. Como la tecnología central de gestión de bases de datos, SQL es ampliamente usado en análisis de datos, inteligencia de negocio, procesamiento ETL, y construcción de almacenes de datos. Es una herramienta de habilidad esencial para profesionales de datos."
    step1: "Pega declaraciones INSERT SQL o sube archivos .sql. La herramienta analiza inteligentemente sintaxis SQL y extrae datos de tabla, soportando múltiples dialectos SQL y procesamiento de declaraciones de consulta complejas."
    step3: "Genera declaraciones INSERT SQL estándar y declaraciones de creación de tabla. Soporta múltiples dialectos de base de datos (MySQL, PostgreSQL, SQLite, SQL Server, Oracle), maneja automáticamente mapeo de tipos de datos, escape de caracteres, y restricciones de clave primaria. Asegura que el código SQL generado pueda ser ejecutado directamente."
    from_alias: "Archivo de Datos SQL"
    to_alias: "Declaración SQL Estándar"
  Qlik:
      alias: "Tabla Qlik"
      what: "Qlik es un proveedor de software especializado en visualización de datos, paneles ejecutivos, y productos de inteligencia de negocio de autoservicio, junto con Tableau y Microsoft."
      step1: ""
      step3: "Finalmente, el [Generador de Tablas](#TableGenerator) muestra los resultados de conversión. Úsalo en tu Qlik Sense, Qlik AutoML, QlikView, u otro software habilitado para Qlik."
      from_alias: "Tabla Qlik"
      to_alias: "Tabla Qlik"
  DAX:
      alias: "Tabla DAX"
      what: "DAX (Data Analysis Expressions) es un lenguaje de programación usado en Microsoft Power BI para crear columnas calculadas, medidas, y tablas personalizadas."
      step1: ""
      step3: "Finalmente, el [Generador de Tablas](#TableGenerator) muestra los resultados de conversión. Como se esperaba, se usa en varios productos de Microsoft incluyendo Microsoft Power BI, Microsoft Analysis Services, y Microsoft Power Pivot para Excel."
      from_alias: "Tabla DAX"
      to_alias: "Tabla DAX"
  Firebase:
    alias: "Lista Firebase"
    what: "Firebase es una plataforma de desarrollo de aplicaciones BaaS que proporciona servicios backend alojados como base de datos en tiempo real, almacenamiento en la nube, autenticación, reporte de fallos, etc."
    step1: ""
    step3: "Finalmente, el [Generador de Tablas](#TableGenerator) muestra los resultados de conversión. Luego puedes usar el método push en la API de Firebase para agregar a una lista de datos en la base de datos Firebase."
    from_alias: "Lista Firebase"
    to_alias: "Lista Firebase"
  HTML:
    alias: "Tabla HTML"
    what: "Las tablas HTML son la forma estándar de mostrar datos estructurados en páginas web, construidas con etiquetas table, tr, td y otras. Soporta personalización de estilo rica, diseño responsivo, y funcionalidad interactiva. Ampliamente usado en desarrollo de sitios web, visualización de datos, y generación de reportes, sirviendo como un componente importante del desarrollo front-end y diseño web."
    step1: "Pega código HTML que contenga tablas o sube archivos HTML. La herramienta reconoce automáticamente y extrae datos de tabla de páginas, soportando estructuras HTML complejas, estilos CSS, y procesamiento de tablas anidadas."
    step3: "Genera código de tabla HTML semántico con soporte para estructura thead/tbody, configuraciones de clases CSS, títulos de tabla, encabezados de fila/columna, y configuración de atributos responsivos. Asegura que el código de tabla generado cumpla con estándares web con buena accesibilidad y amigabilidad SEO."
    from_alias: "Tabla Web HTML"
    to_alias: "Tabla HTML Estándar"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel es el software de hoja de cálculo más popular del mundo, ampliamente usado en análisis de negocio, gestión financiera, procesamiento de datos, y creación de reportes. Sus poderosas capacidades de procesamiento de datos, rica biblioteca de funciones, y características de visualización flexibles lo convierten en la herramienta estándar para automatización de oficina y análisis de datos, con aplicaciones extensas en casi todas las industrias y campos."
    step1: "Sube archivos Excel (soporta formatos .xlsx, .xls) o copia datos de tabla directamente desde Excel y pega. La herramienta soporta procesamiento multi-hoja, reconocimiento de formato complejo, y análisis rápido de archivos grandes, manejando automáticamente celdas fusionadas y tipos de datos."
    step3: "Genera datos de tabla compatibles con Excel que pueden ser pegados directamente en Excel o descargados como archivos .xlsx estándar. Soporta nomenclatura de hojas de trabajo, formato de celdas, ancho de columna automático, estilo de encabezados, y configuraciones de validación de datos. Asegura que los archivos Excel de salida tengan apariencia profesional y funcionalidad completa."
    from_alias: "Hoja de Cálculo Excel"
    to_alias: "Formato Excel Estándar"
  LaTeX:
    alias: "Tabla LaTeX"
    what: "LaTeX es un sistema profesional de composición de documentos, especialmente adecuado para crear artículos académicos, documentos técnicos y publicaciones científicas. Su funcionalidad de tabla es poderosa, soportando fórmulas matemáticas complejas, control de diseño preciso, y salida PDF de alta calidad. Es la herramienta estándar en academia y publicación científica, ampliamente usado en artículos de revistas, disertaciones, y composición de manuales técnicos."
    step1: "Pega código de tabla LaTeX o sube archivos .tex. La herramienta analiza sintaxis de tabla LaTeX y extrae contenido de datos, soportando múltiples entornos de tabla (tabular, longtable, array, etc.) y comandos de formato complejos."
    step3: "Genera código de tabla LaTeX profesional con soporte para selección de múltiples entornos de tabla, configuración de estilo de borde, configuraciones de posición de título, especificación de clase de documento, y gestión de paquetes. Puede generar documentos LaTeX compilables completos, asegurando que las tablas de salida cumplan estándares de publicación académica."
    from_alias: "Tabla de Documento LaTeX"
    to_alias: "Formato Profesional LaTeX"
  ASCII:
    alias: "Tabla ASCII"
    what: "Las tablas ASCII usan caracteres de texto plano para dibujar bordes y estructuras de tabla, proporcionando la mejor compatibilidad y portabilidad. Compatible con todos los editores de texto, entornos de terminal, y sistemas operativos. Ampliamente usado en documentación de código, manuales técnicos, archivos README, y salida de herramientas de línea de comandos. El formato de visualización de datos preferido para programadores y administradores de sistemas."
    step1: "Sube archivos de texto que contengan tablas ASCII o pega directamente datos de tabla. La herramienta reconoce inteligentemente y analiza estructuras de tabla ASCII, soportando múltiples estilos de borde y formatos de alineación."
    step3: "Genera hermosas tablas ASCII de texto plano con soporte para múltiples estilos de borde (línea simple, línea doble, esquinas redondeadas, etc.), métodos de alineación de texto, y ancho de columna automático. Las tablas generadas se muestran perfectamente en editores de código, documentos, y líneas de comandos."
    from_alias: "Tabla de Texto ASCII"
    to_alias: "Formato ASCII Estándar"
  MediaWiki:
    alias: "Tabla MediaWiki"
    what: "MediaWiki es la plataforma de software de código abierto usada por sitios wiki famosos como Wikipedia. Su sintaxis de tabla es concisa pero poderosa, soportando personalización de estilo de tabla, funcionalidad de ordenamiento, e incrustación de enlaces. Ampliamente usado en gestión de conocimiento, edición colaborativa, y sistemas de gestión de contenido, sirviendo como tecnología central para construir enciclopedias wiki y bases de conocimiento."
    step1: "Pega código de tabla MediaWiki o sube archivos fuente wiki. La herramienta analiza sintaxis de marcado wiki y extrae datos de tabla, soportando sintaxis wiki compleja y procesamiento de plantillas."
    step3: "Genera código de tabla MediaWiki estándar con soporte para configuraciones de estilo de encabezado, alineación de celdas, habilitación de funcionalidad de ordenamiento, y opciones de compresión de código. El código generado puede ser usado directamente para edición de páginas wiki, asegurando visualización perfecta en plataformas MediaWiki."
    from_alias: "Código Fuente MediaWiki"
    to_alias: "Sintaxis de Tabla MediaWiki"
  TracWiki:
    alias: "Tabla TracWiki"
    what: "Trac es un sistema de gestión de proyectos y seguimiento de errores basado en web que usa sintaxis wiki simplificada para crear contenido de tabla."
    step1: "Sube archivos TracWiki o pega datos de tabla."
    step3: "Genera código de tabla compatible con TracWiki con soporte para configuraciones de encabezado de fila/columna, facilitando la gestión de documentos de proyecto."
    from_alias: "Tabla TracWiki"
    to_alias: "Formato TracWiki"
  AsciiDoc:
    alias: "Tabla AsciiDoc"
    what: "AsciiDoc es un lenguaje de marcado ligero que puede ser convertido a HTML, PDF, páginas de manual, y otros formatos, ampliamente usado para escritura de documentación técnica."
    step1: "Sube archivos AsciiDoc o pega datos."
    step3: "Genera sintaxis de tabla AsciiDoc con soporte para configuraciones de encabezado, pie de página, y título, directamente usable en editores AsciiDoc."
    from_alias: "Tabla AsciiDoc"
    to_alias: "Formato AsciiDoc"
  reStructuredText:
    alias: "Tabla reStructuredText"
    what: "reStructuredText es el formato de documentación estándar para la comunidad Python, soportando sintaxis de tabla rica, comúnmente usado para generación de documentación Sphinx."
    step1: "Sube archivos .rst o pega datos reStructuredText."
    step3: "Genera tablas reStructuredText estándar con soporte para múltiples estilos de borde, directamente usables en proyectos de documentación Sphinx."
    from_alias: "Tabla reStructuredText"
    to_alias: "Formato reStructuredText"
  PHP:
    alias: "Array PHP"
    what: "PHP es un lenguaje de scripting del lado del servidor popular, con arrays siendo su estructura de datos central, ampliamente utilizado en desarrollo web y procesamiento de datos."
    step1: "Sube archivos que contengan arrays PHP o pega datos directamente."
    step3: "Genera código de array PHP estándar que puede ser usado directamente en proyectos PHP, soportando formatos de array asociativo e indexado."
    from_alias: "Array PHP"
    to_alias: "Código PHP"
  Ruby:
    alias: "Array Ruby"
    what: "Ruby es un lenguaje de programación orientado a objetos dinámico con sintaxis concisa y elegante, con arrays siendo una estructura de datos importante."
    step1: "Sube archivos Ruby o pega datos de array."
    step3: "Genera código de array Ruby que cumple con las especificaciones de sintaxis Ruby, directamente utilizable en proyectos Ruby."
    from_alias: "Array Ruby"
    to_alias: "Código Ruby"
  ASP:
    alias: "Array ASP"
    what: "ASP (Active Server Pages) es el entorno de scripting del lado del servidor de Microsoft, que soporta múltiples lenguajes de programación para desarrollar páginas web dinámicas."
    step1: "Sube archivos ASP o pega datos de array."
    step3: "Genera código de array compatible con ASP con soporte para sintaxis VBScript y JScript, utilizable en proyectos ASP.NET."
    from_alias: "Array ASP"
    to_alias: "Código ASP"
  ActionScript:
    alias: "Array ActionScript"
    what: "ActionScript es un lenguaje de programación orientado a objetos utilizado principalmente para el desarrollo de aplicaciones Adobe Flash y AIR."
    step1: "Sube archivos .as o pega datos ActionScript."
    step3: "Genera código de array ActionScript que cumple con los estándares de sintaxis AS3, utilizable para desarrollo de proyectos Flash y Flex."
    from_alias: "Array ActionScript"
    to_alias: "Código ActionScript"
  BBCode:
    alias: "Tabla BBCode"
    what: "BBCode es un lenguaje de marcado ligero comúnmente usado en foros y comunidades en línea, proporcionando funcionalidad de formato simple incluyendo soporte para tablas."
    step1: "Sube archivos que contengan BBCode o pega datos."
    step3: "Genera código de tabla BBCode adecuado para publicaciones en foros y creación de contenido comunitario, con soporte para formato de salida comprimido."
    from_alias: "Tabla BBCode"
    to_alias: "Formato BBCode"
  PDF:
    alias: "Tabla PDF"
    what: "PDF (Portable Document Format) es un estándar de documento multiplataforma con diseño fijo, visualización consistente y características de impresión de alta calidad. Ampliamente utilizado en documentos formales, informes, facturas, contratos y artículos académicos. El formato preferido para comunicación empresarial y archivo de documentos, asegurando efectos visuales completamente consistentes en diferentes dispositivos y sistemas operativos."
    step1: "Importa datos de tabla en cualquier formato. La herramienta analiza automáticamente la estructura de datos y realiza diseño de diseño inteligente, soportando auto-paginación de tablas grandes y procesamiento de tipos de datos complejos."
    step3: "Genera archivos de tabla PDF de alta calidad con soporte para múltiples estilos de tema profesional (empresarial, académico, minimalista, etc.), fuentes multilingües, auto-paginación, adición de marca de agua y optimización de impresión. Asegura que los documentos PDF de salida tengan apariencia profesional, directamente utilizables para presentaciones empresariales y publicación formal."
    from_alias: "Datos de Tabla"
    to_alias: "Documento PDF Profesional"
  JPEG:
    alias: "Imagen JPEG"
    what: "JPEG es el formato de imagen digital más ampliamente utilizado con excelentes efectos de compresión y amplia compatibilidad. Su pequeño tamaño de archivo y velocidad de carga rápida lo hacen adecuado para visualización web, compartir en redes sociales, ilustraciones de documentos y presentaciones en línea. El formato de imagen estándar para medios digitales y comunicación de red, perfectamente soportado por casi todos los dispositivos y software."
    step1: "Importa datos de tabla en cualquier formato. La herramienta realiza diseño de diseño inteligente y optimización visual, calculando automáticamente el tamaño y resolución óptimos."
    step3: "Genera imágenes de tabla JPEG de alta definición con soporte para múltiples esquemas de color de tema (claro, oscuro, amigable para los ojos, etc.), diseño adaptativo, optimización de claridad de texto y personalización de tamaño. Adecuado para compartir en línea, inserción de documentos y uso de presentación, asegurando excelentes efectos visuales en varios dispositivos de visualización."
    from_alias: "Datos de Tabla"
    to_alias: "Imagen JPEG de Alta Definición"
  Jira:
    alias: "Tabla Jira"
    what: "JIRA es software profesional de gestión de proyectos y seguimiento de errores desarrollado por Atlassian, ampliamente utilizado en desarrollo ágil, pruebas de software y colaboración de proyectos. Su funcionalidad de tabla soporta opciones de formato ricas y visualización de datos, sirviendo como una herramienta importante para equipos de desarrollo de software, gerentes de proyecto y personal de aseguramiento de calidad en gestión de requisitos, seguimiento de errores y reporte de progreso."
    step1: "Sube archivos que contengan datos de tabla o pega contenido de datos directamente. La herramienta procesa automáticamente datos de tabla y escape de caracteres especiales."
    step3: "Genera código de tabla compatible con la plataforma JIRA con soporte para configuraciones de estilo de encabezado, alineación de celdas, procesamiento de escape de caracteres y optimización de formato. El código generado puede ser pegado directamente en descripciones de issues JIRA, comentarios o páginas wiki, asegurando visualización y renderizado correcto en sistemas JIRA."
    from_alias: "Datos de Proyecto"
    to_alias: "Sintaxis de Tabla Jira"
  Textile:
    alias: "Tabla Textile"
    what: "Textile es un lenguaje de marcado ligero conciso con sintaxis simple y fácil de aprender, ampliamente utilizado en sistemas de gestión de contenido, plataformas de blog y sistemas de foros. Su sintaxis de tabla es clara e intuitiva, soportando formato rápido y configuraciones de estilo. Una herramienta ideal para creadores de contenido y administradores de sitios web para escritura rápida de documentos y publicación de contenido."
    step1: "Sube archivos de formato Textile o pega datos de tabla. La herramienta analiza la sintaxis de marcado Textile y extrae contenido de tabla."
    step3: "Genera sintaxis de tabla Textile estándar con soporte para marcado de encabezado, alineación de celdas, escape de caracteres especiales y optimización de formato. El código generado puede ser usado directamente en sistemas CMS, plataformas de blog y sistemas de documentos que soporten Textile, asegurando renderizado y visualización correcta de contenido."
    from_alias: "Documento Textile"
    to_alias: "Sintaxis de Tabla Textile"
  PNG:
    alias: "Imagen PNG"
    what: "PNG (Portable Network Graphics) es un formato de imagen sin pérdida con excelente compresión y soporte de transparencia. Ampliamente utilizado en diseño web, gráficos digitales y fotografía profesional. Su alta calidad y amplia compatibilidad lo hacen ideal para capturas de pantalla, logotipos, diagramas y cualquier imagen que requiera detalles nítidos y fondos transparentes."
    step1: "Importa datos de tabla en cualquier formato. La herramienta realiza diseño de diseño inteligente y optimización visual, calculando automáticamente el tamaño y resolución óptimos para salida PNG."
    step3: "Genera imágenes de tabla PNG de alta calidad con soporte para múltiples esquemas de color de tema, fondos transparentes, diseño adaptativo y optimización de claridad de texto. Perfecto para uso web, inserción de documentos y presentaciones profesionales con excelente calidad visual."
    from_alias: "Datos de Tabla"
    to_alias: "Imagen PNG de Alta Calidad"
  TOML:
    alias: "Configuración TOML"
    what: "TOML (Tom's Obvious, Minimal Language) es un formato de archivo de configuración que es fácil de leer y escribir. Diseñado para ser inequívoco y simple, es ampliamente utilizado en proyectos de software modernos para gestión de configuración. Su sintaxis clara y tipado fuerte lo hacen una excelente opción para configuraciones de aplicación y archivos de configuración de proyecto."
    step1: "Sube archivos TOML o pega datos de configuración. La herramienta analiza la sintaxis TOML y extrae información de configuración estructurada."
    step3: "Genera formato TOML estándar con soporte para estructuras anidadas, tipos de datos y comentarios. Los archivos TOML generados son perfectos para configuración de aplicación, herramientas de construcción y configuraciones de proyecto."
    from_alias: "Configuración TOML"
    to_alias: "Formato TOML"
  INI:
    alias: "Configuración INI"
    what: "Los archivos INI son archivos de configuración simples utilizados por muchas aplicaciones y sistemas operativos. Su estructura directa de pares clave-valor los hace fáciles de leer y editar manualmente. Ampliamente utilizados en aplicaciones Windows, sistemas heredados y escenarios de configuración simples donde la legibilidad humana es importante."
    step1: "Sube archivos INI o pega datos de configuración. La herramienta analiza la sintaxis INI y extrae información de configuración basada en secciones."
    step3: "Genera formato INI estándar con soporte para secciones, comentarios y varios tipos de datos. Los archivos INI generados son compatibles con la mayoría de aplicaciones y sistemas de configuración."
    from_alias: "Configuración INI"
    to_alias: "Formato INI"
  Avro:
    alias: "Esquema Avro"
    what: "Apache Avro es un sistema de serialización de datos que proporciona estructuras de datos ricas, formato binario compacto y capacidades de evolución de esquema. Ampliamente utilizado en procesamiento de big data, colas de mensajes y sistemas distribuidos. Su definición de esquema soporta tipos de datos complejos y compatibilidad de versiones, convirtiéndolo en una herramienta importante para ingenieros de datos y arquitectos de sistemas."
    step1: "Sube archivos de esquema Avro o pega datos. La herramienta analiza definiciones de esquema Avro y extrae información de estructura de tabla."
    step3: "Genera definiciones de esquema Avro estándar con soporte para mapeo de tipos de datos, restricciones de campo y validación de esquema. Los esquemas generados pueden ser utilizados directamente en ecosistemas Hadoop, sistemas de mensajes Kafka y otras plataformas de big data."
    from_alias: "Esquema Avro"
    to_alias: "Formato de Datos Avro"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) es el mecanismo neutral al lenguaje, neutral a la plataforma y extensible de Google para serializar datos estructurados. Ampliamente utilizado en microservicios, desarrollo de API y almacenamiento de datos. Su formato binario eficiente y tipado fuerte lo hacen ideal para aplicaciones de alto rendimiento y comunicación entre lenguajes."
    step1: "Sube archivos .proto o pega definiciones de Protocol Buffer. La herramienta analiza la sintaxis protobuf y extrae información de estructura de mensaje."
    step3: "Genera definiciones de Protocol Buffer estándar con soporte para tipos de mensaje, opciones de campo y definiciones de servicio. Los archivos .proto generados pueden ser compilados para múltiples lenguajes de programación."
    from_alias: "Protocol Buffer"
    to_alias: "Esquema Protobuf"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas es la biblioteca de análisis de datos más popular en Python, con DataFrame siendo su estructura de datos central. Proporciona capacidades poderosas de manipulación, limpieza y análisis de datos, ampliamente utilizada en ciencia de datos, aprendizaje automático e inteligencia empresarial. Una herramienta indispensable para desarrolladores Python y analistas de datos."
    step1: "Sube archivos Python que contengan código DataFrame o pega datos. La herramienta analiza la sintaxis Pandas y extrae información de estructura DataFrame."
    step3: "Genera código Pandas DataFrame estándar con soporte para especificaciones de tipo de datos, configuraciones de índice y operaciones de datos. El código generado puede ser ejecutado directamente en entorno Python para análisis y procesamiento de datos."
    from_alias: "Pandas DataFrame"
    to_alias: "Estructura de Datos Python"
  RDF:
    alias: "Triple RDF"
    what: "RDF (Resource Description Framework) es un modelo estándar para intercambio de datos en la Web, diseñado para representar información sobre recursos en forma de grafo. Ampliamente utilizado en web semántica, grafos de conocimiento y aplicaciones de datos enlazados. Su estructura de triple permite representación rica de metadatos y relaciones semánticas."
    step1: "Sube archivos RDF o pega datos de triple. La herramienta analiza la sintaxis RDF y extrae relaciones semánticas e información de recursos."
    step3: "Genera formato RDF estándar con soporte para varias serializaciones (RDF/XML, Turtle, N-Triples). El RDF generado puede ser utilizado en aplicaciones de web semántica, bases de conocimiento y sistemas de datos enlazados."
    from_alias: "Datos RDF"
    to_alias: "Formato Semántico RDF"
  MATLAB:
    alias: "Array MATLAB"
    what: "MATLAB es un software de computación numérica y visualización de alto rendimiento ampliamente utilizado en computación de ingeniería, análisis de datos y desarrollo de algoritmos. Sus operaciones de array y matriz son poderosas, soportando cálculos matemáticos complejos y procesamiento de datos. Una herramienta esencial para ingenieros, investigadores y científicos de datos."
    step1: "Sube archivos MATLAB .m o pega datos de array. La herramienta analiza la sintaxis MATLAB y extrae información de estructura de array."
    step3: "Genera código de array MATLAB estándar con soporte para arrays multidimensionales, especificaciones de tipo de datos y nomenclatura de variables. El código generado puede ser ejecutado directamente en entorno MATLAB para análisis de datos y computación científica."
    from_alias: "Array MATLAB"
    to_alias: "Formato de Código MATLAB"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame es la estructura de datos central en el lenguaje de programación R, ampliamente utilizada en análisis estadístico, minería de datos y aprendizaje automático. R es la herramienta principal para computación estadística y gráficos, con DataFrame proporcionando capacidades poderosas de manipulación de datos, análisis estadístico y visualización. Esencial para científicos de datos, estadísticos e investigadores que trabajan con análisis de datos estructurados."
    step1: "Sube archivos de datos R o pega código DataFrame. La herramienta analiza la sintaxis R y extrae información de estructura DataFrame incluyendo tipos de columnas, nombres de filas y contenido de datos."
    step3: "Genera código R DataFrame estándar con soporte para especificaciones de tipo de datos, niveles de factor, nombres de fila/columna y estructuras de datos específicas de R. El código generado puede ser ejecutado directamente en entorno R para análisis estadístico y procesamiento de datos."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Comenzar Conversión"
  start_generating: "Comenzar a generar"
  api_docs: "Documentación API"
related:
  section_title: 'Más Convertidores {{ if and .from (ne .from "generator") }}{{ .from }} y {{ end }}{{ .to }}'
  section_description: 'Explora más convertidores para formatos {{ if and .from (ne .from "generator") }}{{ .from }} y {{ end }}{{ .to }}. Transforma tus datos entre múltiples formatos con nuestras herramientas de conversión online profesionales.'
  title: "{{ .from }} a {{ .to }}"
howto:
  step2: "Edita datos usando nuestro editor de tablas online avanzado con características profesionales. Soporta eliminar filas vacías, remover duplicados, transposición de datos, ordenamiento, buscar y reemplazar con regex, y vista previa en tiempo real. Todos los cambios se convierten automáticamente al formato %s con resultados precisos y confiables."
  section_title: "Cómo usar el {{ . }}"
  converter_description: "Aprende a convertir {{ .from }} a {{ .to }} con nuestra guía paso a paso. Convertidor online profesional con características avanzadas y vista previa en tiempo real."
  generator_description: "Aprende a crear tablas {{ .to }} profesionales con nuestro generador online. Edición tipo Excel, vista previa en tiempo real, y capacidades de exportación instantánea."
extension:
  section_title: "Extensión de Detección y Extracción de Tablas"
  section_description: "Extrae tablas de cualquier sitio web con un clic. Convierte a más de 30 formatos incluyendo Excel, CSV, JSON instantáneamente - no se requiere copiar y pegar."
  features:
    extraction_title: "Extracción de Tablas con Un Clic"
    extraction_description: "Extrae instantáneamente tablas de cualquier página web sin copiar y pegar - extracción de datos profesional simplificada"
    formats_title: "Soporte para Más de 30 Formatos"
    formats_description: "Convierte tablas extraídas a Excel, CSV, JSON, Markdown, SQL, y más con nuestro convertidor de tablas avanzado"
    detection_title: "Detección Inteligente de Tablas"
    detection_description: "Detecta y resalta automáticamente tablas en cualquier página web para extracción y conversión rápida de datos"
  hover_tip: "✨ Pasa el cursor sobre cualquier tabla para ver el ícono de extracción"
recommendations:
  section_title: "Recomendado por Universidades y Profesionales"
  section_description: "TableConvert es confiado por profesionales en universidades, instituciones de investigación y equipos de desarrollo para conversión confiable de tablas y procesamiento de datos."
  cards:
    university_title: "Universidad de Wisconsin-Madison"
    university_description: "TableConvert.com - Herramienta profesional gratuita online de conversión de tablas y formatos de datos"
    university_link: "Leer Artículo"
    facebook_title: "Comunidad de Profesionales de Datos"
    facebook_description: "Compartido y recomendado por analistas de datos y profesionales en grupos de desarrolladores de Facebook"
    facebook_link: "Ver Publicación"
    twitter_title: "Comunidad de Desarrolladores"
    twitter_description: "Recomendado por @xiaoying_eth y otros desarrolladores en X (Twitter) para conversión de tablas"
    twitter_link: "Ver Tweet"
faq:
  section_title: "Preguntas Frecuentes"
  section_description: "Preguntas comunes sobre nuestro convertidor de tablas online gratuito, formatos de datos y proceso de conversión."
  what: "¿Qué es el formato %s?"
  howto_convert:
    question: "¿Cómo usar el {{ . }} gratis?"
    answer: "Sube tu archivo {{ .from }}, pega datos, o extrae de páginas web usando nuestro convertidor de tablas online gratuito. Nuestra herramienta de conversión profesional transforma instantáneamente tus datos al formato {{ .to }} con vista previa en tiempo real y características de edición avanzadas. Descarga o copia el resultado convertido inmediatamente."
  security:
    question: "¿Están seguros mis datos al usar este convertidor online?"
    answer: "¡Absolutamente! Todas las conversiones de tablas ocurren localmente en tu navegador - tus datos nunca salen de tu dispositivo. Nuestro convertidor online procesa todo del lado del cliente, asegurando completa privacidad y seguridad de datos. No se almacenan archivos en nuestros servidores."
  free:
    question: "¿Es TableConvert realmente gratis de usar?"
    answer: "¡Sí, TableConvert es completamente gratis! Todas las características del convertidor, editor de tablas, herramientas generadoras de datos, y opciones de exportación están disponibles sin costo, registro, o tarifas ocultas. Convierte archivos ilimitados online gratis."
  filesize:
    question: "¿Qué límites de tamaño de archivo tiene el convertidor online?"
    answer: "Nuestro convertidor de tablas online gratuito soporta archivos hasta 10MB. Para archivos más grandes, procesamiento por lotes, o necesidades empresariales, usa nuestra extensión de navegador o servicio API profesional con límites más altos."
stats:
  conversions: "Tablas Convertidas"
  tables: "Tablas Generadas"
  formats: "Formatos de Archivos de Datos"
  rating: "Calificación del Usuario"
