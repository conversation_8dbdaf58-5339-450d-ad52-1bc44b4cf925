site:
  fullname: "表格转换工具"
  name: "TableConvert"
  subtitle: "免费在线表格转换器和生成器"
  intro: "TableConvert 是一个免费的在线表格转换和数据生成工具，支持 Excel、CSV、JSON、Markdown、LaTeX、SQL 等 30 多种格式之间的转换。"
  followTwitter: "在 X 上关注我们"
title:
  converter: "%s 转 %s"
  generator: "%s 生成器"
post:
  tags:
    converter: "转换器"
    editor: "编辑器"
    generator: "生成器"
    maker: "构建器"
  converter:
    title: "在线 %s 转 %s"
    short: "免费强大的 %s 转 %s 在线工具"
    intro: "易于使用的在线 %s 转 %s 转换器。使用我们直观的转换工具轻松转换表格数据。快速、可靠且用户友好。"
  generator:
    title: "在线 %s 编辑器和生成器"
    short: "功能全面的专业 %s 在线生成工具"
    intro: "易于使用的在线 %s 生成器和表格编辑器。使用我们直观的工具和实时预览轻松创建专业的数据表格。"
navbar:
  search:
    placeholder: "搜索转换器..."
  sponsor: "请我喝咖啡"
  extension: "扩展程序"
  api: "API 文档"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "数据源"
    placeholder: "粘贴您的 %s 数据或拖拽 %s 文件到此处"
    example: "示例"
    upload: "上传文件"
    extract:
      enter: "从网页提取"
      intro: "输入包含表格数据的网页 URL，自动提取结构化数据"
      btn: "提取 %s"
    excel:
      sheet: "工作表"
      none: "无"
  tableEditor:
    title: "在线表格编辑器"
    undo: "撤销"
    redo: "重做"
    transpose: "转置"
    clear: "清空"
    deleteBlank: "删除空行"
    deleteDuplicate: "去重"
    uppercase: "转大写"
    lowercase: "转小写"
    capitalize: "首字母大写"
    replace:
      replace: "查找替换（支持正则表达式）"
      subst: "替换为..."
      btn: "全部替换"
  tableGenerator:
    title: "表格生成器"
    sponsor: "请我喝咖啡"
    copy: "复制到剪贴板"
    download: "下载文件"
    tooltip:
      html:
        escape: "转义 HTML 特殊字符（&、<、>、\"、'）以防止显示错误"
        div: "使用 DIV+CSS 布局代替传统 TABLE 标签，更适合响应式设计"
        minify: "移除空白和换行符，生成压缩的 HTML 代码"
        thead: "生成标准的表头（&lt;thead&gt;）和表体（&lt;tbody&gt;）结构"
        tableCaption: "在表格上方添加描述性标题（&lt;caption&gt; 元素）"
        tableClass: "为表格添加 CSS 类名，便于样式定制"
        tableId: "为表格设置唯一 ID 标识符，便于 JavaScript 操作"
      jira:
        escape: "转义管道字符（|）以避免与 Jira 表格语法冲突"
      json:
        parsingJSON: "智能解析单元格中的 JSON 字符串为对象"
        minify: "生成紧凑的单行 JSON 格式以减小文件大小"
        format: "选择输出 JSON 数据结构：对象数组、二维数组等"
      latex:
        escape: "转义 LaTeX 特殊字符（%、&、_、#、$ 等）以确保正确编译"
        ht: "添加浮动位置参数 [!ht] 控制表格在页面中的位置"
        mwe: "生成完整的 LaTeX 文档"
        tableAlign: "设置表格在页面中的水平对齐方式"
        tableBorder: "配置表格边框样式：无边框、部分边框、完整边框"
        label: "设置表格标签用于 \\ref{} 命令交叉引用"
        caption: "设置表格标题显示在表格上方或下方"
        location: "选择表格标题显示位置：上方或下方"
        tableType: "选择表格环境类型：tabular、longtable、array 等"
      markdown:
        escape: "转义 Markdown 特殊字符（*、_、|、\\ 等）以避免格式冲突"
        pretty: "自动对齐列宽，生成更美观的表格格式"
        simple: "使用简化语法，省略外边框竖线"
        boldFirstRow: "将第一行文本设为粗体"
        boldFirstColumn: "将第一列文本设为粗体"
        firstHeader: "将第一行作为表头并添加分隔线"
        textAlign: "设置列文本对齐方式：左对齐、居中、右对齐"
        multilineHandling: "多行文本处理：保留换行符、转义为 \\n、使用 &lt;br&gt; 标签"

        includeLineNumbers: "在表格左侧添加行号列"
      magic:
        builtin: "选择预定义的常用模板格式"
        rowsTpl: "<table> <tr> <th>魔法语法</th> <th>描述</th> <th>支持的 JS 方法</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>第1、第2...个<b>标题</b>字段，也可写作 {hA} {hB} ...</td> <td>字符串方法</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>当前行的第1、第2...个字段，也可写作 {$A} {$B} ...</td> <td>字符串方法</td> </tr> <tr> <td>{F,} {F;}</td> <td>用 <b>F</b> 后面的字符串分割当前行</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>当前<b>行</b>的行<b>号</b>，从1或100开始</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>结束</b>行的行<b>号</b> </td> <td></td> </tr> <tr> <td>{x ...}</td> <td><b>执行</b> JavaScript 代码，例如：{x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> 使用反斜杠 <b>\\</b> 输出大括号 {...} </td> <td></td> </tr></table>"
        headerTpl: "自定义页眉部分的输出模板"
        footerTpl: "自定义页脚部分的输出模板"
      textile:
        escape: "转义 Textile 语法字符（|、.、-、^）以避免格式冲突"
        rowHeader: "将第一行设为表头行"
        thead: "为表头和表体添加 Textile 语法标记"
      xml:
        escape: "转义 XML 特殊字符（&lt;、&gt;、&amp;、\"、'）以确保有效的 XML"
        minify: "生成压缩的 XML 输出，移除多余空白"
        rootElement: "设置 XML 根元素标签名"
        rowElement: "设置每行数据的 XML 元素标签名"
        declaration: "添加 XML 声明头（&lt;?xml version=\"1.0\"?&gt;）"
        attributes: "将数据输出为 XML 属性而非子元素"
        cdata: "用 CDATA 包装文本内容以保护特殊字符"
        encoding: "设置 XML 文档的字符编码格式"
        indentation: "选择 XML 缩进字符：空格或制表符"
      yaml:
        indentSize: "设置 YAML 层次缩进的空格数（通常为 2 或 4）"
        arrayStyle: "数组格式：块格式（每行一项）或流格式（内联格式）"
        quotationStyle: "字符串引号样式：无引号、单引号、双引号"
      csv:
        bom: "添加 UTF-8 字节顺序标记以帮助 Excel 等软件识别编码"
      excel:
        autoWidth: "根据内容自动调整列宽"
        protectSheet: "启用工作表保护，密码：tableconvert.com"
      sql:
        primaryKey: "为 CREATE TABLE 语句指定主键字段名"
        dialect: "选择数据库类型，影响引号和数据类型语法"
      ascii:
        forceSep: "强制在每行数据之间添加分隔线"
        style: "选择 ASCII 表格边框绘制样式"
        comment: "添加注释标记包装整个表格"
      mediawiki:
        minify: "压缩输出代码，移除多余空白"
        header: "将第一行标记为表头样式"
        sort: "启用表格点击排序功能"
      asciidoc:
        minify: "压缩 AsciiDoc 格式输出"
        firstHeader: "将第一行设为表头行"
        lastFooter: "将最后一行设为表尾行"
        title: "为表格添加标题文本"
      tracwiki:
        rowHeader: "将第一行设为表头"
        colHeader: "将第一列设为表头"
      bbcode:
        minify: "压缩 BBCode 输出格式"
      restructuredtext:
        style: "选择 reStructuredText 表格边框样式"
        forceSep: "强制分隔线"
      pdf:
        theme: "选择 PDF 表格视觉样式，用于专业文档"
        headerColor: "选择 PDF 表格标题背景颜色"
        showHead: "控制 PDF 页面中标题显示方式"
        docTitle: "PDF 文档的可选标题"
        docDescription: "PDF 文档的可选描述文本"
    label:
      ascii:
        forceSep: "行分隔符"
        style: "边框样式"
        comment: "注释包装"
      restructuredtext:
        style: "边框样式"
        forceSep: "强制分隔符"
      bbcode:
        minify: "压缩输出"
      csv:
        doubleQuote: "双引号包装"
        delimiter: "字段分隔符"
        bom: "UTF-8 BOM"
        valueDelimiter: "值分隔符"
        rowDelimiter: "行分隔符"
        prefix: "行前缀"
        suffix: "行后缀"
      excel:
        autoWidth: "自动列宽"
        textFormat: "文本格式"
        protectSheet: "保护工作表"
        boldFirstRow: "首行加粗"
        boldFirstColumn: "首列加粗"
        sheetName: "工作表名称"
      html:
        escape: "转义 HTML 字符"
        div: "DIV 表格"
        minify: "压缩代码"
        thead: "表头结构"
        tableCaption: "表格标题"
        tableClass: "表格类名"
        tableId: "表格 ID"
        rowHeader: "行表头"
        colHeader: "列表头"
      jira:
        escape: "转义字符"
        rowHeader: "行表头"
        colHeader: "列表头"
      json:
        parsingJSON: "解析 JSON"
        minify: "压缩输出"
        format: "数据格式"
        rootName: "根对象名称"
        indentSize: "缩进大小"
      jsonlines:
        parsingJSON: "解析 JSON"
        format: "数据格式"
      latex:
        escape: "转义 LaTeX 表格字符"
        ht: "浮动位置"
        mwe: "完整文档"
        tableAlign: "表格对齐"
        tableBorder: "边框样式"
        label: "引用标签"
        caption: "表格标题"
        location: "标题位置"
        tableType: "表格类型"
        boldFirstRow: "首行加粗"
        boldFirstColumn: "首列加粗"
        textAlign: "文本对齐"
        borders: "边框设置"
      markdown:
        escape: "转义字符"
        pretty: "美化 Markdown 表格"
        simple: "简单 Markdown 格式"
        boldFirstRow: "首行加粗"
        boldFirstColumn: "首列加粗"
        firstHeader: "首行表头"
        textAlign: "文本对齐"
        multilineHandling: "多行处理"

        includeLineNumbers: "添加行号"
        align: "对齐方式"
      mediawiki:
        minify: "压缩代码"
        header: "表头标记"
        sort: "可排序"
      asciidoc:
        minify: "压缩格式"
        firstHeader: "首行表头"
        lastFooter: "末行表尾"
        title: "表格标题"
      tracwiki:
        rowHeader: "行表头"
        colHeader: "列表头"
      sql:
        drop: "删除表（如果存在）"
        create: "创建表"
        oneInsert: "批量插入"
        table: "表名"
        dialect: "数据库类型"
        primaryKey: "主键"
      magic:
        builtin: "内置模板"
        rowsTpl: "行模板，语法 ->"
        headerTpl: "页眉模板"
        footerTpl: "页脚模板"
      textile:
        escape: "转义字符"
        rowHeader: "行表头"
        thead: "表头语法"
      xml:
        escape: "转义 XML 字符"
        minify: "压缩输出"
        rootElement: "根元素"
        rowElement: "行元素"
        declaration: "XML 声明"
        attributes: "属性模式"
        cdata: "CDATA 包装"
        encoding: "编码"
        indentSize: "缩进大小"
      yaml:
        indentSize: "缩进大小"
        arrayStyle: "数组样式"
        quotationStyle: "引号样式"
      pdf:
        theme: "PDF 表格主题"
        headerColor: "PDF 表头颜色"
        showHead: "PDF 表头显示"
        docTitle: "PDF 文档标题"
        docDescription: "PDF 文档描述"
sidebar:
  all: "所有转换工具"
  dataSource:
    title: "数据源"
    description:
      converter: "导入 %s 以转换为 %s。支持文件上传、在线编辑和网页数据提取。"
      generator: "创建表格数据，支持多种输入方式，包括手动输入、文件导入和模板生成。"
  tableEditor:
    title: "在线表格编辑器"
    description:
      converter: "使用我们的表格编辑器在线处理 %s。类似 Excel 的操作体验，支持删除空行、去重、排序和查找替换。"
      generator: "强大的在线表格编辑器，提供类似 Excel 的操作体验。支持删除空行、去重、排序和查找替换。"
  tableGenerator:
    title: "表格生成器"
    description:
      converter: "使用表格生成器快速生成 %s，实时预览。丰富的导出选项，一键复制和下载。"
      generator: "将 %s 数据导出为多种格式，满足不同使用场景。支持自定义选项和实时预览。"
footer:
  changelog: "更新日志"
  sponsor: "赞助商"
  contact: "联系我们"
  privacyPolicy: "隐私政策"
  about: "关于"
  resources: "资源"
  popularConverters: "热门转换器"
  popularGenerators: "热门生成器"
  dataSecurity: "您的数据是安全的 - 所有转换都在您的浏览器中运行。"
converters:
  Markdown:
    alias: "Markdown 表格"
    what: "Markdown 是一种轻量级标记语言，广泛用于技术文档、博客内容创作和网页开发。其表格语法简洁直观，支持文本对齐、链接嵌入和格式化。是程序员和技术写作者的首选工具，与 GitHub、GitLab 等代码托管平台完美兼容。"
    step1: "将 Markdown 表格数据粘贴到数据源区域，或直接拖拽 .md 文件上传。工具自动解析表格结构和格式，支持复杂嵌套内容和特殊字符处理。"
    step3: "实时生成标准 Markdown 表格代码，支持多种对齐方式、文本加粗、行号添加等高级格式设置。生成的代码与 GitHub 和主流 Markdown 编辑器完全兼容，一键复制即可使用。"
    from_alias: "Markdown 表格文件"
    to_alias: "Markdown 表格格式"
  Magic:
    alias: "自定义模板"
    what: "魔法模板是本工具独有的高级数据生成器，允许用户通过自定义模板语法创建任意格式的数据输出。支持变量替换、条件判断和循环处理。是处理复杂数据转换需求和个性化输出格式的终极解决方案，特别适合开发者和数据工程师。"
    step1: "选择内置常用模板或创建自定义模板语法。支持丰富的变量和函数，能够处理复杂的数据结构和业务逻辑。"
    step3: "生成完全符合自定义格式要求的数据输出。支持复杂的数据转换逻辑和条件处理，大大提高数据处理效率和输出质量。批量数据处理的强大工具。"
    from_alias: "表格数据"
    to_alias: "自定义格式输出"
  CSV:
    alias: "CSV"
    what: "CSV（逗号分隔值）是使用最广泛的数据交换格式，完美支持 Excel、Google Sheets、数据库系统和各种数据分析工具。其简单的结构和强大的兼容性使其成为数据迁移、批量导入导出和跨平台数据交换的标准格式，广泛应用于商业分析、数据科学和系统集成。"
    step1: "上传 CSV 文件或直接粘贴 CSV 数据。工具智能识别各种分隔符（逗号、制表符、分号、管道符等），自动检测数据类型和编码格式，支持大文件快速解析和复杂数据结构。"
    step3: "生成标准 CSV 格式文件，支持自定义分隔符、引号样式、编码格式和 BOM 标记设置。确保与目标系统完美兼容，提供下载和压缩选项，满足企业级数据处理需求。"
    from_alias: "CSV 数据文件"
    to_alias: "CSV 标准格式"
  JSON:
    alias: "JSON 数组"
    what: "JSON（JavaScript 对象表示法）是现代 Web 应用程序、REST API 和微服务架构的标准表格数据格式。其清晰的结构和高效的解析使其广泛用于前后端数据交互、配置文件存储和 NoSQL 数据库。支持嵌套对象、数组结构和多种数据类型，是现代软件开发不可缺少的表格数据。"
    step1: "上传 JSON 文件或粘贴 JSON 数组。支持自动识别和解析对象数组、嵌套结构和复杂数据类型。工具智能验证 JSON 语法并提供错误提示。"
    step3: "生成多种 JSON 格式输出：标准对象数组、二维数组、列数组和键值对格式。支持美化输出、压缩模式、自定义根对象名称和缩进设置，完美适配各种 API 接口和数据存储需求。"
    from_alias: "JSON 数组文件"
    to_alias: "JSON 标准格式"
  JSONLines:
    alias: "JSONLines 格式"
    what: "JSON Lines（也称为 NDJSON）是大数据处理和流式数据传输的重要格式，每行包含一个独立的 JSON 对象。广泛用于日志分析、数据流处理、机器学习和分布式系统。支持增量处理和并行计算，是处理大规模结构化数据的理想选择。"
    step1: "上传 JSONLines 文件或粘贴数据。工具逐行解析 JSON 对象，支持大文件流式处理和错误行跳过功能。"
    step3: "生成标准 JSONLines 格式，每行输出一个完整的 JSON 对象。适用于流式处理、批量导入和大数据分析场景，支持数据验证和格式优化。"
    from_alias: "JSONLines 数据"
    to_alias: "JSONLines 流式格式"
  XML:
    alias: "XML"
    what: "XML（可扩展标记语言）是企业级数据交换和配置管理的标准格式，具有严格的语法规范和强大的验证机制。广泛用于 Web 服务、配置文件、文档存储和系统集成。支持命名空间、模式验证和 XSLT 转换，是企业应用的重要表格数据。"
    step1: "上传 XML 文件或粘贴 XML 数据。工具自动解析 XML 结构并转换为表格格式，支持命名空间、属性处理和复杂嵌套结构。"
    step3: "生成符合 XML 标准的输出。支持自定义根元素、行元素名称、属性模式、CDATA 包装和字符编码设置。确保数据完整性和兼容性，满足企业级应用要求。"
    from_alias: "XML 数据文件"
    to_alias: "XML 标准格式"
  YAML:
    alias: "YAML 配置"
    what: "YAML 是一种人性化的数据序列化标准，以其清晰的层次结构和简洁的语法而闻名。广泛用于配置文件、DevOps 工具链、Docker Compose 和 Kubernetes 部署。其强大的可读性和简洁的语法使其成为现代云原生应用和自动化运维的重要配置格式。"
    step1: "上传 YAML 文件或粘贴 YAML 数据。工具智能解析 YAML 结构并验证语法正确性，支持多文档格式和复杂数据类型。"
    step3: "生成标准 YAML 格式输出，支持块式和流式数组样式、多种引号设置、自定义缩进和注释保留。确保输出的 YAML 文件与各种解析器和配置系统完全兼容。"
    from_alias: "YAML 配置文件"
    to_alias: "YAML 标准格式"
  MySQL:
      alias: "MySQL 查询结果"
      what: "MySQL 是世界上最受欢迎的开源关系数据库管理系统，以其高性能、可靠性和易用性而闻名。广泛用于 Web 应用程序、企业系统和数据分析平台。MySQL 查询结果通常包含结构化的表格数据，是数据库管理和数据分析工作中的重要数据源。"
      step1: "将 MySQL 查询输出结果粘贴到数据源区域。工具自动识别和解析 MySQL 命令行输出格式，支持各种查询结果样式和字符编码，智能处理表头和数据行。"
      step3: "快速将 MySQL 查询结果转换为多种表格数据格式，便于数据分析、报告生成、跨系统数据迁移和数据验证。数据库管理员和数据分析师的实用工具。"
      from_alias: "MySQL 查询输出"
      to_alias: "MySQL 表格数据"
  SQL:
    alias: "插入 SQL"
    what: "SQL（结构化查询语言）是关系数据库的标准操作语言，用于数据查询、插入、更新和删除操作。作为数据库管理的核心技术，SQL 广泛用于数据分析、商业智能、ETL 处理和数据仓库构建。是数据专业人员的必备技能工具。"
    step1: "粘贴 INSERT SQL 语句或上传 .sql 文件。工具智能解析 SQL 语法并提取表格数据，支持多种 SQL 方言和复杂查询语句处理。"
    step3: "生成标准 SQL INSERT 语句和表创建语句。支持多种数据库方言（MySQL、PostgreSQL、SQLite、SQL Server、Oracle），自动处理数据类型映射、字符转义和主键约束。确保生成的 SQL 代码可以直接执行。"
    from_alias: "SQL 数据文件"
    to_alias: "SQL 标准语句"
  Qlik:
      alias: "Qlik 表格"
      what: "Qlik 是专门从事数据可视化、执行仪表板和自助式商业智能产品的软件供应商，与 Tableau 和 Microsoft 齐名。"
      step1: ""
      step3: "最后，[表格生成器](#TableGenerator) 显示转换结果。可在您的 Qlik Sense、Qlik AutoML、QlikView 或其他支持 Qlik 的软件中使用。"
      from_alias: "Qlik 表格"
      to_alias: "Qlik 表格"
  DAX:
      alias: "DAX 表格"
      what: "DAX（数据分析表达式）是 Microsoft Power BI 中使用的编程语言，用于创建计算列、度量值和自定义表格。"
      step1: ""
      step3: "最后，[表格生成器](#TableGenerator) 显示转换结果。如预期的那样，它用于多个 Microsoft 产品，包括 Microsoft Power BI、Microsoft Analysis Services 和 Microsoft Power Pivot for Excel。"
      from_alias: "DAX 表格"
      to_alias: "DAX 表格"
  Firebase:
    alias: "Firebase 列表"
    what: "Firebase 是一个 BaaS 应用开发平台，提供托管的后端服务，如实时数据库、云存储、身份验证、崩溃报告等。"
    step1: ""
    step3: "最后，[表格生成器](#TableGenerator) 显示转换结果。然后您可以使用 Firebase API 中的 push 方法将数据添加到 Firebase 数据库的列表中。"
    from_alias: "Firebase 列表"
    to_alias: "Firebase 列表"
  HTML:
    alias: "HTML 表格"
    what: "HTML 表格是在网页中显示结构化数据的标准方式，使用 table、tr、td 等标签构建。支持丰富的样式定制、响应式布局和交互功能。广泛用于网站开发、数据展示和报告生成，是前端开发和网页设计的重要组成部分。"
    step1: "粘贴包含表格的 HTML 代码或上传 HTML 文件。工具自动识别并从页面中提取表格数据，支持复杂的 HTML 结构、CSS 样式和嵌套表格处理。"
    step3: "生成语义化的 HTML 表格代码，支持 thead/tbody 结构、CSS 类设置、表格标题、行/列表头和响应式属性配置。确保生成的表格代码符合 Web 标准，具有良好的可访问性和 SEO 友好性。"
    from_alias: "HTML 网页表格"
    to_alias: "HTML 标准表格"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel 是世界上最受欢迎的电子表格软件，广泛用于商业分析、财务管理、数据处理和报告创建。其强大的数据处理能力、丰富的函数库和灵活的可视化功能使其成为办公自动化和数据分析的标准工具，在几乎所有行业和领域都有广泛应用。"
    step1: "上传 Excel 文件（支持 .xlsx、.xls 格式）或直接从 Excel 复制表格数据并粘贴。工具支持多工作表处理、复杂格式识别和大文件快速解析，自动处理合并单元格和数据类型。"
    step3: "生成与 Excel 兼容的表格数据，可直接粘贴到 Excel 中或下载为标准 .xlsx 文件。支持工作表命名、单元格格式化、自动列宽、表头样式和数据验证设置。确保输出的 Excel 文件具有专业外观和完整功能。"
    from_alias: "Excel 电子表格"
    to_alias: "Excel 标准格式"
  LaTeX:
    alias: "LaTeX 表格"
    what: "LaTeX 是专业的文档排版系统，特别适合创建学术论文、技术文档和科学出版物。其表格功能强大，支持复杂的数学公式、精确的布局控制和高质量的 PDF 输出。是学术界和科学出版的标准工具，广泛用于期刊论文、学位论文和技术手册排版。"
    step1: "粘贴 LaTeX 表格代码或上传 .tex 文件。工具解析 LaTeX 表格语法并提取数据内容，支持多种表格环境（tabular、longtable、array 等）和复杂格式命令。"
    step3: "生成专业的 LaTeX 表格代码，支持多种表格环境选择、边框样式配置、标题位置设置、文档类规范和包管理。可生成完整的可编译 LaTeX 文档，确保输出表格符合学术出版标准。"
    from_alias: "LaTeX 文档表格"
    to_alias: "LaTeX 专业格式"
  ASCII:
    alias: "ASCII 表格"
    what: "ASCII 表格使用纯文本字符绘制表格边框和结构，提供最佳的兼容性和可移植性。与所有文本编辑器、终端环境和操作系统兼容。广泛用于代码文档、技术手册、README 文件和命令行工具输出。程序员和系统管理员的首选数据显示格式。"
    step1: "上传包含 ASCII 表格的文本文件或直接粘贴表格数据。工具智能识别和解析 ASCII 表格结构，支持多种边框样式和对齐格式。"
    step3: "生成美观的纯文本 ASCII 表格，支持多种边框样式（单线、双线、圆角等）、文本对齐方式和自动列宽。生成的表格在代码编辑器、文档和命令行中完美显示。"
    from_alias: "ASCII 文本表格"
    to_alias: "ASCII 标准格式"
  MediaWiki:
    alias: "MediaWiki 表格"
    what: "MediaWiki 是维基百科等著名 wiki 网站使用的开源软件平台。其表格语法简洁而强大，支持表格样式定制、排序功能和链接嵌入。广泛用于知识管理、协作编辑和内容管理系统，是构建 wiki 百科全书和知识库的核心技术。"
    step1: "粘贴 MediaWiki 表格代码或上传 wiki 源文件。工具解析 wiki 标记语法并提取表格数据，支持复杂的 wiki 语法和模板处理。"
    step3: "生成标准 MediaWiki 表格代码，支持表头样式设置、单元格对齐、排序功能启用和代码压缩选项。生成的代码可直接用于 wiki 页面编辑，确保在 MediaWiki 平台上完美显示。"
    from_alias: "MediaWiki 源代码"
    to_alias: "MediaWiki 表格语法"
  TracWiki:
    alias: "TracWiki 表格"
    what: "Trac 是一个基于 Web 的项目管理和错误跟踪系统，使用简化的 wiki 语法创建表格内容。"
    step1: "上传 TracWiki 文件或粘贴表格数据。"
    step3: "生成与 TracWiki 兼容的表格代码，支持行/列表头设置，便于项目文档管理。"
    from_alias: "TracWiki 表格"
    to_alias: "TracWiki 格式"
  AsciiDoc:
    alias: "AsciiDoc 表格"
    what: "AsciiDoc 是一种轻量级标记语言，可以转换为 HTML、PDF、手册页和其他格式，广泛用于技术文档编写。"
    step1: "上传 AsciiDoc 文件或粘贴数据。"
    step3: "生成 AsciiDoc 表格语法，支持表头、表尾和标题设置，可直接在 AsciiDoc 编辑器中使用。"
    from_alias: "AsciiDoc 表格"
    to_alias: "AsciiDoc 格式"
  reStructuredText:
    alias: "reStructuredText 表格"
    what: "reStructuredText 是 Python 社区的标准文档格式，支持丰富的表格语法，常用于 Sphinx 文档生成。"
    step1: "上传 .rst 文件或粘贴 reStructuredText 数据。"
    step3: "生成标准 reStructuredText 表格，支持多种边框样式，可直接用于 Sphinx 文档项目。"
    from_alias: "reStructuredText 表格"
    to_alias: "reStructuredText 格式"
  PHP:
    alias: "PHP 数组"
    what: "PHP 是一种流行的服务器端脚本语言，数组是其核心数据结构，广泛用于 Web 开发和数据处理。"
    step1: "上传包含 PHP 数组的文件或直接粘贴数据。"
    step3: "生成标准 PHP 数组代码，可直接用于 PHP 项目，支持关联数组和索引数组格式。"
    from_alias: "PHP 数组"
    to_alias: "PHP 代码"
  Ruby:
    alias: "Ruby 数组"
    what: "Ruby 是一种动态面向对象编程语言，语法简洁优雅，数组是其重要的数据结构。"
    step1: "上传 Ruby 文件或粘贴数组数据。"
    step3: "生成符合 Ruby 语法规范的数组代码，可直接用于 Ruby 项目。"
    from_alias: "Ruby 数组"
    to_alias: "Ruby 代码"
  ASP:
    alias: "ASP 数组"
    what: "ASP（Active Server Pages）是 Microsoft 的服务器端脚本环境，支持多种编程语言开发动态网页。"
    step1: "上传 ASP 文件或粘贴数组数据。"
    step3: "生成与 ASP 兼容的数组代码，支持 VBScript 和 JScript 语法，可用于 ASP.NET 项目。"
    from_alias: "ASP 数组"
    to_alias: "ASP 代码"
  ActionScript:
    alias: "ActionScript 数组"
    what: "ActionScript 是一种面向对象的编程语言，主要用于 Adobe Flash 和 AIR 应用程序开发。"
    step1: "上传 .as 文件或粘贴 ActionScript 数据。"
    step3: "生成符合 AS3 语法标准的 ActionScript 数组代码，可用于 Flash 和 Flex 项目开发。"
    from_alias: "ActionScript 数组"
    to_alias: "ActionScript 代码"
  BBCode:
    alias: "BBCode 表格"
    what: "BBCode 是一种轻量级标记语言，常用于论坛和在线社区，提供简单的格式化功能，包括表格支持。"
    step1: "上传包含 BBCode 的文件或粘贴数据。"
    step3: "生成适合论坛发帖和社区内容创建的 BBCode 表格代码，支持压缩输出格式。"
    from_alias: "BBCode 表格"
    to_alias: "BBCode 格式"
  PDF:
    alias: "PDF 表格"
    what: "PDF（便携式文档格式）是一种跨平台文档标准，具有固定布局、一致显示和高质量打印特性。广泛用于正式文档、报告、发票、合同和学术论文。是商务沟通和文档归档的首选格式，确保在不同设备和操作系统上完全一致的视觉效果。"
    step1: "导入任何格式的表格数据。工具自动分析数据结构并进行智能布局设计，支持大表格自动分页和复杂数据类型处理。"
    step3: "生成高质量的 PDF 表格文件，支持多种专业主题样式（商务、学术、简约等）、多语言字体、自动分页、水印添加和打印优化。确保输出的 PDF 文档具有专业外观，可直接用于商务演示和正式发布。"
    from_alias: "表格数据"
    to_alias: "PDF 专业文档"
  JPEG:
    alias: "JPEG 图像"
    what: "JPEG 是使用最广泛的数字图像格式，具有出色的压缩效果和广泛的兼容性。其小文件大小和快速加载速度使其适合网页显示、社交媒体分享、文档插图和在线演示。是数字媒体和网络通信的标准图像格式，几乎所有设备和软件都完美支持。"
    step1: "导入任何格式的表格数据。工具进行智能布局设计和视觉优化，自动计算最佳尺寸和分辨率。"
    step3: "生成高清 JPEG 表格图像，支持多种主题配色方案（浅色、深色、护眼等）、自适应布局、文本清晰度优化和尺寸定制。适合在线分享、文档插入和演示使用，确保在各种显示设备上都有出色的视觉效果。"
    from_alias: "表格数据"
    to_alias: "JPEG 高清图像"
  Jira:
    alias: "Jira 表格"
    what: "JIRA 是 Atlassian 开发的专业项目管理和错误跟踪软件，广泛用于敏捷开发、软件测试和项目协作。其表格功能支持丰富的格式选项和数据显示，是软件开发团队、项目经理和质量保证人员在需求管理、错误跟踪和进度报告中的重要工具。"
    step1: "上传包含表格数据的文件或直接粘贴数据内容。工具自动处理表格数据和特殊字符转义。"
    step3: "生成与 JIRA 平台兼容的表格代码，支持表头样式设置、单元格对齐、字符转义处理和格式优化。生成的代码可直接粘贴到 JIRA 问题描述、评论或 wiki 页面中，确保在 JIRA 系统中正确显示和渲染。"
    from_alias: "项目数据"
    to_alias: "Jira 表格语法"
  Textile:
    alias: "Textile 表格"
    what: "Textile 是一种简洁的轻量级标记语言，语法简单易学，广泛用于内容管理系统、博客平台和论坛系统。其表格语法清晰直观，支持快速格式化和样式设置。是内容创作者和网站管理员快速文档编写和内容发布的理想工具。"
    step1: "上传 Textile 格式文件或粘贴表格数据。工具解析 Textile 标记语法并提取表格内容。"
    step3: "生成标准 Textile 表格语法，支持表头标记、单元格对齐、特殊字符转义和格式优化。生成的代码可直接用于支持 Textile 的 CMS 系统、博客平台和文档系统，确保内容正确渲染和显示。"
    from_alias: "Textile 文档"
    to_alias: "Textile 表格语法"
  PNG:
    alias: "PNG 图像"
    what: "PNG（便携式网络图形）是一种无损图像格式，具有出色的压缩和透明度支持。广泛用于网页设计、数字图形和专业摄影。其高质量和广泛兼容性使其成为截图、徽标、图表和任何需要清晰细节和透明背景的图像的理想选择。"
    step1: "导入任何格式的表格数据。工具进行智能布局设计和视觉优化，自动计算 PNG 输出的最佳尺寸和分辨率。"
    step3: "生成高质量的 PNG 表格图像，支持多种主题配色方案、透明背景、自适应布局和文本清晰度优化。完美适用于网页使用、文档插入和专业演示，具有出色的视觉质量。"
    from_alias: "表格数据"
    to_alias: "PNG 高质量图像"
  TOML:
    alias: "TOML 配置"
    what: "TOML（Tom's Obvious, Minimal Language）是一种易于读写的配置文件格式。设计为明确和简单，广泛用于现代软件项目的配置管理。其清晰的语法和强类型使其成为应用程序设置和项目配置文件的绝佳选择。"
    step1: "上传 TOML 文件或粘贴配置数据。工具解析 TOML 语法并提取结构化配置信息。"
    step3: "生成标准 TOML 格式，支持嵌套结构、数据类型和注释。生成的 TOML 文件非常适合应用程序配置、构建工具和项目设置。"
    from_alias: "TOML 配置"
    to_alias: "TOML 格式"
  INI:
    alias: "INI 配置"
    what: "INI 文件是许多应用程序和操作系统使用的简单配置文件。其直接的键值对结构使其易于手动读取和编辑。广泛用于 Windows 应用程序、遗留系统和需要人类可读性的简单配置场景。"
    step1: "上传 INI 文件或粘贴配置数据。工具解析 INI 语法并提取基于节的配置信息。"
    step3: "生成标准 INI 格式，支持节、注释和各种数据类型。生成的 INI 文件与大多数应用程序和配置系统兼容。"
    from_alias: "INI 配置"
    to_alias: "INI 格式"
  Avro:
    alias: "Avro 模式"
    what: "Apache Avro 是一个数据序列化系统，提供丰富的数据结构、紧凑的二进制格式和模式演进功能。广泛用于大数据处理、消息队列和分布式系统。其模式定义支持复杂数据类型和版本兼容性，是数据工程师和系统架构师的重要工具。"
    step1: "上传 Avro 模式文件或粘贴数据。工具解析 Avro 模式定义并提取表格结构信息。"
    step3: "生成标准 Avro 模式定义，支持数据类型映射、字段约束和模式验证。生成的模式可直接用于 Hadoop 生态系统、Kafka 消息系统和其他大数据平台。"
    from_alias: "Avro 模式"
    to_alias: "Avro 数据格式"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers（protobuf）是 Google 的语言中性、平台中性、可扩展的结构化数据序列化机制。广泛用于微服务、API 开发和数据存储。其高效的二进制格式和强类型使其成为高性能应用程序和跨语言通信的理想选择。"
    step1: "上传 .proto 文件或粘贴 Protocol Buffer 定义。工具解析 protobuf 语法并提取消息结构信息。"
    step3: "生成标准 Protocol Buffer 定义，支持消息类型、字段选项和服务定义。生成的 .proto 文件可为多种编程语言编译。"
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf 模式"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas 是 Python 中最受欢迎的数据分析库，DataFrame 是其核心数据结构。它提供强大的数据操作、清理和分析功能，广泛用于数据科学、机器学习和商业智能。是 Python 开发者和数据分析师不可缺少的工具。"
    step1: "上传包含 DataFrame 代码的 Python 文件或粘贴数据。工具解析 Pandas 语法并提取 DataFrame 结构信息。"
    step3: "生成标准 Pandas DataFrame 代码，支持数据类型规范、索引设置和数据操作。生成的代码可直接在 Python 环境中执行，用于数据分析和处理。"
    from_alias: "Pandas DataFrame"
    to_alias: "Python 数据结构"
  RDF:
    alias: "RDF 三元组"
    what: "RDF（资源描述框架）是 Web 上数据交换的标准模型，旨在以图形形式表示有关资源的信息。广泛用于语义网、知识图谱和链接数据应用。其三元组结构能够实现丰富的元数据表示和语义关系。"
    step1: "上传 RDF 文件或粘贴三元组数据。工具解析 RDF 语法并提取语义关系和资源信息。"
    step3: "生成标准 RDF 格式，支持各种序列化（RDF/XML、Turtle、N-Triples）。生成的 RDF 可用于语义网应用、知识库和链接数据系统。"
    from_alias: "RDF 数据"
    to_alias: "RDF 语义格式"
  MATLAB:
    alias: "MATLAB 数组"
    what: "MATLAB 是一款高性能数值计算和可视化软件，广泛用于工程计算、数据分析和算法开发。其数组和矩阵操作功能强大，支持复杂的数学计算和数据处理。是工程师、研究人员和数据科学家的必备工具。"
    step1: "上传 MATLAB .m 文件或粘贴数组数据。工具解析 MATLAB 语法并提取数组结构信息。"
    step3: "生成标准 MATLAB 数组代码，支持多维数组、数据类型规范和变量命名。生成的代码可直接在 MATLAB 环境中执行，用于数据分析和科学计算。"
    from_alias: "MATLAB 数组"
    to_alias: "MATLAB 代码格式"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame 是 R 编程语言的核心数据结构，广泛用于统计分析、数据挖掘和机器学习。R 是统计计算和图形的顶级工具，DataFrame 提供强大的数据操作、统计分析和可视化功能。是数据科学家、统计学家和从事结构化数据分析研究的人员必不可少的工具。"
    step1: "上传 R 数据文件或粘贴 DataFrame 代码。工具解析 R 语法并提取 DataFrame 结构信息，包括列类型、行名和数据内容。"
    step3: "生成标准 R DataFrame 代码，支持数据类型规范、因子水平、行/列名称和 R 特定的数据结构。生成的代码可直接在 R 环境中执行，用于统计分析和数据处理。"
    from_alias: "R DataFrame"
    to_alias: "R 数据框架"
hero:
  start_converting: "开始转换"
  start_generating: "开始生成"
  api_docs: "API 文档"
related:
  section_title: '更多 {{ if and .from (ne .from "generator") }}{{ .from }} 和 {{ end }}{{ .to }} 转换器'
  section_description: '探索更多 {{ if and .from (ne .from "generator") }}{{ .from }} 和 {{ end }}{{ .to }} 格式的转换器。使用我们专业的在线转换工具在多种格式之间转换您的数据。'
  title: "{{ .from }} 转 {{ .to }}"
howto:
  step2: "使用我们具有专业功能的高级在线表格编辑器编辑数据。支持删除空行、去除重复项、数据转置、排序、正则表达式查找替换和实时预览。所有更改都会自动转换为 %s 格式，结果精确可靠。"
  section_title: "如何使用 {{ . }}"
  converter_description: "通过我们的分步指南学习如何将 {{ .from }} 转换为 {{ .to }}。具有高级功能和实时预览的专业在线转换器。"
  generator_description: "学习如何使用我们的在线生成器创建专业的 {{ .to }} 表格。类似 Excel 的编辑、实时预览和即时导出功能。"
extension:
  section_title: "表格检测和提取扩展程序"
  section_description: "一键从任何网站提取表格。即时转换为包括 Excel、CSV、JSON 在内的 30 多种格式 - 无需复制粘贴。"
  features:
    extraction_title: "一键表格提取"
    extraction_description: "无需复制粘贴即可从任何网页即时提取表格 - 专业数据提取变得简单"
    formats_title: "支持 30 多种格式转换"
    formats_description: "使用我们先进的表格转换器将提取的表格转换为 Excel、CSV、JSON、Markdown、SQL 等格式"
    detection_title: "智能表格检测"
    detection_description: "自动检测并高亮显示任何网页上的表格，实现快速数据提取和转换"
  hover_tip: "✨ 将鼠标悬停在任何表格上以查看提取图标"
recommendations:
  section_title: "大学和专业人士推荐"
  section_description: "TableConvert 受到大学、研究机构和开发团队专业人士的信赖，用于可靠的表格转换和数据处理。"
  cards:
    university_title: "威斯康星大学麦迪逊分校"
    university_description: "TableConvert.com - 专业的免费在线表格转换器和数据格式工具"
    university_link: "阅读文章"
    facebook_title: "数据专业社区"
    facebook_description: "在 Facebook 开发者群组中被数据分析师和专业人士分享和推荐"
    facebook_link: "查看帖子"
    twitter_title: "开发者社区"
    twitter_description: "在 X (Twitter) 上被 @xiaoying_eth 和其他开发者推荐用于表格转换"
    twitter_link: "查看推文"
faq:
  section_title: "常见问题"
  section_description: "关于我们免费在线表格转换器、数据格式和转换过程的常见问题。"
  what: "什么是 %s 格式？"
  howto_convert:
    question: "如何免费使用 {{ . }}？"
    answer: "使用我们的免费在线表格转换器上传您的 {{ .from }} 文件、粘贴数据或从网页提取。我们的专业转换器工具可即时将您的数据转换为 {{ .to }} 格式，具有实时预览和高级编辑功能。立即下载或复制转换结果。"
  security:
    question: "使用此在线转换器时我的数据安全吗？"
    answer: "绝对安全！所有表格转换都在您的浏览器中本地进行 - 您的数据永远不会离开您的设备。我们的在线转换器在客户端处理所有内容，确保完全的隐私和数据安全。我们的服务器上不存储任何文件。"
  free:
    question: "TableConvert 真的免费使用吗？"
    answer: "是的，TableConvert 完全免费！所有转换器功能、表格编辑器、数据生成器工具和导出选项都可免费使用，无需费用、注册或隐藏费用。免费在线转换无限文件。"
  filesize:
    question: "在线转换器的文件大小限制是多少？"
    answer: "我们的免费在线表格转换器支持最大 10MB 的文件。对于更大的文件、批处理或企业需求，请使用我们的浏览器扩展程序或具有更高限制的专业 API 服务。"
stats:
  conversions: "已转换表格"
  tables: "已生成表格"
  formats: "数据文件格式"
  rating: "用户评分"
