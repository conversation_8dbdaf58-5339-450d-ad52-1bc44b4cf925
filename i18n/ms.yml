site:
  fullname: "Table Convert"
  name: "TableConvert"
  subtitle: "Penukar dan Penjana Jadual Dalam <PERSON>lian Percuma"
  intro: "TableConvert ialah alat penukar jadual dalam talian percuma dan penjana data yang menyokong penukaran antara 30+ format termasuk Excel, CSV, JSON, Markdown, LaTeX, SQL dan banyak lagi."
  followTwitter: "Ikuti kami di X"
title:
  converter: "%s kepada %s"
  generator: "Penjana %s"
post:
  tags:
    converter: "Penukar"
    editor: "Editor"
    generator: "Penjana"
    maker: "Pembina"
  converter:
    title: "Tukar %s kepada %s <PERSON><PERSON>"
    short: "Alat dalam talian %s kepada %s yang percuma dan berkuasa"
    intro: "Penukar dalam talian %s kepada %s yang mudah digunakan. Ubah data jadual dengan mudah menggunakan alat penukaran intuitif kami. Pantas, boleh dipercayai, dan mesra pengguna."
  generator:
    title: "Editor dan <PERSON> %s <PERSON><PERSON>"
    short: "Alat penjanaan dalam talian %s profesional dengan ciri komprehensif"
    intro: "Penjana dalam talian %s dan editor jadual yang mudah digunakan. Cipta jadual data profesional dengan mudah menggunakan alat intuitif kami dan pratonton masa nyata."
navbar:
  search:
    placeholder: "Cari penukar ..."
  sponsor: "Belikan Saya Kopi"
  extension: "Sambungan"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Sumber Data"
    placeholder: "Tampal data %s anda atau seret fail %s di sini"
    example: "Contoh"
    upload: "Muat Naik Fail"
    extract:
      enter: "Ekstrak dari Halaman Web"
      intro: "Masukkan URL halaman web yang mengandungi data jadual untuk mengekstrak data berstruktur secara automatik"
      btn: "Ekstrak %s"
    excel:
      sheet: "Lembaran Kerja"
      none: "Tiada"
  tableEditor:
    title: "Editor Jadual Dalam Talian"
    undo: "Buat Asal"
    redo: "Buat Semula"
    transpose: "Transpos"
    clear: "Kosongkan"
    deleteBlank: "Padam Kosong"
    deleteDuplicate: "Buang Pendua"
    uppercase: "HURUF BESAR"
    lowercase: "huruf kecil"
    capitalize: "Huruf Besar Awal"
    replace:
      replace: "Cari & Ganti (Regex disokong)"
      subst: "Ganti dengan..."
      btn: "Ganti Semua"
  tableGenerator:
    title: "Penjana Jadual"
    sponsor: "Belikan Saya Kopi"
    copy: "Salin ke Papan Klip"
    download: "Muat Turun Fail"
    tooltip:
      html:
        escape: "Escape aksara khas HTML (&, <, >, \", ') untuk mengelakkan ralat paparan"
        div: "Gunakan susun atur DIV+CSS sebagai ganti tag TABLE tradisional, lebih sesuai untuk reka bentuk responsif"
        minify: "Buang ruang kosong dan pemisah baris untuk menjana kod HTML termampat"
        thead: "Jana struktur kepala jadual standard (&lt;thead&gt;) dan badan (&lt;tbody&gt;)"
        tableCaption: "Tambah tajuk deskriptif di atas jadual (elemen &lt;caption&gt;)"
        tableClass: "Tambah nama kelas CSS pada jadual untuk penyesuaian gaya yang mudah"
        tableId: "Tetapkan pengenal ID unik untuk jadual untuk manipulasi JavaScript"
      jira:
        escape: "Escape aksara paip (|) untuk mengelakkan konflik dengan sintaks jadual Jira"
      json:
        parsingJSON: "Huraikan rentetan JSON dalam sel secara pintar kepada objek"
        minify: "Jana format JSON satu baris padat untuk mengurangkan saiz fail"
        format: "Pilih struktur data JSON keluaran: tatasusunan objek, tatasusunan 2D, dll."
      latex:
        escape: "Escape aksara khas LaTeX (%, &, _, #, $, dll.) untuk memastikan kompilasi yang betul"
        ht: "Tambah parameter kedudukan terapung [!ht] untuk mengawal kedudukan jadual di halaman"
        mwe: "Jana dokumen LaTeX lengkap"
        tableAlign: "Tetapkan penjajaran mendatar jadual di halaman"
        tableBorder: "Konfigurasi gaya sempadan jadual: tiada sempadan, sempadan separa, sempadan penuh"
        label: "Tetapkan label jadual untuk rujukan silang arahan \\ref{}"
        caption: "Tetapkan kapsyen jadual untuk dipaparkan di atas atau bawah jadual"
        location: "Pilih kedudukan paparan kapsyen jadual: atas atau bawah"
        tableType: "Pilih jenis persekitaran jadual: tabular, longtable, array, dll."
      markdown:
        escape: "Escape aksara khas Markdown (*, _, |, \\, dll.) untuk mengelakkan konflik format"
        pretty: "Jajarkan lebar lajur secara automatik untuk menghasilkan format jadual yang lebih cantik"
        simple: "Gunakan sintaks yang dipermudahkan, meninggalkan garis menegak sempadan luar"
        boldFirstRow: "Jadikan teks baris pertama tebal"
        boldFirstColumn: "Jadikan teks lajur pertama tebal"
        firstHeader: "Anggap baris pertama sebagai pengepala dan tambah garis pemisah"
        textAlign: "Tetapkan penjajaran teks lajur: kiri, tengah, kanan"
        multilineHandling: "Pengendalian teks berbilang baris: kekalkan pemisah baris, escape ke \\n, gunakan tag &lt;br&gt;"

        includeLineNumbers: "Tambah lajur nombor baris di sebelah kiri jadual"
      magic:
        builtin: "Pilih format templat biasa yang telah ditetapkan"
        rowsTpl: "<table> <tr> <th>Sintaks Ajaib</th> <th>Penerangan</th> <th>Kaedah JS yang Disokong</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>Medan ke-1, ke-2 ... <b>t</b>ajuk, iaitu {hA} {hB} ...</td> <td>Kaedah rentetan</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>Medan ke-1, ke-2 ... baris semasa, iaitu {$A} {$B} ...</td> <td>Kaedah rentetan</td> </tr> <tr> <td>{F,} {F;}</td> <td>Pisahkan baris semasa dengan rentetan selepas <b>F</b></td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td><b>N</b>ombor baris semasa dari 1 atau 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>N</b>ombor baris <b>a</b>khir </td> <td></td> </tr> <tr> <td>{x ...}</td> <td><b>J</b>alankan kod JavaScript, cth: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Gunakan garis miring terbalik <b>\\</b> untuk output kurungan {...} </td> <td></td> </tr></table>"
        headerTpl: "Templat keluaran tersuai untuk bahagian pengepala"
        footerTpl: "Templat keluaran tersuai untuk bahagian kaki"
      textile:
        escape: "Escape karakter sintaks Textile (|, ., -, ^) untuk mengelakkan konflik format"
        rowHeader: "Tetapkan baris pertama sebagai baris pengepala"
        thead: "Tambah penanda sintaks Textile untuk kepala dan badan jadual"
      xml:
        escape: "Escape karakter khas XML (&lt;, &gt;, &amp;, \", ') untuk memastikan XML yang sah"
        minify: "Jana output XML termampat, mengeluarkan ruang kosong tambahan"
        rootElement: "Tetapkan nama tag elemen root XML"
        rowElement: "Tetapkan nama tag elemen XML untuk setiap baris data"
        declaration: "Tambah pengepala deklarasi XML (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Output data sebagai atribut XML bukannya elemen anak"
        cdata: "Balut kandungan teks dengan CDATA untuk melindungi karakter khas"
        encoding: "Tetapkan format pengekodan karakter untuk dokumen XML"
        indentation: "Pilih karakter indentasi XML: ruang atau tab"
      yaml:
        indentSize: "Tetapkan bilangan ruang untuk indentasi hierarki YAML (biasanya 2 atau 4)"
        arrayStyle: "Format array: blok (satu item per baris) atau aliran (format inline)"
        quotationStyle: "Gaya petikan rentetan: tiada petikan, petikan tunggal, petikan berganda"
      csv:
        bom: "Tambah tanda urutan bait UTF-8 untuk membantu Excel dan perisian lain mengenali pengekodan"
      excel:
        autoWidth: "Laraskan lebar lajur secara automatik berdasarkan kandungan"
        protectSheet: "Dayakan perlindungan lembaran kerja dengan kata laluan: tableconvert.com"
      sql:  
        primaryKey: "Tentukan nama medan kunci utama untuk pernyataan CREATE TABLE"
        dialect: "Pilih jenis pangkalan data, mempengaruhi sintaks petikan dan jenis data"
      ascii:
        forceSep: "Paksa garis pemisah antara setiap baris data"
        style: "Pilih gaya lukisan sempadan jadual ASCII"
        comment: "Tambah penanda komen untuk membalut keseluruhan jadual"
      mediawiki:
        minify: "Mampat kod output, mengeluarkan ruang kosong tambahan"
        header: "Tandakan baris pertama sebagai gaya pengepala"
        sort: "Dayakan fungsi penyusunan klik jadual"
      asciidoc:
        minify: "Mampat output format AsciiDoc"
        firstHeader: "Tetapkan baris pertama sebagai baris pengepala"
        lastFooter: "Tetapkan baris terakhir sebagai baris pengaki"
        title: "Tambah teks tajuk pada jadual"
      tracwiki:
        rowHeader: "Tetapkan baris pertama sebagai pengepala"
        colHeader: "Tetapkan lajur pertama sebagai pengepala"
      bbcode:
        minify: "Mampat format output BBCode"
      restructuredtext:
        style: "Pilih gaya sempadan jadual reStructuredText"
        forceSep: "Paksa garis pemisah"
      pdf:
        theme: "Pilih gaya visual jadual PDF untuk dokumen profesional"
        headerColor: "Pilih warna latar belakang pengepala untuk jadual PDF"
        showHead: "Kawal paparan pengepala merentasi halaman PDF"
        docTitle: "Tajuk pilihan untuk dokumen PDF"
        docDescription: "Teks penerangan pilihan untuk dokumen PDF"
    label:
      ascii:
        forceSep: "Pemisah Baris"
        style: "Gaya Sempadan"
        comment: "Pembungkus Komen"
      restructuredtext:
        style: "Gaya Sempadan"
        forceSep: "Paksa Pemisah"
      bbcode:
        minify: "Minify Output"
      csv:
        doubleQuote: "Balut Petikan Berganda"
        delimiter: "Pemisah Medan"
        bom: "UTF-8 BOM"
        valueDelimiter: "Pemisah Nilai"
        rowDelimiter: "Pemisah Baris"
        prefix: "Awalan Baris"
        suffix: "Akhiran Baris"
      excel:
        autoWidth: "Lebar Auto"
        textFormat: "Format Teks"
        protectSheet: "Lindungi Lembaran"
        boldFirstRow: "Baris Pertama Tebal"
        boldFirstColumn: "Lajur Pertama Tebal"
        sheetName: "Nama Lembaran"
      html:
        escape: "Escape Aksara HTML"
        div: "Jadual DIV"
        minify: "Minifikasi Kod"
        thead: "Struktur Kepala Jadual"
        tableCaption: "Keterangan Jadual"
        tableClass: "Kelas Jadual"
        tableId: "ID Jadual"
        rowHeader: "Kepala Baris"
        colHeader: "Kepala Lajur"
      jira:
        escape: "Escape Aksara"
        rowHeader: "Kepala Baris"
        colHeader: "Kepala Lajur"
      json:
        parsingJSON: "Parse JSON"
        minify: "Minify Output"
        format: "Format Data"
        rootName: "Nama Objek Root"
        indentSize: "Saiz Inden"
      jsonlines:
        parsingJSON: "Parse JSON"
        format: "Format Data"
      latex:
        escape: "Escape Aksara Jadual LaTeX"
        ht: "Kedudukan Terapung"
        mwe: "Dokumen Lengkap"
        tableAlign: "Penjajaran Jadual"
        tableBorder: "Gaya Sempadan"
        label: "Label Rujukan"
        caption: "Keterangan Jadual"
        location: "Kedudukan Keterangan"
        tableType: "Jenis Jadual"
        boldFirstRow: "Tebal Baris Pertama"
        boldFirstColumn: "Tebal Lajur Pertama"
        textAlign: "Penjajaran Teks"
        borders: "Tetapan Sempadan"
      markdown:
        escape: "Escape Aksara"
        pretty: "Jadual Markdown Cantik"
        simple: "Format Markdown Mudah"
        boldFirstRow: "Tebal Baris Pertama"
        boldFirstColumn: "Tebal Lajur Pertama"
        firstHeader: "Pengepala Pertama"
        textAlign: "Penjajaran Teks"
        multilineHandling: "Pengendalian Berbilang Baris"

        includeLineNumbers: "Tambah Nombor Baris"
        align: "Penjajaran"
      mediawiki:
        minify: "Minify Kod"
        header: "Markup Kepala"
        sort: "Boleh Disusun"
      asciidoc:
        minify: "Minify Format"
        firstHeader: "Kepala Pertama"
        lastFooter: "Kaki Terakhir"
        title: "Tajuk Jadual"
      tracwiki:
        rowHeader: "Kepala Baris"
        colHeader: "Kepala Lajur"
      sql:
        drop: "Buang Jadual (Jika Wujud)"
        create: "Cipta Jadual"
        oneInsert: "Sisipan Kelompok"
        table: "Nama Jadual"
        dialect: "Jenis Pangkalan Data"
        primaryKey: "Kunci Utama"
      magic:
        builtin: "Templat Terbina"
        rowsTpl: "Templat Baris, Sintaks ->"
        headerTpl: "Templat Kepala"
        footerTpl: "Templat Kaki"
      textile:
        escape: "Escape Aksara"
        rowHeader: "Kepala Baris"
        thead: "Sintaks Kepala Jadual"
      xml:
        escape: "Escape Aksara XML"
        minify: "Minify Output"
        rootElement: "Elemen Root"
        rowElement: "Elemen Baris"
        declaration: "Deklarasi XML"
        attributes: "Mod Atribut"
        cdata: "Pembungkus CDATA"
        encoding: "Pengekodan"
        indentSize: "Saiz Inden"
      yaml:
        indentSize: "Saiz Inden"
        arrayStyle: "Gaya Array"
        quotationStyle: "Gaya Petikan"
      pdf:
        theme: "Tema Jadual PDF"
        headerColor: "Warna Kepala PDF"
        showHead: "Paparan Kepala PDF"
        docTitle: "Tajuk Dokumen PDF"
        docDescription: "Penerangan Dokumen PDF"

sidebar:
  all: "Semua Alat Penukaran"
  dataSource:
    title: "Sumber Data"
    description:
      converter: "Import %s untuk penukaran kepada %s. Menyokong muat naik fail, penyuntingan dalam talian, dan pengekstrakan data web."
      generator: "Cipta data jadual dengan sokongan untuk pelbagai kaedah input termasuk input manual, import fail, dan penjanaan templat."
  tableEditor:
    title: "Editor Jadual Dalam Talian"
    description:
      converter: "Proses %s dalam talian menggunakan editor jadual kami. Pengalaman operasi seperti Excel dengan sokongan untuk memadam baris kosong, penyahduaan, pengisihan, dan cari & ganti."
      generator: "Editor jadual dalam talian yang berkuasa menyediakan pengalaman operasi seperti Excel. Menyokong memadam baris kosong, penyahduaan, pengisihan, dan cari & ganti."
  tableGenerator:
    title: "Penjana Jadual"
    description:
      converter: "Jana %s dengan cepat dengan pratonton masa nyata penjana jadual. Pilihan eksport yang kaya, salinan dan muat turun satu klik."
      generator: "Eksport data %s dalam pelbagai format untuk memenuhi senario penggunaan yang berbeza. Menyokong pilihan tersuai dan pratonton masa nyata."
footer:
  changelog: "Log Perubahan"
  sponsor: "Penaja"
  contact: "Hubungi Kami"
  privacyPolicy: "Dasar Privasi"
  about: "Tentang"
  resources: "Sumber"
  popularConverters: "Penukar Popular"
  popularGenerators: "Penjana Popular"
  dataSecurity: "Data anda selamat - semua penukaran berjalan dalam pelayar anda."
converters:
  Markdown:
    alias: "Jadual Markdown"
    what: "Markdown adalah bahasa markup ringan yang digunakan secara meluas untuk dokumentasi teknikal, penciptaan kandungan blog, dan pembangunan web. Sintaks jadualnya ringkas dan intuitif, menyokong penjajaran teks, pembenaman pautan, dan pemformatan. Ia adalah alat pilihan untuk pengaturcara dan penulis teknikal, serasi sepenuhnya dengan GitHub, GitLab, dan platform hosting kod lain."
    step1: "Tampal data jadual Markdown ke dalam kawasan sumber data, atau seret dan lepas fail .md secara terus untuk muat naik. Alat ini secara automatik menghuraikan struktur dan pemformatan jadual, menyokong kandungan bersarang yang kompleks dan pengendalian aksara khas."
    step3: "Jana kod jadual Markdown standard dalam masa nyata, menyokong pelbagai kaedah penjajaran, penebalan teks, penambahan nombor baris, dan tetapan format lanjutan lain. Kod yang dijana serasi sepenuhnya dengan GitHub dan editor Markdown utama, sedia untuk digunakan dengan salinan satu klik."
    from_alias: "Fail Jadual Markdown"
    to_alias: "Format Jadual Markdown"
  Magic:
    alias: "Templat Tersuai"
    what: "Templat Magic adalah penjana data lanjutan unik alat ini, membolehkan pengguna mencipta output data format sewenang-wenangnya melalui sintaks templat tersuai. Menyokong penggantian pembolehubah, pertimbangan bersyarat, dan pemprosesan gelung. Ia adalah penyelesaian muktamad untuk mengendalikan keperluan penukaran data yang kompleks dan format output yang diperibadikan, terutamanya sesuai untuk pembangun dan jurutera data."
    step1: "Pilih templat biasa terbina dalam atau cipta sintaks templat tersuai. Menyokong pembolehubah dan fungsi yang kaya yang boleh mengendalikan struktur data yang kompleks dan logik perniagaan."
    step3: "Jana output data yang memenuhi sepenuhnya keperluan format tersuai. Menyokong logik penukaran data yang kompleks dan pemprosesan bersyarat, meningkatkan kecekapan pemprosesan data dan kualiti output dengan ketara. Alat yang berkuasa untuk pemprosesan data kelompok."
    from_alias: "Data Jadual"
    to_alias: "Output Format Tersuai"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) adalah format pertukaran data yang paling banyak digunakan, disokong sepenuhnya oleh Excel, Google Sheets, sistem pangkalan data, dan pelbagai alat analisis data. Struktur mudah dan keserasian yang kuat menjadikannya format standard untuk migrasi data, import/eksport kelompok, dan pertukaran data merentas platform, digunakan secara meluas dalam analisis perniagaan, sains data, dan integrasi sistem."
    step1: "Muat naik fail CSV atau tampal data CSV secara terus. Alat ini secara bijak mengenali pelbagai pemisah (koma, tab, koma bertitik, paip, dll.), secara automatik mengesan jenis data dan format pengekodan, menyokong penghuraian pantas fail besar dan struktur data yang kompleks."
    step3: "Jana fail format CSV standard dengan sokongan untuk pemisah tersuai, gaya petikan, format pengekodan, dan tetapan tanda BOM. Memastikan keserasian sempurna dengan sistem sasaran, menyediakan pilihan muat turun dan pemampatan untuk memenuhi keperluan pemprosesan data peringkat perusahaan."
    from_alias: "Fail Data CSV"
    to_alias: "Format Standard CSV"
  JSON:
    alias: "Array JSON"
    what: "JSON (JavaScript Object Notation) adalah format data jadual standard untuk aplikasi web moden, REST API, dan seni bina mikroservis. Struktur yang jelas dan penghuraian yang cekap menjadikannya digunakan secara meluas dalam interaksi data frontend dan backend, penyimpanan fail konfigurasi, dan pangkalan data NoSQL. Menyokong objek bersarang, struktur array, dan pelbagai jenis data, menjadikannya data jadual yang tidak dapat digantikan untuk pembangunan perisian moden."
    step1: "Muat naik fail JSON atau tampal array JSON. Menyokong pengecaman automatik dan penghuraian array objek, struktur bersarang, dan jenis data yang kompleks. Alat ini secara bijak mengesahkan sintaks JSON dan memberikan gesaan ralat."
    step3: "Jana pelbagai output format JSON: array objek standard, array 2D, array lajur, dan format pasangan kunci-nilai. Menyokong output yang dipercantik, mod pemampatan, nama objek akar tersuai, dan tetapan indentasi, menyesuaikan dengan sempurna kepada pelbagai antara muka API dan keperluan penyimpanan data."
    from_alias: "Fail Array JSON"
    to_alias: "Format Standard JSON"
  JSONLines:
    alias: "Format JSONLines"
    what: "JSON Lines (juga dikenali sebagai NDJSON) adalah format penting untuk pemprosesan data besar dan penghantaran data streaming, dengan setiap baris mengandungi objek JSON yang bebas. Digunakan secara meluas dalam analisis log, pemprosesan aliran data, pembelajaran mesin, dan sistem teragih. Menyokong pemprosesan bertambah dan pengkomputeran selari, menjadikannya pilihan ideal untuk mengendalikan data berstruktur berskala besar."
    step1: "Muat naik fail JSONLines atau tampal data. Alat ini menghuraikan objek JSON baris demi baris, menyokong pemprosesan streaming fail besar dan fungsi melangkau baris ralat."
    step3: "Jana format JSONLines standard dengan setiap baris mengeluarkan objek JSON yang lengkap. Sesuai untuk pemprosesan streaming, import kelompok, dan senario analisis data besar, menyokong pengesahan data dan pengoptimuman format."
    from_alias: "Data JSONLines"
    to_alias: "Format Streaming JSONLines"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) adalah format standard untuk pertukaran data peringkat perusahaan dan pengurusan konfigurasi, dengan spesifikasi sintaks yang ketat dan mekanisme pengesahan yang berkuasa. Digunakan secara meluas dalam perkhidmatan web, fail konfigurasi, penyimpanan dokumen, dan integrasi sistem. Menyokong ruang nama, pengesahan skema, dan transformasi XSLT, menjadikannya data jadual penting untuk aplikasi perusahaan."
    step1: "Muat naik fail XML atau tampal data XML. Alat ini secara automatik menghuraikan struktur XML dan menukarkannya kepada format jadual, menyokong ruang nama, pengendalian atribut, dan struktur bersarang yang kompleks."
    step3: "Jana output XML yang mematuhi standard XML. Menyokong elemen akar tersuai, nama elemen baris, mod atribut, pembungkusan CDATA, dan tetapan pengekodan aksara. Memastikan integriti data dan keserasian, memenuhi keperluan aplikasi peringkat perusahaan."
    from_alias: "Fail Data XML"
    to_alias: "Format Standard XML"
  YAML:
    alias: "Konfigurasi YAML"
    what: "YAML adalah standard pensirialan data yang mesra manusia, terkenal dengan struktur hierarki yang jelas dan sintaks yang ringkas. Digunakan secara meluas dalam fail konfigurasi, rantaian alat DevOps, Docker Compose, dan penggunaan Kubernetes. Kebolehbacaan yang kuat dan sintaks yang ringkas menjadikannya format konfigurasi penting untuk aplikasi cloud-native moden dan operasi automatik."
    step1: "Muat naik fail YAML atau tampal data YAML. Alat ini secara bijak menghuraikan struktur YAML dan mengesahkan ketepatan sintaks, menyokong format multi-dokumen dan jenis data yang kompleks."
    step3: "Jana output format YAML standard dengan sokongan untuk gaya array blok dan aliran, tetapan petikan berbilang, indentasi tersuai, dan pemeliharaan komen. Memastikan fail YAML output serasi sepenuhnya dengan pelbagai penghurai dan sistem konfigurasi."
    from_alias: "Fail Konfigurasi YAML"
    to_alias: "Format Standard YAML"
  MySQL:
      alias: "Hasil Pertanyaan MySQL"
      what: "MySQL adalah sistem pengurusan pangkalan data hubungan sumber terbuka yang paling popular di dunia, terkenal dengan prestasi tinggi, kebolehpercayaan, dan kemudahan penggunaan. Digunakan secara meluas dalam aplikasi web, sistem perusahaan, dan platform analisis data. Hasil pertanyaan MySQL biasanya mengandungi data jadual berstruktur, berfungsi sebagai sumber data penting dalam pengurusan pangkalan data dan kerja analisis data."
      step1: "Tampal hasil output pertanyaan MySQL ke dalam kawasan sumber data. Alat ini secara automatik mengenali dan menghuraikan format output baris arahan MySQL, menyokong pelbagai gaya hasil pertanyaan dan pengekodan aksara, mengendalikan pengepala dan baris data secara bijak."
      step3: "Tukar hasil pertanyaan MySQL dengan pantas kepada pelbagai format data jadual, memudahkan analisis data, penjanaan laporan, migrasi data merentas sistem, dan pengesahan data. Alat praktikal untuk pentadbir pangkalan data dan penganalisis data."
      from_alias: "Output Pertanyaan MySQL"
      to_alias: "Data Jadual MySQL"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) adalah bahasa operasi standard untuk pangkalan data hubungan, digunakan untuk pertanyaan data, operasi sisip, kemas kini, dan padam. Sebagai teknologi teras pengurusan pangkalan data, SQL digunakan secara meluas dalam analisis data, kecerdasan perniagaan, pemprosesan ETL, dan pembinaan gudang data. Ia adalah alat kemahiran penting untuk profesional data."
    step1: "Tampal penyata INSERT SQL atau muat naik fail .sql. Alat ini secara bijak menghuraikan sintaks SQL dan mengekstrak data jadual, menyokong pelbagai dialek SQL dan pemprosesan penyata pertanyaan yang kompleks."
    step3: "Jana penyata INSERT SQL standard dan penyata penciptaan jadual. Menyokong pelbagai dialek pangkalan data (MySQL, PostgreSQL, SQLite, SQL Server, Oracle), secara automatik mengendalikan pemetaan jenis data, pelarian aksara, dan kekangan kunci utama. Memastikan kod SQL yang dijana boleh dilaksanakan secara terus."
    from_alias: "Insert SQL"
    to_alias: "Penyata SQL"
  Qlik:
      alias: "Jadual Qlik"
      what: "Qlik adalah vendor perisian yang mengkhusus dalam visualisasi data, papan pemuka eksekutif, dan produk kecerdasan perniagaan layan diri, bersama dengan Tableau dan Microsoft."
      step1: ""
      step3: "Akhirnya, [Penjana Jadual](#TableGenerator) menunjukkan hasil penukaran. Gunakan dalam Qlik Sense, Qlik AutoML, QlikView, atau perisian berkemampuan Qlik lain anda."
      from_alias: "Jadual Qlik"
      to_alias: "Jadual Qlik"
  DAX:
      alias: "Jadual DAX"
      what: "DAX (Data Analysis Expressions) adalah bahasa pengaturcaraan yang digunakan di seluruh Microsoft Power BI untuk mencipta lajur yang dikira, ukuran, dan jadual tersuai."
      step1: ""
      step3: "Akhirnya, [Penjana Jadual](#TableGenerator) menunjukkan hasil penukaran. Seperti yang dijangkakan, ia digunakan dalam beberapa produk Microsoft termasuk Microsoft Power BI, Microsoft Analysis Services, dan Microsoft Power Pivot untuk Excel."
      from_alias: "Jadual DAX"
      to_alias: "Jadual DAX"
  Firebase:
    alias: "Senarai Firebase"
    what: "Firebase adalah platform pembangunan aplikasi BaaS yang menyediakan perkhidmatan backend yang dihoskan seperti pangkalan data masa nyata, penyimpanan awan, pengesahan, pelaporan ranap, dll."
    step1: ""
    step3: "Akhirnya, [Penjana Jadual](#TableGenerator) menunjukkan hasil penukaran. Anda kemudian boleh menggunakan kaedah tolak dalam API Firebase untuk menambah kepada senarai data dalam pangkalan data Firebase."
    from_alias: "Senarai Firebase"
    to_alias: "Senarai Firebase"
  HTML:
    alias: "Jadual HTML"
    what: "Jadual HTML adalah cara standard untuk memaparkan data berstruktur dalam halaman web, dibina dengan tag table, tr, td dan lain-lain. Menyokong penyesuaian gaya yang kaya, susun atur responsif, dan fungsi interaktif. Digunakan secara meluas dalam pembangunan laman web, paparan data, dan penjanaan laporan, berfungsi sebagai komponen penting pembangunan frontend dan reka bentuk web."
    step1: "Tampal kod HTML yang mengandungi jadual atau muat naik fail HTML. Alat ini secara automatik mengenali dan mengekstrak data jadual dari halaman, menyokong struktur HTML yang kompleks, gaya CSS, dan pemprosesan jadual bersarang."
    step3: "Jana kod jadual HTML semantik dengan sokongan untuk struktur thead/tbody, tetapan kelas CSS, kapsyen jadual, pengepala baris/lajur, dan konfigurasi atribut responsif. Memastikan kod jadual yang dijana memenuhi standard web dengan kebolehcapaian yang baik dan mesra SEO."
    from_alias: "Jadual HTML"
    to_alias: "Jadual HTML"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel adalah perisian hamparan yang paling popular di dunia, digunakan secara meluas dalam analisis perniagaan, pengurusan kewangan, pemprosesan data, dan penciptaan laporan. Keupayaan pemprosesan data yang berkuasa, perpustakaan fungsi yang kaya, dan ciri visualisasi yang fleksibel menjadikannya alat standard untuk automasi pejabat dan analisis data, dengan aplikasi yang luas merentas hampir semua industri dan bidang."
    step1: "Muat naik fail Excel (menyokong format .xlsx, .xls) atau salin data jadual terus dari Excel dan tampal. Alat ini menyokong pemprosesan multi-lembaran kerja, pengecaman format yang kompleks, dan penghuraian pantas fail besar, secara automatik mengendalikan sel yang digabungkan dan jenis data."
    step3: "Jana data jadual yang serasi dengan Excel yang boleh ditampal terus ke dalam Excel atau dimuat turun sebagai fail .xlsx standard. Menyokong penamaan lembaran kerja, pemformatan sel, lebar lajur automatik, penggayaan pengepala, dan tetapan pengesahan data. Memastikan fail Excel output mempunyai penampilan profesional dan fungsi yang lengkap."
    from_alias: "Hamparan Excel"
    to_alias: "Excel"
  LaTeX:
    alias: "Jadual LaTeX"
    what: "LaTeX adalah sistem penyusunan dokumen profesional, terutamanya sesuai untuk mencipta kertas akademik, dokumen teknikal, dan penerbitan saintifik. Fungsi jadualnya berkuasa, menyokong formula matematik yang kompleks, kawalan susun atur yang tepat, dan output PDF berkualiti tinggi. Ia adalah alat standard dalam akademia dan penerbitan saintifik, digunakan secara meluas dalam kertas jurnal, disertasi, dan penyusunan manual teknikal."
    step1: "Tampal kod jadual LaTeX atau muat naik fail .tex. Alat ini menghuraikan sintaks jadual LaTeX dan mengekstrak kandungan data, menyokong pelbagai persekitaran jadual (tabular, longtable, array, dll.) dan arahan format yang kompleks."
    step3: "Jana kod jadual LaTeX profesional dengan sokongan untuk pemilihan persekitaran jadual berbilang, konfigurasi gaya sempadan, tetapan kedudukan kapsyen, spesifikasi kelas dokumen, dan pengurusan pakej. Boleh menjana dokumen LaTeX yang boleh dikompil lengkap, memastikan jadual output memenuhi standard penerbitan akademik."
    from_alias: "Jadual LaTeX"
    to_alias: "Jadual LaTeX"
  ASCII:
    alias: "Jadual Teks ASCII"
    what: "Jadual ASCII menggunakan aksara teks biasa untuk melukis sempadan dan struktur jadual, menyediakan keserasian dan mudah alih yang terbaik. Serasi dengan semua editor teks, persekitaran terminal, dan sistem operasi. Digunakan secara meluas dalam dokumentasi kod, manual teknikal, fail README, dan output alat baris arahan. Format paparan data pilihan untuk pengaturcara dan pentadbir sistem."
    step1: "Muat naik fail teks yang mengandungi jadual ASCII atau tampal data jadual secara terus. Alat ini secara bijak mengenali dan menghuraikan struktur jadual ASCII, menyokong pelbagai gaya sempadan dan format penjajaran."
    step3: "Jana jadual ASCII teks biasa yang cantik dengan sokongan untuk pelbagai gaya sempadan (garis tunggal, garis berganda, sudut bulat, dll.), kaedah penjajaran teks, dan lebar lajur automatik. Jadual yang dijana dipaparkan dengan sempurna dalam editor kod, dokumen, dan baris arahan."
    from_alias: "Jadual Teks ASCII"
    to_alias: "Jadual Teks ASCII"
  MediaWiki:
    alias: "Jadual MediaWiki"
    what: "MediaWiki adalah platform perisian sumber terbuka yang digunakan oleh laman wiki terkenal seperti Wikipedia. Sintaks jadualnya ringkas namun berkuasa, menyokong penyesuaian gaya jadual, fungsi penyusunan, dan pembenaman pautan. Digunakan secara meluas dalam pengurusan pengetahuan, penyuntingan kolaboratif, dan sistem pengurusan kandungan, berfungsi sebagai teknologi teras untuk membina ensiklopedia wiki dan pangkalan pengetahuan."
    step1: "Tampal kod jadual MediaWiki atau muat naik fail sumber wiki. Alat ini menghuraikan sintaks markup wiki dan mengekstrak data jadual, menyokong sintaks wiki yang kompleks dan pemprosesan templat."
    step3: "Jana kod jadual MediaWiki standard dengan sokongan untuk tetapan gaya pengepala, penjajaran sel, membolehkan fungsi penyusunan, dan pilihan pemampatan kod. Kod yang dijana boleh digunakan secara terus untuk penyuntingan halaman wiki, memastikan paparan sempurna pada platform MediaWiki."
    from_alias: "Jadual MediaWiki"
    to_alias: "Jadual MediaWiki"
  TracWiki:
    alias: "Jadual TracWiki"
    what: "Trac adalah sistem pengurusan projek dan penjejakan pepijat berasaskan web yang menggunakan sintaks wiki yang dipermudahkan untuk mencipta kandungan jadual."
    step1: "Muat naik fail TracWiki atau tampal data jadual."
    step3: "Jana kod jadual yang serasi dengan TracWiki dengan sokongan tetapan pengepala baris/lajur, memudahkan pengurusan dokumen projek."
    from_alias: "Jadual TracWiki"
    to_alias: "Jadual TracWiki"
  AsciiDoc:
    alias: "Jadual AsciiDoc"
    what: "AsciiDoc adalah bahasa penanda ringan yang boleh ditukar kepada HTML, PDF, halaman manual, dan format lain, digunakan secara meluas untuk penulisan dokumentasi teknikal."
    step1: "Muat naik fail AsciiDoc atau tampal data."
    step3: "Jana sintaks jadual AsciiDoc dengan sokongan tetapan pengepala, pengaki dan tajuk, boleh digunakan terus dalam editor AsciiDoc."
    from_alias: "Jadual AsciiDoc"
    to_alias: "Jadual AsciiDoc"
  reStructuredText:
    alias: "Jadual reStructuredText"
    what: "reStructuredText adalah format dokumentasi standard untuk komuniti Python, menyokong sintaks jadual yang kaya, biasa digunakan untuk penjanaan dokumentasi Sphinx."
    step1: "Muat naik fail .rst atau tampal data reStructuredText."
    step3: "Jana jadual reStructuredText standard dengan sokongan pelbagai gaya sempadan, boleh digunakan terus dalam projek dokumentasi Sphinx."
    from_alias: "Jadual reStructuredText"
    to_alias: "Jadual reStructuredText"
  PHP:
    alias: "Array PHP"
    what: "PHP adalah bahasa skrip sisi pelayan yang popular, dengan array sebagai struktur data terasnya, digunakan secara meluas dalam pembangunan web dan pemprosesan data."
    step1: "Muat naik fail yang mengandungi array PHP atau tampal data secara langsung."
    step3: "Jana kod array PHP standard yang boleh digunakan secara langsung dalam projek PHP, menyokong format array bersekutu dan berindeks."
    from_alias: "Array PHP"
    to_alias: "Kod PHP"
  Ruby:
    alias: "Array Ruby"
    what: "Ruby adalah bahasa pengaturcaraan berorientasikan objek dinamik dengan sintaks yang ringkas dan elegan, dengan array sebagai struktur data penting."
    step1: "Muat naik fail Ruby atau tampal data array."
    step3: "Jana kod array Ruby yang mematuhi spesifikasi sintaks Ruby, boleh digunakan secara langsung dalam projek Ruby."
    from_alias: "Array Ruby"
    to_alias: "Kod Ruby"
  ASP:
    alias: "Array ASP"
    what: "ASP (Active Server Pages) adalah persekitaran skrip sisi pelayan Microsoft, menyokong pelbagai bahasa pengaturcaraan untuk membangunkan halaman web dinamik."
    step1: "Muat naik fail ASP atau tampal data array."
    step3: "Jana kod array yang serasi dengan ASP dengan sokongan untuk sintaks VBScript dan JScript, boleh digunakan dalam projek ASP.NET."
    from_alias: "Array ASP"
    to_alias: "Kod ASP"
  ActionScript:
    alias: "Array ActionScript"
    what: "ActionScript adalah bahasa pengaturcaraan berorientasikan objek yang digunakan terutamanya untuk pembangunan aplikasi Adobe Flash dan AIR."
    step1: "Muat naik fail .as atau tampal data ActionScript."
    step3: "Jana kod array ActionScript yang mematuhi standard sintaks AS3, boleh digunakan untuk pembangunan projek Flash dan Flex."
    from_alias: "Array ActionScript"
    to_alias: "Kod ActionScript"
  BBCode:
    alias: "Jadual BBCode"
    what: "BBCode adalah bahasa penanda ringan yang biasa digunakan dalam forum dan komuniti dalam talian, menyediakan fungsi pemformatan mudah termasuk sokongan jadual."
    step1: "Muat naik fail yang mengandungi BBCode atau tampal data."
    step3: "Jana kod jadual BBCode yang sesuai untuk penyiaran forum dan penciptaan kandungan komuniti, dengan sokongan untuk format output termampat."
    from_alias: "Jadual BBCode"
    to_alias: "Jadual BBCode"
  PDF:
    alias: "Jadual PDF"
    what: "PDF (Portable Document Format) adalah standard dokumen merentas platform dengan susun atur tetap, paparan konsisten, dan ciri-ciri percetakan berkualiti tinggi. Digunakan secara meluas dalam dokumen rasmi, laporan, invois, kontrak, dan kertas akademik. Format pilihan untuk komunikasi perniagaan dan pengarkiban dokumen, memastikan kesan visual yang konsisten sepenuhnya merentas peranti dan sistem operasi yang berbeza."
    step1: "Import data jadual dalam sebarang format. Alat ini secara automatik menganalisis struktur data dan melakukan reka bentuk susun atur pintar, menyokong penomboran halaman automatik jadual besar dan pemprosesan jenis data yang kompleks."
    step3: "Jana fail jadual PDF berkualiti tinggi dengan sokongan untuk pelbagai gaya tema profesional (perniagaan, akademik, minimalis, dll.), fon berbilang bahasa, penomboran halaman automatik, penambahan tera air, dan pengoptimuman cetak. Memastikan dokumen PDF output mempunyai penampilan profesional, boleh digunakan secara langsung untuk pembentangan perniagaan dan penerbitan rasmi."
    from_alias: "Data Jadual"
    to_alias: "Jadual PDF"
  JPEG:
    alias: "Imej JPEG"
    what: "JPEG adalah format imej digital yang paling banyak digunakan dengan kesan mampatan yang sangat baik dan keserasian yang luas. Saiz fail yang kecil dan kelajuan pemuatan yang pantas menjadikannya sesuai untuk paparan web, perkongsian media sosial, ilustrasi dokumen, dan pembentangan dalam talian. Format imej standard untuk media digital dan komunikasi rangkaian, disokong dengan sempurna oleh hampir semua peranti dan perisian."
    step1: "Import data jadual dalam sebarang format. Alat ini melakukan reka bentuk susun atur pintar dan pengoptimuman visual, mengira secara automatik saiz dan resolusi yang optimum."
    step3: "Jana imej jadual JPEG definisi tinggi dengan sokongan untuk pelbagai skema warna tema (terang, gelap, mesra mata, dll.), susun atur adaptif, pengoptimuman kejelasan teks, dan penyesuaian saiz. Sesuai untuk perkongsian dalam talian, penyisipan dokumen, dan penggunaan pembentangan, memastikan kesan visual yang sangat baik pada pelbagai peranti paparan."
    from_alias: "Data Jadual"
    to_alias: "Imej JPEG"
  Jira:
    alias: "Jadual Jira"
    what: "JIRA adalah perisian pengurusan projek profesional dan penjejakan pepijat yang dibangunkan oleh Atlassian, digunakan secara meluas dalam pembangunan tangkas, ujian perisian, dan kerjasama projek. Fungsi jadualnya menyokong pilihan pemformatan yang kaya dan paparan data, berfungsi sebagai alat penting untuk pasukan pembangunan perisian, pengurus projek, dan kakitangan jaminan kualiti dalam pengurusan keperluan, penjejakan pepijat, dan pelaporan kemajuan."
    step1: "Muat naik fail yang mengandungi data jadual atau tampal kandungan data secara langsung. Alat ini secara automatik memproses data jadual dan melarikan diri aksara khas."
    step3: "Jana kod jadual yang serasi dengan platform JIRA dengan sokongan untuk tetapan gaya pengepala, penjajaran sel, pemprosesan melarikan diri aksara, dan pengoptimuman format. Kod yang dijana boleh ditampal secara langsung ke dalam penerangan isu JIRA, komen, atau halaman wiki, memastikan paparan dan rendering yang betul dalam sistem JIRA."
    from_alias: "Jadual Jira"
    to_alias: "Jadual Jira"
  Textile:
    alias: "Jadual Textile"
    what: "Textile adalah bahasa penanda ringan yang ringkas dengan sintaks yang mudah dan mudah dipelajari, digunakan secara meluas dalam sistem pengurusan kandungan, platform blog, dan sistem forum. Sintaks jadualnya jelas dan intuitif, menyokong pemformatan pantas dan tetapan gaya. Alat yang ideal untuk pencipta kandungan dan pentadbir laman web untuk penulisan dokumen pantas dan penerbitan kandungan."
    step1: "Muat naik fail format Textile atau tampal data jadual. Alat ini menghuraikan sintaks markup Textile dan mengekstrak kandungan jadual."
    step3: "Jana sintaks jadual Textile standard dengan sokongan untuk markup pengepala, penjajaran sel, melarikan diri aksara khas, dan pengoptimuman format. Kod yang dijana boleh digunakan secara langsung dalam sistem CMS, platform blog, dan sistem dokumen yang menyokong Textile, memastikan rendering dan paparan kandungan yang betul."
    from_alias: "Jadual Textile"
    to_alias: "Jadual Textile"
  PNG:
    alias: "Imej PNG"
    what: "PNG (Portable Network Graphics) adalah format imej tanpa kehilangan dengan mampatan yang sangat baik dan sokongan ketelusan. Digunakan secara meluas dalam reka bentuk web, grafik digital, dan fotografi profesional. Kualiti tinggi dan keserasian yang luas menjadikannya ideal untuk tangkapan skrin, logo, rajah, dan sebarang imej yang memerlukan butiran tajam dan latar belakang telus."
    step1: "Import data jadual dalam sebarang format. Alat ini melakukan reka bentuk susun atur pintar dan pengoptimuman visual, mengira secara automatik saiz dan resolusi yang optimum untuk output PNG."
    step3: "Jana imej jadual PNG berkualiti tinggi dengan sokongan untuk pelbagai skema warna tema, latar belakang telus, susun atur adaptif, dan pengoptimuman kejelasan teks. Sempurna untuk penggunaan web, penyisipan dokumen, dan pembentangan profesional dengan kualiti visual yang sangat baik."
    from_alias: ""
    to_alias: "Imej PNG"
  TOML:
    alias: "TOML"
    what: "TOML (Tom's Obvious, Minimal Language) adalah format fail konfigurasi yang mudah dibaca dan ditulis. Direka bentuk untuk menjadi tidak berkekaburan dan mudah, ia digunakan secara meluas dalam projek perisian moden untuk pengurusan konfigurasi. Sintaks yang jelas dan taip yang kuat menjadikannya pilihan yang sangat baik untuk tetapan aplikasi dan fail konfigurasi projek."
    step1: "Muat naik fail TOML atau tampal data konfigurasi. Alat ini menghuraikan sintaks TOML dan mengekstrak maklumat konfigurasi berstruktur."
    step3: "Jana format TOML standard dengan sokongan untuk struktur bersarang, jenis data, dan komen. Fail TOML yang dijana adalah sempurna untuk konfigurasi aplikasi, alat pembinaan, dan tetapan projek."
    from_alias: "TOML"
    to_alias: "Format TOML"
  INI:
    alias: "INI"
    what: "Fail INI adalah fail konfigurasi mudah yang digunakan oleh banyak aplikasi dan sistem operasi. Struktur pasangan kunci-nilai yang mudah menjadikannya mudah dibaca dan diedit secara manual. Digunakan secara meluas dalam aplikasi Windows, sistem warisan, dan senario konfigurasi mudah di mana kebolehbacaan manusia adalah penting."
    step1: "Muat naik fail INI atau tampal data konfigurasi. Alat ini menghuraikan sintaks INI dan mengekstrak maklumat konfigurasi berasaskan seksyen."
    step3: "Jana format INI standard dengan sokongan untuk seksyen, komen, dan pelbagai jenis data. Fail INI yang dijana serasi dengan kebanyakan aplikasi dan sistem konfigurasi."
    from_alias: "INI"
    to_alias: "Format INI"
  Avro:
    alias: "Skema Avro"
    what: "Apache Avro adalah sistem penserialan data yang menyediakan struktur data yang kaya, format binari padat, dan keupayaan evolusi skema. Digunakan secara meluas dalam pemprosesan data besar, baris gilir mesej, dan sistem teragih. Definisi skemanya menyokong jenis data yang kompleks dan keserasian versi, menjadikannya alat penting untuk jurutera data dan arkitek sistem."
    step1: "Muat naik fail skema Avro atau tampal data. Alat ini menghuraikan definisi skema Avro dan mengekstrak maklumat struktur jadual."
    step3: "Jana definisi skema Avro standard dengan sokongan untuk pemetaan jenis data, kekangan medan, dan pengesahan skema. Skema yang dijana boleh digunakan secara langsung dalam ekosistem Hadoop, sistem mesej Kafka, dan platform data besar lain."
    from_alias: "Skema Avro"
    to_alias: "Skema Avro"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) adalah mekanisme Google yang neutral bahasa, neutral platform, boleh diperluas untuk menserialisasi data berstruktur. Digunakan secara meluas dalam mikroperkhidmatan, pembangunan API, dan penyimpanan data. Format binari yang cekap dan taip yang kuat menjadikannya ideal untuk aplikasi prestasi tinggi dan komunikasi merentas bahasa."
    step1: "Muat naik fail .proto atau tampal definisi Protocol Buffer. Alat ini menghuraikan sintaks protobuf dan mengekstrak maklumat struktur mesej."
    step3: "Jana definisi Protocol Buffer standard dengan sokongan untuk jenis mesej, pilihan medan, dan definisi perkhidmatan. Fail .proto yang dijana boleh dikompil untuk pelbagai bahasa pengaturcaraan."
    from_alias: "Protocol Buffer"
    to_alias: "Skema Protobuf"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas adalah perpustakaan analisis data yang paling popular dalam Python, dengan DataFrame sebagai struktur data terasnya. Ia menyediakan keupayaan manipulasi data, pembersihan, dan analisis yang berkuasa, digunakan secara meluas dalam sains data, pembelajaran mesin, dan kecerdasan perniagaan. Alat yang tidak dapat dipisahkan untuk pembangun Python dan penganalisis data."
    step1: "Muat naik fail Python yang mengandungi kod DataFrame atau tampal data. Alat ini menghuraikan sintaks Pandas dan mengekstrak maklumat struktur DataFrame."
    step3: "Jana kod Pandas DataFrame standard dengan sokongan untuk spesifikasi jenis data, tetapan indeks, dan operasi data. Kod yang dijana boleh dilaksanakan secara langsung dalam persekitaran Python untuk analisis dan pemprosesan data."
    from_alias: "Pandas DataFrame"
    to_alias: "Pandas DataFrame"
  RDF:
    alias: "Triple RDF"
    what: "RDF (Resource Description Framework) adalah model standard untuk pertukaran data di Web, direka untuk mewakili maklumat tentang sumber dalam bentuk graf. Digunakan secara meluas dalam web semantik, graf pengetahuan, dan aplikasi data terpaut. Struktur triple-nya membolehkan perwakilan metadata yang kaya dan hubungan semantik."
    step1: "Muat naik fail RDF atau tampal data triple. Alat ini menghuraikan sintaks RDF dan mengekstrak hubungan semantik dan maklumat sumber."
    step3: "Jana format RDF standard dengan sokongan untuk pelbagai penserialan (RDF/XML, Turtle, N-Triples). RDF yang dijana boleh digunakan dalam aplikasi web semantik, pangkalan pengetahuan, dan sistem data terpaut."
    from_alias: "RDF"
    to_alias: "Triple RDF"
  MATLAB:
    alias: "Array MATLAB"
    what: "MATLAB adalah perisian pengkomputeran berangka dan visualisasi berprestasi tinggi yang digunakan secara meluas dalam pengkomputeran kejuruteraan, analisis data, dan pembangunan algoritma. Operasi array dan matriksnya adalah berkuasa, menyokong pengiraan matematik yang kompleks dan pemprosesan data. Alat penting untuk jurutera, penyelidik, dan saintis data."
    step1: "Muat naik fail MATLAB .m atau tampal data array. Alat ini menghuraikan sintaks MATLAB dan mengekstrak maklumat struktur array."
    step3: "Jana kod array MATLAB standard dengan sokongan untuk array berbilang dimensi, spesifikasi jenis data, dan penamaan pembolehubah. Kod yang dijana boleh dilaksanakan secara langsung dalam persekitaran MATLAB untuk analisis data dan pengkomputeran saintifik."
    from_alias: "Array MATLAB"
    to_alias: "Array MATLAB"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame adalah struktur data teras dalam bahasa pengaturcaraan R, digunakan secara meluas dalam analisis statistik, perlombongan data, dan pembelajaran mesin. R adalah alat utama untuk pengkomputeran statistik dan grafik, dengan DataFrame menyediakan keupayaan manipulasi data, analisis statistik, dan visualisasi yang berkuasa. Penting untuk saintis data, ahli statistik, dan penyelidik yang bekerja dengan analisis data berstruktur."
    step1: "Muat naik fail data R atau tampal kod DataFrame. Alat ini menghuraikan sintaks R dan mengekstrak maklumat struktur DataFrame termasuk jenis lajur, nama baris, dan kandungan data."
    step3: "Jana kod R DataFrame standard dengan sokongan untuk spesifikasi jenis data, tahap faktor, nama baris/lajur, dan struktur data khusus R. Kod yang dijana boleh dilaksanakan secara langsung dalam persekitaran R untuk analisis statistik dan pemprosesan data."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Mula Menukar"
  start_generating: "Mula Menjana"
  api_docs: "Dokumentasi API"
related:
  section_title: 'Lebih Banyak Penukar {{ if and .from (ne .from "generator") }}{{ .from }} dan {{ end }}{{ .to }}'
  section_description: 'Terokai lebih banyak penukar untuk format {{ if and .from (ne .from "generator") }}{{ .from }} dan {{ end }}{{ .to }}. Ubah data anda antara pelbagai format dengan alat penukaran dalam talian profesional kami.'
  title: "{{ .from }} kepada {{ .to }}"
howto:
  step2: "Edit data menggunakan editor jadual dalam talian canggih kami dengan ciri profesional. Menyokong pemadaman baris kosong, membuang pendua, transposisi data, penyusunan, cari & ganti regex, dan pratonton masa nyata. Semua perubahan secara automatik ditukar kepada format %s dengan hasil yang tepat dan boleh dipercayai."
  section_title: "Cara menggunakan {{ . }}"
  converter_description: "Belajar menukar {{ .from }} kepada {{ .to }} dengan panduan langkah demi langkah kami. Penukar dalam talian profesional dengan ciri canggih dan pratonton masa nyata."
  generator_description: "Belajar mencipta jadual {{ .to }} profesional dengan penjana dalam talian kami. Pengeditan seperti Excel, pratonton masa nyata, dan keupayaan eksport segera."
extension:
  section_title: "Sambungan Pengesanan & Pengekstrakan Jadual"
  section_description: "Ekstrak jadual dari mana-mana laman web dengan satu klik. Tukar kepada 30+ format termasuk Excel, CSV, JSON dengan serta-merta - tiada salin-tampal diperlukan."
  features:
    extraction_title: "Pengekstrakan Jadual Satu Klik"
    extraction_description: "Ekstrak jadual dengan serta-merta dari mana-mana halaman web tanpa salin-tampal - pengekstrakan data profesional dibuat mudah"
    formats_title: "Sokongan Penukar 30+ Format"
    formats_description: "Tukar jadual yang diekstrak kepada Excel, CSV, JSON, Markdown, SQL, dan banyak lagi dengan penukar jadual canggih kami"
    detection_title: "Pengesanan Jadual Pintar"
    detection_description: "Secara automatik mengesan dan menyerlahkan jadual di mana-mana halaman web untuk pengekstrakan dan penukaran data yang pantas"
  hover_tip: "✨ Hover di atas mana-mana jadual untuk melihat ikon pengekstrakan"
recommendations:
  section_title: "Disyorkan oleh Universiti & Profesional"
  section_description: "TableConvert dipercayai oleh profesional di seluruh universiti, institusi penyelidikan, dan pasukan pembangunan untuk penukaran jadual yang boleh dipercayai dan pemprosesan data."
  cards:
    university_title: "University of Wisconsin-Madison"
    university_description: "TableConvert.com - Alat penukar jadual dalam talian percuma profesional dan format data"
    university_link: "Baca Artikel"
    facebook_title: "Komuniti Profesional Data"
    facebook_description: "Dikongsi dan disyorkan oleh penganalisis data dan profesional dalam kumpulan pembangun Facebook"
    facebook_link: "Lihat Pos"
    twitter_title: "Komuniti Pembangun"
    twitter_description: "Disyorkan oleh @xiaoying_eth dan pembangun lain di X (Twitter) untuk penukaran jadual"
    twitter_link: "Lihat Tweet"
faq:
  section_title: "Soalan Lazim"
  section_description: "Soalan biasa tentang penukar jadual dalam talian percuma kami, format data, dan proses penukaran."
  what: "Apakah format %s?"
  howto_convert:
    question: "Bagaimana untuk menggunakan {{ . }} secara percuma?"
    answer: "Muat naik fail {{ .from }} anda, tampal data, atau ekstrak dari halaman web menggunakan penukar jadual dalam talian percuma kami. Alat penukar profesional kami dengan serta-merta mengubah data anda kepada format {{ .to }} dengan pratonton masa nyata dan ciri pengeditan canggih. Muat turun atau salin hasil yang ditukar dengan serta-merta."
  security:
    question: "Adakah data saya selamat apabila menggunakan penukar dalam talian ini?"
    answer: "Sudah tentu! Semua penukaran jadual berlaku secara tempatan dalam pelayar anda - data anda tidak pernah meninggalkan peranti anda. Penukar dalam talian kami memproses segala-galanya di sisi klien, memastikan privasi lengkap dan keselamatan data. Tiada fail disimpan di pelayan kami."
  free:
    question: "Adakah TableConvert benar-benar percuma untuk digunakan?"
    answer: "Ya, TableConvert adalah percuma sepenuhnya! Semua ciri penukar, editor jadual, alat penjana data, dan pilihan eksport tersedia tanpa kos, pendaftaran, atau yuran tersembunyi. Tukar fail tanpa had dalam talian secara percuma."
  filesize:
    question: "Apakah had saiz fail yang ada pada penukar dalam talian?"
    answer: "Penukar jadual dalam talian percuma kami menyokong fail sehingga 10MB. Untuk fail yang lebih besar, pemprosesan kelompok, atau keperluan perusahaan, gunakan sambungan pelayar kami atau perkhidmatan API profesional dengan had yang lebih tinggi."
stats:
  conversions: "Jadual Ditukar"
  tables: "Jadual Dijana"
  formats: "Format Fail Data"
  rating: "Penilaian Pengguna"
