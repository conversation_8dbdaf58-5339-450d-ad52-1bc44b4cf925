site:
  fullname: "Convertitore di Tabelle Online"
  name: "TableConvert"
  subtitle: "Convertitore e Generatore di Tabelle Online Gratuito"
  intro: "TableConvert è uno strumento gratuito online per la conversione di tabelle e la generazione di dati che supporta la conversione tra oltre 30 formati inclusi Excel, CSV, JSON, Markdown, LaTeX, SQL e altri."
  followTwitter: "Seguici su X"
title:
  converter: "%s a %s"
  generator: "Generatore %s"
post:
  tags:
    converter: "Convertitore"
    editor: "Editor"
    generator: "Generatore"
    maker: "Costruttore"
  converter:
    title: "Converti %s a %s Online"
    short: "Uno strumento online gratuito e potente da %s a %s"
    intro: "Convertitore online da %s a %s facile da usare. Trasforma i dati delle tabelle senza sforzo con il nostro strumento di conversione intuitivo. Veloce, affidabile e user-friendly."
  generator:
    title: "Editor e Generatore %s Online"
    short: "Strumento professionale di generazione online %s con funzionalità complete"
    intro: "Generatore %s online e editor di tabelle facile da usare. Crea tabelle di dati professionali senza sforzo con il nostro strumento intuitivo e anteprima in tempo reale."
navbar:
  search:
    placeholder: "Cerca convertitore..."
  sponsor: "Offrici un Caffè"
  extension: "Estensione"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Fonte Dati"
    placeholder: "Incolla i tuoi dati %s o trascina i file %s qui"
    example: "Esempio"
    upload: "Carica File"
    extract:
      enter: "Estrai da Pagina Web"
      intro: "Inserisci un URL di pagina web contenente dati di tabella per estrarre automaticamente dati strutturati"
      btn: "Estrai %s"
    excel:
      sheet: "Foglio di Lavoro"
      none: "Nessuno"
  tableEditor:
    title: "Editor di Tabelle Online"
    undo: "Annulla"
    redo: "Ripeti"
    transpose: "Trasponi"
    clear: "Cancella"
    deleteBlank: "Elimina Vuoti"
    deleteDuplicate: "Elimina Duplicati"
    uppercase: "MAIUSCOLE"
    lowercase: "minuscole"
    capitalize: "Capitalizza"
    replace:
      replace: "Trova e Sostituisci (Regex supportato)"
      subst: "Sostituisci con..."
      btn: "Sostituisci Tutto"
  tableGenerator:
    title: "Generatore di Tabelle"
    sponsor: "Offrici un Caffè"
    copy: "Copia negli Appunti"
    download: "Scarica File"
    tooltip:
      html:
        escape: "Escape caratteri speciali HTML (&, <, >, \", ') per prevenire errori di visualizzazione"
        div: "Usa layout DIV+CSS invece dei tag TABLE tradizionali, più adatto per design responsive"
        minify: "Rimuovi spazi bianchi e interruzioni di riga per generare codice HTML compresso"
        thead: "Genera struttura standard di intestazione tabella (&lt;thead&gt;) e corpo (&lt;tbody&gt;)"
        tableCaption: "Aggiungi titolo descrittivo sopra la tabella (elemento &lt;caption&gt;)"
        tableClass: "Aggiungi nome classe CSS alla tabella per personalizzazione stile facile"
        tableId: "Imposta identificatore ID unico per la tabella per manipolazione JavaScript"
      jira:
        escape: "Escape caratteri pipe (|) per evitare conflitti con sintassi tabella Jira"
      json:
        parsingJSON: "Analizza intelligentemente stringhe JSON nelle celle in oggetti"
        minify: "Genera formato JSON compatto su singola riga per ridurre dimensione file"
        format: "Seleziona struttura dati JSON di output: array oggetti, array 2D, ecc."
      latex:
        escape: "Escape caratteri speciali LaTeX (%, &, _, #, $, ecc.) per assicurare compilazione corretta"
        ht: "Aggiungi parametro posizione flottante [!ht] per controllare posizione tabella sulla pagina"
        mwe: "Genera documento LaTeX completo"
        tableAlign: "Imposta allineamento orizzontale della tabella sulla pagina"
        tableBorder: "Configura stile bordo tabella: nessun bordo, bordo parziale, bordo completo"
        label: "Imposta etichetta tabella per riferimenti incrociati comando \\ref{}"
        caption: "Imposta didascalia tabella da visualizzare sopra o sotto la tabella"
        location: "Scegli posizione visualizzazione didascalia tabella: sopra o sotto"
        tableType: "Scegli tipo ambiente tabella: tabular, longtable, array, ecc."
      markdown:
        escape: "Escape caratteri speciali Markdown (*, _, |, \\, ecc.) per evitare conflitti formato"
        pretty: "Auto-allinea larghezze colonne per generare formato tabella più bello"
        simple: "Usa sintassi semplificata, omettendo linee verticali bordo esterno"
        boldFirstRow: "Rendi grassetto il testo della prima riga"
        boldFirstColumn: "Rendi grassetto il testo della prima colonna"
        firstHeader: "Tratta prima riga come intestazione e aggiungi linea separatrice"
        textAlign: "Imposta allineamento testo colonna: sinistra, centro, destra"
        multilineHandling: "Gestione testo multiriga: preserva interruzioni riga, escape a \\n, usa tag &lt;br&gt;"

        includeLineNumbers: "Aggiungi colonna numeri riga sul lato sinistro della tabella"
      magic:
        builtin: "Seleziona formati template comuni predefiniti"
        rowsTpl: "<table> <tr> <th>Sintassi Magica</th> <th>Descrizione</th> <th>Metodi JS Supportati</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>1°, 2° ... campo dell'<b>i</b>ntestazione, Alias {hA} {hB} ...</td> <td>Metodi stringa</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>1°, 2° ... campo della riga corrente, Alias {$A} {$B} ...</td> <td>Metodi stringa</td> </tr> <tr> <td>{F,} {F;}</td> <td>Dividi la riga corrente per la stringa dopo <b>F</b></td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td><b>N</b>umero di riga della riga corrente da 1 o 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>N</b>umero di riga <b>f</b>inale delle righe </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>E<b>s</b>egui codice JavaScript, es: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Usa backslash <b>\\</b> per output parentesi graffe {...} </td> <td></td> </tr></table>"
        headerTpl: "Template output personalizzato per sezione intestazione"
        footerTpl: "Template output personalizzato per sezione piè di pagina"
      textile:
        escape: "Escape caratteri sintassi Textile (|, ., -, ^) per evitare conflitti formato"
        rowHeader: "Imposta prima riga come riga intestazione"
        thead: "Aggiungi marcatori sintassi Textile per testa e corpo tabella"
      xml:
        escape: "Escape caratteri speciali XML (&lt;, &gt;, &amp;, \", ') per assicurare XML valido"
        minify: "Genera output XML compresso, rimuovendo spazi bianchi extra"
        rootElement: "Imposta nome tag elemento radice XML"
        rowElement: "Imposta nome tag elemento XML per ogni riga di dati"
        declaration: "Aggiungi intestazione dichiarazione XML (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Output dati come attributi XML invece di elementi figlio"
        cdata: "Avvolgi contenuto testo con CDATA per proteggere caratteri speciali"
        encoding: "Imposta formato codifica caratteri per documento XML"
        indentation: "Scegli carattere indentazione XML: spazi o tab"
      yaml:
        indentSize: "Imposta numero di spazi per indentazione gerarchia YAML (solitamente 2 o 4)"
        arrayStyle: "Formato array: blocco (un elemento per riga) o flusso (formato inline)"
        quotationStyle: "Stile virgolette stringa: nessuna virgoletta, virgolette singole, virgolette doppie"
      pdf:
        theme: "Scegli stile visivo tabella PDF per documenti professionali"
        headerColor: "Scegli colore sfondo intestazione tabella PDF"
        showHead: "Controlla visualizzazione intestazione su pagine PDF"
        docTitle: "Titolo opzionale per il documento PDF"
        docDescription: "Testo descrizione opzionale per documento PDF"
      csv:
        bom: "Aggiungi marca ordine byte UTF-8 per aiutare Excel e altri software a riconoscere codifica"
      excel:
        autoWidth: "Regola automaticamente larghezza colonna basata su contenuto"
        protectSheet: "Abilita protezione foglio di lavoro con password: tableconvert.com"
      sql:
        primaryKey: "Specifica nome campo chiave primaria per istruzione CREATE TABLE"
        dialect: "Seleziona tipo database, influenzando sintassi virgolette e tipo dati"
      ascii:
        forceSep: "Forza linee separatrici tra ogni riga di dati"
        style: "Seleziona stile disegno bordo tabella ASCII"
        comment: "Aggiungi marcatori commento per avvolgere intera tabella"
      mediawiki:
        minify: "Comprimi codice output, rimuovendo spazi bianchi extra"
        header: "Marca prima riga come stile intestazione"
        sort: "Abilita funzionalità ordinamento clic tabella"
      asciidoc:
        minify: "Comprimi output formato AsciiDoc"
        firstHeader: "Imposta prima riga come riga intestazione"
        lastFooter: "Imposta ultima riga come riga piè di pagina"
        title: "Aggiungi testo titolo alla tabella"
      tracwiki:
        rowHeader: "Imposta prima riga come intestazione"
        colHeader: "Imposta prima colonna come intestazione"
      bbcode:
        minify: "Comprimi formato output BBCode"
      restructuredtext:
        style: "Seleziona stile bordo tabella reStructuredText"
        forceSep: "Forza linee separatrici"
    label:
      ascii:
        forceSep: "Separatori Riga"
        style: "Stile Bordo"
        comment: "Wrapper Commento"
      restructuredtext:
        style: "Stile Bordo"
        forceSep: "Forza Separatori"
      bbcode:
        minify: "Comprimi Output"
      csv:
        doubleQuote: "Virgolette Doppie"
        delimiter: "Delimitatore Campo"
        bom: "UTF-8 BOM"
        valueDelimiter: "Delimitatore Valore"
        rowDelimiter: "Delimitatore Riga"
        prefix: "Prefisso Riga"
        suffix: "Suffisso Riga"
      excel:
        autoWidth: "Larghezza Automatica"
        textFormat: "Formato Testo"
        protectSheet: "Proteggi Foglio"
        boldFirstRow: "Prima Riga in Grassetto"
        boldFirstColumn: "Prima Colonna in Grassetto"
        sheetName: "Nome Foglio"
      html:
        escape: "Escape Caratteri HTML"
        div: "Tabella DIV"
        minify: "Comprimi Codice"
        thead: "Struttura Intestazione"
        tableCaption: "Didascalia Tabella"
        tableClass: "Classe Tabella"
        tableId: "ID Tabella"
        rowHeader: "Intestazione Riga"
        colHeader: "Intestazione Colonna"
      jira:
        escape: "Escape Caratteri"
        rowHeader: "Intestazione Riga"
        colHeader: "Intestazione Colonna"
      json:
        parsingJSON: "Analizza JSON"
        minify: "Comprimi Output"
        format: "Formato Dati"
        rootName: "Nome Oggetto Radice"
        indentSize: "Dimensione Indentazione"
      jsonlines:
        parsingJSON: "Analizza JSON"
        format: "Formato Dati"
      latex:
        escape: "Escape Caratteri LaTeX"
        ht: "Posizione Flottante"
        mwe: "Documento Completo"
        tableAlign: "Allineamento Tabella"
        tableBorder: "Stile Bordo"
        label: "Etichetta Riferimento"
        caption: "Didascalia Tabella"
        location: "Posizione Didascalia"
        tableType: "Tipo Tabella"
        boldFirstRow: "Prima Riga in Grassetto"
        boldFirstColumn: "Prima Colonna in Grassetto"
        textAlign: "Allineamento Testo"
        borders: "Impostazioni Bordo"
      markdown:
        escape: "Escape Caratteri"
        pretty: "Tabella Markdown Elegante"
        simple: "Formato Markdown Semplice"
        boldFirstRow: "Prima Riga in Grassetto"
        boldFirstColumn: "Prima Colonna in Grassetto"
        firstHeader: "Prima Intestazione"
        textAlign: "Allineamento Testo"
        multilineHandling: "Gestione Multiriga"

        includeLineNumbers: "Aggiungi Numeri Riga"
        align: "Allineamento"
      mediawiki:
        minify: "Comprimi Codice"
        header: "Markup Intestazione"
        sort: "Ordinabile"
      asciidoc:
        minify: "Comprimi Formato"
        firstHeader: "Prima Intestazione"
        lastFooter: "Ultimo Piè di Pagina"
        title: "Titolo Tabella"
      tracwiki:
        rowHeader: "Intestazione Riga"
        colHeader: "Intestazione Colonna"
      sql:
        drop: "Elimina Tabella (Se Esiste)"
        create: "Crea Tabella"
        oneInsert: "Inserimento Batch"
        table: "Nome Tabella"
        dialect: "Tipo Database"
        primaryKey: "Chiave Primaria"
      magic:
        builtin: "Template Integrato"
        rowsTpl: "Template Riga, Sintassi ->"
        headerTpl: "Template Intestazione"
        footerTpl: "Template Piè di Pagina"
      textile:
        escape: "Escape Caratteri"
        rowHeader: "Intestazione Riga"
        thead: "Sintassi Intestazione"
      xml:
        escape: "Escape Caratteri XML"
        minify: "Comprimi Output"
        rootElement: "Elemento Radice"
        rowElement: "Elemento Riga"
        declaration: "Dichiarazione XML"
        attributes: "Modalità Attributi"
        cdata: "Wrapper CDATA"
        encoding: "Codifica"
        indentSize: "Dimensione Indentazione"
      yaml:
        indentSize: "Dimensione Indentazione"
        arrayStyle: "Stile Array"
        quotationStyle: "Stile Virgolette"
      pdf:
        theme: "Tema Tabella PDF"
        headerColor: "Colore Intestazione PDF"
        showHead: "Visualizzazione Intestazione PDF"
        docTitle: "Titolo Documento PDF"
        docDescription: "Descrizione Documento PDF"
sidebar:
  all: "Tutti gli Strumenti di Conversione"
  dataSource:
    title: "Fonte Dati"
    description:
      converter: "Importa %s per conversione a %s. Supporta caricamento file, modifica online, ed estrazione dati web."
      generator: "Crea dati di tabella con supporto per metodi di input multipli inclusi input manuale, importazione file, e generazione template."
  tableEditor:
    title: "Editor di Tabelle Online"
    description:
      converter: "Elabora %s online usando il nostro editor di tabelle. Esperienza operativa simile a Excel con supporto per eliminazione righe vuote, deduplicazione, ordinamento, e trova e sostituisci."
      generator: "Potente editor di tabelle online che fornisce esperienza operativa simile a Excel. Supporta eliminazione righe vuote, deduplicazione, ordinamento, e trova e sostituisci."
  tableGenerator:
    title: "Generatore di Tabelle"
    description:
      converter: "Genera rapidamente %s con anteprima in tempo reale del generatore di tabelle. Opzioni di esportazione ricche, copia e download con un clic."
      generator: "Esporta dati %s in formati multipli per soddisfare diversi scenari d'uso. Supporta opzioni personalizzate e anteprima in tempo reale."
footer:
  changelog: "Registro Modifiche"
  sponsor: "Sponsor"
  contact: "Contattaci"
  privacyPolicy: "Politica Privacy"
  about: "Chi Siamo"
  resources: "Risorse"
  popularConverters: "Convertitori Popolari"
  popularGenerators: "Generatori Popolari"
  dataSecurity: "I tuoi dati sono sicuri - tutte le conversioni vengono eseguite nel tuo browser."
converters:
  Markdown:
    alias: "Tabella Markdown"
    what: "Markdown è un linguaggio di markup leggero ampiamente utilizzato per documentazione tecnica, creazione di contenuti blog, e sviluppo web. La sua sintassi di tabella è concisa e intuitiva, supportando allineamento testo, incorporamento link, e formattazione. È lo strumento preferito per programmatori e scrittori tecnici, perfettamente compatibile con GitHub, GitLab, e altre piattaforme di hosting codice."
    step1: "Incolla dati di tabella Markdown nell'area fonte dati, o trascina e rilascia direttamente file .md per caricamento. Lo strumento analizza automaticamente struttura e formattazione tabella, supportando contenuto annidato complesso e gestione caratteri speciali."
    step3: "Genera codice tabella Markdown standard in tempo reale, supportando metodi di allineamento multipli, grassetto testo, aggiunta numeri riga, e altre impostazioni formato avanzate. Il codice generato è completamente compatibile con GitHub e principali editor Markdown, pronto all'uso con copia un clic."
    from_alias: "File Tabella Markdown"
    to_alias: "Formato Tabella Markdown"
  Magic:
    alias: "Template Personalizzato"
    what: "Il template Magic è un generatore dati avanzato unico di questo strumento, permettendo agli utenti di creare output dati formato arbitrario attraverso sintassi template personalizzata. Supporta sostituzione variabili, giudizio condizionale, e elaborazione loop. È la soluzione definitiva per gestire esigenze conversione dati complesse e formati output personalizzati, particolarmente adatto per sviluppatori e ingegneri dati."
    step1: "Seleziona template comuni integrati o crea sintassi template personalizzata. Supporta variabili e funzioni ricche che possono gestire strutture dati complesse e logica business."
    step3: "Genera output dati che soddisfa completamente requisiti formato personalizzato. Supporta logica conversione dati complessa e elaborazione condizionale, migliorando notevolmente efficienza elaborazione dati e qualità output. Uno strumento potente per elaborazione dati batch."
    from_alias: "Dati Tabella"
    to_alias: "Output Formato Personalizzato"
  CSV:
    alias: "CSV"
    what: "CSV (Valori Separati da Virgola) è il formato scambio dati più ampiamente utilizzato, perfettamente supportato da Excel, Google Sheets, sistemi database, e vari strumenti analisi dati. La sua struttura semplice e forte compatibilità lo rendono il formato standard per migrazione dati, importazione/esportazione batch, e scambio dati cross-platform, ampiamente utilizzato in analisi business, scienza dati, e integrazione sistemi."
    step1: "Carica file CSV o incolla direttamente dati CSV. Lo strumento riconosce intelligentemente vari delimitatori (virgola, tab, punto e virgola, pipe, ecc.), rileva automaticamente tipi dati e formati codifica, supportando analisi veloce file grandi e strutture dati complesse."
    step3: "Genera file formato CSV standard con supporto per delimitatori personalizzati, stili virgolette, formati codifica, e impostazioni marca BOM. Assicura compatibilità perfetta con sistemi target, fornendo opzioni download e compressione per soddisfare esigenze elaborazione dati livello enterprise."
    from_alias: "File Dati CSV"
    to_alias: "Formato CSV Standard"
  JSON:
    alias: "Array JSON"
    what: "JSON (JavaScript Object Notation) è il formato dati tabella standard per applicazioni web moderne, API REST e architetture microservizi. La sua struttura chiara e parsing efficiente lo rendono ampiamente utilizzato nell'interazione dati front-end e back-end, archiviazione file configurazione e database NoSQL. Supporta oggetti annidati, strutture array e tipi dati multipli, rendendolo dati tabella indispensabili per sviluppo software moderno."
    step1: "Carica file JSON o incolla array JSON. Supporta riconoscimento automatico e parsing di array oggetti, strutture annidate e tipi dati complessi. Lo strumento valida intelligentemente sintassi JSON e fornisce prompt errore."
    step3: "Genera output formato JSON multipli: array oggetti standard, array 2D, array colonne e formati coppie chiave-valore. Supporta output abbellito, modalità compressione, nomi oggetto radice personalizzati e impostazioni indentazione, adattandosi perfettamente a varie interfacce API e esigenze archiviazione dati."
    from_alias: "File Array JSON"
    to_alias: "Formato JSON Standard"
  JSONLines:
    alias: "Formato JSONLines"
    what: "JSON Lines (noto anche come NDJSON) è un formato importante per elaborazione big data e trasmissione dati streaming, con ogni riga contenente un oggetto JSON indipendente. Ampiamente utilizzato in analisi log, elaborazione flussi dati, machine learning e sistemi distribuiti. Supporta elaborazione incrementale e calcolo parallelo, rendendolo la scelta ideale per gestire dati strutturati su larga scala."
    step1: "Carica file JSONLines o incolla dati. Lo strumento analizza oggetti JSON riga per riga, supportando elaborazione streaming file grandi e funzionalità salto righe errore."
    step3: "Genera formato JSONLines standard con ogni riga che produce un oggetto JSON completo. Adatto per elaborazione streaming, importazione batch e scenari analisi big data, supportando validazione dati e ottimizzazione formato."
    from_alias: "Dati JSONLines"
    to_alias: "Formato Streaming JSONLines"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) è il formato standard per scambio dati livello enterprise e gestione configurazione, con specifiche sintassi rigorose e meccanismi validazione potenti. Ampiamente utilizzato in servizi web, file configurazione, archiviazione documenti e integrazione sistemi. Supporta namespace, validazione schema e trasformazione XSLT, rendendolo dati tabella importanti per applicazioni enterprise."
    step1: "Carica file XML o incolla dati XML. Lo strumento analizza automaticamente struttura XML e la converte in formato tabella, supportando namespace, gestione attributi e strutture annidate complesse."
    step3: "Genera output XML conforme agli standard XML. Supporta elementi radice personalizzati, nomi elementi riga, modalità attributi, wrapping CDATA e impostazioni codifica caratteri. Assicura integrità dati e compatibilità, soddisfacendo requisiti applicazioni livello enterprise."
    from_alias: "File Dati XML"
    to_alias: "Formato XML Standard"
  YAML:
    alias: "Configurazione YAML"
    what: "YAML è uno standard serializzazione dati amichevole per umani, rinomato per la sua struttura gerarchica chiara e sintassi concisa. Ampiamente utilizzato in file configurazione, catene strumenti DevOps, Docker Compose e deployment Kubernetes. La sua forte leggibilità e sintassi concisa lo rendono un formato configurazione importante per applicazioni cloud-native moderne e operazioni automatizzate."
    step1: "Carica file YAML o incolla dati YAML. Lo strumento analizza intelligentemente struttura YAML e valida correttezza sintassi, supportando formati multi-documento e tipi dati complessi."
    step3: "Genera output formato YAML standard con supporto per stili array blocco e flusso, impostazioni virgolette multiple, indentazione personalizzata e preservazione commenti. Assicura che file YAML output siano completamente compatibili con vari parser e sistemi configurazione."
    from_alias: "File Configurazione YAML"
    to_alias: "Formato YAML Standard"
  MySQL:
      alias: "Risultati Query MySQL"
      what: "MySQL è il sistema di gestione database relazionale open-source più popolare al mondo, rinomato per le sue alte prestazioni, affidabilità e facilità d'uso. Ampiamente utilizzato in applicazioni web, sistemi enterprise e piattaforme analisi dati. I risultati query MySQL tipicamente contengono dati tabella strutturati, servendo come importante fonte dati nella gestione database e lavoro analisi dati."
      step1: "Incolla risultati output query MySQL nell'area fonte dati. Lo strumento riconosce automaticamente e analizza formato output riga comando MySQL, supportando vari stili risultati query e codifiche caratteri, gestendo intelligentemente intestazioni e righe dati."
      step3: "Converti rapidamente risultati query MySQL in formati dati tabella multipli, facilitando analisi dati, generazione report, migrazione dati cross-sistema e validazione dati. Uno strumento pratico per amministratori database e analisti dati."
      from_alias: "Output Query MySQL"
      to_alias: "Dati Tabella MySQL"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) è il linguaggio operazione standard per database relazionali, utilizzato per operazioni query dati, inserimento, aggiornamento ed eliminazione. Come tecnologia centrale della gestione database, SQL è ampiamente utilizzato in analisi dati, business intelligence, elaborazione ETL e costruzione data warehouse. È uno strumento competenza essenziale per professionisti dati."
    step1: "Incolla istruzioni INSERT SQL o carica file .sql. Lo strumento analizza intelligentemente sintassi SQL ed estrae dati tabella, supportando dialetti SQL multipli e elaborazione istruzioni query complesse."
    step3: "Genera istruzioni INSERT SQL standard e istruzioni creazione tabella. Supporta dialetti database multipli (MySQL, PostgreSQL, SQLite, SQL Server, Oracle), gestisce automaticamente mappatura tipi dati, escape caratteri e vincoli chiave primaria. Assicura che codice SQL generato possa essere eseguito direttamente."
    from_alias: "File Dati SQL"
    to_alias: "Istruzione SQL Standard"
  Qlik:
      alias: "Tabella Qlik"
      what: "Qlik è un fornitore software specializzato in visualizzazione dati, dashboard esecutivi e prodotti business intelligence self-service, insieme a Tableau e Microsoft."
      step1: ""
      step3: "Infine, il [Generatore Tabelle](#TableGenerator) mostra i risultati conversione. Usa nel tuo Qlik Sense, Qlik AutoML, QlikView, o altro software abilitato Qlik."
      from_alias: "Tabella Qlik"
      to_alias: "Tabella Qlik"
  DAX:
      alias: "Tabella DAX"
      what: "DAX (Data Analysis Expressions) è un linguaggio programmazione utilizzato in Microsoft Power BI per creare colonne calcolate, misure e tabelle personalizzate."
      step1: ""
      step3: "Infine, il [Generatore Tabelle](#TableGenerator) mostra i risultati conversione. Come previsto, è utilizzato in diversi prodotti Microsoft inclusi Microsoft Power BI, Microsoft Analysis Services e Microsoft Power Pivot per Excel."
      from_alias: "Tabella DAX"
      to_alias: "Tabella DAX"
  Firebase:
    alias: "Lista Firebase"
    what: "Firebase è una piattaforma sviluppo applicazioni BaaS che fornisce servizi backend ospitati come database tempo reale, archiviazione cloud, autenticazione, segnalazione crash, ecc."
    step1: ""
    step3: "Infine, il [Generatore Tabelle](#TableGenerator) mostra i risultati conversione. Puoi quindi utilizzare il metodo push nell'API Firebase per aggiungere a una lista di dati nel database Firebase."
    from_alias: "Lista Firebase"
    to_alias: "Lista Firebase"
  HTML:
    alias: "Tabella HTML"
    what: "Le tabelle HTML sono il modo standard per visualizzare dati strutturati nelle pagine web, costruite con tag table, tr, td e altri. Supporta personalizzazione stile ricca, layout responsivo e funzionalità interattiva. Ampiamente utilizzate nello sviluppo siti web, visualizzazione dati e generazione report, servendo come componente importante dello sviluppo front-end e web design."
    step1: "Incolla codice HTML contenente tabelle o carica file HTML. Lo strumento riconosce automaticamente ed estrae dati tabella dalle pagine, supportando strutture HTML complesse, stili CSS ed elaborazione tabelle annidate."
    step3: "Genera codice tabella HTML semantico con supporto per struttura thead/tbody, impostazioni classe CSS, didascalie tabella, intestazioni riga/colonna e configurazione attributi responsivi. Assicura che il codice tabella generato soddisfi standard web con buona accessibilità e amichevolezza SEO."
    from_alias: "Tabella Web HTML"
    to_alias: "Tabella HTML Standard"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel è il software foglio di calcolo più popolare al mondo, ampiamente utilizzato in analisi business, gestione finanziaria, elaborazione dati e creazione report. Le sue potenti capacità elaborazione dati, ricca libreria funzioni e caratteristiche visualizzazione flessibili lo rendono lo strumento standard per automazione ufficio e analisi dati, con applicazioni estese in quasi tutte le industrie e campi."
    step1: "Carica file Excel (supporta formati .xlsx, .xls) o copia dati tabella direttamente da Excel e incolla. Lo strumento supporta elaborazione multi-foglio, riconoscimento formato complesso e parsing veloce file grandi, gestendo automaticamente celle unite e tipi dati."
    step3: "Genera dati tabella compatibili Excel che possono essere incollati direttamente in Excel o scaricati come file .xlsx standard. Supporta denominazione foglio lavoro, formattazione celle, larghezza colonna automatica, stile intestazioni e impostazioni validazione dati. Assicura che file Excel output abbiano aspetto professionale e funzionalità completa."
    from_alias: "Foglio di Calcolo Excel"
    to_alias: "Formato Excel Standard"
  LaTeX:
    alias: "Tabella LaTeX"
    what: "LaTeX è un sistema composizione documenti professionale, particolarmente adatto per creare articoli accademici, documenti tecnici e pubblicazioni scientifiche. La sua funzionalità tabella è potente, supportando formule matematiche complesse, controllo layout preciso e output PDF alta qualità. È lo strumento standard in accademia e pubblicazione scientifica, ampiamente utilizzato in articoli riviste, dissertazioni e composizione manuali tecnici."
    step1: "Incolla codice tabella LaTeX o carica file .tex. Lo strumento analizza sintassi tabella LaTeX ed estrae contenuto dati, supportando ambienti tabella multipli (tabular, longtable, array, ecc.) e comandi formato complessi."
    step3: "Genera codice tabella LaTeX professionale con supporto per selezione ambiente tabella multipla, configurazione stile bordi, impostazioni posizione didascalia, specifica classe documento e gestione pacchetti. Può generare documenti LaTeX compilabili completi, assicurando che tabelle output soddisfino standard pubblicazione accademica."
    from_alias: "Tabella Documento LaTeX"
    to_alias: "Formato LaTeX Professionale"
  ASCII:
    alias: "Tabella ASCII"
    what: "Le tabelle ASCII utilizzano caratteri di testo semplice per disegnare bordi e strutture delle tabelle, fornendo la migliore compatibilità e portabilità. Compatibili con tutti gli editor di testo, ambienti terminali e sistemi operativi. Ampiamente utilizzate nella documentazione del codice, manuali tecnici, file README e output di strumenti da riga di comando. Il formato di visualizzazione dati preferito per programmatori e amministratori di sistema."
    step1: "Carica file di testo contenenti tabelle ASCII o incolla direttamente i dati della tabella. Lo strumento riconosce e analizza intelligentemente le strutture delle tabelle ASCII, supportando stili di bordo multipli e formati di allineamento."
    step3: "Genera belle tabelle ASCII in testo semplice con supporto per stili di bordo multipli (linea singola, linea doppia, angoli arrotondati, ecc.), metodi di allineamento testo e larghezza colonna automatica. Le tabelle generate si visualizzano perfettamente negli editor di codice, documenti e righe di comando."
    from_alias: "Tabella Testo ASCII"
    to_alias: "Formato ASCII Standard"
  MediaWiki:
    alias: "Tabella MediaWiki"
    what: "MediaWiki è la piattaforma software open-source utilizzata da famosi siti wiki come Wikipedia. La sua sintassi tabella è concisa ma potente, supportando personalizzazione stile tabella, funzionalità ordinamento e incorporamento link. Ampiamente utilizzata nella gestione conoscenza, editing collaborativo e sistemi gestione contenuti, servendo come tecnologia centrale per costruire enciclopedie wiki e basi di conoscenza."
    step1: "Incolla codice tabella MediaWiki o carica file sorgente wiki. Lo strumento analizza sintassi markup wiki ed estrae dati tabella, supportando sintassi wiki complessa e elaborazione template."
    step3: "Genera codice tabella MediaWiki standard con supporto per impostazioni stile intestazione, allineamento celle, abilitazione funzionalità ordinamento e opzioni compressione codice. Il codice generato può essere utilizzato direttamente per editing pagine wiki, assicurando visualizzazione perfetta su piattaforme MediaWiki."
    from_alias: "Codice Sorgente MediaWiki"
    to_alias: "Sintassi Tabella MediaWiki"
  TracWiki:
    alias: "Tabella TracWiki"
    what: "Trac è un sistema gestione progetti e tracciamento bug basato su web che utilizza sintassi wiki semplificata per creare contenuto tabelle."
    step1: "Carica file TracWiki o incolla dati tabella."
    step3: "Genera codice tabella compatibile TracWiki con supporto per impostazioni intestazione riga/colonna, facilitando gestione documenti progetto."
    from_alias: "Tabella TracWiki"
    to_alias: "Formato TracWiki"
  AsciiDoc:
    alias: "Tabella AsciiDoc"
    what: "AsciiDoc è un linguaggio markup leggero che può essere convertito in HTML, PDF, pagine manuali e altri formati, ampiamente utilizzato per scrittura documentazione tecnica."
    step1: "Carica file AsciiDoc o incolla dati."
    step3: "Genera sintassi tabella AsciiDoc con supporto per impostazioni intestazione, piè di pagina e titolo, direttamente utilizzabile negli editor AsciiDoc."
    from_alias: "Tabella AsciiDoc"
    to_alias: "Formato AsciiDoc"
  reStructuredText:
    alias: "Tabella reStructuredText"
    what: "reStructuredText è il formato documentazione standard per la comunità Python, supportando sintassi tabella ricca, comunemente utilizzato per generazione documentazione Sphinx."
    step1: "Carica file .rst o incolla dati reStructuredText."
    step3: "Genera tabelle reStructuredText standard con supporto per stili bordo multipli, direttamente utilizzabili in progetti documentazione Sphinx."
    from_alias: "Tabella reStructuredText"
    to_alias: "Formato reStructuredText"
  PHP:
    alias: "Array PHP"
    what: "PHP è un linguaggio scripting lato server popolare, con gli array come struttura dati centrale, ampiamente utilizzato nello sviluppo web e elaborazione dati."
    step1: "Carica file contenenti array PHP o incolla direttamente i dati."
    step3: "Genera codice array PHP standard che può essere utilizzato direttamente in progetti PHP, supportando formati array associativi e indicizzati."
    from_alias: "Array PHP"
    to_alias: "Codice PHP"
  Ruby:
    alias: "Array Ruby"
    what: "Ruby è un linguaggio programmazione orientato agli oggetti dinamico con sintassi concisa ed elegante, con gli array come struttura dati importante."
    step1: "Carica file Ruby o incolla dati array."
    step3: "Genera codice array Ruby che rispetta le specifiche sintassi Ruby, direttamente utilizzabile in progetti Ruby."
    from_alias: "Array Ruby"
    to_alias: "Codice Ruby"
  ASP:
    alias: "Array ASP"
    what: "ASP (Active Server Pages) è l'ambiente scripting lato server di Microsoft, supportando linguaggi programmazione multipli per sviluppare pagine web dinamiche."
    step1: "Carica file ASP o incolla dati array."
    step3: "Genera codice array compatibile ASP con supporto per sintassi VBScript e JScript, utilizzabile in progetti ASP.NET."
    from_alias: "Array ASP"
    to_alias: "Codice ASP"
  ActionScript:
    alias: "Array ActionScript"
    what: "ActionScript è un linguaggio programmazione orientato agli oggetti utilizzato principalmente per sviluppo applicazioni Adobe Flash e AIR."
    step1: "Carica file .as o incolla dati ActionScript."
    step3: "Genera codice array ActionScript che rispetta standard sintassi AS3, utilizzabile per sviluppo progetti Flash e Flex."
    from_alias: "Array ActionScript"
    to_alias: "Codice ActionScript"
  BBCode:
    alias: "Tabella BBCode"
    what: "BBCode è un linguaggio markup leggero comunemente utilizzato in forum e comunità online, fornendo funzionalità formattazione semplice incluso supporto tabelle."
    step1: "Carica file contenenti BBCode o incolla dati."
    step3: "Genera codice tabella BBCode adatto per pubblicazione forum e creazione contenuto comunità, con supporto per formato output compresso."
    from_alias: "Tabella BBCode"
    to_alias: "Formato BBCode"
  PDF:
    alias: "Tabella PDF"
    what: "PDF (Portable Document Format) è uno standard documento multipiattaforma con layout fisso, visualizzazione coerente e caratteristiche stampa alta qualità. Ampiamente utilizzato in documenti formali, report, fatture, contratti e articoli accademici. Il formato preferito per comunicazione aziendale e archiviazione documenti, assicurando effetti visivi completamente coerenti su dispositivi e sistemi operativi diversi."
    step1: "Importa dati tabella in qualsiasi formato. Lo strumento analizza automaticamente struttura dati ed esegue progettazione layout intelligente, supportando impaginazione automatica tabelle grandi ed elaborazione tipi dati complessi."
    step3: "Genera file tabella PDF alta qualità con supporto per stili tema professionali multipli (aziendale, accademico, minimalista, ecc.), font multilingue, impaginazione automatica, aggiunta filigrana e ottimizzazione stampa. Assicura che documenti PDF output abbiano aspetto professionale, direttamente utilizzabili per presentazioni aziendali e pubblicazione formale."
    from_alias: "Dati Tabella"
    to_alias: "Documento PDF Professionale"
  JPEG:
    alias: "Immagine JPEG"
    what: "JPEG è il formato immagine digitale più ampiamente utilizzato con eccellenti effetti compressione e ampia compatibilità. La sua dimensione file piccola e velocità caricamento rapida lo rendono adatto per visualizzazione web, condivisione social media, illustrazioni documenti e presentazioni online. Il formato immagine standard per media digitali e comunicazione rete, perfettamente supportato da quasi tutti dispositivi e software."
    step1: "Importa dati tabella in qualsiasi formato. Lo strumento esegue progettazione layout intelligente e ottimizzazione visiva, calcolando automaticamente dimensione e risoluzione ottimali."
    step3: "Genera immagini tabella JPEG alta definizione con supporto per schemi colore tema multipli (chiaro, scuro, amichevole occhi, ecc.), layout adattivo, ottimizzazione chiarezza testo e personalizzazione dimensione. Adatto per condivisione online, inserimento documenti e uso presentazioni, assicurando eccellenti effetti visivi su vari dispositivi visualizzazione."
    from_alias: "Dati Tabella"
    to_alias: "Immagine JPEG Alta Definizione"
  Jira:
    alias: "Tabella Jira"
    what: "JIRA è software professionale gestione progetti e tracciamento bug sviluppato da Atlassian, ampiamente utilizzato in sviluppo agile, test software e collaborazione progetti. La sua funzionalità tabella supporta opzioni formattazione ricche e visualizzazione dati, servendo come strumento importante per team sviluppo software, project manager e personale assicurazione qualità nella gestione requisiti, tracciamento bug e reporting progresso."
    step1: "Carica file contenenti dati tabella o incolla direttamente contenuto dati. Lo strumento elabora automaticamente dati tabella ed escape caratteri speciali."
    step3: "Genera codice tabella compatibile piattaforma JIRA con supporto per impostazioni stile intestazione, allineamento celle, elaborazione escape caratteri e ottimizzazione formato. Il codice generato può essere incollato direttamente in descrizioni issue JIRA, commenti o pagine wiki, assicurando visualizzazione e rendering corretti nei sistemi JIRA."
    from_alias: "Dati Progetto"
    to_alias: "Sintassi Tabella Jira"
  Textile:
    alias: "Tabella Textile"
    what: "Textile è un linguaggio markup leggero conciso con sintassi semplice e facile da imparare, ampiamente utilizzato in sistemi gestione contenuti, piattaforme blog e sistemi forum. La sua sintassi tabella è chiara e intuitiva, supportando formattazione rapida e impostazioni stile. Uno strumento ideale per creatori contenuti e amministratori siti web per scrittura rapida documenti e pubblicazione contenuti."
    step1: "Carica file formato Textile o incolla dati tabella. Lo strumento analizza sintassi markup Textile ed estrae contenuto tabella."
    step3: "Genera sintassi tabella Textile standard con supporto per markup intestazione, allineamento celle, escape caratteri speciali e ottimizzazione formato. Il codice generato può essere utilizzato direttamente in sistemi CMS, piattaforme blog e sistemi documenti che supportano Textile, assicurando rendering e visualizzazione contenuto corretti."
    from_alias: "Documento Textile"
    to_alias: "Sintassi Tabella Textile"
  PNG:
    alias: "Immagine PNG"
    what: "PNG (Portable Network Graphics) è un formato immagine senza perdita con eccellente compressione e supporto trasparenza. Ampiamente utilizzato in web design, grafica digitale e fotografia professionale. La sua alta qualità e ampia compatibilità lo rendono ideale per screenshot, loghi, diagrammi e qualsiasi immagine che richieda dettagli nitidi e sfondi trasparenti."
    step1: "Importa dati tabella in qualsiasi formato. Lo strumento esegue progettazione layout intelligente e ottimizzazione visiva, calcolando automaticamente dimensione e risoluzione ottimali per output PNG."
    step3: "Genera immagini tabella PNG alta qualità con supporto per schemi colore tema multipli, sfondi trasparenti, layout adattivo e ottimizzazione chiarezza testo. Perfetto per uso web, inserimento documenti e presentazioni professionali con eccellente qualità visiva."
    from_alias: "Dati Tabella"
    to_alias: "Immagine PNG Alta Qualità"
  TOML:
    alias: "Configurazione TOML"
    what: "TOML (Tom's Obvious, Minimal Language) è un formato file configurazione facile da leggere e scrivere. Progettato per essere non ambiguo e semplice, è ampiamente utilizzato in progetti software moderni per gestione configurazione. La sua sintassi chiara e tipizzazione forte lo rendono una scelta eccellente per impostazioni applicazioni e file configurazione progetti."
    step1: "Carica file TOML o incolla dati configurazione. Lo strumento analizza sintassi TOML ed estrae informazioni configurazione strutturate."
    step3: "Genera formato TOML standard con supporto per strutture annidate, tipi dati e commenti. I file TOML generati sono perfetti per configurazione applicazioni, strumenti build e impostazioni progetti."
    from_alias: "Configurazione TOML"
    to_alias: "Formato TOML"
  INI:
    alias: "Configurazione INI"
    what: "I file INI sono file configurazione semplici utilizzati da molte applicazioni e sistemi operativi. La loro struttura diretta coppia chiave-valore li rende facili da leggere e modificare manualmente. Ampiamente utilizzati in applicazioni Windows, sistemi legacy e scenari configurazione semplici dove la leggibilità umana è importante."
    step1: "Carica file INI o incolla dati configurazione. Lo strumento analizza sintassi INI ed estrae informazioni configurazione basate su sezioni."
    step3: "Genera formato INI standard con supporto per sezioni, commenti e vari tipi dati. I file INI generati sono compatibili con la maggior parte delle applicazioni e sistemi configurazione."
    from_alias: "Configurazione INI"
    to_alias: "Formato INI"
  Avro:
    alias: "Schema Avro"
    what: "Apache Avro è un sistema serializzazione dati che fornisce strutture dati ricche, formato binario compatto e capacità evoluzione schema. Ampiamente utilizzato nell'elaborazione big data, code messaggi e sistemi distribuiti. La sua definizione schema supporta tipi dati complessi e compatibilità versioni, rendendolo uno strumento importante per ingegneri dati e architetti sistemi."
    step1: "Carica file schema Avro o incolla dati. Lo strumento analizza definizioni schema Avro ed estrae informazioni struttura tabella."
    step3: "Genera definizioni schema Avro standard con supporto per mappatura tipi dati, vincoli campi e validazione schema. Gli schemi generati possono essere utilizzati direttamente in ecosistemi Hadoop, sistemi messaggi Kafka e altre piattaforme big data."
    from_alias: "Schema Avro"
    to_alias: "Formato Dati Avro"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) è il meccanismo neutrale al linguaggio, neutrale alla piattaforma ed estensibile di Google per serializzare dati strutturati. Ampiamente utilizzato in microservizi, sviluppo API e archiviazione dati. Il suo formato binario efficiente e tipizzazione forte lo rendono ideale per applicazioni alte prestazioni e comunicazione cross-linguaggio."
    step1: "Carica file .proto o incolla definizioni Protocol Buffer. Lo strumento analizza sintassi protobuf ed estrae informazioni struttura messaggio."
    step3: "Genera definizioni Protocol Buffer standard con supporto per tipi messaggio, opzioni campi e definizioni servizi. I file .proto generati possono essere compilati per linguaggi programmazione multipli."
    from_alias: "Protocol Buffer"
    to_alias: "Schema Protobuf"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas è la libreria analisi dati più popolare in Python, con DataFrame come sua struttura dati centrale. Fornisce potenti capacità manipolazione, pulizia e analisi dati, ampiamente utilizzata in data science, machine learning e business intelligence. Uno strumento indispensabile per sviluppatori Python e analisti dati."
    step1: "Carica file Python contenenti codice DataFrame o incolla dati. Lo strumento analizza sintassi Pandas ed estrae informazioni struttura DataFrame."
    step3: "Genera codice Pandas DataFrame standard con supporto per specifiche tipi dati, impostazioni indice e operazioni dati. Il codice generato può essere eseguito direttamente in ambiente Python per analisi ed elaborazione dati."
    from_alias: "Pandas DataFrame"
    to_alias: "Struttura Dati Python"
  RDF:
    alias: "Tripla RDF"
    what: "RDF (Resource Description Framework) è un modello standard per scambio dati sul Web, progettato per rappresentare informazioni su risorse in forma grafico. Ampiamente utilizzato in web semantico, grafi conoscenza e applicazioni dati collegati. La sua struttura tripla abilita rappresentazione metadati ricca e relazioni semantiche."
    step1: "Carica file RDF o incolla dati tripla. Lo strumento analizza sintassi RDF ed estrae relazioni semantiche e informazioni risorse."
    step3: "Genera formato RDF standard con supporto per varie serializzazioni (RDF/XML, Turtle, N-Triples). L'RDF generato può essere utilizzato in applicazioni web semantico, basi conoscenza e sistemi dati collegati."
    from_alias: "Dati RDF"
    to_alias: "Formato Semantico RDF"
  MATLAB:
    alias: "Array MATLAB"
    what: "MATLAB è un software calcolo numerico e visualizzazione alte prestazioni ampiamente utilizzato in calcolo ingegneristico, analisi dati e sviluppo algoritmi. Le sue operazioni array e matrice sono potenti, supportando calcoli matematici complessi ed elaborazione dati. Uno strumento essenziale per ingegneri, ricercatori e data scientist."
    step1: "Carica file MATLAB .m o incolla dati array. Lo strumento analizza sintassi MATLAB ed estrae informazioni struttura array."
    step3: "Genera codice array MATLAB standard con supporto per array multidimensionali, specifiche tipi dati e denominazione variabili. Il codice generato può essere eseguito direttamente in ambiente MATLAB per analisi dati e calcolo scientifico."
    from_alias: "Array MATLAB"
    to_alias: "Formato Codice MATLAB"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame è la struttura dati centrale del linguaggio di programmazione R, ampiamente utilizzata nell'analisi statistica, data mining e machine learning. R è lo strumento principale per il calcolo statistico e la grafica, con DataFrame che fornisce potenti capacità di manipolazione dati, analisi statistica e visualizzazione. Essenziale per data scientist, statistici e ricercatori che lavorano con l'analisi di dati strutturati."
    step1: "Carica file dati R o incolla codice DataFrame. Lo strumento analizza la sintassi R ed estrae informazioni sulla struttura DataFrame inclusi tipi di colonne, nomi di righe e contenuto dati."
    step3: "Genera codice R DataFrame standard con supporto per specifiche tipi di dati, livelli di fattori, nomi riga/colonna e strutture dati specifiche di R. Il codice generato può essere eseguito direttamente nell'ambiente R per analisi statistica ed elaborazione dati."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Inizia Conversione"
  start_generating: "Inizia a generare"
  api_docs: "Documentazione API"
related:
  section_title: 'Altri Convertitori {{ if and .from (ne .from "generator") }}{{ .from }} e {{ end }}{{ .to }}'
  section_description: 'Esplora altri convertitori per i formati {{ if and .from (ne .from "generator") }}{{ .from }} e {{ end }}{{ .to }}. Trasforma i tuoi dati tra più formati con i nostri strumenti di conversione online professionali.'
  title: "{{ .from }} a {{ .to }}"
howto:
  step2: "Modifica i dati utilizzando il nostro editor di tabelle online avanzato con funzionalità professionali. Supporta eliminazione righe vuote, rimozione duplicati, trasposizione dati, ordinamento, trova e sostituisci regex, e anteprima in tempo reale. Tutte le modifiche si convertono automaticamente nel formato %s con risultati precisi e affidabili."
  section_title: "Come usare {{ . }}"
  converter_description: "Impara a convertire {{ .from }} in {{ .to }} con la nostra guida passo-passo. Convertitore online professionale con funzionalità avanzate e anteprima in tempo reale."
  generator_description: "Impara a creare tabelle {{ .to }} professionali con il nostro generatore online. Modifica simile a Excel, anteprima in tempo reale, e capacità di esportazione istantanea."
extension:
  section_title: "Estensione Rilevamento ed Estrazione Tabelle"
  section_description: "Estrai tabelle da qualsiasi sito web con un clic. Converti istantaneamente in oltre 30 formati inclusi Excel, CSV, JSON - nessun copia-incolla richiesto."
  features:
    extraction_title: "Estrazione Tabelle con Un Clic"
    extraction_description: "Estrai istantaneamente tabelle da qualsiasi pagina web senza copia-incolla - estrazione dati professionale resa semplice"
    formats_title: "Supporto Convertitore 30+ Formati"
    formats_description: "Converti tabelle estratte in Excel, CSV, JSON, Markdown, SQL, e altro con il nostro convertitore di tabelle avanzato"
    detection_title: "Rilevamento Intelligente Tabelle"
    detection_description: "Rileva automaticamente ed evidenzia tabelle su qualsiasi pagina web per estrazione e conversione dati veloce"
  hover_tip: "✨ Passa il mouse su qualsiasi tabella per vedere l'icona di estrazione"
recommendations:
  section_title: "Raccomandato da Università e Professionisti"
  section_description: "TableConvert è considerato affidabile da professionisti in università, istituti di ricerca, e team di sviluppo per conversione tabelle affidabile e elaborazione dati."
  cards:
    university_title: "Università del Wisconsin-Madison"
    university_description: "TableConvert.com - Strumento professionale gratuito online per conversione tabelle e formati dati"
    university_link: "Leggi Articolo"
    facebook_title: "Comunità Professionisti Dati"
    facebook_description: "Condiviso e raccomandato da analisti dati e professionisti nei gruppi sviluppatori Facebook"
    facebook_link: "Visualizza Post"
    twitter_title: "Comunità Sviluppatori"
    twitter_description: "Raccomandato da @xiaoying_eth e altri sviluppatori su X (Twitter) per conversione tabelle"
    twitter_link: "Visualizza Tweet"
faq:
  section_title: "Domande Frequenti"
  section_description: "Domande comuni sul nostro convertitore di tabelle online gratuito, formati dati, e processo di conversione."
  what: "Cos'è il formato %s?"
  howto_convert:
    question: "Come usare {{ . }} gratuitamente?"
    answer: "Carica il tuo file {{ .from }}, incolla dati, o estrai da pagine web usando il nostro convertitore di tabelle online gratuito. Il nostro strumento convertitore professionale trasforma istantaneamente i tuoi dati nel formato {{ .to }} con anteprima in tempo reale e funzionalità di modifica avanzate. Scarica o copia il risultato convertito immediatamente."
  security:
    question: "I miei dati sono sicuri quando uso questo convertitore online?"
    answer: "Assolutamente! Tutte le conversioni di tabelle avvengono localmente nel tuo browser - i tuoi dati non lasciano mai il tuo dispositivo. Il nostro convertitore online elabora tutto lato client, garantendo completa privacy e sicurezza dati. Nessun file viene memorizzato sui nostri server."
  free:
    question: "TableConvert è davvero gratuito da usare?"
    answer: "Sì, TableConvert è completamente gratuito! Tutte le funzionalità del convertitore, editor tabelle, strumenti generatori dati, e opzioni di esportazione sono disponibili senza costi, registrazione, o tariffe nascoste. Converti file illimitati online gratuitamente."
  filesize:
    question: "Quali sono i limiti di dimensione file del convertitore online?"
    answer: "Il nostro convertitore di tabelle online gratuito supporta file fino a 10MB. Per file più grandi, elaborazione batch, o esigenze aziendali, usa la nostra estensione browser o servizio API professionale con limiti più alti."
stats:
  conversions: "Tabelle Convertite"
  tables: "Tabelle Generate"
  formats: "Formati File Dati"
  rating: "Valutazione Utente"
