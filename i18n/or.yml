site:
  fullname: "ଅନଲାଇନ୍ ଟେବୁଲ୍ କନଭର୍ଟ"
  name: "TableConvert"
  subtitle: "ମାଗଣା ଅନଲାଇନ୍ ଟେବୁଲ୍ କନଭର୍ଟର୍ ଏବଂ ଜେନେରେଟର୍"
  intro: "TableConvert ହେଉଛି ଏକ ମାଗଣା ଅନଲାଇନ୍ ଟେବୁଲ୍ କନଭର୍ଟର୍ ଏବଂ ଡାଟା ଜେନେରେଟର୍ ଟୁଲ୍ ଯାହା Excel, CSV, JSON, Markdown, LaTeX, SQL ଏବଂ ଅଧିକ ସମେତ 30+ ଫର୍ମାଟ୍ ମଧ୍ୟରେ ରୂପାନ୍ତରଣକୁ ସମର୍ଥନ କରେ."
  followTwitter: "X ରେ ଆମକୁ ଫଲୋ କରନ୍ତୁ"
title:
  converter: "%s ରୁ %s"
  generator: "%s ଜେନେରେଟର୍"
post:
  tags:
    converter: "କନଭର୍ଟର୍"
    editor: "ଏଡିଟର୍"
    generator: "ଜେନେରେଟର୍"
    maker: "ବିଲ୍ଡର୍"
  converter:
    title: "%s କୁ %s ରେ ଅନଲାଇନ୍ କନଭର୍ଟ କରନ୍ତୁ"
    short: "ଏକ ମାଗଣା ଏବଂ ଶକ୍ତିଶାଳୀ %s ରୁ %s ଅନଲାଇନ୍ ଟୁଲ୍"
    intro: "ବ୍ୟବହାର କରିବାକୁ ସହଜ ଅନଲାଇନ୍ %s ରୁ %s କନଭର୍ଟର୍। ଆମର ଅନ୍ତର୍ଦୃଷ୍ଟିପୂର୍ଣ୍ଣ ରୂପାନ୍ତରଣ ଉପକରଣ ସହିତ ଟେବୁଲ୍ ଡାଟାକୁ ସହଜରେ ରୂପାନ୍ତରିତ କରନ୍ତୁ। ଦ୍ରୁତ, ନିର୍ଭରଯୋଗ୍ୟ ଏବଂ ଉପଯୋଗକର୍ତ୍ତା-ବନ୍ଧୁ।"
  generator:
    title: "ଅନଲାଇନ୍ %s ଏଡିଟର୍ ଏବଂ ଜେନେରେଟର୍"
    short: "ବ୍ୟାପକ ବୈଶିଷ୍ଟ୍ୟ ସହିତ ପେସାଦାର %s ଅନଲାଇନ୍ ଜେନେରେସନ୍ ଟୁଲ୍"
    intro: "ବ୍ୟବହାର କରିବାକୁ ସହଜ ଅନଲାଇନ୍ %s ଜେନେରେଟର୍ ଏବଂ ଟେବୁଲ୍ ଏଡିଟର୍। ଆମର ଅନ୍ତର୍ଦୃଷ୍ଟିପୂର୍ଣ୍ଣ ଉପକରଣ ଏବଂ ରିଅଲ୍-ଟାଇମ୍ ପ୍ରିଭ୍ୟୁ ସହିତ ପେସାଦାର ଡାଟା ଟେବୁଲ୍ ସହଜରେ ସୃଷ୍ଟି କରନ୍ତୁ।"
navbar:
  search:
    placeholder: "କନଭର୍ଟର୍ ଖୋଜନ୍ତୁ..."
  sponsor: "ଆମକୁ କଫି କିଣନ୍ତୁ"
  extension: "ଏକ୍ସଟେନ୍ସନ୍"
  api: "API ଡକ୍ସ"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "ଡାଟା ସୋର୍ସ"
    placeholder: "ଆପଣଙ୍କର %s ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ କିମ୍ବା %s ଫାଇଲଗୁଡ଼ିକୁ ଏଠାରେ ଡ୍ରାଗ୍ କରନ୍ତୁ"
    example: "ଉଦାହରଣ"
    upload: "ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ"
    extract:
      enter: "ୱେବ୍ ପେଜ୍ ରୁ ଏକ୍ସଟ୍ରାକ୍ଟ କରନ୍ତୁ"
      intro: "ସଂରଚିତ ଡାଟା ସ୍ୱୟଂଚାଳିତ ଭାବରେ ଏକ୍ସଟ୍ରାକ୍ଟ କରିବା ପାଇଁ ଟେବୁଲ୍ ଡାଟା ଥିବା ଏକ ୱେବ୍ ପେଜ୍ URL ପ୍ରବେଶ କରନ୍ତୁ"
      btn: "%s ଏକ୍ସଟ୍ରାକ୍ଟ କରନ୍ତୁ"
    excel:
      sheet: "ୱାର୍କସିଟ୍"
      none: "କିଛି ନାହିଁ"
  tableEditor:
    title: "ଅନଲାଇନ୍ ଟେବୁଲ୍ ଏଡିଟର୍"
    undo: "ପୂର୍ବବତ୍ କରନ୍ତୁ"
    redo: "ପୁନଃ କରନ୍ତୁ"
    transpose: "ଟ୍ରାନ୍ସପୋଜ୍"
    clear: "ସଫା କରନ୍ତୁ"
    deleteBlank: "ଖାଲି ଡିଲିଟ୍ କରନ୍ତୁ"
    deleteDuplicate: "ଡୁପ୍ଲିକେଟ୍ ଡିଲିଟ୍ କରନ୍ତୁ"
    uppercase: "ବଡ଼ ଅକ୍ଷର"
    lowercase: "ଛୋଟ ଅକ୍ଷର"
    capitalize: "ପ୍ରଥମ ଅକ୍ଷର ବଡ଼"
    replace:
      replace: "ଖୋଜନ୍ତୁ ଏବଂ ବଦଳାନ୍ତୁ (Regex ସମର୍ଥିତ)"
      subst: "ଏହା ସହିତ ବଦଳାନ୍ତୁ..."
      btn: "ସବୁ ବଦଳାନ୍ତୁ"
  tableGenerator:
    title: "ଟେବୁଲ୍ ଜେନେରେଟର୍"
    sponsor: "ଆମକୁ କଫି କିଣନ୍ତୁ"
    copy: "କ୍ଲିପବୋର୍ଡରେ କପି କରନ୍ତୁ"
    download: "ଫାଇଲ୍ ଡାଉନଲୋଡ୍ କରନ୍ତୁ"
    tooltip:
      html:
        escape: "ପ୍ରଦର୍ଶନ ତ୍ରୁଟି ରୋକିବା ପାଇଁ HTML ବିଶେଷ ଅକ୍ଷରଗୁଡ଼ିକୁ (&, <, >, \", ') escape କରନ୍ତୁ"
        div: "ପାରମ୍ପରିକ TABLE ଟ୍ୟାଗ୍ ବଦଳରେ DIV+CSS ଲେଆଉଟ୍ ବ୍ୟବହାର କରନ୍ତୁ, responsive ଡିଜାଇନ୍ ପାଇଁ ଅଧିକ ଉପଯୁକ୍ତ"
        minify: "ସଙ୍କୁଚିତ HTML କୋଡ୍ ସୃଷ୍ଟି କରିବା ପାଇଁ ଖାଲି ସ୍ଥାନ ଏବଂ ଲାଇନ ବ୍ରେକ ହଟାନ୍ତୁ"
        thead: "ମାନକ table head (&lt;thead&gt;) ଏବଂ body (&lt;tbody&gt;) ଗଠନ ସୃଷ୍ଟି କରନ୍ତୁ"
        tableCaption: "ଟେବୁଲ୍ ଉପରେ ବର୍ଣ୍ଣନାମୂଳକ ଶୀର୍ଷକ ଯୋଗ କରନ୍ତୁ (&lt;caption&gt; element)"
        tableClass: "ସହଜ ଶୈଳୀ କଷ୍ଟମାଇଜେସନ୍ ପାଇଁ ଟେବୁଲ୍‌ରେ CSS କ୍ଲାସ ନାମ ଯୋଗ କରନ୍ତୁ"
        tableId: "ଜାଭାସ୍କ୍ରିପ୍ଟ ମାନିପୁଲେସନ୍ ପାଇଁ ଟେବୁଲ୍ ପାଇଁ ଅନନ୍ୟ ID ଚିହ୍ନିତକାରୀ ସେଟ୍ କରନ୍ତୁ"
      jira:
        escape: "Jira ଟେବୁଲ୍ ସିଣ୍ଟାକ୍ସ ସହିତ ବିବାଦ ଏଡ଼ାଇବା ପାଇଁ ପାଇପ୍ ଅକ୍ଷର (|) କୁ ଏସ୍କେପ୍ କରନ୍ତୁ"
      json:
        parsingJSON: "ସେଲ୍‌ଗୁଡ଼ିକରେ JSON ଷ୍ଟ୍ରିଂ କୁ ବୁଦ୍ଧିମତ୍ତାର ସହିତ ଅବଜେକ୍ଟ ରେ ପାର୍ସ କରନ୍ତୁ"
        minify: "ଫାଇଲ୍ ଆକାର କମାଇବା ପାଇଁ ସଙ୍କୁଚିତ ଏକ-ଲାଇନ JSON ଫର୍ମାଟ ସୃଷ୍ଟି କରନ୍ତୁ"
        format: "ଆଉଟପୁଟ୍ JSON ଡାଟା ଗଠନ ବାଛନ୍ତୁ: ଅବଜେକ୍ଟ ଆର୍ରେ, 2D ଆର୍ରେ, ଇତ୍ୟାଦି"
      latex:
        escape: "ଉପଯୁକ୍ତ compilation ନିଶ୍ଚିତ କରିବା ପାଇଁ LaTeX ବିଶେଷ ଅକ୍ଷରଗୁଡ଼ିକୁ (%, &, _, #, $, ଇତ୍ୟାଦି) escape କରନ୍ତୁ"
        ht: "ପୃଷ୍ଠାରେ ଟେବୁଲ୍ ସ୍ଥିତି ନିୟନ୍ତ୍ରଣ କରିବା ପାଇଁ ଫ୍ଲୋଟିଂ ପୋଜିସନ ପାରାମିଟର [!ht] ଯୋଗ କରନ୍ତୁ"
        mwe: "ସମ୍ପୂର୍ଣ୍ଣ LaTeX document ସୃଷ୍ଟି କରନ୍ତୁ"
        tableAlign: "ପୃଷ୍ଠାରେ ଟେବୁଲ୍‌ର ଭୂସମାନ୍ତର ସଜ୍ଜା ସେଟ୍ କରନ୍ତୁ"
        tableBorder: "ଟେବୁଲ୍ ସୀମା ଶୈଳୀ କନଫିଗର କରନ୍ତୁ: କୌଣସି ସୀମା ନାହିଁ, ଆଂଶିକ ସୀମା, ସମ୍ପୂର୍ଣ୍ଣ ସୀମା"
        label: "\\ref{} କମାଣ୍ଡ କ୍ରସ-ରେଫରେନ୍ସିଂ ପାଇଁ ଟେବୁଲ୍ ଲେବଲ ସେଟ୍ କରନ୍ତୁ"
        caption: "ଟେବୁଲ୍‌ର ଉପରେ କିମ୍ବା ତଳେ ପ୍ରଦର୍ଶନ କରିବା ପାଇଁ ଟେବୁଲ୍ caption ସେଟ୍ କରନ୍ତୁ"
        location: "ଟେବୁଲ୍ caption ପ୍ରଦର୍ଶନ ସ୍ଥିତି ବାଛନ୍ତୁ: ଉପରେ କିମ୍ବା ତଳେ"
        tableType: "ଟେବୁଲ୍ environment ପ୍ରକାର ବାଛନ୍ତୁ: tabular, longtable, array, ଇତ୍ୟାଦି"
      markdown:
        escape: "format ବିବାଦ ଏଡ଼ାଇବା ପାଇଁ Markdown ବିଶେଷ ଅକ୍ଷରଗୁଡ଼ିକୁ (*, _, |, \\, ଇତ୍ୟାଦି) escape କରନ୍ତୁ"
        pretty: "ଅଧିକ ସୁନ୍ଦର ଟେବୁଲ୍ ଫର୍ମାଟ ସୃଷ୍ଟି କରିବା ପାଇଁ ସ୍ତମ୍ଭ ପ୍ରସ୍ଥକୁ ସ୍ୱୟଂଚାଳିତ ସଜ୍ଜା କରନ୍ତୁ"
        simple: "ବାହ୍ୟ ସୀମା ଉଲମ୍ବ ରେଖା ଛାଡ଼ି ସରଳୀକୃତ ସିଣ୍ଟାକ୍ସ ବ୍ୟବହାର କରନ୍ତୁ"
        boldFirstRow: "ପ୍ରଥମ ଧାଡ଼ିର ଟେକ୍ସଟ୍ କୁ bold କରନ୍ତୁ"
        boldFirstColumn: "ପ୍ରଥମ ସ୍ତମ୍ଭର ଟେକ୍ସଟ୍ କୁ bold କରନ୍ତୁ"
        firstHeader: "ପ୍ରଥମ ଧାଡ଼ିକୁ ହେଡର ଭାବରେ ବିବେଚନା କରନ୍ତୁ ଏବଂ ବିଭାଜକ ରେଖା ଯୋଗ କରନ୍ତୁ"
        textAlign: "ସ୍ତମ୍ଭ ଟେକ୍ସଟ୍ ସଜ୍ଜା ସେଟ୍ କରନ୍ତୁ: ବାମ, ମଧ୍ୟ, ଡାହାଣ"
        multilineHandling: "ମଲ୍ଟିଲାଇନ ଟେକ୍ସଟ୍ ପରିଚାଳନା: ଲାଇନ ବ୍ରେକ ସଂରକ୍ଷଣ କରନ୍ତୁ, \\n ରେ ଏସ୍କେପ କରନ୍ତୁ, &lt;br&gt; ଟ୍ୟାଗ ବ୍ୟବହାର କରନ୍ତୁ"

        includeLineNumbers: "ଟେବୁଲ୍‌ର ବାମ ପାର୍ଶ୍ୱରେ ଲାଇନ ନମ୍ବର ସ୍ତମ୍ଭ ଯୋଗ କରନ୍ତୁ"
      magic:
        builtin: "ପୂର୍ବନିର୍ଧାରିତ ସାଧାରଣ ଟେମ୍ପଲେଟ ଫର୍ମାଟ ବାଛନ୍ତୁ"
        rowsTpl: "<table> <tr> <th>ଯାଦୁକରୀ ସିଣ୍ଟାକ୍ସ</th> <th>ବର୍ଣ୍ଣନା</th> <th>JS ମେଥଡ ସମର୍ଥନ</th> </tr> <tr> <td>{h1} {h2} ...</td> <td><b>ଶୀର୍ଷକ</b>ର ପ୍ରଥମ, ଦ୍ୱିତୀୟ ... ଫିଲ୍ଡ, ଅର୍ଥାତ୍ {hA} {hB} ...</td> <td>ଷ୍ଟ୍ରିଂ ମେଥଡ</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>ବର୍ତ୍ତମାନ ଧାଡ଼ିର ପ୍ରଥମ, ଦ୍ୱିତୀୟ ... ଫିଲ୍ଡ, ଅର୍ଥାତ୍ {$A} {$B} ...</td> <td>ଷ୍ଟ୍ରିଂ ମେଥଡ</td> </tr> <tr> <td>{F,} {F;}</td> <td><b>F</b> ପରେ ଥିବା ଷ୍ଟ୍ରିଂ ଦ୍ୱାରା ବର୍ତ୍ତମାନ ଧାଡ଼ିକୁ ବିଭାଜନ କରନ୍ତୁ</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>ବର୍ତ୍ତମାନ <b>ଧାଡ଼ି</b>ର ଲାଇନ <b>ନମ୍ବର</b> 1 କିମ୍ବା 100 ରୁ</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>ଧାଡ଼ିଗୁଡ଼ିକ</b>ର <b>ଶେଷ</b> ଲାଇନ <b>ନମ୍ବର</b> </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>ଜାଭାସ୍କ୍ରିପ୍ଟ କୋଡ୍ <b>ଚଲାନ୍ତୁ</b>, ଉଦାହରଣ: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> ବ୍ରେସେସ {...} ଆଉଟପୁଟ କରିବା ପାଇଁ ବ୍ୟାକସ୍ଲାସ <b>\\</b> ବ୍ୟବହାର କରନ୍ତୁ </td> <td></td> </tr></table>"
        headerTpl: "ହେଡର ବିଭାଗ ପାଇଁ କଷ୍ଟମ୍ ଆଉଟପୁଟ ଟେମ୍ପଲେଟ"
        footerTpl: "ଫୁଟର ବିଭାଗ ପାଇଁ କଷ୍ଟମ୍ ଆଉଟପୁଟ ଟେମ୍ପଲେଟ"
      textile:
        escape: "ଫର୍ମାଟ୍ ବିବାଦ ଏଡ଼ାଇବା ପାଇଁ Textile ସିଣ୍ଟାକ୍ସ ଅକ୍ଷର (|, ., -, ^) ଏସ୍କେପ୍ କରନ୍ତୁ"
        rowHeader: "ପ୍ରଥମ ଧାଡ଼ିକୁ ହେଡର୍ ଧାଡ଼ି ଭାବରେ ସେଟ୍ କରନ୍ତୁ"
        thead: "ଟେବୁଲ୍ ହେଡ୍ ଏବଂ ବଡି ପାଇଁ Textile ସିଣ୍ଟାକ୍ସ ମାର୍କର ଯୋଗ କରନ୍ତୁ"
      xml:
        escape: "ବୈଧ XML ନିଶ୍ଚିତ କରିବା ପାଇଁ XML ବିଶେଷ ଅକ୍ଷର (&lt;, &gt;, &amp;, \", ') ଏସ୍କେପ୍ କରନ୍ତୁ"
        minify: "ଅତିରିକ୍ତ ହ୍ୱାଇଟସ୍ପେସ୍ ହଟାଇ ସଙ୍କୁଚିତ XML ଆଉଟପୁଟ୍ ଜେନେରେଟ୍ କରନ୍ତୁ"
        rootElement: "XML ରୁଟ୍ ଏଲିମେଣ୍ଟ ଟ୍ୟାଗ୍ ନାମ ସେଟ୍ କରନ୍ତୁ"
        rowElement: "ଡାଟାର ପ୍ରତ୍ୟେକ ଧାଡ଼ି ପାଇଁ XML ଏଲିମେଣ୍ଟ ଟ୍ୟାଗ୍ ନାମ ସେଟ୍ କରନ୍ତୁ"
        declaration: "XML ଘୋଷଣା ହେଡର୍ (&lt;?xml version=\"1.0\"?&gt;) ଯୋଗ କରନ୍ତୁ"
        attributes: "ଚାଇଲ୍ଡ ଏଲିମେଣ୍ଟ ପରିବର୍ତ୍ତେ XML ଆଟ୍ରିବ୍ୟୁଟ ଭାବରେ ଡାଟା ଆଉଟପୁଟ୍ କରନ୍ତୁ"
        cdata: "ବିଶେଷ ଅକ୍ଷର ସୁରକ୍ଷା ପାଇଁ ଟେକ୍ସଟ୍ ବିଷୟବସ୍ତୁକୁ CDATA ସହିତ ରାପ୍ କରନ୍ତୁ"
        encoding: "XML ଡକୁମେଣ୍ଟ ପାଇଁ ଅକ୍ଷର ଏନକୋଡିଂ ଫର୍ମାଟ୍ ସେଟ୍ କରନ୍ତୁ"
        indentation: "XML ଇଣ୍ଡେଣ୍ଟେସନ୍ ଅକ୍ଷର ବାଛନ୍ତୁ: ସ୍ପେସ୍ କିମ୍ବା ଟ୍ୟାବ୍"
      yaml:
        indentSize: "YAML ହାଇରାର୍କି ଇଣ୍ଡେଣ୍ଟେସନ୍ ପାଇଁ ସ୍ପେସ୍ ସଂଖ୍ୟା ସେଟ୍ କରନ୍ତୁ (ସାଧାରଣତ 2 କିମ୍ବା 4)"
        arrayStyle: "ଆର୍ରେ ଫର୍ମାଟ୍: ବ୍ଲକ୍ (ଲାଇନ ପ୍ରତି ଗୋଟିଏ ଆଇଟମ୍) କିମ୍ବା ଫ୍ଲୋ (ଇନଲାଇନ୍ ଫର୍ମାଟ୍)"
        quotationStyle: "ଷ୍ଟ୍ରିଂ କୋଟ୍ ଶୈଳୀ: କୋଟ୍ ନାହିଁ, ଏକକ କୋଟ୍, ଡବଲ୍ କୋଟ୍"
      pdf:
        theme: "ବୃତ୍ତିଗତ ଦଲିଲଗୁଡ଼ିକ ପାଇଁ PDF ଟେବଲ୍ ଭିଜୁଆଲ୍ ଷ୍ଟାଇଲ୍ ବାଛନ୍ତୁ"
        headerColor: "PDF ଟେବଲ୍ ହେଡର୍ ବ୍ୟାକଗ୍ରାଉଣ୍ଡ ରଙ୍ଗ ବାଛନ୍ତୁ"
        showHead: "PDF ପୃଷ୍ଠାଗୁଡ଼ିକରେ ହେଡର୍ ପ୍ରଦର୍ଶନ ନିୟନ୍ତ୍ରଣ କରନ୍ତୁ"
        docTitle: "PDF ଦଲିଲ ପାଇଁ ଇଚ୍ଛାଧୀନ ଶୀର୍ଷକ"
        docDescription: "PDF ଦଲିଲ ପାଇଁ ଇଚ୍ଛାଧୀନ ବର୍ଣ୍ଣନା ପାଠ୍ୟ"
      csv:
        bom: "Excel ଏବଂ ଅନ୍ୟାନ୍ୟ ସଫ୍ଟୱେୟାରକୁ ଏନକୋଡିଂ ଚିହ୍ନିବାରେ ସାହାଯ୍ୟ କରିବା ପାଇଁ UTF-8 ବାଇଟ୍ ଅର୍ଡର ମାର୍କ ଯୋଗ କରନ୍ତୁ"
      excel:
        autoWidth: "ବିଷୟବସ୍ତୁ ଆଧାରରେ ସ୍ୱୟଂଚାଳିତ ଭାବରେ ସ୍ତମ୍ଭ ଓସାର ସଜାନ୍ତୁ"
        protectSheet: "ପାସୱର୍ଡ ସହିତ ୱର୍କସିଟ୍ ସୁରକ୍ଷା ସକ୍ଷମ କରନ୍ତୁ: tableconvert.com"
      sql:  
        primaryKey: "CREATE TABLE ଷ୍ଟେଟମେଣ୍ଟ ପାଇଁ ପ୍ରାଥମିକ କି ଫିଲ୍ଡ ନାମ ନିର୍ଦ୍ଦିଷ୍ଟ କରନ୍ତୁ"
        dialect: "ଡାଟାବେସ୍ ପ୍ରକାର ବାଛନ୍ତୁ, କୋଟ୍ ଏବଂ ଡାଟା ପ୍ରକାର ସିଣ୍ଟାକ୍ସକୁ ପ୍ରଭାବିତ କରେ"
      ascii:
        forceSep: "ଡାଟାର ପ୍ରତ୍ୟେକ ଧାଡ଼ି ମଧ୍ୟରେ ବିଭାଜକ ଲାଇନ ବାଧ୍ୟତାମୂଳକ କରନ୍ତୁ"
        style: "ASCII ଟେବୁଲ୍ ବର୍ଡର୍ ଅଙ୍କନ ଶୈଳୀ ବାଛନ୍ତୁ"
        comment: "ସମଗ୍ର ଟେବୁଲ୍କୁ ରାପ୍ କରିବା ପାଇଁ କମେଣ୍ଟ ମାର୍କର ଯୋଗ କରନ୍ତୁ"
      mediawiki:
        minify: "ଅତିରିକ୍ତ ହ୍ୱାଇଟସ୍ପେସ୍ ହଟାଇ ଆଉଟପୁଟ୍ କୋଡ୍ ସଙ୍କୁଚିତ କରନ୍ତୁ"
        header: "ପ୍ରଥମ ଧାଡ଼ିକୁ ହେଡର୍ ଶୈଳୀ ଭାବରେ ମାର୍କ କରନ୍ତୁ"
        sort: "ଟେବୁଲ୍ କ୍ଲିକ୍ ସର୍ଟିଂ କାର୍ଯ୍ୟକାରିତା ସକ୍ଷମ କରନ୍ତୁ"
      asciidoc:
        minify: "AsciiDoc ଫର୍ମାଟ୍ ଆଉଟପୁଟ୍ ସଙ୍କୁଚିତ କରନ୍ତୁ"
        firstHeader: "ପ୍ରଥମ ଧାଡ଼ିକୁ ହେଡର୍ ଧାଡ଼ି ଭାବରେ ସେଟ୍ କରନ୍ତୁ"
        lastFooter: "ଶେଷ ଧାଡ଼ିକୁ ଫୁଟର୍ ଧାଡ଼ି ଭାବରେ ସେଟ୍ କରନ୍ତୁ"
        title: "ଟେବୁଲ୍‌ରେ ଶୀର୍ଷକ ଟେକ୍ସଟ୍ ଯୋଗ କରନ୍ତୁ"
      tracwiki:
        rowHeader: "ପ୍ରଥମ ଧାଡ଼ିକୁ ହେଡର୍ ଭାବରେ ସେଟ୍ କରନ୍ତୁ"
        colHeader: "ପ୍ରଥମ ସ୍ତମ୍ଭକୁ ହେଡର୍ ଭାବରେ ସେଟ୍ କରନ୍ତୁ"
      bbcode:
        minify: "BBCode ଆଉଟପୁଟ୍ ଫର୍ମାଟ୍ ସଙ୍କୁଚିତ କରନ୍ତୁ"
      restructuredtext:
        style: "reStructuredText ଟେବୁଲ୍ ବର୍ଡର୍ ଶୈଳୀ ବାଛନ୍ତୁ"
        forceSep: "ବିଭାଜକ ଲାଇନ ବାଧ୍ୟତାମୂଳକ କରନ୍ତୁ"
    label:
      ascii:
        forceSep: "ଧାଡ଼ି ବିଭାଜକ"
        style: "ବର୍ଡର୍ ଶୈଳୀ"
        comment: "କମେଣ୍ଟ ରାପର୍"
      restructuredtext:
        style: "ବର୍ଡର୍ ଶୈଳୀ"
        forceSep: "ବାଧ୍ୟତାମୂଳକ ବିଭାଜକ"
      bbcode:
        minify: "ଆଉଟପୁଟ୍ ମିନିଫାଇ କରନ୍ତୁ"
      csv:
        doubleQuote: "ଡବଲ୍ କୋଟ୍ ରାପ୍"
        delimiter: "ଫିଲ୍ଡ ବିଭାଜକ"
        bom: "UTF-8 BOM"
        valueDelimiter: "ମୂଲ୍ୟ ବିଭାଜକ"
        rowDelimiter: "ଧାଡ଼ି ବିଭାଜକ"
        prefix: "ଧାଡ଼ି ଉପସର୍ଗ"
        suffix: "ଧାଡ଼ି ପ୍ରତ୍ୟୟ"
      excel:
        autoWidth: "ସ୍ୱୟଂଚାଳିତ ଓସାର"
        textFormat: "ଟେକ୍ସଟ୍ ଫର୍ମାଟ୍"
        protectSheet: "ସିଟ୍ ସୁରକ୍ଷା"
        boldFirstRow: "ପ୍ରଥମ ଧାଡ଼ି ବୋଲ୍ଡ"
        boldFirstColumn: "ପ୍ରଥମ ସ୍ତମ୍ଭ ବୋଲ୍ଡ"
        sheetName: "ସିଟ୍ ନାମ"
      html:
        escape: "HTML ଅକ୍ଷର ଏସ୍କେପ୍"
        div: "DIV ଟେବୁଲ୍"
        minify: "କୋଡ୍ ମିନିଫାଇ"
        thead: "ଟେବୁଲ୍ ହେଡ୍ ଗଠନ"
        tableCaption: "ଟେବୁଲ୍ କ୍ୟାପସନ୍"
        tableClass: "ଟେବୁଲ୍ କ୍ଲାସ୍"
        tableId: "ଟେବୁଲ୍ ID"
        rowHeader: "ଧାଡ଼ି ହେଡର୍"
        colHeader: "ସ୍ତମ୍ଭ ହେଡର୍"
      jira:
        escape: "ଅକ୍ଷର ଏସ୍କେପ୍"
        rowHeader: "ଧାଡ଼ି ହେଡର୍"
        colHeader: "ସ୍ତମ୍ଭ ହେଡର୍"
      json:
        parsingJSON: "JSON ପାର୍ସ କରନ୍ତୁ"
        minify: "ଆଉଟପୁଟ୍ ମିନିଫାଇ"
        format: "ଡାଟା ଫର୍ମାଟ୍"
        rootName: "ରୁଟ୍ ଅବଜେକ୍ଟ ନାମ"
        indentSize: "ଇଣ୍ଡେଣ୍ଟ ଆକାର"
      jsonlines:
        parsingJSON: "JSON ପାର୍ସ କରନ୍ତୁ"
        format: "ଡାଟା ଫର୍ମାଟ୍"
      latex:
        escape: "LaTeX ଟେବୁଲ୍ ଅକ୍ଷର ଏସ୍କେପ୍"
        ht: "ଫ୍ଲୋଟ୍ ପୋଜିସନ୍"
        mwe: "ସମ୍ପୂର୍ଣ୍ଣ ଡକୁମେଣ୍ଟ"
        tableAlign: "ଟେବୁଲ୍ ସଜ୍ଜା"
        tableBorder: "ବର୍ଡର୍ ଶୈଳୀ"
        label: "ରେଫରେନ୍ସ ଲେବଲ୍"
        caption: "ଟେବୁଲ୍ କ୍ୟାପସନ୍"
        location: "କ୍ୟାପସନ୍ ପୋଜିସନ୍"
        tableType: "ଟେବୁଲ୍ ପ୍ରକାର"
        boldFirstRow: "ପ୍ରଥମ ଧାଡ଼ି ବୋଲ୍ଡ"
        boldFirstColumn: "ପ୍ରଥମ ସ୍ତମ୍ଭ ବୋଲ୍ଡ"
        textAlign: "ଟେକ୍ସଟ୍ ସଜ୍ଜା"
        borders: "ବର୍ଡର୍ ସେଟିଂସ"
      markdown:
        escape: "ଅକ୍ଷର ଏସ୍କେପ୍"
        pretty: "ସୁନ୍ଦର ମାର୍କଡାଉନ୍ ଟେବୁଲ୍"
        simple: "ସରଳ ମାର୍କଡାଉନ୍ ଫର୍ମାଟ୍"
        boldFirstRow: "ପ୍ରଥମ ଧାଡ଼ି ବୋଲ୍ଡ"
        boldFirstColumn: "ପ୍ରଥମ ସ୍ତମ୍ଭ ବୋଲ୍ଡ"
        firstHeader: "ପ୍ରଥମ ହେଡର୍"
        textAlign: "ଟେକ୍ସଟ୍ ସଜ୍ଜା"
        multilineHandling: "ମଲ୍ଟିଲାଇନ୍ ହ୍ୟାଣ୍ଡଲିଂ"

        includeLineNumbers: "ଲାଇନ୍ ନମ୍ବର ଯୋଗ କରନ୍ତୁ"
        align: "ସଜ୍ଜା"
      mediawiki:
        minify: "କୋଡ୍ ମିନିଫାଇ"
        header: "ହେଡର୍ ମାର୍କଅପ୍"
        sort: "ସର୍ଟେବଲ୍"
      asciidoc:
        minify: "ଫର୍ମାଟ୍ ମିନିଫାଇ"
        firstHeader: "ପ୍ରଥମ ହେଡର୍"
        lastFooter: "ଶେଷ ଫୁଟର୍"
        title: "ଟେବୁଲ୍ ଶୀର୍ଷକ"
      tracwiki:
        rowHeader: "ଧାଡ଼ି ହେଡର୍"
        colHeader: "ସ୍ତମ୍ଭ ହେଡର୍"
      sql:
        drop: "ଟେବୁଲ୍ ଡ୍ରପ୍ (ଯଦି ଅଛି)"
        create: "ଟେବୁଲ୍ ସୃଷ୍ଟି"
        oneInsert: "ବ୍ୟାଚ୍ ଇନସର୍ଟ"
        table: "ଟେବୁଲ୍ ନାମ"
        dialect: "ଡାଟାବେସ୍ ପ୍ରକାର"
        primaryKey: "ପ୍ରାଥମିକ କି"
      magic:
        builtin: "ବିଲ୍ଟ-ଇନ୍ ଟେମ୍ପଲେଟ୍"
        rowsTpl: "ଧାଡ଼ି ଟେମ୍ପଲେଟ୍, ସିଣ୍ଟାକ୍ସ ->"
        headerTpl: "ହେଡର୍ ଟେମ୍ପଲେଟ୍"
        footerTpl: "ଫୁଟର୍ ଟେମ୍ପଲେଟ୍"
      textile:
        escape: "ଅକ୍ଷର ଏସ୍କେପ୍"
        rowHeader: "ଧାଡ଼ି ହେଡର୍"
        thead: "ଟେବୁଲ୍ ହେଡ୍ ସିଣ୍ଟାକ୍ସ"
      xml:
        escape: "XML ଅକ୍ଷର ଏସ୍କେପ୍"
        minify: "ଆଉଟପୁଟ୍ ମିନିଫାଇ"
        rootElement: "ରୁଟ୍ ଏଲିମେଣ୍ଟ"
        rowElement: "ଧାଡ଼ି ଏଲିମେଣ୍ଟ"
        declaration: "XML ଘୋଷଣା"
        attributes: "ଆଟ୍ରିବ୍ୟୁଟ୍ ମୋଡ୍"
        cdata: "CDATA ରାପର୍"
        encoding: "ଏନକୋଡିଂ"
        indentSize: "ଇଣ୍ଡେଣ୍ଟ ଆକାର"
      yaml:
        indentSize: "ଇଣ୍ଡେଣ୍ଟ ଆକାର"
        arrayStyle: "ଆର୍ରେ ଶୈଳୀ"
        quotationStyle: "କୋଟ୍ ଶୈଳୀ"
      pdf:
        theme: "PDF ଟେବଲ୍ ଥିମ୍"
        headerColor: "PDF ହେଡର୍ ରଙ୍ଗ"
        showHead: "PDF ହେଡର୍ ପ୍ରଦର୍ଶନ"
        docTitle: "PDF ଦଲିଲ ଶୀର୍ଷକ"
        docDescription: "PDF ଦଲିଲ ବର୍ଣ୍ଣନା"
sidebar:
  all: "ସମସ୍ତ କନଭର୍ସନ୍ ଟୁଲ୍"
  dataSource:
    title: "ଡାଟା ସୋର୍ସ"
    description:
      converter: "%s କୁ %s ରେ କନଭର୍ସନ୍ ପାଇଁ ଇମ୍ପୋର୍ଟ କରନ୍ତୁ। ଫାଇଲ୍ ଅପଲୋଡ୍, ଅନଲାଇନ୍ ଏଡିଟିଂ ଏବଂ ୱେବ୍ ଡାଟା ଏକ୍ସଟ୍ରାକସନକୁ ସମର୍ଥନ କରେ।"
      generator: "ମାନୁଆଲ୍ ଇନପୁଟ୍, ଫାଇଲ୍ ଇମ୍ପୋର୍ଟ ଏବଂ ଟେମ୍ପଲେଟ୍ ଜେନେରେସନ୍ ସହିତ ଏକାଧିକ ଇନପୁଟ୍ ପଦ୍ଧତି ସମର୍ଥନ ସହିତ ଟେବୁଲ୍ ଡାଟା ସୃଷ୍ଟି କରନ୍ତୁ।"
  tableEditor:
    title: "ଅନଲାଇନ୍ ଟେବୁଲ୍ ଏଡିଟର୍"
    description:
      converter: "ଆମର ଟେବୁଲ୍ ଏଡିଟର୍ ବ୍ୟବହାର କରି %s କୁ ଅନଲାଇନ୍ ପ୍ରୋସେସ୍ କରନ୍ତୁ। ଖାଲି ଧାଡ଼ି ଡିଲିଟ୍, ଡୁପ୍ଲିକେସନ୍, ସର୍ଟିଂ ଏବଂ ଖୋଜ ଓ ବଦଳ ସମର୍ଥନ ସହିତ Excel ପରି ଅପରେସନ୍ ଅଭିଜ୍ଞତା।"
      generator: "Excel ପରି ଅପରେସନ୍ ଅଭିଜ୍ଞତା ପ୍ରଦାନ କରୁଥିବା ଶକ୍ତିଶାଳୀ ଅନଲାଇନ୍ ଟେବୁଲ୍ ଏଡିଟର୍। ଖାଲି ଧାଡ଼ି ଡିଲିଟ୍, ଡୁପ୍ଲିକେସନ୍, ସର୍ଟିଂ ଏବଂ ଖୋଜ ଓ ବଦଳକୁ ସମର୍ଥନ କରେ।"
  tableGenerator:
    title: "ଟେବୁଲ୍ ଜେନେରେଟର୍"
    description:
      converter: "ଟେବୁଲ୍ ଜେନେରେଟର୍‌ର ରିଅଲ୍-ଟାଇମ୍ ପ୍ରିଭ୍ୟୁ ସହିତ ଦ୍ରୁତ %s ଜେନେରେଟ୍ କରନ୍ତୁ। ସମୃଦ୍ଧ ଏକ୍ସପୋର୍ଟ ବିକଳ୍ପ, ଏକ-କ୍ଲିକ୍ କପି ଓ ଡାଉନଲୋଡ୍।"
      generator: "ବିଭିନ୍ନ ବ୍ୟବହାର ପରିସ୍ଥିତି ପୂରଣ କରିବା ପାଇଁ ଏକାଧିକ ଫର୍ମାଟରେ %s ଡାଟା ଏକ୍ସପୋର୍ଟ କରନ୍ତୁ। କଷ୍ଟମ୍ ବିକଳ୍ପ ଏବଂ ରିଅଲ୍-ଟାଇମ୍ ପ୍ରିଭ୍ୟୁକୁ ସମର୍ଥନ କରେ।"
footer:
  changelog: "ପରିବର୍ତ୍ତନ ଲଗ୍"
  sponsor: "ପ୍ରାୟୋଜକ"
  contact: "ଆମକୁ ଯୋଗାଯୋଗ କରନ୍ତୁ"
  privacyPolicy: "ଗୋପନୀୟତା ନୀତି"
  about: "ବିଷୟରେ"
  resources: "ସମ୍ବଳ"
  popularConverters: "ଲୋକପ୍ରିୟ କନଭର୍ଟର୍"
  popularGenerators: "ଲୋକପ୍ରିୟ ଜେନେରେଟର୍"
  dataSecurity: "ଆପଣଙ୍କର ଡାଟା ସୁରକ୍ଷିତ - ସମସ୍ତ ରୂପାନ୍ତରଣ ଆପଣଙ୍କ ବ୍ରାଉଜରରେ ଚାଲିଥାଏ."
converters:
  Markdown:
    alias: "ମାର୍କଡାଉନ୍ ଟେବୁଲ୍"
    what: "ମାର୍କଡାଉନ୍ ହେଉଛି ଏକ ହାଲୁକା ମାର୍କଅପ୍ ଭାଷା ଯାହା ବ୍ୟାପକ ଭାବରେ ଯାନ୍ତ୍ରିକ ଡକୁମେଣ୍ଟେସନ୍, ବ୍ଲଗ୍ କଣ୍ଟେଣ୍ଟ ସୃଷ୍ଟି ଏବଂ ୱେବ୍ ଡେଭେଲପମେଣ୍ଟରେ ବ୍ୟବହୃତ ହୁଏ। ଏହାର ଟେବୁଲ୍ ସିଣ୍ଟାକ୍ସ ସଂକ୍ଷିପ୍ତ ଏବଂ ଅନ୍ତର୍ଦୃଷ୍ଟିପୂର୍ଣ୍ଣ, ଟେକ୍ସଟ୍ ସଜ୍ଜା, ଲିଙ୍କ୍ ଏମ୍ବେଡିଂ ଏବଂ ଫର୍ମାଟିଂକୁ ସମର୍ଥନ କରେ। ଏହା ପ୍ରୋଗ୍ରାମର୍ ଏବଂ ଯାନ୍ତ୍ରିକ ଲେଖକମାନଙ୍କ ପାଇଁ ପସନ୍ଦିତ ଟୁଲ୍, GitHub, GitLab ଏବଂ ଅନ୍ୟାନ୍ୟ କୋଡ୍ ହୋଷ୍ଟିଂ ପ୍ଲାଟଫର୍ମ ସହିତ ସମ୍ପୂର୍ଣ୍ଣ ସୁସଙ୍ଗତ।"
    step1: "ଡାଟା ସୋର୍ସ ଅଞ୍ଚଳରେ ମାର୍କଡାଉନ୍ ଟେବୁଲ୍ ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ, କିମ୍ବା ଅପଲୋଡ୍ ପାଇଁ ସିଧାସଳଖ .md ଫାଇଲଗୁଡ଼ିକୁ ଡ୍ରାଗ୍ ଏବଂ ଡ୍ରପ୍ କରନ୍ତୁ। ଟୁଲ୍ ସ୍ୱୟଂଚାଳିତ ଭାବରେ ଟେବୁଲ୍ ଗଠନ ଏବଂ ଫର୍ମାଟିଂ ପାର୍ସ କରେ, ଜଟିଳ ନେଷ୍ଟେଡ୍ କଣ୍ଟେଣ୍ଟ ଏବଂ ବିଶେଷ ଅକ୍ଷର ହ୍ୟାଣ୍ଡଲିଂକୁ ସମର୍ଥନ କରେ।"
    step3: "ରିଅଲ୍-ଟାଇମ୍‌ରେ ମାନକ ମାର୍କଡାଉନ୍ ଟେବୁଲ୍ କୋଡ୍ ଜେନେରେଟ୍ କରନ୍ତୁ, ଏକାଧିକ ସଜ୍ଜା ପଦ୍ଧତି, ଟେକ୍ସଟ୍ ବୋଲ୍ଡିଂ, ଲାଇନ୍ ନମ୍ବର ଯୋଗ ଏବଂ ଅନ୍ୟାନ୍ୟ ଉନ୍ନତ ଫର୍ମାଟ୍ ସେଟିଂସକୁ ସମର୍ଥନ କରେ। ଜେନେରେଟ୍ ହୋଇଥିବା କୋଡ୍ GitHub ଏବଂ ପ୍ରମୁଖ ମାର୍କଡାଉନ୍ ଏଡିଟର୍ ସହିତ ସମ୍ପୂର୍ଣ୍ଣ ସୁସଙ୍ଗତ, ଏକ-କ୍ଲିକ୍ କପି ସହିତ ବ୍ୟବହାର ପାଇଁ ପ୍ରସ୍ତୁତ।"
    from_alias: "ମାର୍କଡାଉନ୍ ଟେବୁଲ୍ ଫାଇଲ୍"
    to_alias: "ମାର୍କଡାଉନ୍ ଟେବୁଲ୍ ଫର୍ମାଟ୍"
  Magic:
    alias: "କଷ୍ଟମ୍ ଟେମ୍ପଲେଟ୍"
    what: "ମ୍ୟାଜିକ୍ ଟେମ୍ପଲେଟ୍ ହେଉଛି ଏହି ଟୁଲ୍‌ର ଏକ ଅନନ୍ୟ ଉନ୍ନତ ଡାଟା ଜେନେରେଟର୍, ଯାହା ବ୍ୟବହାରକାରୀମାନଙ୍କୁ କଷ୍ଟମ୍ ଟେମ୍ପଲେଟ୍ ସିଣ୍ଟାକ୍ସ ମାଧ୍ୟମରେ ଇଚ୍ଛାମତ ଫର୍ମାଟ୍ ଡାଟା ଆଉଟପୁଟ୍ ସୃଷ୍ଟି କରିବାକୁ ଅନୁମତି ଦିଏ। ଭେରିଏବଲ୍ ରିପ୍ଲେସମେଣ୍ଟ, ସର୍ତ୍ତମୂଳକ ବିଚାର ଏବଂ ଲୁପ୍ ପ୍ରୋସେସିଂକୁ ସମର୍ଥନ କରେ। ଜଟିଳ ଡାଟା କନଭର୍ସନ୍ ଆବଶ୍ୟକତା ଏବଂ ବ୍ୟକ୍ତିଗତ ଆଉଟପୁଟ୍ ଫର୍ମାଟ୍ ପରିଚାଳନା ପାଇଁ ଏହା ଚରମ ସମାଧାନ, ବିଶେଷ ଭାବରେ ଡେଭେଲପର୍ ଏବଂ ଡାଟା ଇଞ୍ଜିନିୟରମାନଙ୍କ ପାଇଁ ଉପଯୁକ୍ତ।"
    step1: "ବିଲ୍ଟ-ଇନ୍ ସାଧାରଣ ଟେମ୍ପଲେଟ୍ ବାଛନ୍ତୁ କିମ୍ବା କଷ୍ଟମ୍ ଟେମ୍ପଲେଟ୍ ସିଣ୍ଟାକ୍ସ ସୃଷ୍ଟି କରନ୍ତୁ। ସମୃଦ୍ଧ ଭେରିଏବଲ୍ ଏବଂ ଫଙ୍କସନ୍‌କୁ ସମର୍ଥନ କରେ ଯାହା ଜଟିଳ ଡାଟା ଗଠନ ଏବଂ ବ୍ୟବସାୟିକ ତର୍କ ପରିଚାଳନା କରିପାରେ।"
    step3: "କଷ୍ଟମ୍ ଫର୍ମାଟ୍ ଆବଶ୍ୟକତାକୁ ସମ୍ପୂର୍ଣ୍ଣ ଭାବରେ ପୂରଣ କରୁଥିବା ଡାଟା ଆଉଟପୁଟ୍ ଜେନେରେଟ୍ କରନ୍ତୁ। ଜଟିଳ ଡାଟା କନଭର୍ସନ୍ ତର୍କ ଏବଂ ସର୍ତ୍ତମୂଳକ ପ୍ରୋସେସିଂକୁ ସମର୍ଥନ କରେ, ଡାଟା ପ୍ରୋସେସିଂ ଦକ୍ଷତା ଏବଂ ଆଉଟପୁଟ୍ ଗୁଣବତ୍ତାକୁ ବହୁତ ଉନ୍ନତ କରେ। ବ୍ୟାଚ୍ ଡାଟା ପ୍ରୋସେସିଂ ପାଇଁ ଏକ ଶକ୍ତିଶାଳୀ ଟୁଲ୍।"
    from_alias: "ଟେବୁଲ୍ ଡାଟା"
    to_alias: "କଷ୍ଟମ୍ ଫର୍ମାଟ୍ ଆଉଟପୁଟ୍"
  CSV:
    alias: "CSV"
    what: "CSV (କମା-ସେପାରେଟେଡ୍ ଭାଲ୍ୟୁସ୍) ହେଉଛି ସର୍ବାଧିକ ବ୍ୟବହୃତ ଡାଟା ଏକ୍ସଚେଞ୍ଜ ଫର୍ମାଟ୍, Excel, Google Sheets, ଡାଟାବେସ୍ ସିଷ୍ଟମ୍ ଏବଂ ବିଭିନ୍ନ ଡାଟା ବିଶ୍ଳେଷଣ ଟୁଲ୍ ଦ୍ୱାରା ସମ୍ପୂର୍ଣ୍ଣ ସମର୍ଥିତ। ଏହାର ସରଳ ଗଠନ ଏବଂ ଦୃଢ଼ ସୁସଙ୍ଗତି ଏହାକୁ ଡାଟା ମାଇଗ୍ରେସନ୍, ବ୍ୟାଚ୍ ଇମ୍ପୋର୍ଟ/ଏକ୍ସପୋର୍ଟ ଏବଂ କ୍ରସ୍-ପ୍ଲାଟଫର୍ମ ଡାଟା ଏକ୍ସଚେଞ୍ଜ ପାଇଁ ମାନକ ଫର୍ମାଟ୍ କରିଥାଏ, ବ୍ୟବସାୟିକ ବିଶ୍ଳେଷଣ, ଡାଟା ସାଇନ୍ସ ଏବଂ ସିଷ୍ଟମ୍ ଇଣ୍ଟିଗ୍ରେସନ୍‌ରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ।"
    step1: "CSV ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା ସିଧାସଳଖ CSV ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ। ଟୁଲ୍ ବୁଦ୍ଧିମତ୍ତାର ସହିତ ବିଭିନ୍ନ ବିଭାଜକ (କମା, ଟ୍ୟାବ୍, ସେମିକୋଲନ୍, ପାଇପ୍ ଇତ୍ୟାଦି) ଚିହ୍ନଟ କରେ, ସ୍ୱୟଂଚାଳିତ ଭାବରେ ଡାଟା ପ୍ରକାର ଏବଂ ଏନକୋଡିଂ ଫର୍ମାଟ୍ ଚିହ୍ନଟ କରେ, ବଡ଼ ଫାଇଲ୍ ଏବଂ ଜଟିଳ ଡାଟା ଗଠନର ଦ୍ରୁତ ପାର୍ସିଂକୁ ସମର୍ଥନ କରେ।"
    step3: "କଷ୍ଟମ୍ ବିଭାଜକ, କୋଟ୍ ଶୈଳୀ, ଏନକୋଡିଂ ଫର୍ମାଟ୍ ଏବଂ BOM ମାର୍କ ସେଟିଂସ ସମର୍ଥନ ସହିତ ମାନକ CSV ଫର୍ମାଟ୍ ଫାଇଲ୍ ଜେନେରେଟ୍ କରନ୍ତୁ। ଟାର୍ଗେଟ ସିଷ୍ଟମ୍ ସହିତ ସମ୍ପୂର୍ଣ୍ଣ ସୁସଙ୍ଗତି ନିଶ୍ଚିତ କରେ, ଏଣ୍ଟରପ୍ରାଇଜ୍-ଲେଭଲ୍ ଡାଟା ପ୍ରୋସେସିଂ ଆବଶ୍ୟକତା ପୂରଣ କରିବା ପାଇଁ ଡାଉନଲୋଡ୍ ଏବଂ କମ୍ପ୍ରେସନ୍ ବିକଳ୍ପ ପ୍ରଦାନ କରେ।"
    from_alias: "CSV ଡାଟା ଫାଇଲ୍"
    to_alias: "CSV ମାନକ ଫର୍ମାଟ୍"
  JSON:
    alias: "JSON ଆର୍ରେ"
    what: "JSON (JavaScript Object Notation) ହେଉଛି ଆଧୁନିକ ୱେବ୍ ଆପ୍ଲିକେସନ୍, REST APIs ଏବଂ ମାଇକ୍ରୋସର୍ଭିସ୍ ଆର୍କିଟେକ୍ଚର ପାଇଁ ମାନକ ଟେବୁଲ୍ ଡାଟା ଫର୍ମାଟ୍। ଏହାର ସ୍ପଷ୍ଟ ଗଠନ ଏବଂ ଦକ୍ଷ ପାର୍ସିଂ ଏହାକୁ ଫ୍ରଣ୍ଟ-ଏଣ୍ଡ ଏବଂ ବ୍ୟାକ-ଏଣ୍ଡ ଡାଟା ଇଣ୍ଟରାକସନ୍, କନଫିଗରେସନ୍ ଫାଇଲ୍ ଷ୍ଟୋରେଜ୍ ଏବଂ NoSQL ଡାଟାବେସରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ କରାଏ। ନେଷ୍ଟେଡ୍ ଅବଜେକ୍ଟ, ଆର୍ରେ ଗଠନ ଏବଂ ଏକାଧିକ ଡାଟା ପ୍ରକାରକୁ ସମର୍ଥନ କରେ, ଆଧୁନିକ ସଫ୍ଟୱେୟାର ଡେଭେଲପମେଣ୍ଟ ପାଇଁ ଏହା ଅପରିହାର୍ଯ୍ୟ ଟେବୁଲ୍ ଡାଟା।"
    step1: "JSON ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା JSON ଆର୍ରେ ପେଷ୍ଟ କରନ୍ତୁ। ଅବଜେକ୍ଟ ଆର୍ରେ, ନେଷ୍ଟେଡ୍ ଗଠନ ଏବଂ ଜଟିଳ ଡାଟା ପ୍ରକାରର ସ୍ୱୟଂଚାଳିତ ଚିହ୍ନଟ ଏବଂ ପାର୍ସିଂକୁ ସମର୍ଥନ କରେ। ଟୁଲ୍ ବୁଦ୍ଧିମତ୍ତାର ସହିତ JSON ସିଣ୍ଟାକ୍ସ ଯାଞ୍ଚ କରେ ଏବଂ ତ୍ରୁଟି ପ୍ରମ୍ପ୍ଟ ପ୍ରଦାନ କରେ।"
    step3: "ଏକାଧିକ JSON ଫର୍ମାଟ୍ ଆଉଟପୁଟ୍ ଜେନେରେଟ୍ କରନ୍ତୁ: ମାନକ ଅବଜେକ୍ଟ ଆର୍ରେ, 2D ଆର୍ରେ, ସ୍ତମ୍ଭ ଆର୍ରେ ଏବଂ କି-ଭାଲ୍ୟୁ ପେୟାର ଫର୍ମାଟ୍। ସୁନ୍ଦରୀକୃତ ଆଉଟପୁଟ୍, କମ୍ପ୍ରେସନ୍ ମୋଡ୍, କଷ୍ଟମ୍ ରୁଟ୍ ଅବଜେକ୍ଟ ନାମ ଏବଂ ଇଣ୍ଡେଣ୍ଟେସନ୍ ସେଟିଂସକୁ ସମର୍ଥନ କରେ, ବିଭିନ୍ନ API ଇଣ୍ଟରଫେସ୍ ଏବଂ ଡାଟା ଷ୍ଟୋରେଜ୍ ଆବଶ୍ୟକତା ସହିତ ସମ୍ପୂର୍ଣ୍ଣ ଭାବରେ ଖାପ ଖାଏ।"
    from_alias: "JSON ଆର୍ରେ ଫାଇଲ୍"
    to_alias: "JSON ମାନକ ଫର୍ମାଟ୍"
  JSONLines:
    alias: "JSONLines ଫର୍ମାଟ୍"
    what: "JSON Lines (NDJSON ଭାବରେ ମଧ୍ୟ ଜଣାଶୁଣା) ହେଉଛି ବିଗ୍ ଡାଟା ପ୍ରୋସେସିଂ ଏବଂ ଷ୍ଟ୍ରିମିଂ ଡାଟା ଟ୍ରାନ୍ସମିସନ୍ ପାଇଁ ଏକ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଫର୍ମାଟ୍, ପ୍ରତ୍ୟେକ ଲାଇନରେ ଏକ ସ୍ୱତନ୍ତ୍ର JSON ଅବଜେକ୍ଟ ରହିଥାଏ। ଲଗ୍ ବିଶ୍ଳେଷଣ, ଡାଟା ଷ୍ଟ୍ରିମ୍ ପ୍ରୋସେସିଂ, ମେସିନ୍ ଲର୍ନିଂ ଏବଂ ବିତରଣ ସିଷ୍ଟମରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ। ଇନକ୍ରିମେଣ୍ଟାଲ୍ ପ୍ରୋସେସିଂ ଏବଂ ସମାନ୍ତର ଗଣନାକୁ ସମର୍ଥନ କରେ, ବଡ଼ ଆକାରର ଗଠିତ ଡାଟା ପରିଚାଳନା ପାଇଁ ଏହା ଆଦର୍ଶ ପସନ୍ଦ।"
    step1: "JSONLines ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ। ଟୁଲ୍ ଲାଇନ ଅନୁସାରେ JSON ଅବଜେକ୍ଟ ପାର୍ସ କରେ, ବଡ଼ ଫାଇଲ୍ ଷ୍ଟ୍ରିମିଂ ପ୍ରୋସେସିଂ ଏବଂ ତ୍ରୁଟି ଲାଇନ ସ୍କିପିଂ କାର୍ଯ୍ୟକାରିତାକୁ ସମର୍ଥନ କରେ।"
    step3: "ପ୍ରତ୍ୟେକ ଲାଇନରେ ଏକ ସମ୍ପୂର୍ଣ୍ଣ JSON ଅବଜେକ୍ଟ ଆଉଟପୁଟ୍ କରି ମାନକ JSONLines ଫର୍ମାଟ୍ ଜେନେରେଟ୍ କରନ୍ତୁ। ଷ୍ଟ୍ରିମିଂ ପ୍ରୋସେସିଂ, ବ୍ୟାଚ୍ ଇମ୍ପୋର୍ଟ ଏବଂ ବିଗ୍ ଡାଟା ବିଶ୍ଳେଷଣ ପରିସ୍ଥିତି ପାଇଁ ଉପଯୁକ୍ତ, ଡାଟା ଯାଞ୍ଚ ଏବଂ ଫର୍ମାଟ୍ ଅପ୍ଟିମାଇଜେସନ୍‌କୁ ସମର୍ଥନ କରେ।"
    from_alias: "JSONLines ଡାଟା"
    to_alias: "JSONLines ଷ୍ଟ୍ରିମିଂ ଫର୍ମାଟ୍"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) ହେଉଛି ଏଣ୍ଟରପ୍ରାଇଜ୍-ଲେଭଲ୍ ଡାଟା ଏକ୍ସଚେଞ୍ଜ ଏବଂ କନଫିଗରେସନ୍ ପରିଚାଳନା ପାଇଁ ମାନକ ଫର୍ମାଟ୍, କଠୋର ସିଣ୍ଟାକ୍ସ ନିର୍ଦ୍ଦିଷ୍ଟକରଣ ଏବଂ ଶକ୍ତିଶାଳୀ ଯାଞ୍ଚ ପ୍ରଣାଳୀ ସହିତ। ୱେବ୍ ସର୍ଭିସ, କନଫିଗରେସନ୍ ଫାଇଲ୍, ଡକୁମେଣ୍ଟ ଷ୍ଟୋରେଜ୍ ଏବଂ ସିଷ୍ଟମ୍ ଇଣ୍ଟିଗ୍ରେସନ୍‌ରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ। ନେମସ୍ପେସ, ସ୍କିମା ଯାଞ୍ଚ ଏବଂ XSLT ଟ୍ରାନ୍ସଫର୍ମେସନ୍‌କୁ ସମର୍ଥନ କରେ, ଏଣ୍ଟରପ୍ରାଇଜ୍ ଆପ୍ଲିକେସନ୍ ପାଇଁ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଟେବୁଲ୍ ଡାଟା।"
    step1: "XML ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା XML ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ। ଟୁଲ୍ ସ୍ୱୟଂଚାଳିତ ଭାବରେ XML ଗଠନ ପାର୍ସ କରେ ଏବଂ ଏହାକୁ ଟେବୁଲ୍ ଫର୍ମାଟରେ କନଭର୍ଟ କରେ, ନେମସ୍ପେସ, ଆଟ୍ରିବ୍ୟୁଟ ହ୍ୟାଣ୍ଡଲିଂ ଏବଂ ଜଟିଳ ନେଷ୍ଟେଡ୍ ଗଠନକୁ ସମର୍ଥନ କରେ।"
    step3: "XML ମାନକ ସହିତ ସମ୍ମତ XML ଆଉଟପୁଟ୍ ଜେନେରେଟ୍ କରନ୍ତୁ। କଷ୍ଟମ୍ ରୁଟ୍ ଏଲିମେଣ୍ଟ, ଧାଡ଼ି ଏଲିମେଣ୍ଟ ନାମ, ଆଟ୍ରିବ୍ୟୁଟ ମୋଡ୍, CDATA ରାପିଂ ଏବଂ ଅକ୍ଷର ଏନକୋଡିଂ ସେଟିଂସକୁ ସମର୍ଥନ କରେ। ଡାଟା ଅଖଣ୍ଡତା ଏବଂ ସୁସଙ୍ଗତି ନିଶ୍ଚିତ କରେ, ଏଣ୍ଟରପ୍ରାଇଜ୍-ଲେଭଲ୍ ଆପ୍ଲିକେସନ୍ ଆବଶ୍ୟକତା ପୂରଣ କରେ।"
    from_alias: "XML ଡାଟା ଫାଇଲ୍"
    to_alias: "XML ମାନକ ଫର୍ମାଟ୍"
  YAML:
    alias: "YAML କନଫିଗରେସନ୍"
    what: "YAML ହେଉଛି ଏକ ମାନବ-ବନ୍ଧୁତ୍ୱପୂର୍ଣ୍ଣ ଡାଟା ସିରିଆଲାଇଜେସନ୍ ମାନକ, ଏହାର ସ୍ପଷ୍ଟ ସ୍ତରୀୟ ଗଠନ ଏବଂ ସଂକ୍ଷିପ୍ତ ସିଣ୍ଟାକ୍ସ ପାଇଁ ପ୍ରସିଦ୍ଧ। କନଫିଗରେସନ୍ ଫାଇଲ୍, DevOps ଟୁଲ୍ ଚେନ୍, Docker Compose ଏବଂ Kubernetes ଡିପ୍ଲୋୟମେଣ୍ଟରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ। ଏହାର ଦୃଢ଼ ପଠନୀୟତା ଏବଂ ସଂକ୍ଷିପ୍ତ ସିଣ୍ଟାକ୍ସ ଏହାକୁ ଆଧୁନିକ କ୍ଲାଉଡ୍-ନେଟିଭ ଆପ୍ଲିକେସନ୍ ଏବଂ ସ୍ୱୟଂଚାଳିତ ଅପରେସନ୍ ପାଇଁ ଏକ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ କନଫିଗରେସନ୍ ଫର୍ମାଟ୍ କରିଥାଏ।"
    step1: "YAML ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା YAML ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ। ଟୁଲ୍ ବୁଦ୍ଧିମତ୍ତାର ସହିତ YAML ଗଠନ ପାର୍ସ କରେ ଏବଂ ସିଣ୍ଟାକ୍ସ ସଠିକତା ଯାଞ୍ଚ କରେ, ମଲ୍ଟି-ଡକୁମେଣ୍ଟ ଫର୍ମାଟ୍ ଏବଂ ଜଟିଳ ଡାଟା ପ୍ରକାରକୁ ସମର୍ଥନ କରେ।"
    step3: "ବ୍ଲକ୍ ଏବଂ ଫ୍ଲୋ ଆର୍ରେ ଶୈଳୀ, ଏକାଧିକ କୋଟ୍ ସେଟିଂସ, କଷ୍ଟମ୍ ଇଣ୍ଡେଣ୍ଟେସନ୍ ଏବଂ କମେଣ୍ଟ ସଂରକ୍ଷଣ ସମର୍ଥନ ସହିତ ମାନକ YAML ଫର୍ମାଟ୍ ଆଉଟପୁଟ୍ ଜେନେରେଟ୍ କରନ୍ତୁ। ଆଉଟପୁଟ୍ YAML ଫାଇଲଗୁଡ଼ିକ ବିଭିନ୍ନ ପାର୍ସର ଏବଂ କନଫିଗରେସନ୍ ସିଷ୍ଟମ୍ ସହିତ ସମ୍ପୂର୍ଣ୍ଣ ସୁସଙ୍ଗତ ହେବ ବୋଲି ନିଶ୍ଚିତ କରେ।"
    from_alias: "YAML କନଫିଗରେସନ୍ ଫାଇଲ୍"
    to_alias: "YAML ମାନକ ଫର୍ମାଟ୍"
  MySQL:
      alias: "MySQL କ୍ୱେରୀ ଫଳାଫଳ"
      what: "MySQL ହେଉଛି ବିଶ୍ୱର ସର୍ବାଧିକ ଲୋକପ୍ରିୟ ଓପନ୍-ସୋର୍ସ ରିଲେସନାଲ୍ ଡାଟାବେସ୍ ମ୍ୟାନେଜମେଣ୍ଟ ସିଷ୍ଟମ୍, ଏହାର ଉଚ୍ଚ କାର୍ଯ୍ୟଦକ୍ଷତା, ବିଶ୍ୱସନୀୟତା ଏବଂ ବ୍ୟବହାରର ସହଜତା ପାଇଁ ପ୍ରସିଦ୍ଧ। ୱେବ୍ ଆପ୍ଲିକେସନ୍, ଏଣ୍ଟରପ୍ରାଇଜ୍ ସିଷ୍ଟମ୍ ଏବଂ ଡାଟା ବିଶ୍ଳେଷଣ ପ୍ଲାଟଫର୍ମରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ। MySQL କ୍ୱେରୀ ଫଳାଫଳ ସାଧାରଣତଃ ଗଠିତ ଟେବୁଲ୍ ଡାଟା ଧାରଣ କରେ, ଡାଟାବେସ୍ ପରିଚାଳନା ଏବଂ ଡାଟା ବିଶ୍ଳେଷଣ କାର୍ଯ୍ୟରେ ଏକ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଡାଟା ଉତ୍ସ ଭାବରେ କାର୍ଯ୍ୟ କରେ।"
      step1: "ଡାଟା ସୋର୍ସ ଅଞ୍ଚଳରେ MySQL କ୍ୱେରୀ ଆଉଟପୁଟ୍ ଫଳାଫଳ ପେଷ୍ଟ କରନ୍ତୁ। ଟୁଲ୍ ସ୍ୱୟଂଚାଳିତ ଭାବରେ MySQL କମାଣ୍ଡ-ଲାଇନ୍ ଆଉଟପୁଟ୍ ଫର୍ମାଟ୍ ଚିହ୍ନଟ ଏବଂ ପାର୍ସ କରେ, ବିଭିନ୍ନ କ୍ୱେରୀ ଫଳାଫଳ ଶୈଳୀ ଏବଂ ଅକ୍ଷର ଏନକୋଡିଂକୁ ସମର୍ଥନ କରେ, ବୁଦ୍ଧିମତ୍ତାର ସହିତ ହେଡର୍ ଏବଂ ଡାଟା ଧାଡ଼ି ପରିଚାଳନା କରେ।"
      step3: "MySQL କ୍ୱେରୀ ଫଳାଫଳକୁ ଦ୍ରୁତ ଭାବରେ ଏକାଧିକ ଟେବୁଲ୍ ଡାଟା ଫର୍ମାଟରେ କନଭର୍ଟ କରନ୍ତୁ, ଡାଟା ବିଶ୍ଳେଷଣ, ରିପୋର୍ଟ ଜେନେରେସନ୍, କ୍ରସ୍-ସିଷ୍ଟମ୍ ଡାଟା ମାଇଗ୍ରେସନ୍ ଏବଂ ଡାଟା ଯାଞ୍ଚକୁ ସହଜ କରେ। ଡାଟାବେସ୍ ଆଡମିନିଷ୍ଟ୍ରେଟର ଏବଂ ଡାଟା ଆନାଲିଷ୍ଟମାନଙ୍କ ପାଇଁ ଏକ ବ୍ୟବହାରିକ ଟୁଲ୍।"
      from_alias: "MySQL କ୍ୱେରୀ ଆଉଟପୁଟ୍"
      to_alias: "MySQL ଟେବୁଲ୍ ଡାଟା"
  SQL:
    alias: "SQL ଇନସର୍ଟ"
    what: "SQL (Structured Query Language) ହେଉଛି ରିଲେସନାଲ୍ ଡାଟାବେସ୍ ପାଇଁ ମାନକ ଅପରେସନ୍ ଭାଷା, ଡାଟା କ୍ୱେରୀ, ଇନସର୍ଟ, ଅପଡେଟ୍ ଏବଂ ଡିଲିଟ୍ ଅପରେସନ୍ ପାଇଁ ବ୍ୟବହୃତ। ଡାଟାବେସ୍ ପରିଚାଳନାର ମୂଳ ପ୍ରଯୁକ୍ତି ଭାବରେ, SQL ଡାଟା ବିଶ୍ଳେଷଣ, ବ୍ୟବସାୟିକ ବୁଦ୍ଧିମତ୍ତା, ETL ପ୍ରୋସେସିଂ ଏବଂ ଡାଟା ୱେୟାରହାଉସ ନିର୍ମାଣରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ। ଏହା ଡାଟା ପେସାଦାରମାନଙ୍କ ପାଇଁ ଏକ ଜରୁରୀ କୌଶଳ ଟୁଲ୍।"
    step1: "INSERT SQL ଷ୍ଟେଟମେଣ୍ଟ ପେଷ୍ଟ କରନ୍ତୁ କିମ୍ବା .sql ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ। ଟୁଲ୍ ବୁଦ୍ଧିମତ୍ତାର ସହିତ SQL ସିଣ୍ଟାକ୍ସ ପାର୍ସ କରେ ଏବଂ ଟେବୁଲ୍ ଡାଟା ଏକ୍ସଟ୍ରାକ୍ଟ କରେ, ଏକାଧିକ SQL ଉପଭାଷା ଏବଂ ଜଟିଳ କ୍ୱେରୀ ଷ୍ଟେଟମେଣ୍ଟ ପ୍ରୋସେସିଂକୁ ସମର୍ଥନ କରେ।"
    step3: "ମାନକ SQL INSERT ଷ୍ଟେଟମେଣ୍ଟ ଏବଂ ଟେବୁଲ୍ ସୃଷ୍ଟି ଷ୍ଟେଟମେଣ୍ଟ ଜେନେରେଟ୍ କରନ୍ତୁ। ଏକାଧିକ ଡାଟାବେସ୍ ଉପଭାଷା (MySQL, PostgreSQL, SQLite, SQL Server, Oracle) କୁ ସମର୍ଥନ କରେ, ସ୍ୱୟଂଚାଳିତ ଭାବରେ ଡାଟା ପ୍ରକାର ମ୍ୟାପିଂ, ଅକ୍ଷର ଏସ୍କେପିଂ ଏବଂ ପ୍ରାଥମିକ କି ପ୍ରତିବନ୍ଧକ ପରିଚାଳନା କରେ। ଜେନେରେଟ୍ ହୋଇଥିବା SQL କୋଡ୍ ସିଧାସଳଖ ଏକ୍ଜିକ୍ୟୁଟ୍ ହୋଇପାରିବ ବୋଲି ନିଶ୍ଚିତ କରେ।"
    from_alias: "SQL ଡାଟା ଫାଇଲ୍"
    to_alias: "SQL ମାନକ ଷ୍ଟେଟମେଣ୍ଟ"
  Qlik:
      alias: "Qlik ଟେବୁଲ୍"
      what: "Qlik ହେଉଛି ଡାଟା ଭିଜୁଆଲାଇଜେସନ୍, ଏକ୍ଜିକ୍ୟୁଟିଭ ଡ୍ୟାସବୋର୍ଡ ଏବଂ ସେଲ୍ଫ-ସର୍ଭିସ ବ୍ୟବସାୟିକ ବୁଦ୍ଧିମତ୍ତା ଉତ୍ପାଦରେ ବିଶେଷଜ୍ଞ ଏକ ସଫ୍ଟୱେୟାର ବିକ୍ରେତା, Tableau ଏବଂ Microsoft ସହିତ।"
      step1: ""
      step3: "ଶେଷରେ, [ଟେବୁଲ୍ ଜେନେରେଟର](#TableGenerator) କନଭର୍ସନ୍ ଫଳାଫଳ ଦେଖାଏ। ଆପଣଙ୍କ Qlik Sense, Qlik AutoML, QlikView କିମ୍ବା ଅନ୍ୟାନ୍ୟ Qlik-ସକ୍ଷମ ସଫ୍ଟୱେୟାରରେ ବ୍ୟବହାର କରନ୍ତୁ।"
      from_alias: "Qlik ଟେବୁଲ୍"
      to_alias: "Qlik ଟେବୁଲ୍"
  DAX:
      alias: "DAX ଟେବୁଲ୍"
      what: "DAX (Data Analysis Expressions) ହେଉଛି Microsoft Power BI ରେ ଗଣିତ ସ୍ତମ୍ଭ, ମାପ ଏବଂ କଷ୍ଟମ୍ ଟେବୁଲ୍ ସୃଷ୍ଟି ପାଇଁ ବ୍ୟବହୃତ ଏକ ପ୍ରୋଗ୍ରାମିଂ ଭାଷା।"
      step1: ""
      step3: "ଶେଷରେ, [ଟେବୁଲ୍ ଜେନେରେଟର](#TableGenerator) କନଭର୍ସନ୍ ଫଳାଫଳ ଦେଖାଏ। ଆଶା କରାଯାଇଥିବା ପରି, ଏହା Microsoft Power BI, Microsoft Analysis Services ଏବଂ Excel ପାଇଁ Microsoft Power Pivot ସହିତ ଅନେକ Microsoft ଉତ୍ପାଦରେ ବ୍ୟବହୃତ ହୁଏ।"
      from_alias: "DAX ଟେବୁଲ୍"
      to_alias: "DAX ଟେବୁଲ୍"
  Firebase:
    alias: "Firebase ତାଲିକା"
    what: "Firebase ହେଉଛି ଏକ BaaS ଆପ୍ଲିକେସନ୍ ଡେଭେଲପମେଣ୍ଟ ପ୍ଲାଟଫର୍ମ ଯାହା ରିଅଲ୍-ଟାଇମ୍ ଡାଟାବେସ୍, କ୍ଲାଉଡ୍ ଷ୍ଟୋରେଜ୍, ପ୍ରାମାଣିକରଣ, କ୍ରାସ୍ ରିପୋର୍ଟିଂ ଇତ୍ୟାଦି ପରି ହୋଷ୍ଟେଡ୍ ବ୍ୟାକଏଣ୍ଡ ସେବା ପ୍ରଦାନ କରେ।"
    step1: ""
    step3: "ଶେଷରେ, [ଟେବୁଲ୍ ଜେନେରେଟର](#TableGenerator) କନଭର୍ସନ୍ ଫଳାଫଳ ଦେଖାଏ। ତାପରେ ଆପଣ Firebase ଡାଟାବେସରେ ଡାଟା ତାଲିକାରେ ଯୋଗ କରିବା ପାଇଁ Firebase API ରେ push ପଦ୍ଧତି ବ୍ୟବହାର କରିପାରିବେ।"
    from_alias: "Firebase ତାଲିକା"
    to_alias: "Firebase ତାଲିକା"
  HTML:
    alias: "HTML ଟେବୁଲ୍"
    what: "HTML ଟେବୁଲଗୁଡ଼ିକ ହେଉଛି ୱେବ୍ ପେଜରେ ସଂରଚିତ ଡାଟା ପ୍ରଦର୍ଶନର ମାନକ ଉପାୟ, table, tr, td ଏବଂ ଅନ୍ୟାନ୍ୟ ଟ୍ୟାଗ୍ ସହିତ ନିର୍ମିତ। ସମୃଦ୍ଧ ଶୈଳୀ କଷ୍ଟମାଇଜେସନ୍, ରିସ୍ପନ୍ସିଭ୍ ଲେଆଉଟ୍ ଏବଂ ଇଣ୍ଟରାକ୍ଟିଭ୍ କାର୍ଯ୍ୟକାରିତାକୁ ସମର୍ଥନ କରେ। ୱେବସାଇଟ୍ ଡେଭେଲପମେଣ୍ଟ, ଡାଟା ପ୍ରଦର୍ଶନ ଏବଂ ରିପୋର୍ଟ ଜେନେରେସନ୍‌ରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ, ଫ୍ରଣ୍ଟ-ଏଣ୍ଡ ଡେଭେଲପମେଣ୍ଟ ଏବଂ ୱେବ୍ ଡିଜାଇନର ଏକ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଉପାଦାନ ଭାବରେ କାର୍ଯ୍ୟ କରେ।"
    step1: "ଟେବୁଲ୍ ଧାରଣ କରିଥିବା HTML କୋଡ୍ ପେଷ୍ଟ କରନ୍ତୁ କିମ୍ବା HTML ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ। ଟୁଲ୍ ସ୍ୱୟଂଚାଳିତ ଭାବରେ ପେଜରୁ ଟେବୁଲ୍ ଡାଟା ଚିହ୍ନଟ ଏବଂ ଏକ୍ସଟ୍ରାକ୍ଟ କରେ, ଜଟିଳ HTML ଗଠନ, CSS ଶୈଳୀ ଏବଂ ନେଷ୍ଟେଡ୍ ଟେବୁଲ୍ ପ୍ରୋସେସିଂକୁ ସମର୍ଥନ କରେ।"
    step3: "thead/tbody ଗଠନ, CSS କ୍ଲାସ୍ ସେଟିଂସ, ଟେବୁଲ୍ କ୍ୟାପସନ୍, ଧାଡ଼ି/ସ୍ତମ୍ଭ ହେଡର ଏବଂ ରିସ୍ପନ୍ସିଭ୍ ଆଟ୍ରିବ୍ୟୁଟ ବିନ୍ୟାସ ସମର୍ଥନ ସହିତ ସେମାଣ୍ଟିକ୍ HTML ଟେବୁଲ୍ କୋଡ୍ ଜେନେରେଟ୍ କରନ୍ତୁ। ଜେନେରେଟ୍ ହୋଇଥିବା ଟେବୁଲ୍ କୋଡ୍ ଭଲ ଆକ୍ସେସିବିଲିଟି ଏବଂ SEO ବନ୍ଧୁତ୍ୱ ସହିତ ୱେବ୍ ମାନଦଣ୍ଡ ପୂରଣ କରିବା ନିଶ୍ଚିତ କରେ।"
    from_alias: "HTML ୱେବ୍ ଟେବୁଲ୍"
    to_alias: "HTML ମାନକ ଟେବୁଲ୍"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel ହେଉଛି ବିଶ୍ୱର ସର୍ବାଧିକ ଲୋକପ୍ରିୟ ସ୍ପ୍ରେଡସିଟ୍ ସଫ୍ଟୱେୟାର, ବ୍ୟବସାୟିକ ବିଶ୍ଳେଷଣ, ଆର୍ଥିକ ପରିଚାଳନା, ଡାଟା ପ୍ରୋସେସିଂ ଏବଂ ରିପୋର୍ଟ ସୃଷ୍ଟିରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ। ଏହାର ଶକ୍ତିଶାଳୀ ଡାଟା ପ୍ରୋସେସିଂ କ୍ଷମତା, ସମୃଦ୍ଧ ଫଙ୍କସନ୍ ଲାଇବ୍ରେରୀ ଏବଂ ନମନୀୟ ଭିଜୁଆଲାଇଜେସନ୍ ବୈଶିଷ୍ଟ୍ୟ ଏହାକୁ ଅଫିସ୍ ଅଟୋମେସନ୍ ଏବଂ ଡାଟା ବିଶ୍ଳେଷଣ ପାଇଁ ମାନକ ଟୁଲ୍ କରିଥାଏ, ପ୍ରାୟ ସମସ୍ତ ଶିଳ୍ପ ଏବଂ କ୍ଷେତ୍ରରେ ବ୍ୟାପକ ପ୍ରୟୋଗ ସହିତ।"
    step1: "Excel ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ (.xlsx, .xls ଫର୍ମାଟକୁ ସମର୍ଥନ କରେ) କିମ୍ବା Excel ରୁ ସିଧାସଳଖ ଟେବୁଲ୍ ଡାଟା କପି କରି ପେଷ୍ଟ କରନ୍ତୁ। ଟୁଲ୍ ମଲ୍ଟି-ୱର୍କସିଟ୍ ପ୍ରୋସେସିଂ, ଜଟିଳ ଫର୍ମାଟ୍ ଚିହ୍ନଟ ଏବଂ ବଡ଼ ଫାଇଲଗୁଡ଼ିକର ଦ୍ରୁତ ପାର୍ସିଂକୁ ସମର୍ଥନ କରେ, ସ୍ୱୟଂଚାଳିତ ଭାବରେ ମର୍ଜ ହୋଇଥିବା ସେଲ୍ ଏବଂ ଡାଟା ପ୍ରକାର ପରିଚାଳନା କରେ।"
    step3: "Excel-ସୁସଙ୍ଗତ ଟେବୁଲ୍ ଡାଟା ଜେନେରେଟ୍ କରନ୍ତୁ ଯାହା ସିଧାସଳଖ Excel ରେ ପେଷ୍ଟ କରାଯାଇପାରିବ କିମ୍ବା ମାନକ .xlsx ଫାଇଲ୍ ଭାବରେ ଡାଉନଲୋଡ୍ କରାଯାଇପାରିବ। ୱର୍କସିଟ୍ ନାମକରଣ, ସେଲ୍ ଫର୍ମାଟିଂ, ସ୍ୱୟଂ ସ୍ତମ୍ଭ ଓସାର, ହେଡର ଶୈଳୀ ଏବଂ ଡାଟା ଯାଞ୍ଚ ସେଟିଂସକୁ ସମର୍ଥନ କରେ। ଆଉଟପୁଟ୍ Excel ଫାଇଲଗୁଡ଼ିକର ବୃତ୍ତିଗତ ଦୃଶ୍ୟ ଏବଂ ସମ୍ପୂର୍ଣ୍ଣ କାର୍ଯ୍ୟକାରିତା ଥିବା ନିଶ୍ଚିତ କରେ।"
    from_alias: "Excel ସ୍ପ୍ରେଡସିଟ୍"
    to_alias: "Excel ମାନକ ଫର୍ମାଟ୍"
  LaTeX:
    alias: "LaTeX ଟେବୁଲ୍"
    what: "LaTeX ହେଉଛି ଏକ ବୃତ୍ତିଗତ ଡକୁମେଣ୍ଟ ଟାଇପସେଟିଂ ସିଷ୍ଟମ୍, ବିଶେଷକରି ଏକାଡେମିକ୍ ପେପର, ଟେକ୍ନିକାଲ୍ ଡକୁମେଣ୍ଟ ଏବଂ ବୈଜ୍ଞାନିକ ପ୍ରକାଶନ ସୃଷ୍ଟି ପାଇଁ ଉପଯୁକ୍ତ। ଏହାର ଟେବୁଲ୍ କାର୍ଯ୍ୟକାରିତା ଶକ୍ତିଶାଳୀ, ଜଟିଳ ଗାଣିତିକ ସୂତ୍ର, ସଠିକ ଲେଆଉଟ୍ ନିୟନ୍ତ୍ରଣ ଏବଂ ଉଚ୍ଚ-ଗୁଣବତ୍ତା PDF ଆଉଟପୁଟ୍‌କୁ ସମର୍ଥନ କରେ। ଏହା ଏକାଡେମିଆ ଏବଂ ବୈଜ୍ଞାନିକ ପ୍ରକାଶନରେ ମାନକ ଟୁଲ୍, ଜର୍ନାଲ୍ ପେପର, ଡିସର୍ଟେସନ୍ ଏବଂ ଟେକ୍ନିକାଲ୍ ମାନୁଆଲ୍ ଟାଇପସେଟିଂରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ।"
    step1: "LaTeX ଟେବୁଲ୍ କୋଡ୍ ପେଷ୍ଟ କରନ୍ତୁ କିମ୍ବା .tex ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ। ଟୁଲ୍ LaTeX ଟେବୁଲ୍ ସିଣ୍ଟାକ୍ସ ପାର୍ସ କରେ ଏବଂ ଡାଟା ବିଷୟବସ୍ତୁ ଏକ୍ସଟ୍ରାକ୍ଟ କରେ, ଏକାଧିକ ଟେବୁଲ୍ ପରିବେଶ (tabular, longtable, array, ଇତ୍ୟାଦି) ଏବଂ ଜଟିଳ ଫର୍ମାଟ୍ କମାଣ୍ଡକୁ ସମର୍ଥନ କରେ।"
    step3: "ଏକାଧିକ ଟେବୁଲ୍ ପରିବେଶ ଚୟନ, ବର୍ଡର ଶୈଳୀ ବିନ୍ୟାସ, କ୍ୟାପସନ୍ ସ୍ଥିତି ସେଟିଂସ, ଡକୁମେଣ୍ଟ କ୍ଲାସ୍ ନିର୍ଦ୍ଦିଷ୍ଟକରଣ ଏବଂ ପ୍ୟାକେଜ୍ ପରିଚାଳନା ସମର୍ଥନ ସହିତ ବୃତ୍ତିଗତ LaTeX ଟେବୁଲ୍ କୋଡ୍ ଜେନେରେଟ୍ କରନ୍ତୁ। ସମ୍ପୂର୍ଣ୍ଣ କମ୍ପାଇଲେବଲ୍ LaTeX ଡକୁମେଣ୍ଟ ଜେନେରେଟ୍ କରିପାରେ, ଆଉଟପୁଟ୍ ଟେବୁଲଗୁଡ଼ିକ ଏକାଡେମିକ୍ ପ୍ରକାଶନ ମାନଦଣ୍ଡ ପୂରଣ କରିବା ନିଶ୍ଚିତ କରେ।"
    from_alias: "LaTeX ଡକୁମେଣ୍ଟ ଟେବୁଲ୍"
    to_alias: "LaTeX ବୃତ୍ତିଗତ ଫର୍ମାଟ୍"
  ASCII:
    alias: "ASCII ଟେବୁଲ୍"
    what: "ASCII ଟେବୁଲଗୁଡ଼ିକ ଟେବୁଲ୍ ବର୍ଡର ଏବଂ ଗଠନ ଅଙ୍କନ ପାଇଁ ସାଧା ଟେକ୍ସଟ୍ ଅକ୍ଷର ବ୍ୟବହାର କରେ, ସର୍ବୋତ୍ତମ ସୁସଙ୍ଗତି ଏବଂ ପୋର୍ଟେବିଲିଟି ପ୍ରଦାନ କରେ। ସମସ୍ତ ଟେକ୍ସଟ୍ ଏଡିଟର, ଟର୍ମିନାଲ୍ ପରିବେଶ ଏବଂ ଅପରେଟିଂ ସିଷ୍ଟମ୍ ସହିତ ସୁସଙ୍ଗତ। କୋଡ୍ ଡକୁମେଣ୍ଟେସନ୍, ଟେକ୍ନିକାଲ୍ ମାନୁଆଲ୍, README ଫାଇଲ୍ ଏବଂ କମାଣ୍ଡ-ଲାଇନ୍ ଟୁଲ୍ ଆଉଟପୁଟ୍‌ରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ। ପ୍ରୋଗ୍ରାମର ଏବଂ ସିଷ୍ଟମ୍ ଆଡମିନିଷ୍ଟ୍ରେଟରଙ୍କ ପାଇଁ ପସନ୍ଦର ଡାଟା ପ୍ରଦର୍ଶନ ଫର୍ମାଟ୍।"
    step1: "ASCII ଟେବୁଲ୍ ଧାରଣ କରିଥିବା ଟେକ୍ସଟ୍ ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା ସିଧାସଳଖ ଟେବୁଲ୍ ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ। ଟୁଲ୍ ବୁଦ୍ଧିମତ୍ତାର ସହିତ ASCII ଟେବୁଲ୍ ଗଠନ ଚିହ୍ନଟ ଏବଂ ପାର୍ସ କରେ, ଏକାଧିକ ବର୍ଡର ଶୈଳୀ ଏବଂ ସଜ୍ଜା ଫର୍ମାଟକୁ ସମର୍ଥନ କରେ।"
    step3: "ଏକାଧିକ ବର୍ଡର ଶୈଳୀ (ଏକକ ଲାଇନ, ଦ୍ୱିତୀୟ ଲାଇନ, ଗୋଲାକାର କୋଣ, ଇତ୍ୟାଦି), ଟେକ୍ସଟ୍ ସଜ୍ଜା ପଦ୍ଧତି ଏବଂ ସ୍ୱୟଂ ସ୍ତମ୍ଭ ଓସାର ସମର୍ଥନ ସହିତ ସୁନ୍ଦର ସାଧା ଟେକ୍ସଟ୍ ASCII ଟେବୁଲ୍ ଜେନେରେଟ୍ କରନ୍ତୁ। ଜେନେରେଟ୍ ହୋଇଥିବା ଟେବୁଲଗୁଡ଼ିକ କୋଡ୍ ଏଡିଟର, ଡକୁମେଣ୍ଟ ଏବଂ କମାଣ୍ଡ ଲାଇନରେ ସମ୍ପୂର୍ଣ୍ଣ ଭାବରେ ପ୍ରଦର୍ଶିତ ହୁଏ।"
    from_alias: "ASCII ଟେକ୍ସଟ୍ ଟେବୁଲ୍"
    to_alias: "ASCII ମାନକ ଫର୍ମାଟ୍"
  MediaWiki:
    alias: "MediaWiki ଟେବୁଲ୍"
    what: "MediaWiki ହେଉଛି Wikipedia ପରି ପ୍ରସିଦ୍ଧ ଉଇକି ସାଇଟଗୁଡ଼ିକ ଦ୍ୱାରା ବ୍ୟବହୃତ ଓପନ୍-ସୋର୍ସ ସଫ୍ଟୱେୟାର ପ୍ଲାଟଫର୍ମ। ଏହାର ଟେବୁଲ୍ ସିଣ୍ଟାକ୍ସ ସଂକ୍ଷିପ୍ତ ତଥାପି ଶକ୍ତିଶାଳୀ, ଟେବୁଲ୍ ଶୈଳୀ କଷ୍ଟମାଇଜେସନ୍, ସର୍ଟିଂ କାର୍ଯ୍ୟକାରିତା ଏବଂ ଲିଙ୍କ୍ ଏମ୍ବେଡିଂକୁ ସମର୍ଥନ କରେ। ଜ୍ଞାନ ପରିଚାଳନା, ସହଯୋଗିତା ସମ୍ପାଦନା ଏବଂ ବିଷୟବସ୍ତୁ ପରିଚାଳନା ସିଷ୍ଟମରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ, ଉଇକି ଏନସାଇକ୍ଲୋପିଡିଆ ଏବଂ ଜ୍ଞାନ ଭିତ୍ତି ନିର୍ମାଣ ପାଇଁ ମୂଳ ପ୍ରଯୁକ୍ତି ଭାବରେ କାର୍ଯ୍ୟ କରେ।"
    step1: "MediaWiki ଟେବୁଲ୍ କୋଡ୍ ପେଷ୍ଟ କରନ୍ତୁ କିମ୍ବା ଉଇକି ସୋର୍ସ ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ। ଟୁଲ୍ ଉଇକି ମାର୍କଅପ୍ ସିଣ୍ଟାକ୍ସ ପାର୍ସ କରେ ଏବଂ ଟେବୁଲ୍ ଡାଟା ଏକ୍ସଟ୍ରାକ୍ଟ କରେ, ଜଟିଳ ଉଇକି ସିଣ୍ଟାକ୍ସ ଏବଂ ଟେମ୍ପଲେଟ୍ ପ୍ରୋସେସିଂକୁ ସମର୍ଥନ କରେ।"
    step3: "ହେଡର ଶୈଳୀ ସେଟିଂସ, ସେଲ୍ ସଜ୍ଜା, ସର୍ଟିଂ କାର୍ଯ୍ୟକାରିତା ସକ୍ଷମକରଣ ଏବଂ କୋଡ୍ କମ୍ପ୍ରେସନ୍ ବିକଳ୍ପ ସମର୍ଥନ ସହିତ ମାନକ MediaWiki ଟେବୁଲ୍ କୋଡ୍ ଜେନେରେଟ୍ କରନ୍ତୁ। ଜେନେରେଟ୍ ହୋଇଥିବା କୋଡ୍ ସିଧାସଳଖ ଉଇକି ପେଜ୍ ସମ୍ପାଦନା ପାଇଁ ବ୍ୟବହାର କରାଯାଇପାରିବ, MediaWiki ପ୍ଲାଟଫର୍ମରେ ସମ୍ପୂର୍ଣ୍ଣ ପ୍ରଦର୍ଶନ ନିଶ୍ଚିତ କରେ।"
    from_alias: "MediaWiki ସୋର୍ସ କୋଡ୍"
    to_alias: "MediaWiki ଟେବୁଲ୍ ସିଣ୍ଟାକ୍ସ"
  TracWiki:
    alias: "TracWiki ଟେବୁଲ୍"
    what: "Trac ହେଉଛି ଏକ ୱେବ୍-ଆଧାରିତ ପ୍ରୋଜେକ୍ଟ ପରିଚାଳନା ଏବଂ ବଗ୍ ଟ୍ରାକିଂ ସିଷ୍ଟମ୍ ଯାହା ଟେବୁଲ୍ ବିଷୟବସ୍ତୁ ସୃଷ୍ଟି କରିବା ପାଇଁ ସରଳୀକୃତ ଉଇକି ସିଣ୍ଟାକ୍ସ ବ୍ୟବହାର କରେ।"
    step1: "TracWiki ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା ଟେବୁଲ୍ ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ।"
    step3: "ଧାଡ଼ି/ସ୍ତମ୍ଭ ହେଡର ସେଟିଂସ ସମର୍ଥନ ସହିତ TracWiki-ସୁସଙ୍ଗତ ଟେବୁଲ୍ କୋଡ୍ ଜେନେରେଟ୍ କରନ୍ତୁ, ପ୍ରୋଜେକ୍ଟ ଡକୁମେଣ୍ଟ ପରିଚାଳନାକୁ ସହଜ କରେ।"
    from_alias: "TracWiki ଟେବୁଲ୍"
    to_alias: "TracWiki ଫର୍ମାଟ୍"
  AsciiDoc:
    alias: "AsciiDoc ଟେବୁଲ୍"
    what: "AsciiDoc ହେଉଛି ଏକ ହାଲୁକା ମାର୍କଅପ୍ ଭାଷା ଯାହା HTML, PDF, ମାନୁଆଲ୍ ପେଜ୍ ଏବଂ ଅନ୍ୟାନ୍ୟ ଫର୍ମାଟରେ କନଭର୍ଟ କରାଯାଇପାରିବ, ଟେକ୍ନିକାଲ୍ ଡକୁମେଣ୍ଟେସନ୍ ଲେଖା ପାଇଁ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ।"
    step1: "AsciiDoc ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ।"
    step3: "ହେଡର, ଫୁଟର ଏବଂ ଟାଇଟଲ୍ ସେଟିଂସ ସମର୍ଥନ ସହିତ AsciiDoc ଟେବୁଲ୍ ସିଣ୍ଟାକ୍ସ ଜେନେରେଟ୍ କରନ୍ତୁ, AsciiDoc ଏଡିଟରରେ ସିଧାସଳଖ ବ୍ୟବହାର ଯୋଗ୍ୟ।"
    from_alias: "AsciiDoc ଟେବୁଲ୍"
    to_alias: "AsciiDoc ଫର୍ମାଟ୍"
  reStructuredText:
    alias: "reStructuredText ଟେବୁଲ୍"
    what: "reStructuredText ହେଉଛି Python ସମୁଦାୟ ପାଇଁ ମାନକ ଡକୁମେଣ୍ଟେସନ୍ ଫର୍ମାଟ୍, ସମୃଦ୍ଧ ଟେବୁଲ୍ ସିଣ୍ଟାକ୍ସକୁ ସମର୍ଥନ କରେ, Sphinx ଡକୁମେଣ୍ଟେସନ୍ ଜେନେରେସନ୍ ପାଇଁ ସାଧାରଣତ ବ୍ୟବହୃତ।"
    step1: ".rst ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା reStructuredText ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ।"
    step3: "ଏକାଧିକ ବର୍ଡର ଶୈଳୀ ସମର୍ଥନ ସହିତ ମାନକ reStructuredText ଟେବୁଲ୍ ଜେନେରେଟ୍ କରନ୍ତୁ, Sphinx ଡକୁମେଣ୍ଟେସନ୍ ପ୍ରୋଜେକ୍ଟରେ ସିଧାସଳଖ ବ୍ୟବହାର ଯୋଗ୍ୟ।"
    from_alias: "reStructuredText ଟେବୁଲ୍"
    to_alias: "reStructuredText ଫର୍ମାଟ୍"
  PHP:
    alias: "PHP ଆର୍ରେ"
    what: "PHP ହେଉଛି ଏକ ଲୋକପ୍ରିୟ ସର୍ଭର-ସାଇଡ୍ ସ୍କ୍ରିପ୍ଟିଂ ଭାଷା, ଆର୍ରେ ହେଉଛି ଏହାର ମୂଳ ଡାଟା ଗଠନ, ୱେବ୍ ଡେଭେଲପମେଣ୍ଟ ଏବଂ ଡାଟା ପ୍ରୋସେସିଂରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ।"
    step1: "PHP ଆର୍ରେ ଧାରଣ କରିଥିବା ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା ସିଧାସଳଖ ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ।"
    step3: "ମାନକ PHP ଆର୍ରେ କୋଡ୍ ଜେନେରେଟ୍ କରନ୍ତୁ ଯାହା PHP ପ୍ରୋଜେକ୍ଟରେ ସିଧାସଳଖ ବ୍ୟବହାର କରାଯାଇପାରିବ, ଆସୋସିଏଟିଭ୍ ଏବଂ ଇଣ୍ଡେକ୍ସଡ୍ ଆର୍ରେ ଫର୍ମାଟକୁ ସମର୍ଥନ କରେ।"
    from_alias: "PHP ଆର୍ରେ"
    to_alias: "PHP କୋଡ୍"
  Ruby:
    alias: "Ruby ଆର୍ରେ"
    what: "Ruby ହେଉଛି ଏକ ଗତିଶୀଳ ଅବଜେକ୍ଟ-ଓରିଏଣ୍ଟେଡ୍ ପ୍ରୋଗ୍ରାମିଂ ଭାଷା ଯାହାର ସଂକ୍ଷିପ୍ତ ଏବଂ ସୁନ୍ଦର ସିଣ୍ଟାକ୍ସ ଅଛି, ଆର୍ରେ ହେଉଛି ଏକ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଡାଟା ଗଠନ।"
    step1: "Ruby ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା ଆର୍ରେ ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ।"
    step3: "Ruby ସିଣ୍ଟାକ୍ସ ନିର୍ଦ୍ଦିଷ୍ଟକରଣ ସହିତ ସମ୍ମତ Ruby ଆର୍ରେ କୋଡ୍ ଜେନେରେଟ୍ କରନ୍ତୁ, Ruby ପ୍ରୋଜେକ୍ଟରେ ସିଧାସଳଖ ବ୍ୟବହାର ଯୋଗ୍ୟ।"
    from_alias: "Ruby ଆର୍ରେ"
    to_alias: "Ruby କୋଡ୍"
  ASP:
    alias: "ASP ଆର୍ରେ"
    what: "ASP (Active Server Pages) ହେଉଛି Microsoft ର ସର୍ଭର-ସାଇଡ୍ ସ୍କ୍ରିପ୍ଟିଂ ପରିବେଶ, ଗତିଶୀଳ ୱେବ୍ ପେଜ୍ ବିକାଶ ପାଇଁ ଏକାଧିକ ପ୍ରୋଗ୍ରାମିଂ ଭାଷାକୁ ସମର୍ଥନ କରେ।"
    step1: "ASP ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା ଆର୍ରେ ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ।"
    step3: "VBScript ଏବଂ JScript ସିଣ୍ଟାକ୍ସ ସମର୍ଥନ ସହିତ ASP-ସୁସଙ୍ଗତ ଆର୍ରେ କୋଡ୍ ଜେନେରେଟ୍ କରନ୍ତୁ, ASP.NET ପ୍ରୋଜେକ୍ଟରେ ବ୍ୟବହାର ଯୋଗ୍ୟ।"
    from_alias: "ASP ଆର୍ରେ"
    to_alias: "ASP କୋଡ୍"
  ActionScript:
    alias: "ActionScript ଆର୍ରେ"
    what: "ActionScript ହେଉଛି ଏକ ଅବଜେକ୍ଟ-ଓରିଏଣ୍ଟେଡ୍ ପ୍ରୋଗ୍ରାମିଂ ଭାଷା ଯାହା ମୁଖ୍ୟତ Adobe Flash ଏବଂ AIR ଆପ୍ଲିକେସନ୍ ଡେଭେଲପମେଣ୍ଟ ପାଇଁ ବ୍ୟବହୃତ।"
    step1: ".as ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା ActionScript ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ।"
    step3: "AS3 ସିଣ୍ଟାକ୍ସ ମାନଦଣ୍ଡ ସହିତ ସମ୍ମତ ActionScript ଆର୍ରେ କୋଡ୍ ଜେନେରେଟ୍ କରନ୍ତୁ, Flash ଏବଂ Flex ପ୍ରୋଜେକ୍ଟ ଡେଭେଲପମେଣ୍ଟ ପାଇଁ ବ୍ୟବହାର ଯୋଗ୍ୟ।"
    from_alias: "ActionScript ଆର୍ରେ"
    to_alias: "ActionScript କୋଡ୍"
  BBCode:
    alias: "BBCode ଟେବୁଲ୍"
    what: "BBCode ହେଉଛି ଏକ ହାଲୁକା ମାର୍କଅପ୍ ଭାଷା ଯାହା ଫୋରମ୍ ଏବଂ ଅନଲାଇନ୍ ସମୁଦାୟରେ ସାଧାରଣତ ବ୍ୟବହୃତ, ଟେବୁଲ୍ ସମର୍ଥନ ସହିତ ସରଳ ଫର୍ମାଟିଂ କାର୍ଯ୍ୟକାରିତା ପ୍ରଦାନ କରେ।"
    step1: "BBCode ଧାରଣ କରିଥିବା ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ।"
    step3: "ଫୋରମ୍ ପୋଷ୍ଟିଂ ଏବଂ ସମୁଦାୟ ବିଷୟବସ୍ତୁ ସୃଷ୍ଟି ପାଇଁ ଉପଯୁକ୍ତ BBCode ଟେବୁଲ୍ କୋଡ୍ ଜେନେରେଟ୍ କରନ୍ତୁ, କମ୍ପ୍ରେସଡ୍ ଆଉଟପୁଟ୍ ଫର୍ମାଟ୍ ସମର୍ଥନ ସହିତ।"
    from_alias: "BBCode ଟେବୁଲ୍"
    to_alias: "BBCode ଫର୍ମାଟ୍"
  PDF:
    alias: "PDF ଟେବୁଲ୍"
    what: "PDF (Portable Document Format) ହେଉଛି ସ୍ଥିର ଲେଆଉଟ୍, ସ୍ଥିର ପ୍ରଦର୍ଶନ ଏବଂ ଉଚ୍ଚ-ଗୁଣବତ୍ତା ମୁଦ୍ରଣ ବିଶେଷତା ସହିତ ଏକ କ୍ରସ୍-ପ୍ଲାଟଫର୍ମ ଡକୁମେଣ୍ଟ ମାନକ। ଆନୁଷ୍ଠାନିକ ଡକୁମେଣ୍ଟ, ରିପୋର୍ଟ, ଇନଭଏସ୍, ଚୁକ୍ତିନାମା ଏବଂ ଏକାଡେମିକ୍ ପେପରରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ। ବ୍ୟବସାୟିକ ଯୋଗାଯୋଗ ଏବଂ ଡକୁମେଣ୍ଟ ଆର୍କାଇଭିଂ ପାଇଁ ପସନ୍ଦର ଫର୍ମାଟ୍, ବିଭିନ୍ନ ଡିଭାଇସ୍ ଏବଂ ଅପରେଟିଂ ସିଷ୍ଟମରେ ସମ୍ପୂର୍ଣ୍ଣ ସ୍ଥିର ଭିଜୁଆଲ୍ ଇଫେକ୍ଟ ନିଶ୍ଚିତ କରେ।"
    step1: "ଯେକୌଣସି ଫର୍ମାଟରେ ଟେବୁଲ୍ ଡାଟା ଇମ୍ପୋର୍ଟ କରନ୍ତୁ। ଟୁଲ୍ ସ୍ୱୟଂଚାଳିତ ଭାବରେ ଡାଟା ଗଠନ ବିଶ୍ଳେଷଣ କରେ ଏବଂ ବୁଦ୍ଧିମାନ ଲେଆଉଟ୍ ଡିଜାଇନ୍ କରେ, ବଡ଼ ଟେବୁଲ୍ ସ୍ୱୟଂ-ପୃଷ୍ଠାକରଣ ଏବଂ ଜଟିଳ ଡାଟା ପ୍ରକାର ପ୍ରୋସେସିଂକୁ ସମର୍ଥନ କରେ।"
    step3: "ଏକାଧିକ ବୃତ୍ତିଗତ ଥିମ୍ ଶୈଳୀ (ବ୍ୟବସାୟିକ, ଏକାଡେମିକ୍, ମିନିମାଲିଷ୍ଟ, ଇତ୍ୟାଦି), ବହୁଭାଷୀ ଫଣ୍ଟ, ସ୍ୱୟଂ-ପୃଷ୍ଠାକରଣ, ୱାଟରମାର୍କ ଯୋଗ ଏବଂ ମୁଦ୍ରଣ ଅପ୍ଟିମାଇଜେସନ୍ ସମର୍ଥନ ସହିତ ଉଚ୍ଚ-ଗୁଣବତ୍ତା PDF ଟେବୁଲ୍ ଫାଇଲ୍ ଜେନେରେଟ୍ କରନ୍ତୁ। ଆଉଟପୁଟ୍ PDF ଡକୁମେଣ୍ଟଗୁଡ଼ିକର ବୃତ୍ତିଗତ ଦୃଶ୍ୟ ଥିବା ନିଶ୍ଚିତ କରେ, ବ୍ୟବସାୟିକ ଉପସ୍ଥାପନା ଏବଂ ଆନୁଷ୍ଠାନିକ ପ୍ରକାଶନ ପାଇଁ ସିଧାସଳଖ ବ୍ୟବହାର ଯୋଗ୍ୟ।"
    from_alias: "ଟେବୁଲ୍ ଡାଟା"
    to_alias: "PDF ବୃତ୍ତିଗତ ଡକୁମେଣ୍ଟ"
  JPEG:
    alias: "JPEG ଇମେଜ୍"
    what: "JPEG ହେଉଛି ଉତ୍କୃଷ୍ଟ କମ୍ପ୍ରେସନ୍ ଇଫେକ୍ଟ ଏବଂ ବ୍ୟାପକ ସୁସଙ୍ଗତି ସହିତ ସର୍ବାଧିକ ବ୍ୟବହୃତ ଡିଜିଟାଲ୍ ଇମେଜ୍ ଫର୍ମାଟ୍। ଏହାର ଛୋଟ ଫାଇଲ୍ ସାଇଜ୍ ଏବଂ ଦ୍ରୁତ ଲୋଡିଂ ସ୍ପିଡ୍ ଏହାକୁ ୱେବ୍ ଡିସପ୍ଲେ, ସୋସିଆଲ୍ ମିଡିଆ ସେୟାରିଂ, ଡକୁମେଣ୍ଟ ଚିତ୍ରଣ ଏବଂ ଅନଲାଇନ୍ ଉପସ୍ଥାପନା ପାଇଁ ଉପଯୁକ୍ତ କରିଥାଏ। ଡିଜିଟାଲ୍ ମିଡିଆ ଏବଂ ନେଟୱାର୍କ ଯୋଗାଯୋଗ ପାଇଁ ମାନକ ଇମେଜ୍ ଫର୍ମାଟ୍, ପ୍ରାୟ ସମସ୍ତ ଡିଭାଇସ୍ ଏବଂ ସଫ୍ଟୱେୟାର ଦ୍ୱାରା ସମ୍ପୂର୍ଣ୍ଣ ସମର୍ଥିତ।"
    step1: "ଯେକୌଣସି ଫର୍ମାଟରେ ଟେବୁଲ୍ ଡାଟା ଇମ୍ପୋର୍ଟ କରନ୍ତୁ। ଟୁଲ୍ ବୁଦ୍ଧିମାନ ଲେଆଉଟ୍ ଡିଜାଇନ୍ ଏବଂ ଭିଜୁଆଲ୍ ଅପ୍ଟିମାଇଜେସନ୍ କରେ, ସ୍ୱୟଂଚାଳିତ ଭାବରେ ସର୍ବୋତ୍ତମ ସାଇଜ୍ ଏବଂ ରେଜୋଲ୍ୟୁସନ୍ ଗଣନା କରେ।"
    step3: "ଏକାଧିକ ଥିମ୍ ରଙ୍ଗ ସ୍କିମ୍ (ହାଲୁକା, ଗାଢ଼, ଆଖି-ବନ୍ଧୁ, ଇତ୍ୟାଦି), ଆଡାପ୍ଟିଭ୍ ଲେଆଉଟ୍, ଟେକ୍ସଟ୍ ସ୍ପଷ୍ଟତା ଅପ୍ଟିମାଇଜେସନ୍ ଏବଂ ସାଇଜ୍ କଷ୍ଟମାଇଜେସନ୍ ସମର୍ଥନ ସହିତ ଉଚ୍ଚ-ସଂଜ୍ଞା JPEG ଟେବୁଲ୍ ଇମେଜ୍ ଜେନେରେଟ୍ କରନ୍ତୁ। ଅନଲାଇନ୍ ସେୟାରିଂ, ଡକୁମେଣ୍ଟ ଇନ୍ସର୍ସନ୍ ଏବଂ ଉପସ୍ଥାପନା ବ୍ୟବହାର ପାଇଁ ଉପଯୁକ୍ତ, ବିଭିନ୍ନ ଡିସପ୍ଲେ ଡିଭାଇସରେ ଉତ୍କୃଷ୍ଟ ଭିଜୁଆଲ୍ ଇଫେକ୍ଟ ନିଶ୍ଚିତ କରେ।"
    from_alias: "ଟେବୁଲ୍ ଡାଟା"
    to_alias: "JPEG ଉଚ୍ଚ-ସଂଜ୍ଞା ଇମେଜ୍"
  Jira:
    alias: "Jira ଟେବୁଲ୍"
    what: "JIRA ହେଉଛି Atlassian ଦ୍ୱାରା ବିକଶିତ ବୃତ୍ତିଗତ ପ୍ରୋଜେକ୍ଟ ମ୍ୟାନେଜମେଣ୍ଟ ଏବଂ ବଗ୍ ଟ୍ରାକିଂ ସଫ୍ଟୱେୟାର, ଏଜାଇଲ୍ ଡେଭେଲପମେଣ୍ଟ, ସଫ୍ଟୱେୟାର ଟେଷ୍ଟିଂ ଏବଂ ପ୍ରୋଜେକ୍ଟ ସହଯୋଗରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ। ଏହାର ଟେବୁଲ୍ କାର୍ଯ୍ୟକାରିତା ସମୃଦ୍ଧ ଫର୍ମାଟିଂ ବିକଳ୍ପ ଏବଂ ଡାଟା ପ୍ରଦର୍ଶନକୁ ସମର୍ଥନ କରେ, ସଫ୍ଟୱେୟାର ଡେଭେଲପମେଣ୍ଟ ଟିମ୍, ପ୍ରୋଜେକ୍ଟ ମ୍ୟାନେଜର ଏବଂ ଗୁଣବତ୍ତା ନିଶ୍ଚିତତା କର୍ମଚାରୀଙ୍କ ପାଇଁ ଆବଶ୍ୟକତା ପରିଚାଳନା, ବଗ୍ ଟ୍ରାକିଂ ଏବଂ ପ୍ରଗତି ରିପୋର୍ଟିଂରେ ଏକ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଟୁଲ୍ ଭାବରେ କାର୍ଯ୍ୟ କରେ।"
    step1: "ଟେବୁଲ୍ ଡାଟା ଧାରଣ କରିଥିବା ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା ସିଧାସଳଖ ଡାଟା ବିଷୟବସ୍ତୁ ପେଷ୍ଟ କରନ୍ତୁ। ଟୁଲ୍ ସ୍ୱୟଂଚାଳିତ ଭାବରେ ଟେବୁଲ୍ ଡାଟା ଏବଂ ବିଶେଷ ଅକ୍ଷର ଏସ୍କେପିଂ ପ୍ରୋସେସ କରେ।"
    step3: "ହେଡର ଶୈଳୀ ସେଟିଂସ, ସେଲ୍ ସଜ୍ଜା, ଅକ୍ଷର ଏସ୍କେପ୍ ପ୍ରୋସେସିଂ ଏବଂ ଫର୍ମାଟ୍ ଅପ୍ଟିମାଇଜେସନ୍ ସମର୍ଥନ ସହିତ JIRA ପ୍ଲାଟଫର୍ମ-ସୁସଙ୍ଗତ ଟେବୁଲ୍ କୋଡ୍ ଜେନେରେଟ୍ କରନ୍ତୁ। ଜେନେରେଟ୍ ହୋଇଥିବା କୋଡ୍ ସିଧାସଳଖ JIRA ଇସୁ ବର୍ଣ୍ଣନା, କମେଣ୍ଟ କିମ୍ବା ୱିକି ପେଜରେ ପେଷ୍ଟ କରାଯାଇପାରିବ, JIRA ସିଷ୍ଟମରେ ସଠିକ ପ୍ରଦର୍ଶନ ଏବଂ ରେଣ୍ଡରିଂ ନିଶ୍ଚିତ କରେ।"
    from_alias: "ପ୍ରୋଜେକ୍ଟ ଡାଟା"
    to_alias: "Jira ଟେବୁଲ୍ ସିଣ୍ଟାକ୍ସ"
  Textile:
    alias: "Textile ଟେବୁଲ୍"
    what: "Textile ହେଉଛି ସରଳ ଏବଂ ଶିଖିବା ସହଜ ସିଣ୍ଟାକ୍ସ ସହିତ ଏକ ସଂକ୍ଷିପ୍ତ ହାଲୁକା ମାର୍କଅପ୍ ଭାଷା, ବିଷୟବସ୍ତୁ ପରିଚାଳନା ସିଷ୍ଟମ, ବ୍ଲଗ୍ ପ୍ଲାଟଫର୍ମ ଏବଂ ଫୋରମ୍ ସିଷ୍ଟମରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ। ଏହାର ଟେବୁଲ୍ ସିଣ୍ଟାକ୍ସ ସ୍ପଷ୍ଟ ଏବଂ ଅନ୍ତର୍ଦୃଷ୍ଟିଶୀଳ, ଦ୍ରୁତ ଫର୍ମାଟିଂ ଏବଂ ଶୈଳୀ ସେଟିଂସକୁ ସମର୍ଥନ କରେ। ଦ୍ରୁତ ଡକୁମେଣ୍ଟ ଲେଖା ଏବଂ ବିଷୟବସ୍ତୁ ପ୍ରକାଶନ ପାଇଁ ବିଷୟବସ୍ତୁ ସୃଷ୍ଟିକାରୀ ଏବଂ ୱେବସାଇଟ୍ ଆଡମିନିଷ୍ଟ୍ରେଟରଙ୍କ ପାଇଁ ଏକ ଆଦର୍ଶ ଟୁଲ୍।"
    step1: "Textile ଫର୍ମାଟ୍ ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା ଟେବୁଲ୍ ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ। ଟୁଲ୍ Textile ମାର୍କଅପ୍ ସିଣ୍ଟାକ୍ସ ପାର୍ସ କରେ ଏବଂ ଟେବୁଲ୍ ବିଷୟବସ୍ତୁ ଏକ୍ସଟ୍ରାକ୍ଟ କରେ।"
    step3: "ହେଡର ମାର୍କଅପ୍, ସେଲ୍ ସଜ୍ଜା, ବିଶେଷ ଅକ୍ଷର ଏସ୍କେପିଂ ଏବଂ ଫର୍ମାଟ୍ ଅପ୍ଟିମାଇଜେସନ୍ ସମର୍ଥନ ସହିତ ମାନକ Textile ଟେବୁଲ୍ ସିଣ୍ଟାକ୍ସ ଜେନେରେଟ୍ କରନ୍ତୁ। ଜେନେରେଟ୍ ହୋଇଥିବା କୋଡ୍ ସିଧାସଳଖ CMS ସିଷ୍ଟମ, ବ୍ଲଗ୍ ପ୍ଲାଟଫର୍ମ ଏବଂ Textile କୁ ସମର୍ଥନ କରୁଥିବା ଡକୁମେଣ୍ଟ ସିଷ୍ଟମରେ ବ୍ୟବହାର କରାଯାଇପାରିବ, ସଠିକ ବିଷୟବସ୍ତୁ ରେଣ୍ଡରିଂ ଏବଂ ପ୍ରଦର୍ଶନ ନିଶ୍ଚିତ କରେ।"
    from_alias: "Textile ଡକୁମେଣ୍ଟ"
    to_alias: "Textile ଟେବୁଲ୍ ସିଣ୍ଟାକ୍ସ"
  PNG:
    alias: "PNG ଇମେଜ୍"
    what: "PNG (Portable Network Graphics) ହେଉଛି ଉତ୍କୃଷ୍ଟ କମ୍ପ୍ରେସନ୍ ଏବଂ ସ୍ୱଚ୍ଛତା ସମର୍ଥନ ସହିତ ଏକ ଲସଲେସ୍ ଇମେଜ୍ ଫର୍ମାଟ୍। ୱେବ୍ ଡିଜାଇନ୍, ଡିଜିଟାଲ୍ ଗ୍ରାଫିକ୍ସ ଏବଂ ବୃତ୍ତିଗତ ଫଟୋଗ୍ରାଫିରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ। ଏହାର ଉଚ୍ଚ ଗୁଣବତ୍ତା ଏବଂ ବ୍ୟାପକ ସୁସଙ୍ଗତି ଏହାକୁ ସ୍କ୍ରିନସଟ୍, ଲୋଗୋ, ଚିତ୍ର ଏବଂ ସ୍ପଷ୍ଟ ବିବରଣୀ ଏବଂ ସ୍ୱଚ୍ଛ ବ୍ୟାକଗ୍ରାଉଣ୍ଡ ଆବଶ୍ୟକ କରୁଥିବା ଯେକୌଣସି ଇମେଜ୍ ପାଇଁ ଆଦର୍ଶ କରିଥାଏ।"
    step1: "ଯେକୌଣସି ଫର୍ମାଟରେ ଟେବୁଲ୍ ଡାଟା ଇମ୍ପୋର୍ଟ କରନ୍ତୁ। ଟୁଲ୍ ବୁଦ୍ଧିମାନ ଲେଆଉଟ୍ ଡିଜାଇନ୍ ଏବଂ ଭିଜୁଆଲ୍ ଅପ୍ଟିମାଇଜେସନ୍ କରେ, PNG ଆଉଟପୁଟ୍ ପାଇଁ ସ୍ୱୟଂଚାଳିତ ଭାବରେ ସର୍ବୋତ୍ତମ ସାଇଜ୍ ଏବଂ ରେଜୋଲ୍ୟୁସନ୍ ଗଣନା କରେ।"
    step3: "ଏକାଧିକ ଥିମ୍ ରଙ୍ଗ ସ୍କିମ୍, ସ୍ୱଚ୍ଛ ବ୍ୟାକଗ୍ରାଉଣ୍ଡ, ଆଡାପ୍ଟିଭ୍ ଲେଆଉଟ୍ ଏବଂ ଟେକ୍ସଟ୍ ସ୍ପଷ୍ଟତା ଅପ୍ଟିମାଇଜେସନ୍ ସମର୍ଥନ ସହିତ ଉଚ୍ଚ-ଗୁଣବତ୍ତା PNG ଟେବୁଲ୍ ଇମେଜ୍ ଜେନେରେଟ୍ କରନ୍ତୁ। ଉତ୍କୃଷ୍ଟ ଭିଜୁଆଲ୍ ଗୁଣବତ୍ତା ସହିତ ୱେବ୍ ବ୍ୟବହାର, ଡକୁମେଣ୍ଟ ଇନ୍ସର୍ସନ୍ ଏବଂ ବୃତ୍ତିଗତ ଉପସ୍ଥାପନା ପାଇଁ ସମ୍ପୂର୍ଣ୍ଣ।"
    from_alias: "ଟେବୁଲ୍ ଡାଟା"
    to_alias: "PNG ଉଚ୍ଚ-ଗୁଣବତ୍ତା ଇମେଜ୍"
  TOML:
    alias: "TOML କନଫିଗରେସନ୍"
    what: "TOML (Tom's Obvious, Minimal Language) ହେଉଛି ଏକ କନଫିଗରେସନ୍ ଫାଇଲ୍ ଫର୍ମାଟ୍ ଯାହା ପଢ଼ିବା ଏବଂ ଲେଖିବା ସହଜ। ଅସ୍ପଷ୍ଟତା ରହିତ ଏବଂ ସରଳ ହେବା ପାଇଁ ଡିଜାଇନ୍ କରାଯାଇଛି, ଏହା ଆଧୁନିକ ସଫ୍ଟୱେୟାର ପ୍ରୋଜେକ୍ଟରେ କନଫିଗରେସନ୍ ପରିଚାଳନା ପାଇଁ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ। ଏହାର ସ୍ପଷ୍ଟ ସିଣ୍ଟାକ୍ସ ଏବଂ ଦୃଢ଼ ଟାଇପିଂ ଏହାକୁ ଆପ୍ଲିକେସନ୍ ସେଟିଂସ ଏବଂ ପ୍ରୋଜେକ୍ଟ କନଫିଗରେସନ୍ ଫାଇଲ୍ ପାଇଁ ଏକ ଉତ୍କୃଷ୍ଟ ପସନ୍ଦ କରିଥାଏ।"
    step1: "TOML ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା କନଫିଗରେସନ୍ ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ। ଟୁଲ୍ TOML ସିଣ୍ଟାକ୍ସ ପାର୍ସ କରେ ଏବଂ ସଂରଚିତ କନଫିଗରେସନ୍ ସୂଚନା ଏକ୍ସଟ୍ରାକ୍ଟ କରେ।"
    step3: "ନେଷ୍ଟେଡ୍ ଗଠନ, ଡାଟା ପ୍ରକାର ଏବଂ ମନ୍ତବ୍ୟ ସମର୍ଥନ ସହିତ ମାନକ TOML ଫର୍ମାଟ୍ ଜେନେରେଟ୍ କରନ୍ତୁ। ଜେନେରେଟ୍ ହୋଇଥିବା TOML ଫାଇଲଗୁଡ଼ିକ ଆପ୍ଲିକେସନ୍ କନଫିଗରେସନ୍, ବିଲ୍ଡ ଟୁଲ୍ ଏବଂ ପ୍ରୋଜେକ୍ଟ ସେଟିଂସ ପାଇଁ ଉପଯୁକ୍ତ।"
    from_alias: "TOML କନଫିଗରେସନ୍"
    to_alias: "TOML ଫର୍ମାଟ୍"
  INI:
    alias: "INI କନଫିଗରେସନ୍"
    what: "INI ଫାଇଲଗୁଡ଼ିକ ହେଉଛି ବହୁ ଆପ୍ଲିକେସନ୍ ଏବଂ ଅପରେଟିଂ ସିଷ୍ଟମ୍ ଦ୍ୱାରା ବ୍ୟବହୃତ ସରଳ କନଫିଗରେସନ୍ ଫାଇଲ୍। ସେମାନଙ୍କର ସିଧାସଳଖ କି-ଭାଲ୍ୟୁ ଯୋଡ଼ି ଗଠନ ସେମାନଙ୍କୁ ମାନୁଆଲି ପଢ଼ିବା ଏବଂ ସମ୍ପାଦନା କରିବା ସହଜ କରିଥାଏ। Windows ଆପ୍ଲିକେସନ୍, ପୁରାତନ ସିଷ୍ଟମ୍ ଏବଂ ସରଳ କନଫିଗରେସନ୍ ପରିସ୍ଥିତିରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ ଯେଉଁଠାରେ ମାନବ ପଠନୀୟତା ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ।"
    step1: "INI ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା କନଫିଗରେସନ୍ ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ। ଟୁଲ୍ INI ସିଣ୍ଟାକ୍ସ ପାର୍ସ କରେ ଏବଂ ବିଭାଗ-ଆଧାରିତ କନଫିଗରେସନ୍ ସୂଚନା ଏକ୍ସଟ୍ରାକ୍ଟ କରେ।"
    step3: "ବିଭାଗ, ମନ୍ତବ୍ୟ ଏବଂ ବିଭିନ୍ନ ଡାଟା ପ୍ରକାର ସମର୍ଥନ ସହିତ ମାନକ INI ଫର୍ମାଟ୍ ଜେନେରେଟ୍ କରନ୍ତୁ। ଜେନେରେଟ୍ ହୋଇଥିବା INI ଫାଇଲଗୁଡ଼ିକ ଅଧିକାଂଶ ଆପ୍ଲିକେସନ୍ ଏବଂ କନଫିଗରେସନ୍ ସିଷ୍ଟମ୍ ସହିତ ସୁସଙ୍ଗତ।"
    from_alias: "INI କନଫିଗରେସନ୍"
    to_alias: "INI ଫର୍ମାଟ୍"
  Avro:
    alias: "Avro ସ୍କିମା"
    what: "Apache Avro ହେଉଛି ଏକ ଡାଟା ସିରିଆଲାଇଜେସନ୍ ସିଷ୍ଟମ୍ ଯାହା ସମୃଦ୍ଧ ଡାଟା ଗଠନ, କମ୍ପାକ୍ଟ ବାଇନାରୀ ଫର୍ମାଟ୍ ଏବଂ ସ୍କିମା ବିବର୍ତ୍ତନ କ୍ଷମତା ପ୍ରଦାନ କରେ। ବିଗ୍ ଡାଟା ପ୍ରୋସେସିଂ, ମେସେଜ୍ କ୍ୟୁ ଏବଂ ବିତରଣ ସିଷ୍ଟମରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ। ଏହାର ସ୍କିମା ସଂଜ୍ଞା ଜଟିଳ ଡାଟା ପ୍ରକାର ଏବଂ ସଂସ୍କରଣ ସୁସଙ୍ଗତିକୁ ସମର୍ଥନ କରେ, ଏହାକୁ ଡାଟା ଇଞ୍ଜିନିୟର ଏବଂ ସିଷ୍ଟମ୍ ଆର୍କିଟେକ୍ଟଙ୍କ ପାଇଁ ଏକ ଗୁରୁତ୍ୱପୂର୍ଣ୍ଣ ଟୁଲ୍ କରିଥାଏ।"
    step1: "Avro ସ୍କିମା ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ। ଟୁଲ୍ Avro ସ୍କିମା ସଂଜ୍ଞା ପାର୍ସ କରେ ଏବଂ ଟେବୁଲ୍ ଗଠନ ସୂଚନା ଏକ୍ସଟ୍ରାକ୍ଟ କରେ।"
    step3: "ଡାଟା ପ୍ରକାର ମ୍ୟାପିଂ, ଫିଲ୍ଡ ପ୍ରତିବନ୍ଧକ ଏବଂ ସ୍କିମା ଯାଞ୍ଚ ସମର୍ଥନ ସହିତ ମାନକ Avro ସ୍କିମା ସଂଜ୍ଞା ଜେନେରେଟ୍ କରନ୍ତୁ। ଜେନେରେଟ୍ ହୋଇଥିବା ସ୍କିମାଗୁଡ଼ିକ ସିଧାସଳଖ Hadoop ଇକୋସିଷ୍ଟମ, Kafka ମେସେଜ୍ ସିଷ୍ଟମ ଏବଂ ଅନ୍ୟାନ୍ୟ ବିଗ୍ ଡାଟା ପ୍ଲାଟଫର୍ମରେ ବ୍ୟବହାର କରାଯାଇପାରିବ।"
    from_alias: "Avro ସ୍କିମା"
    to_alias: "Avro ଡାଟା ଫର୍ମାଟ୍"
  Protobuf:
    alias: "ପ୍ରୋଟୋକଲ୍ ବଫର୍"
    what: "Protocol Buffers (protobuf) ହେଉଛି Google ର ଭାଷା-ନିରପେକ୍ଷ, ପ୍ଲାଟଫର୍ମ-ନିରପେକ୍ଷ, ସଂରଚିତ ଡାଟା ସିରିଆଲାଇଜ କରିବା ପାଇଁ ବିସ୍ତାରଯୋଗ୍ୟ ଯନ୍ତ୍ରାଂଶ। ମାଇକ୍ରୋସର୍ଭିସ, API ଡେଭେଲପମେଣ୍ଟ ଏବଂ ଡାଟା ଷ୍ଟୋରେଜରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ। ଏହାର ଦକ୍ଷ ବାଇନାରୀ ଫର୍ମାଟ୍ ଏବଂ ଦୃଢ଼ ଟାଇପିଂ ଏହାକୁ ଉଚ୍ଚ-କାର୍ଯ୍ୟଦକ୍ଷତା ଆପ୍ଲିକେସନ୍ ଏବଂ କ୍ରସ୍-ଭାଷା ଯୋଗାଯୋଗ ପାଇଁ ଆଦର୍ଶ କରିଥାଏ।"
    step1: ".proto ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା Protocol Buffer ସଂଜ୍ଞା ପେଷ୍ଟ କରନ୍ତୁ। ଟୁଲ୍ protobuf ସିଣ୍ଟାକ୍ସ ପାର୍ସ କରେ ଏବଂ ମେସେଜ୍ ଗଠନ ସୂଚନା ଏକ୍ସଟ୍ରାକ୍ଟ କରେ।"
    step3: "ମେସେଜ୍ ପ୍ରକାର, ଫିଲ୍ଡ ବିକଳ୍ପ ଏବଂ ସେବା ସଂଜ୍ଞା ସମର୍ଥନ ସହିତ ମାନକ Protocol Buffer ସଂଜ୍ଞା ଜେନେରେଟ୍ କରନ୍ତୁ। ଜେନେରେଟ୍ ହୋଇଥିବା .proto ଫାଇଲଗୁଡ଼ିକ ଏକାଧିକ ପ୍ରୋଗ୍ରାମିଂ ଭାଷା ପାଇଁ କମ୍ପାଇଲ୍ କରାଯାଇପାରିବ।"
    from_alias: "ପ୍ରୋଟୋକଲ୍ ବଫର୍"
    to_alias: "Protobuf ସ୍କିମା"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas ହେଉଛି Python ରେ ସର୍ବାଧିକ ଲୋକପ୍ରିୟ ଡାଟା ବିଶ୍ଳେଷଣ ଲାଇବ୍ରେରୀ, DataFrame ହେଉଛି ଏହାର ମୂଳ ଡାଟା ଗଠନ। ଏହା ଶକ୍ତିଶାଳୀ ଡାଟା ମ୍ୟାନିପୁଲେସନ୍, ସଫାକରଣ ଏବଂ ବିଶ୍ଳେଷଣ କ୍ଷମତା ପ୍ରଦାନ କରେ, ଡାଟା ସାଇନ୍ସ, ମେସିନ୍ ଲର୍ନିଂ ଏବଂ ବ୍ୟବସାୟିକ ବୁଦ୍ଧିମତ୍ତାରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ। Python ଡେଭେଲପର ଏବଂ ଡାଟା ବିଶ୍ଳେଷକଙ୍କ ପାଇଁ ଏକ ଅପରିହାର୍ଯ୍ୟ ଟୁଲ୍।"
    step1: "DataFrame କୋଡ୍ ଧାରଣ କରିଥିବା Python ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ। ଟୁଲ୍ Pandas ସିଣ୍ଟାକ୍ସ ପାର୍ସ କରେ ଏବଂ DataFrame ଗଠନ ସୂଚନା ଏକ୍ସଟ୍ରାକ୍ଟ କରେ।"
    step3: "ଡାଟା ପ୍ରକାର ନିର୍ଦ୍ଦିଷ୍ଟକରଣ, ଇଣ୍ଡେକ୍ସ ସେଟିଂସ ଏବଂ ଡାଟା ଅପରେସନ୍ ସମର୍ଥନ ସହିତ ମାନକ Pandas DataFrame କୋଡ୍ ଜେନେରେଟ୍ କରନ୍ତୁ। ଜେନେରେଟ୍ ହୋଇଥିବା କୋଡ୍ ଡାଟା ବିଶ୍ଳେଷଣ ଏବଂ ପ୍ରୋସେସିଂ ପାଇଁ Python ପରିବେଶରେ ସିଧାସଳଖ ଏକ୍ଜିକ୍ୟୁଟ୍ କରାଯାଇପାରିବ।"
    from_alias: "Pandas DataFrame"
    to_alias: "Python ଡାଟା ଗଠନ"
  RDF:
    alias: "RDF ଟ୍ରିପଲ୍"
    what: "RDF (Resource Description Framework) ହେଉଛି ୱେବରେ ଡାଟା ଆଦାନପ୍ରଦାନ ପାଇଁ ଏକ ମାନକ ମଡେଲ୍, ଗ୍ରାଫ୍ ଆକାରରେ ଉତ୍ସ ବିଷୟରେ ସୂଚନା ପ୍ରତିନିଧିତ୍ୱ କରିବା ପାଇଁ ଡିଜାଇନ୍ କରାଯାଇଛି। ସେମାଣ୍ଟିକ୍ ୱେବ୍, ଜ୍ଞାନ ଗ୍ରାଫ୍ ଏବଂ ଲିଙ୍କ୍ଡ ଡାଟା ଆପ୍ଲିକେସନରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ। ଏହାର ଟ୍ରିପଲ୍ ଗଠନ ସମୃଦ୍ଧ ମେଟାଡାଟା ପ୍ରତିନିଧିତ୍ୱ ଏବଂ ସେମାଣ୍ଟିକ୍ ସମ୍ପର୍କକୁ ସକ୍ଷମ କରେ।"
    step1: "RDF ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା ଟ୍ରିପଲ୍ ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ। ଟୁଲ୍ RDF ସିଣ୍ଟାକ୍ସ ପାର୍ସ କରେ ଏବଂ ସେମାଣ୍ଟିକ୍ ସମ୍ପର୍କ ଏବଂ ଉତ୍ସ ସୂଚନା ଏକ୍ସଟ୍ରାକ୍ଟ କରେ।"
    step3: "ବିଭିନ୍ନ ସିରିଆଲାଇଜେସନ୍ (RDF/XML, Turtle, N-Triples) ସମର୍ଥନ ସହିତ ମାନକ RDF ଫର୍ମାଟ୍ ଜେନେରେଟ୍ କରନ୍ତୁ। ଜେନେରେଟ୍ ହୋଇଥିବା RDF ସେମାଣ୍ଟିକ୍ ୱେବ୍ ଆପ୍ଲିକେସନ୍, ଜ୍ଞାନ ଭିତ୍ତି ଏବଂ ଲିଙ୍କ୍ଡ ଡାଟା ସିଷ୍ଟମରେ ବ୍ୟବହାର କରାଯାଇପାରିବ।"
    from_alias: "RDF ଡାଟା"
    to_alias: "RDF ସେମାଣ୍ଟିକ୍ ଫର୍ମାଟ୍"
  MATLAB:
    alias: "MATLAB ଆର୍ରେ"
    what: "MATLAB ହେଉଛି ଏକ ଉଚ୍ଚ-କାର୍ଯ୍ୟଦକ୍ଷତା ସାଂଖ୍ୟିକ ଗଣନା ଏବଂ ଭିଜୁଆଲାଇଜେସନ୍ ସଫ୍ଟୱେୟାର ଯାହା ଇଞ୍ଜିନିୟରିଂ ଗଣନା, ଡାଟା ବିଶ୍ଳେଷଣ ଏବଂ ଆଲଗୋରିଦମ ବିକାଶରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ। ଏହାର ଆର୍ରେ ଏବଂ ମ୍ୟାଟ୍ରିକ୍ସ ଅପରେସନ୍ ଶକ୍ତିଶାଳୀ, ଜଟିଳ ଗାଣିତିକ ଗଣନା ଏବଂ ଡାଟା ପ୍ରୋସେସିଂକୁ ସମର୍ଥନ କରେ। ଇଞ୍ଜିନିୟର, ଗବେଷକ ଏବଂ ଡାଟା ବିଜ୍ଞାନୀଙ୍କ ପାଇଁ ଏକ ଅତ୍ୟାବଶ୍ୟକ ଟୁଲ୍।"
    step1: "MATLAB .m ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ କିମ୍ବା ଆର୍ରେ ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ। ଟୁଲ୍ MATLAB ସିଣ୍ଟାକ୍ସ ପାର୍ସ କରେ ଏବଂ ଆର୍ରେ ଗଠନ ସୂଚନା ଏକ୍ସଟ୍ରାକ୍ଟ କରେ।"
    step3: "ବହୁ-ଆୟାମିକ ଆର୍ରେ, ଡାଟା ପ୍ରକାର ନିର୍ଦ୍ଦିଷ୍ଟକରଣ ଏବଂ ଭେରିଏବଲ୍ ନାମକରଣ ସମର୍ଥନ ସହିତ ମାନକ MATLAB ଆର୍ରେ କୋଡ୍ ଜେନେରେଟ୍ କରନ୍ତୁ। ଜେନେରେଟ୍ ହୋଇଥିବା କୋଡ୍ ଡାଟା ବିଶ୍ଳେଷଣ ଏବଂ ବୈଜ୍ଞାନିକ ଗଣନା ପାଇଁ MATLAB ପରିବେଶରେ ସିଧାସଳଖ ଏକ୍ଜିକ୍ୟୁଟ୍ କରାଯାଇପାରିବ।"
    from_alias: "MATLAB ଆର୍ରେ"
    to_alias: "MATLAB କୋଡ୍ ଫର୍ମାଟ୍"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame ହେଉଛି R ପ୍ରୋଗ୍ରାମିଂ ଭାଷାର ମୁଖ୍ୟ ଡାଟା ଗଠନ, ଯାହା ପରିସଂଖ୍ୟାନ ବିଶ୍ଳେଷଣ, ଡାଟା ମାଇନିଂ ଏବଂ ମେସିନ୍ ଲର୍ନିଂରେ ବ୍ୟାପକ ଭାବରେ ବ୍ୟବହୃତ ହୋଇଥାଏ। R ହେଉଛି ପରିସଂଖ୍ୟାନ ଗଣନା ଏବଂ ଗ୍ରାଫିକ୍ସ ପାଇଁ ପ୍ରମୁଖ ଉପକରଣ, DataFrame ଶକ୍ତିଶାଳୀ ଡାଟା ନିପୁଣତା, ପରିସଂଖ୍ୟାନ ବିଶ୍ଳେଷଣ ଏବଂ ଭିଜୁଆଲାଇଜେସନ କ୍ଷମତା ପ୍ରଦାନ କରେ। ଗଠନମୂଳକ ଡାଟା ବିଶ୍ଳେଷଣରେ କାର୍ଯ୍ୟ କରୁଥିବା ଡାଟା ବିଜ୍ଞାନୀ, ପରିସଂଖ୍ୟାନବିଦ ଏବଂ ଗବେଷକମାନଙ୍କ ପାଇଁ ଅତ୍ୟାବଶ୍ୟକ।"
    step1: "R ଡାଟା ଫାଇଲ ଅପଲୋଡ କରନ୍ତୁ କିମ୍ବା DataFrame କୋଡ ପେଷ୍ଟ କରନ୍ତୁ। ଟୁଲ R ସିଣ୍ଟାକ୍ସ ପାର୍ସ କରେ ଏବଂ ସ୍ତମ୍ଭ ପ୍ରକାର, ଧାଡି ନାମ ଏବଂ ଡାଟା ବିଷୟବସ୍ତୁ ସମେତ DataFrame ଗଠନ ସୂଚନା ଏକ୍ସଟ୍ରାକ୍ଟ କରେ।"
    step3: "ଡାଟା ପ୍ରକାର ନିର୍ଦ୍ଦେଶନ, ଫାକ୍ଟର ଲେଭେଲ, ଧାଡି/ସ୍ତମ୍ଭ ନାମ ଏବଂ R-ନିର୍ଦ୍ଦିଷ୍ଟ ଡାଟା ଗଠନ ପାଇଁ ସହାୟତା ସହିତ ମାନକ R DataFrame କୋଡ ସୃଷ୍ଟି କରନ୍ତୁ। ସୃଷ୍ଟି ହୋଇଥିବା କୋଡ ପରିସଂଖ୍ୟାନ ବିଶ୍ଳେଷଣ ଏବଂ ଡାଟା ପ୍ରକ୍ରିୟାକରଣ ପାଇଁ R ପରିବେଶରେ ସିଧାସଳଖ କାର୍ଯ୍ୟକାରୀ ହୋଇପାରିବ।"
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "ରୂପାନ୍ତରଣ ଆରମ୍ଭ କରନ୍ତୁ"
  start_generating: "ଉତ୍ପାଦନ ଆରମ୍ଭ କରନ୍ତୁ"
  api_docs: "API ଡକ୍ସ"
related:
  section_title: 'ଅଧିକ {{ if and .from (ne .from "generator") }}{{ .from }} ଏବଂ {{ end }}{{ .to }} କନ୍ଭର୍ଟର'
  section_description: '{{ if and .from (ne .from "generator") }}{{ .from }} ଏବଂ {{ end }}{{ .to }} ଫର୍ମାଟ୍ ପାଇଁ ଅଧିକ କନ୍ଭର୍ଟର ଅନ୍ବେଷଣ କରନ୍ତୁ। ଆମର ପେସାଦାର ଅନଲାଇନ୍ ରୂପାନ୍ତରଣ ଉପକରଣ ସହିତ ଆପଣଙ୍କ ଡାଟାକୁ ଏକାଧିକ ଫର୍ମାଟ୍ ମଧ୍ୟରେ ରୂପାନ୍ତରିତ କରନ୍ତୁ।'
  title: "{{ .from }} ରୁ {{ .to }}"
howto:
  step2: "ପେସାଦାର ବୈଶିଷ୍ଟ୍ୟ ସହିତ ଆମର ଉନ୍ନତ ଅନଲାଇନ୍ ଟେବୁଲ୍ ଏଡିଟର୍ ବ୍ୟବହାର କରି ଡାଟା ସମ୍ପାଦନା କରନ୍ତୁ। ଖାଲି ଧାଡ଼ି ବିଲୋପ, ନକଲ ହଟାଇବା, ଡାଟା ଟ୍ରାନ୍ସପୋଜିସନ୍, ସର୍ଟିଂ, ରେଜେକ୍ସ ଖୋଜିବା ଏବଂ ବଦଳାଇବା, ଏବଂ ରିଅଲ୍-ଟାଇମ୍ ପ୍ରିଭ୍ୟୁକୁ ସମର୍ଥନ କରେ। ସମସ୍ତ ପରିବର୍ତ୍ତନ ସଠିକ, ବିଶ୍ୱସନୀୟ ଫଳାଫଳ ସହିତ %s ଫର୍ମାଟରେ ସ୍ୱୟଂଚାଳିତ ଭାବରେ ରୂପାନ୍ତରିତ ହୁଏ।"
  section_title: "{{ . }} କିପରି ବ୍ୟବହାର କରିବେ"
  converter_description: "ଆମର ଧାପ-ଦର-ଧାପ ଗାଇଡ୍ ସହିତ {{ .from }} କୁ {{ .to }} ରେ ରୂପାନ୍ତର କରିବା ଶିଖନ୍ତୁ। ଉନ୍ନତ ବୈଶିଷ୍ଟ୍ୟ ଏବଂ ରିଅଲ୍-ଟାଇମ୍ ପ୍ରିଭ୍ୟୁ ସହିତ ପେସାଦାର ଅନଲାଇନ୍ କନ୍ଭର୍ଟର।"
  generator_description: "ଆମର ଅନଲାଇନ୍ ଜେନେରେଟର ସହିତ ପେସାଦାର {{ .to }} ଟେବୁଲ୍ ସୃଷ୍ଟି କରିବା ଶିଖନ୍ତୁ। Excel ପରି ସମ୍ପାଦନା, ରିଅଲ୍-ଟାଇମ୍ ପ୍ରିଭ୍ୟୁ, ଏବଂ ତତକ୍ଷଣାତ୍ ରପ୍ତାନି କ୍ଷମତା।"
extension:
  section_title: "ଟେବୁଲ୍ ଚିହ୍ନଟ ଏବଂ ନିଷ୍କାସନ ଏକ୍ସଟେନ୍ସନ୍"
  section_description: "ଗୋଟିଏ କ୍ଲିକ୍ ରେ ଯେକୌଣସି ୱେବସାଇଟ୍ ରୁ ଟେବୁଲ୍ ବାହାର କରନ୍ତୁ। Excel, CSV, JSON ସମେତ 30+ ଫର୍ମାଟରେ ତତକ୍ଷଣାତ୍ ରୂପାନ୍ତର କରନ୍ତୁ - କପି-ପେଷ୍ଟିଂ ଆବଶ୍ୟକ ନାହିଁ।"
  features:
    extraction_title: "ଗୋଟିଏ-କ୍ଲିକ୍ ଟେବୁଲ୍ ନିଷ୍କାସନ"
    extraction_description: "କପି-ପେଷ୍ଟିଂ ବିନା ଯେକୌଣସି ୱେବପେଜ୍ ରୁ ତତକ୍ଷଣାତ୍ ଟେବୁଲ୍ ବାହାର କରନ୍ତୁ - ପେସାଦାର ଡାଟା ନିଷ୍କାସନ ସରଳ କରାଯାଇଛି"
    formats_title: "30+ ଫର୍ମାଟ୍ କନ୍ଭର୍ଟର ସମର୍ଥନ"
    formats_description: "ଆମର ଉନ୍ନତ ଟେବୁଲ୍ କନ୍ଭର୍ଟର ସହିତ ବାହାର କରାଯାଇଥିବା ଟେବୁଲ୍ କୁ Excel, CSV, JSON, Markdown, SQL, ଏବଂ ଅଧିକରେ ରୂପାନ୍ତର କରନ୍ତୁ"
    detection_title: "ସ୍ମାର୍ଟ ଟେବୁଲ୍ ଚିହ୍ନଟ"
    detection_description: "ଦ୍ରୁତ ଡାଟା ନିଷ୍କାସନ ଏବଂ ରୂପାନ୍ତରଣ ପାଇଁ ଯେକୌଣସି ୱେବପେଜରେ ଟେବୁଲ୍ ସ୍ୱୟଂଚାଳିତ ଭାବରେ ଚିହ୍ନଟ ଏବଂ ହାଇଲାଇଟ୍ କରେ"
  hover_tip: "✨ ନିଷ୍କାସନ ଆଇକନ୍ ଦେଖିବା ପାଇଁ ଯେକୌଣସି ଟେବୁଲ୍ ଉପରେ ହୋଭର୍ କରନ୍ତୁ"
recommendations:
  section_title: "ବିଶ୍ୱବିଦ୍ୟାଳୟ ଏବଂ ପେସାଦାରଙ୍କ ଦ୍ୱାରା ସୁପାରିଶ"
  section_description: "ବିଶ୍ୱସନୀୟ ଟେବୁଲ୍ ରୂପାନ୍ତରଣ ଏବଂ ଡାଟା ପ୍ରକ୍ରିୟାକରଣ ପାଇଁ ବିଶ୍ୱବିଦ୍ୟାଳୟ, ଗବେଷଣା ସଂସ୍ଥା ଏବଂ ବିକାଶ ଦଳରେ ପେସାଦାରମାନେ TableConvert କୁ ବିଶ୍ୱାସ କରନ୍ତି।"
  cards:
    university_title: "ୱିସ୍କନ୍ସିନ୍-ମ୍ୟାଡିସନ୍ ବିଶ୍ୱବିଦ୍ୟାଳୟ"
    university_description: "TableConvert.com - ପେସାଦାର ମାଗଣା ଅନଲାଇନ୍ ଟେବୁଲ୍ କନ୍ଭର୍ଟର ଏବଂ ଡାଟା ଫର୍ମାଟ୍ ଉପକରଣ"
    university_link: "ଆର୍ଟିକିଲ୍ ପଢ଼ନ୍ତୁ"
    facebook_title: "ଡାଟା ପେସାଦାର ସମ୍ପ୍ରଦାୟ"
    facebook_description: "Facebook ଡେଭଲପର ଗ୍ରୁପରେ ଡାଟା ବିଶ୍ଳେଷକ ଏବଂ ପେସାଦାରଙ୍କ ଦ୍ୱାରା ସାଝା ଏବଂ ସୁପାରିଶ କରାଯାଇଛି"
    facebook_link: "ପୋଷ୍ଟ ଦେଖନ୍ତୁ"
    twitter_title: "ଡେଭଲପର ସମ୍ପ୍ରଦାୟ"
    twitter_description: "ଟେବୁଲ୍ ରୂପାନ୍ତରଣ ପାଇଁ X (Twitter) ରେ @xiaoying_eth ଏବଂ ଅନ୍ୟ ଡେଭଲପରଙ୍କ ଦ୍ୱାରା ସୁପାରିଶ କରାଯାଇଛି"
    twitter_link: "ଟ୍ୱିଟ୍ ଦେଖନ୍ତୁ"
faq:
  section_title: "ବାରମ୍ବାର ପଚରାଯାଉଥିବା ପ୍ରଶ୍ନ"
  section_description: "ଆମର ମାଗଣା ଅନଲାଇନ୍ ଟେବୁଲ୍ କନ୍ଭର୍ଟର, ଡାଟା ଫର୍ମାଟ୍ ଏବଂ ରୂପାନ୍ତରଣ ପ୍ରକ୍ରିୟା ବିଷୟରେ ସାଧାରଣ ପ୍ରଶ୍ନ."
  what: "%s ଫର୍ମାଟ୍ କ'ଣ?"
  howto_convert:
    question: "{{ . }} କୁ ମାଗଣାରେ କିପରି ବ୍ୟବହାର କରିବେ?"
    answer: "ଆମର ମାଗଣା ଅନଲାଇନ୍ ଟେବୁଲ୍ କନ୍ଭର୍ଟର ବ୍ୟବହାର କରି ଆପଣଙ୍କ {{ .from }} ଫାଇଲ୍ ଅପଲୋଡ୍ କରନ୍ତୁ, ଡାଟା ପେଷ୍ଟ କରନ୍ତୁ, କିମ୍ବା ୱେବ୍ ପୃଷ୍ଠାରୁ ବାହାର କରନ୍ତୁ। ଆମର ପେସାଦାର କନ୍ଭର୍ଟର ଉପକରଣ ରିଅଲ୍-ଟାଇମ୍ ପ୍ରିଭ୍ୟୁ ଏବଂ ଉନ୍ନତ ସମ୍ପାଦନା ବୈଶିଷ୍ଟ୍ୟ ସହିତ ଆପଣଙ୍କ ଡାଟାକୁ ତତକ୍ଷଣାତ୍ {{ .to }} ଫର୍ମାଟରେ ରୂପାନ୍ତରିତ କରେ। ରୂପାନ୍ତରିତ ଫଳାଫଳକୁ ତତକ୍ଷଣାତ୍ ଡାଉନଲୋଡ୍ କିମ୍ବା କପି କରନ୍ତୁ।"
  security:
    question: "ଏହି ଅନଲାଇନ୍ କନ୍ଭର୍ଟର ବ୍ୟବହାର କରିବା ସମୟରେ ମୋର ଡାଟା ସୁରକ୍ଷିତ କି?"
    answer: "ନିଶ୍ଚିତ! ସମସ୍ତ ଟେବୁଲ୍ ରୂପାନ୍ତରଣ ଆପଣଙ୍କ ବ୍ରାଉଜରରେ ସ୍ଥାନୀୟ ଭାବରେ ହୁଏ - ଆପଣଙ୍କ ଡାଟା କଦାପି ଆପଣଙ୍କ ଡିଭାଇସ୍ ଛାଡ଼େ ନାହିଁ। ଆମର ଅନଲାଇନ୍ କନ୍ଭର୍ଟର ସବୁକିଛି କ୍ଲାଏଣ୍ଟ-ସାଇଡ୍ ପ୍ରକ୍ରିୟା କରେ, ସମ୍ପୂର୍ଣ୍ଣ ଗୋପନୀୟତା ଏବଂ ଡାଟା ସୁରକ୍ଷା ନିଶ୍ଚିତ କରେ। ଆମର ସର୍ଭରରେ କୌଣସି ଫାଇଲ୍ ସଂରକ୍ଷିତ ହୁଏ ନାହିଁ।"
  free:
    question: "TableConvert ପ୍ରକୃତରେ ବ୍ୟବହାର କରିବା ପାଇଁ ମାଗଣା କି?"
    answer: "ହଁ, TableConvert ସମ୍ପୂର୍ଣ୍ଣ ମାଗଣା! ସମସ୍ତ କନ୍ଭର୍ଟର ବୈଶିଷ୍ଟ୍ୟ, ଟେବୁଲ୍ ଏଡିଟର, ଡାଟା ଜେନେରେଟର ଉପକରଣ ଏବଂ ରପ୍ତାନି ବିକଳ୍ପ କୌଣସି ଖର୍ଚ୍ଚ, ପଞ୍ଜୀକରଣ କିମ୍ବା ଲୁକ୍କାୟିତ ଫି ବିନା ଉପଲବ୍ଧ। ମାଗଣାରେ ଅନଲାଇନ୍ରେ ଅସୀମିତ ଫାଇଲ୍ ରୂପାନ୍ତର କରନ୍ତୁ।"
  filesize:
    question: "ଅନଲାଇନ୍ କନ୍ଭର୍ଟରର ଫାଇଲ୍ ସାଇଜ୍ ସୀମା କ'ଣ?"
    answer: "ଆମର ମାଗଣା ଅନଲାଇନ୍ ଟେବୁଲ୍ କନ୍ଭର୍ଟର 10MB ପର୍ଯ୍ୟନ୍ତ ଫାଇଲ୍ ସମର୍ଥନ କରେ। ବଡ଼ ଫାଇଲ୍, ବ୍ୟାଚ୍ ପ୍ରକ୍ରିୟାକରଣ କିମ୍ବା ଏଣ୍ଟରପ୍ରାଇଜ୍ ଆବଶ୍ୟକତା ପାଇଁ, ଉଚ୍ଚ ସୀମା ସହିତ ଆମର ବ୍ରାଉଜର ଏକ୍ସଟେନ୍ସନ୍ କିମ୍ବା ପେସାଦାର API ସେବା ବ୍ୟବହାର କରନ୍ତୁ।"
stats:
  conversions: "ରୂପାନ୍ତରିତ ଟେବୁଲ୍"
  tables: "ସୃଷ୍ଟି ହୋଇଥିବା ଟେବୁଲ୍"
  formats: "ଡାଟା ଫାଇଲ୍ ଫର୍ମାଟ୍"
  rating: "ଉପଯୋଗକର୍ତ୍ତା ରେଟିଂ"
