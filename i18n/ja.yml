site:
  fullname: "オンラインテーブル変換"
  name: "TableConvert"
  subtitle: "無料オンラインテーブルコンバーター＆ジェネレーター"
  intro: "TableConvertは、Excel、CSV、JSON、Markdown、LaTeX、SQLなど30以上のフォーマット間の変換をサポートする無料のオンラインテーブルコンバーター＆データジェネレーターツールです."
  followTwitter: "Xでフォローする"
title:
  converter: "%sから%sへ"
  generator: "%sジェネレーター"
post:
  tags:
    converter: "コンバーター"
    editor: "エディター"
    generator: "ジェネレーター"
    maker: "ビルダー"
  converter:
    title: "%sから%sへオンライン変換"
    short: "無料で強力な%sから%sへのオンラインツール"
    intro: "使いやすいオンライン%sから%sへのコンバーター。直感的な変換ツールでテーブルデータを簡単に変換できます。高速、信頼性が高く、ユーザーフレンドリーです。"
  generator:
    title: "オンライン%sエディター＆ジェネレーター"
    short: "包括的な機能を備えたプロフェッショナルな%sオンライン生成ツール"
    intro: "使いやすいオンライン%sジェネレーター＆テーブルエディター。直感的なツールとリアルタイムプレビューで、プロフェッショナルなデータテーブルを簡単に作成できます。"
navbar:
  search:
    placeholder: "コンバーターを検索..."
  sponsor: "コーヒーを買ってください"
  extension: "拡張機能"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "データソース"
    placeholder: "%sデータを貼り付けるか、%sファイルをここにドラッグしてください"
    example: "例"
    upload: "ファイルをアップロード"
    extract:
      enter: "ウェブページから抽出"
      intro: "テーブルデータを含むウェブページのURLを入力して、構造化データを自動抽出します"
      btn: "%sを抽出"
    excel:
      sheet: "ワークシート"
      none: "なし"
  tableEditor:
    title: "オンラインテーブルエディター"
    undo: "元に戻す"
    redo: "やり直し"
    transpose: "転置"
    clear: "クリア"
    deleteBlank: "空行削除"
    deleteDuplicate: "重複削除"
    uppercase: "大文字"
    lowercase: "小文字"
    capitalize: "先頭大文字"
    replace:
      replace: "検索と置換（正規表現対応）"
      subst: "置換文字列..."
      btn: "すべて置換"
  tableGenerator:
    title: "テーブルジェネレーター"
    sponsor: "コーヒーを買ってください"
    copy: "クリップボードにコピー"
    download: "ファイルをダウンロード"
    tooltip:
      html:
        escape: "表示エラーを防ぐためにHTML特殊文字（&, <, >, \", '）をエスケープ"
        div: "従来のTABLEタグの代わりにDIV+CSSレイアウトを使用、レスポンシブデザインにより適している"
        minify: "空白と改行を削除して圧縮されたHTMLコードを生成"
        thead: "標準的なテーブルヘッド（&lt;thead&gt;）とボディ（&lt;tbody&gt;）構造を生成"
        tableCaption: "テーブルの上に説明的なタイトルを追加（&lt;caption&gt;要素）"
        tableClass: "簡単なスタイルカスタマイズのためにテーブルにCSSクラス名を追加"
        tableId: "JavaScript操作のためにテーブルに一意のID識別子を設定"
      jira:
        escape: "Jiraテーブル構文との競合を避けるためにパイプ文字（|）をエスケープ"
      json:
        parsingJSON: "セル内のJSON文字列をオブジェクトにインテリジェントに解析"
        minify: "ファイルサイズを削減するためにコンパクトな単一行JSON形式を生成"
        format: "出力JSON データ構造を選択：オブジェクト配列、2D配列など"
      latex:
        escape: "適切なコンパイルを確保するためにLaTeX特殊文字（%, &, _, #, $など）をエスケープ"
        ht: "ページ上のテーブル位置を制御するためにフロート位置パラメータ[!ht]を追加"
        mwe: "完全なLaTeXドキュメントを生成"
        tableAlign: "ページ上のテーブルの水平配置を設定"
        tableBorder: "テーブル境界スタイルを設定：境界なし、部分境界、完全境界"
        label: "\\ref{}コマンドの相互参照用にテーブルラベルを設定"
        caption: "テーブルの上または下に表示するテーブルキャプションを設定"
        location: "テーブルキャプション表示位置を選択：上または下"
        tableType: "テーブル環境タイプを選択：tabular、longtable、arrayなど"
      markdown:
        escape: "フォーマット競合を避けるためにMarkdown特殊文字（*, _, |, \\など）をエスケープ"
        pretty: "より美しいテーブル形式を生成するために列幅を自動調整"
        simple: "外側境界の縦線を省略した簡略化構文を使用"
        boldFirstRow: "最初の行のテキストを太字にする"
        boldFirstColumn: "最初の列のテキストを太字にする"
        firstHeader: "最初の行をヘッダーとして扱い、区切り線を追加"
        textAlign: "列テキストの配置を設定：左、中央、右"
        multilineHandling: "複数行テキストの処理：改行を保持、\\nにエスケープ、&lt;br&gt;タグを使用"

        includeLineNumbers: "テーブルの左側に行番号列を追加"
      magic:
        builtin: "事前定義された共通テンプレート形式を選択"
        rowsTpl: "<table> <tr> <th>マジック構文</th> <th>説明</th> <th>JSメソッドサポート</th> </tr> <tr> <td>{h1} {h2} ...</td> <td><b>h</b>eadingの1番目、2番目...フィールド、別名{hA} {hB} ...</td> <td>文字列メソッド</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>現在の行の1番目、2番目...フィールド、別名{$A} {$B} ...</td> <td>文字列メソッド</td> </tr> <tr> <td>{F,} {F;}</td> <td><b>F</b>の後の文字列で現在の行を分割</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>現在の<b>R</b>owの<b>N</b>umber行番号（1または100から）</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>R</b>owsの<b>E</b>nd line <b>N</b>umber </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>JavaScriptコードを実<b>x</b>行、例：{x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> 中括弧{...}を出力するためにバックスラッシュ<b>\\</b>を使用 </td> <td></td> </tr></table>"
        headerTpl: "ヘッダーセクション用のカスタム出力テンプレート"
        footerTpl: "フッターセクション用のカスタム出力テンプレート"
      textile:
        escape: "フォーマット競合を避けるためにTextile構文文字（|, ., -, ^）をエスケープ"
        rowHeader: "最初の行をヘッダー行として設定"
        thead: "テーブルヘッドとボディ用のTextile構文マーカーを追加"
      xml:
        escape: "有効なXMLを確保するためにXML特殊文字（&lt;, &gt;, &amp;, \", '）をエスケープ"
        minify: "余分な空白を削除して圧縮されたXML出力を生成"
        rootElement: "XMLルート要素タグ名を設定"
        rowElement: "各データ行のXML要素タグ名を設定"
        declaration: "XML宣言ヘッダー（&lt;?xml version=\"1.0\"?&gt;）を追加"
        attributes: "子要素の代わりにXML属性としてデータを出力"
        cdata: "特殊文字を保護するためにテキストコンテンツをCDATAでラップ"
        encoding: "XMLドキュメントの文字エンコーディング形式を設定"
        indentation: "XMLインデント文字を選択：スペースまたはタブ"
      yaml:
        indentSize: "YAML階層インデントのスペース数を設定（通常2または4）"
        arrayStyle: "配列形式：ブロック（1行に1項目）またはフロー（インライン形式）"
        quotationStyle: "文字列引用符スタイル：引用符なし、単一引用符、二重引用符"
      pdf:
        theme: "プロフェッショナル文書用のPDF表ビジュアルスタイルを選択"
        headerColor: "PDF表ヘッダーの背景色を選択"
        showHead: "PDFページでのヘッダー表示を制御"
        docTitle: "PDF文書のオプションタイトル"
        docDescription: "PDF文書のオプション説明テキスト"
      csv:
        bom: "ExcelやOther softwareがエンコーディングを認識するのを助けるためにUTF-8バイトオーダーマークを追加"
      excel:
        autoWidth: "コンテンツに基づいて列幅を自動調整"
        protectSheet: "パスワードでワークシート保護を有効化：tableconvert.com"
      sql:
        primaryKey: "CREATE TABLE文の主キーフィールド名を指定"
        dialect: "データベースタイプを選択、引用符とデータタイプ構文に影響"
      ascii:
        forceSep: "各データ行間に区切り線を強制"
        style: "ASCIIテーブル境界描画スタイルを選択"
        comment: "テーブル全体をラップするコメントマーカーを追加"
      mediawiki:
        minify: "余分な空白を削除して出力コードを圧縮"
        header: "最初の行をヘッダースタイルとしてマーク"
        sort: "テーブルクリックソート機能を有効化"
      asciidoc:
        minify: "AsciiDoc形式出力を圧縮"
        firstHeader: "最初の行をヘッダー行として設定"
        lastFooter: "最後の行をフッター行として設定"
        title: "テーブルにタイトルテキストを追加"
      tracwiki:
        rowHeader: "最初の行をヘッダーとして設定"
        colHeader: "最初の列をヘッダーとして設定"
      bbcode:
        minify: "BBCode出力形式を圧縮"
      restructuredtext:
        style: "reStructuredTextテーブル境界スタイルを選択"
        forceSep: "区切り線を強制"
    label:
      ascii:
        forceSep: "行区切り"
        style: "境界スタイル"
        comment: "コメントラッパー"
      restructuredtext:
        style: "境界スタイル"
        forceSep: "区切りを強制"
      bbcode:
        minify: "出力を最小化"
      csv:
        doubleQuote: "二重引用符ラップ"
        delimiter: "フィールド区切り文字"
        bom: "UTF-8 BOM"
        valueDelimiter: "値区切り文字"
        rowDelimiter: "行区切り文字"
        prefix: "行プレフィックス"
        suffix: "行サフィックス"
      excel:
        autoWidth: "自動幅"
        textFormat: "テキスト形式"
        protectSheet: "シート保護"
        boldFirstRow: "最初の行を太字"
        boldFirstColumn: "最初の列を太字"
        sheetName: "シート名"
      html:
        escape: "HTML文字をエスケープ"
        div: "DIVテーブル"
        minify: "コードを最小化"
        thead: "テーブルヘッド構造"
        tableCaption: "テーブルキャプション"
        tableClass: "テーブルクラス"
        tableId: "テーブルID"
        rowHeader: "行ヘッダー"
        colHeader: "列ヘッダー"
      jira:
        escape: "文字をエスケープ"
        rowHeader: "行ヘッダー"
        colHeader: "列ヘッダー"
      json:
        parsingJSON: "JSONを解析"
        minify: "出力を最小化"
        format: "データ形式"
        rootName: "ルートオブジェクト名"
        indentSize: "インデントサイズ"
      jsonlines:
        parsingJSON: "JSONを解析"
        format: "データ形式"
      latex:
        escape: "LaTeXテーブル文字をエスケープ"
        ht: "フロート位置"
        mwe: "完全なドキュメント"
        tableAlign: "テーブル配置"
        tableBorder: "境界スタイル"
        label: "参照ラベル"
        caption: "テーブルキャプション"
        location: "キャプション位置"
        tableType: "テーブルタイプ"
        boldFirstRow: "最初の行を太字"
        boldFirstColumn: "最初の列を太字"
        textAlign: "テキスト配置"
        borders: "境界設定"
      markdown:
        escape: "文字をエスケープ"
        pretty: "美しいMarkdownテーブル"
        simple: "シンプルなMarkdown形式"
        boldFirstRow: "最初の行を太字"
        boldFirstColumn: "最初の列を太字"
        firstHeader: "最初のヘッダー"
        textAlign: "テキスト配置"
        multilineHandling: "複数行処理"

        includeLineNumbers: "行番号を追加"
        align: "配置"
      mediawiki:
        minify: "コードを最小化"
        header: "ヘッダーマークアップ"
        sort: "ソート可能"
      asciidoc:
        minify: "形式を最小化"
        firstHeader: "最初のヘッダー"
        lastFooter: "最後のフッター"
        title: "テーブルタイトル"
      tracwiki:
        rowHeader: "行ヘッダー"
        colHeader: "列ヘッダー"
      sql:
        drop: "テーブルを削除（存在する場合）"
        create: "テーブルを作成"
        oneInsert: "バッチ挿入"
        table: "テーブル名"
        dialect: "データベースタイプ"
        primaryKey: "主キー"
      magic:
        builtin: "組み込みテンプレート"
        rowsTpl: "行テンプレート、構文 ->"
        headerTpl: "ヘッダーテンプレート"
        footerTpl: "フッターテンプレート"
      textile:
        escape: "文字をエスケープ"
        rowHeader: "行ヘッダー"
        thead: "テーブルヘッド構文"
      xml:
        escape: "XML文字をエスケープ"
        minify: "出力を最小化"
        rootElement: "ルート要素"
        rowElement: "行要素"
        declaration: "XML宣言"
        attributes: "属性モード"
        cdata: "CDATAラッパー"
        encoding: "エンコーディング"
        indentSize: "インデントサイズ"
      yaml:
        indentSize: "インデントサイズ"
        arrayStyle: "配列スタイル"
        quotationStyle: "引用符スタイル"
      pdf:
        theme: "PDF表テーマ"
        headerColor: "PDFヘッダー色"
        showHead: "PDFヘッダー表示"
        docTitle: "PDF文書タイトル"
        docDescription: "PDF文書説明"
sidebar:
  all: "すべての変換ツール"
  dataSource:
    title: "データソース"
    description:
      converter: "%sを%sに変換するためにインポート。ファイルアップロード、オンライン編集、ウェブデータ抽出をサポート。"
      generator: "手動入力、ファイルインポート、テンプレート生成を含む複数の入力方法をサポートしてテーブルデータを作成。"
  tableEditor:
    title: "オンラインテーブルエディター"
    description:
      converter: "テーブルエディターを使用して%sをオンラインで処理。空行削除、重複除去、ソート、検索と置換をサポートするExcelライクな操作体験。"
      generator: "Excelライクな操作体験を提供する強力なオンラインテーブルエディター。空行削除、重複除去、ソート、検索と置換をサポート。"
  tableGenerator:
    title: "テーブルジェネレーター"
    description:
      converter: "テーブルジェネレーターのリアルタイムプレビューで%sを迅速に生成。豊富なエクスポートオプション、ワンクリックコピー＆ダウンロード。"
      generator: "異なる使用シナリオに対応するために%sデータを複数の形式でエクスポート。カスタムオプションとリアルタイムプレビューをサポート。"
footer:
  changelog: "変更履歴"
  sponsor: "スポンサー"
  contact: "お問い合わせ"
  privacyPolicy: "プライバシーポリシー"
  about: "概要"
  resources: "リソース"
  popularConverters: "人気のコンバーター"
  popularGenerators: "人気のジェネレーター"
  dataSecurity: "データは安全です - すべての変換はブラウザ内で実行されます."
converters:
  Markdown:
    alias: "Markdownテーブル"
    what: "Markdownは、技術文書、ブログコンテンツ作成、ウェブ開発で広く使用される軽量マークアップ言語です。そのテーブル構文は簡潔で直感的で、テキスト配置、リンク埋め込み、フォーマットをサポートします。プログラマーや技術ライターの好ましいツールで、GitHub、GitLab、その他のコードホスティングプラットフォームと完全に互換性があります。"
    step1: "データソースエリアにMarkdownテーブルデータを貼り付けるか、.mdファイルを直接ドラッグアンドドロップしてアップロード。ツールは自動的にテーブル構造とフォーマットを解析し、複雑なネストされたコンテンツと特殊文字処理をサポートします。"
    step3: "リアルタイムで標準Markdownテーブルコードを生成し、複数の配置方法、テキストの太字化、行番号追加、その他の高度なフォーマット設定をサポート。生成されたコードはGitHubと主要なMarkdownエディターと完全に互換性があり、ワンクリックコピーですぐに使用可能。"
    from_alias: "Markdownテーブルファイル"
    to_alias: "Markdownテーブル形式"
  Magic:
    alias: "カスタムテンプレート"
    what: "Magicテンプレートは、このツールの独自の高度なデータジェネレーターで、ユーザーがカスタムテンプレート構文を通じて任意の形式のデータ出力を作成できます。変数置換、条件判定、ループ処理をサポート。複雑なデータ変換ニーズとパーソナライズされた出力形式を処理するための究極のソリューションで、特に開発者とデータエンジニアに適しています。"
    step1: "組み込みの共通テンプレートを選択するか、カスタムテンプレート構文を作成。複雑なデータ構造とビジネスロジックを処理できる豊富な変数と関数をサポート。"
    step3: "カスタム形式要件を完全に満たすデータ出力を生成。複雑なデータ変換ロジックと条件処理をサポートし、データ処理効率と出力品質を大幅に向上。バッチデータ処理のための強力なツール。"
    from_alias: "テーブルデータ"
    to_alias: "カスタム形式出力"
  CSV:
    alias: "CSV"
    what: "CSV（Comma-Separated Values）は最も広く使用されるデータ交換形式で、Excel、Google Sheets、データベースシステム、さまざまなデータ分析ツールで完全にサポートされています。そのシンプルな構造と強力な互換性により、データ移行、バッチインポート/エクスポート、クロスプラットフォームデータ交換の標準形式となり、ビジネス分析、データサイエンス、システム統合で広く使用されています。"
    step1: "CSVファイルをアップロードするか、CSVデータを直接貼り付け。ツールはさまざまな区切り文字（カンマ、タブ、セミコロン、パイプなど）をインテリジェントに認識し、データタイプとエンコーディング形式を自動検出し、大きなファイルと複雑なデータ構造の高速解析をサポート。"
    step3: "カスタム区切り文字、引用符スタイル、エンコーディング形式、BOMマーク設定をサポートする標準CSV形式ファイルを生成。ターゲットシステムとの完全な互換性を確保し、エンタープライズレベルのデータ処理ニーズを満たすダウンロードと圧縮オプションを提供。"
    from_alias: "CSVデータファイル"
    to_alias: "CSV標準形式"
  JSON:
    alias: "JSON配列"
    what: "JSON（JavaScript Object Notation）は、現代のウェブアプリケーション、REST API、マイクロサービスアーキテクチャの標準テーブルデータ形式です。その明確な構造と効率的な解析により、フロントエンドとバックエンドのデータ相互作用、設定ファイルストレージ、NoSQLデータベースで広く使用されています。ネストされたオブジェクト、配列構造、複数のデータタイプをサポートし、現代のソフトウェア開発に不可欠なテーブルデータとなっています。"
    step1: "JSONファイルをアップロードするか、JSON配列を貼り付け。オブジェクト配列、ネストされた構造、複雑なデータタイプの自動認識と解析をサポート。ツールはJSON構文をインテリジェントに検証し、エラープロンプトを提供。"
    step3: "複数のJSON形式出力を生成：標準オブジェクト配列、2D配列、列配列、キー値ペア形式。美化出力、圧縮モード、カスタムルートオブジェクト名、インデント設定をサポートし、さまざまなAPIインターフェースとデータストレージニーズに完全に適応。"
    from_alias: "JSON配列ファイル"
    to_alias: "JSON標準形式"
  JSONLines:
    alias: "JSONLines形式"
    what: "JSON Lines（NDJSONとしても知られる）は、ビッグデータ処理とストリーミングデータ伝送の重要な形式で、各行に独立したJSONオブジェクトが含まれています。ログ分析、データストリーム処理、機械学習、分散システムで広く使用されています。増分処理と並列コンピューティングをサポートし、大規模な構造化データを処理するための理想的な選択肢となっています。"
    step1: "JSONLinesファイルをアップロードするか、データを貼り付け。ツールはJSONオブジェクトを行ごとに解析し、大きなファイルのストリーミング処理とエラー行スキップ機能をサポート。"
    step3: "各行が完全なJSONオブジェクトを出力する標準JSONLines形式を生成。ストリーミング処理、バッチインポート、ビッグデータ分析シナリオに適し、データ検証と形式最適化をサポート。"
    from_alias: "JSONLinesデータ"
    to_alias: "JSONLinesストリーミング形式"
  XML:
    alias: "XML"
    what: "XML（eXtensible Markup Language）は、厳密な構文仕様と強力な検証メカニズムを持つエンタープライズレベルのデータ交換と設定管理の標準形式です。ウェブサービス、設定ファイル、ドキュメントストレージ、システム統合で広く使用されています。名前空間、スキーマ検証、XSLT変換をサポートし、エンタープライズアプリケーションにとって重要なテーブルデータとなっています。"
    step1: "XMLファイルをアップロードするか、XMLデータを貼り付け。ツールは自動的にXML構造を解析してテーブル形式に変換し、名前空間、属性処理、複雑なネストされた構造をサポート。"
    step3: "XML標準に準拠したXML出力を生成。カスタムルート要素、行要素名、属性モード、CDATAラッピング、文字エンコーディング設定をサポート。データの整合性と互換性を確保し、エンタープライズレベルのアプリケーション要件を満たします。"
    from_alias: "XMLデータファイル"
    to_alias: "XML標準形式"
  YAML:
    alias: "YAML設定"
    what: "YAMLは、明確な階層構造と簡潔な構文で有名な人間に優しいデータシリアライゼーション標準です。設定ファイル、DevOpsツールチェーン、Docker Compose、Kubernetesデプロイメントで広く使用されています。その強力な可読性と簡潔な構文により、現代のクラウドネイティブアプリケーションと自動化オペレーションの重要な設定形式となっています。"
    step1: "YAMLファイルをアップロードするか、YAMLデータを貼り付け。ツールはYAML構造をインテリジェントに解析し、構文の正確性を検証し、マルチドキュメント形式と複雑なデータタイプをサポート。"
    step3: "ブロックとフロー配列スタイル、複数の引用符設定、カスタムインデント、コメント保持をサポートする標準YAML形式出力を生成。出力YAMLファイルがさまざまなパーサーと設定システムと完全に互換性があることを確保。"
    from_alias: "YAML設定ファイル"
    to_alias: "YAML標準形式"
  MySQL:
      alias: "MySQLクエリ結果"
      what: "MySQLは世界で最も人気のあるオープンソースリレーショナルデータベース管理システムで、高性能、信頼性、使いやすさで有名です。ウェブアプリケーション、エンタープライズシステム、データ分析プラットフォームで広く使用されています。MySQLクエリ結果は通常、構造化されたテーブルデータを含み、データベース管理とデータ分析作業における重要なデータソースとして機能します。"
      step1: "MySQLクエリ出力結果をデータソースエリアに貼り付け。ツールは自動的にMySQLコマンドライン出力形式を認識して解析し、さまざまなクエリ結果スタイルと文字エンコーディングをサポートし、ヘッダーとデータ行をインテリジェントに処理。"
      step3: "MySQLクエリ結果を複数のテーブルデータ形式に迅速に変換し、データ分析、レポート生成、クロスシステムデータ移行、データ検証を促進。データベース管理者とデータアナリストのための実用的なツール。"
      from_alias: "MySQLクエリ出力"
      to_alias: "MySQLテーブルデータ"
  SQL:
    alias: "Insert SQL"
    what: "SQL（Structured Query Language）は、データクエリ、挿入、更新、削除操作に使用されるリレーショナルデータベースの標準操作言語です。データベース管理のコア技術として、SQLはデータ分析、ビジネスインテリジェンス、ETL処理、データウェアハウス構築で広く使用されています。データプロフェッショナルにとって不可欠なスキルツールです。"
    step1: "INSERT SQL文を貼り付けるか、.sqlファイルをアップロード。ツールはSQL構文をインテリジェントに解析してテーブルデータを抽出し、複数のSQL方言と複雑なクエリ文処理をサポート。"
    step3: "標準SQL INSERT文とテーブル作成文を生成。複数のデータベース方言（MySQL、PostgreSQL、SQLite、SQL Server、Oracle）をサポートし、データタイプマッピング、文字エスケープ、主キー制約を自動処理。生成されたSQLコードが直接実行できることを確保。"
    from_alias: "SQLデータファイル"
    to_alias: "SQL標準文"
  Qlik:
      alias: "Qlikテーブル"
      what: "Qlikは、TableauやMicrosoftと並んで、データ可視化、エグゼクティブダッシュボード、セルフサービスビジネスインテリジェンス製品を専門とするソフトウェアベンダーです。"
      step1: ""
      step3: "最後に、[テーブルジェネレーター](#TableGenerator)が変換結果を表示します。Qlik Sense、Qlik AutoML、QlikView、またはその他のQlik対応ソフトウェアで使用してください。"
      from_alias: "Qlikテーブル"
      to_alias: "Qlikテーブル"
  DAX:
      alias: "DAXテーブル"
      what: "DAX（Data Analysis Expressions）は、計算列、メジャー、カスタムテーブルを作成するためにMicrosoft Power BI全体で使用されるプログラミング言語です。"
      step1: ""
      step3: "最後に、[テーブルジェネレーター](#TableGenerator)が変換結果を表示します。予想通り、Microsoft Power BI、Microsoft Analysis Services、Microsoft Power Pivot for Excelを含むいくつかのMicrosoft製品で使用されています。"
      from_alias: "DAXテーブル"
      to_alias: "DAXテーブル"
  Firebase:
    alias: "Firebaseリスト"
    what: "Firebaseは、リアルタイムデータベース、クラウドストレージ、認証、クラッシュレポートなどのホストされたバックエンドサービスを提供するBaaSアプリケーション開発プラットフォームです。"
    step1: ""
    step3: "最後に、[テーブルジェネレーター](#TableGenerator)が変換結果を表示します。その後、Firebase APIのpushメソッドを使用してFirebaseデータベースのデータリストに追加できます。"
    from_alias: "Firebaseリスト"
    to_alias: "Firebaseリスト"
  HTML:
    alias: "HTMLテーブル"
    what: "HTMLテーブルは、table、tr、tdなどのタグで構築されたウェブページで構造化データを表示する標準的な方法です。豊富なスタイルカスタマイゼーション、レスポンシブレイアウト、インタラクティブ機能をサポート。ウェブサイト開発、データ表示、レポート生成で広く使用され、フロントエンド開発とウェブデザインの重要なコンポーネントとして機能します。"
    step1: "テーブルを含むHTMLコードを貼り付けるか、HTMLファイルをアップロード。ツールは自動的にページからテーブルデータを認識して抽出し、複雑なHTML構造、CSSスタイル、ネストされたテーブル処理をサポート。"
    step3: "thead/tbody構造、CSSクラス設定、テーブルキャプション、行/列ヘッダー、レスポンシブ属性設定をサポートするセマンティックHTMLテーブルコードを生成。生成されたテーブルコードがウェブ標準を満たし、良好なアクセシビリティとSEOフレンドリーであることを確保。"
    from_alias: "HTMLウェブテーブル"
    to_alias: "HTML標準テーブル"
  Excel:
    alias: "Excel"
    what: "Microsoft Excelは世界で最も人気のあるスプレッドシートソフトウェアで、ビジネス分析、財務管理、データ処理、レポート作成で広く使用されています。その強力なデータ処理機能、豊富な関数ライブラリ、柔軟な可視化機能により、オフィス自動化とデータ分析の標準ツールとなり、ほぼすべての業界と分野で幅広く応用されています。"
    step1: "Excelファイル（.xlsx、.xls形式をサポート）をアップロードするか、Excelからテーブルデータを直接コピーして貼り付け。ツールはマルチワークシート処理、複雑な形式認識、大きなファイルの高速解析をサポートし、結合されたセルとデータタイプを自動処理。"
    step3: "Excelに直接貼り付けるか、標準.xlsxファイルとしてダウンロードできるExcel互換テーブルデータを生成。ワークシート命名、セル書式設定、自動列幅、ヘッダースタイリング、データ検証設定をサポート。出力Excelファイルがプロフェッショナルな外観と完全な機能を持つことを確保。"
    from_alias: "Excelスプレッドシート"
    to_alias: "Excel標準形式"
  LaTeX:
    alias: "LaTeXテーブル"
    what: "LaTeXは、特に学術論文、技術文書、科学出版物の作成に適したプロフェッショナルな文書組版システムです。そのテーブル機能は強力で、複雑な数式、精密なレイアウト制御、高品質なPDF出力をサポートします。学術界と科学出版の標準ツールで、学術論文、学位論文、技術マニュアルの組版で広く使用されています。"
    step1: "LaTeXテーブルコードを貼り付けるか、.texファイルをアップロード。ツールはLaTeXテーブル構文を解析してデータコンテンツを抽出し、複数のテーブル環境（tabular、longtable、arrayなど）と複雑な形式コマンドをサポート。"
    step3: "複数のテーブル環境選択、境界スタイル設定、キャプション位置設定、文書クラス指定、パッケージ管理をサポートするプロフェッショナルなLaTeXテーブルコードを生成。完全にコンパイル可能なLaTeX文書を生成でき、出力テーブルが学術出版標準を満たすことを確保。"
    from_alias: "LaTeX文書テーブル"
    to_alias: "LaTeXプロフェッショナル形式"
  ASCII:
    alias: "ASCIIテーブル"
    what: "ASCIIテーブルは、プレーンテキスト文字を使用してテーブルの境界と構造を描画し、最高の互換性と可搬性を提供します。すべてのテキストエディター、ターミナル環境、オペレーティングシステムと互換性があります。コード文書、技術マニュアル、READMEファイル、コマンドラインツール出力で広く使用されています。プログラマーとシステム管理者の好ましいデータ表示形式です。"
    step1: "ASCIIテーブルを含むテキストファイルをアップロードするか、テーブルデータを直接貼り付け。ツールはASCIIテーブル構造をインテリジェントに認識して解析し、複数の境界スタイルと配置形式をサポート。"
    step3: "複数の境界スタイル（単線、二重線、角丸など）、テキスト配置方法、自動列幅をサポートする美しいプレーンテキストASCIIテーブルを生成。生成されたテーブルはコードエディター、文書、コマンドラインで完璧に表示されます。"
    from_alias: "ASCIIテキストテーブル"
    to_alias: "ASCII標準形式"
  MediaWiki:
    alias: "MediaWikiテーブル"
    what: "MediaWikiは、Wikipediaなどの有名なwikiサイトで使用されるオープンソースソフトウェアプラットフォームです。そのテーブル構文は簡潔でありながら強力で、テーブルスタイルのカスタマイゼーション、ソート機能、リンク埋め込みをサポートします。知識管理、協調編集、コンテンツ管理システムで広く使用され、wiki百科事典と知識ベースを構築するためのコア技術として機能します。"
    step1: "MediaWikiテーブルコードを貼り付けるか、wikiソースファイルをアップロード。ツールはwikiマークアップ構文を解析してテーブルデータを抽出し、複雑なwiki構文とテンプレート処理をサポート。"
    step3: "ヘッダースタイル設定、セル配置、ソート機能有効化、コード圧縮オプションをサポートする標準MediaWikiテーブルコードを生成。生成されたコードはwikiページ編集に直接使用でき、MediaWikiプラットフォームでの完璧な表示を確保。"
    from_alias: "MediaWikiソースコード"
    to_alias: "MediaWikiテーブル構文"
  TracWiki:
    alias: "TracWikiテーブル"
    what: "Tracは、簡略化されたwiki構文を使用してテーブルコンテンツを作成するウェブベースのプロジェクト管理とバグ追跡システムです。"
    step1: "TracWikiファイルをアップロードするか、テーブルデータを貼り付け。"
    step3: "行/列ヘッダー設定をサポートするTracWiki互換テーブルコードを生成し、プロジェクト文書管理を促進。"
    from_alias: "TracWikiテーブル"
    to_alias: "TracWiki形式"
  AsciiDoc:
    alias: "AsciiDocテーブル"
    what: "AsciiDocは、HTML、PDF、マニュアルページ、その他の形式に変換できる軽量マークアップ言語で、技術文書作成で広く使用されています。"
    step1: "AsciiDocファイルをアップロードするか、データを貼り付け。"
    step3: "ヘッダー、フッター、タイトル設定をサポートするAsciiDocテーブル構文を生成し、AsciiDocエディターで直接使用可能。"
    from_alias: "AsciiDocテーブル"
    to_alias: "AsciiDoc形式"
  reStructuredText:
    alias: "reStructuredTextテーブル"
    what: "reStructuredTextは、Pythonコミュニティの標準文書形式で、豊富なテーブル構文をサポートし、Sphinx文書生成で一般的に使用されています。"
    step1: ".rstファイルをアップロードするか、reStructuredTextデータを貼り付け。"
    step3: "複数の境界スタイルをサポートする標準reStructuredTextテーブルを生成し、Sphinx文書プロジェクトで直接使用可能。"
    from_alias: "reStructuredTextテーブル"
    to_alias: "reStructuredText形式"
  PHP:
    alias: "PHP配列"
    what: "PHPは人気のあるサーバーサイドスクリプト言語で、配列がそのコアデータ構造であり、ウェブ開発とデータ処理で広く使用されています。"
    step1: "PHP配列を含むファイルをアップロードするか、データを直接貼り付け。"
    step3: "PHPプロジェクトで直接使用できる標準PHP配列コードを生成し、連想配列とインデックス配列形式をサポート。"
    from_alias: "PHP配列"
    to_alias: "PHPコード"
  Ruby:
    alias: "Ruby配列"
    what: "Rubyは簡潔でエレガントな構文を持つ動的オブジェクト指向プログラミング言語で、配列が重要なデータ構造です。"
    step1: "Rubyファイルをアップロードするか、配列データを貼り付け。"
    step3: "Ruby構文仕様に準拠したRuby配列コードを生成し、Rubyプロジェクトで直接使用可能。"
    from_alias: "Ruby配列"
    to_alias: "Rubyコード"
  ASP:
    alias: "ASP配列"
    what: "ASP（Active Server Pages）は、動的ウェブページを開発するための複数のプログラミング言語をサポートするMicrosoftのサーバーサイドスクリプト環境です。"
    step1: "ASPファイルをアップロードするか、配列データを貼り付け。"
    step3: "VBScriptとJScript構文をサポートするASP互換配列コードを生成し、ASP.NETプロジェクトで使用可能。"
    from_alias: "ASP配列"
    to_alias: "ASPコード"
  ActionScript:
    alias: "ActionScript配列"
    what: "ActionScriptは、主にAdobe FlashとAIRアプリケーション開発に使用されるオブジェクト指向プログラミング言語です。"
    step1: ".asファイルをアップロードするか、ActionScriptデータを貼り付け。"
    step3: "AS3構文標準に準拠したActionScript配列コードを生成し、FlashとFlexプロジェクト開発で使用可能。"
    from_alias: "ActionScript配列"
    to_alias: "ActionScriptコード"
  BBCode:
    alias: "BBCodeテーブル"
    what: "BBCodeは、フォーラムやオンラインコミュニティで一般的に使用される軽量マークアップ言語で、テーブルサポートを含むシンプルなフォーマット機能を提供します。"
    step1: "BBCodeを含むファイルをアップロードするか、データを貼り付け。"
    step3: "フォーラム投稿とコミュニティコンテンツ作成に適したBBCodeテーブルコードを生成し、圧縮出力形式をサポート。"
    from_alias: "BBCodeテーブル"
    to_alias: "BBCode形式"
  PDF:
    alias: "PDFテーブル"
    what: "PDF（Portable Document Format）は、固定レイアウト、一貫した表示、高品質印刷特性を持つクロスプラットフォーム文書標準です。正式文書、レポート、請求書、契約書、学術論文で広く使用されています。ビジネスコミュニケーションと文書アーカイブの好ましい形式で、異なるデバイスとオペレーティングシステム間で完全に一貫した視覚効果を確保します。"
    step1: "任意の形式でテーブルデータをインポート。ツールは自動的にデータ構造を分析してインテリジェントなレイアウト設計を実行し、大きなテーブルの自動ページネーションと複雑なデータタイプ処理をサポート。"
    step3: "複数のプロフェッショナルテーマスタイル（ビジネス、学術、ミニマリストなど）、多言語フォント、自動ページネーション、透かし追加、印刷最適化をサポートする高品質PDFテーブルファイルを生成。出力PDF文書がプロフェッショナルな外観を持ち、ビジネスプレゼンテーションと正式出版に直接使用可能であることを確保。"
    from_alias: "テーブルデータ"
    to_alias: "PDFプロフェッショナル文書"
  JPEG:
    alias: "JPEG画像"
    what: "JPEGは、優れた圧縮効果と広い互換性を持つ最も広く使用されるデジタル画像形式です。その小さなファイルサイズと高速読み込み速度により、ウェブ表示、ソーシャルメディア共有、文書イラスト、オンラインプレゼンテーションに適しています。デジタルメディアとネットワーク通信の標準画像形式で、ほぼすべてのデバイスとソフトウェアで完全にサポートされています。"
    step1: "任意の形式でテーブルデータをインポート。ツールはインテリジェントなレイアウト設計と視覚最適化を実行し、最適なサイズと解像度を自動計算。"
    step3: "複数のテーマカラースキーム（ライト、ダーク、目に優しいなど）、適応レイアウト、テキスト明瞭度最適化、サイズカスタマイゼーションをサポートする高解像度JPEGテーブル画像を生成。オンライン共有、文書挿入、プレゼンテーション使用に適し、さまざまな表示デバイスで優れた視覚効果を確保。"
    from_alias: "テーブルデータ"
    to_alias: "JPEG高解像度画像"
  Jira:
    alias: "Jiraテーブル"
    what: "JIRAは、Atlassianが開発したプロフェッショナルなプロジェクト管理とバグ追跡ソフトウェアで、アジャイル開発、ソフトウェアテスト、プロジェクト協力で広く使用されています。そのテーブル機能は豊富なフォーマットオプションとデータ表示をサポートし、ソフトウェア開発チーム、プロジェクトマネージャー、品質保証担当者の要件管理、バグ追跡、進捗報告における重要なツールとして機能します。"
    step1: "テーブルデータを含むファイルをアップロードするか、データコンテンツを直接貼り付け。ツールは自動的にテーブルデータと特殊文字エスケープを処理。"
    step3: "ヘッダースタイル設定、セル配置、文字エスケープ処理、フォーマット最適化をサポートするJIRAプラットフォーム互換テーブルコードを生成。生成されたコードはJIRA課題説明、コメント、wikiページに直接貼り付けでき、JIRAシステムでの正しい表示とレンダリングを確保。"
    from_alias: "プロジェクトデータ"
    to_alias: "Jiraテーブル構文"
  Textile:
    alias: "Textileテーブル"
    what: "Textileは、シンプルで学習しやすい構文を持つ簡潔な軽量マークアップ言語で、コンテンツ管理システム、ブログプラットフォーム、フォーラムシステムで広く使用されています。そのテーブル構文は明確で直感的で、迅速なフォーマットとスタイル設定をサポート。コンテンツクリエイターとウェブサイト管理者の迅速な文書作成とコンテンツ公開のための理想的なツールです。"
    step1: "Textile形式ファイルをアップロードするか、テーブルデータを貼り付け。ツールはTextileマークアップ構文を解析してテーブルコンテンツを抽出。"
    step3: "ヘッダーマークアップ、セル配置、特殊文字エスケープ、フォーマット最適化をサポートする標準Textileテーブル構文を生成。生成されたコードは、Textileをサポートするcmsシステム、ブログプラットフォーム、文書システムで直接使用でき、正しいコンテンツレンダリングと表示を確保。"
    from_alias: "Textile文書"
    to_alias: "Textileテーブル構文"
  PNG:
    alias: "PNG画像"
    what: "PNG（Portable Network Graphics）は、優れた圧縮と透明度サポートを持つロスレス画像形式です。ウェブデザイン、デジタルグラフィックス、プロフェッショナル写真で広く使用されています。その高品質と広い互換性により、スクリーンショット、ロゴ、図表、鮮明な詳細と透明な背景を必要とする任意の画像に理想的です。"
    step1: "任意の形式でテーブルデータをインポート。ツールはインテリジェントなレイアウト設計と視覚最適化を実行し、PNG出力の最適なサイズと解像度を自動計算。"
    step3: "複数のテーマカラースキーム、透明な背景、適応レイアウト、テキスト明瞭度最適化をサポートする高品質PNGテーブル画像を生成。ウェブ使用、文書挿入、優れた視覚品質を持つプロフェッショナルプレゼンテーションに最適。"
    from_alias: "テーブルデータ"
    to_alias: "PNG高品質画像"
  TOML:
    alias: "TOML設定"
    what: "TOML（Tom's Obvious, Minimal Language）は、読み書きが簡単な設定ファイル形式です。曖昧さがなくシンプルになるように設計され、設定管理のための現代のソフトウェアプロジェクトで広く使用されています。その明確な構文と強い型付けにより、アプリケーション設定とプロジェクト設定ファイルの優れた選択肢となっています。"
    step1: "TOMLファイルをアップロードするか、設定データを貼り付け。ツールはTOML構文を解析して構造化された設定情報を抽出。"
    step3: "ネストされた構造、データタイプ、コメントをサポートする標準TOML形式を生成。生成されたTOMLファイルは、アプリケーション設定、ビルドツール、プロジェクト設定に最適。"
    from_alias: "TOML設定"
    to_alias: "TOML形式"
  INI:
    alias: "INI設定"
    what: "INIファイルは、多くのアプリケーションとオペレーティングシステムで使用されるシンプルな設定ファイルです。その直接的なキー値ペア構造により、手動で読み書きが簡単です。Windowsアプリケーション、レガシーシステム、人間の可読性が重要なシンプルな設定シナリオで広く使用されています。"
    step1: "INIファイルをアップロードするか、設定データを貼り付け。ツールはINI構文を解析してセクションベースの設定情報を抽出。"
    step3: "セクション、コメント、さまざまなデータタイプをサポートする標準INI形式を生成。生成されたINIファイルは、ほとんどのアプリケーションと設定システムと互換性があります。"
    from_alias: "INI設定"
    to_alias: "INI形式"
  Avro:
    alias: "Avroスキーマ"
    what: "Apache Avroは、豊富なデータ構造、コンパクトなバイナリ形式、スキーマ進化機能を提供するデータシリアライゼーションシステムです。ビッグデータ処理、メッセージキュー、分散システムで広く使用されています。そのスキーマ定義は複雑なデータタイプとバージョン互換性をサポートし、データエンジニアとシステムアーキテクトにとって重要なツールとなっています。"
    step1: "Avroスキーマファイルをアップロードするか、データを貼り付け。ツールはAvroスキーマ定義を解析してテーブル構造情報を抽出。"
    step3: "データタイプマッピング、フィールド制約、スキーマ検証をサポートする標準Avroスキーマ定義を生成。生成されたスキーマは、Hadoopエコシステム、Kafkaメッセージシステム、その他のビッグデータプラットフォームで直接使用可能。"
    from_alias: "Avroスキーマ"
    to_alias: "Avroデータ形式"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers（protobuf）は、構造化データをシリアライズするためのGoogleの言語中立、プラットフォーム中立、拡張可能なメカニズムです。マイクロサービス、API開発、データストレージで広く使用されています。その効率的なバイナリ形式と強い型付けにより、高性能アプリケーションと言語間通信に理想的です。"
    step1: ".protoファイルをアップロードするか、Protocol Buffer定義を貼り付け。ツールはprotobuf構文を解析してメッセージ構造情報を抽出。"
    step3: "メッセージタイプ、フィールドオプション、サービス定義をサポートする標準Protocol Buffer定義を生成。生成された.protoファイルは複数のプログラミング言語でコンパイル可能。"
    from_alias: "Protocol Buffer"
    to_alias: "Protobufスキーマ"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "PandasはPythonで最も人気のあるデータ分析ライブラリで、DataFrameがそのコアデータ構造です。強力なデータ操作、クリーニング、分析機能を提供し、データサイエンス、機械学習、ビジネスインテリジェンスで広く使用されています。Python開発者とデータアナリストにとって不可欠なツールです。"
    step1: "DataFrameコードを含むPythonファイルをアップロードするか、データを貼り付け。ツールはPandas構文を解析してDataFrame構造情報を抽出。"
    step3: "データタイプ仕様、インデックス設定、データ操作をサポートする標準Pandas DataFrameコードを生成。生成されたコードはPython環境でデータ分析と処理のために直接実行可能。"
    from_alias: "Pandas DataFrame"
    to_alias: "Pythonデータ構造"
  RDF:
    alias: "RDFトリプル"
    what: "RDF（Resource Description Framework）は、リソースに関する情報をグラフ形式で表現するように設計されたウェブ上のデータ交換の標準モデルです。セマンティックウェブ、知識グラフ、リンクデータアプリケーションで広く使用されています。そのトリプル構造は豊富なメタデータ表現とセマンティック関係を可能にします。"
    step1: "RDFファイルをアップロードするか、トリプルデータを貼り付け。ツールはRDF構文を解析してセマンティック関係とリソース情報を抽出。"
    step3: "さまざまなシリアライゼーション（RDF/XML、Turtle、N-Triples）をサポートする標準RDF形式を生成。生成されたRDFは、セマンティックウェブアプリケーション、知識ベース、リンクデータシステムで使用可能。"
    from_alias: "RDFデータ"
    to_alias: "RDFセマンティック形式"
  MATLAB:
    alias: "MATLAB配列"
    what: "MATLABは、エンジニアリングコンピューティング、データ分析、アルゴリズム開発で広く使用される高性能数値計算と可視化ソフトウェアです。その配列と行列操作は強力で、複雑な数学計算とデータ処理をサポートします。エンジニア、研究者、データサイエンティストにとって不可欠なツールです。"
    step1: "MATLAB .mファイルをアップロードするか、配列データを貼り付け。ツールはMATLAB構文を解析して配列構造情報を抽出。"
    step3: "多次元配列、データタイプ仕様、変数命名をサポートする標準MATLAB配列コードを生成。生成されたコードは、データ分析と科学計算のためにMATLAB環境で直接実行可能。"
    from_alias: "MATLAB配列"
    to_alias: "MATLABコード形式"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrameは、統計解析、データマイニング、機械学習で広く使用されるRプログラミング言語のコアデータ構造です。Rは統計計算とグラフィックスの最高のツールで、DataFrameは強力なデータ操作、統計解析、可視化機能を提供します。構造化データ解析に従事するデータサイエンティスト、統計学者、研究者にとって不可欠です。"
    step1: "Rデータファイルをアップロードするか、DataFrameコードを貼り付け。ツールはR構文を解析し、列タイプ、行名、データ内容を含むDataFrame構造情報を抽出。"
    step3: "データタイプ仕様、因子レベル、行/列名、R固有のデータ構造をサポートする標準R DataFrameコードを生成。生成されたコードは、統計解析とデータ処理のためにR環境で直接実行可能。"
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "変換を開始"
  start_generating: "生成を開始"
  api_docs: "APIドキュメント"
related:
  section_title: 'その他の{{ if and .from (ne .from "generator") }}{{ .from }}と{{ end }}{{ .to }}コンバーター'
  section_description: '{{ if and .from (ne .from "generator") }}{{ .from }}と{{ end }}{{ .to }}フォーマット用のコンバーターをさらに探索してください。プロフェッショナルなオンライン変換ツールで複数のフォーマット間でデータを変換できます。'
  title: "{{ .from }}から{{ .to }}へ"
howto:
  step2: "プロフェッショナル機能を備えた高度なオンラインテーブルエディターを使用してデータを編集します。空行の削除、重複の除去、データの転置、ソート、正規表現検索・置換、リアルタイムプレビューをサポート。すべての変更は自動的に%sフォーマットに正確で信頼性の高い結果で変換されます。"
  section_title: "{{ . }}の使用方法"
  converter_description: "ステップバイステップガイドで{{ .from }}から{{ .to }}への変換方法を学びましょう。高度な機能とリアルタイムプレビューを備えたプロフェッショナルなオンラインコンバーター。"
  generator_description: "オンラインジェネレーターでプロフェッショナルな{{ .to }}テーブルの作成方法を学びましょう。Excel風の編集、リアルタイムプレビュー、即座のエクスポート機能。"
extension:
  section_title: "テーブル検出・抽出拡張機能"
  section_description: "ワンクリックで任意のWebサイトからテーブルを抽出。Excel、CSV、JSONを含む30以上のフォーマットに即座に変換 - コピー＆ペースト不要。"
  features:
    extraction_title: "ワンクリックテーブル抽出"
    extraction_description: "コピー＆ペーストなしで任意のWebページから即座にテーブルを抽出 - プロフェッショナルなデータ抽出を簡単に"
    formats_title: "30以上のフォーマット変換サポート"
    formats_description: "高度なテーブルコンバーターで抽出したテーブルをExcel、CSV、JSON、Markdown、SQLなどに変換"
    detection_title: "スマートテーブル検出"
    detection_description: "高速なデータ抽出と変換のため、任意のWebページでテーブルを自動検出・ハイライト"
  hover_tip: "✨ 任意のテーブルにマウスを合わせて抽出アイコンを表示"
recommendations:
  section_title: "大学・専門家による推奨"
  section_description: "TableConvertは、信頼性の高いテーブル変換とデータ処理のため、大学、研究機関、開発チームの専門家に信頼されています。"
  cards:
    university_title: "ウィスコンシン大学マディソン校"
    university_description: "TableConvert.com - プロフェッショナルな無料オンラインテーブルコンバーター＆データフォーマットツール"
    university_link: "記事を読む"
    facebook_title: "データ専門家コミュニティ"
    facebook_description: "Facebookの開発者グループでデータアナリストと専門家によって共有・推奨"
    facebook_link: "投稿を見る"
    twitter_title: "開発者コミュニティ"
    twitter_description: "テーブル変換のためX（Twitter）で@xiaoying_ethと他の開発者によって推奨"
    twitter_link: "ツイートを見る"
faq:
  section_title: "よくある質問"
  section_description: "無料オンラインテーブルコンバーター、データフォーマット、変換プロセスに関する一般的な質問."
  what: "%sフォーマットとは何ですか？"
  howto_convert:
    question: "{{ . }}を無料で使用するには？"
    answer: "無料オンラインテーブルコンバーターを使用して{{ .from }}ファイルをアップロード、データを貼り付け、またはWebページから抽出してください。プロフェッショナルなコンバーターツールがリアルタイムプレビューと高度な編集機能で即座にデータを{{ .to }}フォーマットに変換します。変換結果を即座にダウンロードまたはコピーできます。"
  security:
    question: "このオンラインコンバーターを使用する際、データは安全ですか？"
    answer: "もちろんです！すべてのテーブル変換はブラウザ内でローカルに実行されます - データがデバイスを離れることはありません。オンラインコンバーターはすべてをクライアントサイドで処理し、完全なプライバシーとデータセキュリティを保証します。ファイルはサーバーに保存されません。"
  free:
    question: "TableConvertは本当に無料で使用できますか？"
    answer: "はい、TableConvertは完全に無料です！すべてのコンバーター機能、テーブルエディター、データジェネレーターツール、エクスポートオプションが費用、登録、隠れた料金なしで利用できます。無制限のファイルをオンラインで無料変換できます。"
  filesize:
    question: "オンラインコンバーターのファイルサイズ制限は？"
    answer: "無料オンラインテーブルコンバーターは最大10MBのファイルをサポートします。より大きなファイル、バッチ処理、エンタープライズニーズには、より高い制限を持つブラウザ拡張機能またはプロフェッショナルAPIサービスをご利用ください。"
stats:
  conversions: "変換されたテーブル"
  tables: "生成されたテーブル"
  formats: "データファイルフォーマット"
  rating: "ユーザー評価"
