site:
  fullname: "Table Convert"
  name: "TableConvert"
  subtitle: "Тегін Онлайн Кесте Түрлендіргіші және Генераторы"
  intro: "TableConvert - бұл Excel, CSV, JSON, Markdown, LaTeX, SQL және басқа 30+ форматтар арасында түрлендіруді қолдайтын тегін онлайн кесте түрлендіргіші және деректер генераторы құралы."
  followTwitter: "Бізді X-те бақылаңыз"
title:
  converter: "%s %s дейін"
  generator: "%s Генераторы"
post:
  tags:
    converter: "Түрлендіргіш"
    editor: "Редактор"
    generator: "Генератор"
    maker: "Құрастырушы"
  converter:
    title: "%s %s дейін Онлайн Түрлендіру"
    short: "Тегін және қуатты %s %s дейін онлайн құралы"
    intro: "Пайдалануға оңай онлайн %s %s дейін түрлендіргіші. Біздің интуитивті түрлендіру құралымен кесте деректерін оңай түрлендіріңіз. Жылдам, сенімді және пайдаланушыға ыңғайлы."
  generator:
    title: "Онлайн %s Редакторы және Генераторы"
    short: "Кешенді мүмкіндіктері бар кәсіби %s онлайн генерация құралы"
    intro: "Пайдалануға оңай онлайн %s генераторы және кесте редакторы. Біздің интуитивті құралымен және нақты уақыттағы алдын ала көрумен кәсіби деректер кестелерін оңай жасаңыз."
navbar:
  search:
    placeholder: "Түрлендіргішті іздеу ..."
  sponsor: "Маған кофе сатып алыңыз"
  extension: "Кеңейту"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Деректер Көзі"
    placeholder: "%s деректеріңізді қойыңыз немесе %s файлдарын осында сүйреңіз"
    example: "Мысал"
    upload: "Файл Жүктеу"
    extract:
      enter: "Веб Бетінен Шығару"
      intro: "Құрылымдалған деректерді автоматты түрде шығару үшін кесте деректері бар веб бет URL мекенжайын енгізіңіз"
      btn: "%s Шығару"
    excel:
      sheet: "Жұмыс Парағы"
      none: "Ешқандай"
  tableEditor:
    title: "Онлайн Кесте Редакторы"
    undo: "Болдырмау"
    redo: "Қайталау"
    transpose: "Транспозиция"
    clear: "Тазалау"
    deleteBlank: "Бос Жолдарды Жою"
    deleteDuplicate: "Қайталанғандарды Жою"
    uppercase: "БАС ӘРІПТЕР"
    lowercase: "кіші әріптер"
    capitalize: "Бас Әріппен"
    replace:
      replace: "Табу және Ауыстыру (Regex қолдауы)"
      subst: "Мынамен ауыстыру..."
      btn: "Барлығын Ауыстыру"
  tableGenerator:
    title: "Кесте Генераторы"
    sponsor: "Маған кофе сатып алыңыз"
    copy: "Буферге Көшіру"
    download: "Файл Жүктеп Алу"
    tooltip:
      html:
        escape: "Көрсету қателерін болдырмау үшін HTML арнайы таңбаларын (&, <, >, \", ') escape жасау"
        div: "Дәстүрлі TABLE тегтерінің орнына DIV+CSS орналасуын пайдалану, адаптивті дизайнға жақсырақ сәйкес келеді"
        minify: "Сығылған HTML кодын жасау үшін бос орындар мен жол ауысуларын жою"
        thead: "Стандартты кесте басы (&lt;thead&gt;) және денесі (&lt;tbody&gt;) құрылымын жасау"
        tableCaption: "Кестенің үстіне сипаттамалық атау қосу (&lt;caption&gt; элементі)"
        tableClass: "Стильді оңай теңшеу үшін кестеге CSS класс атауын қосу"
        tableId: "JavaScript манипуляциясы үшін кестеге бірегей ID идентификаторын орнату"
      jira:
        escape: "Jira кесте синтаксисімен қақтығыстарды болдырмау үшін pipe таңбаларын (|) escape жасау"
      json:
        parsingJSON: "Ұяшықтардағы JSON жолдарын объектілерге ақылды талдау"
        minify: "Файл өлшемін азайту үшін ықшам бір жолды JSON форматын жасау"
        format: "Шығыс JSON деректер құрылымын таңдау: объект массиві, 2D массив, т.б."
      latex:
        escape: "LaTeX арнайы таңбаларын (%, &, _, #, $, т.б.) escape жасау дұрыс компиляцияны қамтамасыз ету үшін"
        ht: "Беттегі кесте орнын басқару үшін қалқымалы орын параметрін [!ht] қосу"
        mwe: "Толық LaTeX құжатын жасау"
        tableAlign: "Беттегі кестенің көлденең туралауын орнату"
        tableBorder: "Кесте шекара стилін конфигурациялау: шекарасыз, ішінара шекара, толық шекара"
        label: "\\ref{} командасының кросс-сілтемесі үшін кесте белгісін орнатыңыз"
        caption: "Кестенің үстінде немесе астында көрсету үшін кесте тақырыбын орнатыңыз"
        location: "Кесте тақырыбының көрсету орнын таңдаңыз: үстінде немесе астында"
        tableType: "Кесте ортасының түрін таңдаңыз: tabular, longtable, array, т.б."
      markdown:
        escape: "Пішім қақтығыстарын болдырмау үшін Markdown арнайы таңбаларын (*, _, |, \\, т.б.) қашу"
        pretty: "Әдемі кесте пішімін жасау үшін баған енін автоматты түрде туралау"
        simple: "Жеңілдетілген синтаксисті пайдалану, сыртқы шекара тік сызықтарын алып тастау"
        boldFirstRow: "Бірінші жол мәтінін қалың қылу"
        boldFirstColumn: "Бірінші баған мәтінін қалың қылу"
        firstHeader: "Бірінші жолды тақырып ретінде қарастыру және бөлгіш сызық қосу"
        textAlign: "Баған мәтінінің туралануын орнату: сол жақ, орта, оң жақ"
        multilineHandling: "Көп жолды мәтінді өңдеу: жол ауысуларын сақтау, \\n-ге қашу, &lt;br&gt; тегтерін пайдалану"

        includeLineNumbers: "Кестенің сол жағына жол нөмірі бағанын қосу"
      magic:
        builtin: "Алдын ала анықталған жалпы үлгі пішімдерін таңдау"
        rowsTpl: "<table> <tr> <th>Сиқырлы Синтаксис</th> <th>Сипаттама</th> <th>Қолдау көрсететін JS Әдістері</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>1-ші, 2-ші ... <b>т</b>ақырып өрісі, яғни {hA} {hB} ...</td> <td>Жол әдістері</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>1-ші, 2-ші ... ағымдағы жолдың өрісі, яғни {$A} {$B} ...</td> <td>Жол әдістері</td> </tr> <tr> <td>{F,} {F;}</td> <td>Ағымдағы жолды <b>F</b> кейінгі жолмен бөлу</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>Ағымдағы <b>ж</b>олдың <b>н</b>өмірі 1 немесе 100-ден</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>Ж</b>олдардың <b>с</b>оңғы нөмірі </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>JavaScript кодын <b>о</b>рындау, мысалы: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Фигуралы жақшаларды шығару үшін кері сызықты <b>\\</b> пайдаланыңыз {...} </td> <td></td> </tr></table>"
        headerTpl: "Тақырып бөлімі үшін теңшелетін шығыс үлгісі"
        footerTpl: "Төменгі деректеме бөлімі үшін теңшелетін шығыс үлгісі"
      textile:
        escape: "Пішім қақтығыстарын болдырмау үшін Textile синтаксис таңбаларын (|, ., -, ^) қашу"
        rowHeader: "Бірінші жолды тақырып жолы ретінде орнату"
        thead: "Кесте басы мен денесі үшін Textile синтаксис белгілерін қосу"
      xml:
        escape: "Жарамды XML қамтамасыз ету үшін XML арнайы таңбаларын (&lt;, &gt;, &amp;, \", ') қашу"
        minify: "Қосымша бос орындарды алып тастап, сығылған XML шығысын жасау"
        rootElement: "XML түбір элемент тег атауын орнату"
        rowElement: "Деректердің әрбір жолы үшін XML элемент тег атауын орнату"
        declaration: "XML декларация тақырыбын қосу (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Балапан элементтердің орнына XML атрибуттары ретінде деректерді шығару"
        cdata: "Арнайы таңбаларды қорғау үшін мәтін мазмұнын CDATA-мен орау"
        encoding: "XML құжаты үшін таңба кодтау пішімін орнату"
        indentation: "XML шегіну таңбасын таңдау: бос орындар немесе табуляторлар"
      yaml:
        indentSize: "YAML иерархиясының шегінуі үшін бос орындар санын орнату (әдетте 2 немесе 4)"
        arrayStyle: "Массив пішімі: блок (жолға бір элемент) немесе ағын (кірістірілген пішім)"
        quotationStyle: "Жол тырнақша стилі: тырнақшасыз, жалғыз тырнақша, қос тырнақша"
      csv:
        bom: "Excel және басқа бағдарламалық жасақтаманың кодтауды тануына көмектесу үшін UTF-8 байт ретінің белгісін қосу"
      excel:
        autoWidth: "Мазмұнға негізделген баған енін автоматты түрде реттеу"
        protectSheet: "Құпия сөзбен жұмыс парағының қорғанысын қосу: tableconvert.com"
      sql:
        primaryKey: "CREATE TABLE мәлімдемесі үшін негізгі кілт өрісінің атын көрсету"
        dialect: "Дерекқор түрін таңдау, тырнақша және деректер түрінің синтаксисіне әсер етеді"
      ascii:
        forceSep: "Деректердің әрбір жолы арасында бөлгіш сызықтарды мәжбүрлеу"
        style: "ASCII кесте шекарасын сызу стилін таңдау"
        comment: "Бүкіл кестені орау үшін пікір белгілерін қосу"
      mediawiki:
        minify: "Шығыс кодын сығымдау, қосымша бос орындарды алып тастау"
        header: "Бірінші жолды тақырып стилі ретінде белгілеу"
        sort: "Кестені шерту арқылы сұрыптау функционалдылығын қосу"
      asciidoc:
        minify: "AsciiDoc формат шығысын сығымдау"
        firstHeader: "Бірінші жолды тақырып жолы ретінде орнату"
        lastFooter: "Соңғы жолды төменгі колонтитул жолы ретінде орнату"
        title: "Кестеге атау мәтінін қосу"
      tracwiki:
        rowHeader: "Бірінші жолды тақырып ретінде орнату"
        colHeader: "Бірінші бағанды тақырып ретінде орнату"
      bbcode:
        minify: "BBCode шығыс форматын сығымдау"
      restructuredtext:
        style: "reStructuredText кесте шекара стилін таңдау"
        forceSep: "Бөлгіш сызықтарды мәжбүрлеу"
      pdf:
        theme: "Кәсіби құжаттар үшін PDF кесте визуалды стилін таңдау"
        headerColor: "PDF кестелері үшін тақырып фон түсін таңдау"
        showHead: "PDF беттерінде тақырып көрсетуін басқару"
        docTitle: "PDF құжаты үшін қосымша атау"
        docDescription: "PDF құжаты үшін қосымша сипаттама мәтіні"
    label:
      ascii:
        forceSep: "Жол бөлгіштері"
        style: "Шекара стилі"
        comment: "Пікір орауышы"
      restructuredtext:
        style: "Шекара стилі"
        forceSep: "Мәжбүрлі бөлгіштер"
      bbcode:
        minify: "Шығысты қысқарту"
      csv:
        doubleQuote: "Қос тырнақша орау"
        delimiter: "Өріс бөлгіші"
        bom: "UTF-8 BOM"
        valueDelimiter: "Мән бөлгіші"
        rowDelimiter: "Жол бөлгіші"
        prefix: "Жол префиксі"
        suffix: "Жол суффиксі"
      excel:
        autoWidth: "Автоматты ені"
        textFormat: "Мәтін форматы"
        protectSheet: "Парақты қорғау"
        boldFirstRow: "Бірінші жолды қалың қылу"
        boldFirstColumn: "Бірінші бағанды қалың қылу"
        sheetName: "Парақ атауы"
      html:
        escape: "HTML таңбаларын қашу"
        div: "DIV кестесі"
        minify: "Кодты қысқарту"
        thead: "Кесте басының құрылымы"
        tableCaption: "Кесте тақырыбы"
        tableClass: "Кесте класы"
        tableId: "Кесте идентификаторы"
        rowHeader: "Жол тақырыбы"
        colHeader: "Баған тақырыбы"
      jira:
        escape: "Таңбаларды қашу"
        rowHeader: "Жол тақырыбы"
        colHeader: "Баған тақырыбы"
      json:
        parsingJSON: "JSON талдау"
        minify: "Шығысты қысқарту"
        format: "Деректер форматы"
        rootName: "Түбір объект атауы"
        indentSize: "Шегіну өлшемі"
      jsonlines:
        parsingJSON: "JSON талдау"
        format: "Деректер форматы"
      latex:
        escape: "LaTeX кесте таңбаларын қашу"
        ht: "Қалқымалы позиция"
        mwe: "Толық құжат"
        tableAlign: "Кесте туралауы"
        tableBorder: "Шекара стилі"
        label: "Сілтеме белгісі"
        caption: "Кесте тақырыбы"
        location: "Тақырып позициясы"
        tableType: "Кесте түрі"
        boldFirstRow: "Қалың бірінші жол"
        boldFirstColumn: "Қалың бірінші баған"
        textAlign: "Мәтін туралауы"
        borders: "Шекара параметрлері"
      markdown:
        escape: "Таңбаларды қашу"
        pretty: "Әдемі Markdown кестесі"
        simple: "Қарапайым Markdown форматы"
        boldFirstRow: "Қалың бірінші жол"
        boldFirstColumn: "Қалың бірінші баған"
        firstHeader: "Бірінші тақырып"
        textAlign: "Мәтін туралау"
        multilineHandling: "Көп жолды өңдеу"

        includeLineNumbers: "Жол нөмірлерін қосу"
        align: "Туралау"
      mediawiki:
        minify: "Кодты қысқарту"
        header: "Тақырып белгілеуі"
        sort: "Сұрыпталатын"
      asciidoc:
        minify: "Форматты қысқарту"
        firstHeader: "Бірінші тақырып"
        lastFooter: "Соңғы төменгі колонтитул"
        title: "Кесте тақырыбы"
      tracwiki:
        rowHeader: "Жол тақырыбы"
        colHeader: "Баған тақырыбы"
      sql:
        drop: "Кестені жою (егер бар болса)"
        create: "Кесте жасау"
        oneInsert: "Топтық енгізу"
        table: "Кесте атауы"
        dialect: "Дерекқор түрі"
        primaryKey: "Негізгі кілт"
      magic:
        builtin: "Кіріктірілген үлгі"
        rowsTpl: "Жол үлгісі, Синтаксис ->"
        headerTpl: "Тақырып үлгісі"
        footerTpl: "Төменгі колонтитул үлгісі"
      textile:
        escape: "Таңбаларды қашу"
        rowHeader: "Жол тақырыбы"
        thead: "Кесте басының синтаксисі"
      xml:
        escape: "XML таңбаларын қашу"
        minify: "Шығысты қысқарту"
        rootElement: "Түбір элемент"
        rowElement: "Жол элементі"
        declaration: "XML декларациясы"
        attributes: "Атрибут режимі"
        cdata: "CDATA орауышы"
        encoding: "Кодтау"
        indentSize: "Шегіну өлшемі"
      yaml:
        indentSize: "Шегіну өлшемі"
        arrayStyle: "Массив стилі"
        quotationStyle: "Тырнақша стилі"
      pdf:
        theme: "PDF кесте темасы"
        headerColor: "PDF тақырып түсі"
        showHead: "PDF тақырып көрсетуі"
        docTitle: "PDF құжат тақырыбы"
        docDescription: "PDF құжат сипаттамасы"

sidebar:
  all: "Барлық Түрлендіру Құралдары"
  dataSource:
    title: "Деректер Көзі"
    description:
      converter: "%s %s дейін түрлендіру үшін импорттау. Файл жүктеуді, онлайн өңдеуді және веб деректерін шығаруды қолдайды."
      generator: "Қолмен енгізу, файл импорты және үлгі жасауды қоса алғанда, бірнеше енгізу әдістерін қолдайтын кесте деректерін жасау."
  tableEditor:
    title: "Онлайн Кесте Редакторы"
    description:
      converter: "Біздің кесте редакторымызды пайдаланып %s онлайн өңдеу. Бос жолдарды жою, қайталанғандарды жою, сұрыптау және табу мен ауыстыруды қолдайтын Excel тәрізді жұмыс тәжірибесі."
      generator: "Excel тәрізді жұмыс тәжірибесін ұсынатын қуатты онлайн кесте редакторы. Бос жолдарды жою, қайталанғандарды жою, сұрыптау және табу мен ауыстыруды қолдайды."
  tableGenerator:
    title: "Кесте Генераторы"
    description:
      converter: "Кесте генераторының нақты уақыттағы алдын ала көрумен %s жылдам жасау. Бай экспорт опциялары, бір шертумен көшіру және жүктеп алу."
      generator: "Әртүрлі пайдалану сценарийлерін қанағаттандыру үшін %s деректерін бірнеше пішімде экспорттау. Теңшелген опциялар мен нақты уақыттағы алдын ала көруді қолдайды."
footer:
  changelog: "Өзгерістер Журналы"
  sponsor: "Демеушілер"
  contact: "Бізбен Байланысу"
  privacyPolicy: "Құпиялылық Саясаты"
  about: "Туралы"
  resources: "Ресурстар"
  popularConverters: "Танымал Түрлендіргіштер"
  popularGenerators: "Танымал Генераторлар"
  dataSecurity: "Сіздің деректеріңіз қауіпсіз - барлық түрлендірулер сіздің браузеріңізде жұмыс істейді."
converters:
  Markdown:
    alias: "Markdown Кестесі"
    what: "Markdown - бұл техникалық құжаттама, блог мазмұнын жасау және веб-дамуда кеңінен қолданылатын жеңіл белгілеу тілі. Оның кесте синтаксисі қысқа және интуитивті, мәтінді туралау, сілтемелерді енгізу және пішімдеуді қолдайды. Бұл бағдарламашылар мен техникалық жазушылардың таңдаулы құралы, GitHub, GitLab және басқа код хостинг платформаларымен толық үйлесімді."
    step1: "Markdown кесте деректерін деректер көзі аймағына қойыңыз немесе .md файлдарын тікелей сүйреп апарып жүктеңіз. Құрал кесте құрылымы мен пішімдеуін автоматты түрде талдайды, күрделі ішкі мазмұн мен арнайы таңбаларды өңдеуді қолдайды."
    step3: "Нақты уақытта стандартты Markdown кесте кодын жасаңыз, бірнеше туралау әдістерін, мәтінді қалыңдатуды, жол нөмірлерін қосуды және басқа кеңейтілген пішім параметрлерін қолдайды. Жасалған код GitHub және негізгі Markdown редакторларымен толық үйлесімді, бір рет басу арқылы көшіруге дайын."
    from_alias: "Markdown Кесте Файлы"
    to_alias: "Markdown Кесте Пішімі"
  Magic:
    alias: "Теңшелген Үлгі"
    what: "Magic үлгісі - бұл осы құралдың бірегей кеңейтілген деректер генераторы, пайдаланушыларға теңшелген үлгі синтаксисі арқылы еркін пішімді деректер шығаруын жасауға мүмкіндік береді. Айнымалыларды ауыстыруды, шартты бағалауды және циклдік өңдеуді қолдайды. Бұл күрделі деректерді түрлендіру қажеттіліктері мен жеке шығару пішімдерін өңдеудің соңғы шешімі, әсіресе әзірлеушілер мен деректер инженерлеріне қолайлы."
    step1: "Кіріктірілген жалпы үлгілерді таңдаңыз немесе теңшелген үлгі синтаксисін жасаңыз. Күрделі деректер құрылымдары мен бизнес логикасын өңдей алатын бай айнымалылар мен функцияларды қолдайды."
    step3: "Теңшелген пішім талаптарын толық қанағаттандыратын деректер шығаруын жасаңыз. Күрделі деректерді түрлендіру логикасы мен шартты өңдеуді қолдайды, деректерді өңдеу тиімділігі мен шығару сапасын айтарлықтай жақсартады. Топтық деректерді өңдеуге арналған қуатты құрал."
    from_alias: "Кесте Деректері"
    to_alias: "Теңшелген Пішім Шығаруы"
  CSV:
    alias: "CSV"
    what: "CSV (Үтірмен бөлінген мәндер) - бұл ең кеңінен қолданылатын деректер алмасу пішімі, Excel, Google Sheets, дерекқор жүйелері және әртүрлі деректерді талдау құралдарымен толық қолдау табады. Оның қарапайым құрылымы мен күшті үйлесімділігі оны деректерді көшіру, топтық импорт/экспорт және платформаралық деректер алмасуға арналған стандартты пішім етеді, бизнес-талдауда, деректер ғылымында және жүйелерді интеграциялауда кеңінен қолданылады."
    step1: "CSV файлдарын жүктеңіз немесе CSV деректерін тікелей қойыңыз. Құрал әртүрлі бөлгіштерді (үтір, таб, нүктелі үтір, құбыр т.б.) ақылды түрде танып, деректер түрлері мен кодтау пішімдерін автоматты анықтайды, үлкен файлдарды жылдам талдауды және күрделі деректер құрылымдарын қолдайды."
    step3: "Теңшелген бөлгіштер, дәйексөз стильдері, кодтау пішімдері және BOM белгі параметрлерін қолдайтын стандартты CSV пішім файлдарын жасаңыз. Мақсатты жүйелермен толық үйлесімділікті қамтамасыз етеді, кәсіпорын деңгейіндегі деректерді өңдеу қажеттіліктерін қанағаттандыру үшін жүктеу және сығымдау опцияларын ұсынады."
    from_alias: "CSV Деректер Файлы"
    to_alias: "CSV Стандартты Пішімі"
  JSON:
    alias: "JSON Массиві"
    what: "JSON (JavaScript Object Notation) - бұл заманауи веб-қосымшалар, REST API және микросервис архитектураларының стандартты кесте деректер пішімі. Оның анық құрылымы мен тиімді талдауы оны frontend және backend деректер өзара әрекеттесуінде, конфигурация файлдарын сақтауда және NoSQL дерекқорларында кеңінен қолданылады. Ішкі объектілерді, массив құрылымдарын және бірнеше деректер түрлерін қолдайды, оны заманауи бағдарламалық жасақтама дамуында ауыстырылмас кесте деректері етеді."
    step1: "JSON файлдарын жүктеңіз немесе JSON массивтерін қойыңыз. Объект массивтерін, ішкі құрылымдар мен күрделі деректер түрлерін автоматты тану және талдауды қолдайды. Құрал JSON синтаксисін ақылды түрде тексереді және қате ескертулерін береді."
    step3: "Бірнеше JSON пішім шығаруларын жасаңыз: стандартты объект массивтері, 2D массивтер, баған массивтері және кілт-мән жұп пішімдері. Сұлуландырылған шығаруды, сығымдау режимін, теңшелген түбір объект атауларын және шегіністер параметрлерін қолдайды, әртүрлі API интерфейстері мен деректерді сақтау қажеттіліктеріне толық бейімделеді."
    from_alias: "JSON Массив Файлы"
    to_alias: "JSON Стандартты Пішімі"
  JSONLines:
    alias: "JSONLines форматы"
    what: "JSON Lines (NDJSON деп те белгілі) - үлкен деректерді өңдеу және ағындық деректерді беру үшін маңызды формат, әр жолда тәуелсіз JSON объектісі бар. Журнал талдауда, деректер ағынын өңдеуде, машиналық оқытуда және таратылған жүйелерде кеңінен пайдаланылады. Инкременталды өңдеу мен параллель есептеуді қолдайды, бұл оны ауқымды құрылымдалған деректерді өңдеуге арналған идеалды таңдау етеді."
    step1: "JSONLines файлдарын жүктеңіз немесе деректерді қойыңыз. Құрал JSON объектілерін жол бойынша талдайды, үлкен файлдарды ағындық өңдеуді және қате жолдарды өткізіп жіберу функционалдылығын қолдайды."
    step3: "Әр жолда толық JSON объектісін шығаратын стандартты JSONLines форматын жасаңыз. Ағындық өңдеуге, топтық импортқа және үлкен деректерді талдау сценарийлеріне қолайлы, деректерді тексеру мен формат оңтайландыруды қолдайды."
    from_alias: "JSONLines деректері"
    to_alias: "JSONLines ағымдық форматы"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) - кәсіпорын деңгейіндегі деректер алмасу және конфигурация басқаруға арналған стандартты формат, қатаң синтаксис спецификациялары мен қуатты тексеру механизмдерімен. Веб-қызметтерде, конфигурация файлдарында, құжат сақтауда және жүйелерді интеграциялауда кеңінен пайдаланылады. Атау кеңістіктерін, схема тексеруін және XSLT түрлендіруін қолдайды, бұл оны кәсіпорын қолданбалары үшін маңызды кесте деректері етеді."
    step1: "XML файлдарын жүктеңіз немесе XML деректерін қойыңыз. Құрал XML құрылымын автоматты түрде талдайды және оны кесте форматына түрлендіреді, атау кеңістігін, атрибут өңдеуін және күрделі кірістірілген құрылымдарды қолдайды."
    step3: "XML стандарттарына сай келетін XML шығысын жасаңыз. Теңшелген түбір элементтерді, жол элемент атауларын, атрибут режимдерін, CDATA орауды және таңба кодтау параметрлерін қолдайды. Деректердің тұтастығы мен үйлесімділігін қамтамасыз етеді, кәсіпорын деңгейіндегі қолданба талаптарын қанағаттандырады."
    from_alias: "XML деректер файлы"
    to_alias: "Стандартты XML форматы"
  YAML:
    alias: "YAML конфигурациясы"
    what: "YAML - адамға ыңғайлы деректерді сериализациялау стандарты, анық иерархиялық құрылымы мен қысқа синтаксисімен танымал. Конфигурация файлдарында, DevOps құрал тізбектерінде, Docker Compose және Kubernetes орналастыруда кеңінен пайдаланылады. Оның күшті оқылымдылығы мен қысқа синтаксисі оны заманауи бұлт-табиғи қолданбалар мен автоматтандырылған операциялар үшін маңызды конфигурация форматына айналдырады."
    step1: "YAML файлдарын жүктеңіз немесе YAML деректерін қойыңыз. Құрал YAML құрылымын ақылды түрде талдайды және синтаксис дұрыстығын тексереді, көп құжатты форматтар мен күрделі деректер түрлерін қолдайды."
    step3: "Блок және ағын массив стильдерін, көптеген дәйексөз параметрлерін, теңшелген шегіністерді және түсініктемелерді сақтауды қолдайтын стандартты YAML формат шығысын жасаңыз. Шығыс YAML файлдарының әртүрлі парсерлер мен конфигурация жүйелерімен толық үйлесімді болуын қамтамасыз етеді."
    from_alias: "YAML конфигурация файлы"
    to_alias: "Стандартты YAML форматы"
  MySQL:
      alias: "MySQL сұраныс нәтижелері"
      what: "MySQL - әлемдегі ең танымал ашық көзді реляциялық дерекқор басқару жүйесі, жоғары өнімділігі, сенімділігі және пайдалану оңайлығымен танымал. Веб-қолданбаларда, кәсіпорын жүйелерінде және деректерді талдау платформаларында кеңінен пайдаланылады. MySQL сұрау нәтижелері әдетте құрылымдалған кесте деректерін қамтиды, дерекқор басқару мен деректерді талдау жұмысында маңызды деректер көзі ретінде қызмет етеді."
      step1: "MySQL сұрау шығыс нәтижелерін деректер көзі аймағына қойыңыз. Құрал MySQL командалық жол шығыс форматын автоматты түрде танып, талдайды, әртүрлі сұрау нәтиже стильдері мен таңба кодтауларын қолдайды, тақырыптар мен деректер жолдарын ақылды түрде өңдейді."
      step3: "MySQL сұрау нәтижелерін көптеген кесте деректер форматтарына жылдам түрлендіріңіз, деректерді талдауды, есеп жасауды, жүйелер арасындағы деректерді көшіруді және деректерді тексеруді жеңілдетеді. Дерекқор әкімшілері мен деректер талдаушылары үшін практикалық құрал."
      from_alias: "MySQL сұраныс нәтижесі"
      to_alias: "MySQL кесте деректері"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) - реляциялық дерекқорлар үшін стандартты операция тілі, деректерді сұрау, енгізу, жаңарту және жою операцияларына пайдаланылады. Дерекқор басқарудың негізгі технологиясы ретінде SQL деректерді талдауда, бизнес-аналитикада, ETL өңдеуде және деректер қоймасын құруда кеңінен пайдаланылады. Бұл деректер мамандары үшін қажетті дағды құралы."
    step1: "INSERT SQL мәлімдемелерін қойыңыз немесе .sql файлдарын жүктеңіз. Құрал SQL синтаксисін ақылды түрде талдайды және кесте деректерін алады, көптеген SQL диалектілері мен күрделі сұрау мәлімдемелерін өңдеуді қолдайды."
    step3: "Стандартты SQL INSERT мәлімдемелері мен кесте жасау мәлімдемелерін жасаңыз. Көптеген дерекқор диалектілерін (MySQL, PostgreSQL, SQLite, SQL Server, Oracle) қолдайды, деректер түрлерін картаға түсіруді, таңбаларды қашуды және негізгі кілт шектеулерін автоматты түрде өңдейді. Жасалған SQL кодының тікелей орындалуын қамтамасыз етеді."
    from_alias: "Insert SQL"
    to_alias: "SQL мәлімдемесі"
  Qlik:
      alias: "Qlik Кестесі"
      what: "Qlik - деректерді көрнекілендіру, басшылық панельдері және өзін-өзі қызмет көрсету бизнес-аналитика өнімдеріне мамандандырылған бағдарлама жеткізушісі, Tableau және Microsoft-пен бірге."
      step1: ""
      step3: "Ақырында, [Кесте Генераторы](#TableGenerator) түрлендіру нәтижелерін көрсетеді. Qlik Sense, Qlik AutoML, QlikView немесе басқа Qlik-қосылған бағдарламаларыңызда пайдаланыңыз."
      from_alias: "Qlik кестесі"
      to_alias: "Qlik кестесі"
  DAX:
      alias: "DAX Кестесі"
      what: "DAX (Data Analysis Expressions) - Microsoft Power BI-да есептелген бағандар, өлшемдер және теңшелген кестелер жасау үшін пайдаланылатын бағдарламалау тілі."
      step1: ""
      step3: "Ақырында, [Кесте Генераторы](#TableGenerator) түрлендіру нәтижелерін көрсетеді. Күтілгендей, ол Microsoft Power BI, Microsoft Analysis Services және Excel үшін Microsoft Power Pivot сияқты бірнеше Microsoft өнімдерінде пайдаланылады."
      from_alias: "DAX кестесі"
      to_alias: "DAX кестесі"
  Firebase:
    alias: "Firebase Тізімі"
    what: "Firebase - нақты уақыт дерекқоры, бұлт сақтау, аутентификация, апат туралы есеп беру және т.б. сияқты орналастырылған backend қызметтерін ұсынатын BaaS қолданба әзірлеу платформасы."
    step1: ""
    step3: "Ақырында, [Кесте Генераторы](#TableGenerator) түрлендіру нәтижелерін көрсетеді. Содан кейін Firebase дерекқорындағы деректер тізіміне қосу үшін Firebase API-дағы push әдісін пайдалана аласыз."
    from_alias: "Firebase тізімі"
    to_alias: "Firebase тізімі"
  HTML:
    alias: "HTML Кестесі"
    what: "HTML кестелері веб-беттерде құрылымдалған деректерді көрсетудің стандартты тәсілі болып табылады, table, tr, td және басқа тегтермен құрылған. Бай стиль теңшеуін, жауап беретін орналасуды және интерактивті функционалдылықты қолдайды. Веб-сайт әзірлеуде, деректерді көрсетуде және есеп жасауда кеңінен пайдаланылады, frontend әзірлеу мен веб-дизайнның маңызды компоненті ретінде қызмет етеді."
    step1: "Кестелері бар HTML кодын қойыңыз немесе HTML файлдарын жүктеңіз. Құрал автоматты түрде беттерден кесте деректерін танып, алады, күрделі HTML құрылымдарын, CSS стильдерін және кірістірілген кесте өңдеуін қолдайды."
    step3: "thead/tbody құрылымын, CSS класс параметрлерін, кесте тақырыптарын, жол/баған тақырыптарын және жауап беретін атрибут конфигурациясын қолдайтын семантикалық HTML кесте кодын жасаңыз. Жасалған кесте коды жақсы қолжетімділік пен SEO достығымен веб стандарттарына сай келетінін қамтамасыз етеді."
    from_alias: "HTML кестесі"
    to_alias: "HTML кестесі"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel - әлемдегі ең танымал электрондық кесте бағдарламасы, бизнес-талдауда, қаржы басқаруда, деректерді өңдеуде және есеп жасауда кеңінен пайдаланылады. Оның қуатты деректерді өңдеу мүмкіндіктері, бай функция кітапханасы және икемді көрнекілендіру мүмкіндіктері оны кеңсе автоматтандыру мен деректерді талдаудың стандартты құралына айналдырады, барлық салалар мен салаларда кең қолданылады."
    step1: "Excel файлдарын жүктеңіз (.xlsx, .xls форматтарын қолдайды) немесе кесте деректерін Excel-ден тікелей көшіріп қойыңыз. Құрал көп парақты өңдеуді, күрделі формат танымын және үлкен файлдарды жылдам талдауды қолдайды, біріктірілген ұяшықтар мен деректер түрлерін автоматты түрде өңдейді."
    step3: "Excel-ге тікелей қойылатын немесе стандартты .xlsx файлдары ретінде жүктелетін Excel-мен үйлесімді кесте деректерін жасаңыз. Жұмыс парағын атауды, ұяшық форматтауды, автоматты баған енін, тақырып стилін және деректерді тексеру параметрлерін қолдайды. Шығыс Excel файлдарының кәсіби көрінісі мен толық функционалдылығы болатынын қамтамасыз етеді."
    from_alias: "Excel кестесі"
    to_alias: "Excel"
  LaTeX:
    alias: "LaTeX Кестесі"
    what: "LaTeX - академиялық мақалалар, техникалық құжаттар және ғылыми басылымдар жасауға арналған кәсіби құжат беттеу жүйесі. Оның кесте функционалдылығы қуатты, күрделі математикалық формулаларды, дәл орналасу бақылауын және жоғары сапалы PDF шығаруды қолдайды. Ол академиядағы және ғылыми баспадағы стандартты құрал болып табылады, журнал мақалаларында, диссертацияларда және техникалық нұсқаулық беттеуде кеңінен пайдаланылады."
    step1: "LaTeX кесте кодын қойыңыз немесе .tex файлдарын жүктеңіз. Құрал LaTeX кесте синтаксисін талдайды және деректер мазмұнын алады, көптеген кесте орталарын (tabular, longtable, array, т.б.) және күрделі формат командаларын қолдайды."
    step3: "Көптеген кесте орта таңдауын, шекара стилі конфигурациясын, тақырып орналасу параметрлерін, құжат класс спецификациясын және пакет басқаруын қолдайтын кәсіби LaTeX кесте кодын жасаңыз. Толық компиляцияланатын LaTeX құжаттарын жасай алады, шығыс кестелердің академиялық баспа стандарттарына сай келетінін қамтамасыз етеді."
    from_alias: "LaTeX кестесі"
    to_alias: "LaTeX кестесі"
  ASCII:
    alias: "ASCII Мәтін Кестесі"
    what: "ASCII кестелері кесте шекаралары мен құрылымдарын салу үшін қарапайым мәтін таңбаларын пайдаланады, ең жақсы үйлесімділік пен тасымалдауды қамтамасыз етеді. Барлық мәтін редакторлары, терминал ортасы және операциялық жүйелермен үйлесімді. Код құжаттамасында, техникалық нұсқаулықтарда, README файлдарында және командалық жол құралдарының шығысында кеңінен пайдаланылады. Бағдарламашылар мен жүйе әкімшілерінің таңдаулы деректерді көрсету форматы."
    step1: "ASCII кестелері бар мәтін файлдарын жүктеңіз немесе кесте деректерін тікелей қойыңыз. Құрал ASCII кесте құрылымдарын ақылды түрде танып, талдайды, көптеген шекара стильдері мен туралау форматтарын қолдайды."
    step3: "Көптеген шекара стильдерін (жалғыз сызық, қос сызық, дөңгеленген бұрыштар, т.б.), мәтін туралау әдістерін және автоматты баған енін қолдайтын әдемі қарапайым мәтін ASCII кестелерін жасаңыз. Жасалған кестелер код редакторларында, құжаттарда және командалық жолдарда мінсіз көрсетіледі."
    from_alias: "ASCII мәтін кестесі"
    to_alias: "ASCII мәтін кестесі"
  MediaWiki:
    alias: "MediaWiki Кестесі"
    what: "MediaWiki - Wikipedia сияқты атақты вики сайттарда пайдаланылатын ашық көзді бағдарлама платформасы. Оның кесте синтаксисі қысқа, бірақ қуатты, кесте стилін теңшеуді, сұрыптау функционалдылығын және сілтеме енгізуді қолдайды. Білім басқаруда, ынтымақтастық редакциялауда және мазмұн басқару жүйелерінде кеңінен пайдаланылады, вики энциклопедиялар мен білім базаларын құрудың негізгі технологиясы ретінде қызмет етеді."
    step1: "MediaWiki кесте кодын қойыңыз немесе вики көз файлдарын жүктеңіз. Құрал вики белгілеу синтаксисін талдайды және кесте деректерін алады, күрделі вики синтаксисі мен үлгі өңдеуді қолдайды."
    step3: "Тақырып стилі параметрлерін, ұяшық туралауын, сұрыптау функционалдылығын қосуды және код сығымдау опцияларын қолдайтын стандартты MediaWiki кесте кодын жасаңыз. Жасалған код вики бет редакциялауға тікелей пайдаланылуы мүмкін, MediaWiki платформаларында мінсіз көрсетуді қамтамасыз етеді."
    from_alias: "MediaWiki кестесі"
    to_alias: "MediaWiki кестесі"
  TracWiki:
    alias: "TracWiki Кестесі"
    what: "Trac - кесте мазмұнын жасау үшін жеңілдетілген вики синтаксисін пайдаланатын веб-негізделген жоба басқару және қате бақылау жүйесі."
    step1: "TracWiki файлдарын жүктеңіз немесе кесте деректерін қойыңыз."
    step3: "Жол/баған тақырып параметрлерін қолдайтын TracWiki-үйлесімді кесте кодын жасаңыз, жоба құжат басқаруын жеңілдетеді."
    from_alias: "TracWiki кестесі"
    to_alias: "TracWiki кестесі"
  AsciiDoc:
    alias: "AsciiDoc Кестесі"
    what: "AsciiDoc - HTML, PDF, нұсқаулық беттері және басқа форматтарға түрлендірілуі мүмкін жеңіл белгілеу тілі, техникалық құжаттама жазуда кеңінен пайдаланылады."
    step1: "AsciiDoc файлдарын жүктеңіз немесе деректерді қойыңыз."
    step3: "Тақырып, төменгі колонтитул және атау параметрлерін қолдайтын AsciiDoc кесте синтаксисін жасаңыз, AsciiDoc редакторларында тікелей пайдаланылады."
    from_alias: "AsciiDoc кестесі"
    to_alias: "AsciiDoc кестесі"
  reStructuredText:
    alias: "reStructuredText Кестесі"
    what: "reStructuredText - Python қауымдастығының стандартты құжаттама форматы, бай кесте синтаксисін қолдайды, Sphinx құжаттама жасауда жиі пайдаланылады."
    step1: ".rst файлдарын жүктеңіз немесе reStructuredText деректерін қойыңыз."
    step3: "Көптеген шекара стильдерін қолдайтын стандартты reStructuredText кестелерін жасаңыз, Sphinx құжаттама жобаларында тікелей пайдаланылады."
    from_alias: "reStructuredText кестесі"
    to_alias: "reStructuredText кестесі"
  PHP:
    alias: "PHP Массиві"
    what: "PHP - танымал сервер жағындағы скрипт тілі, массивтер оның негізгі деректер құрылымы болып табылады, веб әзірлеу мен деректерді өңдеуде кеңінен пайдаланылады."
    step1: "PHP массивтері бар файлдарды жүктеңіз немесе деректерді тікелей қойыңыз."
    step3: "PHP жобаларында тікелей пайдаланылуы мүмкін стандартты PHP массив кодын жасаңыз, ассоциативті және индекстелген массив форматтарын қолдайды."
    from_alias: "PHP массиві"
    to_alias: "PHP коды"
  Ruby:
    alias: "Ruby Массиві"
    what: "Ruby - қысқа және сәнді синтаксисі бар динамикалық объектіге бағытталған бағдарламалау тілі, массивтер маңызды деректер құрылымы болып табылады."
    step1: "Ruby файлдарын жүктеңіз немесе массив деректерін қойыңыз."
    step3: "Ruby синтаксис спецификацияларына сай келетін Ruby массив кодын жасаңыз, Ruby жобаларында тікелей пайдаланылады."
    from_alias: "Ruby массиві"
    to_alias: "Ruby коды"
  ASP:
    alias: "ASP Массиві"
    what: "ASP (Active Server Pages) - Microsoft-тың сервер жағындағы скрипт ортасы, динамикалық веб-беттерді әзірлеу үшін көптеген бағдарламалау тілдерін қолдайды."
    step1: "ASP файлдарын жүктеңіз немесе массив деректерін қойыңыз."
    step3: "VBScript және JScript синтаксисін қолдайтын ASP-үйлесімді массив кодын жасаңыз, ASP.NET жобаларында пайдаланылады."
    from_alias: "ASP массиві"
    to_alias: "ASP коды"
  ActionScript:
    alias: "ActionScript Массиві"
    what: "ActionScript - негізінен Adobe Flash және AIR қолданба әзірлеуге арналған объектіге бағытталған бағдарламалау тілі."
    step1: ".as файлдарын жүктеңіз немесе ActionScript деректерін қойыңыз."
    step3: "AS3 синтаксис стандарттарына сай келетін ActionScript массив кодын жасаңыз, Flash және Flex жоба әзірлеуге пайдаланылады."
    from_alias: "ActionScript массиві"
    to_alias: "ActionScript коды"
  BBCode:
    alias: "BBCode Кестесі"
    what: "BBCode - форумдар мен онлайн қауымдастықтарда жиі пайдаланылатын жеңіл белгілеу тілі, кесте қолдауын қоса алғанда қарапайым форматтау функционалдылығын ұсынады."
    step1: "BBCode бар файлдарды жүктеңіз немесе деректерді қойыңыз."
    step3: "Форум жариялауға және қауымдастық мазмұнын жасауға қолайлы BBCode кесте кодын жасаңыз, сығылған шығыс форматын қолдайды."
    from_alias: "BBCode кестесі"
    to_alias: "BBCode кестесі"
  PDF:
    alias: "PDF кестесі"
    what: "PDF (Portable Document Format) - тұрақты орналасуы, үйлесімді көрсетуі және жоғары сапалы басып шығару сипаттамалары бар платформаралық құжат стандарты. Ресми құжаттарда, есептерде, шот-фактураларда, келісімшарттарда және академиялық мақалаларда кеңінен пайдаланылады. Бизнес байланысы мен құжат мұрағаттауға арналған таңдаулы формат, әртүрлі құрылғылар мен операциялық жүйелерде толық үйлесімді визуалды әсерлерді қамтамасыз етеді."
    step1: "Кез келген форматтағы кесте деректерін импорттаңыз. Құрал деректер құрылымын автоматты түрде талдайды және ақылды орналасу дизайнын орындайды, үлкен кестелердің автоматты беттеуін және күрделі деректер түрлерін өңдеуді қолдайды."
    step3: "Көптеген кәсіби тақырып стильдерін (бизнес, академиялық, минималистік, т.б.), көптілді қаріптерді, автоматты беттеуді, су белгісін қосуды және басып шығаруды оңтайландыруды қолдайтын жоғары сапалы PDF кесте файлдарын жасаңыз. Шығыс PDF құжаттарының кәсіби көрінісі болуын қамтамасыз етеді, бизнес презентациялары мен ресми басылымға тікелей пайдаланылады."
    from_alias: "Кесте деректері"
    to_alias: "PDF кестесі"
  JPEG:
    alias: "JPEG кескіні"
    what: "JPEG - ең кеңінен пайдаланылатын цифрлық кескін форматы, керемет сығымдау әсерлері мен кең үйлесімділікпен. Оның кішкентай файл өлшемі мен жылдам жүктеу жылдамдығы оны веб-көрсетуге, әлеуметтік медиа бөлісуге, құжат иллюстрацияларына және онлайн презентацияларға қолайлы етеді. Цифрлық медиа мен желілік байланысқа арналған стандартты кескін форматы, барлық құрылғылар мен бағдарламалық жасақтамалармен мінсіз қолдау табады."
    step1: "Кез келген форматтағы кесте деректерін импорттаңыз. Құрал ақылды орналасу дизайнын және визуалды оңтайландыруды орындайды, оңтайлы өлшем мен ажыратымдылықты автоматты түрде есептейді."
    step3: "Көптеген тақырып түс схемаларын (жарық, қараңғы, көзге ыңғайлы, т.б.), бейімделгіш орналасуды, мәтін анықтығын оңтайландыруды және өлшемді теңшеуді қолдайтын жоғары анықтықты JPEG кесте кескіндерін жасаңыз. Онлайн бөлісуге, құжат енгізуге және презентация пайдалануға қолайлы, әртүрлі көрсету құрылғыларында керемет визуалды әсерлерді қамтамасыз етеді."
    from_alias: "Кесте деректері"
    to_alias: "JPEG кескіні"
  Jira:
    alias: "Jira кестесі"
    what: "JIRA - Atlassian компаниясы әзірлеген кәсіби жоба басқару және қате бақылау бағдарламасы, икемді әзірлеуде, бағдарламалық жасақтаманы тестілеуде және жоба ынтымақтастығында кеңінен пайдаланылады. Оның кесте функционалдылығы бай форматтау опцияларын және деректерді көрсетуді қолдайды, бағдарламалық жасақтама әзірлеу топтары, жоба менеджерлері және сапа қамтамасыз ету қызметкерлері үшін талаптарды басқаруда, қателерді бақылауда және прогресс туралы есеп беруде маңызды құрал ретінде қызмет етеді."
    step1: "Кесте деректері бар файлдарды жүктеңіз немесе деректер мазмұнын тікелей қойыңыз. Құрал кесте деректерін және арнайы таңбаларды қашуды автоматты түрде өңдейді."
    step3: "Тақырып стилі параметрлерін, ұяшық туралауын, таңбаларды қашу өңдеуін және формат оңтайландыруын қолдайтын JIRA платформасымен үйлесімді кесте кодын жасаңыз. Жасалған код JIRA мәселе сипаттамаларына, түсініктемелеріне немесе вики беттеріне тікелей қойылуы мүмкін, JIRA жүйелерінде дұрыс көрсету мен рендерлеуді қамтамасыз етеді."
    from_alias: "Jira кестесі"
    to_alias: "Jira кестесі"
  Textile:
    alias: "Textile кестесі"
    what: "Textile - бұл қарапайым және үйренуге оңай синтаксисі бар қысқа жеңіл белгілеу тілі, мазмұн басқару жүйелерінде, блог платформаларында және форум жүйелерінде кеңінен пайдаланылады. Оның кесте синтаксисі анық және интуитивті, жылдам форматтау мен стиль параметрлерін қолдайды. Мазмұн жасаушылар мен веб-сайт әкімшілері үшін жылдам құжат жазу және мазмұн жариялау үшін идеалды құрал."
    step1: "Textile форматындағы файлдарды жүктеңіз немесе кесте деректерін қойыңыз. Құрал Textile белгілеу синтаксисін талдайды және кесте мазмұнын алады."
    step3: "Тақырып белгілеуін, ұяшық туралауын, арнайы таңбаларды қашуды және формат оңтайландыруын қолдайтын стандартты Textile кесте синтаксисін жасаңыз. Жасалған код Textile қолдайтын CMS жүйелерінде, блог платформаларында және құжат жүйелерінде тікелей пайдаланылуы мүмкін, мазмұнның дұрыс көрсетілуі мен көрсетілуін қамтамасыз етеді."
    from_alias: "Textile кестесі"
    to_alias: "Textile кестесі"
  PNG:
    alias: "PNG кескіні"
    what: "PNG (Portable Network Graphics) - бұл керемет сығымдау және мөлдірлік қолдауы бар жоғалтусыз кескін форматы. Веб-дизайнда, цифрлық графикада және кәсіби фотографияда кеңінен пайдаланылады. Оның жоғары сапасы мен кең үйлесімділігі оны скриншоттар, логотиптер, диаграммалар және нақты мәліметтер мен мөлдір фон қажет ететін кез келген кескіндер үшін идеалды етеді."
    step1: "Кез келген форматтағы кесте деректерін импорттаңыз. Құрал ақылды орналасу дизайнын және визуалды оңтайландыруды орындайды, PNG шығысы үшін оңтайлы өлшем мен ажыратымдылықты автоматты түрде есептейді."
    step3: "Көптеген тақырып түс схемаларын, мөлдір фондарды, бейімделгіш орналасуды және мәтін анықтығын оңтайландыруды қолдайтын жоғары сапалы PNG кесте кескіндерін жасаңыз. Веб-пайдалануға, құжат енгізуге және керемет визуалды сапасы бар кәсіби презентацияларға мінсіз."
    from_alias: ""
    to_alias: "PNG кескіні"
  TOML:
    alias: "TOML"
    what: "TOML (Tom's Obvious, Minimal Language) - оқуға және жазуға оңай конфигурация файл форматы. Анық және қарапайым болуға арналған, заманауи бағдарламалық жасақтама жобаларында конфигурацияны басқаруға кеңінен пайдаланылады. Оның анық синтаксисі мен күшті типтеуі оны қолданба параметрлері мен жоба конфигурация файлдары үшін керемет таңдау етеді."
    step1: "TOML файлдарын жүктеңіз немесе конфигурация деректерін қойыңыз. Құрал TOML синтаксисін талдайды және құрылымдалған конфигурация ақпаратын алады."
    step3: "Кірістірілген құрылымдарды, деректер түрлерін және түсініктемелерді қолдайтын стандартты TOML форматын жасаңыз. Жасалған TOML файлдары қолданба конфигурациясына, құру құралдарына және жоба параметрлеріне мінсіз."
    from_alias: "TOML"
    to_alias: "TOML форматы"
  INI:
    alias: "INI"
    what: "INI файлдары - көптеген қолданбалар мен операциялық жүйелер пайдаланатын қарапайым конфигурация файлдары. Олардың тікелей кілт-мән жұп құрылымы оларды қолмен оқуға және өңдеуге оңай етеді. Windows қолданбаларында, ескі жүйелерде және адам оқылымдылығы маңызды қарапайым конфигурация сценарийлерінде кеңінен пайдаланылады."
    step1: "INI файлдарын жүктеңіз немесе конфигурация деректерін қойыңыз. Құрал INI синтаксисін талдайды және бөлім негізіндегі конфигурация ақпаратын алады."
    step3: "Бөлімдерді, түсініктемелерді және әртүрлі деректер түрлерін қолдайтын стандартты INI форматын жасаңыз. Жасалған INI файлдары көптеген қолданбалар мен конфигурация жүйелерімен үйлесімді."
    from_alias: "INI"
    to_alias: "INI форматы"
  Avro:
    alias: "Avro схемасы"
    what: "Apache Avro - бай деректер құрылымдарын, ықшам екілік форматты және схема эволюция мүмкіндіктерін ұсынатын деректерді сериализациялау жүйесі. Үлкен деректерді өңдеуде, хабарлама кезектерінде және таратылған жүйелерде кеңінен пайдаланылады. Оның схема анықтамасы күрделі деректер түрлері мен нұсқа үйлесімділігін қолдайды, бұл оны деректер инженерлері мен жүйе архитекторлары үшін маңызды құрал етеді."
    step1: "Avro схема файлдарын жүктеңіз немесе деректерді қойыңыз. Құрал Avro схема анықтамаларын талдайды және кесте құрылым ақпаратын алады."
    step3: "Деректер түрлерін картаға түсіруді, өріс шектеулерін және схема тексеруін қолдайтын стандартты Avro схема анықтамаларын жасаңыз. Жасалған схемалар Hadoop экожүйелерінде, Kafka хабарлама жүйелерінде және басқа үлкен деректер платформаларында тікелей пайдаланылуы мүмкін."
    from_alias: "Avro схемасы"
    to_alias: "Avro схемасы"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) - Google-дың құрылымдалған деректерді сериализациялауға арналған тілге бейтарап, платформаға бейтарап, кеңейтілетін механизмі. Микросервистерде, API әзірлеуде және деректерді сақтауда кеңінен пайдаланылады. Оның тиімді екілік форматы мен күшті типтеуі оны жоғары өнімділікті қолданбалар мен тілдер арасындағы байланысқа идеалды етеді."
    step1: ".proto файлдарын жүктеңіз немесе Protocol Buffer анықтамаларын қойыңыз. Құрал protobuf синтаксисін талдайды және хабарлама құрылым ақпаратын алады."
    step3: "Хабарлама түрлерін, өріс опцияларын және қызмет анықтамаларын қолдайтын стандартты Protocol Buffer анықтамаларын жасаңыз. Жасалған .proto файлдары көптеген бағдарламалау тілдеріне компиляциялануы мүмкін."
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf схемасы"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas - Python-дағы ең танымал деректерді талдау кітапханасы, DataFrame оның негізгі деректер құрылымы болып табылады. Ол қуатты деректерді манипуляциялау, тазалау және талдау мүмкіндіктерін ұсынады, деректер ғылымында, машиналық оқытуда және бизнес-аналитикада кеңінен пайдаланылады. Python әзірлеушілері мен деректер талдаушылары үшін ауыстырылмас құрал."
    step1: "DataFrame кодын қамтитын Python файлдарын жүктеңіз немесе деректерді қойыңыз. Құрал Pandas синтаксисін талдайды және DataFrame құрылым ақпаратын алады."
    step3: "Деректер түрлерінің спецификацияларын, индекс параметрлерін және деректер операцияларын қолдайтын стандартты Pandas DataFrame кодын жасаңыз. Жасалған код деректерді талдау мен өңдеуге арналған Python ортасында тікелей орындалуы мүмкін."
    from_alias: "Pandas DataFrame"
    to_alias: "Pandas DataFrame"
  RDF:
    alias: "RDF үштігі"
    what: "RDF (Resource Description Framework) - бұл Интернеттегі деректер алмасуға арналған стандартты модель, ресурстар туралы ақпаратты граф түрінде көрсету үшін жасалған. Семантикалық веб, білім графиктері және байланысты деректер қолданбаларында кеңінен пайдаланылады. Оның үштік құрылымы бай метадеректерді көрсету мен семантикалық қатынастарды мүмкін етеді."
    step1: "RDF файлдарын жүктеңіз немесе үштік деректерді қойыңыз. Құрал RDF синтаксисін талдайды және семантикалық қатынастар мен ресурс ақпаратын алады."
    step3: "Әртүрлі сериализацияларды (RDF/XML, Turtle, N-Triples) қолдайтын стандартты RDF форматын жасаңыз. Жасалған RDF семантикалық веб қолданбаларында, білім базаларында және байланысты деректер жүйелерінде пайдаланылуы мүмкін."
    from_alias: "RDF"
    to_alias: "RDF үштігі"
  MATLAB:
    alias: "MATLAB массиві"
    what: "MATLAB - бұл инженерлік есептеулерде, деректерді талдауда және алгоритм әзірлеуде кеңінен пайдаланылатын жоғары өнімділікті сандық есептеу және көрнекілендіру бағдарламасы. Оның массив және матрица операциялары қуатты, күрделі математикалық есептеулер мен деректерді өңдеуді қолдайды. Инженерлер, зерттеушілер және деректер ғалымдары үшін қажетті құрал."
    step1: "MATLAB .m файлдарын жүктеңіз немесе массив деректерін қойыңыз. Құрал MATLAB синтаксисін талдайды және массив құрылым ақпаратын алады."
    step3: "Көп өлшемді массивтерді, деректер түрлерінің спецификацияларын және айнымалы атауларды қолдайтын стандартты MATLAB массив кодын жасаңыз. Жасалған код деректерді талдау және ғылыми есептеу үшін MATLAB ортасында тікелей орындалуы мүмкін."
    from_alias: "MATLAB массиві"
    to_alias: "MATLAB массиві"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame - R бағдарламалау тіліндегі негізгі деректер құрылымы, статистикалық талдауда, деректерді өндіруде және машиналық оқытуда кеңінен пайдаланылады. R - статистикалық есептеу мен графикаға арналған жетекші құрал, DataFrame қуатты деректерді манипуляциялау, статистикалық талдау және көрнекілендіру мүмкіндіктерін ұсынады. Құрылымдалған деректерді талдаумен айналысатын деректер ғалымдары, статистиктер және зерттеушілер үшін қажетті."
    step1: "R деректер файлдарын жүктеңіз немесе DataFrame кодын қойыңыз. Құрал R синтаксисін талдайды және баған түрлері, жол атаулары және деректер мазмұнын қоса алғанда DataFrame құрылым ақпаратын алады."
    step3: "Деректер түрлерінің спецификацияларын, фактор деңгейлерін, жол/баған атауларын және R-ге тән деректер құрылымдарын қолдайтын стандартты R DataFrame кодын жасаңыз. Жасалған код статистикалық талдау мен деректерді өңдеуге арналған R ортасында тікелей орындалуы мүмкін."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Түрлендіруді Бастау"
  start_generating: "Генерацияны Бастау"
  api_docs: "API Құжаттары"
related:
  section_title: 'Көбірек {{ if and .from (ne .from "generator") }}{{ .from }} және {{ end }}{{ .to }} Түрлендіргіштері'
  section_description: '{{ if and .from (ne .from "generator") }}{{ .from }} және {{ end }}{{ .to }} форматтары үшін көбірек түрлендіргіштерді зерттеңіз. Біздің кәсіби онлайн түрлендіру құралдарымен деректеріңізді көптеген форматтар арасында түрлендіріңіз.'
  title: "{{ .from }} {{ .to }} дейін"
howto:
  step2: "Кәсіби мүмкіндіктері бар біздің кеңейтілген онлайн кесте редакторын пайдаланып деректерді өңдеңіз. Бос жолдарды жоюды, қайталанғандарды алып тастауды, деректерді транспозициялауды, сұрыптауды, regex табу және ауыстыруды, нақты уақыттағы алдын ала көруді қолдайды. Барлық өзгерістер автоматты түрде %s форматына дәл және сенімді нәтижелермен түрленеді."
  section_title: "{{ . }} қалай пайдалану керек"
  converter_description: "Біздің қадамдық нұсқаулығымен {{ .from }} {{ .to }} дейін түрлендіруді үйреніңіз. Кеңейтілген мүмкіндіктері және нақты уақыттағы алдын ала көруі бар кәсіби онлайн түрлендіргіш."
  generator_description: "Біздің онлайн генераторымен кәсіби {{ .to }} кестелерін жасауды үйреніңіз. Excel тәрізді өңдеу, нақты уақыттағы алдын ала көру және лезде экспорт мүмкіндіктері."
extension:
  section_title: "Кесте Анықтау және Шығару Кеңейтуі"
  section_description: "Кез келген веб-сайттан кестелерді бір шертумен шығарыңыз. Excel, CSV, JSON сияқты 30+ форматқа лезде түрлендіріңіз - көшіру-қою қажет емес."
  features:
    extraction_title: "Бір Шертумен Кесте Шығару"
    extraction_description: "Көшіру-қоюсыз кез келген веб-беттен кестелерді лезде шығарыңыз - кәсіби деректерді шығару оңай болды"
    formats_title: "30+ Формат Түрлендіргіш Қолдауы"
    formats_description: "Шығарылған кестелерді біздің кеңейтілген кесте түрлендіргішімен Excel, CSV, JSON, Markdown, SQL және басқаларға түрлендіріңіз"
    detection_title: "Ақылды Кесте Анықтау"
    detection_description: "Жылдам деректерді шығару және түрлендіру үшін кез келген веб-беттегі кестелерді автоматты түрде анықтайды және ерекшелейді"
  hover_tip: "✨ Шығару белгішесін көру үшін кез келген кестенің үстіне тышқанды апарыңыз"
recommendations:
  section_title: "Университеттер мен Мамандар Ұсынады"
  section_description: "TableConvert сенімді кесте түрлендіру және деректерді өңдеу үшін университеттер, зерттеу институттары және әзірлеу топтарының мамандары сенім артады."
  cards:
    university_title: "University of Wisconsin-Madison"
    university_description: "TableConvert.com - Кәсіби тегін онлайн кесте түрлендіргіші және деректер форматтары құралы"
    university_link: "Мақаланы Оқу"
    facebook_title: "Деректер Мамандары Қауымдастығы"
    facebook_description: "Facebook әзірлеушілер топтарындағы деректер талдаушылары мен мамандары бөлісті және ұсынды"
    facebook_link: "Жазбаны Көру"
    twitter_title: "Әзірлеушілер Қауымдастығы"
    twitter_description: "Кесте түрлендіру үшін @xiaoying_eth және басқа әзірлеушілер X (Twitter) арқылы ұсынды"
    twitter_link: "Твитті Көру"
faq:
  section_title: "Жиі Қойылатын Сұрақтар"
  section_description: "Біздің тегін онлайн кесте түрлендіргішіміз, деректер форматтары және түрлендіру процесі туралы жалпы сұрақтар."
  what: "%s форматы дегеніміз не?"
  howto_convert:
    question: "{{ . }} тегін қалай пайдалануға болады?"
    answer: "{{ .from }} файлыңызды жүктеңіз, деректерді қойыңыз немесе біздің тегін онлайн кесте түрлендіргішімізді пайдаланып веб-беттерден шығарыңыз. Біздің кәсіби түрлендіргіш құралымыз деректеріңізді нақты уақыттағы алдын ала көру және кеңейтілген өңдеу мүмкіндіктерімен лезде {{ .to }} форматына түрлендіреді. Түрлендірілген нәтижені лезде жүктеп алыңыз немесе көшіріңіз."
  security:
    question: "Бұл онлайн түрлендіргішті пайдаланғанда деректерім қауіпсіз бе?"
    answer: "Әрине! Барлық кесте түрлендірулері сіздің браузеріңізде жергілікті жүреді - деректеріңіз ешқашан құрылғыңыздан шықпайды. Біздің онлайн түрлендіргішіміз барлығын клиент жағында өңдейді, толық құпиялылық пен деректер қауіпсіздігін қамтамасыз етеді. Біздің серверлерде ешқандай файлдар сақталмайды."
  free:
    question: "TableConvert шынымен тегін пайдалануға бола ма?"
    answer: "Иә, TableConvert толығымен тегін! Барлық түрлендіргіш мүмкіндіктері, кесте редакторы, деректер генераторы құралдары және экспорт опциялары ақысыз, тіркелусіз немесе жасырын төлемсіз қол жетімді. Онлайн шексіз файлдарды тегін түрлендіріңіз."
  filesize:
    question: "Онлайн түрлендіргіштің файл өлшемі шектеулері қандай?"
    answer: "Біздің тегін онлайн кесте түрлендіргішіміз 10МБ дейінгі файлдарды қолдайды. Үлкен файлдар, пакеттік өңдеу немесе кәсіпорын қажеттіліктері үшін жоғары шектеулері бар браузер кеңейтуімізді немесе кәсіби API қызметімізді пайдаланыңыз."
stats:
  conversions: "Түрлендірілген Кестелер"
  tables: "Жасалған Кестелер"
  formats: "Деректер Файл Форматтары"
  rating: "Пайдаланушы Бағасы"
