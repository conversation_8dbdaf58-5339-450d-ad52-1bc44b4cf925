site:
  fullname: "ഓൺലൈൻ ടേബിൾ കൺവേർട്ട്"
  name: "TableConvert"
  subtitle: "സൗജന്യ ഓൺലൈൻ ടേബിൾ കൺവേർട്ടറും ജനറേറ്ററും"
  intro: "TableConvert എന്നത് Excel, CSV, JSON, Markdown, LaTeX, SQL എന്നിവയും മറ്റും ഉൾപ്പെടെ 30+ ഫോർമാറ്റുകൾ തമ്മിലുള്ള പരിവർത്തനത്തെ പിന്തുണയ്ക്കുന്ന ഒരു സൗജന്യ ഓൺലൈൻ ടേബിൾ കൺവേർട്ടറും ഡാറ്റ ജനറേറ്റർ ടൂളുമാണ്."
  followTwitter: "X-ൽ ഞങ്ങളെ ഫോളോ ചെയ്യുക"
title:
  converter: "%s മുതൽ %s വരെ"
  generator: "%s ജനറേറ്റർ"
post:
  tags:
    converter: "കൺവേർട്ടർ"
    editor: "എഡിറ്റർ"
    generator: "ജനറേറ്റർ"
    maker: "ബിൽഡർ"
  converter:
    title: "%s നെ %s ആയി ഓൺലൈനിൽ കൺവേർട്ട് ചെയ്യുക"
    short: "സൗജന്യവും ശക്തവുമായ %s മുതൽ %s വരെയുള്ള ഓൺലൈൻ ടൂൾ"
    intro: "ഉപയോഗിക്കാൻ എളുപ്പമുള്ള ഓൺലൈൻ %s മുതൽ %s വരെയുള്ള കൺവേർട്ടർ. ഞങ്ങളുടെ അവബോധജന്യമായ പരിവർത്തന ടൂൾ ഉപയോഗിച്ച് ടേബിൾ ഡാറ്റ അനായാസമായി രൂപാന്തരപ്പെടുത്തുക. വേഗതയേറിയതും വിശ്വസനീയവും ഉപയോക്തൃ-സൗഹൃദവുമാണ്."
  generator:
    title: "ഓൺലൈൻ %s എഡിറ്ററും ജനറേറ്ററും"
    short: "സമഗ്ര ഫീച്ചറുകളോടുകൂടിയ പ്രൊഫഷണൽ %s ഓൺലൈൻ ജനറേഷൻ ടൂൾ"
    intro: "ഉപയോഗിക്കാൻ എളുപ്പമുള്ള ഓൺലൈൻ %s ജനറേറ്ററും ടേബിൾ എഡിറ്ററും. ഞങ്ങളുടെ അവബോധജന്യമായ ടൂളും തത്സമയ പ്രിവ്യൂവും ഉപയോഗിച്ച് പ്രൊഫഷണൽ ഡാറ്റ ടേബിളുകൾ അനായാസമായി സൃഷ്ടിക്കുക."
navbar:
  search:
    placeholder: "കൺവേർട്ടർ തിരയുക..."
  sponsor: "ഞങ്ങൾക്ക് കാപ്പി വാങ്ങിത്തരുക"
  extension: "എക്സ്റ്റൻഷൻ"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "ഡാറ്റ സോഴ്സ്"
    placeholder: "നിങ്ങളുടെ %s ഡാറ്റ പേസ്റ്റ് ചെയ്യുക അല്ലെങ്കിൽ %s ഫയലുകൾ ഇവിടെ ഡ്രാഗ് ചെയ്യുക"
    example: "ഉദാഹരണം"
    upload: "ഫയൽ അപ്‌ലോഡ് ചെയ്യുക"
    extract:
      enter: "വെബ് പേജിൽ നിന്ന് എക്സ്ട്രാക്റ്റ് ചെയ്യുക"
      intro: "ഘടനാപരമായ ഡാറ്റ സ്വയമേവ എക്സ്ട്രാക്റ്റ് ചെയ്യുന്നതിന് ടേബിൾ ഡാറ്റ അടങ്ങിയ വെബ് പേജ് URL നൽകുക"
      btn: "%s എക്സ്ട്രാക്റ്റ് ചെയ്യുക"
    excel:
      sheet: "വർക്ക്ഷീറ്റ്"
      none: "ഒന്നുമില്ല"
  tableEditor:
    title: "ഓൺലൈൻ ടേബിൾ എഡിറ്റർ"
    undo: "പഴയപടിയാക്കുക"
    redo: "വീണ്ടും ചെയ്യുക"
    transpose: "ട്രാൻസ്പോസ്"
    clear: "ക്ലിയർ"
    deleteBlank: "ശൂന്യമായവ ഇല്ലാതാക്കുക"
    deleteDuplicate: "ഡ്യൂപ്ലിക്കേറ്റുകൾ ഇല്ലാതാക്കുക"
    uppercase: "വലിയക്ഷരങ്ങൾ"
    lowercase: "ചെറിയക്ഷരങ്ങൾ"
    capitalize: "ആദ്യക്ഷരം വലുത്"
    replace:
      replace: "കണ്ടെത്തി മാറ്റിസ്ഥാപിക്കുക (Regex പിന്തുണ)"
      subst: "ഇതുമായി മാറ്റിസ്ഥാപിക്കുക..."
      btn: "എല്ലാം മാറ്റിസ്ഥാപിക്കുക"
  tableGenerator:
    title: "ടേബിൾ ജനറേറ്റർ"
    sponsor: "ഞങ്ങൾക്ക് കാപ്പി വാങ്ങിത്തരുക"
    copy: "ക്ലിപ്പ്ബോർഡിലേക്ക് കോപ്പി ചെയ്യുക"
    download: "ഫയൽ ഡൗൺലോഡ് ചെയ്യുക"
    tooltip:
      html:
        escape: "പ്രദർശന പിശകുകൾ തടയാൻ HTML പ്രത്യേക അക്ഷരങ്ങൾ (&, <, >, \", ') എസ്കേപ്പ് ചെയ്യുക"
        div: "പരമ്പരാഗത TABLE ടാഗുകൾക്ക് പകരം DIV+CSS ലേഔട്ട് ഉപയോഗിക്കുക, റെസ്പോൺസിവ് ഡിസൈനിന് കൂടുതൽ അനുയോജ്യം"
        minify: "കംപ്രസ്ഡ് HTML കോഡ് ജനറേറ്റ് ചെയ്യാൻ വൈറ്റ്സ്പേസും ലൈൻ ബ്രേക്കുകളും നീക്കം ചെയ്യുക"
        thead: "സ്റ്റാൻഡേർഡ് ടേബിൾ ഹെഡ് (&lt;thead&gt;) ഉം ബോഡി (&lt;tbody&gt;) ഘടനയും ജനറേറ്റ് ചെയ്യുക"
        tableCaption: "ടേബിളിന് മുകളിൽ വിവരണാത്മക ശീർഷകം ചേർക്കുക (&lt;caption&gt; എലിമെന്റ്)"
        tableClass: "എളുപ്പത്തിലുള്ള സ്റ്റൈൽ കസ്റ്റമൈസേഷനായി ടേബിളിൽ CSS ക്ലാസ് നാമം ചേർക്കുക"
        tableId: "JavaScript മാനിപുലേഷനായി ടേബിളിന് അദ്വിതീയ ID ഐഡന്റിഫയർ സെറ്റ് ചെയ്യുക"
      jira:
        escape: "Jira ടേബിൾ സിന്റാക്സുമായുള്ള സംഘർഷങ്ങൾ ഒഴിവാക്കാൻ പൈപ്പ് അക്ഷരങ്ങൾ (|) എസ്കേപ്പ് ചെയ്യുക"
      json:
        parsingJSON: "സെല്ലുകളിലെ JSON സ്ട്രിംഗുകൾ ബുദ്ധിപരമായി ഒബ്ജക്റ്റുകളായി പാഴ്സ് ചെയ്യുക"
        minify: "ഫയൽ വലുപ്പം കുറയ്ക്കാൻ കോംപാക്റ്റ് സിംഗിൾ-ലൈൻ JSON ഫോർമാറ്റ് ജനറേറ്റ് ചെയ്യുക"
        format: "ഔട്ട്പുട്ട് JSON ഡാറ്റ ഘടന തിരഞ്ഞെടുക്കുക: ഒബ്ജക്റ്റ് അറേ, 2D അറേ, മുതലായവ"
      latex:
        escape: "ശരിയായ കംപൈലേഷൻ ഉറപ്പാക്കാൻ LaTeX പ്രത്യേക അക്ഷരങ്ങൾ (%, &, _, #, $, മുതലായവ) എസ്കേപ്പ് ചെയ്യുക"
        ht: "പേജിൽ ടേബിൾ സ്ഥാനം നിയന്ത്രിക്കാൻ ഫ്ലോട്ടിംഗ് പൊസിഷൻ പാരാമീറ്റർ [!ht] ചേർക്കുക"
        mwe: "പൂർണ്ണമായ LaTeX ഡോക്യുമെന്റ് ജനറേറ്റ് ചെയ്യുക"
        tableAlign: "പേജിൽ ടേബിളിന്റെ തിരശ്ചീന അലൈൻമെന്റ് സെറ്റ് ചെയ്യുക"
        tableBorder: "ടേബിൾ ബോർഡർ സ്റ്റൈൽ കോൺഫിഗർ ചെയ്യുക: ബോർഡർ ഇല്ല, ഭാഗിക ബോർഡർ, പൂർണ്ണ ബോർഡർ"
        label: "\\ref{} കമാൻഡ് ക്രോസ്-റഫറൻസിംഗിനായി ടേബിൾ ലേബൽ സെറ്റ് ചെയ്യുക"
        caption: "ടേബിളിന് മുകളിലോ താഴെയോ പ്രദർശിപ്പിക്കാൻ ടേബിൾ ക്യാപ്ഷൻ സെറ്റ് ചെയ്യുക"
        location: "ടേബിൾ ക്യാപ്ഷൻ ഡിസ്പ്ലേ സ്ഥാനം തിരഞ്ഞെടുക്കുക: മുകളിൽ അല്ലെങ്കിൽ താഴെ"
        tableType: "ടേബിൾ എൻവയോൺമെന്റ് തരം തിരഞ്ഞെടുക്കുക: tabular, longtable, array, മുതലായവ"
      markdown:
        escape: "ഫോർമാറ്റ് സംഘർഷങ്ങൾ ഒഴിവാക്കാൻ Markdown പ്രത്യേക അക്ഷരങ്ങൾ (*, _, |, \\, മുതലായവ) എസ്കേപ്പ് ചെയ്യുക"
        pretty: "കൂടുതൽ മനോഹരമായ ടേബിൾ ഫോർമാറ്റ് ജനറേറ്റ് ചെയ്യാൻ കോളം വീതികൾ ഓട്ടോ-അലൈൻ ചെയ്യുക"
        simple: "ബാഹ്യ ബോർഡർ വെർട്ടിക്കൽ ലൈനുകൾ ഒഴിവാക്കി ലളിതമായ സിന്റാക്സ് ഉപയോഗിക്കുക"
        boldFirstRow: "ആദ്യ വരിയിലെ ടെക്സ്റ്റ് ബോൾഡ് ആക്കുക"
        boldFirstColumn: "ആദ്യ കോളത്തിലെ ടെക്സ്റ്റ് ബോൾഡ് ആക്കുക"
        firstHeader: "ആദ്യ വരിയെ ഹെഡറായി കണക്കാക്കി സെപ്പറേറ്റർ ലൈൻ ചേർക്കുക"
        textAlign: "കോളം ടെക്സ്റ്റ് അലൈൻമെന്റ് സെറ്റ് ചെയ്യുക: ഇടത്, മധ്യം, വലത്"
        multilineHandling: "മൾട്ടിലൈൻ ടെക്സ്റ്റ് ഹാൻഡ്ലിംഗ്: ലൈൻ ബ്രേക്കുകൾ സംരക്ഷിക്കുക, \\n ലേക്ക് എസ്കേപ്പ് ചെയ്യുക, &lt;br&gt; ടാഗുകൾ ഉപയോഗിക്കുക"

        includeLineNumbers: "ടേബിളിന്റെ ഇടതുവശത്ത് ലൈൻ നമ്പർ കോളം ചേർക്കുക"
      magic:
        builtin: "മുൻകൂട്ടി നിർവചിച്ച സാധാരണ ടെംപ്ലേറ്റ് ഫോർമാറ്റുകൾ തിരഞ്ഞെടുക്കുക"
        rowsTpl: "<table> <tr> <th>മാജിക് സിന്റാക്സ്</th> <th>വിവരണം</th> <th>JS മെത്തഡുകളുടെ പിന്തുണ</th> </tr> <tr> <td>{h1} {h2} ...</td> <td><b>ഹെഡിംഗിന്റെ</b> 1-ാം, 2-ാം ... ഫീൽഡ്, അതായത് {hA} {hB} ...</td> <td>സ്ട്രിംഗ് മെത്തഡുകൾ</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>നിലവിലെ വരിയുടെ 1-ാം, 2-ാം ... ഫീൽഡ്, അതായത് {$A} {$B} ...</td> <td>സ്ട്രിംഗ് മെത്തഡുകൾ</td> </tr> <tr> <td>{F,} {F;}</td> <td><b>F</b> ന് ശേഷമുള്ള സ്ട്രിംഗ് ഉപയോഗിച്ച് നിലവിലെ വരി വിഭജിക്കുക</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>നിലവിലെ <b>വരിയുടെ</b> ലൈൻ <b>നമ്പർ</b> 1 അല്ലെങ്കിൽ 100 മുതൽ</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>വരികളുടെ</b> <b>അവസാന</b> ലൈൻ <b>നമ്പർ</b> </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>JavaScript കോഡ് <b>എക്സിക്യൂട്ട്</b> ചെയ്യുക, ഉദാ: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> ബ്രേസുകൾ {...} ഔട്ട്പുട്ട് ചെയ്യാൻ ബാക്ക്സ്ലാഷ് <b>\\</b> ഉപയോഗിക്കുക </td> <td></td> </tr></table>"
        headerTpl: "ഹെഡർ വിഭാഗത്തിനായുള്ള കസ്റ്റം ഔട്ട്പുട്ട് ടെംപ്ലേറ്റ്"
        footerTpl: "ഫൂട്ടർ വിഭാഗത്തിനായുള്ള കസ്റ്റം ഔട്ട്പുട്ട് ടെംപ്ലേറ്റ്"
      textile:
        escape: "ഫോർമാറ്റ് സംഘർഷങ്ങൾ ഒഴിവാക്കാൻ Textile സിന്റാക്സ് അക്ഷരങ്ങൾ (|, ., -, ^) എസ്കേപ്പ് ചെയ്യുക"
        rowHeader: "ആദ്യ വരിയെ ഹെഡർ വരിയായി സെറ്റ് ചെയ്യുക"
        thead: "ടേബിൾ ഹെഡിനും ബോഡിക്കുമായി Textile സിന്റാക്സ് മാർക്കറുകൾ ചേർക്കുക"
      xml:
        escape: "സാധുവായ XML ഉറപ്പാക്കാൻ XML പ്രത്യേക അക്ഷരങ്ങൾ (&lt;, &gt;, &amp;, \", ') എസ്കേപ്പ് ചെയ്യുക"
        minify: "അധിക വൈറ്റ്സ്പേസ് നീക്കം ചെയ്ത് കംപ്രസ്ഡ് XML ഔട്ട്പുട്ട് ജനറേറ്റ് ചെയ്യുക"
        rootElement: "XML റൂട്ട് എലിമെന്റ് ടാഗ് നാമം സെറ്റ് ചെയ്യുക"
        rowElement: "ഡാറ്റയുടെ ഓരോ വരിക്കുമായി XML എലിമെന്റ് ടാഗ് നാമം സെറ്റ് ചെയ്യുക"
        declaration: "XML ഡിക്ലറേഷൻ ഹെഡർ ചേർക്കുക (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "ചൈൽഡ് എലിമെന്റുകൾക്ക് പകരം XML അട്രിബ്യൂട്ടുകളായി ഡാറ്റ ഔട്ട്പുട്ട് ചെയ്യുക"
        cdata: "പ്രത്യേക അക്ഷരങ്ങളെ സംരക്ഷിക്കാൻ CDATA ഉപയോഗിച്ച് ടെക്സ്റ്റ് ഉള്ളടക്കം പൊതിയുക"
        encoding: "XML ഡോക്യുമെന്റിനായി കാരക്ടർ എൻകോഡിംഗ് ഫോർമാറ്റ് സെറ്റ് ചെയ്യുക"
        indentation: "XML ഇൻഡന്റേഷൻ കാരക്ടർ തിരഞ്ഞെടുക്കുക: സ്പേസുകൾ അല്ലെങ്കിൽ ടാബുകൾ"
      yaml:
        indentSize: "YAML ഹൈറാർക്കി ഇൻഡന്റേഷനായി സ്പേസുകളുടെ എണ്ണം സെറ്റ് ചെയ്യുക (സാധാരണയായി 2 അല്ലെങ്കിൽ 4)"
        arrayStyle: "അറേ ഫോർമാറ്റ്: ബ്ലോക്ക് (ഓരോ ലൈനിലും ഒരു ഇനം) അല്ലെങ്കിൽ ഫ്ലോ (ഇൻലൈൻ ഫോർമാറ്റ്)"
        quotationStyle: "സ്ട്രിംഗ് കോട്ട് സ്റ്റൈൽ: കോട്ടുകൾ ഇല്ല, സിംഗിൾ കോട്ടുകൾ, ഡബിൾ കോട്ടുകൾ"
      pdf:
        theme: "പ്രൊഫഷണൽ ഡോക്യുമെന്റുകൾക്കായി PDF ടേബിൾ വിഷ്വൽ സ്റ്റൈൽ തിരഞ്ഞെടുക്കുക"
        headerColor: "PDF ടേബിൾ ഹെഡർ പശ്ചാത്തല നിറം തിരഞ്ഞെടുക്കുക"
        showHead: "PDF പേജുകളിൽ ഹെഡർ ഡിസ്പ്ലേ നിയന്ത്രിക്കുക"
        docTitle: "PDF ഡോക്യുമെന്റിനായി ഓപ്ഷണൽ ശീർഷകം"
        docDescription: "PDF ഡോക്യുമെന്റിനായി ഓപ്ഷണൽ വിവരണ ടെക്സ്റ്റ്"
      csv:
        bom: "Excel ഉം മറ്റ് സോഫ്റ്റ്വെയറുകളും എൻകോഡിംഗ് തിരിച്ചറിയാൻ സഹായിക്കുന്നതിന് UTF-8 ബൈറ്റ് ഓർഡർ മാർക്ക് ചേർക്കുക"
      excel:
        autoWidth: "ഉള്ളടക്കത്തെ അടിസ്ഥാനമാക്കി കോളം വീതി സ്വയമേവ ക്രമീകരിക്കുക"
        protectSheet: "പാസ്വേഡ് ഉപയോഗിച്ച് വർക്ക്ഷീറ്റ് പരിരക്ഷ പ്രാപ്തമാക്കുക: tableconvert.com"
      sql:
        primaryKey: "CREATE TABLE സ്റ്റേറ്റ്മെന്റിനായി പ്രൈമറി കീ ഫീൽഡ് നാമം വ്യക്തമാക്കുക"
        dialect: "ഡാറ്റാബേസ് തരം തിരഞ്ഞെടുക്കുക, കോട്ടും ഡാറ്റ ടൈപ്പ് സിന്റാക്സിനെയും ബാധിക്കുന്നു"
      ascii:
        forceSep: "ഡാറ്റയുടെ ഓരോ വരിക്കിടയിലും സെപ്പറേറ്റർ ലൈനുകൾ നിർബന്ധിക്കുക"
        style: "ASCII ടേബിൾ ബോർഡർ ഡ്രോയിംഗ് സ്റ്റൈൽ തിരഞ്ഞെടുക്കുക"
        comment: "മുഴുവൻ ടേബിളും പൊതിയാൻ കമന്റ് മാർക്കറുകൾ ചേർക്കുക"
      mediawiki:
        minify: "അധിക വൈറ്റ്സ്പേസ് നീക്കം ചെയ്ത് ഔട്ട്പുട്ട് കോഡ് കംപ്രസ് ചെയ്യുക"
        header: "ആദ്യ വരിയെ ഹെഡർ സ്റ്റൈലായി അടയാളപ്പെടുത്തുക"
        sort: "ടേബിൾ ക്ലിക്ക് സോർട്ടിംഗ് പ്രവർത്തനം പ്രാപ്തമാക്കുക"
      asciidoc:
        minify: "AsciiDoc ഫോർമാറ്റ് ഔട്ട്പുട്ട് കംപ്രസ് ചെയ്യുക"
        firstHeader: "ആദ്യ വരിയെ ഹെഡർ വരിയായി സെറ്റ് ചെയ്യുക"
        lastFooter: "അവസാന വരിയെ ഫൂട്ടർ വരിയായി സെറ്റ് ചെയ്യുക"
        title: "ടേബിളിൽ ശീർഷക ടെക്സ്റ്റ് ചേർക്കുക"
      tracwiki:
        rowHeader: "ആദ്യ വരിയെ ഹെഡറായി സെറ്റ് ചെയ്യുക"
        colHeader: "ആദ്യ കോളത്തെ ഹെഡറായി സെറ്റ് ചെയ്യുക"
      bbcode:
        minify: "BBCode ഔട്ട്പുട്ട് ഫോർമാറ്റ് കംപ്രസ് ചെയ്യുക"
      restructuredtext:
        style: "reStructuredText ടേബിൾ ബോർഡർ സ്റ്റൈൽ തിരഞ്ഞെടുക്കുക"
        forceSep: "സെപ്പറേറ്റർ ലൈനുകൾ നിർബന്ധിക്കുക"
    label:
      ascii:
        forceSep: "വരി വിഭജകങ്ങൾ"
        style: "ബോർഡർ സ്റ്റൈൽ"
        comment: "കമന്റ് റാപ്പർ"
      restructuredtext:
        style: "ബോർഡർ സ്റ്റൈൽ"
        forceSep: "വിഭജകങ്ങൾ നിർബന്ധിക്കുക"
      bbcode:
        minify: "ഔട്ട്പുട്ട് ചെറുതാക്കുക"
      csv:
        doubleQuote: "ഡബിൾ കോട്ട് റാപ്പ്"
        delimiter: "ഫീൽഡ് വിഭജകം"
        bom: "UTF-8 BOM"
        valueDelimiter: "മൂല്യ വിഭജകം"
        rowDelimiter: "വരി വിഭജകം"
        prefix: "വരി പ്രിഫിക്സ്"
        suffix: "വരി സഫിക്സ്"
      excel:
        autoWidth: "ഓട്ടോ വീതി"
        textFormat: "ടെക്സ്റ്റ് ഫോർമാറ്റ്"
        protectSheet: "ഷീറ്റ് സംരക്ഷിക്കുക"
        boldFirstRow: "ആദ്യ വരി ബോൾഡ്"
        boldFirstColumn: "ആദ്യ കോളം ബോൾഡ്"
        sheetName: "ഷീറ്റ് നാമം"
      html:
        escape: "HTML അക്ഷരങ്ങൾ എസ്കേപ്പ് ചെയ്യുക"
        div: "DIV ടേബിൾ"
        minify: "കോഡ് ചെറുതാക്കുക"
        thead: "ടേബിൾ ഹെഡ് ഘടന"
        tableCaption: "ടേബിൾ ക്യാപ്ഷൻ"
        tableClass: "ടേബിൾ ക്ലാസ്"
        tableId: "ടേബിൾ ID"
        rowHeader: "വരി ഹെഡർ"
        colHeader: "കോളം ഹെഡർ"
      jira:
        escape: "അക്ഷരങ്ങൾ എസ്കേപ്പ് ചെയ്യുക"
        rowHeader: "വരി ഹെഡർ"
        colHeader: "കോളം ഹെഡർ"
      json:
        parsingJSON: "JSON പാഴ്സ് ചെയ്യുക"
        minify: "ഔട്ട്പുട്ട് ചെറുതാക്കുക"
        format: "ഡാറ്റ ഫോർമാറ്റ്"
        rootName: "റൂട്ട് ഒബ്ജക്റ്റ് നാമം"
        indentSize: "ഇൻഡന്റ് വലുപ്പം"
      jsonlines:
        parsingJSON: "JSON പാഴ്സ് ചെയ്യുക"
        format: "ഡാറ്റ ഫോർമാറ്റ്"
      latex:
        escape: "LaTeX ടേബിൾ അക്ഷരങ്ങൾ എസ്കേപ്പ് ചെയ്യുക"
        ht: "ഫ്ലോട്ട് സ്ഥാനം"
        mwe: "പൂർണ്ണ ഡോക്യുമെന്റ്"
        tableAlign: "ടേബിൾ അലൈൻമെന്റ്"
        tableBorder: "ബോർഡർ സ്റ്റൈൽ"
        label: "റഫറൻസ് ലേബൽ"
        caption: "ടേബിൾ ക്യാപ്ഷൻ"
        location: "ക്യാപ്ഷൻ സ്ഥാനം"
        tableType: "ടേബിൾ തരം"
        boldFirstRow: "ആദ്യ വരി ബോൾഡ്"
        boldFirstColumn: "ആദ്യ കോളം ബോൾഡ്"
        textAlign: "ടെക്സ്റ്റ് അലൈൻമെന്റ്"
        borders: "ബോർഡർ സെറ്റിംഗുകൾ"
      markdown:
        escape: "അക്ഷരങ്ങൾ എസ്കേപ്പ് ചെയ്യുക"
        pretty: "മനോഹരമായ Markdown ടേബിൾ"
        simple: "ലളിതമായ Markdown ഫോർമാറ്റ്"
        boldFirstRow: "ആദ്യ വരി ബോൾഡ്"
        boldFirstColumn: "ആദ്യ കോളം ബോൾഡ്"
        firstHeader: "ആദ്യ ഹെഡർ"
        textAlign: "ടെക്സ്റ്റ് അലൈൻമെന്റ്"
        multilineHandling: "മൾട്ടിലൈൻ കൈകാര്യം"

        includeLineNumbers: "ലൈൻ നമ്പറുകൾ ചേർക്കുക"
        align: "അലൈൻമെന്റ്"
      mediawiki:
        minify: "കോഡ് ചെറുതാക്കുക"
        header: "ഹെഡർ മാർക്കപ്പ്"
        sort: "സോർട്ട് ചെയ്യാവുന്ന"
      asciidoc:
        minify: "ഫോർമാറ്റ് ചെറുതാക്കുക"
        firstHeader: "ആദ്യ ഹെഡർ"
        lastFooter: "അവസാന ഫൂട്ടർ"
        title: "ടേബിൾ ശീർഷകം"
      tracwiki:
        rowHeader: "വരി ഹെഡർ"
        colHeader: "കോളം ഹെഡർ"
      sql:
        drop: "ടേബിൾ ഡ്രോപ്പ് ചെയ്യുക (ഉണ്ടെങ്കിൽ)"
        create: "ടേബിൾ സൃഷ്ടിക്കുക"
        oneInsert: "ബാച്ച് ഇൻസേർട്ട്"
        table: "ടേബിൾ നാമം"
        dialect: "ഡാറ്റാബേസ് തരം"
        primaryKey: "പ്രൈമറി കീ"
      magic:
        builtin: "ബിൽറ്റ്-ഇൻ ടെംപ്ലേറ്റ്"
        rowsTpl: "വരി ടെംപ്ലേറ്റ്, സിന്റാക്സ് ->"
        headerTpl: "ഹെഡർ ടെംപ്ലേറ്റ്"
        footerTpl: "ഫൂട്ടർ ടെംപ്ലേറ്റ്"
      textile:
        escape: "അക്ഷരങ്ങൾ എസ്കേപ്പ് ചെയ്യുക"
        rowHeader: "വരി ഹെഡർ"
        thead: "ടേബിൾ ഹെഡ് സിന്റാക്സ്"
      xml:
        escape: "XML അക്ഷരങ്ങൾ എസ്കേപ്പ് ചെയ്യുക"
        minify: "ഔട്ട്പുട്ട് ചെറുതാക്കുക"
        rootElement: "റൂട്ട് എലിമെന്റ്"
        rowElement: "വരി എലിമെന്റ്"
        declaration: "XML ഡിക്ലറേഷൻ"
        attributes: "അട്രിബ്യൂട്ട് മോഡ്"
        cdata: "CDATA റാപ്പർ"
        encoding: "എൻകോഡിംഗ്"
        indentSize: "ഇൻഡന്റ് വലുപ്പം"
      yaml:
        indentSize: "ഇൻഡന്റ് വലുപ്പം"
        arrayStyle: "അറേ സ്റ്റൈൽ"
        quotationStyle: "കോട്ട് സ്റ്റൈൽ"
      pdf:
        theme: "PDF ടേബിൾ തീം"
        headerColor: "PDF ഹെഡർ നിറം"
        showHead: "PDF ഹെഡർ ഡിസ്പ്ലേ"
        docTitle: "PDF ഡോക്യുമെന്റ് ശീർഷകം"
        docDescription: "PDF ഡോക്യുമെന്റ് വിവരണം"
sidebar:
  all: "എല്ലാ കൺവേർഷൻ ടൂളുകളും"
  dataSource:
    title: "ഡാറ്റ സോഴ്സ്"
    description:
      converter: "%s നെ %s ആയി കൺവേർട്ട് ചെയ്യുന്നതിനായി ഇമ്പോർട്ട് ചെയ്യുക. ഫയൽ അപ്‌ലോഡ്, ഓൺലൈൻ എഡിറ്റിംഗ്, വെബ് ഡാറ്റ എക്സ്ട്രാക്ഷൻ എന്നിവയെ പിന്തുണയ്ക്കുന്നു."
      generator: "മാനുവൽ ഇൻപുട്ട്, ഫയൽ ഇമ്പോർട്ട്, ടെംപ്ലേറ്റ് ജനറേഷൻ എന്നിവ ഉൾപ്പെടെ ഒന്നിലധികം ഇൻപുട്ട് രീതികളുടെ പിന്തുണയോടെ ടേബിൾ ഡാറ്റ സൃഷ്ടിക്കുക."
  tableEditor:
    title: "ഓൺലൈൻ ടേബിൾ എഡിറ്റർ"
    description:
      converter: "ഞങ്ങളുടെ ടേബിൾ എഡിറ്റർ ഉപയോഗിച്ച് %s ഓൺലൈനിൽ പ്രോസസ്സ് ചെയ്യുക. ശൂന്യ വരികൾ ഇല്ലാതാക്കൽ, ഡ്യൂപ്ലിക്കേഷൻ, സോർട്ടിംഗ്, കണ്ടെത്തൽ & മാറ്റിസ്ഥാപിക്കൽ എന്നിവയുടെ പിന്തുണയോടെ Excel പോലുള്ള ഓപ്പറേഷൻ അനുഭവം."
      generator: "Excel പോലുള്ള ഓപ്പറേഷൻ അനുഭവം നൽകുന്ന ശക്തമായ ഓൺലൈൻ ടേബിൾ എഡിറ്റർ. ശൂന്യ വരികൾ ഇല്ലാതാക്കൽ, ഡ്യൂപ്ലിക്കേഷൻ, സോർട്ടിംഗ്, കണ്ടെത്തൽ & മാറ്റിസ്ഥാപിക്കൽ എന്നിവയെ പിന്തുണയ്ക്കുന്നു."
  tableGenerator:
    title: "ടേബിൾ ജനറേറ്റർ"
    description:
      converter: "ടേബിൾ ജനറേറ്ററിന്റെ റിയൽ-ടൈം പ്രിവ്യൂ ഉപയോഗിച്ച് %s വേഗത്തിൽ ജനറേറ്റ് ചെയ്യുക. സമ്പന്നമായ എക്സ്പോർട്ട് ഓപ്ഷനുകൾ, വൺ-ക്ലിക്ക് കോപ്പി & ഡൗൺലോഡ്."
      generator: "വ്യത്യസ്ത ഉപയോഗ സാഹചര്യങ്ങൾ നിറവേറ്റുന്നതിനായി %s ഡാറ്റ ഒന്നിലധികം ഫോർമാറ്റുകളിൽ എക്സ്പോർട്ട് ചെയ്യുക. കസ്റ്റം ഓപ്ഷനുകളും റിയൽ-ടൈം പ്രിവ്യൂവും പിന്തുണയ്ക്കുന്നു."
footer:
  changelog: "മാറ്റങ്ങളുടെ ലോഗ്"
  sponsor: "സ്പോൺസർമാർ"
  contact: "ഞങ്ങളെ ബന്ധപ്പെടുക"
  privacyPolicy: "സ്വകാര്യതാ നയം"
  about: "കുറിച്ച്"
  resources: "വിഭവങ്ങൾ"
  popularConverters: "ജനപ്രിയ കൺവേർട്ടറുകൾ"
  popularGenerators: "ജനപ്രിയ ജനറേറ്ററുകൾ"
  dataSecurity: "നിങ്ങളുടെ ഡാറ്റ സുരക്ഷിതമാണ് - എല്ലാ പരിവർത്തനങ്ങളും നിങ്ങളുടെ ബ്രൗസറിൽ പ്രവർത്തിക്കുന്നു."
converters:
  Markdown:
    alias: "Markdown ടേബിൾ"
    what: "Markdown എന്നത് സാങ്കേതിക ഡോക്യുമെന്റേഷൻ, ബ്ലോഗ് ഉള്ളടക്ക സൃഷ്ടി, വെബ് വികസനം എന്നിവയ്ക്കായി വ്യാപകമായി ഉപയോഗിക്കുന്ന ഒരു ലഘുവായ മാർക്കപ്പ് ഭാഷയാണ്. ഇതിന്റെ ടേബിൾ സിന്റാക്സ് സംക്ഷിപ്തവും അവബോധജന്യവുമാണ്, ടെക്സ്റ്റ് അലൈൻമെന്റ്, ലിങ്ക് എംബെഡിംഗ്, ഫോർമാറ്റിംഗ് എന്നിവയെ പിന്തുണയ്ക്കുന്നു. പ്രോഗ്രാമർമാരുടെയും സാങ്കേതിക എഴുത്തുകാരുടെയും ഇഷ്ട ഉപകരണമാണിത്, GitHub, GitLab, മറ്റ് കോഡ് ഹോസ്റ്റിംഗ് പ്ലാറ്റ്ഫോമുകൾ എന്നിവയുമായി പൂർണ്ണമായും പൊരുത്തപ്പെടുന്നു."
    step1: "Markdown ടേബിൾ ഡാറ്റ ഡാറ്റ സോഴ്സ് ഏരിയയിൽ പേസ്റ്റ് ചെയ്യുക, അല്ലെങ്കിൽ അപ്‌ലോഡിനായി .md ഫയലുകൾ നേരിട്ട് ഡ്രാഗ് ആൻഡ് ഡ്രോപ്പ് ചെയ്യുക. ടൂൾ സ്വയമേവ ടേബിൾ ഘടനയും ഫോർമാറ്റിംഗും പാഴ്സ് ചെയ്യുന്നു, സങ്കീർണ്ണമായ നെസ്റ്റഡ് ഉള്ളടക്കവും പ്രത്യേക കാരക്ടർ കൈകാര്യവും പിന്തുണയ്ക്കുന്നു."
    step3: "ഒന്നിലധികം അലൈൻമെന്റ് രീതികൾ, ടെക്സ്റ്റ് ബോൾഡിംഗ്, ലൈൻ നമ്പർ കൂട്ടിച്ചേർക്കൽ, മറ്റ് വിപുലമായ ഫോർമാറ്റ് സെറ്റിംഗുകൾ എന്നിവയെ പിന്തുണയ്ക്കുന്ന സ്റ്റാൻഡേർഡ് Markdown ടേബിൾ കോഡ് റിയൽ-ടൈമിൽ ജനറേറ്റ് ചെയ്യുക. ജനറേറ്റ് ചെയ്ത കോഡ് GitHub ഉം പ്രധാന Markdown എഡിറ്ററുകളുമായി പൂർണ്ണമായും പൊരുത്തപ്പെടുന്നു, വൺ-ക്ലിക്ക് കോപ്പിയോടെ ഉപയോഗിക്കാൻ തയ്യാർ."
    from_alias: "Markdown ടേബിൾ ഫയൽ"
    to_alias: "Markdown ടേബിൾ ഫോർമാറ്റ്"
  Magic:
    alias: "കസ്റ്റം ടെംപ്ലേറ്റ്"
    what: "മാജിക് ടെംപ്ലേറ്റ് ഈ ടൂളിന്റെ അദ്വിതീയമായ വിപുലമായ ഡാറ്റ ജനറേറ്ററാണ്, കസ്റ്റം ടെംപ്ലേറ്റ് സിന്റാക്സിലൂടെ ഏത് ഫോർമാറ്റിലുമുള്ള ഡാറ്റ ഔട്ട്പുട്ട് സൃഷ്ടിക്കാൻ ഉപയോക്താക്കളെ അനുവദിക്കുന്നു. വേരിയബിൾ റീപ്ലേസ്മെന്റ്, കണ്ടിഷണൽ ജഡ്ജ്മെന്റ്, ലൂപ്പ് പ്രോസസ്സിംഗ് എന്നിവയെ പിന്തുണയ്ക്കുന്നു. സങ്കീർണ്ണമായ ഡാറ്റ കൺവേർഷൻ ആവശ്യങ്ങളും വ്യക്തിഗതമാക്കിയ ഔട്ട്പുട്ട് ഫോർമാറ്റുകളും കൈകാര്യം ചെയ്യുന്നതിനുള്ള അന്തിമ പരിഹാരമാണിത്, പ്രത്യേകിച്ച് ഡെവലപ്പർമാർക്കും ഡാറ്റ എഞ്ചിനീയർമാർക്കും അനുയോജ്യം."
    step1: "ബിൽറ്റ്-ഇൻ സാധാരണ ടെംപ്ലേറ്റുകൾ തിരഞ്ഞെടുക്കുക അല്ലെങ്കിൽ കസ്റ്റം ടെംപ്ലേറ്റ് സിന്റാക്സ് സൃഷ്ടിക്കുക. സങ്കീർണ്ണമായ ഡാറ്റ ഘടനകളും ബിസിനസ് ലോജിക്കും കൈകാര്യം ചെയ്യാൻ കഴിയുന്ന സമ്പന്നമായ വേരിയബിളുകളും ഫംഗ്ഷനുകളും പിന്തുണയ്ക്കുന്നു."
    step3: "കസ്റ്റം ഫോർമാറ്റ് ആവശ്യകതകൾ പൂർണ്ണമായും നിറവേറ്റുന്ന ഡാറ്റ ഔട്ട്പുട്ട് ജനറേറ്റ് ചെയ്യുക. സങ്കീർണ്ണമായ ഡാറ്റ കൺവേർഷൻ ലോജിക്കും കണ്ടിഷണൽ പ്രോസസ്സിംഗും പിന്തുണയ്ക്കുന്നു, ഡാറ്റ പ്രോസസ്സിംഗ് കാര്യക്ഷമതയും ഔട്ട്പുട്ട് ഗുണനിലവാരവും ഗണ്യമായി മെച്ചപ്പെടുത്തുന്നു. ബാച്ച് ഡാറ്റ പ്രോസസ്സിംഗിനുള്ള ശക്തമായ ഉപകരണം."
    from_alias: "ടേബിൾ ഡാറ്റ"
    to_alias: "കസ്റ്റം ഫോർമാറ്റ് ഔട്ട്പുട്ട്"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) ഏറ്റവും വ്യാപകമായി ഉപയോഗിക്കുന്ന ഡാറ്റ എക്സ്ചേഞ്ച് ഫോർമാറ്റാണ്, Excel, Google Sheets, ഡാറ്റാബേസ് സിസ്റ്റങ്ങൾ, വിവിധ ഡാറ്റ അനാലിസിസ് ടൂളുകൾ എന്നിവയാൽ പൂർണ്ണമായും പിന്തുണയ്ക്കപ്പെടുന്നു. ഇതിന്റെ ലളിതമായ ഘടനയും ശക്തമായ പൊരുത്തപ്പെടുത്തലും ഇതിനെ ഡാറ്റ മൈഗ്രേഷൻ, ബാച്ച് ഇമ്പോർട്ട്/എക്സ്പോർട്ട്, ക്രോസ്-പ്ലാറ്റ്ഫോം ഡാറ്റ എക്സ്ചേഞ്ച് എന്നിവയ്ക്കുള്ള സ്റ്റാൻഡേർഡ് ഫോർമാറ്റാക്കുന്നു, ബിസിനസ് അനാലിസിസ്, ഡാറ്റ സയൻസ്, സിസ്റ്റം ഇന്റഗ്രേഷൻ എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു."
    step1: "CSV ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ നേരിട്ട് CSV ഡാറ്റ പേസ്റ്റ് ചെയ്യുക. ടൂൾ വിവിധ ഡിലിമിറ്ററുകൾ (കോമ, ടാബ്, സെമികോളൺ, പൈപ്പ്, മുതലായവ) ബുദ്ധിപരമായി തിരിച്ചറിയുന്നു, ഡാറ്റ തരങ്ങളും എൻകോഡിംഗ് ഫോർമാറ്റുകളും സ്വയമേവ കണ്ടെത്തുന്നു, വലിയ ഫയലുകളുടെയും സങ്കീർണ്ണമായ ഡാറ്റ ഘടനകളുടെയും വേഗത്തിലുള്ള പാഴ്സിംഗ് പിന്തുണയ്ക്കുന്നു."
    step3: "കസ്റ്റം ഡിലിമിറ്ററുകൾ, കോട്ട് സ്റ്റൈലുകൾ, എൻകോഡിംഗ് ഫോർമാറ്റുകൾ, BOM മാർക്ക് സെറ്റിംഗുകൾ എന്നിവയ്ക്കുള്ള പിന്തുണയോടെ സ്റ്റാൻഡേർഡ് CSV ഫോർമാറ്റ് ഫയലുകൾ ജനറേറ്റ് ചെയ്യുക. ടാർഗെറ്റ് സിസ്റ്റങ്ങളുമായുള്ള പൂർണ്ണമായ പൊരുത്തപ്പെടുത്തൽ ഉറപ്പാക്കുന്നു, എന്റർപ്രൈസ്-ലെവൽ ഡാറ്റ പ്രോസസ്സിംഗ് ആവശ്യങ്ങൾ നിറവേറ്റുന്നതിന് ഡൗൺലോഡും കംപ്രഷൻ ഓപ്ഷനുകളും നൽകുന്നു."
    from_alias: "CSV ഡാറ്റ ഫയൽ"
    to_alias: "CSV സ്റ്റാൻഡേർഡ് ഫോർമാറ്റ്"
  JSON:
    alias: "JSON അറേ"
    what: "JSON (JavaScript Object Notation) ആധുനിക വെബ് ആപ്ലിക്കേഷനുകൾ, REST API-കൾ, മൈക്രോസർവിസ് ആർക്കിടെക്ചറുകൾ എന്നിവയ്ക്കുള്ള സ്റ്റാൻഡേർഡ് ടേബിൾ ഡാറ്റ ഫോർമാറ്റാണ്. ഇതിന്റെ വ്യക്തമായ ഘടനയും കാര്യക്ഷമമായ പാഴ്സിംഗും ഫ്രണ്ട്-എൻഡ്, ബാക്ക്-എൻഡ് ഡാറ്റ ഇന്ററാക്ഷൻ, കോൺഫിഗറേഷൻ ഫയൽ സ്റ്റോറേജ്, NoSQL ഡാറ്റാബേസുകൾ എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു. നെസ്റ്റഡ് ഒബ്ജക്റ്റുകൾ, അറേ ഘടനകൾ, ഒന്നിലധികം ഡാറ്റ തരങ്ങൾ എന്നിവയെ പിന്തുണയ്ക്കുന്നു, ആധുനിക സോഫ്റ്റ്വെയർ വികസനത്തിന് അത്യന്താപേക്ഷിതമായ ടേബിൾ ഡാറ്റയാക്കുന്നു."
    step1: "JSON ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ JSON അറേകൾ പേസ്റ്റ് ചെയ്യുക. ഒബ്ജക്റ്റ് അറേകൾ, നെസ്റ്റഡ് ഘടനകൾ, സങ്കീർണ്ണമായ ഡാറ്റ തരങ്ങൾ എന്നിവയുടെ സ്വയമേവയുള്ള തിരിച്ചറിയലും പാഴ്സിംഗും പിന്തുണയ്ക്കുന്നു. ടൂൾ JSON സിന്റാക്സ് ബുദ്ധിപരമായി വാലിഡേറ്റ് ചെയ്യുകയും പിശക് പ്രോംപ്റ്റുകൾ നൽകുകയും ചെയ്യുന്നു."
    step3: "ഒന്നിലധികം JSON ഫോർമാറ്റ് ഔട്ട്പുട്ടുകൾ ജനറേറ്റ് ചെയ്യുക: സ്റ്റാൻഡേർഡ് ഒബ്ജക്റ്റ് അറേകൾ, 2D അറേകൾ, കോളം അറേകൾ, കീ-വാല്യൂ പെയർ ഫോർമാറ്റുകൾ. ബ്യൂട്ടിഫൈഡ് ഔട്ട്പുട്ട്, കംപ്രഷൻ മോഡ്, കസ്റ്റം റൂട്ട് ഒബ്ജക്റ്റ് നാമങ്ങൾ, ഇൻഡന്റേഷൻ സെറ്റിംഗുകൾ എന്നിവയെ പിന്തുണയ്ക്കുന്നു, വിവിധ API ഇന്റർഫേസുകളുമായും ഡാറ്റ സ്റ്റോറേജ് ആവശ്യങ്ങളുമായും പൂർണ്ണമായി പൊരുത്തപ്പെടുന്നു."
    from_alias: "JSON അറേ ഫയൽ"
    to_alias: "JSON സ്റ്റാൻഡേർഡ് ഫോർമാറ്റ്"
  JSONLines:
    alias: "JSONLines ഫോർമാറ്റ്"
    what: "JSON Lines (NDJSON എന്നും അറിയപ്പെടുന്നു) ബിഗ് ഡാറ്റ പ്രോസസ്സിംഗിനും സ്ട്രീമിംഗ് ഡാറ്റ ട്രാൻസ്മിഷനുമുള്ള ഒരു പ്രധാന ഫോർമാറ്റാണ്, ഓരോ ലൈനിലും ഒരു സ്വതന്ത്ര JSON ഒബ്ജക്റ്റ് അടങ്ങിയിരിക്കുന്നു. ലോഗ് അനാലിസിസ്, ഡാറ്റ സ്ട്രീം പ്രോസസ്സിംഗ്, മെഷീൻ ലേണിംഗ്, ഡിസ്ട്രിബ്യൂട്ടഡ് സിസ്റ്റങ്ങൾ എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു. ഇൻക്രിമെന്റൽ പ്രോസസ്സിംഗും പാരലൽ കമ്പ്യൂട്ടിംഗും പിന്തുണയ്ക്കുന്നു, വലിയ തോതിലുള്ള ഘടനാപരമായ ഡാറ്റ കൈകാര്യം ചെയ്യുന്നതിനുള്ള അനുയോജ്യമായ തിരഞ്ഞെടുപ്പാക്കുന്നു."
    step1: "JSONLines ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ ഡാറ്റ പേസ്റ്റ് ചെയ്യുക. ടൂൾ JSON ഒബ്ജക്റ്റുകൾ ലൈൻ ബൈ ലൈൻ പാഴ്സ് ചെയ്യുന്നു, വലിയ ഫയൽ സ്ട്രീമിംഗ് പ്രോസസ്സിംഗും പിശക് ലൈൻ സ്കിപ്പിംഗ് പ്രവർത്തനക്ഷമതയും പിന്തുണയ്ക്കുന്നു."
    step3: "ഓരോ ലൈനും പൂർണ്ണമായ JSON ഒബ്ജക്റ്റ് ഔട്ട്പുട്ട് ചെയ്യുന്ന സ്റ്റാൻഡേർഡ് JSONLines ഫോർമാറ്റ് ജനറേറ്റ് ചെയ്യുക. സ്ട്രീമിംഗ് പ്രോസസ്സിംഗ്, ബാച്ച് ഇമ്പോർട്ട്, ബിഗ് ഡാറ്റ അനാലിസിസ് സാഹചര്യങ്ങൾക്ക് അനുയോജ്യം, ഡാറ്റ വാലിഡേഷനും ഫോർമാറ്റ് ഒപ്റ്റിമൈസേഷനും പിന്തുണയ്ക്കുന്നു."
    from_alias: "JSONLines ഡാറ്റ"
    to_alias: "JSONLines സ്ട്രീമിംഗ് ഫോർമാറ്റ്"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) എന്റർപ്രൈസ്-ലെവൽ ഡാറ്റ എക്സ്ചേഞ്ചിനും കോൺഫിഗറേഷൻ മാനേജ്മെന്റിനുമുള്ള സ്റ്റാൻഡേർഡ് ഫോർമാറ്റാണ്, കർശനമായ സിന്റാക്സ് സ്പെസിഫിക്കേഷനുകളും ശക്തമായ വാലിഡേഷൻ മെക്കാനിസങ്ങളുമുണ്ട്. വെബ് സേവനങ്ങൾ, കോൺഫിഗറേഷൻ ഫയലുകൾ, ഡോക്യുമെന്റ് സ്റ്റോറേജ്, സിസ്റ്റം ഇന്റഗ്രേഷൻ എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു. നെയിംസ്പേസുകൾ, സ്കീമ വാലിഡേഷൻ, XSLT ട്രാൻസ്ഫോർമേഷൻ എന്നിവയെ പിന്തുണയ്ക്കുന്നു, എന്റർപ്രൈസ് ആപ്ലിക്കേഷനുകൾക്ക് പ്രധാനപ്പെട്ട ടേബിൾ ഡാറ്റയാക്കുന്നു."
    step1: "XML ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ XML ഡാറ്റ പേസ്റ്റ് ചെയ്യുക. ടൂൾ സ്വയമേവ XML ഘടന പാഴ്സ് ചെയ്യുകയും അതിനെ ടേബിൾ ഫോർമാറ്റിലേക്ക് പരിവർത്തനം ചെയ്യുകയും ചെയ്യുന്നു, നെയിംസ്പേസ്, ആട്രിബ്യൂട്ട് കൈകാര്യം, സങ്കീർണ്ണമായ നെസ്റ്റഡ് ഘടനകൾ എന്നിവയെ പിന്തുണയ്ക്കുന്നു."
    step3: "XML സ്റ്റാൻഡേർഡുകൾ പാലിക്കുന്ന XML ഔട്ട്പുട്ട് ജനറേറ്റ് ചെയ്യുക. കസ്റ്റം റൂട്ട് എലിമെന്റുകൾ, റോ എലിമെന്റ് നാമങ്ങൾ, ആട്രിബ്യൂട്ട് മോഡുകൾ, CDATA റാപ്പിംഗ്, കാരക്ടർ എൻകോഡിംഗ് സെറ്റിംഗുകൾ എന്നിവയെ പിന്തുണയ്ക്കുന്നു. ഡാറ്റ സമഗ്രതയും പൊരുത്തപ്പെടുത്തലും ഉറപ്പാക്കുന്നു, എന്റർപ്രൈസ്-ലെവൽ ആപ്ലിക്കേഷൻ ആവശ്യകതകൾ നിറവേറ്റുന്നു."
    from_alias: "XML ഡാറ്റ ഫയൽ"
    to_alias: "XML സ്റ്റാൻഡേർഡ് ഫോർമാറ്റ്"
  YAML:
    alias: "YAML കോൺഫിഗറേഷൻ"
    what: "YAML മനുഷ്യ-സൗഹൃദമായ ഡാറ്റ സീരിയലൈസേഷൻ സ്റ്റാൻഡേർഡാണ്, അതിന്റെ വ്യക്തമായ ശ്രേണിപരമായ ഘടനയ്ക്കും സംക്ഷിപ്ത സിന്റാക്സിനും പ്രശസ്തമാണ്. കോൺഫിഗറേഷൻ ഫയലുകൾ, DevOps ടൂൾ ചെയിനുകൾ, Docker Compose, Kubernetes ഡിപ്ലോയ്മെന്റ് എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു. ഇതിന്റെ ശക്തമായ വായനാക്ഷമതയും സംക്ഷിപ്ത സിന്റാക്സും ആധുനിക ക്ലൗഡ്-നേറ്റീവ് ആപ്ലിക്കേഷനുകൾക്കും ഓട്ടോമേറ്റഡ് ഓപ്പറേഷനുകൾക്കുമുള്ള പ്രധാന കോൺഫിഗറേഷൻ ഫോർമാറ്റാക്കുന്നു."
    step1: "YAML ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ YAML ഡാറ്റ പേസ്റ്റ് ചെയ്യുക. ടൂൾ YAML ഘടന ബുദ്ധിപരമായി പാഴ്സ് ചെയ്യുകയും സിന്റാക്സ് കൃത്യത വാലിഡേറ്റ് ചെയ്യുകയും ചെയ്യുന്നു, മൾട്ടി-ഡോക്യുമെന്റ് ഫോർമാറ്റുകളും സങ്കീർണ്ണമായ ഡാറ്റ തരങ്ങളും പിന്തുണയ്ക്കുന്നു."
    step3: "ബ്ലോക്ക്, ഫ്ലോ അറേ സ്റ്റൈലുകൾ, ഒന്നിലധികം കോട്ട് സെറ്റിംഗുകൾ, കസ്റ്റം ഇൻഡന്റേഷൻ, കമന്റ് സംരക്ഷണം എന്നിവയ്ക്കുള്ള പിന്തുണയോടെ സ്റ്റാൻഡേർഡ് YAML ഫോർമാറ്റ് ഔട്ട്പുട്ട് ജനറേറ്റ് ചെയ്യുക. ഔട്ട്പുട്ട് YAML ഫയലുകൾ വിവിധ പാഴ്സറുകളുമായും കോൺഫിഗറേഷൻ സിസ്റ്റങ്ങളുമായും പൂർണ്ണമായി പൊരുത്തപ്പെടുന്നുവെന്ന് ഉറപ്പാക്കുന്നു."
    from_alias: "YAML കോൺഫിഗറേഷൻ ഫയൽ"
    to_alias: "YAML സ്റ്റാൻഡേർഡ് ഫോർമാറ്റ്"
  MySQL:
      alias: "MySQL ക്വറി ഫലങ്ങൾ"
      what: "MySQL ലോകത്തിലെ ഏറ്റവും ജനപ്രിയമായ ഓപ്പൺ-സോഴ്സ് റിലേഷണൽ ഡാറ്റാബേസ് മാനേജ്മെന്റ് സിസ്റ്റമാണ്, ഉയർന്ന പ്രകടനം, വിശ്വാസ്യത, ഉപയോഗ എളുപ്പം എന്നിവയ്ക്ക് പ്രശസ്തമാണ്. വെബ് ആപ്ലിക്കേഷനുകൾ, എന്റർപ്രൈസ് സിസ്റ്റങ്ങൾ, ഡാറ്റ അനാലിസിസ് പ്ലാറ്റ്ഫോമുകൾ എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു. MySQL ക്വറി ഫലങ്ങളിൽ സാധാരണയായി ഘടനാപരമായ ടേബിൾ ഡാറ്റ അടങ്ങിയിരിക്കുന്നു, ഡാറ്റാബേസ് മാനേജ്മെന്റിലും ഡാറ്റ അനാലിസിസ് ജോലികളിലും പ്രധാന ഡാറ്റ സോഴ്സായി പ്രവർത്തിക്കുന്നു."
      step1: "MySQL ക്വറി ഔട്ട്പുട്ട് ഫലങ്ങൾ ഡാറ്റ സോഴ്സ് ഏരിയയിൽ പേസ്റ്റ് ചെയ്യുക. ടൂൾ സ്വയമേവ MySQL കമാൻഡ്-ലൈൻ ഔട്ട്പുട്ട് ഫോർമാറ്റ് തിരിച്ചറിയുകയും പാഴ്സ് ചെയ്യുകയും ചെയ്യുന്നു, വിവിധ ക്വറി ഫലം സ്റ്റൈലുകളും കാരക്ടർ എൻകോഡിംഗുകളും പിന്തുണയ്ക്കുന്നു, ഹെഡറുകളും ഡാറ്റ വരികളും ബുദ്ധിപരമായി കൈകാര്യം ചെയ്യുന്നു."
      step3: "MySQL ക്വറി ഫലങ്ങൾ ഒന്നിലധികം ടേബിൾ ഡാറ്റ ഫോർമാറ്റുകളിലേക്ക് വേഗത്തിൽ പരിവർത്തനം ചെയ്യുക, ഡാറ്റ അനാലിസിസ്, റിപ്പോർട്ട് ജനറേഷൻ, ക്രോസ്-സിസ്റ്റം ഡാറ്റ മൈഗ്രേഷൻ, ഡാറ്റ വാലിഡേഷൻ എന്നിവ സുഗമമാക്കുന്നു. ഡാറ്റാബേസ് അഡ്മിനിസ്ട്രേറ്റർമാർക്കും ഡാറ്റ അനലിസ്റ്റുകൾക്കുമുള്ള പ്രായോഗിക ഉപകരണം."
      from_alias: "MySQL ക്വറി ഔട്ട്പുട്ട്"
      to_alias: "MySQL ടേബിൾ ഡാറ്റ"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) റിലേഷണൽ ഡാറ്റാബേസുകൾക്കുള്ള സ്റ്റാൻഡേർഡ് ഓപ്പറേഷൻ ഭാഷയാണ്, ഡാറ്റ ക്വറി, ഇൻസേർട്ട്, അപ്ഡേറ്റ്, ഡിലീറ്റ് ഓപ്പറേഷനുകൾക്കായി ഉപയോഗിക്കുന്നു. ഡാറ്റാബേസ് മാനേജ്മെന്റിന്റെ കോർ ടെക്നോളജി എന്ന നിലയിൽ, SQL ഡാറ്റ അനാലിസിസ്, ബിസിനസ് ഇന്റലിജൻസ്, ETL പ്രോസസ്സിംഗ്, ഡാറ്റ വെയർഹൗസ് നിർമ്മാണം എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു. ഡാറ്റ പ്രൊഫഷണലുകൾക്ക് അത്യന്താപേക്ഷിതമായ സ്കിൽ ടൂളാണിത്."
    step1: "INSERT SQL സ്റ്റേറ്റ്മെന്റുകൾ പേസ്റ്റ് ചെയ്യുക അല്ലെങ്കിൽ .sql ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക. ടൂൾ SQL സിന്റാക്സ് ബുദ്ധിപരമായി പാഴ്സ് ചെയ്യുകയും ടേബിൾ ഡാറ്റ എക്സ്ട്രാക്റ്റ് ചെയ്യുകയും ചെയ്യുന്നു, ഒന്നിലധികം SQL ഡയലക്റ്റുകളും സങ്കീർണ്ണമായ ക്വറി സ്റ്റേറ്റ്മെന്റ് പ്രോസസ്സിംഗും പിന്തുണയ്ക്കുന്നു."
    step3: "സ്റ്റാൻഡേർഡ് SQL INSERT സ്റ്റേറ്റ്മെന്റുകളും ടേബിൾ ക്രിയേഷൻ സ്റ്റേറ്റ്മെന്റുകളും ജനറേറ്റ് ചെയ്യുക. ഒന്നിലധികം ഡാറ്റാബേസ് ഡയലക്റ്റുകൾ (MySQL, PostgreSQL, SQLite, SQL Server, Oracle) പിന്തുണയ്ക്കുന്നു, ഡാറ്റ ടൈപ്പ് മാപ്പിംഗ്, കാരക്ടർ എസ്കേപ്പിംഗ്, പ്രൈമറി കീ നിയന്ത്രണങ്ങൾ എന്നിവ സ്വയമേവ കൈകാര്യം ചെയ്യുന്നു. ജനറേറ്റ് ചെയ്ത SQL കോഡ് നേരിട്ട് എക്സിക്യൂട്ട് ചെയ്യാൻ കഴിയുമെന്ന് ഉറപ്പാക്കുന്നു."
    from_alias: "SQL ഡാറ്റ ഫയൽ"
    to_alias: "SQL സ്റ്റാൻഡേർഡ് സ്റ്റേറ്റ്മെന്റ്"
  Qlik:
      alias: "Qlik ടേബിൾ"
      what: "Qlik ഡാറ്റ വിഷ്വലൈസേഷൻ, എക്സിക്യൂട്ടീവ് ഡാഷ്ബോർഡുകൾ, സെൽഫ്-സർവിസ് ബിസിനസ് ഇന്റലിജൻസ് ഉൽപ്പന്നങ്ങൾ എന്നിവയിൽ വിശേഷജ്ഞതയുള്ള ഒരു സോഫ്റ്റ്വെയർ വെണ്ടറാണ്, Tableau, Microsoft എന്നിവയ്ക്കൊപ്പം."
      step1: ""
      step3: "അവസാനമായി, [ടേബിൾ ജനറേറ്റർ](#TableGenerator) കൺവേർഷൻ ഫലങ്ങൾ കാണിക്കുന്നു. നിങ്ങളുടെ Qlik Sense, Qlik AutoML, QlikView, അല്ലെങ്കിൽ മറ്റ് Qlik-പ്രാപ്തമായ സോഫ്റ്റ്വെയറിൽ ഉപയോഗിക്കുക."
      from_alias: "Qlik ടേബിൾ"
      to_alias: "Qlik ടേബിൾ"
  DAX:
      alias: "DAX ടേബിൾ"
      what: "DAX (Data Analysis Expressions) കാൽക്കുലേറ്റഡ് കോളങ്ങൾ, മെഷറുകൾ, കസ്റ്റം ടേബിളുകൾ എന്നിവ സൃഷ്ടിക്കുന്നതിനായി Microsoft Power BI-യിൽ ഉടനീളം ഉപയോഗിക്കുന്ന ഒരു പ്രോഗ്രാമിംഗ് ഭാഷയാണ്."
      step1: ""
      step3: "അവസാനമായി, [ടേബിൾ ജനറേറ്റർ](#TableGenerator) കൺവേർഷൻ ഫലങ്ങൾ കാണിക്കുന്നു. പ്രതീക്ഷിച്ചതുപോലെ, Microsoft Power BI, Microsoft Analysis Services, Excel-നുള്ള Microsoft Power Pivot എന്നിവയുൾപ്പെടെ നിരവധി Microsoft ഉൽപ്പന്നങ്ങളിൽ ഇത് ഉപയോഗിക്കുന്നു."
      from_alias: "DAX ടേബിൾ"
      to_alias: "DAX ടേബിൾ"
  Firebase:
    alias: "Firebase ലിസ്റ്റ്"
    what: "Firebase റിയൽ-ടൈം ഡാറ്റാബേസ്, ക്ലൗഡ് സ്റ്റോറേജ്, ഓതന്റിക്കേഷൻ, ക്രാഷ് റിപ്പോർട്ടിംഗ് മുതലായ ഹോസ്റ്റഡ് ബാക്കെൻഡ് സേവനങ്ങൾ നൽകുന്ന ഒരു BaaS ആപ്ലിക്കേഷൻ ഡെവലപ്മെന്റ് പ്ലാറ്റ്ഫോമാണ്."
    step1: ""
    step3: "അവസാനമായി, [ടേബിൾ ജനറേറ്റർ](#TableGenerator) കൺവേർഷൻ ഫലങ്ങൾ കാണിക്കുന്നു. തുടർന്ന് Firebase ഡാറ്റാബേസിലെ ഡാറ്റയുടെ ലിസ്റ്റിലേക്ക് ചേർക്കുന്നതിന് Firebase API-യിലെ push മെത്തഡ് ഉപയോഗിക്കാം."
    from_alias: "Firebase ലിസ്റ്റ്"
    to_alias: "Firebase ലിസ്റ്റ്"
  HTML:
    alias: "HTML ടേബിൾ"
    what: "HTML ടേബിളുകൾ വെബ് പേജുകളിൽ ഘടനാപരമായ ഡാറ്റ പ്രദർശിപ്പിക്കുന്നതിനുള്ള സ്റ്റാൻഡേർഡ് മാർഗമാണ്, table, tr, td എന്നിവയും മറ്റ് ടാഗുകളും ഉപയോഗിച്ച് നിർമ്മിച്ചിരിക്കുന്നു. സമ്പന്നമായ സ്റ്റൈൽ കസ്റ്റമൈസേഷൻ, റെസ്പോൺസീവ് ലേഔട്ട്, ഇന്ററാക്ടീവ് പ്രവർത്തനക്ഷമത എന്നിവയെ പിന്തുണയ്ക്കുന്നു. വെബ്സൈറ്റ് വികസനം, ഡാറ്റ പ്രദർശനം, റിപ്പോർട്ട് ജനറേഷൻ എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു, ഫ്രണ്ട്-എൻഡ് വികസനത്തിന്റെയും വെബ് ഡിസൈനിന്റെയും പ്രധാന ഘടകമായി പ്രവർത്തിക്കുന്നു."
    step1: "ടേബിളുകൾ അടങ്ങിയ HTML കോഡ് പേസ്റ്റ് ചെയ്യുക അല്ലെങ്കിൽ HTML ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക. ടൂൾ സ്വയമേവ പേജുകളിൽ നിന്ന് ടേബിൾ ഡാറ്റ തിരിച്ചറിയുകയും എക്‌സ്‌ട്രാക്റ്റ് ചെയ്യുകയും ചെയ്യുന്നു, സങ്കീർണ്ണമായ HTML ഘടനകൾ, CSS സ്റ്റൈലുകൾ, നെസ്റ്റഡ് ടേബിൾ പ്രോസസ്സിംഗ് എന്നിവയെ പിന്തുണയ്ക്കുന്നു."
    step3: "thead/tbody ഘടന, CSS ക്ലാസ് സെറ്റിംഗുകൾ, ടേബിൾ ക്യാപ്ഷനുകൾ, റോ/കോളം ഹെഡറുകൾ, റെസ്പോൺസീവ് ആട്രിബ്യൂട്ട് കോൺഫിഗറേഷൻ എന്നിവയുടെ പിന്തുണയോടെ സെമാന്റിക് HTML ടേബിൾ കോഡ് ജനറേറ്റ് ചെയ്യുക. ജനറേറ്റ് ചെയ്ത ടേബിൾ കോഡ് നല്ല ആക്‌സസിബിലിറ്റിയും SEO സൗഹൃദത്വവുമുള്ള വെബ് സ്റ്റാൻഡേർഡുകൾ പാലിക്കുന്നുവെന്ന് ഉറപ്പാക്കുന്നു."
    from_alias: "HTML വെബ് ടേബിൾ"
    to_alias: "HTML സ്റ്റാൻഡേർഡ് ടേബിൾ"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel ലോകത്തിലെ ഏറ്റവും ജനപ്രിയമായ സ്പ്രെഡ്ഷീറ്റ് സോഫ്റ്റ്വെയറാണ്, ബിസിനസ് അനാലിസിസ്, ഫിനാൻഷ്യൽ മാനേജ്മെന്റ്, ഡാറ്റ പ്രോസസ്സിംഗ്, റിപ്പോർട്ട് സൃഷ്ടി എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു. ഇതിന്റെ ശക്തമായ ഡാറ്റ പ്രോസസ്സിംഗ് കഴിവുകൾ, സമ്പന്നമായ ഫംഗ്ഷൻ ലൈബ്രറി, ഫ്ലെക്സിബിൾ വിഷ്വലൈസേഷൻ ഫീച്ചറുകൾ എന്നിവ ഇതിനെ ഓഫീസ് ഓട്ടോമേഷനും ഡാറ്റ അനാലിസിസിനുമുള്ള സ്റ്റാൻഡേർഡ് ടൂളാക്കുന്നു, മിക്കവാറും എല്ലാ വ്യവസായങ്ങളിലും മേഖലകളിലും വിപുലമായ ആപ്ലിക്കേഷനുകളുണ്ട്."
    step1: "Excel ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക (.xlsx, .xls ഫോർമാറ്റുകൾ പിന്തുണയ്ക്കുന്നു) അല്ലെങ്കിൽ Excel-ൽ നിന്ന് ടേബിൾ ഡാറ്റ നേരിട്ട് കോപ്പി ചെയ്ത് പേസ്റ്റ് ചെയ്യുക. ടൂൾ മൾട്ടി-വർക്ക്ഷീറ്റ് പ്രോസസ്സിംഗ്, സങ്കീർണ്ണമായ ഫോർമാറ്റ് തിരിച്ചറിയൽ, വലിയ ഫയലുകളുടെ വേഗത്തിലുള്ള പാഴ്സിംഗ് എന്നിവയെ പിന്തുണയ്ക്കുന്നു, മെർജ്ഡ് സെല്ലുകളും ഡാറ്റ തരങ്ങളും സ്വയമേവ കൈകാര്യം ചെയ്യുന്നു."
    step3: "Excel-ൽ നേരിട്ട് പേസ്റ്റ് ചെയ്യാവുന്നതോ സ്റ്റാൻഡേർഡ് .xlsx ഫയലുകളായി ഡൗൺലോഡ് ചെയ്യാവുന്നതോ ആയ Excel-പൊരുത്തപ്പെടുന്ന ടേബിൾ ഡാറ്റ ജനറേറ്റ് ചെയ്യുക. വർക്ക്ഷീറ്റ് നാമകരണം, സെൽ ഫോർമാറ്റിംഗ്, ഓട്ടോ കോളം വീതി, ഹെഡർ സ്റ്റൈലിംഗ്, ഡാറ്റ വാലിഡേഷൻ സെറ്റിംഗുകൾ എന്നിവയെ പിന്തുണയ്ക്കുന്നു. ഔട്ട്പുട്ട് Excel ഫയലുകൾക്ക് പ്രൊഫഷണൽ രൂപവും പൂർണ്ണമായ പ്രവർത്തനക്ഷമതയും ഉണ്ടെന്ന് ഉറപ്പാക്കുന്നു."
    from_alias: "Excel സ്പ്രെഡ്ഷീറ്റ്"
    to_alias: "Excel സ്റ്റാൻഡേർഡ് ഫോർമാറ്റ്"
  LaTeX:
    alias: "LaTeX ടേബിൾ"
    what: "LaTeX ഒരു പ്രൊഫഷണൽ ഡോക്യുമെന്റ് ടൈപ്പ്സെറ്റിംഗ് സിസ്റ്റമാണ്, പ്രത്യേകിച്ച് അക്കാദമിക് പേപ്പറുകൾ, സാങ്കേതിക ഡോക്യുമെന്റുകൾ, ശാസ്ത്രീയ പ്രസിദ്ധീകരണങ്ങൾ എന്നിവ സൃഷ്ടിക്കുന്നതിന് അനുയോജ്യമാണ്. ഇതിന്റെ ടേബിൾ പ്രവർത്തനക്ഷമത ശക്തമാണ്, സങ്കീർണ്ണമായ ഗണിത സൂത്രവാക്യങ്ങൾ, കൃത്യമായ ലേഔട്ട് നിയന്ത്രണം, ഉയർന്ന ഗുണമേന്മയുള്ള PDF ഔട്ട്പുട്ട് എന്നിവയെ പിന്തുണയ്ക്കുന്നു. അക്കാദമിയിലും ശാസ്ത്രീയ പ്രസിദ്ധീകരണത്തിലുമുള്ള സ്റ്റാൻഡേർഡ് ടൂളാണിത്, ജേണൽ പേപ്പറുകൾ, പ്രബന്ധങ്ങൾ, സാങ്കേതിക മാനുവൽ ടൈപ്പ്സെറ്റിംഗ് എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു."
    step1: "LaTeX ടേബിൾ കോഡ് പേസ്റ്റ് ചെയ്യുക അല്ലെങ്കിൽ .tex ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക. ടൂൾ LaTeX ടേബിൾ സിന്റാക്സ് പാഴ്സ് ചെയ്യുകയും ഡാറ്റ ഉള്ളടക്കം എക്സ്ട്രാക്റ്റ് ചെയ്യുകയും ചെയ്യുന്നു, ഒന്നിലധികം ടേബിൾ എൻവയോൺമെന്റുകൾ (tabular, longtable, array, മുതലായവ), സങ്കീർണ്ണമായ ഫോർമാറ്റ് കമാൻഡുകൾ എന്നിവയെ പിന്തുണയ്ക്കുന്നു."
    step3: "ഒന്നിലധികം ടേബിൾ എൻവയോൺമെന്റ് സെലക്ഷൻ, ബോർഡർ സ്റ്റൈൽ കോൺഫിഗറേഷൻ, ക്യാപ്ഷൻ പൊസിഷൻ സെറ്റിംഗുകൾ, ഡോക്യുമെന്റ് ക്ലാസ് സ്പെസിഫിക്കേഷൻ, പാക്കേജ് മാനേജ്മെന്റ് എന്നിവയ്ക്കുള്ള പിന്തുണയോടെ പ്രൊഫഷണൽ LaTeX ടേബിൾ കോഡ് ജനറേറ്റ് ചെയ്യുക. പൂർണ്ണമായ കംപൈലബിൾ LaTeX ഡോക്യുമെന്റുകൾ ജനറേറ്റ് ചെയ്യാൻ കഴിയും, ഔട്ട്പുട്ട് ടേബിളുകൾ അക്കാദമിക് പ്രസിദ്ധീകരണ മാനദണ്ഡങ്ങൾ പാലിക്കുന്നുവെന്ന് ഉറപ്പാക്കുന്നു."
    from_alias: "LaTeX ഡോക്യുമെന്റ് ടേബിൾ"
    to_alias: "LaTeX പ്രൊഫഷണൽ ഫോർമാറ്റ്"
  ASCII:
    alias: "ASCII ടേബിൾ"
    what: "ASCII ടേബിളുകൾ ടേബിൾ ബോർഡറുകളും ഘടനകളും വരയ്ക്കാൻ പ്ലെയിൻ ടെക്സ്റ്റ് കാരക്ടറുകൾ ഉപയോഗിക്കുന്നു, മികച്ച പൊരുത്തപ്പെടുത്തലും പോർട്ടബിലിറ്റിയും നൽകുന്നു. എല്ലാ ടെക്സ്റ്റ് എഡിറ്ററുകൾ, ടെർമിനൽ എൻവയോൺമെന്റുകൾ, ഓപ്പറേറ്റിംഗ് സിസ്റ്റങ്ങൾ എന്നിവയുമായി പൊരുത്തപ്പെടുന്നു. കോഡ് ഡോക്യുമെന്റേഷൻ, സാങ്കേതിക മാനുവലുകൾ, README ഫയലുകൾ, കമാൻഡ്-ലൈൻ ടൂൾ ഔട്ട്പുട്ട് എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു. പ്രോഗ്രാമർമാർക്കും സിസ്റ്റം അഡ്മിനിസ്ട്രേറ്റർമാർക്കുമുള്ള ഇഷ്ട ഡാറ്റ ഡിസ്പ്ലേ ഫോർമാറ്റ്."
    step1: "ASCII ടേബിളുകൾ അടങ്ങിയ ടെക്സ്റ്റ് ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ നേരിട്ട് ടേബിൾ ഡാറ്റ പേസ്റ്റ് ചെയ്യുക. ടൂൾ ASCII ടേബിൾ ഘടനകൾ ബുദ്ധിപരമായി തിരിച്ചറിയുകയും പാഴ്സ് ചെയ്യുകയും ചെയ്യുന്നു, ഒന്നിലധികം ബോർഡർ സ്റ്റൈലുകളും അലൈൻമെന്റ് ഫോർമാറ്റുകളും പിന്തുണയ്ക്കുന്നു."
    step3: "ഒന്നിലധികം ബോർഡർ സ്റ്റൈലുകൾ (സിംഗിൾ ലൈൻ, ഡബിൾ ലൈൻ, റൗണ്ടഡ് കോർണറുകൾ, മുതലായവ), ടെക്സ്റ്റ് അലൈൻമെന്റ് രീതികൾ, ഓട്ടോ കോളം വീതി എന്നിവയ്ക്കുള്ള പിന്തുണയോടെ മനോഹരമായ പ്ലെയിൻ ടെക്സ്റ്റ് ASCII ടേബിളുകൾ ജനറേറ്റ് ചെയ്യുക. ജനറേറ്റ് ചെയ്ത ടേബിളുകൾ കോഡ് എഡിറ്ററുകൾ, ഡോക്യുമെന്റുകൾ, കമാൻഡ് ലൈനുകൾ എന്നിവയിൽ പൂർണ്ണമായും പ്രദർശിപ്പിക്കുന്നു."
    from_alias: "ASCII ടെക്സ്റ്റ് ടേബിൾ"
    to_alias: "ASCII സ്റ്റാൻഡേർഡ് ഫോർമാറ്റ്"
  MediaWiki:
    alias: "MediaWiki ടേബിൾ"
    what: "MediaWiki വിക്കിപീഡിയ പോലുള്ള പ്രശസ്തമായ വിക്കി സൈറ്റുകൾ ഉപയോഗിക്കുന്ന ഓപ്പൺ-സോഴ്സ് സോഫ്റ്റ്വെയർ പ്ലാറ്റ്ഫോമാണ്. ഇതിന്റെ ടേബിൾ സിന്റാക്സ് സംക്ഷിപ്തവും എന്നാൽ ശക്തവുമാണ്, ടേബിൾ സ്റ്റൈൽ കസ്റ്റമൈസേഷൻ, സോർട്ടിംഗ് പ്രവർത്തനക്ഷമത, ലിങ്ക് എംബെഡിംഗ് എന്നിവയെ പിന്തുണയ്ക്കുന്നു. വിജ്ഞാന മാനേജ്മെന്റ്, സഹകരണ എഡിറ്റിംഗ്, ഉള്ളടക്ക മാനേജ്മെന്റ് സിസ്റ്റങ്ങൾ എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു, വിക്കി എൻസൈക്ലോപീഡിയകളും വിജ്ഞാന അടിത്തറകളും നിർമ്മിക്കുന്നതിനുള്ള കോർ ടെക്നോളജിയായി പ്രവർത്തിക്കുന്നു."
    step1: "MediaWiki ടേബിൾ കോഡ് പേസ്റ്റ് ചെയ്യുക അല്ലെങ്കിൽ വിക്കി സോഴ്സ് ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക. ടൂൾ വിക്കി മാർക്കപ്പ് സിന്റാക്സ് പാഴ്സ് ചെയ്യുകയും ടേബിൾ ഡാറ്റ എക്സ്ട്രാക്റ്റ് ചെയ്യുകയും ചെയ്യുന്നു, സങ്കീർണ്ണമായ വിക്കി സിന്റാക്സും ടെംപ്ലേറ്റ് പ്രോസസ്സിംഗും പിന്തുണയ്ക്കുന്നു."
    step3: "ഹെഡർ സ്റ്റൈൽ സെറ്റിംഗുകൾ, സെൽ അലൈൻമെന്റ്, സോർട്ടിംഗ് പ്രവർത്തനക്ഷമത പ്രാപ്തമാക്കൽ, കോഡ് കംപ്രഷൻ ഓപ്ഷനുകൾ എന്നിവയ്ക്കുള്ള പിന്തുണയോടെ സ്റ്റാൻഡേർഡ് MediaWiki ടേബിൾ കോഡ് ജനറേറ്റ് ചെയ്യുക. ജനറേറ്റ് ചെയ്ത കോഡ് വിക്കി പേജ് എഡിറ്റിംഗിനായി നേരിട്ട് ഉപയോഗിക്കാം, MediaWiki പ്ലാറ്റ്ഫോമുകളിൽ പൂർണ്ണമായ പ്രദർശനം ഉറപ്പാക്കുന്നു."
    from_alias: "MediaWiki സോഴ്സ് കോഡ്"
    to_alias: "MediaWiki ടേബിൾ സിന്റാക്സ്"
  TracWiki:
    alias: "TracWiki ടേബിൾ"
    what: "Trac ടേബിൾ ഉള്ളടക്കം സൃഷ്ടിക്കുന്നതിന് ലളിതമാക്കിയ വിക്കി സിന്റാക്സ് ഉപയോഗിക്കുന്ന വെബ്-അധിഷ്ഠിത പ്രോജക്റ്റ് മാനേജ്മെന്റും ബഗ് ട്രാക്കിംഗ് സിസ്റ്റവുമാണ്."
    step1: "TracWiki ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ ടേബിൾ ഡാറ്റ പേസ്റ്റ് ചെയ്യുക."
    step3: "റോ/കോളം ഹെഡർ സെറ്റിംഗുകൾക്കുള്ള പിന്തുണയോടെ TracWiki-പൊരുത്തപ്പെടുന്ന ടേബിൾ കോഡ് ജനറേറ്റ് ചെയ്യുക, പ്രോജക്റ്റ് ഡോക്യുമെന്റ് മാനേജ്മെന്റ് സുഗമമാക്കുന്നു."
    from_alias: "TracWiki ടേബിൾ"
    to_alias: "TracWiki ഫോർമാറ്റ്"
  AsciiDoc:
    alias: "AsciiDoc ടേബിൾ"
    what: "AsciiDoc HTML, PDF, മാനുവൽ പേജുകൾ, മറ്റ് ഫോർമാറ്റുകൾ എന്നിവയിലേക്ക് പരിവർത്തനം ചെയ്യാൻ കഴിയുന്ന ഒരു ലഘുവായ മാർക്കപ്പ് ഭാഷയാണ്, സാങ്കേതിക ഡോക്യുമെന്റേഷൻ എഴുത്തിനായി വ്യാപകമായി ഉപയോഗിക്കുന്നു."
    step1: "AsciiDoc ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ ഡാറ്റ പേസ്റ്റ് ചെയ്യുക."
    step3: "ഹെഡർ, ഫൂട്ടർ, ടൈറ്റിൽ സെറ്റിംഗുകൾക്കുള്ള പിന്തുണയോടെ AsciiDoc ടേബിൾ സിന്റാക്സ് ജനറേറ്റ് ചെയ്യുക, AsciiDoc എഡിറ്ററുകളിൽ നേരിട്ട് ഉപയോഗിക്കാം."
    from_alias: "AsciiDoc ടേബിൾ"
    to_alias: "AsciiDoc ഫോർമാറ്റ്"
  reStructuredText:
    alias: "reStructuredText ടേബിൾ"
    what: "reStructuredText Python കമ്മ്യൂണിറ്റിയുടെ സ്റ്റാൻഡേർഡ് ഡോക്യുമെന്റേഷൻ ഫോർമാറ്റാണ്, സമ്പന്നമായ ടേബിൾ സിന്റാക്സിനെ പിന്തുണയ്ക്കുന്നു, Sphinx ഡോക്യുമെന്റേഷൻ ജനറേഷനായി സാധാരണയായി ഉപയോഗിക്കുന്നു."
    step1: ".rst ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ reStructuredText ഡാറ്റ പേസ്റ്റ് ചെയ്യുക."
    step3: "ഒന്നിലധികം ബോർഡർ സ്റ്റൈലുകൾക്കുള്ള പിന്തുണയോടെ സ്റ്റാൻഡേർഡ് reStructuredText ടേബിളുകൾ ജനറേറ്റ് ചെയ്യുക, Sphinx ഡോക്യുമെന്റേഷൻ പ്രോജക്റ്റുകളിൽ നേരിട്ട് ഉപയോഗിക്കാം."
    from_alias: "reStructuredText ടേബിൾ"
    to_alias: "reStructuredText ഫോർമാറ്റ്"
  PHP:
    alias: "PHP അറേ"
    what: "PHP ഒരു ജനപ്രിയ സെർവർ-സൈഡ് സ്ക്രിപ്റ്റിംഗ് ഭാഷയാണ്, അറേകൾ അതിന്റെ കോർ ഡാറ്റ ഘടനയാണ്, വെബ് ഡെവലപ്മെന്റിലും ഡാറ്റ പ്രോസസ്സിംഗിലും വ്യാപകമായി ഉപയോഗിക്കുന്നു."
    step1: "PHP അറേകൾ അടങ്ങിയ ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ നേരിട്ട് ഡാറ്റ പേസ്റ്റ് ചെയ്യുക."
    step3: "PHP പ്രോജക്റ്റുകളിൽ നേരിട്ട് ഉപയോഗിക്കാൻ കഴിയുന്ന സ്റ്റാൻഡേർഡ് PHP അറേ കോഡ് ജനറേറ്റ് ചെയ്യുക, അസോസിയേറ്റീവ്, ഇൻഡക്സ്ഡ് അറേ ഫോർമാറ്റുകളെ പിന്തുണയ്ക്കുന്നു."
    from_alias: "PHP അറേ"
    to_alias: "PHP കോഡ്"
  Ruby:
    alias: "Ruby അറേ"
    what: "Ruby സംക്ഷിപ്തവും ഗംഭീരവുമായ സിന്റാക്സുള്ള ഒരു ഡൈനാമിക് ഒബ്ജക്റ്റ്-ഓറിയന്റഡ് പ്രോഗ്രാമിംഗ് ഭാഷയാണ്, അറേകൾ ഒരു പ്രധാന ഡാറ്റ ഘടനയാണ്."
    step1: "Ruby ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ അറേ ഡാറ്റ പേസ്റ്റ് ചെയ്യുക."
    step3: "Ruby സിന്റാക്സ് സ്പെസിഫിക്കേഷനുകൾ പാലിക്കുന്ന Ruby അറേ കോഡ് ജനറേറ്റ് ചെയ്യുക, Ruby പ്രോജക്റ്റുകളിൽ നേരിട്ട് ഉപയോഗിക്കാം."
    from_alias: "Ruby അറേ"
    to_alias: "Ruby കോഡ്"
  ASP:
    alias: "ASP അറേ"
    what: "ASP (Active Server Pages) ഡൈനാമിക് വെബ് പേജുകൾ വികസിപ്പിക്കുന്നതിനായി ഒന്നിലധികം പ്രോഗ്രാമിംഗ് ഭാഷകളെ പിന്തുണയ്ക്കുന്ന Microsoft-ന്റെ സെർവർ-സൈഡ് സ്ക്രിപ്റ്റിംഗ് എൻവയോൺമെന്റാണ്."
    step1: "ASP ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ അറേ ഡാറ്റ പേസ്റ്റ് ചെയ്യുക."
    step3: "VBScript, JScript സിന്റാക്സിനുള്ള പിന്തുണയോടെ ASP-പൊരുത്തപ്പെടുന്ന അറേ കോഡ് ജനറേറ്റ് ചെയ്യുക, ASP.NET പ്രോജക്റ്റുകളിൽ ഉപയോഗിക്കാം."
    from_alias: "ASP അറേ"
    to_alias: "ASP കോഡ്"
  ActionScript:
    alias: "ActionScript അറേ"
    what: "ActionScript പ്രാഥമികമായി Adobe Flash, AIR ആപ്ലിക്കേഷൻ ഡെവലപ്മെന്റിനായി ഉപയോഗിക്കുന്ന ഒരു ഒബ്ജക്റ്റ്-ഓറിയന്റഡ് പ്രോഗ്രാമിംഗ് ഭാഷയാണ്."
    step1: ".as ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ ActionScript ഡാറ്റ പേസ്റ്റ് ചെയ്യുക."
    step3: "AS3 സിന്റാക്സ് സ്റ്റാൻഡേർഡുകൾ പാലിക്കുന്ന ActionScript അറേ കോഡ് ജനറേറ്റ് ചെയ്യുക, Flash, Flex പ്രോജക്റ്റ് ഡെവലപ്മെന്റിനായി ഉപയോഗിക്കാം."
    from_alias: "ActionScript അറേ"
    to_alias: "ActionScript കോഡ്"
  BBCode:
    alias: "BBCode ടേബിൾ"
    what: "BBCode ഫോറങ്ങളിലും ഓൺലൈൻ കമ്മ്യൂണിറ്റികളിലും സാധാരണയായി ഉപയോഗിക്കുന്ന ഒരു ലഘുവായ മാർക്കപ്പ് ഭാഷയാണ്, ടേബിൾ പിന്തുണ ഉൾപ്പെടെ ലളിതമായ ഫോർമാറ്റിംഗ് പ്രവർത്തനക്ഷമത നൽകുന്നു."
    step1: "BBCode അടങ്ങിയ ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ ഡാറ്റ പേസ്റ്റ് ചെയ്യുക."
    step3: "ഫോറം പോസ്റ്റിംഗിനും കമ്മ്യൂണിറ്റി ഉള്ളടക്ക സൃഷ്ടിക്കുന്നതിനും അനുയോജ്യമായ BBCode ടേബിൾ കോഡ് ജനറേറ്റ് ചെയ്യുക, കംപ്രസ്ഡ് ഔട്ട്പുട്ട് ഫോർമാറ്റിനുള്ള പിന്തുണയോടെ."
    from_alias: "BBCode ടേബിൾ"
    to_alias: "BBCode ഫോർമാറ്റ്"
  PDF:
    alias: "PDF ടേബിൾ"
    what: "PDF (Portable Document Format) സ്ഥിര ലേഔട്ട്, സ്ഥിരമായ പ്രദർശനം, ഉയർന്ന ഗുണമേന്മയുള്ള പ്രിന്റിംഗ് സവിശേഷതകളുള്ള ക്രോസ്-പ്ലാറ്റ്ഫോം ഡോക്യുമെന്റ് സ്റ്റാൻഡേർഡാണ്. ഔപചാരിക ഡോക്യുമെന്റുകൾ, റിപ്പോർട്ടുകൾ, ഇൻവോയ്സുകൾ, കരാറുകൾ, അക്കാദമിക് പേപ്പറുകൾ എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു. ബിസിനസ് കമ്മ്യൂണിക്കേഷനും ഡോക്യുമെന്റ് ആർക്കൈവിംഗിനുമുള്ള ഇഷ്ട ഫോർമാറ്റ്, വിവിധ ഉപകരണങ്ങളിലും ഓപ്പറേറ്റിംഗ് സിസ്റ്റങ്ങളിലും പൂർണ്ണമായും സ്ഥിരമായ വിഷ്വൽ ഇഫക്റ്റുകൾ ഉറപ്പാക്കുന്നു."
    step1: "ഏത് ഫോർമാറ്റിലുമുള്ള ടേബിൾ ഡാറ്റ ഇമ്പോർട്ട് ചെയ്യുക. ടൂൾ സ്വയമേവ ഡാറ്റ ഘടന വിശകലനം ചെയ്യുകയും ബുദ്ധിപരമായ ലേഔട്ട് ഡിസൈൻ നിർവഹിക്കുകയും ചെയ്യുന്നു, വലിയ ടേബിൾ ഓട്ടോ-പേജിനേഷനും സങ്കീർണ്ണമായ ഡാറ്റ ടൈപ്പ് പ്രോസസ്സിംഗും പിന്തുണയ്ക്കുന്നു."
    step3: "ഒന്നിലധികം പ്രൊഫഷണൽ തീം സ്റ്റൈലുകൾ (ബിസിനസ്, അക്കാദമിക്, മിനിമലിസ്റ്റ്, മുതലായവ), മൾട്ടിലിംഗ്വൽ ഫോണ്ടുകൾ, ഓട്ടോ-പേജിനേഷൻ, വാട്ടർമാർക്ക് കൂട്ടിച്ചേർക്കൽ, പ്രിന്റ് ഒപ്റ്റിമൈസേഷൻ എന്നിവയ്ക്കുള്ള പിന്തുണയോടെ ഉയർന്ന ഗുണമേന്മയുള്ള PDF ടേബിൾ ഫയലുകൾ ജനറേറ്റ് ചെയ്യുക. ഔട്ട്പുട്ട് PDF ഡോക്യുമെന്റുകൾക്ക് പ്രൊഫഷണൽ രൂപമുണ്ടെന്ന് ഉറപ്പാക്കുന്നു, ബിസിനസ് പ്രസന്റേഷനുകൾക്കും ഔപചാരിക പ്രസിദ്ധീകരണത്തിനും നേരിട്ട് ഉപയോഗിക്കാം."
    from_alias: "ടേബിൾ ഡാറ്റ"
    to_alias: "PDF പ്രൊഫഷണൽ ഡോക്യുമെന്റ്"
  JPEG:
    alias: "JPEG ഇമേജ്"
    what: "JPEG അതിമനോഹരമായ കംപ്രഷൻ ഇഫക്റ്റുകളും വിശാലമായ പൊരുത്തപ്പെടുത്തലുമുള്ള ഏറ്റവും വ്യാപകമായി ഉപയോഗിക്കുന്ന ഡിജിറ്റൽ ഇമേജ് ഫോർമാറ്റാണ്. ഇതിന്റെ ചെറിയ ഫയൽ സൈസും വേഗത്തിലുള്ള ലോഡിംഗ് സ്പീഡും ഇതിനെ വെബ് ഡിസ്പ്ലേ, സോഷ്യൽ മീഡിയ ഷെയറിംഗ്, ഡോക്യുമെന്റ് ഇല്ലസ്ട്രേഷനുകൾ, ഓൺലൈൻ പ്രസന്റേഷനുകൾ എന്നിവയ്ക്ക് അനുയോജ്യമാക്കുന്നു. ഡിജിറ്റൽ മീഡിയയ്ക്കും നെറ്റ്വർക്ക് കമ്മ്യൂണിക്കേഷനുമുള്ള സ്റ്റാൻഡേർഡ് ഇമേജ് ഫോർമാറ്റ്, മിക്കവാറും എല്ലാ ഉപകരണങ്ങളും സോഫ്റ്റ്വെയറുകളും പൂർണ്ണമായും പിന്തുണയ്ക്കുന്നു."
    step1: "ഏത് ഫോർമാറ്റിലുമുള്ള ടേബിൾ ഡാറ്റ ഇമ്പോർട്ട് ചെയ്യുക. ടൂൾ ബുദ്ധിപരമായ ലേഔട്ട് ഡിസൈനും വിഷ്വൽ ഒപ്റ്റിമൈസേഷനും നിർവഹിക്കുന്നു, ഒപ്റ്റിമൽ സൈസും റെസലൂഷനും സ്വയമേവ കണക്കാക്കുന്നു."
    step3: "ഒന്നിലധികം തീം കളർ സ്കീമുകൾ (ലൈറ്റ്, ഡാർക്ക്, ഐ-ഫ്രണ്ട്ലി, മുതലായവ), അഡാപ്റ്റീവ് ലേഔട്ട്, ടെക്സ്റ്റ് ക്ലാരിറ്റി ഒപ്റ്റിമൈസേഷൻ, സൈസ് കസ്റ്റമൈസേഷൻ എന്നിവയ്ക്കുള്ള പിന്തുണയോടെ ഹൈ-ഡെഫിനിഷൻ JPEG ടേബിൾ ഇമേജുകൾ ജനറേറ്റ് ചെയ്യുക. ഓൺലൈൻ ഷെയറിംഗ്, ഡോക്യുമെന്റ് ഇൻസേർഷൻ, പ്രസന്റേഷൻ ഉപയോഗത്തിന് അനുയോജ്യം, വിവിധ ഡിസ്പ്ലേ ഉപകരണങ്ങളിൽ മികച്ച വിഷ്വൽ ഇഫക്റ്റുകൾ ഉറപ്പാക്കുന്നു."
    from_alias: "ടേബിൾ ഡാറ്റ"
    to_alias: "JPEG ഹൈ-ഡെഫിനിഷൻ ഇമേജ്"
  Jira:
    alias: "Jira ടേബിൾ"
    what: "JIRA Atlassian വികസിപ്പിച്ച പ്രൊഫഷണൽ പ്രോജക്റ്റ് മാനേജ്മെന്റും ബഗ് ട്രാക്കിംഗ് സോഫ്റ്റ്വെയറുമാണ്, അജൈൽ ഡെവലപ്മെന്റ്, സോഫ്റ്റ്വെയർ ടെസ്റ്റിംഗ്, പ്രോജക്റ്റ് കൊളാബറേഷൻ എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു. ഇതിന്റെ ടേബിൾ പ്രവർത്തനക്ഷമത സമ്പന്നമായ ഫോർമാറ്റിംഗ് ഓപ്ഷനുകളും ഡാറ്റ ഡിസ്പ്ലേയും പിന്തുണയ്ക്കുന്നു, ആവശ്യകത മാനേജ്മെന്റ്, ബഗ് ട്രാക്കിംഗ്, പ്രോഗ്രസ് റിപ്പോർട്ടിംഗ് എന്നിവയിൽ സോഫ്റ്റ്വെയർ ഡെവലപ്മെന്റ് ടീമുകൾ, പ്രോജക്റ്റ് മാനേജർമാർ, ഗുണനിലവാര ഉറപ്പ് ഉദ്യോഗസ്ഥർ എന്നിവർക്ക് പ്രധാന ഉപകരണമായി പ്രവർത്തിക്കുന്നു."
    step1: "ടേബിൾ ഡാറ്റ അടങ്ങിയ ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ നേരിട്ട് ഡാറ്റ ഉള്ളടക്കം പേസ്റ്റ് ചെയ്യുക. ടൂൾ സ്വയമേവ ടേബിൾ ഡാറ്റയും പ്രത്യേക കാരക്ടർ എസ്കേപ്പിംഗും പ്രോസസ് ചെയ്യുന്നു."
    step3: "ഹെഡർ സ്റ്റൈൽ സെറ്റിംഗുകൾ, സെൽ അലൈൻമെന്റ്, കാരക്ടർ എസ്കേപ്പ് പ്രോസസ്സിംഗ്, ഫോർമാറ്റ് ഒപ്റ്റിമൈസേഷൻ എന്നിവയ്ക്കുള്ള പിന്തുണയോടെ JIRA പ്ലാറ്റ്ഫോം-പൊരുത്തപ്പെടുന്ന ടേബിൾ കോഡ് ജനറേറ്റ് ചെയ്യുക. ജനറേറ്റ് ചെയ്ത കോഡ് JIRA ഇഷ്യൂ വിവരണങ്ങൾ, കമന്റുകൾ, വിക്കി പേജുകൾ എന്നിവയിൽ നേരിട്ട് പേസ്റ്റ് ചെയ്യാം, JIRA സിസ്റ്റങ്ങളിൽ ശരിയായ ഡിസ്പ്ലേയും റെൻഡറിംഗും ഉറപ്പാക്കുന്നു."
    from_alias: "പ്രോജക്റ്റ് ഡാറ്റ"
    to_alias: "Jira ടേബിൾ സിന്റാക്സ്"
  Textile:
    alias: "Textile ടേബിൾ"
    what: "Textile ലളിതവും പഠിക്കാൻ എളുപ്പവുമായ സിന്റാക്സുള്ള ഒരു സംക്ഷിപ്ത ലഘുവായ മാർക്കപ്പ് ഭാഷയാണ്, ഉള്ളടക്ക മാനേജ്മെന്റ് സിസ്റ്റങ്ങൾ, ബ്ലോഗ് പ്ലാറ്റ്ഫോമുകൾ, ഫോറം സിസ്റ്റങ്ങൾ എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു. ഇതിന്റെ ടേബിൾ സിന്റാക്സ് വ്യക്തവും അവബോധജന്യവുമാണ്, ദ്രുത ഫോർമാറ്റിംഗും സ്റ്റൈൽ സെറ്റിംഗുകളും പിന്തുണയ്ക്കുന്നു. ദ്രുത ഡോക്യുമെന്റ് എഴുത്തിനും ഉള്ളടക്ക പ്രസിദ്ധീകരണത്തിനുമായി ഉള്ളടക്ക സൃഷ്ടാക്കളും വെബ്സൈറ്റ് അഡ്മിനിസ്ട്രേറ്റർമാർക്കുമുള്ള അനുയോജ്യമായ ഉപകരണം."
    step1: "Textile ഫോർമാറ്റ് ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ ടേബിൾ ഡാറ്റ പേസ്റ്റ് ചെയ്യുക. ടൂൾ Textile മാർക്കപ്പ് സിന്റാക്സ് പാഴ്സ് ചെയ്യുകയും ടേബിൾ ഉള്ളടക്കം എക്സ്ട്രാക്റ്റ് ചെയ്യുകയും ചെയ്യുന്നു."
    step3: "ഹെഡർ മാർക്കപ്പ്, സെൽ അലൈൻമെന്റ്, പ്രത്യേക കാരക്ടർ എസ്കേപ്പിംഗ്, ഫോർമാറ്റ് ഒപ്റ്റിമൈസേഷൻ എന്നിവയ്ക്കുള്ള പിന്തുണയോടെ സ്റ്റാൻഡേർഡ് Textile ടേബിൾ സിന്റാക്സ് ജനറേറ്റ് ചെയ്യുക. ജനറേറ്റ് ചെയ്ത കോഡ് CMS സിസ്റ്റങ്ങൾ, ബ്ലോഗ് പ്ലാറ്റ്ഫോമുകൾ, Textile പിന്തുണയ്ക്കുന്ന ഡോക്യുമെന്റ് സിസ്റ്റങ്ങൾ എന്നിവയിൽ നേരിട്ട് ഉപയോഗിക്കാം, ശരിയായ ഉള്ളടക്ക റെൻഡറിംഗും ഡിസ്പ്ലേയും ഉറപ്പാക്കുന്നു."
    from_alias: "Textile ഡോക്യുമെന്റ്"
    to_alias: "Textile ടേബിൾ സിന്റാക്സ്"
  PNG:
    alias: "PNG ഇമേജ്"
    what: "PNG (Portable Network Graphics) മികച്ച കംപ്രഷനും ട്രാൻസ്പാരൻസി പിന്തുണയുമുള്ള ഒരു ലോസ്ലെസ് ഇമേജ് ഫോർമാറ്റാണ്. വെബ് ഡിസൈൻ, ഡിജിറ്റൽ ഗ്രാഫിക്സ്, പ്രൊഫഷണൽ ഫോട്ടോഗ്രാഫി എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു. ഇതിന്റെ ഉയർന്ന ഗുണനിലവാരവും വിശാലമായ പൊരുത്തപ്പെടുത്തലും ഇതിനെ സ്ക്രീൻഷോട്ടുകൾ, ലോഗോകൾ, ഡയഗ്രാമുകൾ, മൂർച്ചയുള്ള വിശദാംശങ്ങളും ട്രാൻസ്പാരൻറ് ബാക്ക്ഗ്രൗണ്ടുകളും ആവശ്യമുള്ള ഏത് ഇമേജുകൾക്കും അനുയോജ്യമാക്കുന്നു."
    step1: "ഏത് ഫോർമാറ്റിലുമുള്ള ടേബിൾ ഡാറ്റ ഇമ്പോർട്ട് ചെയ്യുക. ടൂൾ ബുദ്ധിപരമായ ലേഔട്ട് ഡിസൈനും വിഷ്വൽ ഒപ്റ്റിമൈസേഷനും നിർവഹിക്കുന്നു, PNG ഔട്ട്പുട്ടിനായി ഒപ്റ്റിമൽ സൈസും റെസലൂഷനും സ്വയമേവ കണക്കാക്കുന്നു."
    step3: "ഒന്നിലധികം തീം കളർ സ്കീമുകൾ, ട്രാൻസ്പാരൻറ് ബാക്ക്ഗ്രൗണ്ടുകൾ, അഡാപ്റ്റീവ് ലേഔട്ട്, ടെക്സ്റ്റ് ക്ലാരിറ്റി ഒപ്റ്റിമൈസേഷൻ എന്നിവയ്ക്കുള്ള പിന്തുണയോടെ ഉയർന്ന ഗുണമേന്മയുള്ള PNG ടേബിൾ ഇമേജുകൾ ജനറേറ്റ് ചെയ്യുക. മികച്ച വിഷ്വൽ ഗുണനിലവാരത്തോടെ വെബ് ഉപയോഗം, ഡോക്യുമെന്റ് ഇൻസേർഷൻ, പ്രൊഫഷണൽ പ്രസന്റേഷനുകൾ എന്നിവയ്ക്ക് പെർഫെക്റ്റ്."
    from_alias: "ടേബിൾ ഡാറ്റ"
    to_alias: "PNG ഉയർന്ന ഗുണമേന്മയുള്ള ഇമേജ്"
  TOML:
    alias: "TOML കോൺഫിഗറേഷൻ"
    what: "TOML (Tom's Obvious, Minimal Language) വായിക്കാനും എഴുതാനും എളുപ്പമുള്ള ഒരു കോൺഫിഗറേഷൻ ഫയൽ ഫോർമാറ്റാണ്. അവ്യക്തതയില്ലാത്തതും ലളിതവുമായിരിക്കാൻ രൂപകൽപ്പന ചെയ്തിരിക്കുന്നു, കോൺഫിഗറേഷൻ മാനേജ്മെന്റിനായി ആധുനിക സോഫ്റ്റ്വെയർ പ്രോജക്റ്റുകളിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു. ഇതിന്റെ വ്യക്തമായ സിന്റാക്സും ശക്തമായ ടൈപ്പിംഗും ഇതിനെ ആപ്ലിക്കേഷൻ സെറ്റിംഗുകൾക്കും പ്രോജക്റ്റ് കോൺഫിഗറേഷൻ ഫയലുകൾക്കുമുള്ള മികച്ച തിരഞ്ഞെടുപ്പാക്കുന്നു."
    step1: "TOML ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ കോൺഫിഗറേഷൻ ഡാറ്റ പേസ്റ്റ് ചെയ്യുക. ടൂൾ TOML സിന്റാക്സ് പാഴ്സ് ചെയ്യുകയും ഘടനാപരമായ കോൺഫിഗറേഷൻ വിവരങ്ങൾ എക്സ്ട്രാക്റ്റ് ചെയ്യുകയും ചെയ്യുന്നു."
    step3: "നെസ്റ്റഡ് ഘടനകൾ, ഡാറ്റ തരങ്ങൾ, കമന്റുകൾ എന്നിവയ്ക്കുള്ള പിന്തുണയോടെ സ്റ്റാൻഡേർഡ് TOML ഫോർമാറ്റ് ജനറേറ്റ് ചെയ്യുക. ജനറേറ്റ് ചെയ്ത TOML ഫയലുകൾ ആപ്ലിക്കേഷൻ കോൺഫിഗറേഷൻ, ബിൽഡ് ടൂളുകൾ, പ്രോജക്റ്റ് സെറ്റിംഗുകൾ എന്നിവയ്ക്ക് പെർഫെക്റ്റാണ്."
    from_alias: "TOML കോൺഫിഗറേഷൻ"
    to_alias: "TOML ഫോർമാറ്റ്"
  INI:
    alias: "INI കോൺഫിഗറേഷൻ"
    what: "INI ഫയലുകൾ അനേകം ആപ്ലിക്കേഷനുകളും ഓപ്പറേറ്റിംഗ് സിസ്റ്റങ്ങളും ഉപയോഗിക്കുന്ന ലളിതമായ കോൺഫിഗറേഷൻ ഫയലുകളാണ്. അവയുടെ നേരായ കീ-വാല്യൂ പെയർ ഘടന അവയെ മാനുവലായി വായിക്കാനും എഡിറ്റ് ചെയ്യാനും എളുപ്പമാക്കുന്നു. മനുഷ്യ വായനാക്ഷമത പ്രധാനമായ Windows ആപ്ലിക്കേഷനുകൾ, ലെഗസി സിസ്റ്റങ്ങൾ, ലളിതമായ കോൺഫിഗറേഷൻ സാഹചര്യങ്ങൾ എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു."
    step1: "INI ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ കോൺഫിഗറേഷൻ ഡാറ്റ പേസ്റ്റ് ചെയ്യുക. ടൂൾ INI സിന്റാക്സ് പാഴ്സ് ചെയ്യുകയും സെക്ഷൻ-അധിഷ്ഠിത കോൺഫിഗറേഷൻ വിവരങ്ങൾ എക്സ്ട്രാക്റ്റ് ചെയ്യുകയും ചെയ്യുന്നു."
    step3: "സെക്ഷനുകൾ, കമന്റുകൾ, വിവിധ ഡാറ്റ തരങ്ങൾ എന്നിവയ്ക്കുള്ള പിന്തുണയോടെ സ്റ്റാൻഡേർഡ് INI ഫോർമാറ്റ് ജനറേറ്റ് ചെയ്യുക. ജനറേറ്റ് ചെയ്ത INI ഫയലുകൾ മിക്ക ആപ്ലിക്കേഷനുകളുമായും കോൺഫിഗറേഷൻ സിസ്റ്റങ്ങളുമായും പൊരുത്തപ്പെടുന്നു."
    from_alias: "INI കോൺഫിഗറേഷൻ"
    to_alias: "INI ഫോർമാറ്റ്"
  Avro:
    alias: "Avro സ്കീമ"
    what: "Apache Avro സമ്പന്നമായ ഡാറ്റ ഘടനകൾ, കോംപാക്റ്റ് ബൈനറി ഫോർമാറ്റ്, സ്കീമ എവല്യൂഷൻ കഴിവുകൾ എന്നിവ നൽകുന്ന ഒരു ഡാറ്റ സീരിയലൈസേഷൻ സിസ്റ്റമാണ്. ബിഗ് ഡാറ്റ പ്രോസസ്സിംഗ്, മെസേജ് ക്യൂകൾ, ഡിസ്ട്രിബ്യൂട്ടഡ് സിസ്റ്റങ്ങൾ എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു. ഇതിന്റെ സ്കീമ ഡെഫിനിഷൻ സങ്കീർണ്ണമായ ഡാറ്റ തരങ്ങളെയും വേർഷൻ പൊരുത്തപ്പെടുത്തലിനെയും പിന്തുണയ്ക്കുന്നു, ഇത് ഡാറ്റ എഞ്ചിനീയർമാർക്കും സിസ്റ്റം ആർക്കിടെക്റ്റുമാർക്കും പ്രധാന ഉപകരണമാക്കുന്നു."
    step1: "Avro സ്കീമ ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ ഡാറ്റ പേസ്റ്റ് ചെയ്യുക. ടൂൾ Avro സ്കീമ ഡെഫിനിഷനുകൾ പാഴ്സ് ചെയ്യുകയും ടേബിൾ ഘടന വിവരങ്ങൾ എക്സ്ട്രാക്റ്റ് ചെയ്യുകയും ചെയ്യുന്നു."
    step3: "ഡാറ്റ ടൈപ്പ് മാപ്പിംഗ്, ഫീൽഡ് നിയന്ത്രണങ്ങൾ, സ്കീമ വാലിഡേഷൻ എന്നിവയ്ക്കുള്ള പിന്തുണയോടെ സ്റ്റാൻഡേർഡ് Avro സ്കീമ ഡെഫിനിഷനുകൾ ജനറേറ്റ് ചെയ്യുക. ജനറേറ്റ് ചെയ്ത സ്കീമകൾ Hadoop ഇക്കോസിസ്റ്റങ്ങൾ, Kafka മെസേജ് സിസ്റ്റങ്ങൾ, മറ്റ് ബിഗ് ഡാറ്റ പ്ലാറ്റ്ഫോമുകൾ എന്നിവയിൽ നേരിട്ട് ഉപയോഗിക്കാം."
    from_alias: "Avro സ്കീമ"
    to_alias: "Avro ഡാറ്റ ഫോർമാറ്റ്"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) ഘടനാപരമായ ഡാറ്റ സീരിയലൈസ് ചെയ്യുന്നതിനുള്ള Google-ന്റെ ഭാഷാ-നിഷ്പക്ഷ, പ്ലാറ്റ്ഫോം-നിഷ്പക്ഷ, വിപുലീകരിക്കാവുന്ന മെക്കാനിസമാണ്. മൈക്രോസർവിസുകൾ, API ഡെവലപ്മെന്റ്, ഡാറ്റ സ്റ്റോറേജ് എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു. ഇതിന്റെ കാര്യക്ഷമമായ ബൈനറി ഫോർമാറ്റും ശക്തമായ ടൈപ്പിംഗും ഇതിനെ ഉയർന്ന പ്രകടനമുള്ള ആപ്ലിക്കേഷനുകൾക്കും ക്രോസ്-ലാംഗ്വേജ് കമ്മ്യൂണിക്കേഷനുമായി അനുയോജ്യമാക്കുന്നു."
    step1: ".proto ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ Protocol Buffer ഡെഫിനിഷനുകൾ പേസ്റ്റ് ചെയ്യുക. ടൂൾ protobuf സിന്റാക്സ് പാഴ്സ് ചെയ്യുകയും മെസേജ് ഘടന വിവരങ്ങൾ എക്സ്ട്രാക്റ്റ് ചെയ്യുകയും ചെയ്യുന്നു."
    step3: "മെസേജ് തരങ്ങൾ, ഫീൽഡ് ഓപ്ഷനുകൾ, സേവന ഡെഫിനിഷനുകൾ എന്നിവയ്ക്കുള്ള പിന്തുണയോടെ സ്റ്റാൻഡേർഡ് Protocol Buffer ഡെഫിനിഷനുകൾ ജനറേറ്റ് ചെയ്യുക. ജനറേറ്റ് ചെയ്ത .proto ഫയലുകൾ ഒന്നിലധികം പ്രോഗ്രാമിംഗ് ഭാഷകൾക്കായി കംപൈൽ ചെയ്യാം."
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf സ്കീമ"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas Python-ലെ ഏറ്റവും ജനപ്രിയമായ ഡാറ്റ അനാലിസിസ് ലൈബ്രറിയാണ്, DataFrame അതിന്റെ കോർ ഡാറ്റ ഘടനയാണ്. ഇത് ശക്തമായ ഡാറ്റ മാനിപ്യുലേഷൻ, ക്ലീനിംഗ്, അനാലിസിസ് കഴിവുകൾ നൽകുന്നു, ഡാറ്റ സയൻസ്, മെഷീൻ ലേണിംഗ്, ബിസിനസ് ഇന്റലിജൻസ് എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു. Python ഡെവലപ്പർമാർക്കും ഡാറ്റ അനലിസ്റ്റുകൾക്കും അത്യന്താപേക്ഷിതമായ ഉപകരണം."
    step1: "DataFrame കോഡ് അടങ്ങിയ Python ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ ഡാറ്റ പേസ്റ്റ് ചെയ്യുക. ടൂൾ Pandas സിന്റാക്സ് പാഴ്സ് ചെയ്യുകയും DataFrame ഘടന വിവരങ്ങൾ എക്സ്ട്രാക്റ്റ് ചെയ്യുകയും ചെയ്യുന്നു."
    step3: "ഡാറ്റ ടൈപ്പ് സ്പെസിഫിക്കേഷനുകൾ, ഇൻഡക്സ് സെറ്റിംഗുകൾ, ഡാറ്റ ഓപ്പറേഷനുകൾ എന്നിവയ്ക്കുള്ള പിന്തുണയോടെ സ്റ്റാൻഡേർഡ് Pandas DataFrame കോഡ് ജനറേറ്റ് ചെയ്യുക. ജനറേറ്റ് ചെയ്ത കോഡ് ഡാറ്റ അനാലിസിസിനും പ്രോസസ്സിംഗിനുമായി Python എൻവയോൺമെന്റിൽ നേരിട്ട് എക്സിക്യൂട്ട് ചെയ്യാം."
    from_alias: "Pandas DataFrame"
    to_alias: "Python ഡാറ്റ ഘടന"
  RDF:
    alias: "RDF ട്രിപ്പിൾ"
    what: "RDF (Resource Description Framework) വെബിലെ ഡാറ്റ ഇന്റർചേഞ്ചിനുള്ള ഒരു സ്റ്റാൻഡേർഡ് മോഡലാണ്, റിസോഴ്സുകളെക്കുറിച്ചുള്ള വിവരങ്ങൾ ഗ്രാഫ് രൂപത്തിൽ പ്രതിനിധീകരിക്കാൻ രൂപകൽപ്പന ചെയ്തിരിക്കുന്നു. സെമാന്റിക് വെബ്, നോളജ് ഗ്രാഫുകൾ, ലിങ്ക്ഡ് ഡാറ്റ ആപ്ലിക്കേഷനുകൾ എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു. ഇതിന്റെ ട്രിപ്പിൾ ഘടന സമ്പന്നമായ മെറ്റാഡാറ്റ പ്രാതിനിധ്യവും സെമാന്റിക് ബന്ധങ്ങളും സാധ്യമാക്കുന്നു."
    step1: "RDF ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ ട്രിപ്പിൾ ഡാറ്റ പേസ്റ്റ് ചെയ്യുക. ടൂൾ RDF സിന്റാക്സ് പാഴ്സ് ചെയ്യുകയും സെമാന്റിക് ബന്ധങ്ങളും റിസോഴ്സ് വിവരങ്ങളും എക്സ്ട്രാക്റ്റ് ചെയ്യുകയും ചെയ്യുന്നു."
    step3: "വിവിധ സീരിയലൈസേഷനുകൾക്കുള്ള (RDF/XML, Turtle, N-Triples) പിന്തുണയോടെ സ്റ്റാൻഡേർഡ് RDF ഫോർമാറ്റ് ജനറേറ്റ് ചെയ്യുക. ജനറേറ്റ് ചെയ്ത RDF സെമാന്റിക് വെബ് ആപ്ലിക്കേഷനുകൾ, നോളജ് ബേസുകൾ, ലിങ്ക്ഡ് ഡാറ്റ സിസ്റ്റങ്ങൾ എന്നിവയിൽ ഉപയോഗിക്കാം."
    from_alias: "RDF ഡാറ്റ"
    to_alias: "RDF സെമാന്റിക് ഫോർമാറ്റ്"
  MATLAB:
    alias: "MATLAB അറേ"
    what: "MATLAB എഞ്ചിനീയറിംഗ് കമ്പ്യൂട്ടിംഗ്, ഡാറ്റ അനാലിസിസ്, അൽഗോരിതം ഡെവലപ്മെന്റ് എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്ന ഉയർന്ന പ്രകടനമുള്ള ന്യൂമെറിക്കൽ കമ്പ്യൂട്ടിംഗും വിഷ്വലൈസേഷൻ സോഫ്റ്റ്വെയറുമാണ്. ഇതിന്റെ അറേയും മാട്രിക്സ് ഓപ്പറേഷനുകളും ശക്തമാണ്, സങ്കീർണ്ണമായ ഗണിത കണക്കുകൂട്ടലുകളെയും ഡാറ്റ പ്രോസസ്സിംഗിനെയും പിന്തുണയ്ക്കുന്നു. എഞ്ചിനീയർമാർ, ഗവേഷകർ, ഡാറ്റ സയന്റിസ്റ്റുകൾ എന്നിവർക്ക് അത്യന്താപേക്ഷിതമായ ഉപകരണം."
    step1: "MATLAB .m ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ അറേ ഡാറ്റ പേസ്റ്റ് ചെയ്യുക. ടൂൾ MATLAB സിന്റാക്സ് പാഴ്സ് ചെയ്യുകയും അറേ ഘടന വിവരങ്ങൾ എക്സ്ട്രാക്റ്റ് ചെയ്യുകയും ചെയ്യുന്നു."
    step3: "മൾട്ടി-ഡൈമൻഷണൽ അറേകൾ, ഡാറ്റ ടൈപ്പ് സ്പെസിഫിക്കേഷനുകൾ, വേരിയബിൾ നാമകരണം എന്നിവയ്ക്കുള്ള പിന്തുണയോടെ സ്റ്റാൻഡേർഡ് MATLAB അറേ കോഡ് ജനറേറ്റ് ചെയ്യുക. ജനറേറ്റ് ചെയ്ത കോഡ് ഡാറ്റ അനാലിസിസിനും സയന്റിഫിക് കമ്പ്യൂട്ടിംഗിനുമായി MATLAB എൻവയോൺമെന്റിൽ നേരിട്ട് എക്സിക്യൂട്ട് ചെയ്യാം."
    from_alias: "MATLAB അറേ"
    to_alias: "MATLAB കോഡ് ഫോർമാറ്റ്"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame എന്നത് R പ്രോഗ്രാമിംഗ് ഭാഷയിലെ പ്രധാന ഡാറ്റാ ഘടനയാണ്, സ്ഥിതിവിവരക്കണക്കുകൾ വിശകലനം, ഡാറ്റാ മൈനിംഗ്, മെഷീൻ ലേണിംഗ് എന്നിവയിൽ വ്യാപകമായി ഉപയോഗിക്കുന്നു. R എന്നത് സ്ഥിതിവിവര കമ്പ്യൂട്ടിംഗിനും ഗ്രാഫിക്സിനുമുള്ള പ്രമുഖ ഉപകരണമാണ്, DataFrame ശക്തമായ ഡാറ്റാ കൈകാര്യം, സ്ഥിതിവിവര വിശകലനം, വിഷ്വലൈസേഷൻ കഴിവുകൾ നൽകുന്നു. ഘടനാപരമായ ഡാറ്റാ വിശകലനത്തിൽ പ്രവർത്തിക്കുന്ന ഡാറ്റാ സൈന്റിസ്റ്റുകൾക്കും സ്ഥിതിവിവരശാസ്ത്രജ്ഞർക്കും ഗവേഷകർക്കും അത്യാവശ്യമാണ്."
    step1: "R ഡാറ്റാ ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുക അല്ലെങ്കിൽ DataFrame കോഡ് പേസ്റ്റ് ചെയ്യുക. ടൂൾ R സിന്റാക്സ് പാഴ്സ് ചെയ്ത് കോളം ടൈപ്പുകൾ, റോ നാമങ്ങൾ, ഡാറ്റാ ഉള്ളടക്കം എന്നിവ ഉൾപ്പെടെ DataFrame ഘടന വിവരങ്ങൾ എക്സ്ട്രാക്റ്റ് ചെയ്യുന്നു."
    step3: "ഡാറ്റാ ടൈപ്പ് സ്പെസിഫിക്കേഷനുകൾ, ഫാക്ടർ ലെവലുകൾ, റോ/കോളം നാമങ്ങൾ, R-നിർദ്ദിഷ്ട ഡാറ്റാ ഘടനകൾ എന്നിവയ്ക്കുള്ള പിന്തുണയോടെ സ്റ്റാൻഡേർഡ് R DataFrame കോഡ് ജനറേറ്റ് ചെയ്യുക. ജനറേറ്റ് ചെയ്ത കോഡ് സ്ഥിതിവിവര വിശകലനത്തിനും ഡാറ്റാ പ്രോസസ്സിംഗിനുമായി R പരിതസ്ഥിതിയിൽ നേരിട്ട് എക്സിക്യൂട്ട് ചെയ്യാൻ കഴിയും."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "പരിവർത്തനം ആരംഭിക്കുക"
  start_generating: "ജനറേറ്റ് ചെയ്യാൻ ആരംഭിക്കുക"
  api_docs: "API ഡോക്സ്"
related:
  section_title: 'കൂടുതൽ {{ if and .from (ne .from "generator") }}{{ .from }} ഉം {{ end }}{{ .to }} കൺവേർട്ടറുകൾ'
  section_description: '{{ if and .from (ne .from "generator") }}{{ .from }} ഉം {{ end }}{{ .to }} ഫോർമാറ്റുകൾക്കായി കൂടുതൽ കൺവേർട്ടറുകൾ പര്യവേക്ഷണം ചെയ്യുക. ഞങ്ങളുടെ പ്രൊഫഷണൽ ഓൺലൈൻ പരിവർത്തന ടൂളുകൾ ഉപയോഗിച്ച് നിങ്ങളുടെ ഡാറ്റ ഒന്നിലധികം ഫോർമാറ്റുകൾക്കിടയിൽ രൂപാന്തരപ്പെടുത്തുക.'
  title: "{{ .from }} മുതൽ {{ .to }} വരെ"
howto:
  step2: "പ്രൊഫഷണൽ ഫീച്ചറുകളോടുകൂടിയ ഞങ്ങളുടെ അഡ്വാൻസ്ഡ് ഓൺലൈൻ ടേബിൾ എഡിറ്റർ ഉപയോഗിച്ച് ഡാറ്റ എഡിറ്റ് ചെയ്യുക. ശൂന്യമായ വരികൾ ഇല്ലാതാക്കൽ, ഡ്യൂപ്ലിക്കേറ്റുകൾ നീക്കം ചെയ്യൽ, ഡാറ്റ ട്രാൻസ്പോസിഷൻ, സോർട്ടിംഗ്, റെജെക്സ് കണ്ടെത്തൽ & മാറ്റിസ്ഥാപിക്കൽ, തത്സമയ പ്രിവ്യൂ എന്നിവയെ പിന്തുണയ്ക്കുന്നു. എല്ലാ മാറ്റങ്ങളും കൃത്യവും വിശ്വസനീയവുമായ ഫലങ്ങളോടെ %s ഫോർമാറ്റിലേക്ക് സ്വയമേവ പരിവർത്തനം ചെയ്യുന്നു."
  section_title: "{{ . }} എങ്ങനെ ഉപയോഗിക്കാം"
  converter_description: "ഞങ്ങളുടെ ഘട്ടം-ഘട്ടമായുള്ള ഗൈഡ് ഉപയോഗിച്ച് {{ .from }} നെ {{ .to }} ആയി പരിവർത്തനം ചെയ്യാൻ പഠിക്കുക. അഡ്വാൻസ്ഡ് ഫീച്ചറുകളും തത്സമയ പ്രിവ്യൂവുമുള്ള പ്രൊഫഷണൽ ഓൺലൈൻ കൺവേർട്ടർ."
  generator_description: "ഞങ്ങളുടെ ഓൺലൈൻ ജനറേറ്റർ ഉപയോഗിച്ച് പ്രൊഫഷണൽ {{ .to }} ടേബിളുകൾ സൃഷ്ടിക്കാൻ പഠിക്കുക. Excel പോലുള്ള എഡിറ്റിംഗ്, തത്സമയ പ്രിവ്യൂ, തൽക്ഷണ എക്സ്പോർട്ട് കഴിവുകൾ."
extension:
  section_title: "ടേബിൾ ഡിറ്റക്ഷൻ & എക്സ്ട്രാക്ഷൻ എക്സ്റ്റൻഷൻ"
  section_description: "ഒരു ക്ലിക്കിൽ ഏത് വെബ്സൈറ്റിൽ നിന്നും ടേബിളുകൾ എക്സ്ട്രാക്റ്റ് ചെയ്യുക. Excel, CSV, JSON ഉൾപ്പെടെ 30+ ഫോർമാറ്റുകളിലേക്ക് തൽക്ഷണം പരിവർത്തനം ചെയ്യുക - കോപ്പി-പേസ്റ്റിംഗ് ആവശ്യമില്ല."
  features:
    extraction_title: "വൺ-ക്ലിക്ക് ടേബിൾ എക്സ്ട്രാക്ഷൻ"
    extraction_description: "കോപ്പി-പേസ്റ്റിംഗ് ഇല്ലാതെ ഏത് വെബ്പേജിൽ നിന്നും തൽക്ഷണം ടേബിളുകൾ എക്സ്ട്രാക്റ്റ് ചെയ്യുക - പ്രൊഫഷണൽ ഡാറ്റ എക്സ്ട്രാക്ഷൻ ലളിതമാക്കി"
    formats_title: "30+ ഫോർമാറ്റ് കൺവേർട്ടർ പിന്തുണ"
    formats_description: "ഞങ്ങളുടെ അഡ്വാൻസ്ഡ് ടേബിൾ കൺവേർട്ടർ ഉപയോഗിച്ച് എക്സ്ട്രാക്റ്റ് ചെയ്ത ടേബിളുകൾ Excel, CSV, JSON, Markdown, SQL, കൂടാതെ മറ്റുള്ളവയിലേക്ക് പരിവർത്തനം ചെയ്യുക"
    detection_title: "സ്മാർട്ട് ടേബിൾ ഡിറ്റക്ഷൻ"
    detection_description: "വേഗത്തിലുള്ള ഡാറ്റ എക്സ്ട്രാക്ഷനും പരിവർത്തനത്തിനുമായി ഏത് വെബ്പേജിലെയും ടേബിളുകൾ സ്വയമേവ കണ്ടെത്തുകയും ഹൈലൈറ്റ് ചെയ്യുകയും ചെയ്യുന്നു"
  hover_tip: "✨ എക്സ്ട്രാക്ഷൻ ഐക്കൺ കാണാൻ ഏത് ടേബിളിലും ഹോവർ ചെയ്യുക"
recommendations:
  section_title: "സർവകലാശാലകളും പ്രൊഫഷണലുകളും ശുപാർശ ചെയ്യുന്നു"
  section_description: "വിശ്വസനീയമായ ടേബിൾ പരിവർത്തനത്തിനും ഡാറ്റ പ്രോസസ്സിംഗിനുമായി സർവകലാശാലകൾ, ഗവേഷണ സ്ഥാപനങ്ങൾ, വികസന ടീമുകൾ എന്നിവയിലെ പ്രൊഫഷണലുകൾ TableConvert-നെ വിശ്വസിക്കുന്നു."
  cards:
    university_title: "വിസ്കോൺസിൻ-മാഡിസൺ സർവകലാശാല"
    university_description: "TableConvert.com - പ്രൊഫഷണൽ സൗജന്യ ഓൺലൈൻ ടേബിൾ കൺവേർട്ടറും ഡാറ്റ ഫോർമാറ്റ് ടൂളും"
    university_link: "ലേഖനം വായിക്കുക"
    facebook_title: "ഡാറ്റ പ്രൊഫഷണൽ കമ്മ്യൂണിറ്റി"
    facebook_description: "Facebook ഡെവലപ്പർ ഗ്രൂപ്പുകളിൽ ഡാറ്റ അനലിസ്റ്റുകളും പ്രൊഫഷണലുകളും പങ്കിടുകയും ശുപാർശ ചെയ്യുകയും ചെയ്തു"
    facebook_link: "പോസ്റ്റ് കാണുക"
    twitter_title: "ഡെവലപ്പർ കമ്മ്യൂണിറ്റി"
    twitter_description: "ടേബിൾ പരിവർത്തനത്തിനായി X (Twitter) ൽ @xiaoying_eth ഉം മറ്റ് ഡെവലപ്പർമാരും ശുപാർശ ചെയ്തു"
    twitter_link: "ട്വീറ്റ് കാണുക"
faq:
  section_title: "പതിവായി ചോദിക്കുന്ന ചോദ്യങ്ങൾ"
  section_description: "ഞങ്ങളുടെ സൗജന്യ ഓൺലൈൻ ടേബിൾ കൺവേർട്ടർ, ഡാറ്റ ഫോർമാറ്റുകൾ, പരിവർത്തന പ്രക്രിയ എന്നിവയെക്കുറിച്ചുള്ള സാധാരണ ചോദ്യങ്ങൾ."
  what: "%s ഫോർമാറ്റ് എന്താണ്?"
  howto_convert:
    question: "{{ . }} സൗജന്യമായി എങ്ങനെ ഉപയോഗിക്കാം?"
    answer: "ഞങ്ങളുടെ സൗജന്യ ഓൺലൈൻ ടേബിൾ കൺവേർട്ടർ ഉപയോഗിച്ച് നിങ്ങളുടെ {{ .from }} ഫയൽ അപ്‌ലോഡ് ചെയ്യുക, ഡാറ്റ പേസ്റ്റ് ചെയ്യുക, അല്ലെങ്കിൽ വെബ് പേജുകളിൽ നിന്ന് എക്സ്ട്രാക്റ്റ് ചെയ്യുക. ഞങ്ങളുടെ പ്രൊഫഷണൽ കൺവേർട്ടർ ടൂൾ തത്സമയ പ്രിവ്യൂവും അഡ്വാൻസ്ഡ് എഡിറ്റിംഗ് ഫീച്ചറുകളും ഉപയോഗിച്ച് നിങ്ങളുടെ ഡാറ്റയെ തൽക്ഷണം {{ .to }} ഫോർമാറ്റിലേക്ക് രൂപാന്തരപ്പെടുത്തുന്നു. പരിവർത്തനം ചെയ്ത ഫലം ഉടനെ ഡൗൺലോഡ് ചെയ്യുക അല്ലെങ്കിൽ കോപ്പി ചെയ്യുക."
  security:
    question: "ഈ ഓൺലൈൻ കൺവേർട്ടർ ഉപയോഗിക്കുമ്പോൾ എന്റെ ഡാറ്റ സുരക്ഷിതമാണോ?"
    answer: "തീർച്ചയായും! എല്ലാ ടേബിൾ പരിവർത്തനങ്ങളും നിങ്ങളുടെ ബ്രൗസറിൽ പ്രാദേശികമായി സംഭവിക്കുന്നു - നിങ്ങളുടെ ഡാറ്റ ഒരിക്കലും നിങ്ങളുടെ ഉപകരണം വിടുന്നില്ല. ഞങ്ങളുടെ ഓൺലൈൻ കൺവേർട്ടർ എല്ലാം ക്ലയന്റ്-സൈഡിൽ പ്രോസസ്സ് ചെയ്യുന്നു, പൂർണ്ണ സ്വകാര്യതയും ഡാറ്റ സുരക്ഷയും ഉറപ്പാക്കുന്നു. ഞങ്ങളുടെ സെർവറുകളിൽ ഫയലുകളൊന്നും സംഭരിച്ചിട്ടില്ല."
  free:
    question: "TableConvert ശരിക്കും ഉപയോഗിക്കാൻ സൗജന്യമാണോ?"
    answer: "അതെ, TableConvert പൂർണ്ണമായും സൗജന്യമാണ്! എല്ലാ കൺവേർട്ടർ ഫീച്ചറുകളും, ടേബിൾ എഡിറ്റർ, ഡാറ്റ ജനറേറ്റർ ടൂളുകൾ, എക്സ്പോർട്ട് ഓപ്ഷനുകൾ എന്നിവ ചെലവ്, രജിസ്ട്രേഷൻ, അല്ലെങ്കിൽ മറഞ്ഞിരിക്കുന്ന ഫീസ് എന്നിവയില്ലാതെ ലഭ്യമാണ്. സൗജന്യമായി ഓൺലൈനിൽ അനിയന്ത്രിതമായ ഫയലുകൾ പരിവർത്തനം ചെയ്യുക."
  filesize:
    question: "ഓൺലൈൻ കൺവേർട്ടറിന് എന്ത് ഫയൽ സൈസ് പരിധികളാണുള്ളത്?"
    answer: "ഞങ്ങളുടെ സൗജന്യ ഓൺലൈൻ ടേബിൾ കൺവേർട്ടർ 10MB വരെയുള്ള ഫയലുകളെ പിന്തുണയ്ക്കുന്നു. വലിയ ഫയലുകൾ, ബാച്ച് പ്രോസസ്സിംഗ്, അല്ലെങ്കിൽ എന്റർപ്രൈസ് ആവശ്യങ്ങൾക്കായി, ഉയർന്ന പരിധികളുള്ള ഞങ്ങളുടെ ബ്രൗസർ എക്സ്റ്റൻഷൻ അല്ലെങ്കിൽ പ്രൊഫഷണൽ API സേവനം ഉപയോഗിക്കുക."
stats:
  conversions: "പരിവർത്തനം ചെയ്ത ടേബിളുകൾ"
  tables: "ജനറേറ്റ് ചെയ്ത ടേബിളുകൾ"
  formats: "ഡാറ്റ ഫയൽ ഫോർമാറ്റുകൾ"
  rating: "ഉപയോക്തൃ റേറ്റിംഗ്"
