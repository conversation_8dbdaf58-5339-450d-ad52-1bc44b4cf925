site:
  fullname: "محول الجداول عبر الإنترنت"
  name: "TableConvert"
  subtitle: "محول ومولد الجداول المجاني عبر الإنترنت"
  intro: "TableConvert هو أداة مجانية لتحويل الجداول وتوليد البيانات عبر الإنترنت تدعم التحويل بين أكثر من 30 تنسيقاً بما في ذلك Excel وCSV وJSON وMarkdown وLaTeX وSQL والمزيد."
  followTwitter: "تابعنا على X"
title:
  converter: "%s إلى %s"
  generator: "مولد %s"
post:
  tags:
    converter: "محول"
    editor: "محرر"
    generator: "مولد"
    maker: "منشئ"
  converter:
    title: "تحويل %s إلى %s عبر الإنترنت"
    short: "أداة مجانية وقوية لتحويل %s إلى %s عبر الإنترنت"
    intro: "محول %s إلى %s سهل الاستخدام عبر الإنترنت. حوّل بيانات الجداول بسهولة باستخدام أداة التحويل البديهية. سريع وموثوق وسهل الاستخدام."
  generator:
    title: "محرر ومولد %s عبر الإنترنت"
    short: "أداة توليد %s احترافية عبر الإنترنت بميزات شاملة"
    intro: "مولد ومحرر %s سهل الاستخدام عبر الإنترنت. أنشئ جداول بيانات احترافية بسهولة باستخدام أداتنا البديهية والمعاينة الفورية."
navbar:
  search:
    placeholder: "البحث عن محول..."
  sponsor: "ادعمنا بقهوة"
  extension: "الإضافة"
  api: "واجهة برمجة التطبيقات"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "مصدر البيانات"
    placeholder: "الصق بيانات %s أو اسحب ملفات %s هنا"
    example: "مثال"
    upload: "رفع ملف"
    extract:
      enter: "استخراج من صفحة ويب"
      intro: "أدخل رابط صفحة ويب تحتوي على بيانات جدول لاستخراج البيانات المنظمة تلقائياً"
      btn: "استخراج %s"
    excel:
      sheet: "ورقة العمل"
      none: "لا شيء"
  tableEditor:
    title: "محرر الجداول عبر الإنترنت"
    undo: "تراجع"
    redo: "إعادة"
    transpose: "تبديل الصفوف والأعمدة"
    clear: "مسح"
    deleteBlank: "حذف الفارغة"
    deleteDuplicate: "إزالة المكررات"
    uppercase: "أحرف كبيرة"
    lowercase: "أحرف صغيرة"
    capitalize: "أحرف استهلالية"
    replace:
      replace: "بحث واستبدال (يدعم Regex)"
      subst: "استبدال بـ..."
      btn: "استبدال الكل"
  tableGenerator:
    title: "مولد الجداول"
    sponsor: "ادعمنا بقهوة"
    copy: "نسخ إلى الحافظة"
    download: "تنزيل الملف"
    tooltip:
      html:
        escape: "تجنب أحرف HTML الخاصة (&, <, >, \", ') لمنع أخطاء العرض"
        div: "استخدام تخطيط DIV+CSS بدلاً من علامات TABLE التقليدية، أنسب للتصميم المتجاوب"
        minify: "إزالة المسافات وفواصل الأسطر لتوليد كود HTML مضغوط"
        thead: "توليد هيكل رأس الجدول (&lt;thead&gt;) والجسم (&lt;tbody&gt;) القياسي"
        tableCaption: "إضافة عنوان وصفي فوق الجدول (عنصر &lt;caption&gt;)"
        tableClass: "إضافة اسم فئة CSS للجدول لسهولة تخصيص النمط"
        tableId: "تعيين معرف فريد للجدول للتلاعب بـ JavaScript"
      jira:
        escape: "تجنب أحرف الأنبوب (|) لتجنب التعارض مع صيغة جدول Jira"
      json:
        parsingJSON: "تحليل ذكي لسلاسل JSON في الخلايا إلى كائنات"
        minify: "توليد تنسيق JSON مضغوط أحادي السطر لتقليل حجم الملف"
        format: "اختيار هيكل بيانات JSON الناتج: مصفوفة كائنات، مصفوفة ثنائية الأبعاد، إلخ."
      latex:
        escape: "تجنب أحرف LaTeX الخاصة (%, &, _, #, $, إلخ.) لضمان التجميع الصحيح"
        ht: "إضافة معامل الموضع العائم [!ht] للتحكم في موضع الجدول على الصفحة"
        mwe: "توليد وثيقة LaTeX كاملة"
        tableAlign: "تعيين المحاذاة الأفقية للجدول على الصفحة"
        tableBorder: "تكوين نمط حدود الجدول: بلا حدود، حدود جزئية، حدود كاملة"
        label: "تعيين تسمية الجدول لأمر \\ref{} للمرجعية المتقاطعة"
        caption: "تعيين تسمية الجدول للعرض أعلى أو أسفل الجدول"
        location: "اختيار موضع عرض تسمية الجدول: أعلى أو أسفل"
        tableType: "اختيار نوع بيئة الجدول: tabular، longtable، array، إلخ."
      markdown:
        escape: "تجنب أحرف Markdown الخاصة (*, _, |, \\, إلخ.) لتجنب تعارض التنسيق"
        pretty: "محاذاة تلقائية لعرض الأعمدة لتوليد تنسيق جدول أجمل"
        simple: "استخدام صيغة مبسطة، حذف خطوط الحدود العمودية الخارجية"
        boldFirstRow: "جعل نص الصف الأول عريضاً"
        boldFirstColumn: "جعل نص العمود الأول عريضاً"
        firstHeader: "معاملة الصف الأول كرأس وإضافة خط فاصل"
        textAlign: "تعيين محاذاة نص العمود: يسار، وسط، يمين"
        multilineHandling: "معالجة النص متعدد الأسطر: الحفاظ على فواصل الأسطر، تجنب إلى \\n، استخدام علامات &lt;br&gt;"

        includeLineNumbers: "إضافة عمود أرقام الأسطر على الجانب الأيسر من الجدول"
      magic:
        builtin: "اختيار تنسيقات القوالب الشائعة المحددة مسبقاً"
        rowsTpl: "<table> <tr> <th>صيغة السحر</th> <th>الوصف</th> <th>دعم طرق JS</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>الحقل الأول، الثاني ... من <b>الرأس</b>، أي {hA} {hB} ...</td> <td>طرق السلاسل النصية</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>الحقل الأول، الثاني ... من الصف الحالي، أي {$A} {$B} ...</td> <td>طرق السلاسل النصية</td> </tr> <tr> <td>{F,} {F;}</td> <td>تقسيم الصف الحالي بالسلسلة النصية بعد <b>F</b></td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td><b>رقم</b> السطر للصف الحالي من 1 أو 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> رقم السطر <b>الأخير</b> للصفوف </td> <td></td> </tr> <tr> <td>{x ...}</td> <td><b>تنفيذ</b> كود JavaScript، مثال: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> استخدام الشرطة المائلة العكسية <b>\\</b> لإخراج الأقواس المعقوفة {...} </td> <td></td> </tr></table>"
        headerTpl: "قالب إخراج مخصص لقسم الرأس"
        footerTpl: "قالب إخراج مخصص لقسم التذييل"
      textile:
        escape: "تجنب أحرف صيغة Textile (|, ., -, ^) لتجنب تعارض التنسيق"
        rowHeader: "تعيين الصف الأول كصف رأس"
        thead: "إضافة علامات صيغة Textile لرأس وجسم الجدول"
      xml:
        escape: "تجنب أحرف XML الخاصة (&lt;, &gt;, &amp;, \", ') لضمان XML صالح"
        minify: "توليد إخراج XML مضغوط، إزالة المسافات الإضافية"
        rootElement: "تعيين اسم علامة العنصر الجذر لـ XML"
        rowElement: "تعيين اسم علامة عنصر XML لكل صف من البيانات"
        declaration: "إضافة رأس إعلان XML (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "إخراج البيانات كخصائص XML بدلاً من العناصر الفرعية"
        cdata: "تغليف محتوى النص بـ CDATA لحماية الأحرف الخاصة"
        encoding: "تعيين تنسيق ترميز الأحرف لوثيقة XML"
        indentation: "اختيار حرف المسافة البادئة لـ XML: مسافات أو علامات تبويب"
      yaml:
        indentSize: "تعيين عدد المسافات للمسافة البادئة الهرمية لـ YAML (عادة 2 أو 4)"
        arrayStyle: "تنسيق المصفوفة: كتلة (عنصر واحد لكل سطر) أو تدفق (تنسيق مضمن)"
        quotationStyle: "نمط اقتباس السلسلة النصية: بلا اقتباس، اقتباس مفرد، اقتباس مزدوج"
      pdf:
        theme: "اختيار النمط البصري لجدول PDF للوثائق المهنية"
        headerColor: "اختيار لون خلفية رأس جدول PDF"
        showHead: "التحكم في عرض الرأس على صفحات PDF"
        docTitle: "عنوان اختياري لوثيقة PDF"
        docDescription: "نص وصف اختياري لوثيقة PDF"
      csv:
        bom: "إضافة علامة ترتيب البايت UTF-8 لمساعدة Excel والبرامج الأخرى على التعرف على الترميز"
      excel:
        autoWidth: "ضبط عرض العمود تلقائياً بناءً على المحتوى"
        protectSheet: "تمكين حماية ورقة العمل بكلمة مرور: tableconvert.com"
      sql:
        primaryKey: "تحديد اسم حقل المفتاح الأساسي لبيان CREATE TABLE"
        dialect: "اختيار نوع قاعدة البيانات، مما يؤثر على صيغة الاقتباس ونوع البيانات"
      ascii:
        forceSep: "فرض خطوط فاصلة بين كل صف من البيانات"
        style: "اختيار نمط رسم حدود جدول ASCII"
        comment: "إضافة علامات تعليق لتغليف الجدول بأكمله"
      mediawiki:
        minify: "ضغط كود الإخراج، إزالة المسافات الإضافية"
        header: "وضع علامة على الصف الأول كنمط رأس"
        sort: "تمكين وظيفة ترتيب الجدول بالنقر"
      asciidoc:
        minify: "ضغط إخراج تنسيق AsciiDoc"
        firstHeader: "تعيين الصف الأول كصف رأس"
        lastFooter: "تعيين الصف الأخير كصف تذييل"
        title: "إضافة نص عنوان للجدول"
      tracwiki:
        rowHeader: "تعيين الصف الأول كرأس"
        colHeader: "تعيين العمود الأول كرأس"
      bbcode:
        minify: "ضغط تنسيق إخراج BBCode"
      restructuredtext:
        style: "اختيار نمط حدود جدول reStructuredText"
        forceSep: "فرض خطوط فاصلة"
    label:
      ascii:
        forceSep: "فواصل الصفوف"
        style: "نمط الحدود"
        comment: "مغلف التعليق"
      restructuredtext:
        style: "نمط الحدود"
        forceSep: "فرض الفواصل"
      bbcode:
        minify: "تصغير الإخراج"
      csv:
        doubleQuote: "تغليف الاقتباس المزدوج"
        delimiter: "فاصل الحقل"
        bom: "UTF-8 BOM"
        valueDelimiter: "فاصل القيمة"
        rowDelimiter: "فاصل الصف"
        prefix: "بادئة الصف"
        suffix: "لاحقة الصف"
      excel:
        autoWidth: "العرض التلقائي"
        textFormat: "تنسيق النص"
        protectSheet: "حماية الورقة"
        boldFirstRow: "الصف الأول عريض"
        boldFirstColumn: "العمود الأول عريض"
        sheetName: "اسم الورقة"
      html:
        escape: "تجنب أحرف HTML"
        div: "جدول DIV"
        minify: "تصغير الكود"
        thead: "هيكل رأس الجدول"
        tableCaption: "تسمية الجدول"
        tableClass: "فئة الجدول"
        tableId: "معرف الجدول"
        rowHeader: "رأس الصف"
        colHeader: "رأس العمود"
      jira:
        escape: "تجنب الأحرف"
        rowHeader: "رأس الصف"
        colHeader: "رأس العمود"
      json:
        parsingJSON: "تحليل JSON"
        minify: "تصغير الإخراج"
        format: "تنسيق البيانات"
        rootName: "اسم الكائن الجذر"
        indentSize: "حجم المسافة البادئة"
      jsonlines:
        parsingJSON: "تحليل JSON"
        format: "تنسيق البيانات"
      latex:
        escape: "تجنب أحرف جدول LaTeX"
        ht: "موضع العائم"
        mwe: "وثيقة كاملة"
        tableAlign: "محاذاة الجدول"
        tableBorder: "نمط الحدود"
        label: "تسمية المرجع"
        caption: "تسمية الجدول"
        location: "موضع التسمية"
        tableType: "نوع الجدول"
        boldFirstRow: "الصف الأول عريض"
        boldFirstColumn: "العمود الأول عريض"
        textAlign: "محاذاة النص"
        borders: "إعدادات الحدود"
      markdown:
        escape: "تجنب الأحرف"
        pretty: "جدول Markdown جميل"
        simple: "تنسيق Markdown بسيط"
        boldFirstRow: "الصف الأول عريض"
        boldFirstColumn: "العمود الأول عريض"
        firstHeader: "الرأس الأول"
        textAlign: "محاذاة النص"
        multilineHandling: "معالجة متعددة الأسطر"

        includeLineNumbers: "إضافة أرقام الأسطر"
        align: "المحاذاة"
      mediawiki:
        minify: "تصغير الكود"
        header: "ترميز الرأس"
        sort: "قابل للترتيب"
      asciidoc:
        minify: "تصغير التنسيق"
        firstHeader: "الرأس الأول"
        lastFooter: "التذييل الأخير"
        title: "عنوان الجدول"
      tracwiki:
        rowHeader: "رأس الصف"
        colHeader: "رأس العمود"
      sql:
        drop: "حذف الجدول (إذا كان موجوداً)"
        create: "إنشاء جدول"
        oneInsert: "إدراج مجمع"
        table: "اسم الجدول"
        dialect: "نوع قاعدة البيانات"
        primaryKey: "المفتاح الأساسي"
      magic:
        builtin: "قالب مدمج"
        rowsTpl: "قالب الصف، الصيغة ->"
        headerTpl: "قالب الرأس"
        footerTpl: "قالب التذييل"
      textile:
        escape: "تجنب الأحرف"
        rowHeader: "رأس الصف"
        thead: "صيغة رأس الجدول"
      xml:
        escape: "تجنب أحرف XML"
        minify: "تصغير الإخراج"
        rootElement: "العنصر الجذر"
        rowElement: "عنصر الصف"
        declaration: "إعلان XML"
        attributes: "وضع الخاصية"
        cdata: "مغلف CDATA"
        encoding: "الترميز"
        indentSize: "حجم المسافة البادئة"
      yaml:
        indentSize: "حجم المسافة البادئة"
        arrayStyle: "نمط المصفوفة"
        quotationStyle: "نمط الاقتباس"
      pdf:
        theme: "موضوع جدول PDF"
        headerColor: "لون رأس PDF"
        showHead: "عرض رأس PDF"
        docTitle: "عنوان وثيقة PDF"
        docDescription: "وصف وثيقة PDF"
sidebar:
  all: "جميع أدوات التحويل"
  dataSource:
    title: "مصدر البيانات"
    description:
      converter: "استيراد %s للتحويل إلى %s. يدعم رفع الملفات والتحرير عبر الإنترنت واستخراج بيانات الويب."
      generator: "إنشاء بيانات الجدول مع دعم طرق إدخال متعددة بما في ذلك الإدخال اليدوي واستيراد الملفات وتوليد القوالب."
  tableEditor:
    title: "محرر الجداول عبر الإنترنت"
    description:
      converter: "معالجة %s عبر الإنترنت باستخدام محرر الجداول الخاص بنا. تجربة تشغيل شبيهة بـ Excel مع دعم حذف الصفوف الفارغة وإزالة التكرار والترتيب والبحث والاستبدال."
      generator: "محرر جداول قوي عبر الإنترنت يوفر تجربة تشغيل شبيهة بـ Excel. يدعم حذف الصفوف الفارغة وإزالة التكرار والترتيب والبحث والاستبدال."
  tableGenerator:
    title: "مولد الجداول"
    description:
      converter: "توليد %s بسرعة مع معاينة في الوقت الفعلي لمولد الجداول. خيارات تصدير غنية، نسخ وتنزيل بنقرة واحدة."
      generator: "تصدير بيانات %s بتنسيقات متعددة لتلبية سيناريوهات الاستخدام المختلفة. يدعم الخيارات المخصصة والمعاينة في الوقت الفعلي."
footer:
  changelog: "سجل التغييرات"
  sponsor: "الرعاة"
  contact: "اتصل بنا"
  privacyPolicy: "سياسة الخصوصية"
  about: "حول"
  resources: "الموارد"
  popularConverters: "المحولات الشائعة"
  popularGenerators: "المولدات الشائعة"
  dataSecurity: "بياناتك آمنة - جميع التحويلات تتم في متصفحك."
converters:
  Markdown:
    alias: "جدول Markdown"
    what: "Markdown هي لغة ترميز خفيفة الوزن تُستخدم على نطاق واسع للوثائق التقنية وإنشاء محتوى المدونات وتطوير الويب. صيغة الجدول الخاصة بها موجزة وبديهية، تدعم محاذاة النص وتضمين الروابط والتنسيق. إنها الأداة المفضلة للمبرمجين والكتاب التقنيين، متوافقة تماماً مع GitHub و GitLab ومنصات استضافة الكود الأخرى."
    step1: "الصق بيانات جدول Markdown في منطقة مصدر البيانات، أو اسحب وأفلت ملفات .md مباشرة للرفع. تحلل الأداة تلقائياً هيكل الجدول والتنسيق، تدعم المحتوى المتداخل المعقد ومعالجة الأحرف الخاصة."
    step3: "توليد كود جدول Markdown قياسي في الوقت الفعلي، يدعم طرق محاذاة متعددة وتعريض النص وإضافة أرقام الأسطر وإعدادات تنسيق متقدمة أخرى. الكود المولد متوافق تماماً مع GitHub ومحررات Markdown الرئيسية، جاهز للاستخدام بنسخة واحدة."
    from_alias: "ملف جدول Markdown"
    to_alias: "تنسيق جدول Markdown"
  Magic:
    alias: "قالب مخصص"
    what: "قالب Magic هو مولد بيانات متقدم فريد لهذه الأداة، يسمح للمستخدمين بإنشاء إخراج بيانات بتنسيق تعسفي من خلال صيغة قالب مخصصة. يدعم استبدال المتغيرات والحكم الشرطي ومعالجة الحلقات. إنه الحل النهائي للتعامل مع احتياجات تحويل البيانات المعقدة وتنسيقات الإخراج الشخصية، مناسب بشكل خاص للمطورين ومهندسي البيانات."
    step1: "اختيار قوالب شائعة مدمجة أو إنشاء صيغة قالب مخصصة. يدعم متغيرات ووظائف غنية يمكنها التعامل مع هياكل البيانات المعقدة ومنطق الأعمال."
    step3: "توليد إخراج بيانات يلبي تماماً متطلبات التنسيق المخصص. يدعم منطق تحويل البيانات المعقد والمعالجة الشرطية، مما يحسن بشكل كبير كفاءة معالجة البيانات وجودة الإخراج. أداة قوية لمعالجة البيانات المجمعة."
    from_alias: "بيانات الجدول"
    to_alias: "إخراج تنسيق مخصص"
  CSV:
    alias: "CSV"
    what: "CSV (القيم المفصولة بفواصل) هو تنسيق تبادل البيانات الأكثر استخداماً على نطاق واسع، مدعوم تماماً من Excel و Google Sheets وأنظمة قواعد البيانات وأدوات تحليل البيانات المختلفة. هيكله البسيط والتوافق القوي يجعله التنسيق القياسي لترحيل البيانات والاستيراد/التصدير المجمع وتبادل البيانات عبر المنصات، يُستخدم على نطاق واسع في تحليل الأعمال وعلوم البيانات وتكامل الأنظمة."
    step1: "رفع ملفات CSV أو لصق بيانات CSV مباشرة. تتعرف الأداة بذكاء على فواصل مختلفة (فاصلة، تبويب، فاصلة منقوطة، أنبوب، إلخ.)، تكتشف تلقائياً أنواع البيانات وتنسيقات الترميز، تدعم التحليل السريع للملفات الكبيرة وهياكل البيانات المعقدة."
    step3: "توليد ملفات تنسيق CSV قياسية مع دعم فواصل مخصصة وأنماط اقتباس وتنسيقات ترميز وإعدادات علامة BOM. يضمن التوافق المثالي مع الأنظمة المستهدفة، يوفر خيارات التنزيل والضغط لتلبية احتياجات معالجة البيانات على مستوى المؤسسة."
    from_alias: "ملف بيانات CSV"
    to_alias: "تنسيق CSV قياسي"
  JSON:
    alias: "مصفوفة JSON"
    what: "JSON (JavaScript Object Notation) هو تنسيق بيانات الجدول القياسي لتطبيقات الويب الحديثة و REST APIs وبنيات الخدمات المصغرة. هيكله الواضح والتحليل الفعال يجعله مستخدماً على نطاق واسع في تفاعل البيانات الأمامية والخلفية وتخزين ملفات التكوين وقواعد البيانات NoSQL. يدعم الكائنات المتداخلة وهياكل المصفوفات وأنواع البيانات المتعددة، مما يجعله بيانات جدول لا غنى عنها لتطوير البرمجيات الحديثة."
    step1: "رفع ملفات JSON أو لصق مصفوفات JSON. يدعم التعرف التلقائي وتحليل مصفوفات الكائنات والهياكل المتداخلة وأنواع البيانات المعقدة. تتحقق الأداة بذكاء من صيغة JSON وتوفر تنبيهات الأخطاء."
    step3: "توليد إخراجات تنسيق JSON متعددة: مصفوفات كائنات قياسية، مصفوفات ثنائية الأبعاد، مصفوفات أعمدة، وتنسيقات أزواج مفتاح-قيمة. يدعم الإخراج المجمل ووضع الضغط وأسماء الكائنات الجذر المخصصة وإعدادات المسافة البادئة، يتكيف تماماً مع واجهات API المختلفة واحتياجات تخزين البيانات."
    from_alias: "ملف مصفوفة JSON"
    to_alias: "تنسيق JSON قياسي"
  JSONLines:
    alias: "تنسيق JSONLines"
    what: "JSON Lines (المعروف أيضاً باسم NDJSON) هو تنسيق مهم لمعالجة البيانات الضخمة ونقل البيانات المتدفقة، مع كل سطر يحتوي على كائن JSON مستقل. يُستخدم على نطاق واسع في تحليل السجلات ومعالجة تدفق البيانات والتعلم الآلي والأنظمة الموزعة. يدعم المعالجة التدريجية والحوسبة المتوازية، مما يجعله الخيار الأمثل للتعامل مع البيانات المنظمة واسعة النطاق."
    step1: "رفع ملفات JSONLines أو لصق البيانات. تحلل الأداة كائنات JSON سطراً بسطر، تدعم معالجة تدفق الملفات الكبيرة ووظيفة تخطي الأسطر الخاطئة."
    step3: "توليد تنسيق JSONLines قياسي مع كل سطر يخرج كائن JSON كامل. مناسب لمعالجة التدفق والاستيراد المجمع وسيناريوهات تحليل البيانات الضخمة، يدعم التحقق من صحة البيانات وتحسين التنسيق."
    from_alias: "بيانات JSONLines"
    to_alias: "تنسيق تدفق JSONLines"
  XML:
    alias: "XML"
    what: "XML (لغة الترميز القابلة للتوسيع) هو التنسيق القياسي لتبادل البيانات على مستوى المؤسسة وإدارة التكوين، مع مواصفات صيغة صارمة وآليات تحقق قوية. يُستخدم على نطاق واسع في خدمات الويب وملفات التكوين وتخزين الوثائق وتكامل الأنظمة. يدعم مساحات الأسماء والتحقق من المخطط وتحويل XSLT، مما يجعله بيانات جدول مهمة لتطبيقات المؤسسة."
    step1: "رفع ملفات XML أو لصق بيانات XML. تحلل الأداة تلقائياً هيكل XML وتحوله إلى تنسيق جدول، تدعم مساحة الأسماء ومعالجة الخصائص والهياكل المتداخلة المعقدة."
    step3: "توليد إخراج XML يتوافق مع معايير XML. يدعم العناصر الجذر المخصصة وأسماء عناصر الصفوف وأوضاع الخصائص وتغليف CDATA وإعدادات ترميز الأحرف. يضمن سلامة البيانات والتوافق، يلبي متطلبات التطبيقات على مستوى المؤسسة."
    from_alias: "ملف بيانات XML"
    to_alias: "تنسيق XML قياسي"
  YAML:
    alias: "تكوين YAML"
    what: "YAML هو معيار تسلسل بيانات صديق للإنسان، مشهور بهيكله الهرمي الواضح وصيغته الموجزة. يُستخدم على نطاق واسع في ملفات التكوين وسلاسل أدوات DevOps و Docker Compose ونشر Kubernetes. قابليته القوية للقراءة وصيغته الموجزة تجعله تنسيق تكوين مهم لتطبيقات السحابة الأصلية الحديثة والعمليات الآلية."
    step1: "رفع ملفات YAML أو لصق بيانات YAML. تحلل الأداة بذكاء هيكل YAML وتتحقق من صحة الصيغة، تدعم تنسيقات متعددة الوثائق وأنواع البيانات المعقدة."
    step3: "توليد إخراج تنسيق YAML قياسي مع دعم أنماط مصفوفة الكتلة والتدفق وإعدادات اقتباس متعددة ومسافة بادئة مخصصة والحفاظ على التعليقات. يضمن أن ملفات YAML الناتجة متوافقة تماماً مع محللات وأنظمة التكوين المختلفة."
    from_alias: "ملف تكوين YAML"
    to_alias: "تنسيق YAML قياسي"
  MySQL:
      alias: "نتائج استعلام MySQL"
      what: "MySQL هو نظام إدارة قواعد البيانات العلائقية مفتوح المصدر الأكثر شعبية في العالم، مشهور بأدائه العالي وموثوقيته وسهولة استخدامه. يُستخدم على نطاق واسع في تطبيقات الويب وأنظمة المؤسسات ومنصات تحليل البيانات. تحتوي نتائج استعلام MySQL عادة على بيانات جدول منظمة، تخدم كمصدر بيانات مهم في إدارة قواعد البيانات وأعمال تحليل البيانات."
      step1: "الصق نتائج إخراج استعلام MySQL في منطقة مصدر البيانات. تتعرف الأداة تلقائياً وتحلل تنسيق إخراج سطر أوامر MySQL، تدعم أنماط نتائج استعلام مختلفة وترميزات أحرف، تتعامل بذكاء مع الرؤوس وصفوف البيانات."
      step3: "تحويل سريع لنتائج استعلام MySQL إلى تنسيقات بيانات جدول متعددة، تسهل تحليل البيانات وتوليد التقارير وترحيل البيانات عبر الأنظمة والتحقق من صحة البيانات. أداة عملية لمديري قواعد البيانات ومحللي البيانات."
      from_alias: "إخراج استعلام MySQL"
      to_alias: "بيانات جدول MySQL"
  SQL:
    alias: "إدراج SQL"
    what: "SQL (لغة الاستعلام المنظمة) هي لغة التشغيل القياسية لقواعد البيانات العلائقية، تُستخدم لعمليات استعلام البيانات والإدراج والتحديث والحذف. كتقنية أساسية لإدارة قواعد البيانات، يُستخدم SQL على نطاق واسع في تحليل البيانات وذكاء الأعمال ومعالجة ETL وبناء مستودع البيانات. إنها أداة مهارة أساسية لمحترفي البيانات."
    step1: "الصق بيانات INSERT SQL أو رفع ملفات .sql. تحلل الأداة بذكاء صيغة SQL وتستخرج بيانات الجدول، تدعم لهجات SQL متعددة ومعالجة بيانات استعلام معقدة."
    step3: "توليد بيانات INSERT SQL قياسية وبيانات إنشاء الجداول. يدعم لهجات قواعد بيانات متعددة (MySQL، PostgreSQL، SQLite، SQL Server، Oracle)، يتعامل تلقائياً مع تعيين أنواع البيانات وتجنب الأحرف وقيود المفتاح الأساسي. يضمن أن كود SQL المولد يمكن تنفيذه مباشرة."
    from_alias: "ملف بيانات SQL"
    to_alias: "بيان SQL قياسي"
  Qlik:
      alias: "جدول Qlik"
      what: "Qlik هو بائع برمجيات متخصص في تصور البيانات ولوحات المعلومات التنفيذية ومنتجات ذكاء الأعمال الخدمة الذاتية، إلى جانب Tableau و Microsoft."
      step1: ""
      step3: "أخيراً، يعرض [مولد الجداول](#TableGenerator) نتائج التحويل. استخدم في Qlik Sense أو Qlik AutoML أو QlikView أو برامج Qlik الأخرى المدعومة."
      from_alias: "جدول Qlik"
      to_alias: "جدول Qlik"
  DAX:
      alias: "جدول DAX"
      what: "DAX (تعبيرات تحليل البيانات) هي لغة برمجة تُستخدم في جميع أنحاء Microsoft Power BI لإنشاء أعمدة محسوبة ومقاييس وجداول مخصصة."
      step1: ""
      step3: "أخيراً، يعرض [مولد الجداول](#TableGenerator) نتائج التحويل. كما هو متوقع، يُستخدم في عدة منتجات Microsoft بما في ذلك Microsoft Power BI و Microsoft Analysis Services و Microsoft Power Pivot for Excel."
      from_alias: "جدول DAX"
      to_alias: "جدول DAX"
  Firebase:
    alias: "قائمة Firebase"
    what: "Firebase هو منصة تطوير تطبيقات BaaS توفر خدمات خلفية مستضافة مثل قاعدة البيانات في الوقت الفعلي والتخزين السحابي والمصادقة وتقارير الأعطال وغيرها."
    step1: ""
    step3: "أخيراً، يعرض [مولد الجداول](#TableGenerator) نتائج التحويل. يمكنك بعد ذلك استخدام طريقة push في Firebase API لإضافة قائمة من البيانات في قاعدة بيانات Firebase."
    from_alias: "قائمة Firebase"
    to_alias: "قائمة Firebase"
  HTML:
    alias: "جدول HTML"
    what: "جداول HTML هي الطريقة القياسية لعرض البيانات المنظمة في صفحات الويب، مبنية بعلامات table و tr و td وعلامات أخرى. تدعم تخصيص نمط غني وتخطيط متجاوب ووظائف تفاعلية. تُستخدم على نطاق واسع في تطوير المواقع وعرض البيانات وتوليد التقارير، تخدم كمكون مهم لتطوير الواجهة الأمامية وتصميم الويب."
    step1: "الصق كود HTML يحتوي على جداول أو رفع ملفات HTML. تتعرف الأداة تلقائياً وتستخرج بيانات الجدول من الصفحات، تدعم هياكل HTML معقدة وأنماط CSS ومعالجة الجداول المتداخلة."
    step3: "توليد كود جدول HTML دلالي مع دعم هيكل thead/tbody وإعدادات فئة CSS وتسميات الجداول ورؤوس الصفوف/الأعمدة وتكوين خصائص متجاوبة. يضمن أن كود الجدول المولد يلبي معايير الويب مع إمكانية وصول جيدة وصداقة SEO."
    from_alias: "جدول ويب HTML"
    to_alias: "جدول HTML قياسي"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel هو برنامج جداول البيانات الأكثر شعبية في العالم، يُستخدم على نطاق واسع في تحليل الأعمال والإدارة المالية ومعالجة البيانات وإنشاء التقارير. قدراته القوية في معالجة البيانات ومكتبة الوظائف الغنية وميزات التصور المرنة تجعله الأداة القياسية لأتمتة المكاتب وتحليل البيانات، مع تطبيقات واسعة عبر جميع الصناعات والمجالات تقريباً."
    step1: "رفع ملفات Excel (يدعم تنسيقات .xlsx، .xls) أو نسخ بيانات الجدول مباشرة من Excel والصق. تدعم الأداة معالجة أوراق عمل متعددة والتعرف على التنسيق المعقد والتحليل السريع للملفات الكبيرة، تتعامل تلقائياً مع الخلايا المدمجة وأنواع البيانات."
    step3: "توليد بيانات جدول متوافقة مع Excel يمكن لصقها مباشرة في Excel أو تنزيلها كملفات .xlsx قياسية. يدعم تسمية أوراق العمل وتنسيق الخلايا والعرض التلقائي للأعمدة وتنسيق الرؤوس وإعدادات التحقق من صحة البيانات. يضمن أن ملفات Excel الناتجة لها مظهر احترافي ووظائف كاملة."
    from_alias: "جدول بيانات Excel"
    to_alias: "تنسيق Excel قياسي"
  LaTeX:
    alias: "جدول LaTeX"
    what: "LaTeX هو نظام تنضيد وثائق احترافي، مناسب بشكل خاص لإنشاء الأوراق الأكاديمية والوثائق التقنية والمنشورات العلمية. وظيفة الجدول الخاصة به قوية، تدعم الصيغ الرياضية المعقدة والتحكم الدقيق في التخطيط وإخراج PDF عالي الجودة. إنها الأداة القياسية في الأوساط الأكاديمية والنشر العلمي، تُستخدم على نطاق واسع في أوراق المجلات والأطروحات وتنضيد الأدلة التقنية."
    step1: "الصق كود جدول LaTeX أو رفع ملفات .tex. تحلل الأداة صيغة جدول LaTeX وتستخرج محتوى البيانات، تدعم بيئات جدول متعددة (tabular، longtable، array، إلخ.) وأوامر تنسيق معقدة."
    step3: "توليد كود جدول LaTeX احترافي مع دعم اختيار بيئة جدول متعددة وتكوين نمط الحدود وإعدادات موضع التسمية وتحديد فئة الوثيقة وإدارة الحزم. يمكن توليد وثائق LaTeX قابلة للتجميع كاملة، يضمن أن الجداول الناتجة تلبي معايير النشر الأكاديمي."
    from_alias: "جدول وثيقة LaTeX"
    to_alias: "تنسيق LaTeX احترافي"
  ASCII:
    alias: "جدول ASCII"
    what: "جداول ASCII تستخدم أحرف نص عادي لرسم حدود وهياكل الجداول، توفر أفضل توافق وقابلية نقل. متوافقة مع جميع محررات النصوص وبيئات الطرفية وأنظمة التشغيل. تُستخدم على نطاق واسع في وثائق الكود والأدلة التقنية وملفات README وإخراج أدوات سطر الأوامر. تنسيق عرض البيانات المفضل للمبرمجين ومديري الأنظمة."
    step1: "رفع ملفات نصية تحتوي على جداول ASCII أو لصق بيانات الجدول مباشرة. تتعرف الأداة بذكاء وتحلل هياكل جدول ASCII، تدعم أنماط حدود متعددة وتنسيقات محاذاة."
    step3: "توليد جداول ASCII نص عادي جميلة مع دعم أنماط حدود متعددة (خط مفرد، خط مزدوج، زوايا مدورة، إلخ.) وطرق محاذاة النص والعرض التلقائي للأعمدة. الجداول المولدة تعرض بشكل مثالي في محررات الكود والوثائق وأسطر الأوامر."
    from_alias: "جدول نص ASCII"
    to_alias: "تنسيق ASCII قياسي"
  MediaWiki:
    alias: "جدول MediaWiki"
    what: "MediaWiki هو منصة البرمجيات مفتوحة المصدر المستخدمة من قبل مواقع ويكي مشهورة مثل ويكيبيديا. صيغة الجدول الخاصة بها موجزة لكن قوية، تدعم تخصيص نمط الجدول ووظيفة الترتيب وتضمين الروابط. تُستخدم على نطاق واسع في إدارة المعرفة والتحرير التعاوني وأنظمة إدارة المحتوى، تخدم كتقنية أساسية لبناء موسوعات ويكي وقواعد المعرفة."
    step1: "الصق كود جدول MediaWiki أو رفع ملفات مصدر ويكي. تحلل الأداة صيغة ترميز ويكي وتستخرج بيانات الجدول، تدعم صيغة ويكي معقدة ومعالجة القوالب."
    step3: "توليد كود جدول MediaWiki قياسي مع دعم إعدادات نمط الرأس ومحاذاة الخلايا وتمكين وظيفة الترتيب وخيارات ضغط الكود. الكود المولد يمكن استخدامه مباشرة لتحرير صفحات ويكي، يضمن العرض المثالي على منصات MediaWiki."
    from_alias: "كود مصدر MediaWiki"
    to_alias: "صيغة جدول MediaWiki"
  TracWiki:
    alias: "جدول TracWiki"
    what: "Trac هو نظام إدارة مشاريع وتتبع أخطاء قائم على الويب يستخدم صيغة ويكي مبسطة لإنشاء محتوى الجدول."
    step1: "رفع ملفات TracWiki أو لصق بيانات الجدول."
    step3: "توليد كود جدول متوافق مع TracWiki مع دعم إعدادات رأس الصف/العمود، يسهل إدارة وثائق المشروع."
    from_alias: "جدول TracWiki"
    to_alias: "تنسيق TracWiki"
  AsciiDoc:
    alias: "جدول AsciiDoc"
    what: "AsciiDoc هي لغة ترميز خفيفة الوزن يمكن تحويلها إلى HTML و PDF وصفحات دليل وتنسيقات أخرى، تُستخدم على نطاق واسع لكتابة الوثائق التقنية."
    step1: "رفع ملفات AsciiDoc أو لصق البيانات."
    step3: "توليد صيغة جدول AsciiDoc مع دعم إعدادات الرأس والتذييل والعنوان، قابلة للاستخدام مباشرة في محررات AsciiDoc."
    from_alias: "جدول AsciiDoc"
    to_alias: "تنسيق AsciiDoc"
  reStructuredText:
    alias: "جدول reStructuredText"
    what: "reStructuredText هو تنسيق الوثائق القياسي لمجتمع Python، يدعم صيغة جدول غنية، يُستخدم عادة لتوليد وثائق Sphinx."
    step1: "رفع ملفات .rst أو لصق بيانات reStructuredText."
    step3: "توليد جداول reStructuredText قياسية مع دعم أنماط حدود متعددة، قابلة للاستخدام مباشرة في مشاريع وثائق Sphinx."
    from_alias: "جدول reStructuredText"
    to_alias: "تنسيق reStructuredText"
  PHP:
    alias: "مصفوفة PHP"
    what: "PHP هي لغة برمجة نصية من جانب الخادم شائعة، مع المصفوفات كونها هيكل البيانات الأساسي، تُستخدم على نطاق واسع في تطوير الويب ومعالجة البيانات."
    step1: "رفع ملفات تحتوي على مصفوفات PHP أو لصق البيانات مباشرة."
    step3: "توليد كود مصفوفة PHP قياسي يمكن استخدامه مباشرة في مشاريع PHP، يدعم تنسيقات المصفوفة الترابطية والمفهرسة."
    from_alias: "مصفوفة PHP"
    to_alias: "كود PHP"
  Ruby:
    alias: "مصفوفة Ruby"
    what: "Ruby هي لغة برمجة كائنية التوجه ديناميكية بصيغة موجزة وأنيقة، مع المصفوفات كونها هيكل بيانات مهم."
    step1: "رفع ملفات Ruby أو لصق بيانات المصفوفة."
    step3: "توليد كود مصفوفة Ruby يتوافق مع مواصفات صيغة Ruby، قابل للاستخدام مباشرة في مشاريع Ruby."
    from_alias: "مصفوفة Ruby"
    to_alias: "كود Ruby"
  ASP:
    alias: "مصفوفة ASP"
    what: "ASP (صفحات الخادم النشطة) هي بيئة برمجة نصية من جانب الخادم من Microsoft، تدعم لغات برمجة متعددة لتطوير صفحات ويب ديناميكية."
    step1: "رفع ملفات ASP أو لصق بيانات المصفوفة."
    step3: "توليد كود مصفوفة متوافق مع ASP مع دعم صيغة VBScript و JScript، قابل للاستخدام في مشاريع ASP.NET."
    from_alias: "مصفوفة ASP"
    to_alias: "كود ASP"
  ActionScript:
    alias: "مصفوفة ActionScript"
    what: "ActionScript هي لغة برمجة كائنية التوجه تُستخدم بشكل أساسي لتطوير تطبيقات Adobe Flash و AIR."
    step1: "رفع ملفات .as أو لصق بيانات ActionScript."
    step3: "توليد كود مصفوفة ActionScript يتوافق مع معايير صيغة AS3، قابل للاستخدام لتطوير مشاريع Flash و Flex."
    from_alias: "مصفوفة ActionScript"
    to_alias: "كود ActionScript"
  BBCode:
    alias: "جدول BBCode"
    what: "BBCode هي لغة ترميز خفيفة الوزن تُستخدم عادة في المنتديات والمجتمعات عبر الإنترنت، توفر وظائف تنسيق بسيطة بما في ذلك دعم الجداول."
    step1: "رفع ملفات تحتوي على BBCode أو لصق البيانات."
    step3: "توليد كود جدول BBCode مناسب لنشر المنتديات وإنشاء محتوى المجتمع، مع دعم تنسيق إخراج مضغوط."
    from_alias: "جدول BBCode"
    to_alias: "تنسيق BBCode"
  PDF:
    alias: "جدول PDF"
    what: "PDF (تنسيق الوثيقة المحمولة) هو معيار وثيقة عبر المنصات مع تخطيط ثابت وعرض متسق وخصائص طباعة عالية الجودة. يُستخدم على نطاق واسع في الوثائق الرسمية والتقارير والفواتير والعقود والأوراق الأكاديمية. التنسيق المفضل للتواصل التجاري وأرشفة الوثائق، يضمن تأثيرات بصرية متسقة تماماً عبر أجهزة وأنظمة تشغيل مختلفة."
    step1: "استيراد بيانات الجدول بأي تنسيق. تحلل الأداة تلقائياً هيكل البيانات وتؤدي تصميم تخطيط ذكي، تدعم ترقيم الصفحات التلقائي للجداول الكبيرة ومعالجة أنواع البيانات المعقدة."
    step3: "توليد ملفات جدول PDF عالية الجودة مع دعم أنماط موضوع احترافية متعددة (أعمال، أكاديمي، بسيط، إلخ.) وخطوط متعددة اللغات وترقيم صفحات تلقائي وإضافة علامة مائية وتحسين الطباعة. يضمن أن وثائق PDF الناتجة لها مظهر احترافي، قابلة للاستخدام مباشرة للعروض التجارية والنشر الرسمي."
    from_alias: "بيانات الجدول"
    to_alias: "وثيقة PDF احترافية"
  JPEG:
    alias: "صورة JPEG"
    what: "JPEG هو تنسيق الصورة الرقمية الأكثر استخداماً على نطاق واسع مع تأثيرات ضغط ممتازة وتوافق واسع. حجم ملفه الصغير وسرعة التحميل السريعة تجعله مناسباً لعرض الويب ومشاركة وسائل التواصل الاجتماعي ورسوم الوثائق والعروض التقديمية عبر الإنترنت. تنسيق الصورة القياسي للوسائط الرقمية والتواصل الشبكي، مدعوم تماماً من جميع الأجهزة والبرامج تقريباً."
    step1: "استيراد بيانات الجدول بأي تنسيق. تؤدي الأداة تصميم تخطيط ذكي وتحسين بصري، تحسب تلقائياً الحجم والدقة الأمثل."
    step3: "توليد صور جدول JPEG عالية الدقة مع دعم مخططات ألوان موضوع متعددة (فاتح، داكن، صديق للعين، إلخ.) وتخطيط تكيفي وتحسين وضوح النص وتخصيص الحجم. مناسب للمشاركة عبر الإنترنت وإدراج الوثائق واستخدام العروض التقديمية، يضمن تأثيرات بصرية ممتازة على أجهزة عرض مختلفة."
    from_alias: "بيانات الجدول"
    to_alias: "صورة JPEG عالية الدقة"
  Jira:
    alias: "جدول Jira"
    what: "JIRA هو برنامج إدارة مشاريع وتتبع أخطاء احترافي طورته Atlassian، يُستخدم على نطاق واسع في التطوير الرشيق واختبار البرمجيات والتعاون في المشاريع. وظيفة الجدول الخاصة به تدعم خيارات تنسيق غنية وعرض البيانات، تخدم كأداة مهمة لفرق تطوير البرمجيات ومديري المشاريع وموظفي ضمان الجودة في إدارة المتطلبات وتتبع الأخطاء وتقارير التقدم."
    step1: "رفع ملفات تحتوي على بيانات الجدول أو لصق محتوى البيانات مباشرة. تعالج الأداة تلقائياً بيانات الجدول وتجنب الأحرف الخاصة."
    step3: "توليد كود جدول متوافق مع منصة JIRA مع دعم إعدادات نمط الرأس ومحاذاة الخلايا ومعالجة تجنب الأحرف وتحسين التنسيق. الكود المولد يمكن لصقه مباشرة في أوصاف مشاكل JIRA أو التعليقات أو صفحات ويكي، يضمن العرض والتقديم الصحيح في أنظمة JIRA."
    from_alias: "بيانات المشروع"
    to_alias: "صيغة جدول Jira"
  Textile:
    alias: "جدول Textile"
    what: "Textile هي لغة ترميز خفيفة الوزن موجزة بصيغة بسيطة وسهلة التعلم، تُستخدم على نطاق واسع في أنظمة إدارة المحتوى ومنصات المدونات وأنظمة المنتديات. صيغة الجدول الخاصة بها واضحة وبديهية، تدعم التنسيق السريع وإعدادات النمط. أداة مثالية لمنشئي المحتوى ومديري المواقع للكتابة السريعة للوثائق ونشر المحتوى."
    step1: "رفع ملفات تنسيق Textile أو لصق بيانات الجدول. تحلل الأداة صيغة ترميز Textile وتستخرج محتوى الجدول."
    step3: "توليد صيغة جدول Textile قياسية مع دعم ترميز الرأس ومحاذاة الخلايا وتجنب الأحرف الخاصة وتحسين التنسيق. الكود المولد يمكن استخدامه مباشرة في أنظمة CMS ومنصات المدونات وأنظمة الوثائق التي تدعم Textile، يضمن التقديم والعرض الصحيح للمحتوى."
    from_alias: "وثيقة Textile"
    to_alias: "صيغة جدول Textile"
  PNG:
    alias: "صورة PNG"
    what: "PNG (رسوميات الشبكة المحمولة) هو تنسيق صورة بدون فقدان مع ضغط ممتاز ودعم الشفافية. يُستخدم على نطاق واسع في تصميم الويب والرسوميات الرقمية والتصوير الاحترافي. جودته العالية والتوافق الواسع يجعله مثالياً للقطات الشاشة والشعارات والمخططات وأي صور تتطلب تفاصيل واضحة وخلفيات شفافة."
    step1: "استيراد بيانات الجدول بأي تنسيق. تؤدي الأداة تصميم تخطيط ذكي وتحسين بصري، تحسب تلقائياً الحجم والدقة الأمثل لإخراج PNG."
    step3: "توليد صور جدول PNG عالية الجودة مع دعم مخططات ألوان موضوع متعددة وخلفيات شفافة وتخطيط تكيفي وتحسين وضوح النص. مثالي لاستخدام الويب وإدراج الوثائق والعروض التقديمية الاحترافية مع جودة بصرية ممتازة."
    from_alias: "بيانات الجدول"
    to_alias: "صورة PNG عالية الجودة"
  TOML:
    alias: "تكوين TOML"
    what: "TOML (لغة Tom الواضحة والبسيطة) هو تنسيق ملف تكوين سهل القراءة والكتابة. مصمم ليكون واضحاً وبسيطاً، يُستخدم على نطاق واسع في مشاريع البرمجيات الحديثة لإدارة التكوين. صيغته الواضحة والكتابة القوية تجعله خياراً ممتازاً لإعدادات التطبيقات وملفات تكوين المشاريع."
    step1: "رفع ملفات TOML أو لصق بيانات التكوين. تحلل الأداة صيغة TOML وتستخرج معلومات التكوين المنظمة."
    step3: "توليد تنسيق TOML قياسي مع دعم الهياكل المتداخلة وأنواع البيانات والتعليقات. ملفات TOML المولدة مثالية لتكوين التطبيقات وأدوات البناء وإعدادات المشاريع."
    from_alias: "تكوين TOML"
    to_alias: "تنسيق TOML"
  INI:
    alias: "تكوين INI"
    what: "ملفات INI هي ملفات تكوين بسيطة تُستخدم من قبل العديد من التطبيقات وأنظمة التشغيل. هيكل أزواج مفتاح-قيمة المباشر يجعلها سهلة القراءة والتحرير يدوياً. تُستخدم على نطاق واسع في تطبيقات Windows والأنظمة القديمة وسيناريوهات التكوين البسيطة حيث قابلية القراءة البشرية مهمة."
    step1: "رفع ملفات INI أو لصق بيانات التكوين. تحلل الأداة صيغة INI وتستخرج معلومات التكوين القائمة على الأقسام."
    step3: "توليد تنسيق INI قياسي مع دعم الأقسام والتعليقات وأنواع البيانات المختلفة. ملفات INI المولدة متوافقة مع معظم التطبيقات وأنظمة التكوين."
    from_alias: "تكوين INI"
    to_alias: "تنسيق INI"
  Avro:
    alias: "مخطط Avro"
    what: "Apache Avro هو نظام تسلسل بيانات يوفر هياكل بيانات غنية وتنسيق ثنائي مضغوط وقدرات تطوير المخطط. يُستخدم على نطاق واسع في معالجة البيانات الضخمة وطوابير الرسائل والأنظمة الموزعة. تعريف المخطط الخاص به يدعم أنواع البيانات المعقدة وتوافق الإصدارات، مما يجعله أداة مهمة لمهندسي البيانات ومهندسي الأنظمة."
    step1: "رفع ملفات مخطط Avro أو لصق البيانات. تحلل الأداة تعريفات مخطط Avro وتستخرج معلومات هيكل الجدول."
    step3: "توليد تعريفات مخطط Avro قياسية مع دعم تعيين أنواع البيانات وقيود الحقول والتحقق من صحة المخطط. المخططات المولدة يمكن استخدامها مباشرة في أنظمة Hadoop البيئية وأنظمة رسائل Kafka ومنصات البيانات الضخمة الأخرى."
    from_alias: "مخطط Avro"
    to_alias: "تنسيق بيانات Avro"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) هي آلية Google المحايدة للغة والمنصة والقابلة للتوسيع لتسلسل البيانات المنظمة. تُستخدم على نطاق واسع في الخدمات المصغرة وتطوير API وتخزين البيانات. تنسيقها الثنائي الفعال والكتابة القوية تجعلها مثالية للتطبيقات عالية الأداء والتواصل عبر اللغات."
    step1: "رفع ملفات .proto أو لصق تعريفات Protocol Buffer. تحلل الأداة صيغة protobuf وتستخرج معلومات هيكل الرسالة."
    step3: "توليد تعريفات Protocol Buffer قياسية مع دعم أنواع الرسائل وخيارات الحقول وتعريفات الخدمة. ملفات .proto المولدة يمكن تجميعها لعدة لغات برمجة."
    from_alias: "Protocol Buffer"
    to_alias: "مخطط Protobuf"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas هي مكتبة تحليل البيانات الأكثر شعبية في Python، مع DataFrame كونها هيكل البيانات الأساسي. توفر قدرات قوية لمعالجة البيانات وتنظيفها وتحليلها، تُستخدم على نطاق واسع في علوم البيانات والتعلم الآلي وذكاء الأعمال. أداة لا غنى عنها لمطوري Python ومحللي البيانات."
    step1: "رفع ملفات Python تحتوي على كود DataFrame أو لصق البيانات. تحلل الأداة صيغة Pandas وتستخرج معلومات هيكل DataFrame."
    step3: "توليد كود Pandas DataFrame قياسي مع دعم مواصفات أنواع البيانات وإعدادات الفهرس وعمليات البيانات. الكود المولد يمكن تنفيذه مباشرة في بيئة Python لتحليل ومعالجة البيانات."
    from_alias: "Pandas DataFrame"
    to_alias: "هيكل بيانات Python"
  RDF:
    alias: "ثلاثية RDF"
    what: "RDF (إطار وصف الموارد) هو نموذج قياسي لتبادل البيانات على الويب، مصمم لتمثيل معلومات حول الموارد في شكل رسم بياني. يُستخدم على نطاق واسع في الويب الدلالي ورسوم المعرفة وتطبيقات البيانات المترابطة. هيكله الثلاثي يمكن تمثيل البيانات الوصفية الغنية والعلاقات الدلالية."
    step1: "رفع ملفات RDF أو لصق بيانات ثلاثية. تحلل الأداة صيغة RDF وتستخرج العلاقات الدلالية ومعلومات الموارد."
    step3: "توليد تنسيق RDF قياسي مع دعم تسلسلات مختلفة (RDF/XML، Turtle، N-Triples). RDF المولد يمكن استخدامه في تطبيقات الويب الدلالي وقواعد المعرفة وأنظمة البيانات المترابطة."
    from_alias: "بيانات RDF"
    to_alias: "تنسيق RDF دلالي"
  MATLAB:
    alias: "مصفوفة MATLAB"
    what: "MATLAB هو برنامج حوسبة رقمية وتصور عالي الأداء يُستخدم على نطاق واسع في الحوسبة الهندسية وتحليل البيانات وتطوير الخوارزميات. عمليات المصفوفة والمصفوفة الخاصة به قوية، تدعم الحسابات الرياضية المعقدة ومعالجة البيانات. أداة أساسية للمهندسين والباحثين وعلماء البيانات."
    step1: "رفع ملفات MATLAB .m أو لصق بيانات المصفوفة. تحلل الأداة صيغة MATLAB وتستخرج معلومات هيكل المصفوفة."
    step3: "توليد كود مصفوفة MATLAB قياسي مع دعم المصفوفات متعددة الأبعاد ومواصفات أنواع البيانات وتسمية المتغيرات. الكود المولد يمكن تنفيذه مباشرة في بيئة MATLAB لتحليل البيانات والحوسبة العلمية."
    from_alias: "مصفوفة MATLAB"
    to_alias: "تنسيق كود MATLAB"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame هو هيكل البيانات الأساسي في لغة البرمجة R، يُستخدم على نطاق واسع في التحليل الإحصائي واستخراج البيانات والتعلم الآلي. R هي الأداة الرائدة للحوسبة الإحصائية والرسوميات، مع DataFrame توفر قدرات قوية لمعالجة البيانات والتحليل الإحصائي والتصور. أساسية لعلماء البيانات والإحصائيين والباحثين العاملين في تحليل البيانات المنظمة."
    step1: "رفع ملفات بيانات R أو لصق كود DataFrame. تحلل الأداة صيغة R وتستخرج معلومات هيكل DataFrame بما في ذلك أنواع الأعمدة وأسماء الصفوف ومحتوى البيانات."
    step3: "توليد كود R DataFrame قياسي مع دعم مواصفات أنواع البيانات ومستويات العوامل وأسماء الصفوف/الأعمدة وهياكل البيانات الخاصة بـ R. الكود المولد يمكن تنفيذه مباشرة في بيئة R للتحليل الإحصائي ومعالجة البيانات."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "ابدأ التحويل"
  start_generating: "بدء الإنشاء"
  api_docs: "وثائق واجهة برمجة التطبيقات"
related:
  section_title: 'المزيد من محولات {{ if and .from (ne .from "generator") }}{{ .from }} و{{ end }}{{ .to }}'
  section_description: 'استكشف المزيد من المحولات لتنسيقات {{ if and .from (ne .from "generator") }}{{ .from }} و{{ end }}{{ .to }}. حوّل بياناتك بين تنسيقات متعددة باستخدام أدوات التحويل الاحترافية عبر الإنترنت.'
  title: "{{ .from }} إلى {{ .to }}"
howto:
  step2: "حرر البيانات باستخدام محرر الجداول المتقدم عبر الإنترنت مع الميزات الاحترافية. يدعم حذف الصفوف الفارغة وإزالة التكرارات وتبديل البيانات والفرز والبحث والاستبدال بالتعبيرات النمطية والمعاينة الفورية. جميع التغييرات تتحول تلقائياً إلى تنسيق %s بنتائج دقيقة وموثوقة."
  section_title: "كيفية استخدام {{ . }}"
  converter_description: "تعلم كيفية تحويل {{ .from }} إلى {{ .to }} مع دليلنا خطوة بخطوة. محول احترافي عبر الإنترنت مع ميزات متقدمة ومعاينة فورية."
  generator_description: "تعلم كيفية إنشاء جداول {{ .to }} احترافية باستخدام مولدنا عبر الإنترنت. تحرير شبيه بـ Excel ومعاينة فورية وقدرات تصدير فورية."
extension:
  section_title: "إضافة كشف واستخراج الجداول"
  section_description: "استخرج الجداول من أي موقع ويب بنقرة واحدة. حوّل إلى أكثر من 30 تنسيقاً بما في ذلك Excel وCSV وJSON فوراً - لا حاجة للنسخ واللصق."
  features:
    extraction_title: "استخراج الجداول بنقرة واحدة"
    extraction_description: "استخرج الجداول فوراً من أي صفحة ويب دون نسخ ولصق - استخراج البيانات الاحترافي أصبح بسيطاً"
    formats_title: "دعم محول أكثر من 30 تنسيقاً"
    formats_description: "حوّل الجداول المستخرجة إلى Excel وCSV وJSON وMarkdown وSQL والمزيد باستخدام محول الجداول المتقدم"
    detection_title: "كشف الجداول الذكي"
    detection_description: "يكتشف ويبرز الجداول تلقائياً في أي صفحة ويب لاستخراج وتحويل البيانات بسرعة"
  hover_tip: "✨ مرر الماوس فوق أي جدول لرؤية أيقونة الاستخراج"
recommendations:
  section_title: "موصى به من الجامعات والمحترفين"
  section_description: "TableConvert موثوق به من قبل المحترفين في الجامعات ومؤسسات البحث وفرق التطوير لتحويل الجداول الموثوق ومعالجة البيانات."
  cards:
    university_title: "جامعة ويسكونسن-ماديسون"
    university_description: "TableConvert.com - أداة محول الجداول وتنسيقات البيانات الاحترافية المجانية عبر الإنترنت"
    university_link: "اقرأ المقال"
    facebook_title: "مجتمع محترفي البيانات"
    facebook_description: "مشارك وموصى به من قبل محللي البيانات والمحترفين في مجموعات مطوري Facebook"
    facebook_link: "عرض المنشور"
    twitter_title: "مجتمع المطورين"
    twitter_description: "موصى به من قبل @xiaoying_eth ومطورين آخرين على X (Twitter) لتحويل الجداول"
    twitter_link: "عرض التغريدة"
faq:
  section_title: "الأسئلة الشائعة"
  section_description: "أسئلة شائعة حول محول الجداول المجاني عبر الإنترنت وتنسيقات البيانات وعملية التحويل."
  what: "ما هو تنسيق %s؟"
  howto_convert:
    question: "كيفية استخدام {{ . }} مجاناً؟"
    answer: "ارفع ملف {{ .from }} أو الصق البيانات أو استخرج من صفحات الويب باستخدام محول الجداول المجاني عبر الإنترنت. أداة المحول الاحترافية تحوّل بياناتك فوراً إلى تنسيق {{ .to }} مع معاينة فورية وميزات تحرير متقدمة. حمّل أو انسخ النتيجة المحولة فوراً."
  security:
    question: "هل بياناتي آمنة عند استخدام هذا المحول عبر الإنترنت؟"
    answer: "بالطبع! جميع تحويلات الجداول تحدث محلياً في متصفحك - بياناتك لا تغادر جهازك أبداً. محولنا عبر الإنترنت يعالج كل شيء من جانب العميل، مما يضمن الخصوصية الكاملة وأمان البيانات. لا يتم تخزين أي ملفات على خوادمنا."
  free:
    question: "هل TableConvert مجاني حقاً للاستخدام؟"
    answer: "نعم، TableConvert مجاني تماماً! جميع ميزات المحول ومحرر الجداول وأدوات مولد البيانات وخيارات التصدير متاحة بدون تكلفة أو تسجيل أو رسوم خفية. حوّل ملفات غير محدودة عبر الإنترنت مجاناً."
  filesize:
    question: "ما هي حدود حجم الملف للمحول عبر الإنترنت؟"
    answer: "محول الجداول المجاني عبر الإنترنت يدعم ملفات حتى 10 ميجابايت. للملفات الأكبر أو المعالجة المجمعة أو الاحتياجات المؤسسية، استخدم إضافة المتصفح أو خدمة واجهة برمجة التطبيقات الاحترافية مع حدود أعلى."
stats:
  conversions: "الجداول المحولة"
  tables: "الجداول المولدة"
  formats: "تنسيقات ملفات البيانات"
  rating: "تقييم المستخدم"
