site:
  fullname: "অনলাইন টেবিল কনভার্ট"
  name: "TableConvert"
  subtitle: "বিনামূল্যে অনলাইন টেবিল কনভার্টার এবং জেনারেটর"
  intro: "TableConvert হল একটি বিনামূল্যে অনলাইন টেবিল কনভার্টার এবং ডেটা জেনারেটর টুল যা Excel, CSV, JSON, Markdown, LaTeX, SQL এবং আরও অনেক সহ ৩০+ ফরম্যাটের মধ্যে রূপান্তর সমর্থন করে."
  followTwitter: "X-এ আমাদের অনুসরণ করুন"
title:
  converter: "%s থেকে %s"
  generator: "%s জেনারেটর"
post:
  tags:
    converter: "কনভার্টার"
    editor: "এডিটর"
    generator: "জেনারেটর"
    maker: "বিল্ডার"
  converter:
    title: "%s থেকে %s অনলাইনে রূপান্তর করুন"
    short: "একটি বিনামূল্যে এবং শক্তিশালী %s থেকে %s অনলাইন টুল"
    intro: "ব্যবহার করা সহজ অনলাইন %s থেকে %s কনভার্টার। আমাদের স্বজ্ঞাত রূপান্তর টুল দিয়ে অনায়াসে টেবিল ডেটা রূপান্তর করুন। দ্রুত, নির্ভরযোগ্য এবং ব্যবহারকারী-বান্ধব।"
  generator:
    title: "অনলাইন %s এডিটর এবং জেনারেটর"
    short: "ব্যাপক বৈশিষ্ট্য সহ পেশাদার %s অনলাইন জেনারেশন টুল"
    intro: "ব্যবহার করা সহজ অনলাইন %s জেনারেটর এবং টেবিল এডিটর। আমাদের স্বজ্ঞাত টুল এবং রিয়েল-টাইম প্রিভিউ দিয়ে অনায়াসে পেশাদার ডেটা টেবিল তৈরি করুন।"
navbar:
  search:
    placeholder: "কনভার্টার খুঁজুন..."
  sponsor: "আমাদের একটি কফি কিনুন"
  extension: "এক্সটেনশন"
  api: "এপিআই"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "ডেটা সোর্স"
    placeholder: "আপনার %s ডেটা পেস্ট করুন বা %s ফাইল এখানে টেনে আনুন"
    example: "উদাহরণ"
    upload: "ফাইল আপলোড করুন"
    extract:
      enter: "ওয়েব পেজ থেকে এক্সট্র্যাক্ট করুন"
      intro: "স্বয়ংক্রিয়ভাবে কাঠামোগত ডেটা এক্সট্র্যাক্ট করতে টেবিল ডেটা সম্বলিত একটি ওয়েব পেজ URL প্রবেশ করান"
      btn: "%s এক্সট্র্যাক্ট করুন"
    excel:
      sheet: "ওয়ার্কশিট"
      none: "কিছুই নয়"
  tableEditor:
    title: "অনলাইন টেবিল এডিটর"
    undo: "পূর্বাবস্থায় ফিরুন"
    redo: "পুনরায় করুন"
    transpose: "ট্রান্সপোজ"
    clear: "পরিষ্কার করুন"
    deleteBlank: "খালি মুছুন"
    deleteDuplicate: "ডুপ্লিকেট মুছুন"
    uppercase: "বড় হাতের অক্ষর"
    lowercase: "ছোট হাতের অক্ষর"
    capitalize: "প্রথম অক্ষর বড়"
    replace:
      replace: "খুঁজুন এবং প্রতিস্থাপন করুন (Regex সমর্থিত)"
      subst: "এর সাথে প্রতিস্থাপন করুন..."
      btn: "সব প্রতিস্থাপন করুন"
  tableGenerator:
    title: "টেবিল জেনারেটর"
    sponsor: "আমাদের একটি কফি কিনুন"
    copy: "ক্লিপবোর্ডে কপি করুন"
    download: "ফাইল ডাউনলোড করুন"
    tooltip:
      html:
        escape: "প্রদর্শন ত্রুটি প্রতিরোধ করতে HTML বিশেষ অক্ষর (&, <, >, \", ') এস্কেপ করুন"
        div: "ঐতিহ্যবাহী TABLE ট্যাগের পরিবর্তে DIV+CSS লেআউট ব্যবহার করুন, রেসপন্সিভ ডিজাইনের জন্য আরও উপযুক্ত"
        minify: "সংকুচিত HTML কোড তৈরি করতে হোয়াইটস্পেস এবং লাইন ব্রেক সরান"
        thead: "স্ট্যান্ডার্ড টেবিল হেড (&lt;thead&gt;) এবং বডি (&lt;tbody&gt;) কাঠামো তৈরি করুন"
        tableCaption: "টেবিলের উপরে বর্ণনামূলক শিরোনাম যোগ করুন (&lt;caption&gt; এলিমেন্ট)"
        tableClass: "সহজ স্টাইল কাস্টমাইজেশনের জন্য টেবিলে CSS ক্লাস নাম যোগ করুন"
        tableId: "JavaScript ম্যানিপুলেশনের জন্য টেবিলের জন্য অনন্য ID আইডেন্টিফায়ার সেট করুন"
      jira:
        escape: "Jira টেবিল সিনট্যাক্সের সাথে দ্বন্দ্ব এড়াতে পাইপ অক্ষর (|) এস্কেপ করুন"
      json:
        parsingJSON: "কোষে JSON স্ট্রিংগুলি বুদ্ধিমত্তার সাথে অবজেক্টে পার্স করুন"
        minify: "ফাইলের আকার কমাতে কমপ্যাক্ট একক-লাইন JSON ফরম্যাট তৈরি করুন"
        format: "আউটপুট JSON ডেটা কাঠামো নির্বাচন করুন: অবজেক্ট অ্যারে, 2D অ্যারে, ইত্যাদি।"
      latex:
        escape: "সঠিক কম্পাইলেশন নিশ্চিত করতে LaTeX বিশেষ অক্ষর (%, &, _, #, $, ইত্যাদি) এস্কেপ করুন"
        ht: "পৃষ্ঠায় টেবিলের অবস্থান নিয়ন্ত্রণ করতে ফ্লোটিং পজিশন প্যারামিটার [!ht] যোগ করুন"
        mwe: "সম্পূর্ণ LaTeX ডকুমেন্ট তৈরি করুন"
        tableAlign: "পৃষ্ঠায় টেবিলের অনুভূমিক সারিবদ্ধতা সেট করুন"
        tableBorder: "টেবিল বর্ডার স্টাইল কনফিগার করুন: কোন বর্ডার নেই, আংশিক বর্ডার, সম্পূর্ণ বর্ডার"
        label: "\\ref{} কমান্ড ক্রস-রেফারেন্সিংয়ের জন্য টেবিল লেবেল সেট করুন"
        caption: "টেবিলের উপরে বা নিচে প্রদর্শনের জন্য টেবিল ক্যাপশন সেট করুন"
        location: "টেবিল ক্যাপশন প্রদর্শনের অবস্থান বেছে নিন: উপরে বা নিচে"
        tableType: "টেবিল এনভায়রনমেন্ট টাইপ বেছে নিন: tabular, longtable, array, ইত্যাদি।"
      markdown:
        escape: "ফরম্যাট দ্বন্দ্ব এড়াতে Markdown বিশেষ অক্ষর (*, _, |, \\, ইত্যাদি) এস্কেপ করুন"
        pretty: "আরও সুন্দর টেবিল ফরম্যাট তৈরি করতে কলামের প্রস্থ স্বয়ংক্রিয়ভাবে সারিবদ্ধ করুন"
        simple: "সরলীকৃত সিনট্যাক্স ব্যবহার করুন, বাইরের বর্ডার উল্লম্ব লাইন বাদ দিয়ে"
        boldFirstRow: "প্রথম সারির টেক্সট বোল্ড করুন"
        boldFirstColumn: "প্রথম কলামের টেক্সট বোল্ড করুন"
        firstHeader: "প্রথম সারিকে হেডার হিসেবে বিবেচনা করুন এবং বিভাজক লাইন যোগ করুন"
        textAlign: "কলাম টেক্সট সারিবদ্ধতা সেট করুন: বাম, কেন্দ্র, ডান"
        multilineHandling: "মাল্টিলাইন টেক্সট হ্যান্ডলিং: লাইন ব্রেক সংরক্ষণ করুন, \\n এ এস্কেপ করুন, &lt;br&gt; ট্যাগ ব্যবহার করুন"

        includeLineNumbers: "টেবিলের বাম দিকে লাইন নম্বর কলাম যোগ করুন"
      magic:
        builtin: "পূর্বনির্ধারিত সাধারণ টেমপ্লেট ফরম্যাট নির্বাচন করুন"
        rowsTpl: "<table> <tr> <th>ম্যাজিক সিনট্যাক্স</th> <th>বর্ণনা</th> <th>JS মেথড সাপোর্ট</th> </tr> <tr> <td>{h1} {h2} ...</td> <td><b>হেডিং</b>এর ১ম, ২য় ... ফিল্ড, অর্থাৎ {hA} {hB} ...</td> <td>স্ট্রিং মেথড</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>বর্তমান সারির ১ম, ২য় ... ফিল্ড, অর্থাৎ {$A} {$B} ...</td> <td>স্ট্রিং মেথড</td> </tr> <tr> <td>{F,} {F;}</td> <td><b>F</b> এর পরের স্ট্রিং দ্বারা বর্তমান সারি বিভক্ত করুন</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>বর্তমান <b>সারির</b> লাইন <b>নম্বর</b> ১ বা ১০০ থেকে</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>সারিগুলির</b> <b>শেষ</b> লাইন <b>নম্বর</b> </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>JavaScript কোড <b>এক্সিকিউট</b> করুন, যেমন: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> ব্রেসেস {...} আউটপুট করতে ব্যাকস্ল্যাশ <b>\\</b> ব্যবহার করুন </td> <td></td> </tr></table>"
        headerTpl: "হেডার বিভাগের জন্য কাস্টম আউটপুট টেমপ্লেট"
        footerTpl: "ফুটার বিভাগের জন্য কাস্টম আউটপুট টেমপ্লেট"
      textile:
        escape: "ফরম্যাট দ্বন্দ্ব এড়াতে Textile সিনট্যাক্স অক্ষর (|, ., -, ^) এস্কেপ করুন"
        rowHeader: "প্রথম সারিকে হেডার সারি হিসেবে সেট করুন"
        thead: "টেবিল হেড এবং বডির জন্য Textile সিনট্যাক্স মার্কার যোগ করুন"
      xml:
        escape: "বৈধ XML নিশ্চিত করতে XML বিশেষ অক্ষর (&lt;, &gt;, &amp;, \", ') এস্কেপ করুন"
        minify: "অতিরিক্ত হোয়াইটস্পেস সরিয়ে সংকুচিত XML আউটপুট তৈরি করুন"
        rootElement: "XML রুট এলিমেন্ট ট্যাগ নাম সেট করুন"
        rowElement: "ডেটার প্রতিটি সারির জন্য XML এলিমেন্ট ট্যাগ নাম সেট করুন"
        declaration: "XML ডিক্লারেশন হেডার যোগ করুন (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "চাইল্ড এলিমেন্টের পরিবর্তে XML অ্যাট্রিবিউট হিসেবে ডেটা আউটপুট করুন"
        cdata: "বিশেষ অক্ষর সুরক্ষিত করতে CDATA দিয়ে টেক্সট কন্টেন্ট মোড়ান"
        encoding: "XML ডকুমেন্টের জন্য অক্ষর এনকোডিং ফরম্যাট সেট করুন"
        indentation: "XML ইন্ডেন্টেশন অক্ষর বেছে নিন: স্পেস বা ট্যাব"
      yaml:
        indentSize: "YAML হায়ারার্কি ইন্ডেন্টেশনের জন্য স্পেসের সংখ্যা সেট করুন (সাধারণত ২ বা ৪)"
        arrayStyle: "অ্যারে ফরম্যাট: ব্লক (প্রতি লাইনে একটি আইটেম) বা ফ্লো (ইনলাইন ফরম্যাট)"
        quotationStyle: "স্ট্রিং কোট স্টাইল: কোন কোট নেই, একক কোট, ডাবল কোট"
      pdf:
        theme: "পেশাদার ডকুমেন্টের জন্য PDF টেবিল ভিজ্যুয়াল স্টাইল বেছে নিন"
        headerColor: "PDF টেবিল হেডার ব্যাকগ্রাউন্ড রঙ বেছে নিন"
        showHead: "PDF পৃষ্ঠায় হেডার প্রদর্শন নিয়ন্ত্রণ করুন"
        docTitle: "PDF ডকুমেন্টের জন্য ঐচ্ছিক শিরোনাম"
        docDescription: "PDF ডকুমেন্টের জন্য ঐচ্ছিক বিবরণ টেক্সট"
      csv:
        bom: "Excel এবং অন্যান্য সফটওয়্যারকে এনকোডিং চিনতে সাহায্য করতে UTF-8 বাইট অর্ডার মার্ক যোগ করুন"
      excel:
        autoWidth: "কন্টেন্টের ভিত্তিতে স্বয়ংক্রিয়ভাবে কলামের প্রস্থ সামঞ্জস্য করুন"
        protectSheet: "পাসওয়ার্ড দিয়ে ওয়ার্কশিট সুরক্ষা সক্ষম করুন: tableconvert.com"
      sql:
        primaryKey: "CREATE TABLE স্টেটমেন্টের জন্য প্রাইমারি কী ফিল্ডের নাম নির্দিষ্ট করুন"
        dialect: "ডেটাবেস টাইপ নির্বাচন করুন, কোট এবং ডেটা টাইপ সিনট্যাক্সকে প্রভাবিত করে"
      ascii:
        forceSep: "ডেটার প্রতিটি সারির মধ্যে বিভাজক লাইন জোর করুন"
        style: "ASCII টেবিল বর্ডার অঙ্কন স্টাইল নির্বাচন করুন"
        comment: "সম্পূর্ণ টেবিল মোড়ানোর জন্য কমেন্ট মার্কার যোগ করুন"
      mediawiki:
        minify: "অতিরিক্ত হোয়াইটস্পেস সরিয়ে আউটপুট কোড সংকুচিত করুন"
        header: "প্রথম সারিকে হেডার স্টাইল হিসেবে চিহ্নিত করুন"
        sort: "টেবিল ক্লিক সর্টিং কার্যকারিতা সক্ষম করুন"
      asciidoc:
        minify: "AsciiDoc ফরম্যাট আউটপুট সংকুচিত করুন"
        firstHeader: "প্রথম সারিকে হেডার সারি হিসেবে সেট করুন"
        lastFooter: "শেষ সারিকে ফুটার সারি হিসেবে সেট করুন"
        title: "টেবিলে শিরোনাম টেক্সট যোগ করুন"
      tracwiki:
        rowHeader: "প্রথম সারিকে হেডার হিসেবে সেট করুন"
        colHeader: "প্রথম কলামকে হেডার হিসেবে সেট করুন"
      bbcode:
        minify: "BBCode আউটপুট ফরম্যাট সংকুচিত করুন"
      restructuredtext:
        style: "reStructuredText টেবিল বর্ডার স্টাইল নির্বাচন করুন"
        forceSep: "বিভাজক লাইন জোর করুন"
    label:
      ascii:
        forceSep: "সারি বিভাজক"
        style: "বর্ডার স্টাইল"
        comment: "কমেন্ট র‍্যাপার"
      restructuredtext:
        style: "বর্ডার স্টাইল"
        forceSep: "বিভাজক জোর করুন"
      bbcode:
        minify: "আউটপুট মিনিফাই করুন"
      csv:
        doubleQuote: "ডাবল কোট র‍্যাপ"
        delimiter: "ফিল্ড ডিলিমিটার"
        bom: "UTF-8 BOM"
        valueDelimiter: "ভ্যালু ডিলিমিটার"
        rowDelimiter: "সারি ডিলিমিটার"
        prefix: "সারি প্রিফিক্স"
        suffix: "সারি সাফিক্স"
      excel:
        autoWidth: "অটো প্রস্থ"
        textFormat: "টেক্সট ফরম্যাট"
        protectSheet: "শিট সুরক্ষা"
        boldFirstRow: "প্রথম সারি বোল্ড"
        boldFirstColumn: "প্রথম কলাম বোল্ড"
        sheetName: "শিটের নাম"
      html:
        escape: "HTML অক্ষর এস্কেপ"
        div: "DIV টেবিল"
        minify: "কোড মিনিফাই"
        thead: "টেবিল হেড কাঠামো"
        tableCaption: "টেবিল ক্যাপশন"
        tableClass: "টেবিল ক্লাস"
        tableId: "টেবিল ID"
        rowHeader: "সারি হেডার"
        colHeader: "কলাম হেডার"
      jira:
        escape: "অক্ষর এস্কেপ"
        rowHeader: "সারি হেডার"
        colHeader: "কলাম হেডার"
      json:
        parsingJSON: "JSON পার্স"
        minify: "আউটপুট মিনিফাই"
        format: "ডেটা ফরম্যাট"
        rootName: "রুট অবজেক্টের নাম"
        indentSize: "ইন্ডেন্ট সাইজ"
      jsonlines:
        parsingJSON: "JSON পার্স"
        format: "ডেটা ফরম্যাট"
      latex:
        escape: "LaTeX টেবিল অক্ষর এস্কেপ"
        ht: "ফ্লোট পজিশন"
        mwe: "সম্পূর্ণ ডকুমেন্ট"
        tableAlign: "টেবিল সারিবদ্ধতা"
        tableBorder: "বর্ডার স্টাইল"
        label: "রেফারেন্স লেবেল"
        caption: "টেবিল ক্যাপশন"
        location: "ক্যাপশন অবস্থান"
        tableType: "টেবিল টাইপ"
        boldFirstRow: "প্রথম সারি বোল্ড"
        boldFirstColumn: "প্রথম কলাম বোল্ড"
        textAlign: "টেক্সট সারিবদ্ধতা"
        borders: "বর্ডার সেটিংস"
      markdown:
        escape: "অক্ষর এস্কেপ"
        pretty: "সুন্দর Markdown টেবিল"
        simple: "সরল Markdown ফরম্যাট"
        boldFirstRow: "প্রথম সারি বোল্ড"
        boldFirstColumn: "প্রথম কলাম বোল্ড"
        firstHeader: "প্রথম হেডার"
        textAlign: "টেক্সট সারিবদ্ধতা"
        multilineHandling: "মাল্টিলাইন হ্যান্ডলিং"

        includeLineNumbers: "লাইন নম্বর যোগ করুন"
        align: "সারিবদ্ধতা"
      mediawiki:
        minify: "কোড মিনিফাই"
        header: "হেডার মার্কআপ"
        sort: "সর্টযোগ্য"
      asciidoc:
        minify: "ফরম্যাট মিনিফাই"
        firstHeader: "প্রথম হেডার"
        lastFooter: "শেষ ফুটার"
        title: "টেবিল শিরোনাম"
      tracwiki:
        rowHeader: "সারি হেডার"
        colHeader: "কলাম হেডার"
      sql:
        drop: "টেবিল ড্রপ (যদি থাকে)"
        create: "টেবিল তৈরি"
        oneInsert: "ব্যাচ ইনসার্ট"
        table: "টেবিলের নাম"
        dialect: "ডেটাবেস টাইপ"
        primaryKey: "প্রাইমারি কী"
      magic:
        builtin: "বিল্ট-ইন টেমপ্লেট"
        rowsTpl: "সারি টেমপ্লেট, সিনট্যাক্স ->"
        headerTpl: "হেডার টেমপ্লেট"
        footerTpl: "ফুটার টেমপ্লেট"
      textile:
        escape: "অক্ষর এস্কেপ"
        rowHeader: "সারি হেডার"
        thead: "টেবিল হেড সিনট্যাক্স"
      xml:
        escape: "XML অক্ষর এস্কেপ"
        minify: "আউটপুট মিনিফাই"
        rootElement: "রুট এলিমেন্ট"
        rowElement: "সারি এলিমেন্ট"
        declaration: "XML ডিক্লারেশন"
        attributes: "অ্যাট্রিবিউট মোড"
        cdata: "CDATA র‍্যাপার"
        encoding: "এনকোডিং"
        indentSize: "ইন্ডেন্ট সাইজ"
      yaml:
        indentSize: "ইন্ডেন্ট সাইজ"
        arrayStyle: "অ্যারে স্টাইল"
        quotationStyle: "কোট স্টাইল"
      pdf:
        theme: "PDF টেবিল থিম"
        headerColor: "PDF হেডার রঙ"
        showHead: "PDF হেডার প্রদর্শন"
        docTitle: "PDF ডকুমেন্ট শিরোনাম"
        docDescription: "PDF ডকুমেন্ট বিবরণ"
sidebar:
  all: "সব রূপান্তর টুল"
  dataSource:
    title: "ডেটা সোর্স"
    description:
      converter: "%s থেকে %s রূপান্তরের জন্য ইম্পোর্ট করুন। ফাইল আপলোড, অনলাইন এডিটিং এবং ওয়েব ডেটা এক্সট্র্যাকশন সমর্থন করে।"
      generator: "ম্যানুয়াল ইনপুট, ফাইল ইম্পোর্ট এবং টেমপ্লেট জেনারেশন সহ একাধিক ইনপুট পদ্ধতির সাথে টেবিল ডেটা তৈরি করুন।"
  tableEditor:
    title: "অনলাইন টেবিল এডিটর"
    description:
      converter: "আমাদের টেবিল এডিটর ব্যবহার করে অনলাইনে %s প্রসেস করুন। খালি সারি মুছে ফেলা, ডুপ্লিকেশন, সর্টিং এবং খুঁজে ও প্রতিস্থাপনের সাথে Excel-এর মতো অপারেশন অভিজ্ঞতা।"
      generator: "Excel-এর মতো অপারেশন অভিজ্ঞতা প্রদানকারী শক্তিশালী অনলাইন টেবিল এডিটর। খালি সারি মুছে ফেলা, ডুপ্লিকেশন, সর্টিং এবং খুঁজে ও প্রতিস্থাপন সমর্থন করে।"
  tableGenerator:
    title: "টেবিল জেনারেটর"
    description:
      converter: "টেবিল জেনারেটরের রিয়েল-টাইম প্রিভিউ সহ দ্রুত %s জেনারেট করুন। সমৃদ্ধ এক্সপোর্ট অপশন, এক-ক্লিক কপি এবং ডাউনলোড।"
      generator: "বিভিন্ন ব্যবহারের পরিস্থিতি পূরণের জন্য একাধিক ফরম্যাটে %s ডেটা এক্সপোর্ট করুন। কাস্টম অপশন এবং রিয়েল-টাইম প্রিভিউ সমর্থন করে।"
footer:
  changelog: "পরিবর্তনের লগ"
  sponsor: "স্পনসর"
  contact: "যোগাযোগ করুন"
  privacyPolicy: "গোপনীয়তা নীতি"
  about: "সম্পর্কে"
  resources: "রিসোর্স"
  popularConverters: "জনপ্রিয় কনভার্টার"
  popularGenerators: "জনপ্রিয় জেনারেটর"
  dataSecurity: "আপনার ডেটা নিরাপদ - সমস্ত রূপান্তর আপনার ব্রাউজারে চলে."
converters:
  Markdown:
    alias: "Markdown টেবিল"
    what: "Markdown একটি হালকা মার্কআপ ভাষা যা প্রযুক্তিগত ডকুমেন্টেশন, ব্লগ কন্টেন্ট তৈরি এবং ওয়েব ডেভেলপমেন্টের জন্য ব্যাপকভাবে ব্যবহৃত হয়। এর টেবিল সিনট্যাক্স সংক্ষিপ্ত এবং স্বজ্ঞাত, টেক্সট সারিবদ্ধতা, লিঙ্ক এম্বেডিং এবং ফরম্যাটিং সমর্থন করে। এটি প্রোগ্রামার এবং প্রযুক্তিগত লেখকদের পছন্দের টুল, GitHub, GitLab এবং অন্যান্য কোড হোস্টিং প্ল্যাটফর্মের সাথে নিখুঁতভাবে সামঞ্জস্যপূর্ণ।"
    step1: "ডেটা সোর্স এলাকায় Markdown টেবিল ডেটা পেস্ট করুন, বা সরাসরি .md ফাইল আপলোডের জন্য ড্র্যাগ এবং ড্রপ করুন। টুলটি স্বয়ংক্রিয়ভাবে টেবিল কাঠামো এবং ফরম্যাটিং পার্স করে, জটিল নেস্টেড কন্টেন্ট এবং বিশেষ অক্ষর হ্যান্ডলিং সমর্থন করে।"
    step3: "রিয়েল-টাইমে স্ট্যান্ডার্ড Markdown টেবিল কোড তৈরি করুন, একাধিক সারিবদ্ধতা পদ্ধতি, টেক্সট বোল্ডিং, লাইন নম্বর যোগ এবং অন্যান্য উন্নত ফরম্যাট সেটিংস সমর্থন করে। তৈরি কোড GitHub এবং প্রধান Markdown এডিটরগুলির সাথে সম্পূর্ণভাবে সামঞ্জস্যপূর্ণ, এক-ক্লিক কপি দিয়ে ব্যবহারের জন্য প্রস্তুত।"
    from_alias: "Markdown টেবিল ফাইল"
    to_alias: "Markdown টেবিল ফরম্যাট"
  Magic:
    alias: "কাস্টম টেমপ্লেট"
    what: "ম্যাজিক টেমপ্লেট এই টুলের একটি অনন্য উন্নত ডেটা জেনারেটর, যা ব্যবহারকারীদের কাস্টম টেমপ্লেট সিনট্যাক্সের মাধ্যমে যেকোনো ফরম্যাটের ডেটা আউটপুট তৈরি করতে দেয়। ভেরিয়েবল প্রতিস্থাপন, শর্তাধীন বিচার এবং লুপ প্রসেসিং সমর্থন করে। এটি জটিল ডেটা রূপান্তরের প্রয়োজন এবং ব্যক্তিগতকৃত আউটপুট ফরম্যাট পরিচালনার জন্য চূড়ান্ত সমাধান, বিশেষত ডেভেলপার এবং ডেটা ইঞ্জিনিয়ারদের জন্য উপযুক্ত।"
    step1: "বিল্ট-ইন সাধারণ টেমপ্লেট নির্বাচন করুন বা কাস্টম টেমপ্লেট সিনট্যাক্স তৈরি করুন। সমৃদ্ধ ভেরিয়েবল এবং ফাংশন সমর্থন করে যা জটিল ডেটা কাঠামো এবং ব্যবসায়িক লজিক পরিচালনা করতে পারে।"
    step3: "কাস্টম ফরম্যাট প্রয়োজনীয়তা সম্পূর্ণভাবে পূরণ করে এমন ডেটা আউটপুট তৈরি করুন। জটিল ডেটা রূপান্তর লজিক এবং শর্তাধীন প্রসেসিং সমর্থন করে, ডেটা প্রসেসিং দক্ষতা এবং আউটপুট গুণমান ব্যাপকভাবে উন্নত করে। ব্যাচ ডেটা প্রসেসিংয়ের জন্য একটি শক্তিশালী টুল।"
    from_alias: "টেবিল ডেটা"
    to_alias: "কাস্টম ফরম্যাট আউটপুট"
  CSV:
    alias: "CSV"
    what: "CSV (কমা-বিভক্ত মান) সবচেয়ে ব্যাপকভাবে ব্যবহৃত ডেটা এক্সচেঞ্জ ফরম্যাট, Excel, Google Sheets, ডেটাবেস সিস্টেম এবং বিভিন্ন ডেটা বিশ্লেষণ টুল দ্বারা নিখুঁতভাবে সমর্থিত। এর সরল কাঠামো এবং শক্তিশালী সামঞ্জস্য এটিকে ডেটা মাইগ্রেশন, ব্যাচ ইম্পোর্ট/এক্সপোর্ট এবং ক্রস-প্ল্যাটফর্ম ডেটা এক্সচেঞ্জের জন্য স্ট্যান্ডার্ড ফরম্যাট করে তোলে, ব্যবসায়িক বিশ্লেষণ, ডেটা সায়েন্স এবং সিস্টেম ইন্টিগ্রেশনে ব্যাপকভাবে ব্যবহৃত।"
    step1: "CSV ফাইল আপলোড করুন বা সরাসরি CSV ডেটা পেস্ট করুন। টুলটি বুদ্ধিমত্তার সাথে বিভিন্ন ডিলিমিটার (কমা, ট্যাব, সেমিকোলন, পাইপ, ইত্যাদি) চিনতে পারে, স্বয়ংক্রিয়ভাবে ডেটা টাইপ এবং এনকোডিং ফরম্যাট সনাক্ত করে, বড় ফাইল এবং জটিল ডেটা কাঠামোর দ্রুত পার্সিং সমর্থন করে।"
    step3: "কাস্টম ডিলিমিটার, কোট স্টাইল, এনকোডিং ফরম্যাট এবং BOM মার্ক সেটিংস সহ স্ট্যান্ডার্ড CSV ফরম্যাট ফাইল তৈরি করুন। টার্গেট সিস্টেমের সাথে নিখুঁত সামঞ্জস্য নিশ্চিত করে, এন্টারপ্রাইজ-স্তরের ডেটা প্রসেসিং প্রয়োজন পূরণের জন্য ডাউনলোড এবং কম্প্রেশন অপশন প্রদান করে।"
    from_alias: "CSV ডেটা ফাইল"
    to_alias: "CSV স্ট্যান্ডার্ড ফরম্যাট"
  JSON:
    alias: "JSON অ্যারে"
    what: "JSON (JavaScript Object Notation) আধুনিক ওয়েব অ্যাপ্লিকেশন, REST API এবং মাইক্রোসার্ভিস আর্কিটেকচারের জন্য স্ট্যান্ডার্ড টেবিল ডেটা ফরম্যাট। এর স্পষ্ট কাঠামো এবং দক্ষ পার্সিং এটিকে ফ্রন্ট-এন্ড এবং ব্যাক-এন্ড ডেটা ইন্টারঅ্যাকশন, কনফিগারেশন ফাইল স্টোরেজ এবং NoSQL ডেটাবেসে ব্যাপকভাবে ব্যবহৃত করে তোলে। নেস্টেড অবজেক্ট, অ্যারে কাঠামো এবং একাধিক ডেটা টাইপ সমর্থন করে, এটিকে আধুনিক সফটওয়্যার ডেভেলপমেন্টের জন্য অপরিহার্য টেবিল ডেটা করে তোলে।"
    step1: "JSON ফাইল আপলোড করুন বা JSON অ্যারে পেস্ট করুন। অবজেক্ট অ্যারে, নেস্টেড কাঠামো এবং জটিল ডেটা টাইপের স্বয়ংক্রিয় স্বীকৃতি এবং পার্সিং সমর্থন করে। টুলটি বুদ্ধিমত্তার সাথে JSON সিনট্যাক্স যাচাই করে এবং ত্রুটির প্রম্পট প্রদান করে।"
    step3: "একাধিক JSON ফরম্যাট আউটপুট তৈরি করুন: স্ট্যান্ডার্ড অবজেক্ট অ্যারে, 2D অ্যারে, কলাম অ্যারে এবং কী-ভ্যালু পেয়ার ফরম্যাট। সুন্দরীকৃত আউটপুট, কম্প্রেশন মোড, কাস্টম রুট অবজেক্ট নাম এবং ইন্ডেন্টেশন সেটিংস সমর্থন করে, বিভিন্ন API ইন্টারফেস এবং ডেটা স্টোরেজ প্রয়োজনের সাথে নিখুঁতভাবে খাপ খায়।"
    from_alias: "JSON অ্যারে ফাইল"
    to_alias: "JSON স্ট্যান্ডার্ড ফরম্যাট"
  JSONLines:
    alias: "JSONLines ফরম্যাট"
    what: "JSON Lines (NDJSON নামেও পরিচিত) বিগ ডেটা প্রসেসিং এবং স্ট্রিমিং ডেটা ট্রান্সমিশনের জন্য একটি গুরুত্বপূর্ণ ফরম্যাট, প্রতিটি লাইনে একটি স্বাধীন JSON অবজেক্ট রয়েছে। লগ বিশ্লেষণ, ডেটা স্ট্রিম প্রসেসিং, মেশিন লার্নিং এবং বিতরণ করা সিস্টেমে ব্যাপকভাবে ব্যবহৃত। ইনক্রিমেন্টাল প্রসেসিং এবং প্যারালেল কম্পিউটিং সমর্থন করে, এটিকে বৃহৎ-স্কেল কাঠামোগত ডেটা পরিচালনার জন্য আদর্শ পছন্দ করে তোলে।"
    step1: "JSONLines ফাইল আপলোড করুন বা ডেটা পেস্ট করুন। টুলটি লাইন বাই লাইন JSON অবজেক্ট পার্স করে, বড় ফাইল স্ট্রিমিং প্রসেসিং এবং ত্রুটি লাইন এড়িয়ে যাওয়ার কার্যকারিতা সমর্থন করে।"
    step3: "প্রতিটি লাইনে একটি সম্পূর্ণ JSON অবজেক্ট আউটপুট করে স্ট্যান্ডার্ড JSONLines ফরম্যাট তৈরি করুন। স্ট্রিমিং প্রসেসিং, ব্যাচ ইম্পোর্ট এবং বিগ ডেটা বিশ্লেষণ পরিস্থিতির জন্য উপযুক্ত, ডেটা যাচাইকরণ এবং ফরম্যাট অপ্টিমাইজেশন সমর্থন করে।"
    from_alias: "JSONLines ডেটা"
    to_alias: "JSONLines স্ট্রিমিং ফরম্যাট"
  XML:
    alias: "XML"
    what: "XML (এক্সটেনসিবল মার্কআপ ল্যাঙ্গুয়েজ) এন্টারপ্রাইজ-স্তরের ডেটা এক্সচেঞ্জ এবং কনফিগারেশন ম্যানেজমেন্টের জন্য স্ট্যান্ডার্ড ফরম্যাট, কঠোর সিনট্যাক্স স্পেসিফিকেশন এবং শক্তিশালী যাচাইকরণ প্রক্রিয়া সহ। ওয়েব সার্ভিস, কনফিগারেশন ফাইল, ডকুমেন্ট স্টোরেজ এবং সিস্টেম ইন্টিগ্রেশনে ব্যাপকভাবে ব্যবহৃত। নেমস্পেস, স্কিমা যাচাইকরণ এবং XSLT রূপান্তর সমর্থন করে, এটিকে এন্টারপ্রাইজ অ্যাপ্লিকেশনের জন্য গুরুত্বপূর্ণ টেবিল ডেটা করে তোলে।"
    step1: "XML ফাইল আপলোড করুন বা XML ডেটা পেস্ট করুন। টুলটি স্বয়ংক্রিয়ভাবে XML কাঠামো পার্স করে এবং এটিকে টেবিল ফরম্যাটে রূপান্তরিত করে, নেমস্পেস, অ্যাট্রিবিউট হ্যান্ডলিং এবং জটিল নেস্টেড কাঠামো সমর্থন করে।"
    step3: "XML স্ট্যান্ডার্ডের সাথে সামঞ্জস্যপূর্ণ XML আউটপুট তৈরি করুন। কাস্টম রুট এলিমেন্ট, সারি এলিমেন্ট নাম, অ্যাট্রিবিউট মোড, CDATA র‍্যাপিং এবং অক্ষর এনকোডিং সেটিংস সমর্থন করে। ডেটা অখণ্ডতা এবং সামঞ্জস্য নিশ্চিত করে, এন্টারপ্রাইজ-স্তরের অ্যাপ্লিকেশন প্রয়োজনীয়তা পূরণ করে।"
    from_alias: "XML ডেটা ফাইল"
    to_alias: "XML স্ট্যান্ডার্ড ফরম্যাট"
  YAML:
    alias: "YAML কনফিগারেশন"
    what: "YAML একটি মানব-বান্ধব ডেটা সিরিয়ালাইজেশন স্ট্যান্ডার্ড, এর স্পষ্ট হায়ারার্কিক্যাল কাঠামো এবং সংক্ষিপ্ত সিনট্যাক্সের জন্য বিখ্যাত। কনফিগারেশন ফাইল, DevOps টুল চেইন, Docker Compose এবং Kubernetes ডিপ্লয়মেন্টে ব্যাপকভাবে ব্যবহৃত। এর শক্তিশালী পাঠযোগ্যতা এবং সংক্ষিপ্ত সিনট্যাক্স এটিকে আধুনিক ক্লাউড-নেটিভ অ্যাপ্লিকেশন এবং স্বয়ংক্রিয় অপারেশনের জন্য একটি গুরুত্বপূর্ণ কনফিগারেশন ফরম্যাট করে তোলে।"
    step1: "YAML ফাইল আপলোড করুন বা YAML ডেটা পেস্ট করুন। টুলটি বুদ্ধিমত্তার সাথে YAML কাঠামো পার্স করে এবং সিনট্যাক্স সঠিকতা যাচাই করে, মাল্টি-ডকুমেন্ট ফরম্যাট এবং জটিল ডেটা টাইপ সমর্থন করে।"
    step3: "ব্লক এবং ফ্লো অ্যারে স্টাইল, একাধিক কোট সেটিংস, কাস্টম ইন্ডেন্টেশন এবং কমেন্ট সংরক্ষণের সাথে স্ট্যান্ডার্ড YAML ফরম্যাট আউটপুট তৈরি করুন। নিশ্চিত করে যে আউটপুট YAML ফাইলগুলি বিভিন্ন পার্সার এবং কনফিগারেশন সিস্টেমের সাথে সম্পূর্ণভাবে সামঞ্জস্যপূর্ণ।"
    from_alias: "YAML কনফিগারেশন ফাইল"
    to_alias: "YAML স্ট্যান্ডার্ড ফরম্যাট"
  MySQL:
      alias: "MySQL কোয়েরি ফলাফল"
      what: "MySQL বিশ্বের সবচেয়ে জনপ্রিয় ওপেন-সোর্স রিলেশনাল ডেটাবেস ম্যানেজমেন্ট সিস্টেম, এর উচ্চ কর্মক্ষমতা, নির্ভরযোগ্যতা এবং ব্যবহারের সহজতার জন্য বিখ্যাত। ওয়েব অ্যাপ্লিকেশন, এন্টারপ্রাইজ সিস্টেম এবং ডেটা বিশ্লেষণ প্ল্যাটফর্মে ব্যাপকভাবে ব্যবহৃত। MySQL কোয়েরি ফলাফল সাধারণত কাঠামোগত টেবিল ডেটা ধারণ করে, ডেটাবেস ম্যানেজমেন্ট এবং ডেটা বিশ্লেষণ কাজে একটি গুরুত্বপূর্ণ ডেটা সোর্স হিসেবে কাজ করে।"
      step1: "ডেটা সোর্স এলাকায় MySQL কোয়েরি আউটপুট ফলাফল পেস্ট করুন। টুলটি স্বয়ংক্রিয়ভাবে MySQL কমান্ড-লাইন আউটপুট ফরম্যাট চিনতে এবং পার্স করে, বিভিন্ন কোয়েরি ফলাফল স্টাইল এবং অক্ষর এনকোডিং সমর্থন করে, বুদ্ধিমত্তার সাথে হেডার এবং ডেটা সারি পরিচালনা করে।"
      step3: "MySQL কোয়েরি ফলাফলকে একাধিক টেবিল ডেটা ফরম্যাটে দ্রুত রূপান্তর করুন, ডেটা বিশ্লেষণ, রিপোর্ট তৈরি, ক্রস-সিস্টেম ডেটা মাইগ্রেশন এবং ডেটা যাচাইকরণ সহজতর করে। ডেটাবেস প্রশাসক এবং ডেটা বিশ্লেষকদের জন্য একটি ব্যবহারিক টুল।"
      from_alias: "MySQL কোয়েরি আউটপুট"
      to_alias: "MySQL টেবিল ডেটা"
  SQL:
    alias: "ইনসার্ট SQL"
    what: "SQL (স্ট্রাকচার্ড কোয়েরি ল্যাঙ্গুয়েজ) রিলেশনাল ডেটাবেসের জন্য স্ট্যান্ডার্ড অপারেশন ভাষা, ডেটা কোয়েরি, ইনসার্ট, আপডেট এবং ডিলিট অপারেশনের জন্য ব্যবহৃত। ডেটাবেস ম্যানেজমেন্টের মূল প্রযুক্তি হিসেবে, SQL ডেটা বিশ্লেষণ, বিজনেস ইন্টেলিজেন্স, ETL প্রসেসিং এবং ডেটা ওয়্যারহাউস নির্মাণে ব্যাপকভাবে ব্যবহৃত। এটি ডেটা পেশাদারদের জন্য একটি অপরিহার্য দক্ষতার টুল।"
    step1: "INSERT SQL স্টেটমেন্ট পেস্ট করুন বা .sql ফাইল আপলোড করুন। টুলটি বুদ্ধিমত্তার সাথে SQL সিনট্যাক্স পার্স করে এবং টেবিল ডেটা এক্সট্র্যাক্ট করে, একাধিক SQL ডায়ালেক্ট এবং জটিল কোয়েরি স্টেটমেন্ট প্রসেসিং সমর্থন করে।"
    step3: "স্ট্যান্ডার্ড SQL INSERT স্টেটমেন্ট এবং টেবিল তৈরির স্টেটমেন্ট তৈরি করুন। একাধিক ডেটাবেস ডায়ালেক্ট (MySQL, PostgreSQL, SQLite, SQL Server, Oracle) সমর্থন করে, স্বয়ংক্রিয়ভাবে ডেটা টাইপ ম্যাপিং, অক্ষর এস্কেপিং এবং প্রাইমারি কী সীমাবদ্ধতা পরিচালনা করে। নিশ্চিত করে যে তৈরি SQL কোড সরাসরি এক্সিকিউট করা যায়।"
    from_alias: "SQL ডেটা ফাইল"
    to_alias: "SQL স্ট্যান্ডার্ড স্টেটমেন্ট"
  Qlik:
      alias: "Qlik টেবিল"
      what: "Qlik একটি সফটওয়্যার বিক্রেতা যা ডেটা ভিজুয়ালাইজেশন, এক্সিকিউটিভ ড্যাশবোর্ড এবং সেলফ-সার্ভিস বিজনেস ইন্টেলিজেন্স পণ্যে বিশেষজ্ঞ, Tableau এবং Microsoft এর সাথে।"
      step1: ""
      step3: "অবশেষে, [টেবিল জেনারেটর](#TableGenerator) রূপান্তরের ফলাফল দেখায়। আপনার Qlik Sense, Qlik AutoML, QlikView, বা অন্যান্য Qlik-সক্ষম সফটওয়্যারে ব্যবহার করুন।"
      from_alias: "Qlik টেবিল"
      to_alias: "Qlik টেবিল"
  DAX:
      alias: "DAX টেবিল"
      what: "DAX (ডেটা অ্যানালাইসিস এক্সপ্রেশন) একটি প্রোগ্রামিং ভাষা যা Microsoft Power BI জুড়ে গণনাকৃত কলাম, পরিমাপ এবং কাস্টম টেবিল তৈরির জন্য ব্যবহৃত হয়।"
      step1: ""
      step3: "অবশেষে, [টেবিল জেনারেটর](#TableGenerator) রূপান্তরের ফলাফল দেখায়। প্রত্যাশিত হিসাবে, এটি Microsoft Power BI, Microsoft Analysis Services এবং Microsoft Power Pivot for Excel সহ বেশ কয়েকটি Microsoft পণ্যে ব্যবহৃত হয়।"
      from_alias: "DAX টেবিল"
      to_alias: "DAX টেবিল"
  Firebase:
    alias: "Firebase তালিকা"
    what: "Firebase একটি BaaS অ্যাপ্লিকেশন ডেভেলপমেন্ট প্ল্যাটফর্ম যা রিয়েল-টাইম ডেটাবেস, ক্লাউড স্টোরেজ, অথেন্টিকেশন, ক্র্যাশ রিপোর্টিং ইত্যাদির মতো হোস্টেড ব্যাকএন্ড সেবা প্রদান করে।"
    step1: ""
    step3: "অবশেষে, [টেবিল জেনারেটর](#TableGenerator) রূপান্তরের ফলাফল দেখায়। তারপর আপনি Firebase ডেটাবেসে ডেটার তালিকায় যোগ করতে Firebase API-তে push মেথড ব্যবহার করতে পারেন।"
    from_alias: "Firebase তালিকা"
    to_alias: "Firebase তালিকা"
  HTML:
    alias: "HTML টেবিল"
    what: "HTML টেবিল ওয়েব পেজে কাঠামোগত ডেটা প্রদর্শনের স্ট্যান্ডার্ড উপায়, table, tr, td এবং অন্যান্য ট্যাগ দিয়ে তৈরি। সমৃদ্ধ স্টাইল কাস্টমাইজেশন, রেসপন্সিভ লেআউট এবং ইন্টারঅ্যাক্টিভ কার্যকারিতা সমর্থন করে। ওয়েবসাইট ডেভেলপমেন্ট, ডেটা প্রদর্শন এবং রিপোর্ট তৈরিতে ব্যাপকভাবে ব্যবহৃত, ফ্রন্ট-এন্ড ডেভেলপমেন্ট এবং ওয়েব ডিজাইনের একটি গুরুত্বপূর্ণ উপাদান হিসেবে কাজ করে।"
    step1: "টেবিল সম্বলিত HTML কোড পেস্ট করুন বা HTML ফাইল আপলোড করুন। টুলটি স্বয়ংক্রিয়ভাবে পেজ থেকে টেবিল ডেটা চিনতে এবং এক্সট্র্যাক্ট করে, জটিল HTML কাঠামো, CSS স্টাইল এবং নেস্টেড টেবিল প্রসেসিং সমর্থন করে।"
    step3: "thead/tbody কাঠামো, CSS ক্লাস সেটিংস, টেবিল ক্যাপশন, সারি/কলাম হেডার এবং রেসপন্সিভ অ্যাট্রিবিউট কনফিগারেশনের সাথে সিমান্টিক HTML টেবিল কোড তৈরি করুন। নিশ্চিত করে যে তৈরি টেবিল কোড ওয়েব স্ট্যান্ডার্ড পূরণ করে ভাল অ্যাক্সেসিবিলিটি এবং SEO বন্ধুত্ব সহ।"
    from_alias: "HTML ওয়েব টেবিল"
    to_alias: "HTML স্ট্যান্ডার্ড টেবিল"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel বিশ্বের সবচেয়ে জনপ্রিয় স্প্রেডশিট সফটওয়্যার, ব্যবসায়িক বিশ্লেষণ, আর্থিক ব্যবস্থাপনা, ডেটা প্রসেসিং এবং রিপোর্ট তৈরিতে ব্যাপকভাবে ব্যবহৃত। এর শক্তিশালী ডেটা প্রসেসিং ক্ষমতা, সমৃদ্ধ ফাংশন লাইব্রেরি এবং নমনীয় ভিজুয়ালাইজেশন বৈশিষ্ট্য এটিকে অফিস অটোমেশন এবং ডেটা বিশ্লেষণের জন্য স্ট্যান্ডার্ড টুল করে তোলে, প্রায় সব শিল্প এবং ক্ষেত্রে ব্যাপক প্রয়োগ সহ।"
    step1: "Excel ফাইল আপলোড করুন (.xlsx, .xls ফরম্যাট সমর্থিত) বা Excel থেকে সরাসরি টেবিল ডেটা কপি করে পেস্ট করুন। টুলটি মাল্টি-ওয়ার্কশিট প্রসেসিং, জটিল ফরম্যাট স্বীকৃতি এবং বড় ফাইলের দ্রুত পার্সিং সমর্থন করে, স্বয়ংক্রিয়ভাবে মার্জ করা সেল এবং ডেটা টাইপ পরিচালনা করে।"
    step3: "Excel-সামঞ্জস্যপূর্ণ টেবিল ডেটা তৈরি করুন যা সরাসরি Excel-এ পেস্ট করা যায় বা স্ট্যান্ডার্ড .xlsx ফাইল হিসেবে ডাউনলোড করা যায়। ওয়ার্কশিট নামকরণ, সেল ফরম্যাটিং, অটো কলাম প্রস্থ, হেডার স্টাইলিং এবং ডেটা ভ্যালিডেশন সেটিংস সমর্থন করে। নিশ্চিত করে যে আউটপুট Excel ফাইলগুলির পেশাদার চেহারা এবং সম্পূর্ণ কার্যকারিতা রয়েছে।"
    from_alias: "Excel স্প্রেডশিট"
    to_alias: "Excel স্ট্যান্ডার্ড ফরম্যাট"
  LaTeX:
    alias: "LaTeX টেবিল"
    what: "LaTeX একটি পেশাদার ডকুমেন্ট টাইপসেটিং সিস্টেম, বিশেষত একাডেমিক পেপার, প্রযুক্তিগত ডকুমেন্ট এবং বৈজ্ঞানিক প্রকাশনা তৈরির জন্য উপযুক্ত। এর টেবিল কার্যকারিতা শক্তিশালী, জটিল গাণিতিক সূত্র, নির্ভুল লেআউট নিয়ন্ত্রণ এবং উচ্চ-মানের PDF আউটপুট সমর্থন করে। এটি একাডেমিয়া এবং বৈজ্ঞানিক প্রকাশনায় স্ট্যান্ডার্ড টুল, জার্নাল পেপার, গবেষণাপত্র এবং প্রযুক্তিগত ম্যানুয়াল টাইপসেটিংয়ে ব্যাপকভাবে ব্যবহৃত।"
    step1: "LaTeX টেবিল কোড পেস্ট করুন বা .tex ফাইল আপলোড করুন। টুলটি LaTeX টেবিল সিনট্যাক্স পার্স করে এবং ডেটা কন্টেন্ট এক্সট্র্যাক্ট করে, একাধিক টেবিল এনভায়রনমেন্ট (tabular, longtable, array, ইত্যাদি) এবং জটিল ফরম্যাট কমান্ড সমর্থন করে।"
    step3: "একাধিক টেবিল এনভায়রনমেন্ট নির্বাচন, বর্ডার স্টাইল কনফিগারেশন, ক্যাপশন অবস্থান সেটিংস, ডকুমেন্ট ক্লাস স্পেসিফিকেশন এবং প্যাকেজ ম্যানেজমেন্টের সাথে পেশাদার LaTeX টেবিল কোড তৈরি করুন। সম্পূর্ণ কম্পাইলযোগ্য LaTeX ডকুমেন্ট তৈরি করতে পারে, নিশ্চিত করে যে আউটপুট টেবিল একাডেমিক প্রকাশনার মান পূরণ করে।"
    from_alias: "LaTeX ডকুমেন্ট টেবিল"
    to_alias: "LaTeX পেশাদার ফরম্যাট"
  ASCII:
    alias: "ASCII টেবিল"
    what: "ASCII টেবিল সাধারণ টেক্সট অক্ষর ব্যবহার করে টেবিল বর্ডার এবং কাঠামো আঁকে, সেরা সামঞ্জস্য এবং বহনযোগ্যতা প্রদান করে। সব টেক্সট এডিটর, টার্মিনাল এনভায়রনমেন্ট এবং অপারেটিং সিস্টেমের সাথে সামঞ্জস্যপূর্ণ। কোড ডকুমেন্টেশন, প্রযুক্তিগত ম্যানুয়াল, README ফাইল এবং কমান্ড-লাইন টুল আউটপুটে ব্যাপকভাবে ব্যবহৃত। প্রোগ্রামার এবং সিস্টেম অ্যাডমিনিস্ট্রেটরদের পছন্দের ডেটা প্রদর্শন ফরম্যাট।"
    step1: "ASCII টেবিল সম্বলিত টেক্সট ফাইল আপলোড করুন বা সরাসরি টেবিল ডেটা পেস্ট করুন। টুলটি বুদ্ধিমত্তার সাথে ASCII টেবিল কাঠামো চিনতে এবং পার্স করে, একাধিক বর্ডার স্টাইল এবং সারিবদ্ধতা ফরম্যাট সমর্থন করে।"
    step3: "একাধিক বর্ডার স্টাইল (একক লাইন, ডাবল লাইন, গোলাকার কোণ, ইত্যাদি), টেক্সট সারিবদ্ধতা পদ্ধতি এবং অটো কলাম প্রস্থের সাথে সুন্দর সাধারণ টেক্সট ASCII টেবিল তৈরি করুন। তৈরি টেবিল কোড এডিটর, ডকুমেন্ট এবং কমান্ড লাইনে নিখুঁতভাবে প্রদর্শিত হয়।"
    from_alias: "ASCII টেক্সট টেবিল"
    to_alias: "ASCII স্ট্যান্ডার্ড ফরম্যাট"
  MediaWiki:
    alias: "MediaWiki টেবিল"
    what: "MediaWiki হল ওপেন-সোর্স সফটওয়্যার প্ল্যাটফর্ম যা উইকিপিডিয়ার মতো বিখ্যাত উইকি সাইটগুলি ব্যবহার করে। এর টেবিল সিনট্যাক্স সংক্ষিপ্ত অথচ শক্তিশালী, টেবিল স্টাইল কাস্টমাইজেশন, সর্টিং কার্যকারিতা এবং লিঙ্ক এম্বেডিং সমর্থন করে। জ্ঞান ব্যবস্থাপনা, সহযোগিতামূলক সম্পাদনা এবং কন্টেন্ট ম্যানেজমেন্ট সিস্টেমে ব্যাপকভাবে ব্যবহৃত, উইকি এনসাইক্লোপিডিয়া এবং জ্ঞানের ভিত্তি তৈরির জন্য মূল প্রযুক্তি হিসেবে কাজ করে।"
    step1: "MediaWiki টেবিল কোড পেস্ট করুন বা উইকি সোর্স ফাইল আপলোড করুন। টুলটি উইকি মার্কআপ সিনট্যাক্স পার্স করে এবং টেবিল ডেটা এক্সট্র্যাক্ট করে, জটিল উইকি সিনট্যাক্স এবং টেমপ্লেট প্রসেসিং সমর্থন করে।"
    step3: "হেডার স্টাইল সেটিংস, সেল সারিবদ্ধতা, সর্টিং কার্যকারিতা সক্ষমকরণ এবং কোড কম্প্রেশন অপশনের সাথে স্ট্যান্ডার্ড MediaWiki টেবিল কোড তৈরি করুন। তৈরি কোড সরাসরি উইকি পেজ সম্পাদনার জন্য ব্যবহার করা যায়, MediaWiki প্ল্যাটফর্মে নিখুঁত প্রদর্শন নিশ্চিত করে।"
    from_alias: "MediaWiki সোর্স কোড"
    to_alias: "MediaWiki টেবিল সিনট্যাক্স"
  TracWiki:
    alias: "TracWiki টেবিল"
    what: "Trac একটি ওয়েব-ভিত্তিক প্রকল্প ব্যবস্থাপনা এবং বাগ ট্র্যাকিং সিস্টেম যা টেবিল কন্টেন্ট তৈরির জন্য সরলীকৃত উইকি সিনট্যাক্স ব্যবহার করে।"
    step1: "TracWiki ফাইল আপলোড করুন বা টেবিল ডেটা পেস্ট করুন।"
    step3: "সারি/কলাম হেডার সেটিংসের সাথে TracWiki-সামঞ্জস্যপূর্ণ টেবিল কোড তৈরি করুন, প্রকল্প ডকুমেন্ট ব্যবস্থাপনা সহজতর করে।"
    from_alias: "TracWiki টেবিল"
    to_alias: "TracWiki ফরম্যাট"
  AsciiDoc:
    alias: "AsciiDoc টেবিল"
    what: "AsciiDoc একটি হালকা মার্কআপ ভাষা যা HTML, PDF, ম্যানুয়াল পেজ এবং অন্যান্য ফরম্যাটে রূপান্তরিত হতে পারে, প্রযুক্তিগত ডকুমেন্টেশন লেখার জন্য ব্যাপকভাবে ব্যবহৃত।"
    step1: "AsciiDoc ফাইল আপলোড করুন বা ডেটা পেস্ট করুন।"
    step3: "হেডার, ফুটার এবং শিরোনাম সেটিংসের সাথে AsciiDoc টেবিল সিনট্যাক্স তৈরি করুন, AsciiDoc এডিটরে সরাসরি ব্যবহারযোগ্য।"
    from_alias: "AsciiDoc টেবিল"
    to_alias: "AsciiDoc ফরম্যাট"
  reStructuredText:
    alias: "reStructuredText টেবিল"
    what: "reStructuredText Python কমিউনিটির জন্য স্ট্যান্ডার্ড ডকুমেন্টেশন ফরম্যাট, সমৃদ্ধ টেবিল সিনট্যাক্স সমর্থন করে, সাধারণত Sphinx ডকুমেন্টেশন তৈরির জন্য ব্যবহৃত।"
    step1: ".rst ফাইল আপলোড করুন বা reStructuredText ডেটা পেস্ট করুন।"
    step3: "একাধিক বর্ডার স্টাইলের সাথে স্ট্যান্ডার্ড reStructuredText টেবিল তৈরি করুন, Sphinx ডকুমেন্টেশন প্রকল্পে সরাসরি ব্যবহারযোগ্য।"
    from_alias: "reStructuredText টেবিল"
    to_alias: "reStructuredText ফরম্যাট"
  PHP:
    alias: "PHP অ্যারে"
    what: "PHP একটি জনপ্রিয় সার্ভার-সাইড স্ক্রিপ্টিং ভাষা, অ্যারে এর মূল ডেটা কাঠামো, ওয়েব ডেভেলপমেন্ট এবং ডেটা প্রসেসিংয়ে ব্যাপকভাবে ব্যবহৃত।"
    step1: "PHP অ্যারে সম্বলিত ফাইল আপলোড করুন বা সরাসরি ডেটা পেস্ট করুন।"
    step3: "স্ট্যান্ডার্ড PHP অ্যারে কোড তৈরি করুন যা সরাসরি PHP প্রকল্পে ব্যবহার করা যায়, অ্যাসোসিয়েটিভ এবং ইনডেক্সড অ্যারে ফরম্যাট সমর্থন করে।"
    from_alias: "PHP অ্যারে"
    to_alias: "PHP কোড"
  Ruby:
    alias: "Ruby অ্যারে"
    what: "Ruby একটি ডায়নামিক অবজেক্ট-ওরিয়েন্টেড প্রোগ্রামিং ভাষা সংক্ষিপ্ত এবং মার্জিত সিনট্যাক্স সহ, অ্যারে একটি গুরুত্বপূর্ণ ডেটা কাঠামো।"
    step1: "Ruby ফাইল আপলোড করুন বা অ্যারে ডেটা পেস্ট করুন।"
    step3: "Ruby সিনট্যাক্স স্পেসিফিকেশনের সাথে সামঞ্জস্যপূর্ণ Ruby অ্যারে কোড তৈরি করুন, Ruby প্রকল্পে সরাসরি ব্যবহারযোগ্য।"
    from_alias: "Ruby অ্যারে"
    to_alias: "Ruby কোড"
  ASP:
    alias: "ASP অ্যারে"
    what: "ASP (অ্যাক্টিভ সার্ভার পেজ) Microsoft এর সার্ভার-সাইড স্ক্রিপ্টিং এনভায়রনমেন্ট, ডায়নামিক ওয়েব পেজ ডেভেলপ করার জন্য একাধিক প্রোগ্রামিং ভাষা সমর্থন করে।"
    step1: "ASP ফাইল আপলোড করুন বা অ্যারে ডেটা পেস্ট করুন।"
    step3: "VBScript এবং JScript সিনট্যাক্সের সাথে ASP-সামঞ্জস্যপূর্ণ অ্যারে কোড তৈরি করুন, ASP.NET প্রকল্পে ব্যবহারযোগ্য।"
    from_alias: "ASP অ্যারে"
    to_alias: "ASP কোড"
  ActionScript:
    alias: "ActionScript অ্যারে"
    what: "ActionScript একটি অবজেক্ট-ওরিয়েন্টেড প্রোগ্রামিং ভাষা প্রাথমিকভাবে Adobe Flash এবং AIR অ্যাপ্লিকেশন ডেভেলপমেন্টের জন্য ব্যবহৃত।"
    step1: ".as ফাইল আপলোড করুন বা ActionScript ডেটা পেস্ট করুন।"
    step3: "AS3 সিনট্যাক্স স্ট্যান্ডার্ডের সাথে সামঞ্জস্যপূর্ণ ActionScript অ্যারে কোড তৈরি করুন, Flash এবং Flex প্রকল্প ডেভেলপমেন্টের জন্য ব্যবহারযোগ্য।"
    from_alias: "ActionScript অ্যারে"
    to_alias: "ActionScript কোড"
  BBCode:
    alias: "BBCode টেবিল"
    what: "BBCode একটি হালকা মার্কআপ ভাষা সাধারণত ফোরাম এবং অনলাইন কমিউনিটিতে ব্যবহৃত, টেবিল সমর্থন সহ সরল ফরম্যাটিং কার্যকারিতা প্রদান করে।"
    step1: "BBCode সম্বলিত ফাইল আপলোড করুন বা ডেটা পেস্ট করুন।"
    step3: "ফোরাম পোস্টিং এবং কমিউনিটি কন্টেন্ট তৈরির জন্য উপযুক্ত BBCode টেবিল কোড তৈরি করুন, সংকুচিত আউটপুট ফরম্যাটের সাথে।"
    from_alias: "BBCode টেবিল"
    to_alias: "BBCode ফরম্যাট"
  PDF:
    alias: "PDF টেবিল"
    what: "PDF (পোর্টেবল ডকুমেন্ট ফরম্যাট) একটি ক্রস-প্ল্যাটফর্ম ডকুমেন্ট স্ট্যান্ডার্ড স্থির লেআউট, সামঞ্জস্যপূর্ণ প্রদর্শন এবং উচ্চ-মানের মুদ্রণ বৈশিষ্ট্য সহ। আনুষ্ঠানিক ডকুমেন্ট, রিপোর্ট, চালান, চুক্তি এবং একাডেমিক পেপারে ব্যাপকভাবে ব্যবহৃত। ব্যবসায়িক যোগাযোগ এবং ডকুমেন্ট আর্কাইভিংয়ের জন্য পছন্দের ফরম্যাট, বিভিন্ন ডিভাইস এবং অপারেটিং সিস্টেম জুড়ে সম্পূর্ণভাবে সামঞ্জস্যপূর্ণ ভিজ্যুয়াল প্রভাব নিশ্চিত করে।"
    step1: "যেকোনো ফরম্যাটে টেবিল ডেটা ইম্পোর্ট করুন। টুলটি স্বয়ংক্রিয়ভাবে ডেটা কাঠামো বিশ্লেষণ করে এবং বুদ্ধিমান লেআউট ডিজাইন সম্পাদন করে, বড় টেবিল অটো-পেজিনেশন এবং জটিল ডেটা টাইপ প্রসেসিং সমর্থন করে।"
    step3: "একাধিক পেশাদার থিম স্টাইল (ব্যবসায়িক, একাডেমিক, মিনিমালিস্ট, ইত্যাদি), বহুভাষিক ফন্ট, অটো-পেজিনেশন, ওয়াটারমার্ক যোগ এবং প্রিন্ট অপ্টিমাইজেশনের সাথে উচ্চ-মানের PDF টেবিল ফাইল তৈরি করুন। নিশ্চিত করে যে আউটপুট PDF ডকুমেন্টগুলির পেশাদার চেহারা রয়েছে, ব্যবসায়িক উপস্থাপনা এবং আনুষ্ঠানিক প্রকাশনার জন্য সরাসরি ব্যবহারযোগ্য।"
    from_alias: "টেবিল ডেটা"
    to_alias: "PDF পেশাদার ডকুমেন্ট"
  JPEG:
    alias: "JPEG ছবি"
    what: "JPEG সবচেয়ে ব্যাপকভাবে ব্যবহৃত ডিজিটাল ইমেজ ফরম্যাট চমৎকার কম্প্রেশন প্রভাব এবং ব্যাপক সামঞ্জস্য সহ। এর ছোট ফাইল সাইজ এবং দ্রুত লোডিং গতি এটিকে ওয়েব প্রদর্শন, সোশ্যাল মিডিয়া শেয়ারিং, ডকুমেন্ট ইলাস্ট্রেশন এবং অনলাইন উপস্থাপনার জন্য উপযুক্ত করে তোলে। ডিজিটাল মিডিয়া এবং নেটওয়ার্ক যোগাযোগের জন্য স্ট্যান্ডার্ড ইমেজ ফরম্যাট, প্রায় সব ডিভাইস এবং সফটওয়্যার দ্বারা নিখুঁতভাবে সমর্থিত।"
    step1: "যেকোনো ফরম্যাটে টেবিল ডেটা ইম্পোর্ট করুন। টুলটি বুদ্ধিমান লেআউট ডিজাইন এবং ভিজ্যুয়াল অপ্টিমাইজেশন সম্পাদন করে, স্বয়ংক্রিয়ভাবে সর্বোত্তম আকার এবং রেজোলিউশন গণনা করে।"
    step3: "একাধিক থিম রঙের স্কিম (হালকা, অন্ধকার, চোখ-বান্ধব, ইত্যাদি), অভিযোজিত লেআউট, টেক্সট স্পষ্টতা অপ্টিমাইজেশন এবং আকার কাস্টমাইজেশনের সাথে উচ্চ-সংজ্ঞা JPEG টেবিল ছবি তৈরি করুন। অনলাইন শেয়ারিং, ডকুমেন্ট সন্নিবেশ এবং উপস্থাপনা ব্যবহারের জন্য উপযুক্ত, বিভিন্ন প্রদর্শন ডিভাইসে চমৎকার ভিজ্যুয়াল প্রভাব নিশ্চিত করে।"
    from_alias: "টেবিল ডেটা"
    to_alias: "JPEG উচ্চ-সংজ্ঞা ছবি"
  Jira:
    alias: "Jira টেবিল"
    what: "JIRA হল Atlassian দ্বারা উন্নত পেশাদার প্রকল্প ব্যবস্থাপনা এবং বাগ ট্র্যাকিং সফটওয়্যার, চটপটে উন্নয়ন, সফটওয়্যার পরীক্ষা এবং প্রকল্প সহযোগিতায় ব্যাপকভাবে ব্যবহৃত। এর টেবিল কার্যকারিতা সমৃদ্ধ ফরম্যাটিং অপশন এবং ডেটা প্রদর্শন সমর্থন করে, প্রয়োজনীয়তা ব্যবস্থাপনা, বাগ ট্র্যাকিং এবং অগ্রগতি রিপোর্টিংয়ে সফটওয়্যার ডেভেলপমেন্ট টিম, প্রকল্প ব্যবস্থাপক এবং গুণমান নিশ্চিতকরণ কর্মীদের জন্য একটি গুরুত্বপূর্ণ টুল হিসেবে কাজ করে।"
    step1: "টেবিল ডেটা সম্বলিত ফাইল আপলোড করুন বা সরাসরি ডেটা কন্টেন্ট পেস্ট করুন। টুলটি স্বয়ংক্রিয়ভাবে টেবিল ডেটা এবং বিশেষ অক্ষর এস্কেপিং প্রসেস করে।"
    step3: "হেডার স্টাইল সেটিংস, সেল সারিবদ্ধতা, অক্ষর এস্কেপ প্রসেসিং এবং ফরম্যাট অপ্টিমাইজেশনের সাথে JIRA প্ল্যাটফর্ম-সামঞ্জস্যপূর্ণ টেবিল কোড তৈরি করুন। তৈরি কোড সরাসরি JIRA ইস্যু বর্ণনা, মন্তব্য বা উইকি পেজে পেস্ট করা যায়, JIRA সিস্টেমে সঠিক প্রদর্শন এবং রেন্ডারিং নিশ্চিত করে।"
    from_alias: "প্রকল্প ডেটা"
    to_alias: "Jira টেবিল সিনট্যাক্স"
  Textile:
    alias: "Textile টেবিল"
    what: "Textile একটি সংক্ষিপ্ত হালকা মার্কআপ ভাষা সরল এবং সহজ-শেখার সিনট্যাক্স সহ, কন্টেন্ট ম্যানেজমেন্ট সিস্টেম, ব্লগ প্ল্যাটফর্ম এবং ফোরাম সিস্টেমে ব্যাপকভাবে ব্যবহৃত। এর টেবিল সিনট্যাক্স স্পষ্ট এবং স্বজ্ঞাত, দ্রুত ফরম্যাটিং এবং স্টাইল সেটিংস সমর্থন করে। দ্রুত ডকুমেন্ট লেখা এবং কন্টেন্ট প্রকাশনার জন্য কন্টেন্ট নির্মাতা এবং ওয়েবসাইট প্রশাসকদের জন্য একটি আদর্শ টুল।"
    step1: "Textile ফরম্যাট ফাইল আপলোড করুন বা টেবিল ডেটা পেস্ট করুন। টুলটি Textile মার্কআপ সিনট্যাক্স পার্স করে এবং টেবিল কন্টেন্ট এক্সট্র্যাক্ট করে।"
    step3: "হেডার মার্কআপ, সেল সারিবদ্ধতা, বিশেষ অক্ষর এস্কেপিং এবং ফরম্যাট অপ্টিমাইজেশনের সাথে স্ট্যান্ডার্ড Textile টেবিল সিনট্যাক্স তৈরি করুন। তৈরি কোড সরাসরি CMS সিস্টেম, ব্লগ প্ল্যাটফর্ম এবং Textile সমর্থনকারী ডকুমেন্ট সিস্টেমে ব্যবহার করা যায়, সঠিক কন্টেন্ট রেন্ডারিং এবং প্রদর্শন নিশ্চিত করে।"
    from_alias: "Textile ডকুমেন্ট"
    to_alias: "Textile টেবিল সিনট্যাক্স"
  PNG:
    alias: "PNG ছবি"
    what: "PNG (পোর্টেবল নেটওয়ার্ক গ্রাফিক্স) একটি ক্ষতিহীন ইমেজ ফরম্যাট চমৎকার কম্প্রেশন এবং স্বচ্ছতা সমর্থন সহ। ওয়েব ডিজাইন, ডিজিটাল গ্রাফিক্স এবং পেশাদার ফটোগ্রাফিতে ব্যাপকভাবে ব্যবহৃত। এর উচ্চ মান এবং ব্যাপক সামঞ্জস্য এটিকে স্ক্রিনশট, লোগো, ডায়াগ্রাম এবং তীক্ষ্ণ বিবরণ এবং স্বচ্ছ পটভূমি প্রয়োজন এমন যেকোনো ছবির জন্য আদর্শ করে তোলে।"
    step1: "যেকোনো ফরম্যাটে টেবিল ডেটা ইম্পোর্ট করুন। টুলটি বুদ্ধিমান লেআউট ডিজাইন এবং ভিজ্যুয়াল অপ্টিমাইজেশন সম্পাদন করে, PNG আউটপুটের জন্য স্বয়ংক্রিয়ভাবে সর্বোত্তম আকার এবং রেজোলিউশন গণনা করে।"
    step3: "একাধিক থিম রঙের স্কিম, স্বচ্ছ পটভূমি, অভিযোজিত লেআউট এবং টেক্সট স্পষ্টতা অপ্টিমাইজেশনের সাথে উচ্চ-মানের PNG টেবিল ছবি তৈরি করুন। ওয়েব ব্যবহার, ডকুমেন্ট সন্নিবেশ এবং চমৎকার ভিজ্যুয়াল মানের সাথে পেশাদার উপস্থাপনার জন্য নিখুঁত।"
    from_alias: "টেবিল ডেটা"
    to_alias: "PNG উচ্চ-মানের ছবি"
  TOML:
    alias: "TOML কনফিগারেশন"
    what: "TOML (Tom's Obvious, Minimal Language) একটি কনফিগারেশন ফাইল ফরম্যাট যা পড়তে এবং লিখতে সহজ। দ্ব্যর্থহীন এবং সরল হওয়ার জন্য ডিজাইন করা, এটি কনফিগারেশন ব্যবস্থাপনার জন্য আধুনিক সফটওয়্যার প্রকল্পে ব্যাপকভাবে ব্যবহৃত। এর স্পষ্ট সিনট্যাক্স এবং শক্তিশালী টাইপিং এটিকে অ্যাপ্লিকেশন সেটিংস এবং প্রকল্প কনফিগারেশন ফাইলের জন্য একটি চমৎকার পছন্দ করে তোলে।"
    step1: "TOML ফাইল আপলোড করুন বা কনফিগারেশন ডেটা পেস্ট করুন। টুলটি TOML সিনট্যাক্স পার্স করে এবং কাঠামোগত কনফিগারেশন তথ্য এক্সট্র্যাক্ট করে।"
    step3: "নেস্টেড কাঠামো, ডেটা টাইপ এবং মন্তব্যের সাথে স্ট্যান্ডার্ড TOML ফরম্যাট তৈরি করুন। তৈরি TOML ফাইলগুলি অ্যাপ্লিকেশন কনফিগারেশন, বিল্ড টুল এবং প্রকল্প সেটিংসের জন্য নিখুঁত।"
    from_alias: "TOML কনফিগারেশন"
    to_alias: "TOML ফরম্যাট"
  INI:
    alias: "INI কনফিগারেশন"
    what: "INI ফাইলগুলি সরল কনফিগারেশন ফাইল যা অনেক অ্যাপ্লিকেশন এবং অপারেটিং সিস্টেম দ্বারা ব্যবহৃত। তাদের সরাসরি কী-ভ্যালু পেয়ার কাঠামো তাদের ম্যানুয়ালি পড়তে এবং সম্পাদনা করতে সহজ করে তোলে। Windows অ্যাপ্লিকেশন, লিগেসি সিস্টেম এবং সরল কনফিগারেশন পরিস্থিতিতে ব্যাপকভাবে ব্যবহৃত যেখানে মানুষের পাঠযোগ্যতা গুরুত্বপূর্ণ।"
    step1: "INI ফাইল আপলোড করুন বা কনফিগারেশন ডেটা পেস্ট করুন। টুলটি INI সিনট্যাক্স পার্স করে এবং বিভাগ-ভিত্তিক কনফিগারেশন তথ্য এক্সট্র্যাক্ট করে।"
    step3: "বিভাগ, মন্তব্য এবং বিভিন্ন ডেটা টাইপের সাথে স্ট্যান্ডার্ড INI ফরম্যাট তৈরি করুন। তৈরি INI ফাইলগুলি বেশিরভাগ অ্যাপ্লিকেশন এবং কনফিগারেশন সিস্টেমের সাথে সামঞ্জস্যপূর্ণ।"
    from_alias: "INI কনফিগারেশন"
    to_alias: "INI ফরম্যাট"
  Avro:
    alias: "Avro স্কিমা"
    what: "Apache Avro একটি ডেটা সিরিয়ালাইজেশন সিস্টেম যা সমৃদ্ধ ডেটা কাঠামো, কমপ্যাক্ট বাইনারি ফরম্যাট এবং স্কিমা বিবর্তন ক্ষমতা প্রদান করে। বিগ ডেটা প্রসেসিং, মেসেজ কিউ এবং বিতরণ করা সিস্টেমে ব্যাপকভাবে ব্যবহৃত। এর স্কিমা সংজ্ঞা জটিল ডেটা টাইপ এবং সংস্করণ সামঞ্জস্য সমর্থন করে, এটিকে ডেটা ইঞ্জিনিয়ার এবং সিস্টেম আর্কিটেক্টদের জন্য একটি গুরুত্বপূর্ণ টুল করে তোলে।"
    step1: "Avro স্কিমা ফাইল আপলোড করুন বা ডেটা পেস্ট করুন। টুলটি Avro স্কিমা সংজ্ঞা পার্স করে এবং টেবিল কাঠামো তথ্য এক্সট্র্যাক্ট করে।"
    step3: "ডেটা টাইপ ম্যাপিং, ফিল্ড সীমাবদ্ধতা এবং স্কিমা যাচাইকরণের সাথে স্ট্যান্ডার্ড Avro স্কিমা সংজ্ঞা তৈরি করুন। তৈরি স্কিমাগুলি সরাসরি Hadoop ইকোসিস্টেম, Kafka মেসেজ সিস্টেম এবং অন্যান্য বিগ ডেটা প্ল্যাটফর্মে ব্যবহার করা যায়।"
    from_alias: "Avro স্কিমা"
    to_alias: "Avro ডেটা ফরম্যাট"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) হল Google এর ভাষা-নিরপেক্ষ, প্ল্যাটফর্ম-নিরপেক্ষ, কাঠামোগত ডেটা সিরিয়ালাইজ করার জন্য এক্সটেনসিবল মেকানিজম। মাইক্রোসার্ভিস, API ডেভেলপমেন্ট এবং ডেটা স্টোরেজে ব্যাপকভাবে ব্যবহৃত। এর দক্ষ বাইনারি ফরম্যাট এবং শক্তিশালী টাইপিং এটিকে উচ্চ-কর্মক্ষমতা অ্যাপ্লিকেশন এবং ক্রস-ল্যাঙ্গুয়েজ যোগাযোগের জন্য আদর্শ করে তোলে।"
    step1: ".proto ফাইল আপলোড করুন বা Protocol Buffer সংজ্ঞা পেস্ট করুন। টুলটি protobuf সিনট্যাক্স পার্স করে এবং মেসেজ কাঠামো তথ্য এক্সট্র্যাক্ট করে।"
    step3: "মেসেজ টাইপ, ফিল্ড অপশন এবং সার্ভিস সংজ্ঞার সাথে স্ট্যান্ডার্ড Protocol Buffer সংজ্ঞা তৈরি করুন। তৈরি .proto ফাইলগুলি একাধিক প্রোগ্রামিং ভাষার জন্য কম্পাইল করা যায়।"
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf স্কিমা"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas হল Python এর সবচেয়ে জনপ্রিয় ডেটা বিশ্লেষণ লাইব্রেরি, DataFrame এর মূল ডেটা কাঠামো। এটি শক্তিশালী ডেটা ম্যানিপুলেশন, পরিষ্কারকরণ এবং বিশ্লেষণ ক্ষমতা প্রদান করে, ডেটা সায়েন্স, মেশিন লার্নিং এবং বিজনেস ইন্টেলিজেন্সে ব্যাপকভাবে ব্যবহৃত। Python ডেভেলপার এবং ডেটা বিশ্লেষকদের জন্য একটি অপরিহার্য টুল।"
    step1: "DataFrame কোড সম্বলিত Python ফাইল আপলোড করুন বা ডেটা পেস্ট করুন। টুলটি Pandas সিনট্যাক্স পার্স করে এবং DataFrame কাঠামো তথ্য এক্সট্র্যাক্ট করে।"
    step3: "ডেটা টাইপ স্পেসিফিকেশন, ইনডেক্স সেটিংস এবং ডেটা অপারেশনের সাথে স্ট্যান্ডার্ড Pandas DataFrame কোড তৈরি করুন। তৈরি কোড ডেটা বিশ্লেষণ এবং প্রসেসিংয়ের জন্য Python এনভায়রনমেন্টে সরাসরি এক্সিকিউট করা যায়।"
    from_alias: "Pandas DataFrame"
    to_alias: "Python ডেটা কাঠামো"
  RDF:
    alias: "RDF ট্রিপল"
    what: "RDF (রিসোর্স ডেসক্রিপশন ফ্রেমওয়ার্ক) ওয়েবে ডেটা ইন্টারচেঞ্জের জন্য একটি স্ট্যান্ডার্ড মডেল, গ্রাফ আকারে রিসোর্স সম্পর্কে তথ্য উপস্থাপন করার জন্য ডিজাইন করা। সিমান্টিক ওয়েব, নলেজ গ্রাফ এবং লিঙ্কড ডেটা অ্যাপ্লিকেশনে ব্যাপকভাবে ব্যবহৃত। এর ট্রিপল কাঠামো সমৃদ্ধ মেটাডেটা উপস্থাপনা এবং সিমান্টিক সম্পর্ক সক্ষম করে।"
    step1: "RDF ফাইল আপলোড করুন বা ট্রিপল ডেটা পেস্ট করুন। টুলটি RDF সিনট্যাক্স পার্স করে এবং সিমান্টিক সম্পর্ক এবং রিসোর্স তথ্য এক্সট্র্যাক্ট করে।"
    step3: "বিভিন্ন সিরিয়ালাইজেশনের সাথে স্ট্যান্ডার্ড RDF ফরম্যাট তৈরি করুন (RDF/XML, Turtle, N-Triples)। তৈরি RDF সিমান্টিক ওয়েব অ্যাপ্লিকেশন, নলেজ বেস এবং লিঙ্কড ডেটা সিস্টেমে ব্যবহার করা যায়।"
    from_alias: "RDF ডেটা"
    to_alias: "RDF সিমান্টিক ফরম্যাট"
  MATLAB:
    alias: "MATLAB অ্যারে"
    what: "MATLAB একটি উচ্চ-কর্মক্ষমতা সংখ্যাগত কম্পিউটিং এবং ভিজুয়ালাইজেশন সফটওয়্যার ইঞ্জিনিয়ারিং কম্পিউটিং, ডেটা বিশ্লেষণ এবং অ্যালগরিদম ডেভেলপমেন্টে ব্যাপকভাবে ব্যবহৃত। এর অ্যারে এবং ম্যাট্রিক্স অপারেশন শক্তিশালী, জটিল গাণিতিক গণনা এবং ডেটা প্রসেসিং সমর্থন করে। ইঞ্জিনিয়ার, গবেষক এবং ডেটা বিজ্ঞানীদের জন্য একটি অপরিহার্য টুল।"
    step1: "MATLAB .m ফাইল আপলোড করুন বা অ্যারে ডেটা পেস্ট করুন। টুলটি MATLAB সিনট্যাক্স পার্স করে এবং অ্যারে কাঠামো তথ্য এক্সট্র্যাক্ট করে।"
    step3: "বহুমাত্রিক অ্যারে, ডেটা টাইপ স্পেসিফিকেশন এবং ভেরিয়েবল নামকরণের সাথে স্ট্যান্ডার্ড MATLAB অ্যারে কোড তৈরি করুন। তৈরি কোড ডেটা বিশ্লেষণ এবং বৈজ্ঞানিক কম্পিউটিংয়ের জন্য MATLAB এনভায়রনমেন্টে সরাসরি এক্সিকিউট করা যায়।"
    from_alias: "MATLAB অ্যারে"
    to_alias: "MATLAB কোড ফরম্যাট"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame হল R প্রোগ্রামিং ভাষার মূল ডেটা কাঠামো, যা পরিসংখ্যানগত বিশ্লেষণ, ডেটা মাইনিং এবং মেশিন লার্নিংয়ে ব্যাপকভাবে ব্যবহৃত হয়। R হল পরিসংখ্যানগত কম্পিউটিং এবং গ্রাফিক্সের জন্য প্রধান টুল, যেখানে DataFrame শক্তিশালী ডেটা ম্যানিপুলেশন, পরিসংখ্যানগত বিশ্লেষণ এবং ভিজুয়ালাইজেশন ক্ষমতা প্রদান করে। কাঠামোগত ডেটা বিশ্লেষণের সাথে কাজ করা ডেটা বিজ্ঞানী, পরিসংখ্যানবিদ এবং গবেষকদের জন্য অপরিহার্য।"
    step1: "R ডেটা ফাইল আপলোড করুন বা DataFrame কোড পেস্ট করুন। টুলটি R সিনট্যাক্স পার্স করে এবং কলাম টাইপ, সারির নাম এবং ডেটা বিষয়বস্তু সহ DataFrame কাঠামো তথ্য এক্সট্র্যাক্ট করে।"
    step3: "ডেটা টাইপ স্পেসিফিকেশন, ফ্যাক্টর লেভেল, সারি/কলাম নাম এবং R-নির্দিষ্ট ডেটা কাঠামোর সাথে স্ট্যান্ডার্ড R DataFrame কোড তৈরি করুন। তৈরি কোড পরিসংখ্যানগত বিশ্লেষণ এবং ডেটা প্রসেসিংয়ের জন্য R এনভায়রনমেন্টে সরাসরি এক্সিকিউট করা যায়।"
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "রূপান্তর শুরু করুন"
  start_generating: "তৈরি শুরু করুন"
  api_docs: "এপিআই ডকুমেন্টেশন"
related:
  section_title: 'আরও {{ if and .from (ne .from "generator") }}{{ .from }} এবং {{ end }}{{ .to }} কনভার্টার'
  section_description: '{{ if and .from (ne .from "generator") }}{{ .from }} এবং {{ end }}{{ .to }} ফরম্যাটের জন্য আরও কনভার্টার অন্বেষণ করুন। আমাদের পেশাদার অনলাইন রূপান্তর টুল দিয়ে একাধিক ফরম্যাটের মধ্যে আপনার ডেটা রূপান্তর করুন।'
  title: "{{ .from }} থেকে {{ .to }}"
howto:
  step2: "পেশাদার বৈশিষ্ট্য সহ আমাদের উন্নত অনলাইন টেবিল এডিটর ব্যবহার করে ডেটা সম্পাদনা করুন। খালি সারি মুছে ফেলা, ডুপ্লিকেট অপসারণ, ডেটা ট্রান্সপোজিশন, সর্টিং, রেজেক্স খুঁজে এবং প্রতিস্থাপন এবং রিয়েল-টাইম প্রিভিউ সমর্থন করে। সমস্ত পরিবর্তন স্বয়ংক্রিয়ভাবে %s ফরম্যাটে নির্ভুল, নির্ভরযোগ্য ফলাফল সহ রূপান্তরিত হয়।"
  section_title: "{{ . }} কীভাবে ব্যবহার করবেন"
  converter_description: "আমাদের ধাপে ধাপে গাইড দিয়ে {{ .from }} থেকে {{ .to }} রূপান্তর করতে শিখুন। উন্নত বৈশিষ্ট্য এবং রিয়েল-টাইম প্রিভিউ সহ পেশাদার অনলাইন কনভার্টার।"
  generator_description: "আমাদের অনলাইন জেনারেটর দিয়ে পেশাদার {{ .to }} টেবিল তৈরি করতে শিখুন। Excel-এর মতো সম্পাদনা, রিয়েল-টাইম প্রিভিউ এবং তাৎক্ষণিক এক্সপোর্ট ক্ষমতা।"
extension:
  section_title: "টেবিল সনাক্তকরণ এবং এক্সট্র্যাকশন এক্সটেনশন"
  section_description: "এক ক্লিকে যেকোনো ওয়েবসাইট থেকে টেবিল এক্সট্র্যাক্ট করুন। Excel, CSV, JSON সহ ৩০+ ফরম্যাটে তাৎক্ষণিক রূপান্তর - কপি-পেস্টের প্রয়োজন নেই।"
  features:
    extraction_title: "এক-ক্লিক টেবিল এক্সট্র্যাকশন"
    extraction_description: "কপি-পেস্ট ছাড়াই যেকোনো ওয়েবপেজ থেকে তাৎক্ষণিক টেবিল এক্সট্র্যাক্ট করুন - পেশাদার ডেটা এক্সট্র্যাকশন সহজ করা হয়েছে"
    formats_title: "৩০+ ফরম্যাট কনভার্টার সাপোর্ট"
    formats_description: "আমাদের উন্নত টেবিল কনভার্টার দিয়ে এক্সট্র্যাক্ট করা টেবিলগুলি Excel, CSV, JSON, Markdown, SQL এবং আরও অনেক কিছুতে রূপান্তর করুন"
    detection_title: "স্মার্ট টেবিল সনাক্তকরণ"
    detection_description: "দ্রুত ডেটা এক্সট্র্যাকশন এবং রূপান্তরের জন্য যেকোনো ওয়েবপেজে স্বয়ংক্রিয়ভাবে টেবিল সনাক্ত এবং হাইলাইট করে"
  hover_tip: "✨ এক্সট্র্যাকশন আইকন দেখতে যেকোনো টেবিলের উপর হোভার করুন"
recommendations:
  section_title: "বিশ্ববিদ্যালয় এবং পেশাদারদের দ্বারা সুপারিশকৃত"
  section_description: "নির্ভরযোগ্য টেবিল রূপান্তর এবং ডেটা প্রক্রিয়াকরণের জন্য বিশ্ববিদ্যালয়, গবেষণা প্রতিষ্ঠান এবং ডেভেলপমেন্ট টিমের পেশাদারদের দ্বারা TableConvert বিশ্বস্ত।"
  cards:
    university_title: "ইউনিভার্সিটি অফ উইসকনসিন-ম্যাডিসন"
    university_description: "TableConvert.com - পেশাদার বিনামূল্যে অনলাইন টেবিল কনভার্টার এবং ডেটা ফরম্যাট টুল"
    university_link: "নিবন্ধ পড়ুন"
    facebook_title: "ডেটা পেশাদার কমিউনিটি"
    facebook_description: "Facebook ডেভেলপার গ্রুপে ডেটা বিশ্লেষক এবং পেশাদারদের দ্বারা শেয়ার এবং সুপারিশকৃত"
    facebook_link: "পোস্ট দেখুন"
    twitter_title: "ডেভেলপার কমিউনিটি"
    twitter_description: "টেবিল রূপান্তরের জন্য X (Twitter) এ @xiaoying_eth এবং অন্যান্য ডেভেলপারদের দ্বারা সুপারিশকৃত"
    twitter_link: "টুইট দেখুন"
faq:
  section_title: "প্রায়শই জিজ্ঞাসিত প্রশ্ন"
  section_description: "আমাদের বিনামূল্যে অনলাইন টেবিল কনভার্টার, ডেটা ফরম্যাট এবং রূপান্তর প্রক্রিয়া সম্পর্কে সাধারণ প্রশ্ন."
  what: "%s ফরম্যাট কী?"
  howto_convert:
    question: "{{ . }} বিনামূল্যে কীভাবে ব্যবহার করবেন?"
    answer: "আমাদের বিনামূল্যে অনলাইন টেবিল কনভার্টার ব্যবহার করে আপনার {{ .from }} ফাইল আপলোড করুন, ডেটা পেস্ট করুন বা ওয়েব পেজ থেকে এক্সট্র্যাক্ট করুন। আমাদের পেশাদার কনভার্টার টুল রিয়েল-টাইম প্রিভিউ এবং উন্নত সম্পাদনা বৈশিষ্ট্য সহ তাৎক্ষণিকভাবে আপনার ডেটাকে {{ .to }} ফরম্যাটে রূপান্তরিত করে। রূপান্তরিত ফলাফল তাৎক্ষণিক ডাউনলোড বা কপি করুন।"
  security:
    question: "এই অনলাইন কনভার্টার ব্যবহার করার সময় আমার ডেটা কি নিরাপদ?"
    answer: "একদম! সমস্ত টেবিল রূপান্তর স্থানীয়ভাবে আপনার ব্রাউজারে ঘটে - আপনার ডেটা কখনও আপনার ডিভাইস ছেড়ে যায় না। আমাদের অনলাইন কনভার্টার সবকিছু ক্লায়েন্ট-সাইডে প্রক্রিয়া করে, সম্পূর্ণ গোপনীয়তা এবং ডেটা নিরাপত্তা নিশ্চিত করে। আমাদের সার্ভারে কোনো ফাইল সংরক্ষণ করা হয় না।"
  free:
    question: "TableConvert কি সত্যিই বিনামূল্যে ব্যবহার করা যায়?"
    answer: "হ্যাঁ, TableConvert সম্পূর্ণ বিনামূল্যে! সমস্ত কনভার্টার বৈশিষ্ট্য, টেবিল এডিটর, ডেটা জেনারেটর টুল এবং এক্সপোর্ট অপশন কোনো খরচ, নিবন্ধন বা লুকানো ফি ছাড়াই উপলব্ধ। বিনামূল্যে অনলাইনে সীমাহীন ফাইল রূপান্তর করুন।"
  filesize:
    question: "অনলাইন কনভার্টারের ফাইল সাইজ সীমা কত?"
    answer: "আমাদের বিনামূল্যে অনলাইন টেবিল কনভার্টার ১০MB পর্যন্ত ফাইল সমর্থন করে। বড় ফাইল, ব্যাচ প্রক্রিয়াকরণ বা এন্টারপ্রাইজ প্রয়োজনের জন্য, উচ্চ সীমা সহ আমাদের ব্রাউজার এক্সটেনশন বা পেশাদার API সেবা ব্যবহার করুন।"
stats:
  conversions: "রূপান্তরিত টেবিল"
  tables: "তৈরি টেবিল"
  formats: "ডেটা ফাইল ফরম্যাট"
  rating: "ব্যবহারকারী রেটিং"
