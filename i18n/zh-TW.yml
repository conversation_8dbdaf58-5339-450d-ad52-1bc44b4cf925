site:
  fullname: "表格轉換工具"
  name: "TableConvert"
  subtitle: "免費線上表格轉換器和生成器"
  intro: "TableConvert 是一個免費的線上表格轉換和資料生成工具，支援 Excel、CSV、JSON、Markdown、LaTeX、SQL 等 30 多種格式之間的轉換。"
  followTwitter: "在 X 上關注我們"
title:
  converter: "%s 轉 %s"
  generator: "%s 生成器"
post:
  tags:
    converter: "轉換器"
    editor: "編輯器"
    generator: "生成器"
    maker: "構建器"
  converter:
    title: "在線 %s 轉 %s"
    short: "免費強大的 %s 轉 %s 在線工具"
    intro: "易於使用的線上 %s 轉 %s 轉換器。使用我們直觀的轉換工具輕鬆轉換表格資料。快速、可靠且使用者友善。"
  generator:
    title: "線上 %s 編輯器和生成器"
    short: "功能全麵的專業 %s 在線生成工具"
    intro: "易於使用的線上 %s 生成器和表格編輯器。使用我們直觀的工具和即時預覽輕鬆建立專業的資料表格。"
navbar:
  search:
    placeholder: "蒐索轉換器..."
  sponsor: "請我喝咖啡"
  extension: "擴充功能"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "數據源"
    placeholder: "粘貼您的 %s 數據或拖拽 %s 文件到此處"
    example: "示例"
    upload: "上傳文件"
    extract:
      enter: "從網頁提取"
      intro: "輸入包含表格數據的網頁 URL，自動提取結構化數據"
      btn: "提取 %s"
    excel:
      sheet: "工作表"
      none: "無"
  tableEditor:
    title: "在線表格編輯器"
    undo: "撤銷"
    redo: "重做"
    transpose: "轉置"
    clear: "清空"
    deleteBlank: "刪除空行"
    deleteDuplicate: "去重"
    uppercase: "轉大冩"
    lowercase: "轉小冩"
    capitalize: "首字母大冩"
    replace:
      replace: "查找替換（支持正則表達式）"
      subst: "替換爲..."
      btn: "全部替換"
  tableGenerator:
    title: "表格生成器"
    sponsor: "請我喝咖啡"
    copy: "複製到剪貼闆"
    download: "下載文件"
    tooltip:
      html:
        escape: "轉義 HTML 特殊字符（&、<、>、\"、'）以防止顯示錯誤"
        div: "使用 DIV+CSS 佈局代替傳統 TABLE 標籤，更適合響應式設計"
        minify: "移除空白和換行符，生成壓縮的 HTML 代碼"
        thead: "生成標準的表頭（&lt;thead&gt;）和表體（&lt;tbody&gt;）結構"
        tableCaption: "在表格上方添加描述性標題（&lt;caption&gt; 元素）"
        tableClass: "爲表格添加 CSS 類名，便於樣式定製"
        tableId: "爲表格設置唯一 ID 標識符，便於 JavaScript 操作"
      jira:
        escape: "轉義管道字符（|）以避免與 Jira 表格語法衝突"
      json:
        parsingJSON: "智能解析單元格中的 JSON 字符串爲對象"
        minify: "生成緊湊的單行 JSON 格式以減小文件大小"
        format: "選擇輸出 JSON 數據結構：對象數組、二維數組等"
      latex:
        escape: "轉義 LaTeX 特殊字符（%、&、_、#、$ 等）以確保正確編譯"
        ht: "添加浮動位置參數 [!ht] 控製表格在頁麵中的位置"
        mwe: "生成完整的 LaTeX 文檔"
        tableAlign: "設置表格在頁麵中的水平對齊方式"
        tableBorder: "配置表格邊框樣式：無邊框、部分邊框、完整邊框"
        label: "設置表格標籤用於 \\ref{} 命令交叉引用"
        caption: "設置表格標題顯示在表格上方或下方"
        location: "選擇表格標題顯示位置：上方或下方"
        tableType: "選擇表格環境類型：tabular、longtable、array 等"
      markdown:
        escape: "轉義 Markdown 特殊字符（*、_、|、\\ 等）以避免格式衝突"
        pretty: "自動對齊列寬，生成更美觀的表格格式"
        simple: "使用簡化語法，省略外邊框豎線"
        boldFirstRow: "將第一行文本設爲粗體"
        boldFirstColumn: "將第一列文本設爲粗體"
        firstHeader: "將第一行作爲表頭並添加分隔線"
        textAlign: "設置列文本對齊方式：左對齊、居中、右對齊"
        multilineHandling: "多行文本處理：保留換行符、轉義爲 \\n、使用 &lt;br&gt; 標籤"

        includeLineNumbers: "在表格左側添加行號列"
      magic:
        builtin: "選擇預定義的常用模闆格式"
        rowsTpl: "<table> <tr> <th>魔法語法</th> <th>描述</th> <th>支持的 JS 方法</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>第1、第2...個<b>標題</b>字段，也可冩作 {hA} {hB} ...</td> <td>字符串方法</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>當前行的第1、第2...個字段，也可冩作 {$A} {$B} ...</td> <td>字符串方法</td> </tr> <tr> <td>{F,} {F;}</td> <td>用 <b>F</b> 後麵的字符串分割當前行</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>當前<b>行</b>的行<b>號</b>，從1或100開始</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>結束</b>行的行<b>號</b> </td> <td></td> </tr> <tr> <td>{x ...}</td> <td><b>執行</b> JavaScript 代碼，例如：{x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> 使用反斜槓 <b>\\</b> 輸出大括號 {...} </td> <td></td> </tr></table>"
        headerTpl: "自定義頁眉部分的輸出模闆"
        footerTpl: "自定義頁腳部分的輸出模闆"
      textile:
        escape: "轉義 Textile 語法字符（|、.、-、^）以避免格式衝突"
        rowHeader: "將第一行設爲表頭行"
        thead: "爲表頭和表體添加 Textile 語法標記"
      xml:
        escape: "轉義 XML 特殊字符（&lt;、&gt;、&amp;、\"、'）以確保有效的 XML"
        minify: "生成壓縮的 XML 輸出，移除多餘空白"
        rootElement: "設置 XML 根元素標籤名"
        rowElement: "設置每行數據的 XML 元素標籤名"
        declaration: "添加 XML 聲明頭（&lt;?xml version=\"1.0\"?&gt;）"
        attributes: "將數據輸出爲 XML 屬性而非子元素"
        cdata: "用 CDATA 包裝文本內容以保護特殊字符"
        encoding: "設置 XML 文檔的字符編碼格式"
        indentation: "選擇 XML 縮進字符：空格或製表符"
      yaml:
        indentSize: "設置 YAML 層次縮進的空格數（通常爲 2 或 4）"
        arrayStyle: "數組格式：塊格式（每行一項）或流格式（內聯格式）"
        quotationStyle: "字符串引號樣式：無引號、單引號、雙引號"
      pdf:
        theme: "選擇 PDF 表格視覺樣式，用於專業文檔"
        headerColor: "選擇 PDF 表格標題背景顏色"
        showHead: "控制 PDF 頁面中標題顯示方式"
        docTitle: "PDF 文檔的可選標題"
        docDescription: "PDF 文檔的可選描述文本"
      csv:
        bom: "添加 UTF-8 字節順序標記以幫助 Excel 等軟件識別編碼"
      excel:
        autoWidth: "根據內容自動調整列寬"
        protectSheet: "啟用工作表保護，密碼：tableconvert.com"
      sql:
        primaryKey: "爲 CREATE TABLE 語句指定主鍵字段名"
        dialect: "選擇數據庫類型，影響引號和數據類型語法"
      ascii:
        forceSep: "強製在每行數據之間添加分隔線"
        style: "選擇 ASCII 表格邊框繪製樣式"
        comment: "添加注釋標記包裝整個表格"
      mediawiki:
        minify: "壓縮輸出代碼，移除多餘空白"
        header: "將第一行標記爲表頭樣式"
        sort: "啟用表格點擊排序功能"
      asciidoc:
        minify: "壓縮 AsciiDoc 格式輸出"
        firstHeader: "將第一行設爲表頭行"
        lastFooter: "將最後一行設爲表尾行"
        title: "爲表格添加標題文本"
      tracwiki:
        rowHeader: "將第一行設爲表頭"
        colHeader: "將第一列設爲表頭"
      bbcode:
        minify: "壓縮 BBCode 輸出格式"
      restructuredtext:
        style: "選擇 reStructuredText 表格邊框樣式"
        forceSep: "強製分隔線"
    label:
      ascii:
        forceSep: "行分隔符"
        style: "邊框樣式"
        comment: "注釋包裝"
      restructuredtext:
        style: "邊框樣式"
        forceSep: "強製分隔符"
      bbcode:
        minify: "壓縮輸出"
      csv:
        doubleQuote: "雙引號包裝"
        delimiter: "字段分隔符"
        bom: "UTF-8 BOM"
        valueDelimiter: "值分隔符"
        rowDelimiter: "行分隔符"
        prefix: "行前綴"
        suffix: "行後綴"
      excel:
        autoWidth: "自動列寬"
        textFormat: "文本格式"
        protectSheet: "保護工作表"
        boldFirstRow: "首行加粗"
        boldFirstColumn: "首列加粗"
        sheetName: "工作表名稱"
      html:
        escape: "轉義 HTML 字符"
        div: "DIV 表格"
        minify: "壓縮代碼"
        thead: "表頭結構"
        tableCaption: "表格標題"
        tableClass: "表格類名"
        tableId: "表格 ID"
        rowHeader: "行表頭"
        colHeader: "列表頭"
      jira:
        escape: "轉義字符"
        rowHeader: "行表頭"
        colHeader: "列表頭"
      json:
        parsingJSON: "解析 JSON"
        minify: "壓縮輸出"
        format: "數據格式"
        rootName: "根對象名稱"
        indentSize: "縮進大小"
      jsonlines:
        parsingJSON: "解析 JSON"
        format: "數據格式"
      latex:
        escape: "轉義 LaTeX 表格字符"
        ht: "浮動位置"
        mwe: "完整文檔"
        tableAlign: "表格對齊"
        tableBorder: "邊框樣式"
        label: "引用標籤"
        caption: "表格標題"
        location: "標題位置"
        tableType: "表格類型"
        boldFirstRow: "首行加粗"
        boldFirstColumn: "首列加粗"
        textAlign: "文本對齊"
        borders: "邊框設置"
      markdown:
        escape: "轉義字符"
        pretty: "美化 Markdown 表格"
        simple: "簡單 Markdown 格式"
        boldFirstRow: "首行加粗"
        boldFirstColumn: "首列加粗"
        firstHeader: "首行表頭"
        textAlign: "文本對齊"
        multilineHandling: "多行處理"

        includeLineNumbers: "添加行號"
        align: "對齊方式"
      mediawiki:
        minify: "壓縮代碼"
        header: "表頭標記"
        sort: "可排序"
      asciidoc:
        minify: "壓縮格式"
        firstHeader: "首行表頭"
        lastFooter: "末行表尾"
        title: "表格標題"
      tracwiki:
        rowHeader: "行表頭"
        colHeader: "列表頭"
      sql:
        drop: "刪除表（如果存在）"
        create: "創建表"
        oneInsert: "批量插入"
        table: "表名"
        dialect: "數據庫類型"
        primaryKey: "主鍵"
      magic:
        builtin: "內置模闆"
        rowsTpl: "行模闆，語法 ->"
        headerTpl: "頁眉模闆"
        footerTpl: "頁腳模闆"
      textile:
        escape: "轉義字符"
        rowHeader: "行表頭"
        thead: "表頭語法"
      xml:
        escape: "轉義 XML 字符"
        minify: "壓縮輸出"
        rootElement: "根元素"
        rowElement: "行元素"
        declaration: "XML 聲明"
        attributes: "屬性模式"
        cdata: "CDATA 包裝"
        encoding: "編碼"
        indentSize: "縮進大小"
      yaml:
        indentSize: "縮進大小"
        arrayStyle: "數組樣式"
        quotationStyle: "引號樣式"
      pdf:
        theme: "PDF 表格主題"
        headerColor: "PDF 表頭顏色"
        showHead: "PDF 表頭顯示"
        docTitle: "PDF 文檔標題"
        docDescription: "PDF 文檔描述"
sidebar:
  all: "所有轉換工具"
  dataSource:
    title: "數據源"
    description:
      converter: "導入 %s 以轉換爲 %s。支持文件上傳、在線編輯和網頁數據提取。"
      generator: "創建表格數據，支持多種輸入方式，包括手動輸入、文件導入和模闆生成。"
  tableEditor:
    title: "在線表格編輯器"
    description:
      converter: "使用我們的表格編輯器在線處理 %s。類似 Excel 的操作體驗，支持刪除空行、去重、排序和查找替換。"
      generator: "強大的在線表格編輯器，提供類似 Excel 的操作體驗。支持刪除空行、去重、排序和查找替換。"
  tableGenerator:
    title: "表格生成器"
    description:
      converter: "使用表格生成器快速生成 %s，實時預覽。豐富的導出選項，一鍵複製和下載。"
      generator: "將 %s 數據導出爲多種格式，滿足不同使用場景。支持自定義選項和實時預覽。"
footer:
  changelog: "更新日誌"
  sponsor: "讚助商"
  contact: "聯繫我們"
  privacyPolicy: "隱私政策"
  about: "關於"
  resources: "資源"
  popularConverters: "熱門轉換器"
  popularGenerators: "熱門生成器"
  dataSecurity: "您的資料是安全的 - 所有轉換都在您的瀏覽器中執行。"
converters:
  Markdown:
    alias: "Markdown 表格"
    what: "Markdown 是一種輕量級標記語言，廣泛用於技術文檔、博客內容創作和網頁開髮。其表格語法簡潔直觀，支持文本對齊、鏈接嵌入和格式化。是程序員和技術冩作者的首選工具，與 GitHub、GitLab 等代碼托管平颱完美兼容。"
    step1: "將 Markdown 表格數據粘貼到數據源區域，或直接拖拽 .md 文件上傳。工具自動解析表格結構和格式，支持複雜嵌套內容和特殊字符處理。"
    step3: "實時生成標準 Markdown 表格代碼，支持多種對齊方式、文本加粗、行號添加等高級格式設置。生成的代碼與 GitHub 和主流 Markdown 編輯器完全兼容，一鍵複製即可使用。"
    from_alias: "Markdown 表格文件"
    to_alias: "Markdown 表格格式"
  Magic:
    alias: "自定義模闆"
    what: "魔法模闆是本工具獨有的高級數據生成器，允許用戶通過自定義模闆語法創建任意格式的數據輸出。支持變量替換、條件判斷和循環處理。是處理複雜數據轉換需求和個性化輸出格式的終極解決方案，特別適合開髮者和數據工程師。"
    step1: "選擇內置常用模闆或創建自定義模闆語法。支持豐富的變量和函數，能夠處理複雜的數據結構和業務邏輯。"
    step3: "生成完全符合自定義格式要求的數據輸出。支持複雜的數據轉換邏輯和條件處理，大大提高數據處理效率和輸出質量。批量數據處理的強大工具。"
    from_alias: "表格數據"
    to_alias: "自定義格式輸出"
  CSV:
    alias: "CSV"
    what: "CSV（逗號分隔值）是使用最廣泛的數據交換格式，完美支持 Excel、Google Sheets、數據庫繫統和各種數據分析工具。其簡單的結構和強大的兼容性使其成爲數據遷移、批量導入導出和跨平颱數據交換的標準格式，廣泛應用於商業分析、數據科學和繫統集成。"
    step1: "上傳 CSV 文件或直接粘貼 CSV 數據。工具智能識別各種分隔符（逗號、製表符、分號、管道符等），自動檢測數據類型和編碼格式，支持大文件快速解析和複雜數據結構。"
    step3: "生成標準 CSV 格式文件，支持自定義分隔符、引號樣式、編碼格式和 BOM 標記設置。確保與目標繫統完美兼容，提供下載和壓縮選項，滿足企業級數據處理需求。"
    from_alias: "CSV 數據文件"
    to_alias: "CSV 標準格式"
  JSON:
    alias: "JSON 數組"
    what: "JSON（JavaScript 對象表示法）是現代 Web 應用程序、REST API 和微服務架構的標準表格數據格式。其清晰的結構和高效的解析使其廣泛用於前後端數據交互、配置文件存儲和 NoSQL 數據庫。支持嵌套對象、數組結構和多種數據類型，是現代軟件開髮不可缺少的表格數據。"
    step1: "上傳 JSON 文件或粘貼 JSON 數組。支持自動識別和解析對象數組、嵌套結構和複雜數據類型。工具智能驗証 JSON 語法並提供錯誤提示。"
    step3: "生成多種 JSON 格式輸出：標準對象數組、二維數組、列數組和鍵值對格式。支持美化輸出、壓縮模式、自定義根對象名稱和縮進設置，完美適配各種 API 接口和數據存儲需求。"
    from_alias: "JSON 數組文件"
    to_alias: "JSON 標準格式"
  JSONLines:
    alias: "JSONLines 格式"
    what: "JSON Lines（也稱爲 NDJSON）是大數據處理和流式數據傳輸的重要格式，每行包含一個獨立的 JSON 對象。廣泛用於日誌分析、數據流處理、機器學習和分佈式繫統。支持增量處理和並行計算，是處理大規模結構化數據的理想選擇。"
    step1: "上傳 JSONLines 文件或粘貼數據。工具逐行解析 JSON 對象，支持大文件流式處理和錯誤行跳過功能。"
    step3: "生成標準 JSONLines 格式，每行輸出一個完整的 JSON 對象。適用於流式處理、批量導入和大數據分析場景，支持數據驗証和格式優化。"
    from_alias: "JSONLines 數據"
    to_alias: "JSONLines 流式格式"
  XML:
    alias: "XML"
    what: "XML（可擴展標記語言）是企業級數據交換和配置管理的標準格式，具有嚴格的語法規範和強大的驗証機製。廣泛用於 Web 服務、配置文件、文檔存儲和繫統集成。支持命名空間、模式驗証和 XSLT 轉換，是企業應用的重要表格數據。"
    step1: "上傳 XML 文件或粘貼 XML 數據。工具自動解析 XML 結構並轉換爲表格格式，支持命名空間、屬性處理和複雜嵌套結構。"
    step3: "生成符合 XML 標準的輸出。支持自定義根元素、行元素名稱、屬性模式、CDATA 包裝和字符編碼設置。確保數據完整性和兼容性，滿足企業級應用要求。"
    from_alias: "XML 數據文件"
    to_alias: "XML 標準格式"
  YAML:
    alias: "YAML 配置"
    what: "YAML 是一種人性化的數據序列化標準，以其清晰的層次結構和簡潔的語法而聞名。廣泛用於配置文件、DevOps 工具鏈、Docker Compose 和 Kubernetes 部署。其強大的可讀性和簡潔的語法使其成爲現代雲原生應用和自動化運維的重要配置格式。"
    step1: "上傳 YAML 文件或粘貼 YAML 數據。工具智能解析 YAML 結構並驗証語法正確性，支持多文檔格式和複雜數據類型。"
    step3: "生成標準 YAML 格式輸出，支持塊式和流式數組樣式、多種引號設置、自定義縮進和注釋保留。確保輸出的 YAML 文件與各種解析器和配置繫統完全兼容。"
    from_alias: "YAML 配置文件"
    to_alias: "YAML 標準格式"
  MySQL:
      alias: "MySQL 查詢結果"
      what: "MySQL 是世界上最受歡迎的開源關繫數據庫管理繫統，以其高性能、可靠性和易用性而聞名。廣泛用於 Web 應用程序、企業繫統和數據分析平颱。MySQL 查詢結果通常包含結構化的表格數據，是數據庫管理和數據分析工作中的重要數據源。"
      step1: "將 MySQL 查詢輸出結果粘貼到數據源區域。工具自動識別和解析 MySQL 命令行輸出格式，支持各種查詢結果樣式和字符編碼，智能處理表頭和數據行。"
      step3: "快速將 MySQL 查詢結果轉換爲多種表格數據格式，便於數據分析、報告生成、跨繫統數據遷移和數據驗証。數據庫管理員和數據分析師的實用工具。"
      from_alias: "MySQL 查詢輸出"
      to_alias: "MySQL 表格數據"
  SQL:
    alias: "插入 SQL"
    what: "SQL（結構化查詢語言）是關繫數據庫的標準操作語言，用於數據查詢、插入、更新和刪除操作。作爲數據庫管理的核心技術，SQL 廣泛用於數據分析、商業智能、ETL 處理和數據倉庫構建。是數據專業人員的必備技能工具。"
    step1: "粘貼 INSERT SQL 語句或上傳 .sql 文件。工具智能解析 SQL 語法並提取表格數據，支持多種 SQL 方言和複雜查詢語句處理。"
    step3: "生成標準 SQL INSERT 語句和表創建語句。支持多種數據庫方言（MySQL、PostgreSQL、SQLite、SQL Server、Oracle），自動處理數據類型映射、字符轉義和主鍵約束。確保生成的 SQL 代碼可以直接執行。"
    from_alias: "SQL 數據文件"
    to_alias: "SQL 標準語句"
  Qlik:
      alias: "Qlik 表格"
      what: "Qlik 是專門從事數據可視化、執行儀表闆和自助式商業智能産品的軟件供應商，與 Tableau 和 Microsoft 齊名。"
      step1: ""
      step3: "最後，[表格生成器](#TableGenerator) 顯示轉換結果。可在您的 Qlik Sense、Qlik AutoML、QlikView 或其他支持 Qlik 的軟件中使用。"
      from_alias: "Qlik 表格"
      to_alias: "Qlik 表格"
  DAX:
      alias: "DAX 表格"
      what: "DAX（數據分析表達式）是 Microsoft Power BI 中使用的編程語言，用於創建計算列、度量值和自定義表格。"
      step1: ""
      step3: "最後，[表格生成器](#TableGenerator) 顯示轉換結果。如預期的那樣，它用於多個 Microsoft 産品，包括 Microsoft Power BI、Microsoft Analysis Services 和 Microsoft Power Pivot for Excel。"
      from_alias: "DAX 表格"
      to_alias: "DAX 表格"
  Firebase:
    alias: "Firebase 列表"
    what: "Firebase 是一個 BaaS 應用開髮平颱，提供托管的後端服務，如實時數據庫、雲存儲、身份驗証、崩潰報告等。"
    step1: ""
    step3: "最後，[表格生成器](#TableGenerator) 顯示轉換結果。然後您可以使用 Firebase API 中的 push 方法將數據添加到 Firebase 數據庫的列表中。"
    from_alias: "Firebase 列表"
    to_alias: "Firebase 列表"
  HTML:
    alias: "HTML 表格"
    what: "HTML 表格是在網頁中顯示結構化數據的標準方式，使用 table、tr、td 等標籤構建。支持豐富的樣式定製、響應式佈局和交互功能。廣泛用於網站開髮、數據展示和報告生成，是前端開髮和網頁設計的重要組成部分。"
    step1: "粘貼包含表格的 HTML 代碼或上傳 HTML 文件。工具自動識別並從頁麵中提取表格數據，支持複雜的 HTML 結構、CSS 樣式和嵌套表格處理。"
    step3: "生成語義化的 HTML 表格代碼，支持 thead/tbody 結構、CSS 類設置、表格標題、行/列表頭和響應式屬性配置。確保生成的表格代碼符合 Web 標準，具有良好的可訪問性和 SEO 友好性。"
    from_alias: "HTML 網頁表格"
    to_alias: "HTML 標準表格"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel 是世界上最受歡迎的電子表格軟件，廣泛用於商業分析、財務管理、數據處理和報告創建。其強大的數據處理能力、豐富的函數庫和靈活的可視化功能使其成爲辦公自動化和數據分析的標準工具，在幾乎所有行業和領域都有廣泛應用。"
    step1: "上傳 Excel 文件（支持 .xlsx、.xls 格式）或直接從 Excel 複製表格數據並粘貼。工具支持多工作表處理、複雜格式識別和大文件快速解析，自動處理合並單元格和數據類型。"
    step3: "生成與 Excel 兼容的表格數據，可直接粘貼到 Excel 中或下載爲標準 .xlsx 文件。支持工作表命名、單元格格式化、自動列寬、表頭樣式和數據驗証設置。確保輸出的 Excel 文件具有專業外觀和完整功能。"
    from_alias: "Excel 電子表格"
    to_alias: "Excel 標準格式"
  LaTeX:
    alias: "LaTeX 表格"
    what: "LaTeX 是專業的文檔排版繫統，特別適合創建學術論文、技術文檔和科學出版物。其表格功能強大，支持複雜的數學公式、精確的佈局控製和高質量的 PDF 輸出。是學術界和科學出版的標準工具，廣泛用於期刊論文、學位論文和技術手冊排版。"
    step1: "粘貼 LaTeX 表格代碼或上傳 .tex 文件。工具解析 LaTeX 表格語法並提取數據內容，支持多種表格環境（tabular、longtable、array 等）和複雜格式命令。"
    step3: "生成專業的 LaTeX 表格代碼，支持多種表格環境選擇、邊框樣式配置、標題位置設置、文檔類規範和包管理。可生成完整的可編譯 LaTeX 文檔，確保輸出表格符合學術出版標準。"
    from_alias: "LaTeX 文檔表格"
    to_alias: "LaTeX 專業格式"
  ASCII:
    alias: "ASCII 表格"
    what: "ASCII 表格使用純文本字符繪製表格邊框和結構，提供最佳的兼容性和可移植性。與所有文本編輯器、終端環境和操作繫統兼容。廣泛用於代碼文檔、技術手冊、README 文件和命令行工具輸出。程序員和繫統管理員的首選數據顯示格式。"
    step1: "上傳包含 ASCII 表格的文本文件或直接粘貼表格數據。工具智能識別和解析 ASCII 表格結構，支持多種邊框樣式和對齊格式。"
    step3: "生成美觀的純文本 ASCII 表格，支持多種邊框樣式（單線、雙線、圓角等）、文本對齊方式和自動列寬。生成的表格在代碼編輯器、文檔和命令行中完美顯示。"
    from_alias: "ASCII 文本表格"
    to_alias: "ASCII 標準格式"
  MediaWiki:
    alias: "MediaWiki 表格"
    what: "MediaWiki 是維基百科等著名 wiki 網站使用的開源軟件平颱。其表格語法簡潔而強大，支持表格樣式定製、排序功能和鏈接嵌入。廣泛用於知識管理、協作編輯和內容管理繫統，是構建 wiki 百科全書和知識庫的核心技術。"
    step1: "粘貼 MediaWiki 表格代碼或上傳 wiki 源文件。工具解析 wiki 標記語法並提取表格數據，支持複雜的 wiki 語法和模闆處理。"
    step3: "生成標準 MediaWiki 表格代碼，支持表頭樣式設置、單元格對齊、排序功能啟用和代碼壓縮選項。生成的代碼可直接用於 wiki 頁麵編輯，確保在 MediaWiki 平颱上完美顯示。"
    from_alias: "MediaWiki 源代碼"
    to_alias: "MediaWiki 表格語法"
  TracWiki:
    alias: "TracWiki 表格"
    what: "Trac 是一個基於 Web 的項目管理和錯誤跟踪繫統，使用簡化的 wiki 語法創建表格內容。"
    step1: "上傳 TracWiki 文件或粘貼表格數據。"
    step3: "生成與 TracWiki 兼容的表格代碼，支持行/列表頭設置，便於項目文檔管理。"
    from_alias: "TracWiki 表格"
    to_alias: "TracWiki 格式"
  AsciiDoc:
    alias: "AsciiDoc 表格"
    what: "AsciiDoc 是一種輕量級標記語言，可以轉換爲 HTML、PDF、手冊頁和其他格式，廣泛用於技術文檔編冩。"
    step1: "上傳 AsciiDoc 文件或粘貼數據。"
    step3: "生成 AsciiDoc 表格語法，支持表頭、表尾和標題設置，可直接在 AsciiDoc 編輯器中使用。"
    from_alias: "AsciiDoc 表格"
    to_alias: "AsciiDoc 格式"
  reStructuredText:
    alias: "reStructuredText 表格"
    what: "reStructuredText 是 Python 社區的標準文檔格式，支持豐富的表格語法，常用於 Sphinx 文檔生成。"
    step1: "上傳 .rst 文件或粘貼 reStructuredText 數據。"
    step3: "生成標準 reStructuredText 表格，支持多種邊框樣式，可直接用於 Sphinx 文檔項目。"
    from_alias: "reStructuredText 表格"
    to_alias: "reStructuredText 格式"
  PHP:
    alias: "PHP 數組"
    what: "PHP 是一種流行的服務器端腳本語言，數組是其核心數據結構，廣泛用於 Web 開髮和數據處理。"
    step1: "上傳包含 PHP 數組的文件或直接粘貼數據。"
    step3: "生成標準 PHP 數組代碼，可直接用於 PHP 項目，支持關聯數組和索引數組格式。"
    from_alias: "PHP 數組"
    to_alias: "PHP 代碼"
  Ruby:
    alias: "Ruby 數組"
    what: "Ruby 是一種動態麵向對象編程語言，語法簡潔優雅，數組是其重要的數據結構。"
    step1: "上傳 Ruby 文件或粘貼數組數據。"
    step3: "生成符合 Ruby 語法規範的數組代碼，可直接用於 Ruby 項目。"
    from_alias: "Ruby 數組"
    to_alias: "Ruby 代碼"
  ASP:
    alias: "ASP 數組"
    what: "ASP（Active Server Pages）是 Microsoft 的服務器端腳本環境，支持多種編程語言開髮動態網頁。"
    step1: "上傳 ASP 文件或粘貼數組數據。"
    step3: "生成與 ASP 兼容的數組代碼，支持 VBScript 和 JScript 語法，可用於 ASP.NET 項目。"
    from_alias: "ASP 數組"
    to_alias: "ASP 代碼"
  ActionScript:
    alias: "ActionScript 數組"
    what: "ActionScript 是一種麵向對象的編程語言，主要用於 Adobe Flash 和 AIR 應用程序開髮。"
    step1: "上傳 .as 文件或粘貼 ActionScript 數據。"
    step3: "生成符合 AS3 語法標準的 ActionScript 數組代碼，可用於 Flash 和 Flex 項目開髮。"
    from_alias: "ActionScript 數組"
    to_alias: "ActionScript 代碼"
  BBCode:
    alias: "BBCode 表格"
    what: "BBCode 是一種輕量級標記語言，常用於論壇和在線社區，提供簡單的格式化功能，包括表格支持。"
    step1: "上傳包含 BBCode 的文件或粘貼數據。"
    step3: "生成適合論壇髮帖和社區內容創建的 BBCode 表格代碼，支持壓縮輸出格式。"
    from_alias: "BBCode 表格"
    to_alias: "BBCode 格式"
  PDF:
    alias: "PDF 表格"
    what: "PDF（便攜式文檔格式）是一種跨平颱文檔標準，具有固定佈局、一緻顯示和高質量打印特性。廣泛用於正式文檔、報告、髮票、合同和學術論文。是商務溝通和文檔歸檔的首選格式，確保在不同設備和操作繫統上完全一緻的視覺效果。"
    step1: "導入任何格式的表格數據。工具自動分析數據結構並進行智能佈局設計，支持大表格自動分頁和複雜數據類型處理。"
    step3: "生成高質量的 PDF 表格文件，支持多種專業主題樣式（商務、學術、簡約等）、多語言字體、自動分頁、水印添加和打印優化。確保輸出的 PDF 文檔具有專業外觀，可直接用於商務演示和正式髮佈。"
    from_alias: "表格數據"
    to_alias: "PDF 專業文檔"
  JPEG:
    alias: "JPEG 圖像"
    what: "JPEG 是使用最廣泛的數字圖像格式，具有出色的壓縮效果和廣泛的兼容性。其小文件大小和快速加載速度使其適合網頁顯示、社交媒體分享、文檔插圖和在線演示。是數字媒體和網絡通信的標準圖像格式，幾乎所有設備和軟件都完美支持。"
    step1: "導入任何格式的表格數據。工具進行智能佈局設計和視覺優化，自動計算最佳尺寸和分辨率。"
    step3: "生成高清 JPEG 表格圖像，支持多種主題配色方案（淺色、深色、護眼等）、自適應佈局、文本清晰度優化和尺寸定製。適合在線分享、文檔插入和演示使用，確保在各種顯示設備上都有出色的視覺效果。"
    from_alias: "表格數據"
    to_alias: "JPEG 高清圖像"
  Jira:
    alias: "Jira 表格"
    what: "JIRA 是 Atlassian 開髮的專業項目管理和錯誤跟踪軟件，廣泛用於敏捷開髮、軟件測試和項目協作。其表格功能支持豐富的格式選項和數據顯示，是軟件開髮團隊、項目經理和質量保証人員在需求管理、錯誤跟踪和進度報告中的重要工具。"
    step1: "上傳包含表格數據的文件或直接粘貼數據內容。工具自動處理表格數據和特殊字符轉義。"
    step3: "生成與 JIRA 平颱兼容的表格代碼，支持表頭樣式設置、單元格對齊、字符轉義處理和格式優化。生成的代碼可直接粘貼到 JIRA 問題描述、評論或 wiki 頁麵中，確保在 JIRA 繫統中正確顯示和渲染。"
    from_alias: "項目數據"
    to_alias: "Jira 表格語法"
  Textile:
    alias: "Textile 表格"
    what: "Textile 是一種簡潔的輕量級標記語言，語法簡單易學，廣泛用於內容管理繫統、博客平颱和論壇繫統。其表格語法清晰直觀，支持快速格式化和樣式設置。是內容創作者和網站管理員快速文檔編冩和內容髮佈的理想工具。"
    step1: "上傳 Textile 格式文件或粘貼表格數據。工具解析 Textile 標記語法並提取表格內容。"
    step3: "生成標準 Textile 表格語法，支持表頭標記、單元格對齊、特殊字符轉義和格式優化。生成的代碼可直接用於支持 Textile 的 CMS 繫統、博客平颱和文檔繫統，確保內容正確渲染和顯示。"
    from_alias: "Textile 文檔"
    to_alias: "Textile 表格語法"
  PNG:
    alias: "PNG 圖像"
    what: "PNG（便攜式網絡圖形）是一種無損圖像格式，具有出色的壓縮和透明度支持。廣泛用於網頁設計、數字圖形和專業攝影。其高質量和廣泛兼容性使其成爲截圖、徽標、圖表和任何需要清晰細節和透明背景的圖像的理想選擇。"
    step1: "導入任何格式的表格數據。工具進行智能佈局設計和視覺優化，自動計算 PNG 輸出的最佳尺寸和分辨率。"
    step3: "生成高質量的 PNG 表格圖像，支持多種主題配色方案、透明背景、自適應佈局和文本清晰度優化。完美適用於網頁使用、文檔插入和專業演示，具有出色的視覺質量。"
    from_alias: "表格數據"
    to_alias: "PNG 高質量圖像"
  TOML:
    alias: "TOML 配置"
    what: "TOML（Tom's Obvious, Minimal Language）是一種易於讀冩的配置文件格式。設計爲明確和簡單，廣泛用於現代軟件項目的配置管理。其清晰的語法和強類型使其成爲應用程序設置和項目配置文件的絶佳選擇。"
    step1: "上傳 TOML 文件或粘貼配置數據。工具解析 TOML 語法並提取結構化配置信息。"
    step3: "生成標準 TOML 格式，支持嵌套結構、數據類型和注釋。生成的 TOML 文件非常適合應用程序配置、構建工具和項目設置。"
    from_alias: "TOML 配置"
    to_alias: "TOML 格式"
  INI:
    alias: "INI 配置"
    what: "INI 文件是許多應用程序和操作繫統使用的簡單配置文件。其直接的鍵值對結構使其易於手動讀取和編輯。廣泛用於 Windows 應用程序、遺留繫統和需要人類可讀性的簡單配置場景。"
    step1: "上傳 INI 文件或粘貼配置數據。工具解析 INI 語法並提取基於節的配置信息。"
    step3: "生成標準 INI 格式，支持節、注釋和各種數據類型。生成的 INI 文件與大多數應用程序和配置繫統兼容。"
    from_alias: "INI 配置"
    to_alias: "INI 格式"
  Avro:
    alias: "Avro 模式"
    what: "Apache Avro 是一個數據序列化繫統，提供豐富的數據結構、緊湊的二進製格式和模式演進功能。廣泛用於大數據處理、消息隊列和分佈式繫統。其模式定義支持複雜數據類型和版本兼容性，是數據工程師和繫統架構師的重要工具。"
    step1: "上傳 Avro 模式文件或粘貼數據。工具解析 Avro 模式定義並提取表格結構信息。"
    step3: "生成標準 Avro 模式定義，支持數據類型映射、字段約束和模式驗証。生成的模式可直接用於 Hadoop 生態繫統、Kafka 消息繫統和其他大數據平颱。"
    from_alias: "Avro 模式"
    to_alias: "Avro 數據格式"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers（protobuf）是 Google 的語言中性、平颱中性、可擴展的結構化數據序列化機製。廣泛用於微服務、API 開髮和數據存儲。其高效的二進製格式和強類型使其成爲高性能應用程序和跨語言通信的理想選擇。"
    step1: "上傳 .proto 文件或粘貼 Protocol Buffer 定義。工具解析 protobuf 語法並提取消息結構信息。"
    step3: "生成標準 Protocol Buffer 定義，支持消息類型、字段選項和服務定義。生成的 .proto 文件可爲多種編程語言編譯。"
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf 模式"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas 是 Python 中最受歡迎的數據分析庫，DataFrame 是其核心數據結構。它提供強大的數據操作、清理和分析功能，廣泛用於數據科學、機器學習和商業智能。是 Python 開髮者和數據分析師不可缺少的工具。"
    step1: "上傳包含 DataFrame 代碼的 Python 文件或粘貼數據。工具解析 Pandas 語法並提取 DataFrame 結構信息。"
    step3: "生成標準 Pandas DataFrame 代碼，支持數據類型規範、索引設置和數據操作。生成的代碼可直接在 Python 環境中執行，用於數據分析和處理。"
    from_alias: "Pandas DataFrame"
    to_alias: "Python 數據結構"
  RDF:
    alias: "RDF 三元組"
    what: "RDF（資源描述框架）是 Web 上數據交換的標準模型，旨在以圖形形式表示有關資源的信息。廣泛用於語義網、知識圖譜和鏈接數據應用。其三元組結構能夠實現豐富的元數據表示和語義關繫。"
    step1: "上傳 RDF 文件或粘貼三元組數據。工具解析 RDF 語法並提取語義關繫和資源信息。"
    step3: "生成標準 RDF 格式，支持各種序列化（RDF/XML、Turtle、N-Triples）。生成的 RDF 可用於語義網應用、知識庫和鏈接數據繫統。"
    from_alias: "RDF 數據"
    to_alias: "RDF 語義格式"
  MATLAB:
    alias: "MATLAB 數組"
    what: "MATLAB 是一款高性能數值計算和可視化軟件，廣泛用於工程計算、數據分析和算法開髮。其數組和矩陣操作功能強大，支持複雜的數學計算和數據處理。是工程師、研究人員和數據科學家的必備工具。"
    step1: "上傳 MATLAB .m 文件或粘貼數組數據。工具解析 MATLAB 語法並提取數組結構信息。"
    step3: "生成標準 MATLAB 數組代碼，支持多維數組、數據類型規範和變量命名。生成的代碼可直接在 MATLAB 環境中執行，用於數據分析和科學計算。"
    from_alias: "MATLAB 數組"
    to_alias: "MATLAB 代碼格式"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame 是 R 編程語言的核心數據結構，廣泛用於統計分析、數據挖掘和機器學習。R 是統計計算和圖形的頂級工具，DataFrame 提供強大的數據操作、統計分析和可視化功能。對於從事結構化數據分析的數據科學家、統計學家和研究人員來說是必不可少的。"
    step1: "上傳 R 數據文件或貼上 DataFrame 代碼。工具解析 R 語法並提取 DataFrame 結構信息，包括列類型、行名稱和數據內容。"
    step3: "生成標準 R DataFrame 代碼，支持數據類型規範、因子級別、行/列名稱和 R 特定的數據結構。生成的代碼可直接在 R 環境中執行，用於統計分析和數據處理。"
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "開始轉換"
  start_generating: "開始生成"
  api_docs: "API 文件"
related:
  section_title: '更多 {{ if and .from (ne .from "generator") }}{{ .from }} 和 {{ end }}{{ .to }} 轉換器'
  section_description: '探索更多 {{ if and .from (ne .from "generator") }}{{ .from }} 和 {{ end }}{{ .to }} 格式的轉換器。使用我們專業的線上轉換工具在多種格式之間轉換您的資料。'
  title: "{{ .from }} 轉 {{ .to }}"
howto:
  step2: "使用我們具有專業功能的進階線上表格編輯器編輯資料。支援刪除空白列、移除重複項、資料轉置、排序、正規表達式尋找和取代，以及即時預覽。所有變更都會自動轉換為 %s 格式，結果精確可靠。"
  section_title: "如何使用 {{ . }}"
  converter_description: "透過我們的逐步指南學習如何將 {{ .from }} 轉換為 {{ .to }}。具有進階功能和即時預覽的專業線上轉換器。"
  generator_description: "學習如何使用我們的線上生成器建立專業的 {{ .to }} 表格。類似 Excel 的編輯、即時預覽和即時匯出功能。"
extension:
  section_title: "表格偵測和提取擴充功能"
  section_description: "一鍵從任何網站提取表格。即時轉換為包括 Excel、CSV、JSON 在內的 30 多種格式 - 無需複製貼上。"
  features:
    extraction_title: "一鍵表格提取"
    extraction_description: "無需複製貼上即可從任何網頁即時提取表格 - 專業資料提取變得簡單"
    formats_title: "支援 30 多種格式轉換"
    formats_description: "使用我們先進的表格轉換器將提取的表格轉換為 Excel、CSV、JSON、Markdown、SQL 等格式"
    detection_title: "智慧表格偵測"
    detection_description: "自動偵測並突出顯示任何網頁上的表格，實現快速資料提取和轉換"
  hover_tip: "✨ 將滑鼠懸停在任何表格上以查看提取圖示"
recommendations:
  section_title: "大學和專業人士推薦"
  section_description: "TableConvert 受到大學、研究機構和開發團隊專業人士的信賴，用於可靠的表格轉換和資料處理。"
  cards:
    university_title: "威斯康辛大學麥迪遜分校"
    university_description: "TableConvert.com - 專業的免費線上表格轉換器和資料格式工具"
    university_link: "閱讀文章"
    facebook_title: "資料專業社群"
    facebook_description: "在 Facebook 開發者群組中被資料分析師和專業人士分享和推薦"
    facebook_link: "查看貼文"
    twitter_title: "開發者社群"
    twitter_description: "在 X (Twitter) 上被 @xiaoying_eth 和其他開發者推薦用於表格轉換"
    twitter_link: "查看推文"
faq:
  section_title: "常見問題"
  section_description: "關於我們免費線上表格轉換器、資料格式和轉換過程的常見問題。"
  what: "什麼是 %s 格式？"
  howto_convert:
    question: "如何免費使用 {{ . }}？"
    answer: "使用我們的免費線上表格轉換器上傳您的 {{ .from }} 檔案、貼上資料或從網頁提取。我們的專業轉換器工具可即時將您的資料轉換為 {{ .to }} 格式，具有即時預覽和進階編輯功能。立即下載或複製轉換結果。"
  security:
    question: "使用此線上轉換器時我的資料安全嗎？"
    answer: "絕對安全！所有表格轉換都在您的瀏覽器中本地進行 - 您的資料永遠不會離開您的裝置。我們的線上轉換器在用戶端處理所有內容，確保完全的隱私和資料安全。我們的伺服器上不儲存任何檔案。"
  free:
    question: "TableConvert 真的免費使用嗎？"
    answer: "是的，TableConvert 完全免費！所有轉換器功能、表格編輯器、資料生成器工具和匯出選項都可免費使用，無需費用、註冊或隱藏費用。免費線上轉換無限檔案。"
  filesize:
    question: "線上轉換器的檔案大小限制是多少？"
    answer: "我們的免費線上表格轉換器支援最大 10MB 的檔案。對於更大的檔案、批次處理或企業需求，請使用我們的瀏覽器擴充功能或具有更高限制的專業 API 服務。"
stats:
  conversions: "已轉換表格"
  tables: "已生成表格"
  formats: "資料檔案格式"
  rating: "使用者評分"
