site:
  fullname: "<PERSON>lay<PERSON>verteri"
  name: "TableConvert"
  subtitle: "Bepul Onlayn Jadval <PERSON>vert<PERSON> va Generatori"
  intro: "TableConvert - bu Excel, CSV, JSON, Markdown, LaTeX, SQL va boshqalar kabi 30+ format o'rtasida konvertatsiyani qo'llab-quvvatlaydigan bepul onlayn jadval konverteri va ma'lumot generatori vositasidir."
  followTwitter: "X da bizni kuzatib boring"
title:
  converter: "%s dan %s ga"
  generator: "%s Generator"
post:
  tags:
    converter: "Konverter"
    editor: "Muharrir"
    generator: "Generator"
    maker: "Quruvchi"
  converter:
    title: "%s ni %s ga Onlayn Konvertatsiya qiling"
    short: "Bepul va kuchli %s dan %s ga onlayn vosita"
    intro: "Foydalanish uchun oson onlayn %s dan %s ga konverter. Bizning intuitiv konvertatsiya vositamiz bilan jadval ma'lumotlarini osonlik bilan o'zgartiring. <PERSON><PERSON>, ishon<PERSON><PERSON> va foydalanuvchi uchun qulay."
  generator:
    title: "Onlayn %s Mu<PERSON>rir va Generator"
    short: "Keng imkoniyatlarga ega professional %s onlayn yaratish vositasi"
    intro: "Foydalanish uchun oson onlayn %s generator va jadval muharriri. Bizning intuitiv vosita va real vaqt ko'rinishi bilan professional ma'lumot jadvallarini osonlik bilan yarating."
navbar:
  search:
    placeholder: "Konverterni qidiring..."
  sponsor: "Bizga Qahva Sotib Oling"
  extension: "Kengaytma"
  api: "API hujjatlari"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Ma'lumot Manbai"
    placeholder: "%s ma'lumotlaringizni joylashtiring yoki %s fayllarini bu yerga sudrab oling"
    example: "Misol"
    upload: "Fayl Yuklash"
    extract:
      enter: "Veb Sahifadan Chiqarish"
      intro: "Tuzilgan ma'lumotlarni avtomatik ravishda chiqarish uchun jadval ma'lumotlari bo'lgan veb sahifa URL manzilini kiriting"
      btn: "%s ni Chiqarish"
    excel:
      sheet: "Ish Varaqi"
      none: "Hech narsa"
  tableEditor:
    title: "Onlayn Jadval Muharriri"
    undo: "Bekor qilish"
    redo: "Qaytarish"
    transpose: "Transpozitsiya"
    clear: "Tozalash"
    deleteBlank: "Bo'shlarni O'chirish"
    deleteDuplicate: "Takrorlarni O'chirish"
    uppercase: "KATTA HARFLAR"
    lowercase: "kichik harflar"
    capitalize: "Bosh Harf Bilan"
    replace:
      replace: "Topish va Almashtirish (Regex qo'llab-quvvatlanadi)"
      subst: "Bilan almashtirish..."
      btn: "Barchasini Almashtirish"
  tableGenerator:
    title: "Jadval Generator"
    sponsor: "Bizga Qahva Sotib Oling"
    copy: "Clipboardga Nusxalash"
    download: "Faylni Yuklab Olish"
    tooltip:
      html:
        escape: "Ko'rsatish xatolarini oldini olish uchun HTML maxsus belgilarini (&, <, >, \", ') escape qiling"
        div: "An'anaviy TABLE teglari o'rniga DIV+CSS tartibidan foydalaning, responsive dizayn uchun yaxshiroq mos keladi"
        minify: "Siqilgan HTML kodi yaratish uchun bo'sh joylar va qator uzilishlarini olib tashlang"
        thead: "Standart jadval boshi (&lt;thead&gt;) va tanasi (&lt;tbody&gt;) tuzilmasini yarating"
        tableCaption: "Jadval ustiga tavsiflovchi sarlavha qo'shing (&lt;caption&gt; elementi)"
        tableClass: "Oson uslub sozlash uchun jadvalga CSS sinf nomini qo'shing"
        tableId: "JavaScript manipulyatsiyasi uchun jadval uchun noyob ID identifikatorini o'rnating"
      jira:
        escape: "Jira jadval sintaksisi bilan ziddiyatlarni oldini olish uchun pipe belgilarini (|) escape qiling"
      json:
        parsingJSON: "Hujayralardagi JSON qatorlarini aqlli ravishda obyektlarga tahlil qiling"
        minify: "Fayl hajmini kamaytirish uchun ixcham bir qatorli JSON formatini yarating"
        format: "Chiqish JSON ma'lumotlar tuzilmasini tanlang: obyekt massivi, 2D massiv va boshqalar"
      latex:
        escape: "To'g'ri kompilyatsiyani ta'minlash uchun LaTeX maxsus belgilarini (%, &, _, #, $, va boshqalar) escape qiling"
        ht: "Sahifada jadval pozitsiyasini boshqarish uchun suzuvchi pozitsiya parametrini [!ht] qo'shing"
        mwe: "To'liq LaTeX hujjatini yarating"
        tableAlign: "Sahifada jadvalning gorizontal tekislashini o'rnating"
        tableBorder: "Jadval chegara uslubini sozlang: chegara yo'q, qisman chegara, to'liq chegara"
        label: "\\ref{} buyruq o'zaro havolasi uchun jadval yorlig'ini o'rnating"
        caption: "Jadval ustida yoki ostida ko'rsatish uchun jadval sarlavhasini o'rnating"
        location: "Jadval sarlavhasi ko'rsatish pozitsiyasini tanlang: ustida yoki ostida"
        tableType: "Jadval muhit turini tanlang: tabular, longtable, array va boshqalar"
      markdown:
        escape: "Format ziddiyatlarini oldini olish uchun Markdown maxsus belgilarini (*, _, |, \\, va boshqalar) escape qiling"
        pretty: "Chiroyliroq jadval formatini yaratish uchun ustun kengliklarini avtomatik tekislang"
        simple: "Tashqi chegara vertikal chiziqlarini qoldirib, soddalashtirilgan sintaksisdan foydalaning"
        boldFirstRow: "Birinchi qator matnini qalin qiling"
        boldFirstColumn: "Birinchi ustun matnini qalin qiling"
        firstHeader: "Birinchi qatorni sarlavha sifatida ko'rib chiqing va ajratuvchi chiziq qo'shing"
        textAlign: "Ustun matn tekislashini o'rnating: chap, markaziy, o'ng"
        multilineHandling: "Ko'p qatorli matn bilan ishlash: qator uzilishlarini saqlang, \\n ga escape qiling, &lt;br&gt; teglaridan foydalaning"

        includeLineNumbers: "Jadvalning chap tomoniga qator raqami ustunini qo'shing"
      magic:
        builtin: "Oldindan belgilangan umumiy shablon formatlarini tanlang"
        rowsTpl: "<table> <tr> <th>Sehrli Sintaksis</th> <th>Tavsif</th> <th>JS Metodlar Qo'llab-quvvatlash</th> </tr> <tr> <td>{h1} {h2} ...</td> <td><b>Sarlavha</b>ning 1-chi, 2-chi ... maydoni, ya'ni {hA} {hB} ...</td> <td>String metodlar</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>Joriy qatorning 1-chi, 2-chi ... maydoni, ya'ni {$A} {$B} ...</td> <td>String metodlar</td> </tr> <tr> <td>{F,} {F;}</td> <td><b>F</b> dan keyingi qator bilan joriy qatorni bo'ling</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>Joriy <b>Qator</b>ning Qator <b>Raqami</b> 1 yoki 100 dan</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>Qatorlar</b>ning <b>Oxirgi</b> Qator <b>Raqami</b> </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>JavaScript kodini <b>bajarish</b>, masalan: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Qavslarni {...} chiqarish uchun backslash <b>\\</b> dan foydalaning </td> <td></td> </tr></table>"
        headerTpl: "Sarlavha bo'limi uchun maxsus chiqish shabloni"
        footerTpl: "Pastki qism bo'limi uchun maxsus chiqish shabloni"
      textile:
        escape: "Format ziddiyatlarini oldini olish uchun Textile sintaksis belgilarini (|, ., -, ^) escape qiling"
        rowHeader: "Birinchi qatorni sarlavha qatori sifatida o'rnating"
        thead: "Jadval boshi va tanasi uchun Textile sintaksis belgilarini qo'shing"
      xml:
        escape: "To'g'ri XML ni ta'minlash uchun XML maxsus belgilarini (&lt;, &gt;, &amp;, \", ') escape qiling"
        minify: "Qo'shimcha bo'sh joylarni olib tashlab, siqilgan XML chiqarishini yarating"
        rootElement: "XML ildiz elementi teg nomini o'rnating"
        rowElement: "Har bir ma'lumot qatori uchun XML elementi teg nomini o'rnating"
        declaration: "XML deklaratsiya sarlavhasini qo'shing (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Ma'lumotlarni bola elementlari o'rniga XML atributlari sifatida chiqaring"
        cdata: "Maxsus belgilarni himoya qilish uchun matn mazmunini CDATA bilan o'rang"
        encoding: "XML hujjat uchun belgi kodlash formatini o'rnating"
        indentation: "XML chekinish belgisini tanlang: bo'shliqlar yoki tablar"
      yaml:
        indentSize: "YAML ierarxiya chekinishi uchun bo'shliqlar sonini o'rnating (odatda 2 yoki 4)"
        arrayStyle: "Massiv formati: blok (har bir element alohida qatorda) yoki oqim (inline format)"
        quotationStyle: "Satr tirnoq uslubi: tirnoqsiz, bitta tirnoq, qo'sh tirnoq"
      pdf:
        theme: "Professional hujjatlar uchun PDF jadval vizual uslubini tanlang"
        headerColor: "PDF jadval sarlavhasi fon rangini tanlang"
        showHead: "PDF sahifalarida sarlavha ko'rsatishni boshqaring"
        docTitle: "PDF hujjat uchun ixtiyoriy sarlavha"
        docDescription: "PDF hujjat uchun ixtiyoriy tavsif matni"
      csv:
        bom: "Excel va boshqa dasturlarga kodlashni tanishda yordam berish uchun UTF-8 bayt tartib belgisini qo'shing"
      excel:
        autoWidth: "Kontent asosida ustun kengligini avtomatik sozlang"
        protectSheet: "Ish varaqi himoyasini parol bilan yoqing: tableconvert.com"
      sql:
        primaryKey: "CREATE TABLE bayonoti uchun asosiy kalit maydon nomini belgilang"
        dialect: "Ma'lumotlar bazasi turini tanlang, tirnoq va ma'lumot turi sintaksisiga ta'sir qiladi"
      ascii:
        forceSep: "Har bir ma'lumot qatori orasida majburiy ajratuvchi chiziqlar"
        style: "ASCII jadval chegara chizish uslubini tanlang"
        comment: "Butun jadvalni o'rash uchun izoh belgilarini qo'shing"
      mediawiki:
        minify: "Qo'shimcha bo'sh joylarni olib tashlab, chiqish kodini siqing"
        header: "Birinchi qatorni sarlavha uslubi sifatida belgilang"
        sort: "Jadval bosish orqali saralash funksiyasini yoqing"
      asciidoc:
        minify: "AsciiDoc format chiqarishini siqing"
        firstHeader: "Birinchi qatorni sarlavha qatori sifatida o'rnating"
        lastFooter: "Oxirgi qatorni pastki qator sifatida o'rnating"
        title: "Jadvalga sarlavha matnini qo'shing"
      tracwiki:
        rowHeader: "Birinchi qatorni sarlavha sifatida o'rnating"
        colHeader: "Birinchi ustunni sarlavha sifatida o'rnating"
      bbcode:
        minify: "BBCode chiqish formatini siqing"
      restructuredtext:
        style: "reStructuredText jadval chegara uslubini tanlang"
        forceSep: "Majburiy ajratuvchi chiziqlar"
    label:
      ascii:
        forceSep: "Qator Ajratgichlari"
        style: "Chegara Uslubi"
        comment: "Izoh O'rash"
      restructuredtext:
        style: "Chegara Uslubi"
        forceSep: "Majburiy Ajratgichlar"
      bbcode:
        minify: "Chiqishni Siqish"
      csv:
        doubleQuote: "Qo'sh Tirnoq O'rash"
        delimiter: "Maydon Ajratgichi"
        bom: "UTF-8 BOM"
        valueDelimiter: "Qiymat Ajratgichi"
        rowDelimiter: "Qator Ajratgichi"
        prefix: "Qator Prefiksi"
        suffix: "Qator Suffiksi"
      excel:
        autoWidth: "Avtomatik Kenglik"
        textFormat: "Matn Formati"
        protectSheet: "Varakni Himoya Qilish"
        boldFirstRow: "Birinchi Qatorni Qalin Qilish"
        boldFirstColumn: "Birinchi Ustunni Qalin Qilish"
        sheetName: "Varaq Nomi"
      html:
        escape: "HTML Belgilarini Escape Qilish"
        div: "DIV Jadvali"
        minify: "Kodni Siqish"
        thead: "Jadval Boshi Tuzilmasi"
        tableCaption: "Jadval Sarlavhasi"
        tableClass: "Jadval Sinfi"
        tableId: "Jadval ID"
        rowHeader: "Qator Sarlavhasi"
        colHeader: "Ustun Sarlavhasi"
      jira:
        escape: "Belgilarni Escape Qilish"
        rowHeader: "Qator Sarlavhasi"
        colHeader: "Ustun Sarlavhasi"
      json:
        parsingJSON: "JSON ni Tahlil Qilish"
        minify: "Chiqishni Siqish"
        format: "Ma'lumot Formati"
        rootName: "Ildiz Obyekt Nomi"
        indentSize: "Satr Boshi O'lchami"
      jsonlines:
        parsingJSON: "JSON ni Tahlil Qilish"
        format: "Ma'lumot Formati"
      latex:
        escape: "LaTeX Jadval Belgilarini Escape Qilish"
        ht: "Suzuvchi Pozitsiya"
        mwe: "To'liq Hujjat"
        tableAlign: "Jadval Tekislash"
        tableBorder: "Chegara Uslubi"
        label: "Havola Yorlig'i"
        caption: "Jadval Sarlavhasi"
        location: "Sarlavha Pozitsiyasi"
        tableType: "Jadval Turi"
        boldFirstRow: "Birinchi Qatorni Qalin Qilish"
        boldFirstColumn: "Birinchi Ustunni Qalin Qilish"
        textAlign: "Matn Tekislash"
        borders: "Chegara Sozlamalari"
      markdown:
        escape: "Belgilarni Escape Qilish"
        pretty: "Chiroyli Markdown Jadvali"
        simple: "Oddiy Markdown Formati"
        boldFirstRow: "Birinchi Qatorni Qalin Qilish"
        boldFirstColumn: "Birinchi Ustunni Qalin Qilish"
        firstHeader: "Birinchi Sarlavha"
        textAlign: "Matn Tekislash"
        multilineHandling: "Ko'p Qatorli Boshqarish"

        includeLineNumbers: "Qator Raqamlarini Qo'shish"
        align: "Tekislash"
      mediawiki:
        minify: "Kodni Siqish"
        header: "Sarlavha Belgilash"
        sort: "Saralanuvchi"
      asciidoc:
        minify: "Formatni Siqish"
        firstHeader: "Birinchi Sarlavha"
        lastFooter: "Oxirgi Pastki Qism"
        title: "Jadval Sarlavhasi"
      tracwiki:
        rowHeader: "Qator Sarlavhasi"
        colHeader: "Ustun Sarlavhasi"
      sql:
        drop: "Jadvalni O'chirish (Agar Mavjud Bo'lsa)"
        create: "Jadval Yaratish"
        oneInsert: "Paket Kiritish"
        table: "Jadval Nomi"
        dialect: "Ma'lumotlar Bazasi Turi"
        primaryKey: "Asosiy Kalit"
      magic:
        builtin: "O'rnatilgan Shablon"
        rowsTpl: "Qator Shabloni, Sintaksis ->"
        headerTpl: "Sarlavha Shabloni"
        footerTpl: "Pastki Qism Shabloni"
      textile:
        escape: "Belgilarni Escape Qilish"
        rowHeader: "Qator Sarlavhasi"
        thead: "Jadval Bosh Sintaksisi"
      xml:
        escape: "XML Maxsus Belgilarini Escape Qilish"
        minify: "Chiqishni Siqish"
        rootElement: "Ildiz Element"
        rowElement: "Qator Elementi"
        declaration: "XML Deklaratsiyasi"
        attributes: "Atribut Rejimi"
        cdata: "CDATA O'rash"
        encoding: "Kodlash"
        indentSize: "Satr Boshi O'lchami"
      yaml:
        indentSize: "Satr Boshi O'lchami"
        arrayStyle: "Massiv Uslubi"
        quotationStyle: "Iqtibos Uslubi"
      pdf:
        theme: "PDF jadval mavzusi"
        headerColor: "PDF sarlavha rangi"
        showHead: "PDF sarlavha ko'rsatish"
        docTitle: "PDF hujjat sarlavhasi"
        docDescription: "PDF hujjat tavsifi"
sidebar:
  all: "Barcha konvertatsiya vositalari"
  dataSource:
    title: "Ma'lumot manbai"
    description:
      converter: "%s ni %s ga konvertatsiya qilish uchun import qiling. Fayl yuklash, onlayn tahrirlash va veb ma'lumotlarini chiqarishni qo'llab-quvvatlaydi."
      generator: "Qo'lda kiritish, fayl import va shablon yaratishni o'z ichiga olgan bir nechta kiritish usullarini qo'llab-quvvatlovchi jadval ma'lumotlarini yarating."
  tableEditor:
    title: "Onlayn jadval muharriri"
    description:
      converter: "Bizning jadval muharririmizdan foydalanib %s ni onlayn ravishda qayta ishlang. Bo'sh qatorlarni o'chirish, nusxa ko'chirishni olib tashlash, saralash va topish va almashtirishni qo'llab-quvvatlovchi Excel-ga o'xshash operatsiya tajribasi."
      generator: "Excel-ga o'xshash operatsiya tajribasini taqdim etuvchi kuchli onlayn jadval muharriri. Bo'sh qatorlarni o'chirish, nusxa ko'chirishni olib tashlash, saralash va topish va almashtirishni qo'llab-quvvatlaydi."
  tableGenerator:
    title: "Jadval generatori"
    description:
      converter: "Jadval generatorining real vaqt ko'rib chiqishi bilan %s ni tez yarating. Boy eksport variantlari, bir marta bosish orqali nusxalash va yuklab olish."
      generator: "Turli foydalanish stsenariylarini qondirish uchun %s ma'lumotlarini bir nechta formatlarda eksport qiling. Maxsus variantlar va real vaqt ko'rib chiqishni qo'llab-quvvatlaydi."
footer:
  changelog: "O'zgarishlar jurnali"
  sponsor: "Homiylar"
  contact: "Biz bilan bog'laning"
  privacyPolicy: "Maxfiylik siyosati"
  about: "Biz haqimizda"
  resources: "Manbalar"
  popularConverters: "Mashhur Konverterlar"
  popularGenerators: "Mashhur Generatorlar"
  dataSecurity: "Sizning ma'lumotlaringiz xavfsiz - barcha konvertatsiyalar brauzeringizda ishlaydi."
converters:
  Markdown:
    alias: "Markdown Jadvali"
    what: "Markdown texnik hujjatlar, blog kontenti yaratish va veb-ishlanma uchun keng qo'llaniladigan yengil belgilash tilidir. Uning jadval sintaksisi qisqa va tushunarli bo'lib, matn tekislash, havola joylash va formatlashni qo'llab-quvvatlaydi. Bu dasturchilar va texnik yozuvchilarning afzal qiladigan vositasi bo'lib, GitHub, GitLab va boshqa kod hosting platformalari bilan mukammal mos keladi."
    step1: "Markdown jadval ma'lumotlarini ma'lumot manbai hududiga joylashtiring yoki .md fayllarini to'g'ridan-to'g'ri tortib tashlang. Vosita avtomatik ravishda jadval tuzilishi va formatlanishini tahlil qiladi, murakkab ichma-ich kontent va maxsus belgilarni qayta ishlashni qo'llab-quvvatlaydi."
    step3: "Real vaqtda standart Markdown jadval kodini yarating, bir nechta tekislash usullari, matnni qalin qilish, qator raqamlarini qo'shish va boshqa ilg'or format sozlamalarini qo'llab-quvvatlaydi. Yaratilgan kod GitHub va asosiy Markdown muharrirlari bilan to'liq mos keladi, bir marta bosish bilan foydalanish uchun tayyor."
    from_alias: "Markdown Jadval Fayli"
    to_alias: "Markdown Jadval Formati"
  Magic:
    alias: "Maxsus Shablon"
    what: "Magic shablon ushbu vositaning noyob ilg'or ma'lumot generatori bo'lib, foydalanuvchilarga maxsus shablon sintaksisi orqali ixtiyoriy format ma'lumot chiqarish imkonini beradi. O'zgaruvchilarni almashtirish, shartli hukm qilish va tsikl qayta ishlashni qo'llab-quvvatlaydi. Bu murakkab ma'lumot konvertatsiya ehtiyojlari va shaxsiylashtirilgan chiqish formatlarini hal qilish uchun yakuniy yechim bo'lib, ayniqsa ishlab chiquvchilar va ma'lumot muhandislari uchun mos keladi."
    step1: "O'rnatilgan umumiy shablonlarni tanlang yoki maxsus shablon sintaksisini yarating. Murakkab ma'lumot tuzilmalari va biznes mantiqini qayta ishlay oladigan boy o'zgaruvchilar va funktsiyalarni qo'llab-quvvatlaydi."
    step3: "Maxsus format talablariga to'liq javob beradigan ma'lumot chiqarishini yarating. Murakkab ma'lumot konvertatsiya mantiqi va shartli qayta ishlashni qo'llab-quvvatlaydi, ma'lumot qayta ishlash samaradorligi va chiqish sifatini sezilarli darajada yaxshilaydi. Ommaviy ma'lumot qayta ishlash uchun kuchli vosita."
    from_alias: "Jadval Ma'lumotlari"
    to_alias: "Maxsus Format Chiqarish"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) eng keng qo'llaniladigan ma'lumot almashinuv formati bo'lib, Excel, Google Sheets, ma'lumotlar bazasi tizimlari va turli ma'lumot tahlili vositalari tomonidan mukammal qo'llab-quvvatlanadi. Uning oddiy tuzilishi va kuchli moslashuvchanlik xususiyatlari uni ma'lumot ko'chirish, ommaviy import/eksport va platformalararo ma'lumot almashinuvi uchun standart formatga aylantiradi, biznes tahlili, ma'lumot fanlari va tizim integratsiyasida keng qo'llaniladi."
    step1: "CSV fayllarini yuklang yoki to'g'ridan-to'g'ri CSV ma'lumotlarini joylashtiring. Vosita turli ajratgichlarni (vergul, tab, nuqta-vergul, quvur va boshqalar) aqlli tarzda taniydi, ma'lumot turlarini va kodlash formatlarini avtomatik aniqlaydi, katta fayllar va murakkab ma'lumot tuzilmalarini tez tahlil qilishni qo'llab-quvvatlaydi."
    step3: "Maxsus ajratgichlar, tirnoq uslublari, kodlash formatlari va BOM belgi sozlamalarini qo'llab-quvvatlagan holda standart CSV format fayllarini yarating. Maqsadli tizimlar bilan mukammal moslashuvni ta'minlaydi, korxona darajasidagi ma'lumot qayta ishlash ehtiyojlarini qondirish uchun yuklab olish va siqish variantlarini taqdim etadi."
    from_alias: "CSV Ma'lumot Fayli"
    to_alias: "CSV Standart Format"
  JSON:
    alias: "JSON Massivi"
    what: "JSON (JavaScript Object Notation) zamonaviy veb-ilovalar, REST API-lar va mikroservis arxitekturalari uchun standart jadval ma'lumot formatidir. Uning aniq tuzilishi va samarali tahlil qilish xususiyatlari uni frontend va backend ma'lumot o'zaro ta'siri, konfiguratsiya fayl saqlash va NoSQL ma'lumotlar bazalarida keng qo'llanilishiga olib keladi. Ichma-ich obyektlar, massiv tuzilmalari va ko'p ma'lumot turlarini qo'llab-quvvatlaydi, bu uni zamonaviy dasturiy ta'minot ishlab chiqish uchun ajralmas jadval ma'lumotiga aylantiradi."
    step1: "JSON fayllarini yuklang yoki JSON massivlarini joylashtiring. Obyekt massivlari, ichma-ich tuzilmalar va murakkab ma'lumot turlarini avtomatik tanish va tahlil qilishni qo'llab-quvvatlaydi. Vosita JSON sintaksisini aqlli tarzda tasdiqlaydi va xato ogohlantirishlarini beradi."
    step3: "Bir nechta JSON format chiqarishlarini yarating: standart obyekt massivlari, 2D massivlar, ustun massivlari va kalit-qiymat juftlik formatlari. Chiroyli chiqarish, siqish rejimi, maxsus ildiz obyekt nomlari va chekinish sozlamalarini qo'llab-quvvatlaydi, turli API interfeyslari va ma'lumot saqlash ehtiyojlariga mukammal moslashadi."
    from_alias: "JSON Massiv Fayli"
    to_alias: "JSON Standart Format"
  JSONLines:
    alias: "JSONLines Formati"
    what: "JSON Lines (NDJSON nomi bilan ham tanilgan) katta ma'lumotlarni qayta ishlash va oqim ma'lumotlarini uzatish uchun muhim format bo'lib, har bir qatorda mustaqil JSON obyekti mavjud. Log tahlili, ma'lumot oqimi qayta ishlash, mashinali o'rganish va taqsimlangan tizimlarda keng qo'llaniladi. Bosqichma-bosqich qayta ishlash va parallel hisoblashni qo'llab-quvvatlaydi, bu uni keng ko'lamli tuzilgan ma'lumotlarni qayta ishlash uchun ideal tanlovga aylantiradi."
    step1: "JSONLines fayllarini yuklang yoki ma'lumotlarni joylashtiring. Vosita JSON obyektlarini qator-qator tahlil qiladi, katta fayl oqimi qayta ishlash va xato qatorlarini o'tkazib yuborish funksiyasini qo'llab-quvvatlaydi."
    step3: "Har bir qatorda to'liq JSON obyektini chiqaradigan standart JSONLines formatini yarating. Oqim qayta ishlash, ommaviy import va katta ma'lumot tahlili stsenariylari uchun mos keladi, ma'lumot tekshirish va format optimallashtirish qo'llab-quvvatlanadi."
    from_alias: "JSONLines Ma'lumotlari"
    to_alias: "JSONLines Oqim Formati"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) korxona darajasidagi ma'lumot almashinuvi va konfiguratsiya boshqaruvi uchun standart format bo'lib, qat'iy sintaksis spetsifikatsiyalari va kuchli tekshirish mexanizmlariga ega. Veb-xizmatlar, konfiguratsiya fayllari, hujjat saqlash va tizim integratsiyasida keng qo'llaniladi. Nom maydonlari, sxema tekshiruvi va XSLT transformatsiyasini qo'llab-quvvatlaydi, bu uni korxona ilovalari uchun muhim jadval ma'lumotiga aylantiradi."
    step1: "XML fayllarini yuklang yoki XML ma'lumotlarini joylashtiring. Vosita avtomatik ravishda XML tuzilishini tahlil qiladi va uni jadval formatiga aylantiradi, nom maydonlari, atribut qayta ishlash va murakkab ichma-ich tuzilmalarni qo'llab-quvvatlaydi."
    step3: "XML standartlariga mos XML chiqarishini yarating. Maxsus ildiz elementlari, qator element nomlari, atribut rejimlari, CDATA o'rash va belgi kodlash sozlamalarini qo'llab-quvvatlaydi. Ma'lumot yaxlitligi va moslashuvni ta'minlaydi, korxona darajasidagi ilova talablarini qondiradi."
    from_alias: "XML Ma'lumot Fayli"
    to_alias: "XML Standart Format"
  YAML:
    alias: "YAML Konfiguratsiyasi"
    what: "YAML inson uchun qulay ma'lumot serializatsiya standarti bo'lib, aniq ierarxik tuzilishi va qisqa sintaksisi bilan mashhur. Konfiguratsiya fayllari, DevOps vosita zanjirlari, Docker Compose va Kubernetes joylashtirishda keng qo'llaniladi. Uning kuchli o'qilishi va qisqa sintaksisi uni zamonaviy bulutga asoslangan ilovalar va avtomatlashtirilgan operatsiyalar uchun muhim konfiguratsiya formatiga aylantiradi."
    step1: "YAML fayllarini yuklang yoki YAML ma'lumotlarini joylashtiring. Vosita YAML tuzilishini aqlli tarzda tahlil qiladi va sintaksis to'g'riligini tekshiradi, ko'p hujjat formatlari va murakkab ma'lumot turlarini qo'llab-quvvatlaydi."
    step3: "Blok va oqim massiv uslublari, ko'p tirnoq sozlamalari, maxsus chekinish va izohlarni saqlashni qo'llab-quvvatlagan holda standart YAML format chiqarishini yarating. Chiqarish YAML fayllari turli tahlilchilar va konfiguratsiya tizimlari bilan to'liq mos kelishini ta'minlaydi."
    from_alias: "YAML Konfiguratsiya Fayli"
    to_alias: "YAML Standart Format"
  MySQL:
      alias: "MySQL So'rov Natijalari"
      what: "MySQL dunyodagi eng mashhur ochiq manbali relyatsion ma'lumotlar bazasi boshqaruv tizimi bo'lib, yuqori unumdorligi, ishonchliligi va foydalanish qulayligi bilan mashhur. Veb-ilovalar, korxona tizimlari va ma'lumot tahlili platformalarida keng qo'llaniladi. MySQL so'rov natijalari odatda tuzilgan jadval ma'lumotlarini o'z ichiga oladi va ma'lumotlar bazasi boshqaruvi va ma'lumot tahlili ishlarida muhim ma'lumot manbai bo'lib xizmat qiladi."
      step1: "MySQL so'rov chiqarish natijalarini ma'lumot manbai hududiga joylashtiring. Vosita avtomatik ravishda MySQL buyruq qatori chiqarish formatini taniydi va tahlil qiladi, turli so'rov natija uslublari va belgi kodlashlarini qo'llab-quvvatlaydi, sarlavhalar va ma'lumot qatorlarini aqlli tarzda qayta ishlaydi."
      step3: "MySQL so'rov natijalarini bir nechta jadval ma'lumot formatlariga tez aylantiring, ma'lumot tahlili, hisobot yaratish, tizimlararo ma'lumot ko'chirish va ma'lumot tekshirishni osonlashtiradi. Ma'lumotlar bazasi ma'murlari va ma'lumot tahlilchilari uchun amaliy vosita."
      from_alias: "MySQL So'rov Chiqarish"
      to_alias: "MySQL Jadval Ma'lumotlari"
  SQL:
    alias: "SQL Kiritish"
    what: "SQL (Structured Query Language) relyatsion ma'lumotlar bazalari uchun standart operatsiya tili bo'lib, ma'lumotlarni so'rash, kiritish, yangilash va o'chirish operatsiyalari uchun ishlatiladi. Ma'lumotlar bazasi boshqaruvining asosiy texnologiyasi sifatida SQL ma'lumot tahlili, biznes intellekti, ETL qayta ishlash va ma'lumotlar omborini qurishda keng qo'llaniladi. Bu ma'lumot mutaxassislari uchun muhim ko'nikma vositasidir."
    step1: "INSERT SQL bayonotlarini joylashtiring yoki .sql fayllarini yuklang. Vosita SQL sintaksisini aqlli tarzda tahlil qiladi va jadval ma'lumotlarini ajratib oladi, bir nechta SQL dialektlari va murakkab so'rov bayonoti qayta ishlashni qo'llab-quvvatlaydi."
    step3: "Standart SQL INSERT bayonotlari va jadval yaratish bayonotlarini yarating. Bir nechta ma'lumotlar bazasi dialektlarini (MySQL, PostgreSQL, SQLite, SQL Server, Oracle) qo'llab-quvvatlaydi, ma'lumot turi xaritalash, belgilarni qochish va asosiy kalit cheklovlarini avtomatik qayta ishlaydi. Yaratilgan SQL kodini to'g'ridan-to'g'ri bajarish mumkinligini ta'minlaydi."
    from_alias: "SQL Ma'lumot Fayli"
    to_alias: "SQL Standart Bayonot"
  Qlik:
      alias: "Qlik Jadvali"
      what: "Qlik ma'lumot vizualizatsiyasi, boshqaruv panellari va o'z-o'ziga xizmat ko'rsatuvchi biznes intellekt mahsulotlarida ixtisoslashgan dasturiy ta'minot yetkazib beruvchisi bo'lib, Tableau va Microsoft bilan birga."
      step1: ""
      step3: "Nihoyat, [Jadval Generatori](#TableGenerator) konvertatsiya natijalarini ko'rsatadi. Qlik Sense, Qlik AutoML, QlikView yoki boshqa Qlik-ni qo'llab-quvvatlovchi dasturlarda foydalaning."
      from_alias: "Qlik Jadvali"
      to_alias: "Qlik Jadvali"
  DAX:
      alias: "DAX Jadvali"
      what: "DAX (Data Analysis Expressions) Microsoft Power BI'da hisoblangan ustunlar, o'lchovlar va maxsus jadvallar yaratish uchun ishlatiladigan dasturlash tilidir."
      step1: ""
      step3: "Nihoyat, [Jadval Generatori](#TableGenerator) konvertatsiya natijalarini ko'rsatadi. Kutilganidek, u Microsoft Power BI, Microsoft Analysis Services va Excel uchun Microsoft Power Pivot kabi bir nechta Microsoft mahsulotlarida ishlatiladi."
      from_alias: "DAX Jadvali"
      to_alias: "DAX Jadvali"
  Firebase:
    alias: "Firebase Ro'yxati"
    what: "Firebase real vaqt ma'lumotlar bazasi, bulut saqlash, autentifikatsiya, xato hisoboti va boshqalar kabi hosting backend xizmatlarini taqdim etuvchi BaaS ilova ishlab chiqish platformasidir."
    step1: ""
    step3: "Nihoyat, [Jadval Generatori](#TableGenerator) konvertatsiya natijalarini ko'rsatadi. Keyin Firebase ma'lumotlar bazasidagi ma'lumotlar ro'yxatiga qo'shish uchun Firebase API'da push usulidan foydalanishingiz mumkin."
    from_alias: "Firebase Ro'yxati"
    to_alias: "Firebase Ro'yxati"
  HTML:
    alias: "HTML Jadvali"
    what: "HTML jadvallari veb-sahifalarda tuzilgan ma'lumotlarni ko'rsatishning standart usuli bo'lib, table, tr, td va boshqa teglar bilan quriladi. Boy uslub sozlamalari, javob beruvchi tartib va interaktiv funksionallikni qo'llab-quvvatlaydi. Veb-sayt ishlab chiqish, ma'lumotlarni ko'rsatish va hisobot yaratishda keng qo'llaniladi, frontend ishlab chiqish va veb-dizaynning muhim komponenti sifatida xizmat qiladi."
    step1: "Jadvallar bo'lgan HTML kodini joylashtiring yoki HTML fayllarini yuklang. Vosita avtomatik ravishda sahifalardan jadval ma'lumotlarini taniydi va ajratib oladi, murakkab HTML tuzilmalari, CSS uslublari va ichma-ich jadval qayta ishlashni qo'llab-quvvatlaydi."
    step3: "thead/tbody tuzilmasi, CSS sinf sozlamalari, jadval sarlavhalari, qator/ustun sarlavhalari va javob beruvchi atribut konfiguratsiyasini qo'llab-quvvatlagan holda semantik HTML jadval kodini yarating. Yaratilgan jadval kodi yaxshi kirish imkoniyati va SEO do'stligi bilan veb-standartlarga javob berishini ta'minlaydi."
    from_alias: "HTML Veb Jadvali"
    to_alias: "HTML Standart Jadval"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel dunyodagi eng mashhur elektron jadval dasturidir, biznes tahlili, moliyaviy boshqaruv, ma'lumot qayta ishlash va hisobot yaratishda keng qo'llaniladi. Uning kuchli ma'lumot qayta ishlash imkoniyatlari, boy funksiya kutubxonasi va moslashuvchan vizualizatsiya xususiyatlari uni ofis avtomatlashtirish va ma'lumot tahlili uchun standart vositaga aylantiradi, deyarli barcha sohalar va sohalarda keng qo'llaniladi."
    step1: "Excel fayllarini yuklang (.xlsx, .xls formatlarini qo'llab-quvvatlaydi) yoki jadval ma'lumotlarini to'g'ridan-to'g'ri Excel'dan nusxalab joylashtiring. Vosita ko'p ish varaqlari qayta ishlash, murakkab format tanish va katta fayllarni tez tahlil qilishni qo'llab-quvvatlaydi, birlashtirilgan kataklar va ma'lumot turlarini avtomatik qayta ishlaydi."
    step3: "To'g'ridan-to'g'ri Excel'ga joylashtirilishi yoki standart .xlsx fayllari sifatida yuklab olinishi mumkin bo'lgan Excel bilan mos jadval ma'lumotlarini yarating. Ish varaqlari nomlash, katak formatlash, avtomatik ustun kengligi, sarlavha uslubi va ma'lumot tekshirish sozlamalarini qo'llab-quvvatlaydi. Chiqarish Excel fayllari professional ko'rinish va to'liq funksionallikka ega bo'lishini ta'minlaydi."
    from_alias: "Excel Elektron Jadvali"
    to_alias: "Excel Standart Format"
  LaTeX:
    alias: "LaTeX Jadvali"
    what: "LaTeX professional hujjat sahifalash tizimi bo'lib, ayniqsa akademik maqolalar, texnik hujjatlar va ilmiy nashrlar yaratish uchun mos keladi. Uning jadval funksiyasi kuchli bo'lib, murakkab matematik formulalar, aniq tartib nazorati va yuqori sifatli PDF chiqarishni qo'llab-quvvatlaydi. Bu akademiya va ilmiy nashriyotda standart vosita bo'lib, jurnal maqolalari, dissertatsiyalar va texnik qo'llanma sahifalashda keng qo'llaniladi."
    step1: "LaTeX jadval kodini joylashtiring yoki .tex fayllarini yuklang. Vosita LaTeX jadval sintaksisini tahlil qiladi va ma'lumot mazmunini ajratib oladi, bir nechta jadval muhitlari (tabular, longtable, array va boshqalar) va murakkab format buyruqlarini qo'llab-quvvatlaydi."
    step3: "Bir nechta jadval muhiti tanlash, chegara uslubi konfiguratsiyasi, sarlavha pozitsiyasi sozlamalari, hujjat sinfi spetsifikatsiyasi va paket boshqaruvini qo'llab-quvvatlagan holda professional LaTeX jadval kodini yarating. To'liq kompilyatsiya qilinadigan LaTeX hujjatlarini yaratishi mumkin, chiqarish jadvallari akademik nashriyot standartlariga javob berishini ta'minlaydi."
    from_alias: "LaTeX Hujjat Jadvali"
    to_alias: "LaTeX Professional Format"
  ASCII:
    alias: "ASCII Jadvali"
    what: "ASCII jadvallari jadval chegaralari va tuzilmalarini chizish uchun oddiy matn belgilaridan foydalanadi, eng yaxshi moslashuvchilik va ko'chma xususiyatni ta'minlaydi. Barcha matn muharrirlari, terminal muhitlari va operatsion tizimlar bilan mos keladi. Kod hujjatlari, texnik qo'llanmalar, README fayllari va buyruq qatori vosita chiqarishida keng qo'llaniladi. Dasturchilar va tizim ma'murlari uchun afzal qilingan ma'lumot ko'rsatish formati."
    step1: "ASCII jadvallari bo'lgan matn fayllarini yuklang yoki to'g'ridan-to'g'ri jadval ma'lumotlarini joylashtiring. Vosita ASCII jadval tuzilmalarini aqlli tarzda taniydi va tahlil qiladi, bir nechta chegara uslublari va tekislash formatlarini qo'llab-quvvatlaydi."
    step3: "Bir nechta chegara uslublari (bitta chiziq, ikki chiziq, yumaloq burchaklar va boshqalar), matn tekislash usullari va avtomatik ustun kengligini qo'llab-quvvatlagan holda chiroyli oddiy matn ASCII jadvallarini yarating. Yaratilgan jadvallar kod muharrirlari, hujjatlar va buyruq qatorlarida mukammal ko'rsatiladi."
    from_alias: "ASCII Matn Jadvali"
    to_alias: "ASCII Standart Format"
  MediaWiki:
    alias: "MediaWiki Jadvali"
    what: "MediaWiki Wikipedia kabi mashhur viki saytlar tomonidan ishlatiladigan ochiq manbali dasturiy ta'minot platformasidir. Uning jadval sintaksisi qisqa, ammo kuchli bo'lib, jadval uslubini sozlash, saralash funksiyalari va havolalarni joylashtirish imkoniyatlarini qo'llab-quvvatlaydi. Bilim boshqaruvi, hamkorlikdagi tahrirlash va kontent boshqaruv tizimlarida keng qo'llaniladi, viki ensiklopediyalar va bilim bazalarini yaratish uchun asosiy texnologiya sifatida xizmat qiladi."
    step1: "MediaWiki jadval kodini joylashtiring yoki viki manba fayllarini yuklang. Vosita viki belgilash sintaksisini tahlil qiladi va jadval ma'lumotlarini chiqaradi, murakkab viki sintaksisi va shablon qayta ishlashni qo'llab-quvvatlaydi."
    step3: "Sarlavha uslubi sozlamalari, katak hizalanishi, saralash funksiyasini yoqish va kod siqish variantlarini qo'llab-quvvatlovchi standart MediaWiki jadval kodini yarating. Yaratilgan kod to'g'ridan-to'g'ri viki sahifa tahrirlash uchun ishlatilishi mumkin, MediaWiki platformalarida mukammal ko'rsatishni ta'minlaydi."
    from_alias: "MediaWiki Manba Kodi"
    to_alias: "MediaWiki Jadval Sintaksisi"
  TracWiki:
    alias: "TracWiki Jadvali"
    what: "Trac jadval kontentini yaratish uchun soddalashtirilgan viki sintaksisidan foydalanadigan veb-asosli loyiha boshqaruvi va xato kuzatuv tizimidir."
    step1: "TracWiki fayllarini yuklang yoki jadval ma'lumotlarini joylashtiring."
    step3: "Qator/ustun sarlavha sozlamalarini qo'llab-quvvatlovchi TracWiki-ga mos jadval kodini yarating, loyiha hujjat boshqaruvini osonlashtiradi."
    from_alias: "TracWiki Jadvali"
    to_alias: "TracWiki Formati"
  AsciiDoc:
    alias: "AsciiDoc Jadvali"
    what: "AsciiDoc HTML, PDF, qo'llanma sahifalari va boshqa formatlarga aylantirilishi mumkin bo'lgan yengil belgilash tili bo'lib, texnik hujjat yozishda keng qo'llaniladi."
    step1: "AsciiDoc fayllarini yuklang yoki ma'lumotlarni joylashtiring."
    step3: "Sarlavha, pastki qism va sarlavha sozlamalarini qo'llab-quvvatlovchi AsciiDoc jadval sintaksisini yarating, to'g'ridan-to'g'ri AsciiDoc muharrirlarida foydalanish mumkin."
    from_alias: "AsciiDoc Jadvali"
    to_alias: "AsciiDoc Formati"
  reStructuredText:
    alias: "reStructuredText Jadvali"
    what: "reStructuredText Python hamjamiyati uchun standart hujjat formati bo'lib, boy jadval sintaksisini qo'llab-quvvatlaydi, odatda Sphinx hujjat yaratishda ishlatiladi."
    step1: ".rst fayllarini yuklang yoki reStructuredText ma'lumotlarini joylashtiring."
    step3: "Bir nechta chegara uslublarini qo'llab-quvvatlovchi standart reStructuredText jadvallarini yarating, to'g'ridan-to'g'ri Sphinx hujjat loyihalarida foydalanish mumkin."
    from_alias: "reStructuredText Jadvali"
    to_alias: "reStructuredText Formati"
  PHP:
    alias: "PHP Massivi"
    what: "PHP mashhur server tomonidagi skript tili bo'lib, massivlar uning asosiy ma'lumot tuzilmasi hisoblanadi, veb-ishlanma va ma'lumot qayta ishlashda keng qo'llaniladi."
    step1: "PHP massivlari bo'lgan fayllarni yuklang yoki ma'lumotlarni to'g'ridan-to'g'ri joylashtiring."
    step3: "PHP loyihalarida to'g'ridan-to'g'ri ishlatilishi mumkin bo'lgan standart PHP massiv kodini yarating, assotsiativ va indeksli massiv formatlarini qo'llab-quvvatlaydi."
    from_alias: "PHP Massivi"
    to_alias: "PHP Kodi"
  Ruby:
    alias: "Ruby Massivi"
    what: "Ruby qisqa va nafis sintaksisga ega dinamik obyektga yo'naltirilgan dasturlash tili bo'lib, massivlar muhim ma'lumot tuzilmasi hisoblanadi."
    step1: "Ruby fayllarini yuklang yoki massiv ma'lumotlarini joylashtiring."
    step3: "Ruby sintaksis spetsifikatsiyalariga mos keluvchi Ruby massiv kodini yarating, to'g'ridan-to'g'ri Ruby loyihalarida foydalanish mumkin."
    from_alias: "Ruby Massivi"
    to_alias: "Ruby Kodi"
  ASP:
    alias: "ASP Massivi"
    what: "ASP (Active Server Pages) Microsoft'ning server tomonidagi skript muhiti bo'lib, dinamik veb-sahifalar ishlab chiqish uchun bir nechta dasturlash tillarini qo'llab-quvvatlaydi."
    step1: "ASP fayllarini yuklang yoki massiv ma'lumotlarini joylashtiring."
    step3: "VBScript va JScript sintaksisini qo'llab-quvvatlovchi ASP-ga mos massiv kodini yarating, ASP.NET loyihalarida foydalanish mumkin."
    from_alias: "ASP Massivi"
    to_alias: "ASP Kodi"
  ActionScript:
    alias: "ActionScript Massivi"
    what: "ActionScript asosan Adobe Flash va AIR ilova ishlab chiqish uchun ishlatiladigan obyektga yo'naltirilgan dasturlash tilidir."
    step1: ".as fayllarini yuklang yoki ActionScript ma'lumotlarini joylashtiring."
    step3: "AS3 sintaksis standartlariga mos keluvchi ActionScript massiv kodini yarating, Flash va Flex loyiha ishlab chiqishda foydalanish mumkin."
    from_alias: "ActionScript Massivi"
    to_alias: "ActionScript Kodi"
  BBCode:
    alias: "BBCode Jadvali"
    what: "BBCode forumlar va onlayn hamjamiyatlarda keng qo'llaniladigan yengil belgilash tili bo'lib, jadval qo'llab-quvvatlash jumladan oddiy formatlash funksiyalarini taqdim etadi."
    step1: "BBCode bo'lgan fayllarni yuklang yoki ma'lumotlarni joylashtiring."
    step3: "Forum joylash va hamjamiyat kontenti yaratish uchun mos BBCode jadval kodini yarating, siqilgan chiqish formatini qo'llab-quvvatlaydi."
    from_alias: "BBCode Jadvali"
    to_alias: "BBCode Formati"
  PDF:
    alias: "PDF Jadvali"
    what: "PDF (Portable Document Format) sobit tartib, izchil ko'rsatish va yuqori sifatli chop etish xususiyatlariga ega bo'lgan platformalararo hujjat standartidir. Rasmiy hujjatlar, hisobotlar, hisob-fakturalar, shartnomalar va akademik maqolalarda keng qo'llaniladi. Biznes aloqasi va hujjat arxivlash uchun afzal qilingan format bo'lib, turli qurilmalar va operatsion tizimlar bo'ylab mutlaqo izchil vizual effektlarni ta'minlaydi."
    step1: "Har qanday formatdagi jadval ma'lumotlarini import qiling. Vosita avtomatik ravishda ma'lumot tuzilishini tahlil qiladi va aqlli tartib dizaynini amalga oshiradi, katta jadval avtomatik sahifalash va murakkab ma'lumot turi qayta ishlashni qo'llab-quvvatlaydi."
    step3: "Bir nechta professional mavzu uslublari (biznes, akademik, minimalist va boshqalar), ko'p tilli shriftlar, avtomatik sahifalash, suv belgisi qo'shish va chop etish optimallashtirish qo'llab-quvvatlash bilan yuqori sifatli PDF jadval fayllarini yarating. Chiqarish PDF hujjatlari professional ko'rinishga ega bo'lishini ta'minlaydi, to'g'ridan-to'g'ri biznes taqdimotlari va rasmiy nashr uchun foydalanish mumkin."
    from_alias: "Jadval Ma'lumotlari"
    to_alias: "PDF Professional Hujjat"
  JPEG:
    alias: "JPEG Rasmi"
    what: "JPEG ajoyib siqish effektlari va keng moslashuvchanligi bilan eng keng qo'llaniladigan raqamli rasm formatidir. Uning kichik fayl hajmi va tez yuklash tezligi uni veb-ko'rsatish, ijtimoiy media ulashish, hujjat illyustratsiyalari va onlayn taqdimotlar uchun mos qiladi. Raqamli media va tarmoq aloqasi uchun standart rasm formati bo'lib, deyarli barcha qurilmalar va dasturiy ta'minot tomonidan mukammal qo'llab-quvvatlanadi."
    step1: "Har qanday formatdagi jadval ma'lumotlarini import qiling. Vosita aqlli tartib dizayni va vizual optimallashtirish amalga oshiradi, avtomatik ravishda optimal o'lcham va rezolyutsiyani hisoblab chiqadi."
    step3: "Bir nechta mavzu rang sxemalari (yorug', qorong'u, ko'zga qulay va boshqalar), moslashuvchan tartib, matn aniqligi optimallashtirish va o'lcham sozlash qo'llab-quvvatlash bilan yuqori aniqlikdagi JPEG jadval rasmlarini yarating. Onlayn ulashish, hujjat kiritish va taqdimot foydalanish uchun mos, turli ko'rsatish qurilmalarida ajoyib vizual effektlarni ta'minlaydi."
    from_alias: "Jadval Ma'lumotlari"
    to_alias: "JPEG Yuqori Aniqlikdagi Rasm"
  Jira:
    alias: "Jira Jadvali"
    what: "JIRA Atlassian tomonidan ishlab chiqilgan professional loyiha boshqaruvi va xato kuzatuv dasturidir, agile rivojlanish, dasturiy ta'minot testlash va loyiha hamkorligida keng qo'llaniladi. Uning jadval funksiyasi boy formatlash variantlari va ma'lumotlarni ko'rsatishni qo'llab-quvvatlaydi, dasturiy ta'minot ishlab chiqish jamoalari, loyiha menejerlari va sifat ta'minoti xodimlari uchun talablarni boshqarish, xatolarni kuzatish va taraqqiyot hisobotlarida muhim vosita sifatida xizmat qiladi."
    step1: "Jadval ma'lumotlari bo'lgan fayllarni yuklang yoki to'g'ridan-to'g'ri ma'lumot mazmunini joylashtiring. Vosita avtomatik ravishda jadval ma'lumotlari va maxsus belgilarni escape qayta ishlaydi."
    step3: "JIRA platformasi bilan mos jadval kodini yarating, u sarlavha uslubi sozlamalari, katak hizalanishi, belgilarni escape qayta ishlash va format optimallashtirish qo'llab-quvvatlaydi. Yaratilgan kod to'g'ridan-to'g'ri JIRA muammo tavsiflari, izohlar yoki viki sahifalariga joylashtirilishi mumkin, bu JIRA tizimlarida to'g'ri ko'rsatish va render qilishni ta'minlaydi."
    from_alias: "Loyiha Ma'lumotlari"
    to_alias: "Jira Jadval Sintaksisi"
  Textile:
    alias: "Textile Jadvali"
    what: "Textile ixcham yengil belgilash tili bo'lib, oddiy va o'rganish oson sintaksisga ega, kontent boshqaruv tizimlarida, blog platformalarida va forum tizimlarida keng qo'llaniladi. Uning jadval sintaksisi aniq va intuitiv bo'lib, tez formatlash va uslub sozlamalarini qo'llab-quvvatlaydi. Tez hujjat yozish va kontent nashr qilish uchun kontent yaratuvchilar va veb-sayt ma'murlari uchun ideal vosita."
    step1: "Textile format fayllarini yuklang yoki jadval ma'lumotlarini joylashtiring. Vosita Textile belgilash sintaksisini tahlil qiladi va jadval mazmunini chiqaradi."
    step3: "Standart Textile jadval sintaksisini yarating, u sarlavha belgilash, katak hizalanishi, maxsus belgilarni escape qilish va format optimallashtirish qo'llab-quvvatlaydi. Yaratilgan kod to'g'ridan-to'g'ri CMS tizimlarida, blog platformalarida va Textile-ni qo'llab-quvvatlovchi hujjat tizimlarida ishlatilishi mumkin, bu to'g'ri kontent renderi va ko'rsatishni ta'minlaydi."
    from_alias: "Textile Hujjati"
    to_alias: "Textile Jadval Sintaksisi"
  PNG:
    alias: "PNG Rasmi"
    what: "PNG (Portable Network Graphics) ajoyib siqish va shaffoflik qo'llab-quvvatlash bilan zararli rasm formatidir. Veb-dizayn, raqamli grafika va professional fotografiyada keng qo'llaniladi. Uning yuqori sifati va keng moslashuvchanligi uni ekran rasmlari, logotiplar, diagrammalar va aniq tafsilotlar va shaffof fonni talab qiladigan har qanday rasmlar uchun ideal qiladi."
    step1: "Har qanday formatdagi jadval ma'lumotlarini import qiling. Vosita aqlli tartib dizayni va vizual optimallashtirish amalga oshiradi, PNG chiqishi uchun avtomatik ravishda optimal o'lcham va rezolyutsiyani hisoblab chiqadi."
    step3: "Yuqori sifatli PNG jadval rasmlarini yarating, ular bir necha mavzu rang sxemalari, shaffof fonlar, moslashuvchan tartib va matn aniqligi optimallashtirish qo'llab-quvvatlaydi. Veb foydalanish, hujjat kiritish va ajoyib vizual sifat bilan professional taqdimotlar uchun mukammal."
    from_alias: "Jadval Ma'lumotlari"
    to_alias: "PNG Yuqori Sifatli Rasm"
  TOML:
    alias: "TOML Konfiguratsiyasi"
    what: "TOML (Tom's Obvious, Minimal Language) o'qish va yozish oson bo'lgan konfiguratsiya fayl formatidir. Noaniq va oddiy bo'lish uchun mo'ljallangan, u konfiguratsiya boshqaruvi uchun zamonaviy dasturiy ta'minot loyihalarida keng qo'llaniladi. Uning aniq sintaksisi va kuchli tipizatsiya uni ilova sozlamalari va loyiha konfiguratsiya fayllari uchun ajoyib tanlov qiladi."
    step1: "TOML fayllarini yuklang yoki konfiguratsiya ma'lumotlarini joylashtiring. Vosita TOML sintaksisini tahlil qiladi va tuzilgan konfiguratsiya ma'lumotlarini chiqaradi."
    step3: "Ichki joylashgan tuzilmalar, ma'lumot turlari va izohlarni qo'llab-quvvatlovchi standart TOML formatini yarating. Yaratilgan TOML fayllari ilova konfiguratsiyasi, qurilish vositalari va loyiha sozlamalari uchun mukammaldir."
    from_alias: "TOML Konfiguratsiyasi"
    to_alias: "TOML Formati"
  INI:
    alias: "INI Konfiguratsiyasi"
    what: "INI fayllari ko'plab ilovalar va operatsion tizimlar tomonidan ishlatiladigan oddiy konfiguratsiya fayllaridir. Ularning to'g'ridan-to'g'ri kalit-qiymat juftlik tuzilishi ularni qo'lda o'qish va tahrirlashni osonlashtiradi. Windows ilovalarida, eski tizimlarda va insoniy o'qish muhim bo'lgan oddiy konfiguratsiya stsenalariylarida keng qo'llaniladi."
    step1: "INI fayllarini yuklang yoki konfiguratsiya ma'lumotlarini joylashtiring. Vosita INI sintaksisini tahlil qiladi va bo'limga asoslangan konfiguratsiya ma'lumotlarini chiqaradi."
    step3: "Bo'limlar, izohlar va turli ma'lumot turlarini qo'llab-quvvatlovchi standart INI formatini yarating. Yaratilgan INI fayllari ko'pchilik ilovalar va konfiguratsiya tizimlari bilan mos keladi."
    from_alias: "INI Konfiguratsiyasi"
    to_alias: "INI Formati"
  Avro:
    alias: "Avro Sxemasi"
    what: "Apache Avro boy ma'lumot tuzilmalari, ixcham ikkilik format va sxema evolyutsiya imkoniyatlarini ta'minlovchi ma'lumot serializatsiya tizimidir. Katta ma'lumotlarni qayta ishlash, xabar navbatlari va taqsimlangan tizimlarda keng qo'llaniladi. Uning sxema ta'rifi murakkab ma'lumot turlarini va versiya mosligini qo'llab-quvvatlaydi, bu uni ma'lumot muhandislari va tizim me'morları uchun muhim vosita qiladi."
    step1: "Avro sxema fayllarini yuklang yoki ma'lumotlarni joylashtiring. Vosita Avro sxema ta'riflarini tahlil qiladi va jadval tuzilmasi ma'lumotlarini chiqaradi."
    step3: "Ma'lumot turi kartalashtirish, maydon cheklovlari va sxema tekshiruvini qo'llab-quvvatlovchi standart Avro sxema ta'riflarini yarating. Yaratilgan sxemalar to'g'ridan-to'g'ri Hadoop ekotizimlarida, Kafka xabar tizimlarida va boshqa katta ma'lumot platformalarida ishlatilishi mumkin."
    from_alias: "Avro Sxemasi"
    to_alias: "Avro Ma'lumot Formati"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) Google'ning tilga nisbatan neytral, platformaga nisbatan neytral, tuzilgan ma'lumotlarni serializatsiya qilish uchun kengaytiriladigan mexanizmidir. Mikroservislar, API rivojlanishi va ma'lumotlarni saqlashda keng qo'llaniladi. Uning samarali ikkilik formati va kuchli tipizatsiya uni yuqori unumdorlik ilovalar va tillararo aloqa uchun ideal qiladi."
    step1: ".proto fayllarini yuklang yoki Protocol Buffer ta'riflarini joylashtiring. Vosita protobuf sintaksisini tahlil qiladi va xabar tuzilmasi ma'lumotlarini chiqaradi."
    step3: "Xabar turlari, maydon variantlari va xizmat ta'riflarini qo'llab-quvvatlovchi standart Protocol Buffer ta'riflarini yarating. Yaratilgan .proto fayllari bir nechta dasturlash tillari uchun kompilyatsiya qilinishi mumkin."
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf Sxemasi"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas Python-dagi eng mashhur ma'lumot tahlili kutubxonasi bo'lib, DataFrame uning asosiy ma'lumot tuzilmasidir. U kuchli ma'lumotlarni manipulyatsiya qilish, tozalash va tahlil qilish imkoniyatlarini ta'minlaydi, ma'lumot fanlari, mashinani o'rganish va biznes intellektida keng qo'llaniladi. Python ishlab chiquvchilari va ma'lumot tahlilchilari uchun ajralmas vosita."
    step1: "DataFrame kodi bo'lgan Python fayllarini yuklang yoki ma'lumotlarni joylashtiring. Vosita Pandas sintaksisini tahlil qiladi va DataFrame tuzilmasi ma'lumotlarini chiqaradi."
    step3: "Ma'lumot turi spetsifikatsiyalari, indeks sozlamalari va ma'lumot operatsiyalarini qo'llab-quvvatlovchi standart Pandas DataFrame kodini yarating. Yaratilgan kod to'g'ridan-to'g'ri Python muhitida ma'lumot tahlili va qayta ishlash uchun bajarilishi mumkin."
    from_alias: "Pandas DataFrame"
    to_alias: "Python Ma'lumot Tuzilmasi"
  RDF:
    alias: "RDF Uch Barobar"
    what: "RDF (Resource Description Framework) Vebda ma'lumot almashuvi uchun standart model bo'lib, resurslar haqidagi ma'lumotlarni grafik shaklda ifodalash uchun mo'ljallangan. Semantik veb, bilim graflari va bog'langan ma'lumot ilovalarida keng qo'llaniladi. Uning uch barobar tuzilmasi boy metadata vakillik va semantik munosabatlarni ta'minlaydi."
    step1: "RDF fayllarini yuklang yoki uch barobar ma'lumotlarni joylashtiring. Vosita RDF sintaksisini tahlil qiladi va semantik munosabatlar va resurs ma'lumotlarini chiqaradi."
    step3: "Turli serializatsiyalarni (RDF/XML, Turtle, N-Triples) qo'llab-quvvatlovchi standart RDF formatini yarating. Yaratilgan RDF semantik veb ilovalar, bilim bazalari va bog'langan ma'lumot tizimlarida ishlatilishi mumkin."
    from_alias: "RDF Ma'lumotlari"
    to_alias: "RDF Semantik Format"
  MATLAB:
    alias: "MATLAB Massivi"
    what: "MATLAB muhandislik hisoblash, ma'lumot tahlili va algoritm rivojlanishida keng qo'llaniladigan yuqori unumdorlik raqamli hisoblash va vizualizatsiya dasturidir. Uning massiv va matritsa operatsiyalari kuchli bo'lib, murakkab matematik hisob-kitoblar va ma'lumotlarni qayta ishlashni qo'llab-quvvatlaydi. Muhandislar, tadqiqotchilar va ma'lumot olimlari uchun muhim vosita."
    step1: "MATLAB .m fayllarini yuklang yoki massiv ma'lumotlarini joylashtiring. Vosita MATLAB sintaksisini tahlil qiladi va massiv tuzilmasi ma'lumotlarini chiqaradi."
    step3: "Ko'p o'lchovli massivlar, ma'lumot turi spetsifikatsiyalari va o'zgaruvchi nomlashni qo'llab-quvvatlovchi standart MATLAB massiv kodini yarating. Yaratilgan kod to'g'ridan-to'g'ri MATLAB muhitida ma'lumot tahlili va ilmiy hisoblash uchun bajarilishi mumkin."
    from_alias: "MATLAB Massivi"
    to_alias: "MATLAB Kod Formati"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame bu R dasturlash tilining asosiy ma'lumotlar strukturasi bo'lib, statistik tahlil, ma'lumotlar qazib olish va mashinani o'rganishda keng qo'llaniladi. R statistik hisoblash va grafikalar uchun yetakchi vosita bo'lib, DataFrame kuchli ma'lumotlar boshqaruvi, statistik tahlil va vizualizatsiya imkoniyatlarini taqdim etadi. Tuzilgan ma'lumotlar tahlili bilan shug'ullanuvchi ma'lumotlar olimlari, statistiklar va tadqiqotchilar uchun zarurdir."
    step1: "R ma'lumotlar fayllarini yuklang yoki DataFrame kodini joylashtiring. Vosita R sintaksisini tahlil qiladi va ustun turlari, qator nomlari va ma'lumotlar mazmuni kabi DataFrame tuzilishi ma'lumotlarini ajratib oladi."
    step3: "Ma'lumotlar turi spetsifikatsiyalari, omil darajalari, qator/ustun nomlari va R-maxsus ma'lumotlar tuzilmalari uchun qo'llab-quvvatlash bilan standart R DataFrame kodini yarating. Yaratilgan kod statistik tahlil va ma'lumotlarni qayta ishlash uchun R muhitida bevosita bajarilishi mumkin."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Konvertatsiyani boshlash"
  start_generating: "Yaratishni boshlash"
  api_docs: "API hujjatlari"
related:
  section_title: "Ko'proq {{ if and .from (ne .from \"generator\") }}{{ .from }} va {{ end }}{{ .to }} konverterlari"
  section_description: "{{ if and .from (ne .from \"generator\") }}{{ .from }} va {{ end }}{{ .to }} formatlari uchun ko'proq konverterlarni o'rganing. Bizning professional onlayn konvertatsiya vositalarimiz bilan ma'lumotlaringizni bir nechta formatlar o'rtasida o'zgartiring."
  title: "{{ .from }} dan {{ .to }} ga"
howto:
  step2: "Professional xususiyatlar bilan bizning ilg'or onlayn jadval muharririmizdan foydalanib ma'lumotlarni tahrirlang. Bo'sh qatorlarni o'chirish, nusxalarni olib tashlash, ma'lumotlar transpozitsiyasi, saralash, regex topish va almashtirish va real vaqt ko'rinishini qo'llab-quvvatlaydi. Barcha o'zgarishlar aniq, ishonchli natijalar bilan avtomatik ravishda %s formatiga aylanadi."
  section_title: "{{ . }} dan qanday foydalanish kerak"
  converter_description: "Bizning bosqichma-bosqich yo'riqnomamiz bilan {{ .from }} ni {{ .to }} ga aylantirish usulini o'rganing. Ilg'or xususiyatlar va real vaqt ko'rinishi bilan professional onlayn konverter."
  generator_description: "Bizning onlayn generatorimiz bilan professional {{ .to }} jadvallarini yaratishni o'rganing. Excel kabi tahrirlash, real vaqt ko'rinishi va tezkor eksport imkoniyatlari."
extension:
  section_title: "Jadval aniqlash va chiqarish kengaytmasi"
  section_description: "Bir marta bosish bilan istalgan veb-saytdan jadvallarni chiqaring. Excel, CSV, JSON kabi 30+ formatga darhol konvertatsiya qiling - nusxa ko'chirish-joylashtirish talab qilinmaydi."
  features:
    extraction_title: "Bir bosishda jadval chiqarish"
    extraction_description: "Nusxalash-joylashtirmasdan har qanday veb-sahifadan jadvallarni darhol chiqarib oling - professional ma'lumot chiqarish oddiy qilingan"
    formats_title: "30+ format konverteri qo'llab-quvvatlash"
    formats_description: "Bizning ilg'or jadval konverterimiz bilan chiqarilgan jadvallarni Excel, CSV, JSON, Markdown, SQL va boshqalarga aylantiring"
    detection_title: "Aqlli jadval aniqlash"
    detection_description: "Tez ma'lumot chiqarish va konvertatsiya uchun har qanday veb-sahifada jadvallarni avtomatik ravishda aniqlaydi va ajratib ko'rsatadi"
  hover_tip: "✨ Chiqarish belgisini ko'rish uchun har qanday jadval ustiga suring"
recommendations:
  section_title: "Universitetlar va mutaxassislar tomonidan tavsiya etilgan"
  section_description: "Ishonchli jadval konvertatsiyasi va ma'lumotlarni qayta ishlash uchun universitetlar, tadqiqot institutlari va rivojlanish jamoalarining mutaxassislari TableConvert-ga ishonishadi."
  cards:
    university_title: "Viskonsin-Madison universiteti"
    university_description: "TableConvert.com - Professional bepul onlayn jadval konverteri va ma'lumot formatlari vositasi"
    university_link: "Maqolani o'qish"
    facebook_title: "Ma'lumotlar mutaxassislari jamoasi"
    facebook_description: "Facebook dasturchilar guruhlarida ma'lumotlar tahlilchilari va mutaxassislar tomonidan baham ko'rilgan va tavsiya etilgan"
    facebook_link: "Postni ko'rish"
    twitter_title: "Dasturchilar jamoasi"
    twitter_description: "Jadval konvertatsiyasi uchun X (Twitter)da @xiaoying_eth va boshqa dasturchilar tomonidan tavsiya etilgan"
    twitter_link: "Tvitni ko'rish"
faq:
  section_title: "Tez-tez so'raladigan savollar"
  section_description: "Bizning bepul onlayn jadval konverteri, ma'lumot formatlari va konvertatsiya jarayoni haqida umumiy savollar."
  what: "%s format nima?"
  howto_convert:
    question: "{{ . }} ni bepul qanday ishlatish mumkin?"
    answer: "Bizning bepul onlayn jadval konverterimiz yordamida {{ .from }} faylingizni yuklang, ma'lumotlarni joylashtiring yoki veb-sahifalardan chiqarib oling. Bizning professional konverter vositamiz real vaqt ko'rinishi va ilg'or tahrirlash xususiyatlari bilan ma'lumotlaringizni darhol {{ .to }} formatiga aylantiradi. Konvertatsiya qilingan natijani darhol yuklab oling yoki nusxalang."
  security:
    question: "Ushbu onlayn konverterni ishlatganda ma'lumotlarim xavfsizmi?"
    answer: "Albatta! Barcha jadval konvertatsiyalari brauzeringizda mahalliy darajada amalga oshiriladi - ma'lumotlaringiz hech qachon qurilmangizni tark etmaydi. Bizning onlayn konverterimiz hamma narsani mijoz tomonida qayta ishlaydi, to'liq maxfiylik va ma'lumotlar xavfsizligini ta'minlaydi. Bizning serverlarimizda hech qanday fayllar saqlanmaydi."
  free:
    question: "TableConvert haqiqatan ham foydalanish uchun bepulmi?"
    answer: "Ha, TableConvert butunlay bepul! Barcha konverter xususiyatlari, jadval muharriri, ma'lumot generator vositalari va eksport variantlari hech qanday xarajat, ro'yxatdan o'tish yoki yashirin to'lovlarsiz mavjud. Bepul onlayn cheksiz fayllarni konvertatsiya qiling."
  filesize:
    question: "Onlayn konverterning fayl hajmi chegaralari qanday?"
    answer: "Bizning bepul onlayn jadval konverterimiz 10MB gacha fayllarni qo'llab-quvvatlaydi. Kattaroq fayllar, paketli qayta ishlash yoki korxona ehtiyojlari uchun yuqori chegaralar bilan bizning brauzer kengaytmasi yoki professional API xizmatidan foydalaning."
stats:
  conversions: "Konvertatsiya qilingan jadvallar"
  tables: "Yaratilgan jadvallar"
  formats: "Ma'lumot fayl formatlari"
  rating: "Foydalanuvchi reytingi"
