site:
  fullname: "Table Convert"
  name: "TableConvert"
  subtitle: "Bezplatný Online Převodník a Generátor <PERSON>"
  intro: "TableConvert je bezplatný online převodník tabulek a nástroj pro generování dat podporující převod mezi 30+ formáty včetně Excel, CSV, JSON, Markdown, LaTeX, SQL a dalších."
  followTwitter: "Sledujte nás na X"
title:
  converter: "%s na %s"
  generator: "Generátor %s"
post:
  tags:
    converter: "Převodník"
    editor: "Editor"
    generator: "Generátor"
    maker: "Tvůrce"
  converter:
    title: "Převést %s na %s Online"
    short: "Bezplatný a výkonný online nástroj %s na %s"
    intro: "Snadno použitelný online převodník %s na %s. Transformujte data tabulek bez námahy s naším intuitivním nástrojem pro převod. R<PERSON><PERSON><PERSON>, s<PERSON><PERSON><PERSON><PERSON> a uživatelsky přívětivý."
  generator:
    title: "Online Editor a Generátor %s"
    short: "Profesionální online nástroj pro generování %s s komplexními funkcemi"
    intro: "Snadno použitelný online generátor %s a editor tabulek. Vytvářejte profesionální datové tabulky bez námahy s naším intuitivním nástrojem a náhledem v reálném čase."
navbar:
  search:
    placeholder: "Hledat převodník ..."
  sponsor: "Kupte mi kávu"
  extension: "Rozšíření"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Zdroj dat"
    placeholder: "Vložte vaše %s data nebo přetáhněte %s soubory sem"
    example: "Příklad"
    upload: "Nahrát soubor"
    extract:
      enter: "Extrahovat z webové stránky"
      intro: "Zadejte URL webové stránky obsahující data tabulky pro automatickou extrakci strukturovaných dat"
      btn: "Extrahovat %s"
    excel:
      sheet: "Pracovní list"
      none: "Žádný"
  tableEditor:
    title: "Online Editor Tabulek"
    undo: "Zpět"
    redo: "Znovu"
    transpose: "Transponovat"
    clear: "Vymazat"
    deleteBlank: "Smazat prázdné"
    deleteDuplicate: "Odstranit duplikáty"
    uppercase: "VELKÁ PÍSMENA"
    lowercase: "malá písmena"
    capitalize: "Velké první písmeno"
    replace:
      replace: "Najít a nahradit (Regex podporován)"
      subst: "Nahradit za..."
      btn: "Nahradit vše"
  tableGenerator:
    title: "Generátor tabulek"
    sponsor: "Kupte mi kávu"
    copy: "Kopírovat do schránky"
    download: "Stáhnout soubor"
    tooltip:
      html:
        escape: "Escape HTML speciální znaky (&, <, >, \", ') pro zabránění chybám zobrazení"
        div: "Použijte DIV+CSS rozložení místo tradičních TABLE tagů, lépe vhodné pro responzivní design"
        minify: "Odstraňte mezery a zalomení řádků pro generování komprimovaného HTML kódu"
        thead: "Generujte standardní hlavičku tabulky (&lt;thead&gt;) a tělo (&lt;tbody&gt;) strukturu"
        tableCaption: "Přidejte popisný titulek nad tabulku (&lt;caption&gt; element)"
        tableClass: "Přidejte CSS název třídy k tabulce pro snadné přizpůsobení stylu"
        tableId: "Nastavte jedinečný ID identifikátor pro tabulku pro JavaScript manipulaci"
      jira:
        escape: "Escapujte znaky roury (|) pro zabránění konfliktům se syntaxí Jira tabulky"
      json:
        parsingJSON: "Inteligentně analyzovat JSON řetězce v buňkách na objekty"
        minify: "Generujte kompaktní jednořádkový JSON formát pro snížení velikosti souboru"
        format: "Vyberte výstupní strukturu JSON dat: pole objektů, 2D pole, atd."
      latex:
        escape: "Escape LaTeX speciální znaky (%, &, _, #, $, atd.) pro zajištění správné kompilace"
        ht: "Přidejte plovoucí poziční parametr [!ht] pro ovládání pozice tabulky na stránce"
        mwe: "Generujte kompletní LaTeX dokument"
        tableAlign: "Nastavte horizontální zarovnání tabulky na stránce"
        tableBorder: "Konfigurujte styl ohraničení tabulky: bez ohraničení, částečné ohraničení, úplné ohraničení"
        label: "Nastavte štítek tabulky pro křížové odkazy příkazu \\ref{}"
        caption: "Nastavte titulek tabulky pro zobrazení nad nebo pod tabulkou"
        location: "Vyberte pozici zobrazení titulku tabulky: nahoře nebo dole"
        tableType: "Vyberte typ prostředí tabulky: tabular, longtable, array, atd."
      markdown:
        escape: "Escape Markdown speciální znaky (*, _, |, \\, atd.) pro zabránění konfliktům formátu"
        pretty: "Automaticky zarovnejte šířky sloupců pro generování krásnějšího formátu tabulky"
        simple: "Použijte zjednodušenou syntaxi, vynechejte vnější ohraničení vertikálních čar"
        boldFirstRow: "Udělejte text prvního řádku tučný"
        boldFirstColumn: "Udělejte text prvního sloupce tučný"
        firstHeader: "Považujte první řádek za záhlaví a přidejte oddělovací čáru"
        textAlign: "Nastavte zarovnání textu sloupce: vlevo, na střed, vpravo"
        multilineHandling: "Zpracování víceřádkového textu: zachovat zalomení řádků, escape na \\n, použít &lt;br&gt; tagy"

        includeLineNumbers: "Přidejte sloupec čísel řádků na levou stranu tabulky"
      magic:
        builtin: "Vyberte předdefinované běžné formáty šablon"
        rowsTpl: "<table> <tr> <th>Magická syntaxe</th> <th>Popis</th> <th>Podporované JS metody</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>1., 2. ... pole <b>z</b>áhlaví, tj. {hA} {hB} ...</td> <td>Metody řetězců</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>1., 2. ... pole aktuálního řádku, tj. {$A} {$B} ...</td> <td>Metody řetězců</td> </tr> <tr> <td>{F,} {F;}</td> <td>Rozdělit aktuální řádek řetězcem za <b>F</b></td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td><b>Č</b>íslo <b>ř</b>ádku aktuálního řádku od 1 nebo 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>K</b>onečné <b>č</b>íslo <b>ř</b>ádků </td> <td></td> </tr> <tr> <td>{x ...}</td> <td><b>S</b>pustit JavaScript kód, např.: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Použít zpětné lomítko <b>\\</b> pro výstup složených závorek {...} </td> <td></td> </tr></table>"
        headerTpl: "Vlastní výstupní šablona pro sekci hlavičky"
        footerTpl: "Vlastní výstupní šablona pro sekci zápatí"
      textile:
        escape: "Escapujte znaky syntaxe Textile (|, ., -, ^) pro zabránění konfliktům formátu"
        rowHeader: "Nastavte první řádek jako řádek hlavičky"
        thead: "Přidejte značky syntaxe Textile pro hlavičku a tělo tabulky"
      xml:
        escape: "Escapujte XML speciální znaky (&lt;, &gt;, &amp;, \", ') pro zajištění platného XML"
        minify: "Generujte komprimovaný XML výstup, odstraňuje extra mezery"
        rootElement: "Nastavte název tagu kořenového XML elementu"
        rowElement: "Nastavte název tagu XML elementu pro každý řádek dat"
        declaration: "Přidejte XML deklarační hlavičku (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Výstup dat jako XML atributy místo podřízených elementů"
        cdata: "Obalte textový obsah pomocí CDATA pro ochranu speciálních znaků"
        encoding: "Nastavte formát kódování znaků pro XML dokument"
        indentation: "Vyberte znak odsazení XML: mezery nebo tabulátory"
      yaml:
        indentSize: "Nastavte počet mezer pro odsazení YAML hierarchie (obvykle 2 nebo 4)"
        arrayStyle: "Formát pole: blok (jedna položka na řádek) nebo tok (inline formát)"
        quotationStyle: "Styl uvozovek řetězce: bez uvozovek, jednoduché uvozovky, dvojité uvozovky"
      csv:
        bom: "Přidejte UTF-8 byte order mark pro pomoc Excelu a dalšímu softwaru rozpoznat kódování"
      excel:
        autoWidth: "Automaticky upravte šířku sloupce na základě obsahu"
        protectSheet: "Povolte ochranu listu heslem: tableconvert.com"
      sql:  
        primaryKey: "Zadejte název pole primárního klíče pro příkaz CREATE TABLE"
        dialect: "Vyberte typ databáze, ovlivňuje syntaxi uvozovek a datových typů"
      ascii:
        forceSep: "Vynutit oddělovací čáry mezi každým řádkem dat"
        style: "Vyberte styl kreslení ohraničení ASCII tabulky"
        comment: "Přidejte komentářové značky pro obalení celé tabulky"
      mediawiki:
        minify: "Komprimujte výstupní kód, odstraňte extra mezery"
        header: "Označte první řádek jako styl záhlaví"
        sort: "Povolte funkci řazení kliknutím na tabulku"
      asciidoc:
        minify: "Komprimujte výstup formátu AsciiDoc"
        firstHeader: "Nastavte první řádek jako řádek záhlaví"
        lastFooter: "Nastavte poslední řádek jako řádek zápatí"
        title: "Přidejte text nadpisu k tabulce"
      tracwiki:
        rowHeader: "Nastavte první řádek jako záhlaví"
        colHeader: "Nastavte první sloupec jako záhlaví"
      bbcode:
        minify: "Komprimujte výstupní formát BBCode"
      restructuredtext:
        style: "Vyberte styl ohraničení tabulky reStructuredText"
        forceSep: "Vynutit oddělovací čáry"
      pdf:
        theme: "Vyberte vizuální styl PDF tabulky pro profesionální dokumenty"
        headerColor: "Vyberte barvu pozadí záhlaví pro PDF tabulky"
        showHead: "Ovládejte zobrazení záhlaví napříč PDF stránkami"
        docTitle: "Volitelný nadpis pro PDF dokument"
        docDescription: "Volitelný popisný text pro PDF dokument"
    label:
      ascii:
        forceSep: "Oddělovače řádků"
        style: "Styl ohraničení"
        comment: "Obal komentáře"
      restructuredtext:
        style: "Styl ohraničení"
        forceSep: "Vynutit oddělovače"
      bbcode:
        minify: "Minifikovat výstup"
      csv:
        doubleQuote: "Obal dvojitých uvozovek"
        delimiter: "Oddělovač polí"
        bom: "UTF-8 BOM"
        valueDelimiter: "Oddělovač hodnot"
        rowDelimiter: "Oddělovač řádků"
        prefix: "Prefix řádku"
        suffix: "Suffix řádku"
      excel:
        autoWidth: "Automatická šířka"
        textFormat: "Formát textu"
        protectSheet: "Chránit list"
        boldFirstRow: "Tučný první řádek"
        boldFirstColumn: "Tučný první sloupec"
        sheetName: "Název listu"
      html:
        escape: "Escapovat HTML znaky"
        div: "DIV tabulka"
        minify: "Minifikovat kód"
        thead: "Struktura hlavičky tabulky"
        tableCaption: "Popisek tabulky"
        tableClass: "Třída tabulky"
        tableId: "ID tabulky"
        rowHeader: "Záhlaví řádku"
        colHeader: "Záhlaví sloupce"
      jira:
        escape: "Escapovat znaky"
        rowHeader: "Záhlaví řádku"
        colHeader: "Záhlaví sloupce"
      json:
        parsingJSON: "Parsovat JSON"
        minify: "Minifikovat výstup"
        format: "Formát dat"
        rootName: "Název kořenového objektu"
        indentSize: "Velikost odsazení"
      jsonlines:
        parsingJSON: "Parsovat JSON"
        format: "Formát dat"
      latex:
        escape: "Escapovat LaTeX znaky tabulky"
        ht: "Plovoucí pozice"
        mwe: "Kompletní dokument"
        tableAlign: "Zarovnání tabulky"
        tableBorder: "Styl ohraničení"
        label: "Referenční štítek"
        caption: "Popisek tabulky"
        location: "Pozice popisku"
        tableType: "Typ tabulky"
        boldFirstRow: "Tučný první řádek"
        boldFirstColumn: "Tučný první sloupec"
        textAlign: "Zarovnání textu"
        borders: "Nastavení ohraničení"
      markdown:
        escape: "Escapovat znaky"
        pretty: "Pěkná Markdown tabulka"
        simple: "Jednoduchý Markdown formát"
        boldFirstRow: "Tučný první řádek"
        boldFirstColumn: "Tučný první sloupec"
        firstHeader: "První záhlaví"
        textAlign: "Zarovnání textu"
        multilineHandling: "Zpracování víceřádkového textu"

        includeLineNumbers: "Přidat čísla řádků"
        align: "Zarovnání"
      mediawiki:
        minify: "Minifikovat kód"
        header: "Značkování záhlaví"
        sort: "Seřaditelné"
      asciidoc:
        minify: "Minifikovat formát"
        firstHeader: "První záhlaví"
        lastFooter: "Poslední zápatí"
        title: "Název tabulky"
      tracwiki:
        rowHeader: "Záhlaví řádku"
        colHeader: "Záhlaví sloupce"
      sql:
        drop: "Smazat tabulku (pokud existuje)"
        create: "Vytvořit tabulku"
        oneInsert: "Dávkové vložení"
        table: "Název tabulky"
        dialect: "Typ databáze"
        primaryKey: "Primární klíč"
      magic:
        builtin: "Vestavěná šablona"
        rowsTpl: "Row Template, Syntax ->"
        headerTpl: "Šablona záhlaví"
        footerTpl: "Šablona zápatí"
      textile:
        escape: "Escapovat znaky"
        rowHeader: "Záhlaví řádku"
        thead: "Syntaxe hlavičky tabulky"
      xml:
        escape: "Escapovat XML znaky"
        minify: "Minifikovat výstup"
        rootElement: "Kořenový element"
        rowElement: "Element řádku"
        declaration: "XML deklarace"
        attributes: "Režim atributů"
        cdata: "CDATA obal"
        encoding: "Kódování"
        indentSize: "Velikost odsazení"
      yaml:
        indentSize: "Velikost odsazení"
        arrayStyle: "Styl pole"
        quotationStyle: "Styl uvozovek"
      pdf:
        theme: "Téma PDF tabulky"
        headerColor: "Barva záhlaví PDF"
        showHead: "Zobrazení záhlaví PDF"
        docTitle: "Název PDF dokumentu"
        docDescription: "Popis PDF dokumentu"

sidebar:
  all: "Všechny Konverzní Nástroje"
  dataSource:
    title: "Zdroj Dat"
    description:
      converter: "Importujte %s pro konverzi na %s. Podporuje nahrávání souborů, online editaci a extrakci webových dat."
      generator: "Vytvořte data tabulky s podporou více vstupních metod včetně manuálního vstupu, importu souborů a generování šablon."
  tableEditor:
    title: "Online Editor Tabulek"
    description:
      converter: "Zpracujte %s online pomocí našeho editoru tabulek. Zkušenost s operacemi podobnými Excelu s podporou mazání prázdných řádků, deduplikace, řazení a hledání a nahrazování."
      generator: "Výkonný online editor tabulek poskytující zkušenost s operacemi podobnými Excelu. Podporuje mazání prázdných řádků, deduplikaci, řazení a hledání a nahrazování."
  tableGenerator:
    title: "Generátor Tabulek"
    description:
      converter: "Rychle generujte %s s náhledem v reálném čase generátoru tabulek. Bohaté možnosti exportu, kopírování a stahování jedním kliknutím."
      generator: "Exportujte data %s v několika formátech pro splnění různých scénářů použití. Podporuje vlastní možnosti a náhled v reálném čase."
footer:
  changelog: "Seznam Změn"
  sponsor: "Sponzoři"
  contact: "Kontaktujte Nás"
  privacyPolicy: "Zásady Ochrany Osobních Údajů"
  about: "O Nás"
  resources: "Zdroje"
  popularConverters: "Populární Konvertory"
  popularGenerators: "Populární Generátory"
  dataSecurity: "Vaše data jsou v bezpečí - všechny konverze běží ve vašem prohlížeči."
converters:
  Markdown:
    alias: "Markdown Tabulka"
    what: "Markdown je lehký značkovací jazyk široce používaný pro technickou dokumentaci, tvorbu obsahu blogů a webový vývoj. Jeho syntaxe tabulek je stručná a intuitivní, podporuje zarovnání textu, vkládání odkazů a formátování. Je to preferovaný nástroj pro programátory a technické spisovatele, dokonale kompatibilní s GitHub, GitLab a dalšími platformami pro hostování kódu."
    step1: "Vložte data Markdown tabulky do oblasti zdroje dat nebo přímo přetáhněte .md soubory pro nahrání. Nástroj automaticky analyzuje strukturu a formátování tabulky, podporuje složitý vnořený obsah a zpracování speciálních znaků."
    step3: "Generujte standardní kód Markdown tabulky v reálném čase, podporuje více metod zarovnání, tučné písmo, přidání čísel řádků a další pokročilá nastavení formátu. Generovaný kód je plně kompatibilní s GitHub a hlavními Markdown editory, připraven k použití jedním kliknutím."
    from_alias: "Soubor Markdown tabulky"
    to_alias: "Formát Markdown tabulky"
  Magic:
    alias: "Vlastní Šablona"
    what: "Magic šablona je jedinečný pokročilý generátor dat tohoto nástroje, umožňující uživatelům vytvářet libovolný formát výstupu dat prostřednictvím vlastní syntaxe šablony. Podporuje nahrazování proměnných, podmíněné posuzování a zpracování smyček. Je to ultimátní řešení pro zpracování složitých potřeb konverze dat a personalizovaných výstupních formátů, zvláště vhodné pro vývojáře a datové inženýry."
    step1: "Vyberte vestavěné běžné šablony nebo vytvořte vlastní syntaxi šablony. Podporuje bohaté proměnné a funkce, které mohou zpracovat složité datové struktury a obchodní logiku."
    step3: "Generujte datový výstup, který plně splňuje požadavky vlastního formátu. Podporuje složitou logiku konverze dat a podmíněné zpracování, výrazně zlepšuje efektivitu zpracování dat a kvalitu výstupu. Mocný nástroj pro dávkové zpracování dat."
    from_alias: "Data tabulky"
    to_alias: "Vlastní formát výstupu"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) je nejšířeji používaný formát výměny dat, dokonale podporovaný Excelem, Google Sheets, databázovými systémy a různými nástroji pro analýzu dat. Jeho jednoduchá struktura a silná kompatibilita z něj činí standardní formát pro migraci dat, dávkový import/export a výměnu dat napříč platformami, široce používaný v obchodní analýze, datové vědě a systémové integraci."
    step1: "Nahrajte CSV soubory nebo přímo vložte CSV data. Nástroj inteligentně rozpoznává různé oddělovače (čárka, tabulátor, středník, svislítko atd.), automaticky detekuje datové typy a formáty kódování, podporuje rychlé parsování velkých souborů a složitých datových struktur."
    step3: "Generujte standardní CSV formátové soubory s podporou vlastních oddělovačů, stylů uvozovek, formátů kódování a nastavení BOM značek. Zajišťuje dokonalou kompatibilitu s cílovými systémy, poskytuje možnosti stahování a komprese pro splnění potřeb zpracování dat na podnikové úrovni."
    from_alias: "CSV datový soubor"
    to_alias: "Standardní CSV formát"
  JSON:
    alias: "JSON Pole"
    what: "JSON (JavaScript Object Notation) je standardní formát tabulkových dat pro moderní webové aplikace, REST API a architektury mikroslužeb. Jeho jasná struktura a efektivní parsování jej činí široce používaným v interakci front-end a back-end dat, ukládání konfiguračních souborů a NoSQL databázích. Podporuje vnořené objekty, struktury polí a více datových typů, což z něj činí nepostradatelná tabulková data pro moderní vývoj softwaru."
    step1: "Nahrajte JSON soubory nebo vložte JSON pole. Podporuje automatické rozpoznání a parsování polí objektů, vnořených struktur a složitých datových typů. Nástroj inteligentně validuje JSON syntaxi a poskytuje chybové výzvy."
    step3: "Generujte více JSON formátových výstupů: standardní pole objektů, 2D pole, sloupcová pole a formáty klíč-hodnota párů. Podporuje zkrášlený výstup, kompresní režim, vlastní názvy kořenových objektů a nastavení odsazení, dokonale se přizpůsobuje různým API rozhraním a potřebám ukládání dat."
    from_alias: "JSON pole soubor"
    to_alias: "Standardní JSON formát"
  JSONLines:
    alias: "JSONLines Formát"
    what: "JSON Lines (také známý jako NDJSON) je důležitý formát pro zpracování velkých dat a streamování dat, kde každý řádek obsahuje nezávislý JSON objekt. Široce používaný v analýze logů, zpracování datových toků, strojovém učení a distribuovaných systémech. Podporuje inkrementální zpracování a paralelní výpočty, což z něj činí ideální volbu pro zpracování rozsáhlých strukturovaných dat."
    step1: "Nahrajte JSONLines soubory nebo vložte data. Nástroj parsuje JSON objekty řádek po řádku, podporuje streamování velkých souborů a funkci přeskakování chybných řádků."
    step3: "Generujte standardní JSONLines formát s každým řádkem vypisujícím kompletní JSON objekt. Vhodné pro streamování, dávkový import a scénáře analýzy velkých dat, podporuje validaci dat a optimalizaci formátu."
    from_alias: "JSONLines data"
    to_alias: "JSONLines streamovací formát"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) je standardní formát pro výměnu dat na podnikové úrovni a správu konfigurace, se striktními syntaktickými specifikacemi a mocnými validačními mechanismy. Široce používaný ve webových službách, konfiguračních souborech, ukládání dokumentů a systémové integraci. Podporuje jmenné prostory, validaci schémat a XSLT transformace, což z něj činí důležitá tabulková data pro podnikové aplikace."
    step1: "Nahrajte XML soubory nebo vložte XML data. Nástroj automaticky parsuje XML strukturu a převádí ji do tabulkového formátu, podporuje jmenné prostory, zpracování atributů a složité vnořené struktury."
    step3: "Generujte XML výstup, který odpovídá XML standardům. Podporuje vlastní kořenové elementy, názvy řádkových elementů, režimy atributů, CDATA obalování a nastavení kódování znaků. Zajišťuje integritu dat a kompatibilitu, splňuje požadavky aplikací na podnikové úrovni."
    from_alias: "XML datový soubor"
    to_alias: "Standardní XML formát"
  YAML:
    alias: "YAML Konfigurace"
    what: "YAML je lidsky přívětivý standard serializace dat, proslulý svou jasnou hierarchickou strukturou a stručnou syntaxí. Široce používaný v konfiguračních souborech, DevOps nástrojových řetězcích, Docker Compose a Kubernetes nasazení. Jeho silná čitelnost a stručná syntaxe z něj činí důležitý konfigurační formát pro moderní cloud-native aplikace a automatizované operace."
    step1: "Nahrajte YAML soubory nebo vložte YAML data. Nástroj inteligentně parsuje YAML strukturu a validuje správnost syntaxe, podporuje multi-dokumentové formáty a složité datové typy."
    step3: "Generujte standardní YAML formátový výstup s podporou blokových a průtokových stylů polí, více nastavení uvozovek, vlastní odsazení a zachování komentářů. Zajišťuje, že výstupní YAML soubory jsou plně kompatibilní s různými parsery a konfiguračními systémy."
    from_alias: "YAML konfigurační soubor"
    to_alias: "Standardní YAML formát"
  MySQL:
      alias: "MySQL Výsledky Dotazů"
      what: "MySQL je nejpopulárnější open-source relační databázový management systém na světě, proslulý svým vysokým výkonem, spolehlivostí a snadností použití. Široce používaný ve webových aplikacích, podnikových systémech a platformách pro analýzu dat. MySQL výsledky dotazů typicky obsahují strukturovaná tabulková data, sloužící jako důležitý zdroj dat v databázovém managementu a práci s analýzou dat."
      step1: "Vložte výsledky MySQL dotazů do oblasti zdroje dat. Nástroj automaticky rozpoznává a parsuje MySQL command-line výstupní formát, podporuje různé styly výsledků dotazů a kódování znaků, inteligentně zpracovává hlavičky a datové řádky."
      step3: "Rychle převeďte MySQL výsledky dotazů do více formátů tabulkových dat, usnadňuje analýzu dat, generování reportů, migraci dat mezi systémy a validaci dat. Praktický nástroj pro databázové administrátory a datové analytiky."
      from_alias: "MySQL výstup dotazu"
      to_alias: "MySQL tabulková data"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) je standardní operační jazyk pro relační databáze, používaný pro dotazování, vkládání, aktualizaci a mazání dat. Jako základní technologie databázového managementu je SQL široce používán v analýze dat, business intelligence, ETL zpracování a konstrukci datových skladů. Je to nezbytný dovednostní nástroj pro datové profesionály."
    step1: "Vložte INSERT SQL příkazy nebo nahrajte .sql soubory. Nástroj inteligentně parsuje SQL syntaxi a extrahuje tabulková data, podporuje více SQL dialektů a zpracování složitých dotazových příkazů."
    step3: "Generujte standardní SQL INSERT příkazy a příkazy pro vytváření tabulek. Podporuje více databázových dialektů (MySQL, PostgreSQL, SQLite, SQL Server, Oracle), automaticky zpracovává mapování datových typů, escapování znaků a omezení primárních klíčů. Zajišťuje, že generovaný SQL kód může být přímo spuštěn."
    from_alias: "Insert SQL"
    to_alias: "SQL příkaz"
  Qlik:
      alias: "Qlik Tabulka"
      what: "Qlik je dodavatel softwaru specializující se na vizualizaci dat, výkonné dashboardy a samoobslužné produkty business intelligence, spolu s Tableau a Microsoft."
      step1: ""
      step3: "Nakonec [Generátor Tabulek](#TableGenerator) zobrazuje výsledky konverze. Použijte ve vašem Qlik Sense, Qlik AutoML, QlikView nebo jiném softwaru s podporou Qlik."
      from_alias: "Qlik tabulka"
      to_alias: "Qlik tabulka"
  DAX:
      alias: "DAX Tabulka"
      what: "DAX (Data Analysis Expressions) je programovací jazyk používaný v celém Microsoft Power BI pro vytváření vypočítaných sloupců, měr a vlastních tabulek."
      step1: ""
      step3: "Nakonec [Generátor Tabulek](#TableGenerator) zobrazuje výsledky konverze. Jak se očekává, používá se v několika produktech Microsoft včetně Microsoft Power BI, Microsoft Analysis Services a Microsoft Power Pivot pro Excel."
      from_alias: "DAX tabulka"
      to_alias: "DAX tabulka"
  Firebase:
    alias: "Firebase Seznam"
    what: "Firebase je BaaS platforma pro vývoj aplikací, která poskytuje hostované backend služby jako real-time databáze, cloud úložiště, autentifikace, hlášení pádů atd."
    step1: ""
    step3: "Nakonec [Generátor Tabulek](#TableGenerator) zobrazuje výsledky konverze. Poté můžete použít push metodu v Firebase API pro přidání do seznamu dat v Firebase databázi."
    from_alias: "Firebase seznam"
    to_alias: "Firebase seznam"
  HTML:
    alias: "HTML Tabulka"
    what: "HTML tabulky jsou standardní způsob zobrazení strukturovaných dat na webových stránkách, postavené s table, tr, td a dalšími tagy. Podporuje bohaté přizpůsobení stylu, responzivní layout a interaktivní funkcionalitu. Široce používané ve vývoji webových stránek, zobrazení dat a generování reportů, sloužící jako důležitá komponenta front-end vývoje a web designu."
    step1: "Vložte HTML kód obsahující tabulky nebo nahrajte HTML soubory. Nástroj automaticky rozpoznává a extrahuje data tabulek ze stránek, podporuje složité HTML struktury, CSS styly a zpracování vnořených tabulek."
    step3: "Generujte sémantický HTML kód tabulky s podporou thead/tbody struktury, nastavení CSS tříd, popisků tabulek, hlaviček řádků/sloupců a konfigurace responzivních atributů. Zajišťuje, že generovaný kód tabulky splňuje webové standardy s dobrou přístupností a SEO přívětivostí."
    from_alias: "HTML tabulka"
    to_alias: "HTML tabulka"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel je nejpopulárnější tabulkový software na světě, široce používaný v obchodní analýze, finančním managementu, zpracování dat a vytváření reportů. Jeho mocné schopnosti zpracování dat, bohatá knihovna funkcí a flexibilní vizualizační funkce z něj činí standardní nástroj pro kancelářskou automatizaci a analýzu dat, s rozsáhlými aplikacemi napříč téměř všemi odvětvími a oblastmi."
    step1: "Nahrajte Excel soubory (podporuje .xlsx, .xls formáty) nebo zkopírujte data tabulky přímo z Excelu a vložte. Nástroj podporuje zpracování více listů, rozpoznání složitých formátů a rychlé parsování velkých souborů, automaticky zpracovává sloučené buňky a datové typy."
    step3: "Generujte Excel-kompatibilní data tabulky, která mohou být přímo vložena do Excelu nebo stažena jako standardní .xlsx soubory. Podporuje pojmenování listů, formátování buněk, automatickou šířku sloupců, stylování hlaviček a nastavení validace dat. Zajišťuje, že výstupní Excel soubory mají profesionální vzhled a kompletní funkcionalitu."
    from_alias: "Excel tabulka"
    to_alias: "Excel"
  LaTeX:
    alias: "LaTeX Tabulka"
    what: "LaTeX je profesionální systém sazby dokumentů, zvláště vhodný pro vytváření akademických prací, technických dokumentů a vědeckých publikací. Jeho funkcionalita tabulek je mocná, podporuje složité matematické vzorce, přesné ovládání layoutu a vysoce kvalitní PDF výstup. Je to standardní nástroj v akademii a vědeckém publikování, široce používaný v časopiseckých článcích, dizertacích a sazbě technických manuálů."
    step1: "Vložte LaTeX kód tabulky nebo nahrajte .tex soubory. Nástroj parsuje LaTeX syntaxi tabulek a extrahuje datový obsah, podporuje více prostředí tabulek (tabular, longtable, array atd.) a složité formátovací příkazy."
    step3: "Generujte profesionální LaTeX kód tabulky s podporou výběru více prostředí tabulek, konfigurace stylu ohraničení, nastavení pozice popisků, specifikace třídy dokumentu a správy balíčků. Může generovat kompletní kompilovatelné LaTeX dokumenty, zajišťuje, že výstupní tabulky splňují standardy akademického publikování."
    from_alias: "LaTeX tabulka"
    to_alias: "LaTeX tabulka"
  ASCII:
    alias: "ASCII Textová Tabulka"
    what: "ASCII tabulky používají znaky prostého textu k vykreslení ohraničení a struktur tabulek, poskytují nejlepší kompatibilitu a přenositelnost. Kompatibilní se všemi textovými editory, terminálními prostředími a operačními systémy. Široce používané v dokumentaci kódu, technických manuálech, README souborech a výstupu nástrojů příkazové řádky. Preferovaný formát zobrazení dat pro programátory a systémové administrátory."
    step1: "Nahrajte textové soubory obsahující ASCII tabulky nebo přímo vložte data tabulky. Nástroj inteligentně rozpoznává a parsuje struktury ASCII tabulek, podporuje více stylů ohraničení a formátů zarovnání."
    step3: "Generujte krásné ASCII tabulky prostého textu s podporou více stylů ohraničení (jednoduchá čára, dvojitá čára, zaoblené rohy atd.), metod zarovnání textu a automatické šířky sloupců. Generované tabulky se zobrazují dokonale v editorech kódu, dokumentech a příkazových řádcích."
    from_alias: "ASCII textová tabulka"
    to_alias: "ASCII textová tabulka"
  MediaWiki:
    alias: "MediaWiki Tabulka"
    what: "MediaWiki je open-source softwarová platforma používaná slavnými wiki stránkami jako Wikipedia. Jeho syntaxe tabulek je stručná, ale mocná, podporuje přizpůsobení stylu tabulek, funkcionalitu řazení a vkládání odkazů. Široce používaná ve správě znalostí, kolaborativním editování a systémech správy obsahu, sloužící jako základní technologie pro budování wiki encyklopedií a znalostních bází."
    step1: "Vložte MediaWiki kód tabulky nebo nahrajte wiki zdrojové soubory. Nástroj parsuje wiki markup syntaxi a extrahuje data tabulky, podporuje složitou wiki syntaxi a zpracování šablon."
    step3: "Generujte standardní MediaWiki kód tabulky s podporou nastavení stylu hlaviček, zarovnání buněk, povolení funkcionality řazení a možností komprese kódu. Generovaný kód může být přímo použit pro editování wiki stránek, zajišťuje dokonalé zobrazení na MediaWiki platformách."
    from_alias: "MediaWiki tabulka"
    to_alias: "MediaWiki tabulka"
  TracWiki:
    alias: "TracWiki Tabulka"
    what: "Trac je webový systém správy projektů a sledování chyb, který používá zjednodušenou wiki syntaxi k vytváření obsahu tabulek."
    step1: "Nahrajte TracWiki soubory nebo vložte data tabulky."
    step3: "Generujte TracWiki-kompatibilní kód tabulky s podporou nastavení hlaviček řádků/sloupců, usnadňuje správu projektových dokumentů."
    from_alias: "TracWiki tabulka"
    to_alias: "TracWiki tabulka"
  AsciiDoc:
    alias: "AsciiDoc Tabulka"
    what: "AsciiDoc je lehký značkovací jazyk, který může být převeden na HTML, PDF, manuálové stránky a další formáty, široce používaný pro psaní technické dokumentace."
    step1: "Nahrajte AsciiDoc soubory nebo vložte data."
    step3: "Generujte AsciiDoc syntaxi tabulky s podporou nastavení hlavičky, zápatí a titulku, přímo použitelnou v AsciiDoc editorech."
    from_alias: "AsciiDoc tabulka"
    to_alias: "AsciiDoc tabulka"
  reStructuredText:
    alias: "reStructuredText Tabulka"
    what: "reStructuredText je standardní formát dokumentace pro Python komunitu, podporuje bohatou syntaxi tabulek, běžně používaný pro generování Sphinx dokumentace."
    step1: "Nahrajte .rst soubory nebo vložte reStructuredText data."
    step3: "Generujte standardní reStructuredText tabulky s podporou více stylů ohraničení, přímo použitelné v Sphinx dokumentačních projektech."
    from_alias: "reStructuredText tabulka"
    to_alias: "reStructuredText tabulka"
  PHP:
    alias: "PHP Pole"
    what: "PHP je populární server-side skriptovací jazyk, s poli jako jeho základní datovou strukturou, široce používaný ve webovém vývoji a zpracování dat."
    step1: "Nahrajte soubory obsahující PHP pole nebo přímo vložte data."
    step3: "Generujte standardní PHP kód pole, který může být přímo použit v PHP projektech, podporuje asociativní a indexované formáty polí."
    from_alias: "PHP pole"
    to_alias: "PHP kód"
  Ruby:
    alias: "Ruby Pole"
    what: "Ruby je dynamický objektově orientovaný programovací jazyk se stručnou a elegantní syntaxí, s poli jako důležitou datovou strukturou."
    step1: "Nahrajte Ruby soubory nebo vložte data pole."
    step3: "Generujte Ruby kód pole, který odpovídá Ruby syntaktickým specifikacím, přímo použitelný v Ruby projektech."
    from_alias: "Ruby pole"
    to_alias: "Ruby kód"
  ASP:
    alias: "ASP Pole"
    what: "ASP (Active Server Pages) je Microsoft server-side skriptovací prostředí, podporující více programovacích jazyků pro vývoj dynamických webových stránek."
    step1: "Nahrajte ASP soubory nebo vložte data pole."
    step3: "Generujte ASP-kompatibilní kód pole s podporou VBScript a JScript syntaxe, použitelný v ASP.NET projektech."
    from_alias: "ASP pole"
    to_alias: "ASP kód"
  ActionScript:
    alias: "ActionScript Pole"
    what: "ActionScript je objektově orientovaný programovací jazyk primárně používaný pro vývoj Adobe Flash a AIR aplikací."
    step1: "Nahrajte .as soubory nebo vložte ActionScript data."
    step3: "Generujte ActionScript kód pole, který odpovídá AS3 syntaktickým standardům, použitelný pro vývoj Flash a Flex projektů."
    from_alias: "ActionScript pole"
    to_alias: "ActionScript kód"
  BBCode:
    alias: "BBCode Tabulka"
    what: "BBCode je lehký značkovací jazyk běžně používaný ve fórech a online komunitách, poskytující jednoduchou formátovací funkcionalitu včetně podpory tabulek."
    step1: "Nahrajte soubory obsahující BBCode nebo vložte data."
    step3: "Generujte BBCode kód tabulky vhodný pro publikování ve fórech a vytváření komunitního obsahu, s podporou komprimovaného výstupního formátu."
    from_alias: "BBCode tabulka"
    to_alias: "BBCode tabulka"
  PDF:
    alias: "PDF Tabulka"
    what: "PDF (Portable Document Format) je cross-platform standard dokumentů s pevným layoutem, konzistentním zobrazením a vysoce kvalitními charakteristikami tisku. Široce používaný v formálních dokumentech, reportech, fakturách, smlouvách a akademických pracích. Preferovaný formát pro obchodní komunikaci a archivaci dokumentů, zajišťuje kompletně konzistentní vizuální efekty napříč různými zařízeními a operačními systémy."
    step1: "Importujte data tabulky v jakémkoli formátu. Nástroj automaticky analyzuje strukturu dat a provádí inteligentní design layoutu, podporuje automatické stránkování velkých tabulek a zpracování složitých datových typů."
    step3: "Generujte vysoce kvalitní PDF soubory tabulek s podporou více profesionálních stylů témat (obchodní, akademický, minimalistický atd.), vícejazyčných fontů, automatického stránkování, přidání vodoznaku a optimalizace tisku. Zajišťuje, že výstupní PDF dokumenty mají profesionální vzhled, přímo použitelné pro obchodní prezentace a formální publikování."
    from_alias: "Data tabulky"
    to_alias: "PDF tabulka"
  JPEG:
    alias: "JPEG Obrázek"
    what: "JPEG je nejšířeji používaný formát digitálních obrázků s vynikajícími kompresními efekty a širokou kompatibilitou. Jeho malá velikost souboru a rychlá rychlost načítání jej činí vhodným pro webové zobrazení, sdílení na sociálních médiích, ilustrace dokumentů a online prezentace. Standardní formát obrázků pro digitální média a síťovou komunikaci, dokonale podporovaný téměř všemi zařízeními a softwarem."
    step1: "Importujte data tabulky v jakémkoli formátu. Nástroj provádí inteligentní design layoutu a vizuální optimalizaci, automaticky vypočítává optimální velikost a rozlišení."
    step3: "Generujte vysoce kvalitní JPEG obrázky tabulek s podporou více barevných schémat témat (světlé, tmavé, šetrné k očím atd.), adaptivního layoutu, optimalizace čitelnosti textu a přizpůsobení velikosti. Vhodné pro online sdílení, vkládání do dokumentů a použití v prezentacích, zajišťuje vynikající vizuální efekty na různých zobrazovacích zařízeních."
    from_alias: "Data tabulky"
    to_alias: "JPEG obrázek"
  Jira:
    alias: "Jira Tabulka"
    what: "JIRA je profesionální software pro správu projektů a sledování chyb vyvinutý společností Atlassian, široce používaný v agilním vývoji, testování softwaru a projektové spolupráci. Jeho funkcionalita tabulek podporuje bohaté možnosti formátování a zobrazení dat, sloužící jako důležitý nástroj pro týmy vývoje softwaru, projektové manažery a personál zajišťování kvality ve správě požadavků, sledování chyb a reportování pokroku."
    step1: "Nahrajte soubory obsahující data tabulky nebo přímo vložte obsah dat. Nástroj automaticky zpracovává data tabulky a escapování speciálních znaků."
    step3: "Generujte JIRA platformě kompatibilní kód tabulky s podporou nastavení stylu hlaviček, zarovnání buněk, zpracování escapování znaků a optimalizace formátu. Generovaný kód může být přímo vložen do JIRA popisů problémů, komentářů nebo wiki stránek, zajišťuje správné zobrazení a vykreslování v JIRA systémech."
    from_alias: "Jira tabulka"
    to_alias: "Jira tabulka"
  Textile:
    alias: "Textile Tabulka"
    what: "Textile je stručný lehký značkovací jazyk s jednoduchou a snadno naučitelnou syntaxí, široce používaný v systémech správy obsahu, blogových platformách a fórových systémech. Jeho syntaxe tabulek je jasná a intuitivní, podporuje rychlé formátování a nastavení stylů. Ideální nástroj pro tvůrce obsahu a administrátory webových stránek pro rychlé psaní dokumentů a publikování obsahu."
    step1: "Nahrajte soubory formátu Textile nebo vložte data tabulky. Nástroj parsuje Textile markup syntaxi a extrahuje obsah tabulky."
    step3: "Generujte standardní Textile syntaxi tabulky s podporou markup hlaviček, zarovnání buněk, escapování speciálních znaků a optimalizace formátu. Generovaný kód může být přímo použit v CMS systémech, blogových platformách a dokumentových systémech, které podporují Textile, zajišťuje správné vykreslování a zobrazení obsahu."
    from_alias: "Textile tabulka"
    to_alias: "Textile tabulka"
  PNG:
    alias: "PNG Obrázek"
    what: "PNG (Portable Network Graphics) je bezztrátový formát obrázků s vynikající kompresí a podporou průhlednosti. Široce používaný ve webovém designu, digitální grafice a profesionální fotografii. Jeho vysoká kvalita a široká kompatibilita jej činí ideálním pro screenshoty, loga, diagramy a jakékoli obrázky vyžadující ostré detaily a průhledné pozadí."
    step1: "Importujte data tabulky v jakémkoli formátu. Nástroj provádí inteligentní design layoutu a vizuální optimalizaci, automaticky vypočítává optimální velikost a rozlišení pro PNG výstup."
    step3: "Generujte vysoce kvalitní PNG obrázky tabulek s podporou více barevných schémat témat, průhledných pozadí, adaptivního layoutu a optimalizace čitelnosti textu. Dokonalé pro webové použití, vkládání do dokumentů a profesionální prezentace s vynikající vizuální kvalitou."
    from_alias: ""
    to_alias: "PNG obrázek"
  TOML:
    alias: "TOML"
    what: "TOML (Tom's Obvious, Minimal Language) je formát konfiguračního souboru, který je snadno čitelný a zapisovatelný. Navržený tak, aby byl jednoznačný a jednoduchý, je široce používán v moderních softwarových projektech pro správu konfigurace. Jeho jasná syntaxe a silné typování z něj činí vynikající volbu pro nastavení aplikací a konfigurační soubory projektů."
    step1: "Nahrajte TOML soubory nebo vložte konfigurační data. Nástroj parsuje TOML syntaxi a extrahuje strukturované konfigurační informace."
    step3: "Generujte standardní TOML formát s podporou vnořených struktur, datových typů a komentářů. Generované TOML soubory jsou dokonalé pro konfiguraci aplikací, build nástroje a nastavení projektů."
    from_alias: "TOML"
    to_alias: "TOML formát"
  INI:
    alias: "INI"
    what: "INI soubory jsou jednoduché konfigurační soubory používané mnoha aplikacemi a operačními systémy. Jejich přímočará struktura párů klíč-hodnota je činí snadno čitelnými a ručně editovatelnými. Široce používané ve Windows aplikacích, legacy systémech a jednoduchých konfiguračních scénářích, kde je důležitá lidská čitelnost."
    step1: "Nahrajte INI soubory nebo vložte konfigurační data. Nástroj parsuje INI syntaxi a extrahuje sekčně založené konfigurační informace."
    step3: "Generujte standardní INI formát s podporou sekcí, komentářů a různých datových typů. Generované INI soubory jsou kompatibilní s většinou aplikací a konfiguračních systémů."
    from_alias: "INI"
    to_alias: "INI formát"
  Avro:
    alias: "Avro Schéma"
    what: "Apache Avro je systém serializace dat, který poskytuje bohaté datové struktury, kompaktní binární formát a schopnosti evoluce schématu. Široce používaný ve zpracování velkých dat, frontách zpráv a distribuovaných systémech. Jeho definice schématu podporuje složité datové typy a kompatibilitu verzí, což z něj činí důležitý nástroj pro datové inženýry a systémové architekty."
    step1: "Nahrajte Avro schéma soubory nebo vložte data. Nástroj parsuje Avro definice schémat a extrahuje informace o struktuře tabulky."
    step3: "Generujte standardní Avro definice schémat s podporou mapování datových typů, omezení polí a validace schématu. Generovaná schémata mohou být přímo použita v Hadoop ekosystémech, Kafka systémech zpráv a dalších platformách velkých dat."
    from_alias: "Avro schéma"
    to_alias: "Avro schéma"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) je Google jazykově neutrální, platformě neutrální, rozšiřitelný mechanismus pro serializaci strukturovaných dat. Široce používaný v mikroslužbách, vývoji API a ukládání dat. Jeho efektivní binární formát a silné typování jej činí ideálním pro vysoce výkonné aplikace a komunikaci napříč jazyky."
    step1: "Nahrajte .proto soubory nebo vložte Protocol Buffer definice. Nástroj parsuje protobuf syntaxi a extrahuje informace o struktuře zpráv."
    step3: "Generujte standardní Protocol Buffer definice s podporou typů zpráv, možností polí a definic služeb. Generované .proto soubory mohou být kompilovány pro více programovacích jazyků."
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf schéma"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas je nejpopulárnější knihovna pro analýzu dat v Pythonu, s DataFrame jako jeho základní datovou strukturou. Poskytuje mocné schopnosti manipulace, čištění a analýzy dat, široce používané v datové vědě, strojovém učení a business intelligence. Nepostradatelný nástroj pro Python vývojáře a datové analytiky."
    step1: "Nahrajte Python soubory obsahující DataFrame kód nebo vložte data. Nástroj parsuje Pandas syntaxi a extrahuje informace o struktuře DataFrame."
    step3: "Generujte standardní Pandas DataFrame kód s podporou specifikací datových typů, nastavení indexu a datových operací. Generovaný kód může být přímo spuštěn v Python prostředí pro analýzu a zpracování dat."
    from_alias: "Pandas DataFrame"
    to_alias: "Pandas DataFrame"
  RDF:
    alias: "RDF Trojice"
    what: "RDF (Resource Description Framework) je standardní model pro výměnu dat na webu, navržený k reprezentaci informací o zdrojích v grafové formě. Široce používaný v sémantickém webu, znalostních grafech a aplikacích propojených dat. Jeho trojicová struktura umožňuje bohatou reprezentaci metadat a sémantické vztahy."
    step1: "Nahrajte RDF soubory nebo vložte data trojic. Nástroj parsuje RDF syntaxi a extrahuje sémantické vztahy a informace o zdrojích."
    step3: "Generujte standardní RDF formát s podporou různých serializací (RDF/XML, Turtle, N-Triples). Generovaný RDF může být použit v aplikacích sémantického webu, znalostních bázích a systémech propojených dat."
    from_alias: "RDF"
    to_alias: "RDF trojice"
  MATLAB:
    alias: "MATLAB Pole"
    what: "MATLAB je vysoce výkonný software pro numerické výpočty a vizualizaci široce používaný v inženýrských výpočtech, analýze dat a vývoji algoritmů. Jeho operace s poli a maticemi jsou mocné, podporují složité matematické výpočty a zpracování dat. Nezbytný nástroj pro inženýry, výzkumníky a datové vědce."
    step1: "Nahrajte MATLAB .m soubory nebo vložte data pole. Nástroj parsuje MATLAB syntaxi a extrahuje informace o struktuře pole."
    step3: "Generujte standardní MATLAB kód pole s podporou vícerozměrných polí, specifikací datových typů a pojmenování proměnných. Generovaný kód může být přímo spuštěn v MATLAB prostředí pro analýzu dat a vědecké výpočty."
    from_alias: "MATLAB pole"
    to_alias: "MATLAB pole"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame je základní datová struktura v programovacím jazyce R, široce používaná ve statistické analýze, dolování dat a strojovém učení. R je přední nástroj pro statistické výpočty a grafiku, s DataFrame poskytujícím mocné schopnosti manipulace dat, statistické analýzy a vizualizace. Nezbytný pro datové vědce, statistiky a výzkumníky pracující se strukturovanou analýzou dat."
    step1: "Nahrajte R datové soubory nebo vložte DataFrame kód. Nástroj parsuje R syntaxi a extrahuje informace o struktuře DataFrame včetně typů sloupců, názvů řádků a obsahu dat."
    step3: "Generujte standardní R DataFrame kód s podporou specifikací datových typů, úrovní faktorů, názvů řádků/sloupců a R-specifických datových struktur. Generovaný kód může být přímo spuštěn v R prostředí pro statistickou analýzu a zpracování dat."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Začít převádění"
  start_generating: "Začít generování"
  api_docs: "API dokumentace"
related:
  section_title: 'Více {{ if and .from (ne .from "generator") }}{{ .from }} a {{ end }}{{ .to }} převodníků'
  section_description: 'Prozkoumejte více převodníků pro {{ if and .from (ne .from "generator") }}{{ .from }} a {{ end }}{{ .to }} formáty. Transformujte vaše data mezi více formáty s našimi profesionálními online nástroji pro převod.'
  title: "{{ .from }} na {{ .to }}"
howto:
  step2: "Upravujte data pomocí našeho pokročilého online editoru tabulek s profesionálními funkcemi. Podporuje mazání prázdných řádků, odstraňování duplikátů, transpozici dat, řazení, regex hledání a nahrazování a náhled v reálném čase. Všechny změny se automaticky převedou do formátu %s s přesnými a spolehlivými výsledky."
  section_title: "Jak používat {{ . }}"
  converter_description: "Naučte se převádět {{ .from }} na {{ .to }} s naším průvodcem krok za krokem. Profesionální online převodník s pokročilými funkcemi a náhledem v reálném čase."
  generator_description: "Naučte se vytvářet profesionální {{ .to }} tabulky s naším online generátorem. Úpravy podobné Excelu, náhled v reálném čase a možnosti okamžitého exportu."
extension:
  section_title: "Rozšíření pro detekci a extrakci tabulek"
  section_description: "Extrahujte tabulky z jakékoliv webové stránky jedním kliknutím. Převeďte na 30+ formátů včetně Excel, CSV, JSON okamžitě - není potřeba kopírování a vkládání."
  features:
    extraction_title: "Extrakce tabulek jedním kliknutím"
    extraction_description: "Okamžitě extrahujte tabulky z jakékoliv webové stránky bez kopírování a vkládání - profesionální extrakce dat zjednodušená"
    formats_title: "Podpora převodníku 30+ formátů"
    formats_description: "Převeďte extrahované tabulky na Excel, CSV, JSON, Markdown, SQL a další s naším pokročilým převodníkem tabulek"
    detection_title: "Inteligentní detekce tabulek"
    detection_description: "Automaticky detekuje a zvýrazňuje tabulky na jakékoliv webové stránce pro rychlou extrakci a převod dat"
  hover_tip: "✨ Najeďte myší na jakoukoli tabulku pro zobrazení ikony extrakce"
recommendations:
  section_title: "Doporučeno univerzitami a profesionály"
  section_description: "TableConvert je důvěryhodný pro profesionály napříč univerzitami, výzkumnými institucemi a vývojovými týmy pro spolehlivý převod tabulek a zpracování dat."
  cards:
    university_title: "University of Wisconsin-Madison"
    university_description: "TableConvert.com - Profesionální bezplatný online převodník tabulek a nástroj pro datové formáty"
    university_link: "Přečíst článek"
    facebook_title: "Komunita datových profesionálů"
    facebook_description: "Sdíleno a doporučeno datovými analytiky a profesionály ve Facebook vývojářských skupinách"
    facebook_link: "Zobrazit příspěvek"
    twitter_title: "Vývojářská komunita"
    twitter_description: "Doporučeno @xiaoying_eth a dalšími vývojáři na X (Twitter) pro převod tabulek"
    twitter_link: "Zobrazit tweet"
faq:
  section_title: "Často kladené otázky"
  section_description: "Běžné otázky o našem bezplatném online převodníku tabulek, datových formátech a procesu převodu."
  what: "Co je formát %s?"
  howto_convert:
    question: "Jak používat {{ . }} zdarma?"
    answer: "Nahrajte váš {{ .from }} soubor, vložte data nebo extrahujte z webových stránek pomocí našeho bezplatného online převodníku tabulek. Náš profesionální převodní nástroj okamžitě transformuje vaše data do formátu {{ .to }} s náhledem v reálném čase a pokročilými funkcemi úprav. Stáhněte nebo zkopírujte převedený výsledek okamžitě."
  security:
    question: "Jsou moje data bezpečná při používání tohoto online převodníku?"
    answer: "Absolutně! Všechny převody tabulek probíhají lokálně ve vašem prohlížeči - vaše data nikdy neopustí vaše zařízení. Náš online převodník zpracovává vše na straně klienta, zajišťuje úplné soukromí a bezpečnost dat. Žádné soubory nejsou uloženy na našich serverech."
  free:
    question: "Je TableConvert opravdu zdarma k použití?"
    answer: "Ano, TableConvert je zcela zdarma! Všechny funkce převodníku, editor tabulek, nástroje generátoru dat a možnosti exportu jsou dostupné bez nákladů, registrace nebo skrytých poplatků. Převádějte neomezené soubory online zdarma."
  filesize:
    question: "Jaké jsou limity velikosti souborů online převodníku?"
    answer: "Náš bezplatný online převodník tabulek podporuje soubory do 10MB. Pro větší soubory, dávkové zpracování nebo podnikové potřeby použijte naše rozšíření prohlížeče nebo profesionální API službu s vyššími limity."
stats:
  conversions: "Převedené tabulky"
  tables: "Generované tabulky"
  formats: "Formáty datových souborů"
  rating: "Hodnocení uživatelů"
