site:
  fullname: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Конвертер Таблиць"
  name: "TableConvert"
  subtitle: "Безкоштовний Онлайн Конвертер і Генератор Таблиць"
  intro: "TableConvert - це безкоштовний онлайн-інструмент для конвертації таблиць і генерації даних, що підтримує конвертацію між понад 30 форматами, включаючи Excel, CSV, JSON, Markdown, LaTeX, SQL та інші."
  followTwitter: "Слідкуйте за нами в X"
title:
  converter: "%s в %s"
  generator: "Генератор %s"
post:
  tags:
    converter: "Конвертер"
    editor: "Редактор"
    generator: "Генератор"
    maker: "Конструктор"
  converter:
    title: "Конвертувати %s в %s Онлайн"
    short: "Безкоштовний та потужний онлайн-інструмент %s в %s"
    intro: "Простий у використанні онлайн-конвертер %s в %s. Перетворюйте дані таблиць без зусиль за допомогою нашого інтуїтивного інструменту конвертації. Швидкий, надійний та зручний для користувача."
  generator:
    title: "Онлайн Редактор і Генератор %s"
    short: "Професійний онлайн-інструмент генерації %s з комплексними функціями"
    intro: "Простий у використанні онлайн-генератор %s і редактор таблиць. Створюйте професійні таблиці даних без зусиль за допомогою нашого інтуїтивного інструменту та попереднього перегляду в реальному часі."
navbar:
  search:
    placeholder: "Пошук конвертера..."
  sponsor: "Купіть нам каву"
  extension: "Розширення"
  api: "API документація"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Джерело Даних"
    placeholder: "Вставте ваші дані %s або перетягніть файли %s сюди"
    example: "Приклад"
    upload: "Завантажити Файл"
    extract:
      enter: "Витягти з Веб-сторінки"
      intro: "Введіть URL веб-сторінки, що містить табличні дані, для автоматичного витягування структурованих даних"
      btn: "Витягти %s"
    excel:
      sheet: "Робочий Аркуш"
      none: "Немає"
  tableEditor:
    title: "Онлайн Редактор Таблиць"
    undo: "Скасувати"
    redo: "Повторити"
    transpose: "Транспонувати"
    clear: "Очистити"
    deleteBlank: "Видалити Порожні"
    deleteDuplicate: "Видалити Дублікати"
    uppercase: "ВЕЛИКІ ЛІТЕРИ"
    lowercase: "малі літери"
    capitalize: "Велика Перша"
    replace:
      replace: "Знайти та Замінити (підтримка Regex)"
      subst: "Замінити на..."
      btn: "Замінити Все"
  tableGenerator:
    title: "Генератор Таблиць"
    sponsor: "Купіть нам каву"
    copy: "Копіювати в Буфер"
    download: "Завантажити Файл"
    tooltip:
      html:
        escape: "Екранувати спеціальні HTML символи (&, <, >, \", ') для запобігання помилкам відображення"
        div: "Використовувати DIV+CSS макет замість традиційних TABLE тегів, краще підходить для адаптивного дизайну"
        minify: "Видалити пробіли та переноси рядків для генерації стисненого HTML коду"
        thead: "Генерувати стандартну структуру заголовка (&lt;thead&gt;) та тіла (&lt;tbody&gt;) таблиці"
        tableCaption: "Додати описовий заголовок над таблицею (елемент &lt;caption&gt;)"
        tableClass: "Додати ім'я CSS класу до таблиці для легкого налаштування стилю"
        tableId: "Встановити унікальний ID ідентифікатор для таблиці для JavaScript маніпуляцій"
      jira:
        escape: "Екранувати символи pipe (|) для уникнення конфліктів з синтаксисом таблиці Jira"
      json:
        parsingJSON: "Інтелектуально парсити JSON рядки в комірках в об'єкти"
        minify: "Генерувати компактний однорядковий JSON формат для зменшення розміру файлу"
        format: "Вибрати структуру вихідних JSON даних: масив об'єктів, 2D масив тощо"
      latex:
        escape: "Екранувати спеціальні LaTeX символи (%, &, _, #, $ тощо) для забезпечення правильної компіляції"
        ht: "Додати параметр плаваючої позиції [!ht] для контролю позиції таблиці на сторінці"
        mwe: "Генерувати повний LaTeX документ"
        tableAlign: "Встановити горизонтальне вирівнювання таблиці на сторінці"
        tableBorder: "Налаштувати стиль рамки таблиці: без рамки, часткова рамка, повна рамка"
        label: "Встановити мітку таблиці для команди \\ref{} перехресного посилання"
        caption: "Встановити підпис таблиці для відображення над або під таблицею"
        location: "Вибрати позицію відображення підпису таблиці: над або під"
        tableType: "Вибрати тип середовища таблиці: tabular, longtable, array тощо"
      markdown:
        escape: "Екранувати спеціальні Markdown символи (*, _, |, \\ тощо) для уникнення конфліктів формату"
        pretty: "Автоматично вирівняти ширину стовпців для генерації красивішого формату таблиці"
        simple: "Використовувати спрощений синтаксис, пропускаючи зовнішні вертикальні лінії рамки"
        boldFirstRow: "Зробити текст першого рядка жирним"
        boldFirstColumn: "Зробити текст першого стовпця жирним"
        firstHeader: "Обробляти перший рядок як заголовок та додати роздільну лінію"
        textAlign: "Встановити вирівнювання тексту стовпця: ліворуч, по центру, праворуч"
        multilineHandling: "Обробка багаторядкового тексту: зберегти переноси рядків, екранувати в \\n, використовувати теги &lt;br&gt;"

        includeLineNumbers: "Додати стовпець номерів рядків ліворуч від таблиці"
      magic:
        builtin: "Вибрати попередньо визначені загальні формати шаблонів"
        rowsTpl: "<table> <tr> <th>Магічний Синтаксис</th> <th>Опис</th> <th>Підтримувані JS Методи</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>1-е, 2-е ... поле <b>з</b>аголовка, Також {hA} {hB} ...</td> <td>Методи рядків</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>1-е, 2-е ... поле поточного рядка, Також {$A} {$B} ...</td> <td>Методи рядків</td> </tr> <tr> <td>{F,} {F;}</td> <td>Розділити поточний рядок рядком після <b>F</b></td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td><b>Н</b>омер <b>р</b>ядка поточного рядка від 1 або 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>К</b>інцевий <b>н</b>омер <b>р</b>ядків </td> <td></td> </tr> <tr> <td>{x ...}</td> <td><b>В</b>иконати JavaScript код, напр: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Використовувати зворотну косу риску <b>\\</b> для виводу фігурних дужок {...} </td> <td></td> </tr></table>"
        headerTpl: "Користувацький шаблон виводу для секції заголовка"
        footerTpl: "Користувацький шаблон виводу для секції підвалу"
      textile:
        escape: "Екранувати символи синтаксису Textile (|, ., -, ^) для уникнення конфліктів формату"
        rowHeader: "Встановити перший рядок як рядок заголовка"
        thead: "Додати маркери синтаксису Textile для заголовка та тіла таблиці"
      xml:
        escape: "Екранувати спеціальні XML символи (&lt;, &gt;, &amp;, \", ') для забезпечення валідного XML"
        minify: "Генерувати стиснений XML вивід, видаляючи зайві пробіли"
        rootElement: "Встановити ім'я тегу кореневого XML елемента"
        rowElement: "Встановити ім'я тегу XML елемента для кожного рядка даних"
        declaration: "Додати заголовок XML декларації (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Виводити дані як XML атрибути замість дочірніх елементів"
        cdata: "Обгорнути текстовий контент CDATA для захисту спеціальних символів"
        encoding: "Встановити формат кодування символів для XML документа"
        indentation: "Вибрати символ відступу XML: пробіли або табуляції"
      yaml:
        indentSize: "Встановити кількість пробілів для відступу ієрархії YAML (зазвичай 2 або 4)"
        arrayStyle: "Формат масиву: блок (один елемент на рядок) або потік (вбудований формат)"
        quotationStyle: "Стиль лапок рядка: без лапок, одинарні лапки, подвійні лапки"
      pdf:
        theme: "Вибрати візуальний стиль PDF таблиці для професійних документів"
        headerColor: "Вибрати колір фону заголовка PDF таблиці"
        showHead: "Контролювати відображення заголовка на PDF сторінках"
        docTitle: "Опціональний заголовок для PDF документа"
        docDescription: "Опціональний текст опису для PDF документа"
      csv:
        bom: "Додати позначку порядку байтів UTF-8 для допомоги Excel та іншому ПЗ розпізнати кодування"
      excel:
        autoWidth: "Автоматично налаштувати ширину стовпця на основі контенту"
        protectSheet: "Увімкнути захист робочого аркуша з паролем: tableconvert.com"
      sql:
        primaryKey: "Вказати ім'я поля первинного ключа для оператора CREATE TABLE"
        dialect: "Вибрати тип бази даних, що впливає на синтаксис лапок та типів даних"
      ascii:
        forceSep: "Примусово додати роздільні лінії між кожним рядком даних"
        style: "Вибрати стиль малювання рамки ASCII таблиці"
        comment: "Додати маркери коментарів для обгортання всієї таблиці"
      mediawiki:
        minify: "Стиснути вихідний код, видаляючи зайві пробіли"
        header: "Позначити перший рядок як стиль заголовка"
        sort: "Увімкнути функціональність сортування таблиці кліком"
      asciidoc:
        minify: "Стиснути вивід формату AsciiDoc"
        firstHeader: "Встановити перший рядок як рядок заголовка"
        lastFooter: "Встановити останній рядок як рядок підвалу"
        title: "Додати текст заголовка до таблиці"
      tracwiki:
        rowHeader: "Встановити перший рядок як заголовок"
        colHeader: "Встановити перший стовпець як заголовок"
      bbcode:
        minify: "Стиснути формат виводу BBCode"
      restructuredtext:
        style: "Вибрати стиль рамки таблиці reStructuredText"
        forceSep: "Примусово додати роздільні лінії"
    label:
      ascii:
        forceSep: "Роздільники Рядків"
        style: "Стиль Рамки"
        comment: "Обгортка Коментарів"
      restructuredtext:
        style: "Стиль Рамки"
        forceSep: "Примусові Роздільники"
      bbcode:
        minify: "Мінімізувати Вивід"
      csv:
        doubleQuote: "Обгортка Подвійними Лапками"
        delimiter: "Роздільник Полів"
        bom: "UTF-8 BOM"
        valueDelimiter: "Роздільник Значень"
        rowDelimiter: "Роздільник Рядків"
        prefix: "Префікс Рядка"
        suffix: "Суфікс Рядка"
      excel:
        autoWidth: "Автоширина"
        textFormat: "Текстовий Формат"
        protectSheet: "Захистити Аркуш"
        boldFirstRow: "Жирний Перший Рядок"
        boldFirstColumn: "Жирний Перший Стовпець"
        sheetName: "Ім'я Аркуша"
      html:
        escape: "Екранувати HTML Символи"
        div: "DIV Таблиця"
        minify: "Мінімізувати Код"
        thead: "Структура Заголовка Таблиці"
        tableCaption: "Підпис Таблиці"
        tableClass: "Клас Таблиці"
        tableId: "ID Таблиці"
        rowHeader: "Заголовок Рядка"
        colHeader: "Заголовок Стовпця"
      jira:
        escape: "Екранувати Символи"
        rowHeader: "Заголовок Рядка"
        colHeader: "Заголовок Стовпця"
      json:
        parsingJSON: "Парсити JSON"
        minify: "Мінімізувати Вивід"
        format: "Формат Даних"
        rootName: "Ім'я Кореневого Об'єкта"
        indentSize: "Розмір Відступу"
      jsonlines:
        parsingJSON: "Парсити JSON"
        format: "Формат Даних"
      latex:
        escape: "Екранувати Символи Таблиці LaTeX"
        ht: "Плаваюча Позиція"
        mwe: "Повний Документ"
        tableAlign: "Вирівнювання Таблиці"
        tableBorder: "Стиль Рамки"
        label: "Мітка Посилання"
        caption: "Підпис Таблиці"
        location: "Позиція Підпису"
        tableType: "Тип Таблиці"
        boldFirstRow: "Жирний Перший Рядок"
        boldFirstColumn: "Жирний Перший Стовпець"
        textAlign: "Вирівнювання Тексту"
        borders: "Налаштування Рамки"
      markdown:
        escape: "Екранувати Символи"
        pretty: "Красива Markdown Таблиця"
        simple: "Простий Markdown Формат"
        boldFirstRow: "Жирний Перший Рядок"
        boldFirstColumn: "Жирний Перший Стовпець"
        firstHeader: "Перший Заголовок"
        textAlign: "Вирівнювання Тексту"
        multilineHandling: "Обробка Багаторядкового"

        includeLineNumbers: "Додати Номери Рядків"
        align: "Вирівнювання"
      mediawiki:
        minify: "Мінімізувати Код"
        header: "Розмітка Заголовка"
        sort: "Сортування"
      asciidoc:
        minify: "Мінімізувати Формат"
        firstHeader: "Перший Заголовок"
        lastFooter: "Останній Підвал"
        title: "Заголовок Таблиці"
      tracwiki:
        rowHeader: "Заголовок Рядка"
        colHeader: "Заголовок Стовпця"
      sql:
        drop: "Видалити Таблицю (Якщо Існує)"
        create: "Створити Таблицю"
        oneInsert: "Пакетна Вставка"
        table: "Ім'я Таблиці"
        dialect: "Тип Бази Даних"
        primaryKey: "Первинний Ключ"
      magic:
        builtin: "Вбудований Шаблон"
        rowsTpl: "Шаблон Рядка, Синтаксис ->"
        headerTpl: "Шаблон Заголовка"
        footerTpl: "Шаблон Підвалу"
      textile:
        escape: "Екранувати Символи"
        rowHeader: "Заголовок Рядка"
        thead: "Синтаксис Заголовка Таблиці"
      xml:
        escape: "Екранувати XML Символи"
        minify: "Мінімізувати Вивід"
        rootElement: "Кореневий Елемент"
        rowElement: "Елемент Рядка"
        declaration: "XML Декларація"
        attributes: "Режим Атрибутів"
        cdata: "CDATA Обгортка"
        encoding: "Кодування"
        indentSize: "Розмір Відступу"
      yaml:
        indentSize: "Розмір Відступу"
        arrayStyle: "Стиль Масиву"
        quotationStyle: "Стиль Лапок"
      pdf:
        theme: "Тема PDF Таблиці"
        headerColor: "Колір Заголовка PDF"
        showHead: "Відображення Заголовка PDF"
        docTitle: "Заголовок PDF Документа"
        docDescription: "Опис PDF Документа"
sidebar:
  all: "Всі Інструменти Конвертації"
  dataSource:
    title: "Джерело Даних"
    description:
      converter: "Імпортуйте %s для конвертації в %s. Підтримує завантаження файлів, онлайн-редагування та витягування веб-даних."
      generator: "Створюйте табличні дані з підтримкою кількох методів введення, включаючи ручне введення, імпорт файлів та генерацію шаблонів."
  tableEditor:
    title: "Онлайн Редактор Таблиць"
    description:
      converter: "Обробляйте %s онлайн за допомогою нашого редактора таблиць. Досвід роботи, подібний до Excel, з підтримкою видалення порожніх рядків, дедуплікації, сортування та пошуку і заміни."
      generator: "Потужний онлайн-редактор таблиць, що забезпечує досвід роботи, подібний до Excel. Підтримує видалення порожніх рядків, дедуплікацію, сортування та пошук і заміну."
  tableGenerator:
    title: "Генератор Таблиць"
    description:
      converter: "Швидко генеруйте %s з попереднім переглядом генератора таблиць у реальному часі. Багаті опції експорту, копіювання та завантаження одним кліком."
      generator: "Експортуйте дані %s у кількох форматах для задоволення різних сценаріїв використання. Підтримує користувацькі опції та попередній перегляд у реальному часі."
footer:
  changelog: "Журнал Змін"
  sponsor: "Спонсори"
  contact: "Зв'язатися з Нами"
  privacyPolicy: "Політика Конфіденційності"
  about: "Про Нас"
  resources: "Ресурси"
  popularConverters: "Популярні Конвертери"
  popularGenerators: "Популярні Генератори"
  dataSecurity: "Ваші дані захищені - всі конвертації виконуються у вашому браузері."
converters:
  Markdown:
    alias: "Таблиця Markdown"
    what: "Markdown - це легка мова розмітки, широко використовувана для технічної документації, створення контенту блогів та веб-розробки. Його синтаксис таблиць є стислим та інтуїтивним, підтримує вирівнювання тексту, вбудовування посилань та форматування. Це переважний інструмент для програмістів та технічних письменників, ідеально сумісний з GitHub, GitLab та іншими платформами хостингу коду."
    step1: "Вставте дані таблиці Markdown в область джерела даних або безпосередньо перетягніть файли .md для завантаження. Інструмент автоматично аналізує структуру та форматування таблиці, підтримуючи складний вкладений контент та обробку спеціальних символів."
    step3: "Генеруйте стандартний код таблиці Markdown в реальному часі, підтримуючи кілька методів вирівнювання, виділення тексту жирним, додавання номерів рядків та інші розширені налаштування формату. Згенерований код повністю сумісний з GitHub та основними редакторами Markdown, готовий до використання одним кліком копіювання."
    from_alias: "Файл Таблиці Markdown"
    to_alias: "Формат Таблиці Markdown"
  Magic:
    alias: "Користувацький Шаблон"
    what: "Магічний шаблон - це унікальний розширений генератор даних цього інструменту, що дозволяє користувачам створювати вивід даних довільного формату через користувацький синтаксис шаблону. Підтримує заміну змінних, умовні судження та обробку циклів. Це остаточне рішення для обробки складних потреб конвертації даних та персоналізованих форматів виводу, особливо підходить для розробників та інженерів даних."
    step1: "Виберіть вбудовані загальні шаблони або створіть користувацький синтаксис шаблону. Підтримує багаті змінні та функції, які можуть обробляти складні структури даних та бізнес-логіку."
    step3: "Генеруйте вивід даних, який повністю відповідає вимогам користувацького формату. Підтримує складну логіку конвертації даних та умовну обробку, значно покращуючи ефективність обробки даних та якість виводу. Потужний інструмент для пакетної обробки даних."
    from_alias: "Дані Таблиці"
    to_alias: "Вивід Користувацького Формату"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) - це найбільш широко використовуваний формат обміну даними, ідеально підтримуваний Excel, Google Sheets, системами баз даних та різними інструментами аналізу даних. Його проста структура та сильна сумісність роблять його стандартним форматом для міграції даних, пакетного імпорту/експорту та міжплатформного обміну даними, широко використовуваним в бізнес-аналізі, науці про дані та системній інтеграції."
    step1: "Завантажте файли CSV або безпосередньо вставте дані CSV. Інструмент розумно розпізнає різні роздільники (кома, табуляція, крапка з комою, вертикальна риска тощо), автоматично виявляє типи даних та формати кодування, підтримуючи швидкий аналіз великих файлів та складних структур даних."
    step3: "Генеруйте стандартні файли формату CSV з підтримкою користувацьких роздільників, стилів лапок, форматів кодування та налаштувань позначки BOM. Забезпечує ідеальну сумісність з цільовими системами, надаючи опції завантаження та стиснення для задоволення потреб обробки даних корпоративного рівня."
    from_alias: "Файл Даних CSV"
    to_alias: "Стандартний Формат CSV"
  JSON:
    alias: "JSON Масив"
    what: "JSON (JavaScript Object Notation) - це стандартний формат табличних даних для сучасних веб-додатків, REST API та архітектур мікросервісів. Його чітка структура та ефективний парсинг роблять його широко використовуваним у взаємодії даних фронтенду та бекенду, зберіганні файлів конфігурації та NoSQL базах даних. Підтримує вкладені об'єкти, структури масивів та кілька типів даних, роблячи його незамінними табличними даними для сучасної розробки програмного забезпечення."
    step1: "Завантажте JSON файли або вставте JSON масиви. Підтримує автоматичне розпізнавання та парсинг масивів об'єктів, вкладених структур та складних типів даних. Інструмент інтелектуально валідує JSON синтаксис та надає підказки про помилки."
    step3: "Генеруйте кілька форматів виводу JSON: стандартні масиви об'єктів, 2D масиви, масиви стовпців та формати пар ключ-значення. Підтримує красивий вивід, режим стиснення, користувацькі імена кореневих об'єктів та налаштування відступів, ідеально адаптуючись до різних API інтерфейсів та потреб зберігання даних."
    from_alias: "JSON Файл Масиву"
    to_alias: "JSON Стандартний Формат"
  JSONLines:
    alias: "JSONLines Формат"
    what: "JSON Lines (також відомий як NDJSON) - це важливий формат для обробки великих даних та передачі потокових даних, з кожним рядком, що містить незалежний JSON об'єкт. Широко використовується в аналізі логів, обробці потоків даних, машинному навчанні та розподілених системах. Підтримує інкрементальну обробку та паралельні обчислення, роблячи його ідеальним вибором для обробки великомасштабних структурованих даних."
    step1: "Завантажте JSONLines файли або вставте дані. Інструмент парсить JSON об'єкти рядок за рядком, підтримуючи потокову обробку великих файлів та функціональність пропуску рядків з помилками."
    step3: "Генеруйте стандартний формат JSONLines з кожним рядком, що виводить повний JSON об'єкт. Підходить для потокової обробки, пакетного імпорту та сценаріїв аналізу великих даних, підтримуючи валідацію даних та оптимізацію формату."
    from_alias: "JSONLines Дані"
    to_alias: "JSONLines Потоковий Формат"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) - це стандартний формат для корпоративного обміну даними та управління конфігурацією, з суворими специфікаціями синтаксису та потужними механізмами валідації. Широко використовується у веб-сервісах, файлах конфігурації, зберіганні документів та системній інтеграції. Підтримує простори імен, валідацію схем та XSLT трансформацію, роблячи його важливими табличними даними для корпоративних додатків."
    step1: "Завантажте XML файли або вставте XML дані. Інструмент автоматично парсить XML структуру та конвертує її в табличний формат, підтримуючи простори імен, обробку атрибутів та складні вкладені структури."
    step3: "Генеруйте XML вивід, що відповідає XML стандартам. Підтримує користувацькі кореневі елементи, імена елементів рядків, режими атрибутів, обгортання CDATA та налаштування кодування символів. Забезпечує цілісність та сумісність даних, відповідаючи вимогам корпоративних додатків."
    from_alias: "XML Файл Даних"
    to_alias: "XML Стандартний Формат"
  YAML:
    alias: "YAML Конфігурація"
    what: "YAML - це дружній до людини стандарт серіалізації даних, відомий своєю чіткою ієрархічною структурою та стислим синтаксисом. Широко використовується в файлах конфігурації, ланцюжках інструментів DevOps, Docker Compose та розгортанні Kubernetes. Його сильна читабельність та стислий синтаксис роблять його важливим форматом конфігурації для сучасних хмарно-нативних додатків та автоматизованих операцій."
    step1: "Завантажте YAML файли або вставте YAML дані. Інструмент інтелектуально парсить YAML структуру та валідує правильність синтаксису, підтримуючи багатодокументні формати та складні типи даних."
    step3: "Генеруйте стандартний YAML формат виводу з підтримкою блокових та потокових стилів масивів, кількох налаштувань лапок, користувацьких відступів та збереження коментарів. Забезпечує повну сумісність вихідних YAML файлів з різними парсерами та системами конфігурації."
    from_alias: "YAML Файл Конфігурації"
    to_alias: "YAML Стандартний Формат"
  MySQL:
      alias: "MySQL Результати Запитів"
      what: "MySQL - це найпопулярніша у світі система управління реляційними базами даних з відкритим кодом, відома своєю високою продуктивністю, надійністю та простотою використання. Широко використовується у веб-додатках, корпоративних системах та платформах аналізу даних. Результати запитів MySQL зазвичай містять структуровані табличні дані, служачи важливим джерелом даних у роботі з управління базами даних та аналізу даних."
      step1: "Вставте результати виводу запитів MySQL в область джерела даних. Інструмент автоматично розпізнає та парсить формат виводу командного рядка MySQL, підтримуючи різні стилі результатів запитів та кодування символів, інтелектуально обробляючи заголовки та рядки даних."
      step3: "Швидко конвертуйте результати запитів MySQL у кілька форматів табличних даних, полегшуючи аналіз даних, генерацію звітів, міграцію даних між системами та валідацію даних. Практичний інструмент для адміністраторів баз даних та аналітиків даних."
      from_alias: "MySQL Вивід Запиту"
      to_alias: "MySQL Табличні Дані"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) - це стандартна мова операцій для реляційних баз даних, що використовується для операцій запиту, вставки, оновлення та видалення даних. Як основна технологія управління базами даних, SQL широко використовується в аналізі даних, бізнес-аналітиці, ETL обробці та побудові сховищ даних. Це незамінний інструмент навичок для професіоналів даних."
    step1: "Вставте оператори INSERT SQL або завантажте .sql файли. Інструмент інтелектуально парсить SQL синтаксис та витягує табличні дані, підтримуючи кілька SQL діалектів та обробку складних операторів запитів."
    step3: "Генеруйте стандартні оператори SQL INSERT та оператори створення таблиць. Підтримує кілька діалектів баз даних (MySQL, PostgreSQL, SQLite, SQL Server, Oracle), автоматично обробляє відображення типів даних, екранування символів та обмеження первинних ключів. Забезпечує можливість прямого виконання згенерованого SQL коду."
    from_alias: "SQL Файл Даних"
    to_alias: "SQL Стандартний Оператор"
  Qlik:
      alias: "Qlik Таблиця"
      what: "Qlik - це постачальник програмного забезпечення, що спеціалізується на продуктах візуалізації даних, виконавчих панелях та самообслуговуючій бізнес-аналітиці, разом з Tableau та Microsoft."
      step1: ""
      step3: "Нарешті, [Генератор Таблиць](#TableGenerator) показує результати конвертації. Використовуйте у вашому Qlik Sense, Qlik AutoML, QlikView або іншому програмному забезпеченні з підтримкою Qlik."
      from_alias: "Qlik Таблиця"
      to_alias: "Qlik Таблиця"
  DAX:
      alias: "DAX Таблиця"
      what: "DAX (Data Analysis Expressions) - це мова програмування, що використовується в Microsoft Power BI для створення обчислюваних стовпців, мір та користувацьких таблиць."
      step1: ""
      step3: "Нарешті, [Генератор Таблиць](#TableGenerator) показує результати конвертації. Як очікувалося, він використовується в кількох продуктах Microsoft, включаючи Microsoft Power BI, Microsoft Analysis Services та Microsoft Power Pivot для Excel."
      from_alias: "DAX Таблиця"
      to_alias: "DAX Таблиця"
  Firebase:
    alias: "Firebase Список"
    what: "Firebase - це платформа розробки додатків BaaS, що надає хостингові бекенд-сервіси, такі як база даних реального часу, хмарне сховище, аутентифікація, звітування про збої тощо."
    step1: ""
    step3: "Нарешті, [Генератор Таблиць](#TableGenerator) показує результати конвертації. Потім ви можете використовувати метод push у Firebase API для додавання до списку даних у базі даних Firebase."
    from_alias: "Firebase Список"
    to_alias: "Firebase Список"
  HTML:
    alias: "HTML Таблиця"
    what: "HTML таблиці - це стандартний спосіб відображення структурованих даних на веб-сторінках, побудований за допомогою тегів table, tr, td та інших. Підтримує багате налаштування стилів, адаптивне компонування та інтерактивну функціональність. Широко використовується в розробці веб-сайтів, відображенні даних та генерації звітів, служачи важливим компонентом фронтенд-розробки та веб-дизайну."
    step1: "Вставте HTML код, що містить таблиці, або завантажте HTML файли. Інструмент автоматично розпізнає та витягне дані таблиць зі сторінок, підтримуючи складні HTML структури, CSS стилі та обробку вкладених таблиць."
    step3: "Генеруйте семантичний HTML код таблиць з підтримкою структури thead/tbody, налаштувань CSS класів, заголовків таблиць, заголовків рядків/стовпців та конфігурації адаптивних атрибутів. Забезпечує відповідність згенерованого коду таблиць веб-стандартам з хорошою доступністю та SEO-дружелюбністю."
    from_alias: "HTML Веб Таблиця"
    to_alias: "HTML Стандартна Таблиця"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel - це найпопулярніше у світі програмне забезпечення для роботи з електронними таблицями, широко використовується в бізнес-аналізі, фінансовому управлінні, обробці даних та створенні звітів. Його потужні можливості обробки даних, багата бібліотека функцій та гнучкі функції візуалізації роблять його стандартним інструментом для офісної автоматизації та аналізу даних, з широким застосуванням майже в усіх галузях та сферах."
    step1: "Завантажте файли Excel (підтримує формати .xlsx, .xls) або скопіюйте дані таблиці безпосередньо з Excel та вставте. Інструмент підтримує обробку кількох робочих аркушів, розпізнавання складних форматів та швидкий аналіз великих файлів, автоматично обробляючи об'єднані комірки та типи даних."
    step3: "Генеруйте сумісні з Excel дані таблиць, які можна безпосередньо вставити в Excel або завантажити як стандартні файли .xlsx. Підтримує найменування робочих аркушів, форматування комірок, автоматичну ширину стовпців, стилізацію заголовків та налаштування перевірки даних. Забезпечує професійний вигляд та повну функціональність вихідних файлів Excel."
    from_alias: "Excel Електронна Таблиця"
    to_alias: "Excel Стандартний Формат"
  LaTeX:
    alias: "LaTeX Таблиця"
    what: "LaTeX - це професійна система верстки документів, особливо підходяща для створення академічних статей, технічних документів та наукових публікацій. Його функціональність таблиць є потужною, підтримуючи складні математичні формули, точний контроль макета та високоякісний PDF вивід. Це стандартний інструмент в академічних колах та науковому видавництві, широко використовується у журнальних статтях, дисертаціях та верстці технічних посібників."
    step1: "Вставте код таблиці LaTeX або завантажте .tex файли. Інструмент парсить синтаксис таблиці LaTeX та витягує вміст даних, підтримуючи кілька середовищ таблиць (tabular, longtable, array тощо) та складні команди форматування."
    step3: "Генеруйте професійний код таблиці LaTeX з підтримкою вибору кількох середовищ таблиць, конфігурації стилю рамки, налаштувань позиції підпису, специфікації класу документа та управління пакетами. Може генерувати повні компільовані LaTeX документи, забезпечуючи відповідність вихідних таблиць стандартам академічного видавництва."
    from_alias: "LaTeX Таблиця Документа"
    to_alias: "LaTeX Професійний Формат"
  ASCII:
    alias: "ASCII Таблиця"
    what: "ASCII таблиці використовують символи звичайного тексту для малювання рамок та структур таблиць, забезпечуючи найкращу сумісність та портативність. Сумісні з усіма текстовими редакторами, терміналами та операційними системами. Широко використовуються в документації коду, технічних посібниках, README файлах та виводі інструментів командного рядка. Переважний формат відображення даних для програмістів та системних адміністраторів."
    step1: "Завантажте текстові файли, що містять ASCII таблиці, або безпосередньо вставте дані таблиці. Інструмент інтелектуально розпізнає та парсить структури ASCII таблиць, підтримуючи кілька стилів рамок та форматів вирівнювання."
    step3: "Генеруйте красиві ASCII таблиці звичайного тексту з підтримкою кількох стилів рамок (одинарна лінія, подвійна лінія, закруглені кути тощо), методів вирівнювання тексту та автоматичної ширини стовпців. Згенеровані таблиці ідеально відображаються в редакторах коду, документах та командних рядках."
    from_alias: "ASCII Текстова Таблиця"
    to_alias: "ASCII Стандартний Формат"
  MediaWiki:
    alias: "MediaWiki Таблиця"
    what: "MediaWiki - це платформа програмного забезпечення з відкритим кодом, що використовується відомими вікі-сайтами, такими як Wikipedia. Його синтаксис таблиць є стислим, але потужним, підтримуючи налаштування стилю таблиць, функціональність сортування та вбудовування посилань. Широко використовується в управлінні знаннями, спільному редагуванні та системах управління контентом, служачи основною технологією для побудови вікі-енциклопедій та баз знань."
    step1: "Вставте код таблиці MediaWiki або завантажте файли джерел вікі. Інструмент парсить синтаксис розмітки вікі та витягує дані таблиці, підтримуючи складний синтаксис вікі та обробку шаблонів."
    step3: "Генеруйте стандартний код таблиці MediaWiki з підтримкою налаштувань стилю заголовка, вирівнювання комірок, увімкнення функціональності сортування та опцій стиснення коду. Згенерований код може бути безпосередньо використаний для редагування вікі-сторінок, забезпечуючи ідеальне відображення на платформах MediaWiki."
    from_alias: "MediaWiki Вихідний Код"
    to_alias: "MediaWiki Синтаксис Таблиці"
  TracWiki:
    alias: "TracWiki Таблиця"
    what: "Trac - це веб-система управління проектами та відстеження помилок, що використовує спрощений синтаксис вікі для створення табличного контенту."
    step1: "Завантажте файли TracWiki або вставте дані таблиці."
    step3: "Генеруйте сумісний з TracWiki код таблиці з підтримкою налаштувань заголовків рядків/стовпців, полегшуючи управління документами проекту."
    from_alias: "TracWiki Таблиця"
    to_alias: "TracWiki Формат"
  AsciiDoc:
    alias: "AsciiDoc Таблиця"
    what: "AsciiDoc - це легка мова розмітки, яка може бути конвертована в HTML, PDF, сторінки посібників та інші формати, широко використовується для написання технічної документації."
    step1: "Завантажте файли AsciiDoc або вставте дані."
    step3: "Генеруйте синтаксис таблиці AsciiDoc з підтримкою налаштувань заголовка, підвалу та заголовка, безпосередньо використовуваний в редакторах AsciiDoc."
    from_alias: "AsciiDoc Таблиця"
    to_alias: "AsciiDoc Формат"
  reStructuredText:
    alias: "reStructuredText Таблиця"
    what: "reStructuredText - це стандартний формат документації для спільноти Python, що підтримує багатий синтаксис таблиць, зазвичай використовується для генерації документації Sphinx."
    step1: "Завантажте .rst файли або вставте дані reStructuredText."
    step3: "Генеруйте стандартні таблиці reStructuredText з підтримкою кількох стилів рамок, безпосередньо використовувані в проектах документації Sphinx."
    from_alias: "reStructuredText Таблиця"
    to_alias: "reStructuredText Формат"
  PHP:
    alias: "PHP Масив"
    what: "PHP - це популярна серверна мова сценаріїв, з масивами як основною структурою даних, широко використовується у веб-розробці та обробці даних."
    step1: "Завантажте файли, що містять PHP масиви, або безпосередньо вставте дані."
    step3: "Генеруйте стандартний код PHP масиву, який може бути безпосередньо використаний у PHP проектах, підтримуючи асоціативні та індексовані формати масивів."
    from_alias: "PHP Масив"
    to_alias: "PHP Код"
  Ruby:
    alias: "Ruby Масив"
    what: "Ruby - це динамічна об'єктно-орієнтована мова програмування зі стислим та елегантним синтаксисом, з масивами як важливою структурою даних."
    step1: "Завантажте файли Ruby або вставте дані масиву."
    step3: "Генеруйте код масиву Ruby, що відповідає специфікаціям синтаксису Ruby, безпосередньо використовуваний у проектах Ruby."
    from_alias: "Ruby Масив"
    to_alias: "Ruby Код"
  ASP:
    alias: "ASP Масив"
    what: "ASP (Active Server Pages) - це серверне середовище сценаріїв Microsoft, що підтримує кілька мов програмування для розробки динамічних веб-сторінок."
    step1: "Завантажте файли ASP або вставте дані масиву."
    step3: "Генеруйте сумісний з ASP код масиву з підтримкою синтаксису VBScript та JScript, використовуваний у проектах ASP.NET."
    from_alias: "ASP Масив"
    to_alias: "ASP Код"
  ActionScript:
    alias: "ActionScript Масив"
    what: "ActionScript - це об'єктно-орієнтована мова програмування, що в основному використовується для розробки додатків Adobe Flash та AIR."
    step1: "Завантажте .as файли або вставте дані ActionScript."
    step3: "Генеруйте код масиву ActionScript, що відповідає стандартам синтаксису AS3, використовуваний для розробки проектів Flash та Flex."
    from_alias: "ActionScript Масив"
    to_alias: "ActionScript Код"
  BBCode:
    alias: "BBCode Таблиця"
    what: "BBCode - це легка мова розмітки, зазвичай використовувана на форумах та в онлайн-спільнотах, що надає просту функціональність форматування, включаючи підтримку таблиць."
    step1: "Завантажте файли, що містять BBCode, або вставте дані."
    step3: "Генеруйте код таблиці BBCode, підходящий для публікації на форумах та створення контенту спільноти, з підтримкою стисненого формату виводу."
    from_alias: "BBCode Таблиця"
    to_alias: "BBCode Формат"
  PDF:
    alias: "PDF Таблиця"
    what: "PDF (Portable Document Format) - це міжплатформний стандарт документів з фіксованим макетом, послідовним відображенням та характеристиками високоякісного друку. Широко використовується в офіційних документах, звітах, рахунках-фактурах, контрактах та академічних статтях. Переважний формат для ділового спілкування та архівування документів, забезпечуючи повністю послідовні візуальні ефекти на різних пристроях та операційних системах."
    step1: "Імпортуйте табличні дані в будь-якому форматі. Інструмент автоматично аналізує структуру даних та виконує інтелектуальний дизайн макета, підтримуючи автоматичну пагінацію великих таблиць та обробку складних типів даних."
    step3: "Генеруйте високоякісні PDF файли таблиць з підтримкою кількох професійних стилів тем (діловий, академічний, мінімалістичний тощо), багатомовних шрифтів, автоматичної пагінації, додавання водяних знаків та оптимізації друку. Забезпечує професійний вигляд вихідних PDF документів, безпосередньо використовуваних для ділових презентацій та офіційних публікацій."
    from_alias: "Табличні Дані"
    to_alias: "PDF Професійний Документ"
  JPEG:
    alias: "JPEG Зображення"
    what: "JPEG - це найбільш широко використовуваний формат цифрових зображень з відмінними ефектами стиснення та широкою сумісністю. Його малий розмір файлу та швидка швидкість завантаження роблять його підходящим для веб-відображення, обміну в соціальних мережах, ілюстрацій документів та онлайн-презентацій. Стандартний формат зображень для цифрових медіа та мережевого спілкування, ідеально підтримуваний майже всіма пристроями та програмним забезпеченням."
    step1: "Імпортуйте табличні дані в будь-якому форматі. Інструмент виконує інтелектуальний дизайн макета та візуальну оптимізацію, автоматично обчислюючи оптимальний розмір та роздільну здатність."
    step3: "Генеруйте високочіткі JPEG зображення таблиць з підтримкою кількох тематичних кольорових схем (світла, темна, дружня до очей тощо), адаптивного макета, оптимізації чіткості тексту та налаштування розміру. Підходить для онлайн-обміну, вставки в документи та використання в презентаціях, забезпечуючи відмінні візуальні ефекти на різних пристроях відображення."
    from_alias: "Табличні Дані"
    to_alias: "JPEG Високочітке Зображення"
  Jira:
    alias: "Jira Таблиця"
    what: "JIRA - це професійне програмне забезпечення для управління проектами та відстеження помилок, розроблене Atlassian, широко використовується в гнучкій розробці, тестуванні програмного забезпечення та співпраці над проектами. Його функціональність таблиць підтримує багаті опції форматування та відображення даних, служачи важливим інструментом для команд розробки програмного забезпечення, менеджерів проектів та персоналу забезпечення якості в управлінні вимогами, відстеженні помилок та звітуванні про прогрес."
    step1: "Завантажте файли, що містять табличні дані, або безпосередньо вставте вміст даних. Інструмент автоматично обробляє табличні дані та екранування спеціальних символів."
    step3: "Генеруйте сумісний з платформою JIRA код таблиці з підтримкою налаштувань стилю заголовка, вирівнювання комірок, обробки екранування символів та оптимізації формату. Згенерований код може бути безпосередньо вставлений в описи проблем JIRA, коментарі або вікі-сторінки, забезпечуючи правильне відображення та рендеринг у системах JIRA."
    from_alias: "Дані Проекту"
    to_alias: "Jira Синтаксис Таблиці"
  Textile:
    alias: "Textile Таблиця"
    what: "Textile - це стисла легка мова розмітки з простим та легким для вивчення синтаксисом, широко використовується в системах управління контентом, блог-платформах та форумних системах. Його синтаксис таблиць є чітким та інтуїтивним, підтримуючи швидке форматування та налаштування стилю. Ідеальний інструмент для створювачів контенту та адміністраторів веб-сайтів для швидкого написання документів та публікації контенту."
    step1: "Завантажте файли формату Textile або вставте дані таблиці. Інструмент парсить синтаксис розмітки Textile та витягує вміст таблиці."
    step3: "Генеруйте стандартний синтаксис таблиці Textile з підтримкою розмітки заголовка, вирівнювання комірок, екранування спеціальних символів та оптимізації формату. Згенерований код може бути безпосередньо використаний у CMS системах, блог-платформах та документних системах, що підтримують Textile, забезпечуючи правильний рендеринг та відображення контенту."
    from_alias: "Textile Документ"
    to_alias: "Textile Синтаксис Таблиці"
  PNG:
    alias: "PNG Зображення"
    what: "PNG (Portable Network Graphics) - це формат зображень без втрат з відмінним стисненням та підтримкою прозорості. Широко використовується у веб-дизайні, цифровій графіці та професійній фотографії. Його висока якість та широка сумісність роблять його ідеальним для скріншотів, логотипів, діаграм та будь-яких зображень, що потребують чітких деталей та прозорих фонів."
    step1: "Імпортуйте табличні дані в будь-якому форматі. Інструмент виконує інтелектуальний дизайн макета та візуальну оптимізацію, автоматично обчислюючи оптимальний розмір та роздільну здатність для PNG виводу."
    step3: "Генеруйте високоякісні PNG зображення таблиць з підтримкою кількох тематичних кольорових схем, прозорих фонів, адаптивного макета та оптимізації чіткості тексту. Ідеально підходить для веб-використання, вставки в документи та професійних презентацій з відмінною візуальною якістю."
    from_alias: "Табличні Дані"
    to_alias: "PNG Високоякісне Зображення"
  TOML:
    alias: "TOML Конфігурація"
    what: "TOML (Tom's Obvious, Minimal Language) - це формат файлу конфігурації, який легко читати та писати. Розроблений бути однозначним та простим, він широко використовується в сучасних програмних проектах для управління конфігурацією. Його чіткий синтаксис та сильна типізація роблять його відмінним вибором для налаштувань додатків та файлів конфігурації проектів."
    step1: "Завантажте TOML файли або вставте дані конфігурації. Інструмент парсить синтаксис TOML та витягує структуровану інформацію конфігурації."
    step3: "Генеруйте стандартний формат TOML з підтримкою вкладених структур, типів даних та коментарів. Згенеровані TOML файли ідеально підходять для конфігурації додатків, інструментів збірки та налаштувань проектів."
    from_alias: "TOML Конфігурація"
    to_alias: "TOML Формат"
  INI:
    alias: "INI Конфігурація"
    what: "INI файли - це прості файли конфігурації, що використовуються багатьма додатками та операційними системами. Їх проста структура пар ключ-значення робить їх легкими для читання та ручного редагування. Широко використовуються в додатках Windows, застарілих системах та простих сценаріях конфігурації, де важлива читабельність людиною."
    step1: "Завантажте INI файли або вставте дані конфігурації. Інструмент парсить синтаксис INI та витягує інформацію конфігурації на основі секцій."
    step3: "Генеруйте стандартний формат INI з підтримкою секцій, коментарів та різних типів даних. Згенеровані INI файли сумісні з більшістю додатків та систем конфігурації."
    from_alias: "INI Конфігурація"
    to_alias: "INI Формат"
  Avro:
    alias: "Avro Схема"
    what: "Apache Avro - це система серіалізації даних, що надає багаті структури даних, компактний бінарний формат та можливості еволюції схем. Широко використовується в обробці великих даних, чергах повідомлень та розподілених системах. Його визначення схеми підтримує складні типи даних та сумісність версій, роблячи його важливим інструментом для інженерів даних та системних архітекторів."
    step1: "Завантажте файли схем Avro або вставте дані. Інструмент парсить визначення схем Avro та витягує інформацію про структуру таблиці."
    step3: "Генеруйте стандартні визначення схем Avro з підтримкою відображення типів даних, обмежень полів та валідації схем. Згенеровані схеми можуть бути безпосередньо використані в екосистемах Hadoop, системах повідомлень Kafka та інших платформах великих даних."
    from_alias: "Avro Схема"
    to_alias: "Avro Формат Даних"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) - це мовно-нейтральний, платформо-нейтральний, розширюваний механізм Google для серіалізації структурованих даних. Широко використовується в мікросервісах, розробці API та зберіганні даних. Його ефективний бінарний формат та сильна типізація роблять його ідеальним для високопродуктивних додатків та міжмовного спілкування."
    step1: "Завантажте .proto файли або вставте визначення Protocol Buffer. Інструмент парсить синтаксис protobuf та витягує інформацію про структуру повідомлень."
    step3: "Генеруйте стандартні визначення Protocol Buffer з підтримкою типів повідомлень, опцій полів та визначень сервісів. Згенеровані .proto файли можуть бути скомпільовані для кількох мов програмування."
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf Схема"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas - це найпопулярніша бібліотека аналізу даних у Python, з DataFrame як основною структурою даних. Вона надає потужні можливості маніпуляції, очищення та аналізу даних, широко використовується в науці про дані, машинному навчанні та бізнес-аналітиці. Незамінний інструмент для розробників Python та аналітиків даних."
    step1: "Завантажте Python файли, що містять код DataFrame, або вставте дані. Інструмент парсить синтаксис Pandas та витягує інформацію про структуру DataFrame."
    step3: "Генеруйте стандартний код Pandas DataFrame з підтримкою специфікацій типів даних, налаштувань індексу та операцій з даними. Згенерований код може бути безпосередньо виконаний у середовищі Python для аналізу та обробки даних."
    from_alias: "Pandas DataFrame"
    to_alias: "Python Структура Даних"
  RDF:
    alias: "RDF Трійка"
    what: "RDF (Resource Description Framework) - це стандартна модель для обміну даними в Інтернеті, розроблена для представлення інформації про ресурси у формі графа. Широко використовується в семантичному вебі, графах знань та додатках пов'язаних даних. Його трійкова структура дозволяє багате представлення метаданих та семантичних відносин."
    step1: "Завантажте RDF файли або вставте дані трійок. Інструмент парсить синтаксис RDF та витягує семантичні відносини та інформацію про ресурси."
    step3: "Генеруйте стандартний формат RDF з підтримкою різних серіалізацій (RDF/XML, Turtle, N-Triples). Згенерований RDF може бути використаний у додатках семантичного вебу, базах знань та системах пов'язаних даних."
    from_alias: "RDF Дані"
    to_alias: "RDF Семантичний Формат"
  MATLAB:
    alias: "MATLAB Масив"
    what: "MATLAB - це високопродуктивне програмне забезпечення для чисельних обчислень та візуалізації, широко використовується в інженерних обчисленнях, аналізі даних та розробці алгоритмів. Його операції з масивами та матрицями є потужними, підтримуючи складні математичні обчислення та обробку даних. Незамінний інструмент для інженерів, дослідників та спеціалістів з даних."
    step1: "Завантажте файли MATLAB .m або вставте дані масиву. Інструмент аналізує синтаксис MATLAB та витягує інформацію про структуру масиву."
    step3: "Генеруйте стандартний код масиву MATLAB з підтримкою багатовимірних масивів, специфікацій типів даних та найменування змінних. Згенерований код може бути безпосередньо виконаний у середовищі MATLAB для аналізу даних та наукових обчислень."
    from_alias: "MATLAB Масив"
    to_alias: "MATLAB Формат Коду"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame є основною структурою даних у мові програмування R, широко використовується в статистичному аналізі, видобуванні даних та машинному навчанні. R є провідним інструментом для статистичних обчислень та графіки, з DataFrame, що надає потужні можливості маніпуляції даними, статистичного аналізу та візуалізації. Незамінний для спеціалістів з даних, статистиків та дослідників, які працюють з аналізом структурованих даних."
    step1: "Завантажте файли даних R або вставте код DataFrame. Інструмент парсить синтаксис R та витягує інформацію про структуру DataFrame, включаючи типи стовпців, назви рядків та вміст даних."
    step3: "Генеруйте стандартний код R DataFrame з підтримкою специфікацій типів даних, рівнів факторів, назв рядків/стовпців та специфічних для R структур даних. Згенерований код може бути безпосередньо виконаний у середовищі R для статистичного аналізу та обробки даних."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Почати конвертацію"
  start_generating: "Почати генерацію"
  api_docs: "API документація"
related:
  section_title: 'Більше конвертерів {{ if and .from (ne .from "generator") }}{{ .from }} та {{ end }}{{ .to }}'
  section_description: 'Досліджуйте більше конвертерів для форматів {{ if and .from (ne .from "generator") }}{{ .from }} та {{ end }}{{ .to }}. Перетворюйте ваші дані між кількома форматами за допомогою наших професійних онлайн-інструментів конвертації.'
  title: "{{ .from }} до {{ .to }}"
howto:
  step2: "Редагуйте дані за допомогою нашого розширеного онлайн-редактора таблиць з професійними функціями. Підтримує видалення порожніх рядків, видалення дублікатів, транспозицію даних, сортування, пошук і заміну regex та попередній перегляд у реальному часі. Всі зміни автоматично конвертуються у формат %s з точними, надійними результатами."
  section_title: "Як використовувати {{ . }}"
  converter_description: "Навчіться конвертувати {{ .from }} у {{ .to }} за допомогою нашого покрокового посібника. Професійний онлайн-конвертер з розширеними функціями та попереднім переглядом у реальному часі."
  generator_description: "Навчіться створювати професійні таблиці {{ .to }} за допомогою нашого онлайн-генератора. Редагування як у Excel, попередній перегляд у реальному часі та можливості миттєвого експорту."
extension:
  section_title: "Розширення виявлення та вилучення таблиць"
  section_description: "Витягуйте таблиці з будь-якого веб-сайту одним кліком. Миттєво конвертуйте у 30+ форматів, включаючи Excel, CSV, JSON - копіювання та вставка не потрібні."
  features:
    extraction_title: "Витягування таблиць одним кліком"
    extraction_description: "Миттєво витягуйте таблиці з будь-якої веб-сторінки без копіювання-вставлення - професійне витягування даних стало простим"
    formats_title: "Підтримка конвертера 30+ форматів"
    formats_description: "Конвертуйте витягнуті таблиці в Excel, CSV, JSON, Markdown, SQL та інші формати за допомогою нашого розширеного конвертера таблиць"
    detection_title: "Розумне виявлення таблиць"
    detection_description: "Автоматично виявляє та виділяє таблиці на будь-якій веб-сторінці для швидкого витягування та конвертації даних"
  hover_tip: "✨ Наведіть курсор на будь-яку таблицю, щоб побачити іконку витягування"
recommendations:
  section_title: "Рекомендовано університетами та професіоналами"
  section_description: "TableConvert довіряють професіонали з університетів, дослідницьких інститутів та команд розробки для надійної конвертації таблиць та обробки даних."
  cards:
    university_title: "Університет Вісконсін-Медісон"
    university_description: "TableConvert.com - Професійний безкоштовний онлайн-конвертер таблиць та інструмент форматів даних"
    university_link: "Прочитати статтю"
    facebook_title: "Спільнота професіоналів даних"
    facebook_description: "Поділено та рекомендовано аналітиками даних та професіоналами в групах розробників Facebook"
    facebook_link: "Переглянути пост"
    twitter_title: "Спільнота розробників"
    twitter_description: "Рекомендовано @xiaoying_eth та іншими розробниками на X (Twitter) для конвертації таблиць"
    twitter_link: "Переглянути твіт"
faq:
  section_title: "Часті запитання"
  section_description: "Поширені запитання про наш безкоштовний онлайн-конвертер таблиць, формати даних та процес конвертації."
  what: "Що таке формат %s?"
  howto_convert:
    question: "Як використовувати {{ . }} безкоштовно?"
    answer: "Завантажте свій файл {{ .from }}, вставте дані або витягніть з веб-сторінок за допомогою нашого безкоштовного онлайн-конвертера таблиць. Наш професійний інструмент конвертера миттєво перетворює ваші дані у формат {{ .to }} з попереднім переглядом у реальному часі та розширеними функціями редагування. Завантажте або скопіюйте конвертований результат негайно."
  security:
    question: "Чи безпечні мої дані при використанні цього онлайн-конвертера?"
    answer: "Абсолютно! Всі конвертації таблиць відбуваються локально у вашому браузері - ваші дані ніколи не залишають ваш пристрій. Наш онлайн-конвертер обробляє все на стороні клієнта, забезпечуючи повну конфіденційність та безпеку даних. Жодні файли не зберігаються на наших серверах."
  free:
    question: "Чи справді TableConvert безкоштовний для використання?"
    answer: "Так, TableConvert повністю безкоштовний! Всі функції конвертера, редактор таблиць, інструменти генератора даних та опції експорту доступні без вартості, реєстрації або прихованих платежів. Конвертуйте необмежену кількість файлів онлайн безкоштовно."
  filesize:
    question: "Які обмеження розміру файлів має онлайн-конвертер?"
    answer: "Наш безкоштовний онлайн-конвертер таблиць підтримує файли до 10МБ. Для більших файлів, пакетної обробки або корпоративних потреб використовуйте наше розширення браузера або професійну службу API з вищими обмеженнями."
stats:
  conversions: "Конвертовані таблиці"
  tables: "Згенеровані таблиці"
  formats: "Формати файлів даних"
  rating: "Рейтинг користувачів"
