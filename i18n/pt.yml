site:
  fullname: "Conversor de Tabelas Online"
  name: "TableConvert"
  subtitle: "Conversor e Gerador de Tabelas Online Gratuito"
  intro: "TableConvert é uma ferramenta gratuita online de conversão de tabelas e geração de dados que suporta conversão entre mais de 30 formatos incluindo Excel, CSV, JSON, Markdown, LaTeX, SQL e mais."
  followTwitter: "Siga-nos no X"
title:
  converter: "%s para %s"
  generator: "Gerador %s"
post:
  tags:
    converter: "Conversor"
    editor: "Editor"
    generator: "Gerador"
    maker: "Construtor"
  converter:
    title: "Converter %s para %s Online"
    short: "Uma ferramenta online gratuita e poderosa de %s para %s"
    intro: "Conversor online de %s para %s fácil de usar. Transforme dados de tabela sem esforço com nossa ferramenta de conversão intuitiva. Rápido, confiável e amigável ao usuário."
  generator:
    title: "Editor e Gerador %s Online"
    short: "Ferramenta profissional de geração online %s com recursos abrangentes"
    intro: "Gerador %s online e editor de tabela fácil de usar. Crie tabelas de dados profissionais sem esforço com nossa ferramenta intuitiva e visualização em tempo real."
navbar:
  search:
    placeholder: "Buscar conversor..."
  sponsor: "Nos Pague um Café"
  extension: "Extensão"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Fonte de Dados"
    placeholder: "Cole seus dados %s ou arraste arquivos %s aqui"
    example: "Exemplo"
    upload: "Carregar Arquivo"
    extract:
      enter: "Extrair de Página Web"
      intro: "Digite uma URL de página web contendo dados de tabela para extrair automaticamente dados estruturados"
      btn: "Extrair %s"
    excel:
      sheet: "Planilha"
      none: "Nenhuma"
  tableEditor:
    title: "Editor de Tabelas Online"
    undo: "Desfazer"
    redo: "Refazer"
    transpose: "Transpor"
    clear: "Limpar"
    deleteBlank: "Deletar Vazios"
    deleteDuplicate: "Remover Duplicatas"
    uppercase: "MAIÚSCULAS"
    lowercase: "minúsculas"
    capitalize: "Capitalizar"
    replace:
      replace: "Localizar e Substituir (Regex suportado)"
      subst: "Substituir por..."
      btn: "Substituir Tudo"
  tableGenerator:
    title: "Gerador de Tabelas"
    sponsor: "Nos Pague um Café"
    copy: "Copiar para Área de Transferência"
    download: "Baixar Arquivo"
    tooltip:
      html:
        escape: "Escapar caracteres especiais HTML (&, <, >, \", ') para prevenir erros de exibição"
        div: "Usar layout DIV+CSS em vez de tags TABLE tradicionais, mais adequado para design responsivo"
        minify: "Remover espaços em branco e quebras de linha para gerar código HTML comprimido"
        thead: "Gerar estrutura padrão de cabeçalho (&lt;thead&gt;) e corpo (&lt;tbody&gt;) da tabela"
        tableCaption: "Adicionar título descritivo acima da tabela (elemento &lt;caption&gt;)"
        tableClass: "Adicionar nome de classe CSS à tabela para personalização fácil de estilo"
        tableId: "Definir identificador ID único para a tabela para manipulação JavaScript"
      jira:
        escape: "Escapar caracteres pipe (|) para evitar conflitos com sintaxe de tabela Jira"
      json:
        parsingJSON: "Analisar inteligentemente strings JSON em células em objetos"
        minify: "Gerar formato JSON compacto de linha única para reduzir tamanho do arquivo"
        format: "Selecionar estrutura de dados JSON de saída: array de objetos, array 2D, etc."
      latex:
        escape: "Escapar caracteres especiais LaTeX (%, &, _, #, $, etc.) para garantir compilação adequada"
        ht: "Adicionar parâmetro de posição flutuante [!ht] para controlar posição da tabela na página"
        mwe: "Gerar documento LaTeX completo"
        tableAlign: "Definir alinhamento horizontal da tabela na página"
        tableBorder: "Configurar estilo de borda da tabela: sem borda, borda parcial, borda completa"
        label: "Definir rótulo da tabela para referência cruzada do comando \\ref{}"
        caption: "Definir legenda da tabela para exibir acima ou abaixo da tabela"
        location: "Escolher posição de exibição da legenda da tabela: acima ou abaixo"
        tableType: "Escolher tipo de ambiente da tabela: tabular, longtable, array, etc."
      markdown:
        escape: "Escapar caracteres especiais Markdown (*, _, |, \\, etc.) para evitar conflitos de formato"
        pretty: "Auto-alinhar larguras de colunas para gerar formato de tabela mais bonito"
        simple: "Usar sintaxe simplificada, omitindo linhas verticais de borda externa"
        boldFirstRow: "Tornar o texto da primeira linha em negrito"
        boldFirstColumn: "Tornar o texto da primeira coluna em negrito"
        firstHeader: "Tratar primeira linha como cabeçalho e adicionar linha separadora"
        textAlign: "Definir alinhamento de texto da coluna: esquerda, centro, direita"
        multilineHandling: "Manipulação de texto multilinha: preservar quebras de linha, escapar para \\n, usar tags &lt;br&gt;"

        includeLineNumbers: "Adicionar coluna de números de linha no lado esquerdo da tabela"
      magic:
        builtin: "Selecionar formatos de modelo comum predefinidos"
        rowsTpl: "<table> <tr> <th>Sintaxe Mágica</th> <th>Descrição</th> <th>Métodos JS Suportados</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>1º, 2º ... campo do <b>c</b>abeçalho, Também {hA} {hB} ...</td> <td>Métodos de string</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>1º, 2º ... campo da linha atual, Também {$A} {$B} ...</td> <td>Métodos de string</td> </tr> <tr> <td>{F,} {F;}</td> <td>Dividir a linha atual pela string após <b>F</b></td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td><b>N</b>úmero da linha atual a partir de 1 ou 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>N</b>úmero da linha <b>f</b>inal das linhas </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>E<b>x</b>ecutar código JavaScript, ex: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Usar barra invertida <b>\\</b> para saída de chaves {...} </td> <td></td> </tr></table>"
        headerTpl: "Modelo de saída personalizado para seção de cabeçalho"
        footerTpl: "Modelo de saída personalizado para seção de rodapé"
      textile:
        escape: "Escapar caracteres de sintaxe Textile (|, ., -, ^) para evitar conflitos de formato"
        rowHeader: "Definir primeira linha como linha de cabeçalho"
        thead: "Adicionar marcadores de sintaxe Textile para cabeça e corpo da tabela"
      xml:
        escape: "Escapar caracteres especiais XML (&lt;, &gt;, &amp;, \", ') para garantir XML válido"
        minify: "Gerar saída XML comprimida, removendo espaços em branco extras"
        rootElement: "Definir nome da tag do elemento raiz XML"
        rowElement: "Definir nome da tag do elemento XML para cada linha de dados"
        declaration: "Adicionar cabeçalho de declaração XML (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Saída de dados como atributos XML em vez de elementos filhos"
        cdata: "Envolver conteúdo de texto com CDATA para proteger caracteres especiais"
        encoding: "Definir formato de codificação de caracteres para documento XML"
        indentation: "Escolher caractere de indentação XML: espaços ou tabs"
      yaml:
        indentSize: "Definir número de espaços para indentação de hierarquia YAML (geralmente 2 ou 4)"
        arrayStyle: "Formato de array: bloco (um item por linha) ou fluxo (formato inline)"
        quotationStyle: "Estilo de aspas de string: sem aspas, aspas simples, aspas duplas"
      pdf:
        theme: "Escolher estilo visual de tabela PDF para documentos profissionais"
        headerColor: "Escolher cor de fundo do cabeçalho da tabela PDF"
        showHead: "Controlar exibição do cabeçalho em páginas PDF"
        docTitle: "Título opcional para o documento PDF"
        docDescription: "Texto de descrição opcional para documento PDF"
      csv:
        bom: "Adicionar marca de ordem de bytes UTF-8 para ajudar Excel e outros softwares a reconhecer codificação"
      excel:
        autoWidth: "Ajustar automaticamente largura da coluna baseada no conteúdo"
        protectSheet: "Habilitar proteção de planilha com senha: tableconvert.com"
      sql:
        primaryKey: "Especificar nome do campo de chave primária para declaração CREATE TABLE"
        dialect: "Selecionar tipo de banco de dados, afetando sintaxe de aspas e tipo de dados"
      ascii:
        forceSep: "Forçar linhas separadoras entre cada linha de dados"
        style: "Selecionar estilo de desenho de borda de tabela ASCII"
        comment: "Adicionar marcadores de comentário para envolver toda a tabela"
      mediawiki:
        minify: "Comprimir código de saída, removendo espaços em branco extras"
        header: "Marcar primeira linha como estilo de cabeçalho"
        sort: "Habilitar funcionalidade de classificação por clique na tabela"
      asciidoc:
        minify: "Comprimir saída de formato AsciiDoc"
        firstHeader: "Definir primeira linha como linha de cabeçalho"
        lastFooter: "Definir última linha como linha de rodapé"
        title: "Adicionar texto de título à tabela"
      tracwiki:
        rowHeader: "Definir primeira linha como cabeçalho"
        colHeader: "Definir primeira coluna como cabeçalho"
      bbcode:
        minify: "Comprimir formato de saída BBCode"
      restructuredtext:
        style: "Selecionar estilo de borda de tabela reStructuredText"
        forceSep: "Forçar linhas separadoras"
    label:
      ascii:
        forceSep: "Separadores de Linha"
        style: "Estilo de Borda"
        comment: "Envolvedor de Comentário"
      restructuredtext:
        style: "Estilo de Borda"
        forceSep: "Forçar Separadores"
      bbcode:
        minify: "Minificar Saída"
      csv:
        doubleQuote: "Envolver com Aspas Duplas"
        delimiter: "Delimitador de Campo"
        bom: "UTF-8 BOM"
        valueDelimiter: "Delimitador de Valor"
        rowDelimiter: "Delimitador de Linha"
        prefix: "Prefixo de Linha"
        suffix: "Sufixo de Linha"
      excel:
        autoWidth: "Largura Automática"
        textFormat: "Formato de Texto"
        protectSheet: "Proteger Planilha"
        boldFirstRow: "Primeira Linha em Negrito"
        boldFirstColumn: "Primeira Coluna em Negrito"
        sheetName: "Nome da Planilha"
      html:
        escape: "Escapar Caracteres HTML"
        div: "Tabela DIV"
        minify: "Minificar Código"
        thead: "Estrutura de Cabeçalho da Tabela"
        tableCaption: "Legenda da Tabela"
        tableClass: "Classe da Tabela"
        tableId: "ID da Tabela"
        rowHeader: "Cabeçalho de Linha"
        colHeader: "Cabeçalho de Coluna"
      jira:
        escape: "Escapar Caracteres"
        rowHeader: "Cabeçalho de Linha"
        colHeader: "Cabeçalho de Coluna"
      json:
        parsingJSON: "Analisar JSON"
        minify: "Minificar Saída"
        format: "Formato de Dados"
        rootName: "Nome do Objeto Raiz"
        indentSize: "Tamanho da Indentação"
      jsonlines:
        parsingJSON: "Analisar JSON"
        format: "Formato de Dados"
      latex:
        escape: "Escapar Caracteres de Tabela LaTeX"
        ht: "Posição Flutuante"
        mwe: "Documento Completo"
        tableAlign: "Alinhamento da Tabela"
        tableBorder: "Estilo de Borda"
        label: "Rótulo de Referência"
        caption: "Legenda da Tabela"
        location: "Posição da Legenda"
        tableType: "Tipo de Tabela"
        boldFirstRow: "Primeira Linha em Negrito"
        boldFirstColumn: "Primeira Coluna em Negrito"
        textAlign: "Alinhamento de Texto"
        borders: "Configurações de Borda"
      markdown:
        escape: "Escapar Caracteres"
        pretty: "Tabela Markdown Bonita"
        simple: "Formato Markdown Simples"
        boldFirstRow: "Primeira Linha em Negrito"
        boldFirstColumn: "Primeira Coluna em Negrito"
        firstHeader: "Primeiro Cabeçalho"
        textAlign: "Alinhamento de Texto"
        multilineHandling: "Manipulação Multilinha"

        includeLineNumbers: "Adicionar Números de Linha"
        align: "Alinhamento"
      mediawiki:
        minify: "Minificar Código"
        header: "Marcação de Cabeçalho"
        sort: "Classificável"
      asciidoc:
        minify: "Minificar Formato"
        firstHeader: "Primeiro Cabeçalho"
        lastFooter: "Último Rodapé"
        title: "Título da Tabela"
      tracwiki:
        rowHeader: "Cabeçalho de Linha"
        colHeader: "Cabeçalho de Coluna"
      sql:
        drop: "Excluir Tabela (Se Existir)"
        create: "Criar Tabela"
        oneInsert: "Inserção em Lote"
        table: "Nome da Tabela"
        dialect: "Tipo de Banco de Dados"
        primaryKey: "Chave Primária"
      magic:
        builtin: "Modelo Integrado"
        rowsTpl: "Modelo de Linha, Sintaxe ->"
        headerTpl: "Modelo de Cabeçalho"
        footerTpl: "Modelo de Rodapé"
      textile:
        escape: "Escapar Caracteres"
        rowHeader: "Cabeçalho de Linha"
        thead: "Sintaxe de Cabeçalho da Tabela"
      xml:
        escape: "Escapar Caracteres XML"
        minify: "Minificar Saída"
        rootElement: "Elemento Raiz"
        rowElement: "Elemento de Linha"
        declaration: "Declaração XML"
        attributes: "Modo de Atributo"
        cdata: "Envolvedor CDATA"
        encoding: "Codificação"
        indentSize: "Tamanho da Indentação"
      yaml:
        indentSize: "Tamanho da Indentação"
        arrayStyle: "Estilo de Array"
        quotationStyle: "Estilo de Aspas"
      pdf:
        theme: "Tema de Tabela PDF"
        headerColor: "Cor do Cabeçalho PDF"
        showHead: "Exibição do Cabeçalho PDF"
        docTitle: "Título do Documento PDF"
        docDescription: "Descrição do Documento PDF"
sidebar:
  all: "Todas as Ferramentas de Conversão"
  dataSource:
    title: "Fonte de Dados"
    description:
      converter: "Importe %s para conversão para %s. Suporta upload de arquivo, edição online e extração de dados web."
      generator: "Crie dados de tabela com suporte para múltiplos métodos de entrada incluindo entrada manual, importação de arquivo e geração de modelo."
  tableEditor:
    title: "Editor de Tabelas Online"
    description:
      converter: "Processe %s online usando nosso editor de tabelas. Experiência de operação similar ao Excel com suporte para deletar linhas vazias, desduplicação, classificação e localizar e substituir."
      generator: "Poderoso editor de tabelas online fornecendo experiência de operação similar ao Excel. Suporta deletar linhas vazias, desduplicação, classificação e localizar e substituir."
  tableGenerator:
    title: "Gerador de Tabelas"
    description:
      converter: "Gere rapidamente %s com visualização em tempo real do gerador de tabelas. Opções ricas de exportação, cópia e download com um clique."
      generator: "Exporte dados %s em múltiplos formatos para atender diferentes cenários de uso. Suporta opções personalizadas e visualização em tempo real."
footer:
  changelog: "Registro de Alterações"
  sponsor: "Patrocinadores"
  contact: "Entre em Contato"
  privacyPolicy: "Política de Privacidade"
  about: "Sobre"
  resources: "Recursos"
  popularConverters: "Conversores Populares"
  popularGenerators: "Geradores Populares"
  dataSecurity: "Seus dados estão seguros - todas as conversões são executadas no seu navegador."
converters:
  Markdown:
    alias: "Tabela Markdown"
    what: "Markdown é uma linguagem de marcação leve amplamente usada para documentação técnica, criação de conteúdo de blog e desenvolvimento web. Sua sintaxe de tabela é concisa e intuitiva, suportando alinhamento de texto, incorporação de links e formatação. É a ferramenta preferida para programadores e escritores técnicos, perfeitamente compatível com GitHub, GitLab e outras plataformas de hospedagem de código."
    step1: "Cole dados de tabela Markdown na área de fonte de dados, ou arraste e solte arquivos .md diretamente para upload. A ferramenta analisa automaticamente a estrutura e formatação da tabela, suportando conteúdo aninhado complexo e manipulação de caracteres especiais."
    step3: "Gere código de tabela Markdown padrão em tempo real, suportando múltiplos métodos de alinhamento, negrito de texto, adição de números de linha e outras configurações de formato avançadas. O código gerado é totalmente compatível com GitHub e principais editores Markdown, pronto para usar com cópia de um clique."
    from_alias: "Arquivo de Tabela Markdown"
    to_alias: "Formato de Tabela Markdown"
  Magic:
    alias: "Modelo Personalizado"
    what: "O modelo Magic é um gerador de dados avançado único desta ferramenta, permitindo aos usuários criar saída de dados de formato arbitrário através de sintaxe de modelo personalizada. Suporta substituição de variáveis, julgamento condicional e processamento de loop. É a solução definitiva para lidar com necessidades complexas de conversão de dados e formatos de saída personalizados, especialmente adequado para desenvolvedores e engenheiros de dados."
    step1: "Selecione modelos comuns integrados ou crie sintaxe de modelo personalizada. Suporta variáveis e funções ricas que podem lidar com estruturas de dados complexas e lógica de negócios."
    step3: "Gere saída de dados que atende completamente aos requisitos de formato personalizado. Suporta lógica de conversão de dados complexa e processamento condicional, melhorando muito a eficiência de processamento de dados e qualidade de saída. Uma ferramenta poderosa para processamento de dados em lote."
    from_alias: "Dados de Tabela"
    to_alias: "Saída de Formato Personalizado"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) é o formato de troca de dados mais amplamente usado, perfeitamente suportado por Excel, Google Sheets, sistemas de banco de dados e várias ferramentas de análise de dados. Sua estrutura simples e forte compatibilidade o tornam o formato padrão para migração de dados, importação/exportação em lote e troca de dados multiplataforma, amplamente usado em análise de negócios, ciência de dados e integração de sistemas."
    step1: "Carregue arquivos CSV ou cole dados CSV diretamente. A ferramenta reconhece inteligentemente vários delimitadores (vírgula, tab, ponto e vírgula, pipe, etc.), detecta automaticamente tipos de dados e formatos de codificação, suportando análise rápida de arquivos grandes e estruturas de dados complexas."
    step3: "Gere arquivos de formato CSV padrão com suporte para delimitadores personalizados, estilos de aspas, formatos de codificação e configurações de marca BOM. Garante compatibilidade perfeita com sistemas de destino, fornecendo opções de download e compressão para atender necessidades de processamento de dados de nível empresarial."
    from_alias: "Arquivo de Dados CSV"
    to_alias: "Formato CSV Padrão"
  JSON:
    alias: "Array JSON"
    what: "JSON (JavaScript Object Notation) é o formato padrão de dados de tabela para aplicações web modernas, APIs REST e arquiteturas de microsserviços. Sua estrutura clara e análise eficiente o tornam amplamente usado na interação de dados front-end e back-end, armazenamento de arquivos de configuração e bancos de dados NoSQL. Suporta objetos aninhados, estruturas de array e múltiplos tipos de dados, tornando-o dados de tabela indispensáveis para desenvolvimento de software moderno."
    step1: "Carregue arquivos JSON ou cole arrays JSON. Suporta reconhecimento automático e análise de arrays de objetos, estruturas aninhadas e tipos de dados complexos. A ferramenta valida inteligentemente a sintaxe JSON e fornece avisos de erro."
    step3: "Gere múltiplas saídas de formato JSON: arrays de objetos padrão, arrays 2D, arrays de colunas e formatos de pares chave-valor. Suporta saída embelezada, modo de compressão, nomes de objetos raiz personalizados e configurações de indentação, adaptando-se perfeitamente a várias interfaces de API e necessidades de armazenamento de dados."
    from_alias: "Arquivo de Array JSON"
    to_alias: "Formato JSON Padrão"
  JSONLines:
    alias: "Formato JSONLines"
    what: "JSON Lines (também conhecido como NDJSON) é um formato importante para processamento de big data e transmissão de dados em streaming, com cada linha contendo um objeto JSON independente. Amplamente usado em análise de logs, processamento de fluxo de dados, aprendizado de máquina e sistemas distribuídos. Suporta processamento incremental e computação paralela, tornando-o a escolha ideal para lidar com dados estruturados em larga escala."
    step1: "Carregue arquivos JSONLines ou cole dados. A ferramenta analisa objetos JSON linha por linha, suportando processamento de streaming de arquivos grandes e funcionalidade de pular linhas com erro."
    step3: "Gere formato JSONLines padrão com cada linha produzindo um objeto JSON completo. Adequado para processamento de streaming, importação em lote e cenários de análise de big data, suportando validação de dados e otimização de formato."
    from_alias: "Dados JSONLines"
    to_alias: "Formato de Streaming JSONLines"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) é o formato padrão para troca de dados de nível empresarial e gerenciamento de configuração, com especificações de sintaxe rigorosas e mecanismos de validação poderosos. Amplamente usado em serviços web, arquivos de configuração, armazenamento de documentos e integração de sistemas. Suporta namespaces, validação de esquema e transformação XSLT, tornando-o dados de tabela importantes para aplicações empresariais."
    step1: "Carregue arquivos XML ou cole dados XML. A ferramenta analisa automaticamente a estrutura XML e a converte para formato de tabela, suportando namespace, manipulação de atributos e estruturas aninhadas complexas."
    step3: "Gere saída XML que está em conformidade com os padrões XML. Suporta elementos raiz personalizados, nomes de elementos de linha, modos de atributo, envolvimento CDATA e configurações de codificação de caracteres. Garante integridade e compatibilidade de dados, atendendo aos requisitos de aplicações de nível empresarial."
    from_alias: "Arquivo de Dados XML"
    to_alias: "Formato XML Padrão"
  YAML:
    alias: "Configuração YAML"
    what: "YAML é um padrão de serialização de dados amigável ao humano, renomado por sua estrutura hierárquica clara e sintaxe concisa. Amplamente usado em arquivos de configuração, cadeias de ferramentas DevOps, Docker Compose e implantação Kubernetes. Sua forte legibilidade e sintaxe concisa o tornam um formato de configuração importante para aplicações nativas da nuvem modernas e operações automatizadas."
    step1: "Carregue arquivos YAML ou cole dados YAML. A ferramenta analisa inteligentemente a estrutura YAML e valida a correção da sintaxe, suportando formatos multi-documento e tipos de dados complexos."
    step3: "Gere saída de formato YAML padrão com suporte para estilos de array de bloco e fluxo, múltiplas configurações de aspas, indentação personalizada e preservação de comentários. Garante que os arquivos YAML de saída sejam totalmente compatíveis com vários analisadores e sistemas de configuração."
    from_alias: "Arquivo de Configuração YAML"
    to_alias: "Formato YAML Padrão"
  MySQL:
      alias: "Resultados de Consulta MySQL"
      what: "MySQL é o sistema de gerenciamento de banco de dados relacional de código aberto mais popular do mundo, renomado por seu alto desempenho, confiabilidade e facilidade de uso. Amplamente usado em aplicações web, sistemas empresariais e plataformas de análise de dados. Os resultados de consulta MySQL tipicamente contêm dados de tabela estruturados, servindo como uma fonte de dados importante no gerenciamento de banco de dados e trabalho de análise de dados."
      step1: "Cole os resultados de saída de consulta MySQL na área de fonte de dados. A ferramenta reconhece automaticamente e analisa o formato de saída da linha de comando MySQL, suportando vários estilos de resultados de consulta e codificações de caracteres, manipulando inteligentemente cabeçalhos e linhas de dados."
      step3: "Converta rapidamente resultados de consulta MySQL para múltiplos formatos de dados de tabela, facilitando análise de dados, geração de relatórios, migração de dados entre sistemas e validação de dados. Uma ferramenta prática para administradores de banco de dados e analistas de dados."
      from_alias: "Saída de Consulta MySQL"
      to_alias: "Dados de Tabela MySQL"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) é a linguagem de operação padrão para bancos de dados relacionais, usada para operações de consulta, inserção, atualização e exclusão de dados. Como a tecnologia central do gerenciamento de banco de dados, SQL é amplamente usado em análise de dados, inteligência de negócios, processamento ETL e construção de data warehouse. É uma ferramenta de habilidade essencial para profissionais de dados."
    step1: "Cole declarações INSERT SQL ou carregue arquivos .sql. A ferramenta analisa inteligentemente a sintaxe SQL e extrai dados de tabela, suportando múltiplos dialetos SQL e processamento de declarações de consulta complexas."
    step3: "Gere declarações INSERT SQL padrão e declarações de criação de tabela. Suporta múltiplos dialetos de banco de dados (MySQL, PostgreSQL, SQLite, SQL Server, Oracle), manipula automaticamente mapeamento de tipos de dados, escape de caracteres e restrições de chave primária. Garante que o código SQL gerado possa ser executado diretamente."
    from_alias: "Arquivo de Dados SQL"
    to_alias: "Declaração SQL Padrão"
  Qlik:
      alias: "Tabela Qlik"
      what: "Qlik é um fornecedor de software especializado em visualização de dados, painéis executivos e produtos de inteligência de negócios de autoatendimento, junto com Tableau e Microsoft."
      step1: ""
      step3: "Finalmente, o [Gerador de Tabelas](#TableGenerator) mostra os resultados da conversão. Use no seu Qlik Sense, Qlik AutoML, QlikView ou outro software habilitado para Qlik."
      from_alias: "Tabela Qlik"
      to_alias: "Tabela Qlik"
  DAX:
      alias: "Tabela DAX"
      what: "DAX (Data Analysis Expressions) é uma linguagem de programação usada em todo o Microsoft Power BI para criar colunas calculadas, medidas e tabelas personalizadas."
      step1: ""
      step3: "Finalmente, o [Gerador de Tabelas](#TableGenerator) mostra os resultados da conversão. Como esperado, é usado em vários produtos Microsoft incluindo Microsoft Power BI, Microsoft Analysis Services e Microsoft Power Pivot para Excel."
      from_alias: "Tabela DAX"
      to_alias: "Tabela DAX"
  Firebase:
    alias: "Lista Firebase"
    what: "Firebase é uma plataforma de desenvolvimento de aplicações BaaS que fornece serviços de backend hospedados como banco de dados em tempo real, armazenamento em nuvem, autenticação, relatórios de falhas, etc."
    step1: ""
    step3: "Finalmente, o [Gerador de Tabelas](#TableGenerator) mostra os resultados da conversão. Você pode então usar o método push na API Firebase para adicionar a uma lista de dados no banco de dados Firebase."
    from_alias: "Lista Firebase"
    to_alias: "Lista Firebase"
  HTML:
    alias: "Tabela HTML"
    what: "As tabelas HTML são a forma padrão de exibir dados estruturados em páginas web, construídas com tags table, tr, td e outras. Suporta personalização rica de estilos, layout responsivo e funcionalidade interativa. Amplamente usado no desenvolvimento de sites, exibição de dados e geração de relatórios, servindo como um componente importante do desenvolvimento front-end e design web."
    step1: "Cole código HTML contendo tabelas ou carregue arquivos HTML. A ferramenta reconhece automaticamente e extrai dados de tabela das páginas, suportando estruturas HTML complexas, estilos CSS e processamento de tabelas aninhadas."
    step3: "Gere código de tabela HTML semântico com suporte para estrutura thead/tbody, configurações de classe CSS, legendas de tabela, cabeçalhos de linha/coluna e configuração de atributos responsivos. Garante que o código de tabela gerado atenda aos padrões web com boa acessibilidade e amigabilidade para SEO."
    from_alias: "Tabela Web HTML"
    to_alias: "Tabela HTML Padrão"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel é o software de planilhas mais popular do mundo, amplamente usado em análise de negócios, gestão financeira, processamento de dados e criação de relatórios. Suas poderosas capacidades de processamento de dados, rica biblioteca de funções e recursos flexíveis de visualização o tornam a ferramenta padrão para automação de escritório e análise de dados, com aplicações extensas em quase todas as indústrias e campos."
    step1: "Carregue arquivos Excel (suporta formatos .xlsx, .xls) ou copie dados de tabela diretamente do Excel e cole. A ferramenta suporta processamento de múltiplas planilhas, reconhecimento de formato complexo e análise rápida de arquivos grandes, manipulando automaticamente células mescladas e tipos de dados."
    step3: "Gere dados de tabela compatíveis com Excel que podem ser colados diretamente no Excel ou baixados como arquivos .xlsx padrão. Suporta nomeação de planilhas, formatação de células, largura automática de colunas, estilização de cabeçalhos e configurações de validação de dados. Garante que os arquivos Excel de saída tenham aparência profissional e funcionalidade completa."
    from_alias: "Planilha Excel"
    to_alias: "Formato Padrão Excel"
  LaTeX:
    alias: "Tabela LaTeX"
    what: "LaTeX é um sistema profissional de composição de documentos, especialmente adequado para criar artigos acadêmicos, documentos técnicos e publicações científicas. Sua funcionalidade de tabela é poderosa, suportando fórmulas matemáticas complexas, controle preciso de layout e saída PDF de alta qualidade. É a ferramenta padrão na academia e publicação científica, amplamente usada em artigos de revistas, dissertações e composição de manuais técnicos."
    step1: "Cole código de tabela LaTeX ou carregue arquivos .tex. A ferramenta analisa a sintaxe de tabela LaTeX e extrai conteúdo de dados, suportando múltiplos ambientes de tabela (tabular, longtable, array, etc.) e comandos de formato complexos."
    step3: "Gere código de tabela LaTeX profissional com suporte para seleção de múltiplos ambientes de tabela, configuração de estilo de borda, configurações de posição de legenda, especificação de classe de documento e gerenciamento de pacotes. Pode gerar documentos LaTeX compiláveis completos, garantindo que as tabelas de saída atendam aos padrões de publicação acadêmica."
    from_alias: "Tabela de Documento LaTeX"
    to_alias: "Formato Profissional LaTeX"
  ASCII:
    alias: "Tabela ASCII"
    what: "Tabelas ASCII usam caracteres de texto simples para desenhar bordas e estruturas de tabela, fornecendo a melhor compatibilidade e portabilidade. Compatível com todos os editores de texto, ambientes de terminal e sistemas operacionais. Amplamente usado em documentação de código, manuais técnicos, arquivos README e saída de ferramentas de linha de comando. O formato de exibição de dados preferido para programadores e administradores de sistema."
    step1: "Carregue arquivos de texto contendo tabelas ASCII ou cole dados de tabela diretamente. A ferramenta reconhece inteligentemente e analisa estruturas de tabela ASCII, suportando múltiplos estilos de borda e formatos de alinhamento."
    step3: "Gere belas tabelas ASCII de texto simples com suporte para múltiplos estilos de borda (linha simples, linha dupla, cantos arredondados, etc.), métodos de alinhamento de texto e largura automática de coluna. Tabelas geradas são exibidas perfeitamente em editores de código, documentos e linhas de comando."
    from_alias: "Tabela de Texto ASCII"
    to_alias: "Formato ASCII Padrão"
  MediaWiki:
    alias: "Tabela MediaWiki"
    what: "MediaWiki é a plataforma de software de código aberto usada por sites wiki famosos como Wikipedia. Sua sintaxe de tabela é concisa, mas poderosa, suportando personalização de estilo de tabela, funcionalidade de classificação e incorporação de links. Amplamente usado em gerenciamento de conhecimento, edição colaborativa e sistemas de gerenciamento de conteúdo, servindo como tecnologia central para construir enciclopédias wiki e bases de conhecimento."
    step1: "Cole código de tabela MediaWiki ou carregue arquivos fonte wiki. A ferramenta analisa sintaxe de marcação wiki e extrai dados de tabela, suportando sintaxe wiki complexa e processamento de modelos."
    step3: "Gere código de tabela MediaWiki padrão com suporte para configurações de estilo de cabeçalho, alinhamento de células, habilitação de funcionalidade de classificação e opções de compressão de código. O código gerado pode ser usado diretamente para edição de páginas wiki, garantindo exibição perfeita em plataformas MediaWiki."
    from_alias: "Código Fonte MediaWiki"
    to_alias: "Sintaxe de Tabela MediaWiki"
  TracWiki:
    alias: "Tabela TracWiki"
    what: "Trac é um sistema de gerenciamento de projetos e rastreamento de bugs baseado na web que usa sintaxe wiki simplificada para criar conteúdo de tabela."
    step1: "Carregue arquivos TracWiki ou cole dados de tabela."
    step3: "Gere código de tabela compatível com TracWiki com suporte para configurações de cabeçalho de linha/coluna, facilitando o gerenciamento de documentos de projeto."
    from_alias: "Tabela TracWiki"
    to_alias: "Formato TracWiki"
  AsciiDoc:
    alias: "Tabela AsciiDoc"
    what: "AsciiDoc é uma linguagem de marcação leve que pode ser convertida para HTML, PDF, páginas de manual e outros formatos, amplamente usada para escrita de documentação técnica."
    step1: "Carregue arquivos AsciiDoc ou cole dados."
    step3: "Gere sintaxe de tabela AsciiDoc com suporte para configurações de cabeçalho, rodapé e título, diretamente utilizável em editores AsciiDoc."
    from_alias: "Tabela AsciiDoc"
    to_alias: "Formato AsciiDoc"
  reStructuredText:
    alias: "Tabela reStructuredText"
    what: "reStructuredText é o formato de documentação padrão para a comunidade Python, suportando sintaxe rica de tabela, comumente usado para geração de documentação Sphinx."
    step1: "Carregue arquivos .rst ou cole dados reStructuredText."
    step3: "Gere tabelas reStructuredText padrão com suporte para múltiplos estilos de borda, diretamente utilizáveis em projetos de documentação Sphinx."
    from_alias: "Tabela reStructuredText"
    to_alias: "Formato reStructuredText"
  PHP:
    alias: "Array PHP"
    what: "PHP é uma linguagem de script do lado do servidor popular, com arrays sendo sua estrutura de dados central, amplamente usada no desenvolvimento web e processamento de dados."
    step1: "Carregue arquivos contendo arrays PHP ou cole dados diretamente."
    step3: "Gere código de array PHP padrão que pode ser usado diretamente em projetos PHP, suportando formatos de array associativo e indexado."
    from_alias: "Array PHP"
    to_alias: "Código PHP"
  Ruby:
    alias: "Array Ruby"
    what: "Ruby é uma linguagem de programação orientada a objetos dinâmica com sintaxe concisa e elegante, com arrays sendo uma estrutura de dados importante."
    step1: "Carregue arquivos Ruby ou cole dados de array."
    step3: "Gere código de array Ruby que está em conformidade com as especificações de sintaxe Ruby, diretamente utilizável em projetos Ruby."
    from_alias: "Array Ruby"
    to_alias: "Código Ruby"
  ASP:
    alias: "Array ASP"
    what: "ASP (Active Server Pages) é o ambiente de script do lado do servidor da Microsoft, suportando múltiplas linguagens de programação para desenvolver páginas web dinâmicas."
    step1: "Carregue arquivos ASP ou cole dados de array."
    step3: "Gere código de array compatível com ASP com suporte para sintaxe VBScript e JScript, utilizável em projetos ASP.NET."
    from_alias: "Array ASP"
    to_alias: "Código ASP"
  ActionScript:
    alias: "Array ActionScript"
    what: "ActionScript é uma linguagem de programação orientada a objetos usada principalmente para desenvolvimento de aplicações Adobe Flash e AIR."
    step1: "Carregue arquivos .as ou cole dados ActionScript."
    step3: "Gere código de array ActionScript que está em conformidade com os padrões de sintaxe AS3, utilizável para desenvolvimento de projetos Flash e Flex."
    from_alias: "Array ActionScript"
    to_alias: "Código ActionScript"
  BBCode:
    alias: "Tabela BBCode"
    what: "BBCode é uma linguagem de marcação leve comumente usada em fóruns e comunidades online, fornecendo funcionalidade de formatação simples incluindo suporte a tabelas."
    step1: "Carregue arquivos contendo BBCode ou cole dados."
    step3: "Gere código de tabela BBCode adequado para postagem em fóruns e criação de conteúdo da comunidade, com suporte para formato de saída comprimido."
    from_alias: "Tabela BBCode"
    to_alias: "Formato BBCode"
  PDF:
    alias: "Tabela PDF"
    what: "PDF (Portable Document Format) é um padrão de documento multiplataforma com layout fixo, exibição consistente e características de impressão de alta qualidade. Amplamente usado em documentos formais, relatórios, faturas, contratos e artigos acadêmicos. O formato preferido para comunicação empresarial e arquivamento de documentos, garantindo efeitos visuais completamente consistentes em diferentes dispositivos e sistemas operacionais."
    step1: "Importe dados de tabela em qualquer formato. A ferramenta analisa automaticamente a estrutura de dados e executa design de layout inteligente, suportando paginação automática de tabelas grandes e processamento de tipos de dados complexos."
    step3: "Gere arquivos de tabela PDF de alta qualidade com suporte para múltiplos estilos de tema profissionais (empresarial, acadêmico, minimalista, etc.), fontes multilíngues, paginação automática, adição de marca d'água e otimização de impressão. Garante que os documentos PDF de saída tenham aparência profissional, diretamente utilizáveis para apresentações empresariais e publicação formal."
    from_alias: "Dados de Tabela"
    to_alias: "Documento PDF Profissional"
  JPEG:
    alias: "Imagem JPEG"
    what: "JPEG é o formato de imagem digital mais amplamente usado com excelentes efeitos de compressão e ampla compatibilidade. Seu tamanho de arquivo pequeno e velocidade de carregamento rápida o tornam adequado para exibição web, compartilhamento em mídias sociais, ilustrações de documentos e apresentações online. O formato de imagem padrão para mídia digital e comunicação de rede, perfeitamente suportado por quase todos os dispositivos e software."
    step1: "Importe dados de tabela em qualquer formato. A ferramenta executa design de layout inteligente e otimização visual, calculando automaticamente tamanho e resolução ideais."
    step3: "Gere imagens de tabela JPEG de alta definição com suporte para múltiplos esquemas de cores de tema (claro, escuro, amigável aos olhos, etc.), layout adaptativo, otimização de clareza de texto e personalização de tamanho. Adequado para compartilhamento online, inserção de documentos e uso em apresentações, garantindo excelentes efeitos visuais em vários dispositivos de exibição."
    from_alias: "Dados de Tabela"
    to_alias: "Imagem JPEG de Alta Definição"
  Jira:
    alias: "Tabela Jira"
    what: "JIRA é um software profissional de gerenciamento de projetos e rastreamento de bugs desenvolvido pela Atlassian, amplamente usado em desenvolvimento ágil, teste de software e colaboração de projetos. Sua funcionalidade de tabela suporta opções ricas de formatação e exibição de dados, servindo como uma ferramenta importante para equipes de desenvolvimento de software, gerentes de projeto e pessoal de garantia de qualidade no gerenciamento de requisitos, rastreamento de bugs e relatórios de progresso."
    step1: "Carregue arquivos contendo dados de tabela ou cole conteúdo de dados diretamente. A ferramenta processa automaticamente dados de tabela e escape de caracteres especiais."
    step3: "Gere código de tabela compatível com a plataforma JIRA com suporte para configurações de estilo de cabeçalho, alinhamento de células, processamento de escape de caracteres e otimização de formato. O código gerado pode ser colado diretamente em descrições de problemas JIRA, comentários ou páginas wiki, garantindo exibição e renderização corretas em sistemas JIRA."
    from_alias: "Dados de Projeto"
    to_alias: "Sintaxe de Tabela Jira"
  Textile:
    alias: "Tabela Textile"
    what: "Textile é uma linguagem de marcação leve concisa com sintaxe simples e fácil de aprender, amplamente usada em sistemas de gerenciamento de conteúdo, plataformas de blog e sistemas de fórum. Sua sintaxe de tabela é clara e intuitiva, suportando formatação rápida e configurações de estilo. Uma ferramenta ideal para criadores de conteúdo e administradores de sites para escrita rápida de documentos e publicação de conteúdo."
    step1: "Carregue arquivos de formato Textile ou cole dados de tabela. A ferramenta analisa sintaxe de marcação Textile e extrai conteúdo de tabela."
    step3: "Gere sintaxe de tabela Textile padrão com suporte para marcação de cabeçalho, alinhamento de células, escape de caracteres especiais e otimização de formato. O código gerado pode ser usado diretamente em sistemas CMS, plataformas de blog e sistemas de documentos que suportam Textile, garantindo renderização e exibição corretas de conteúdo."
    from_alias: "Documento Textile"
    to_alias: "Sintaxe de Tabela Textile"
  PNG:
    alias: "Imagem PNG"
    what: "PNG (Portable Network Graphics) é um formato de imagem sem perdas com excelente compressão e suporte à transparência. Amplamente usado em design web, gráficos digitais e fotografia profissional. Sua alta qualidade e ampla compatibilidade o tornam ideal para capturas de tela, logotipos, diagramas e qualquer imagem que requeira detalhes nítidos e fundos transparentes."
    step1: "Importe dados de tabela em qualquer formato. A ferramenta executa design de layout inteligente e otimização visual, calculando automaticamente tamanho e resolução ideais para saída PNG."
    step3: "Gere imagens de tabela PNG de alta qualidade com suporte para múltiplos esquemas de cores de tema, fundos transparentes, layout adaptativo e otimização de clareza de texto. Perfeito para uso web, inserção de documentos e apresentações profissionais com excelente qualidade visual."
    from_alias: "Dados de Tabela"
    to_alias: "Imagem PNG de Alta Qualidade"
  TOML:
    alias: "Configuração TOML"
    what: "TOML (Tom's Obvious, Minimal Language) é um formato de arquivo de configuração que é fácil de ler e escrever. Projetado para ser inequívoco e simples, é amplamente usado em projetos de software modernos para gerenciamento de configuração. Sua sintaxe clara e tipagem forte o tornam uma excelente escolha para configurações de aplicação e arquivos de configuração de projeto."
    step1: "Carregue arquivos TOML ou cole dados de configuração. A ferramenta analisa sintaxe TOML e extrai informações de configuração estruturadas."
    step3: "Gere formato TOML padrão com suporte para estruturas aninhadas, tipos de dados e comentários. Arquivos TOML gerados são perfeitos para configuração de aplicação, ferramentas de build e configurações de projeto."
    from_alias: "Configuração TOML"
    to_alias: "Formato TOML"
  INI:
    alias: "Configuração INI"
    what: "Arquivos INI são arquivos de configuração simples usados por muitas aplicações e sistemas operacionais. Sua estrutura direta de pares chave-valor os torna fáceis de ler e editar manualmente. Amplamente usados em aplicações Windows, sistemas legados e cenários de configuração simples onde a legibilidade humana é importante."
    step1: "Carregue arquivos INI ou cole dados de configuração. A ferramenta analisa sintaxe INI e extrai informações de configuração baseadas em seções."
    step3: "Gere formato INI padrão com suporte para seções, comentários e vários tipos de dados. Arquivos INI gerados são compatíveis com a maioria das aplicações e sistemas de configuração."
    from_alias: "Configuração INI"
    to_alias: "Formato INI"
  Avro:
    alias: "Schema Avro"
    what: "Apache Avro é um sistema de serialização de dados que fornece estruturas de dados ricas, formato binário compacto e capacidades de evolução de esquema. Amplamente usado em processamento de big data, filas de mensagens e sistemas distribuídos. Sua definição de esquema suporta tipos de dados complexos e compatibilidade de versão, tornando-o uma ferramenta importante para engenheiros de dados e arquitetos de sistema."
    step1: "Carregue arquivos de esquema Avro ou cole dados. A ferramenta analisa definições de esquema Avro e extrai informações de estrutura de tabela."
    step3: "Gere definições de esquema Avro padrão com suporte para mapeamento de tipos de dados, restrições de campo e validação de esquema. Esquemas gerados podem ser usados diretamente em ecossistemas Hadoop, sistemas de mensagens Kafka e outras plataformas de big data."
    from_alias: "Schema Avro"
    to_alias: "Formato de Dados Avro"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) é o mecanismo neutro de linguagem, neutro de plataforma e extensível do Google para serializar dados estruturados. Amplamente usado em microsserviços, desenvolvimento de API e armazenamento de dados. Seu formato binário eficiente e tipagem forte o tornam ideal para aplicações de alto desempenho e comunicação entre linguagens."
    step1: "Carregue arquivos .proto ou cole definições de Protocol Buffer. A ferramenta analisa sintaxe protobuf e extrai informações de estrutura de mensagem."
    step3: "Gere definições de Protocol Buffer padrão com suporte para tipos de mensagem, opções de campo e definições de serviço. Arquivos .proto gerados podem ser compilados para múltiplas linguagens de programação."
    from_alias: "Protocol Buffer"
    to_alias: "Schema Protobuf"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas é a biblioteca de análise de dados mais popular em Python, com DataFrame sendo sua estrutura de dados central. Fornece capacidades poderosas de manipulação, limpeza e análise de dados, amplamente usada em ciência de dados, aprendizado de máquina e inteligência de negócios. Uma ferramenta indispensável para desenvolvedores Python e analistas de dados."
    step1: "Carregue arquivos Python contendo código DataFrame ou cole dados. A ferramenta analisa sintaxe Pandas e extrai informações de estrutura DataFrame."
    step3: "Gere código Pandas DataFrame padrão com suporte para especificações de tipos de dados, configurações de índice e operações de dados. O código gerado pode ser executado diretamente no ambiente Python para análise e processamento de dados."
    from_alias: "Pandas DataFrame"
    to_alias: "Estrutura de Dados Python"
  RDF:
    alias: "RDF Triple"
    what: "RDF (Resource Description Framework) é um modelo padrão para intercâmbio de dados na Web, projetado para representar informações sobre recursos em forma de grafo. Amplamente usado em web semântica, grafos de conhecimento e aplicações de dados vinculados. Sua estrutura tripla permite representação rica de metadados e relacionamentos semânticos."
    step1: "Carregue arquivos RDF ou cole dados triplos. A ferramenta analisa sintaxe RDF e extrai relacionamentos semânticos e informações de recursos."
    step3: "Gere formato RDF padrão com suporte para várias serializações (RDF/XML, Turtle, N-Triples). RDF gerado pode ser usado em aplicações de web semântica, bases de conhecimento e sistemas de dados vinculados."
    from_alias: "Dados RDF"
    to_alias: "Formato Semântico RDF"
  MATLAB:
    alias: "Array MATLAB"
    what: "MATLAB é um software de computação numérica e visualização de alto desempenho amplamente usado em computação de engenharia, análise de dados e desenvolvimento de algoritmos. Suas operações de array e matriz são poderosas, suportando cálculos matemáticos complexos e processamento de dados. Uma ferramenta essencial para engenheiros, pesquisadores e cientistas de dados."
    step1: "Carregue arquivos .m MATLAB ou cole dados de array. A ferramenta analisa sintaxe MATLAB e extrai informações de estrutura de array."
    step3: "Gere código de array MATLAB padrão com suporte para arrays multidimensionais, especificações de tipos de dados e nomeação de variáveis. O código gerado pode ser executado diretamente no ambiente MATLAB para análise de dados e computação científica."
    from_alias: "Array MATLAB"
    to_alias: "Formato de Código MATLAB"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame é a estrutura de dados central na linguagem de programação R, amplamente utilizada em análise estatística, mineração de dados e aprendizado de máquina. R é a ferramenta principal para computação estatística e gráficos, com DataFrame fornecendo poderosas capacidades de manipulação de dados, análise estatística e visualização. Essencial para cientistas de dados, estatísticos e pesquisadores trabalhando com análise de dados estruturados."
    step1: "Carregue arquivos de dados R ou cole código DataFrame. A ferramenta analisa a sintaxe R e extrai informações de estrutura DataFrame incluindo tipos de coluna, nomes de linha e conteúdo de dados."
    step3: "Gere código R DataFrame padrão com suporte para especificações de tipos de dados, níveis de fatores, nomes de linha/coluna e estruturas de dados específicas do R. O código gerado pode ser executado diretamente no ambiente R para análise estatística e processamento de dados."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Começar Conversão"
  start_generating: "Começar a gerar"
  api_docs: "Documentação da API"
related:
  section_title: 'Mais Conversores {{ if and .from (ne .from "generator") }}{{ .from }} e {{ end }}{{ .to }}'
  section_description: 'Explore mais conversores para formatos {{ if and .from (ne .from "generator") }}{{ .from }} e {{ end }}{{ .to }}. Transforme seus dados entre múltiplos formatos com nossas ferramentas de conversão online profissionais.'
  title: "{{ .from }} para {{ .to }}"
howto:
  step2: "Edite dados usando nosso editor de tabela online avançado com recursos profissionais. Suporta exclusão de linhas vazias, remoção de duplicatas, transposição de dados, classificação, localizar e substituir regex, e visualização em tempo real. Todas as alterações convertem automaticamente para o formato %s com resultados precisos e confiáveis."
  section_title: "Como usar o {{ . }}"
  converter_description: "Aprenda a converter {{ .from }} para {{ .to }} com nosso guia passo a passo. Conversor online profissional com recursos avançados e visualização em tempo real."
  generator_description: "Aprenda a criar tabelas {{ .to }} profissionais com nosso gerador online. Edição similar ao Excel, visualização em tempo real, e capacidades de exportação instantânea."
extension:
  section_title: "Extensão de Detecção e Extração de Tabelas"
  section_description: "Extraia tabelas de qualquer site com um clique. Converta para mais de 30 formatos incluindo Excel, CSV, JSON instantaneamente - sem necessidade de copiar e colar."
  features:
    extraction_title: "Extração de Tabelas com Um Clique"
    extraction_description: "Extraia instantaneamente tabelas de qualquer página web sem copiar e colar - extração de dados profissional simplificada"
    formats_title: "Suporte a Conversor de Mais de 30 Formatos"
    formats_description: "Converta tabelas extraídas para Excel, CSV, JSON, Markdown, SQL, e mais com nosso conversor de tabelas avançado"
    detection_title: "Detecção Inteligente de Tabelas"
    detection_description: "Detecta e destaca automaticamente tabelas em qualquer página web para extração e conversão rápida de dados"
  hover_tip: "✨ Passe o mouse sobre qualquer tabela para ver o ícone de extração"
recommendations:
  section_title: "Recomendado por Universidades e Profissionais"
  section_description: "TableConvert é confiado por profissionais em universidades, instituições de pesquisa e equipes de desenvolvimento para conversão confiável de tabelas e processamento de dados."
  cards:
    university_title: "Universidade de Wisconsin-Madison"
    university_description: "TableConvert.com - Ferramenta profissional gratuita online de conversor de tabelas e formatos de dados"
    university_link: "Ler Artigo"
    facebook_title: "Comunidade de Profissionais de Dados"
    facebook_description: "Compartilhado e recomendado por analistas de dados e profissionais em grupos de desenvolvedores do Facebook"
    facebook_link: "Ver Post"
    twitter_title: "Comunidade de Desenvolvedores"
    twitter_description: "Recomendado por @xiaoying_eth e outros desenvolvedores no X (Twitter) para conversão de tabelas"
    twitter_link: "Ver Tweet"
faq:
  section_title: "Perguntas Frequentes"
  section_description: "Perguntas comuns sobre nosso conversor de tabelas online gratuito, formatos de dados e processo de conversão."
  what: "O que é o formato %s?"
  howto_convert:
    question: "Como usar o {{ . }} gratuitamente?"
    answer: "Carregue seu arquivo {{ .from }}, cole dados, ou extraia de páginas web usando nosso conversor de tabelas online gratuito. Nossa ferramenta de conversor profissional transforma instantaneamente seus dados no formato {{ .to }} com visualização em tempo real e recursos de edição avançados. Baixe ou copie o resultado convertido imediatamente."
  security:
    question: "Meus dados estão seguros ao usar este conversor online?"
    answer: "Absolutamente! Todas as conversões de tabela acontecem localmente no seu navegador - seus dados nunca deixam seu dispositivo. Nosso conversor online processa tudo no lado do cliente, garantindo completa privacidade e segurança de dados. Nenhum arquivo é armazenado em nossos servidores."
  free:
    question: "O TableConvert é realmente gratuito para usar?"
    answer: "Sim, o TableConvert é completamente gratuito! Todos os recursos do conversor, editor de tabelas, ferramentas geradoras de dados e opções de exportação estão disponíveis sem custo, registro ou taxas ocultas. Converta arquivos ilimitados online gratuitamente."
  filesize:
    question: "Quais são os limites de tamanho de arquivo do conversor online?"
    answer: "Nosso conversor de tabelas online gratuito suporta arquivos de até 10MB. Para arquivos maiores, processamento em lote ou necessidades empresariais, use nossa extensão de navegador ou serviço de API profissional com limites mais altos."
stats:
  conversions: "Tabelas Convertidas"
  tables: "Tabelas Geradas"
  formats: "Formatos de Arquivos de Dados"
  rating: "Avaliação do Usuário"
