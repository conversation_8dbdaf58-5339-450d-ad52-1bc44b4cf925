site:
  fullname: "ऑनलाइन टेबल कन्वर्ट"
  name: "TableConvert"
  subtitle: "मुफ्त ऑनलाइन टेबल कन्वर्टर और जेनरेटर"
  intro: "TableConvert एक मुफ्त ऑनलाइन टेबल कन्वर्टर और डेटा जेनरेटर टूल है जो Excel, CSV, JSON, Markdown, LaTeX, SQL और अन्य सहित 30+ फॉर्मेट के बीच रूपांतरण का समर्थन करता है."
  followTwitter: "X पर हमें फॉलो करें"
title:
  converter: "%s से %s"
  generator: "%s जेनरेटर"
post:
  tags:
    converter: "कन्वर्टर"
    editor: "एडिटर"
    generator: "जेनरेटर"
    maker: "बिल्डर"
  converter:
    title: "%s से %s ऑनलाइन कन्वर्ट करें"
    short: "एक मुफ्त और शक्तिशाली %s से %s ऑनलाइन टूल"
    intro: "उपयोग में आसान ऑनलाइन %s से %s कन्वर्टर। हमारे सहज रूपांतरण टूल के साथ टेबल डेटा को आसानी से रूपांतरित करें। तेज़, विश्वसनीय और उपयोगकर्ता-अनुकूल।"
  generator:
    title: "ऑनलाइन %s एडिटर और जेनरेटर"
    short: "व्यापक सुविधाओं के साथ पेशेवर %s ऑनलाइन जेनरेशन टूल"
    intro: "उपयोग में आसान ऑनलाइन %s जेनरेटर और टेबल एडिटर। हमारे सहज टूल और रियल-टाइम प्रीव्यू के साथ आसानी से पेशेवर डेटा टेबल बनाएं।"
navbar:
  search:
    placeholder: "कन्वर्टर खोजें..."
  sponsor: "हमें कॉफी खिलाएं"
  extension: "एक्सटेंशन"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "डेटा स्रोत"
    placeholder: "अपना %s डेटा पेस्ट करें या %s फाइलें यहाँ ड्रैग करें"
    example: "उदाहरण"
    upload: "फाइल अपलोड करें"
    extract:
      enter: "वेब पेज से निकालें"
      intro: "संरचित डेटा को स्वचालित रूप से निकालने के लिए टेबल डेटा वाले वेब पेज का URL दर्ज करें"
      btn: "%s निकालें"
    excel:
      sheet: "वर्कशीट"
      none: "कोई नहीं"
  tableEditor:
    title: "ऑनलाइन टेबल एडिटर"
    undo: "पूर्ववत करें"
    redo: "फिर से करें"
    transpose: "ट्रांसपोज़"
    clear: "साफ़ करें"
    deleteBlank: "खाली हटाएं"
    deleteDuplicate: "डुप्लिकेट हटाएं"
    uppercase: "बड़े अक्षर"
    lowercase: "छोटे अक्षर"
    capitalize: "पहला अक्षर बड़ा"
    replace:
      replace: "खोजें और बदलें (Regex समर्थित)"
      subst: "इससे बदलें..."
      btn: "सभी बदलें"
  tableGenerator:
    title: "टेबल जेनरेटर"
    sponsor: "हमें कॉफी खिलाएं"
    copy: "क्लिपबोर्ड में कॉपी करें"
    download: "फाइल डाउनलोड करें"
    tooltip:
      html:
        escape: "प्रदर्शन त्रुटियों को रोकने के लिए HTML विशेष वर्णों (&, <, >, \", ') को एस्केप करें"
        div: "पारंपरिक TABLE टैग के बजाय DIV+CSS लेआउट का उपयोग करें, रिस्पॉन्सिव डिज़ाइन के लिए बेहतर"
        minify: "संपीड़ित HTML कोड उत्पन्न करने के लिए व्हाइटस्पेस और लाइन ब्रेक हटाएं"
        thead: "मानक तालिका हेड (&lt;thead&gt;) और बॉडी (&lt;tbody&gt;) संरचना उत्पन्न करें"
        tableCaption: "तालिका के ऊपर वर्णनात्मक शीर्षक जोड़ें (&lt;caption&gt; एलिमेंट)"
        tableClass: "आसान स्टाइल कस्टमाइज़ेशन के लिए तालिका में CSS क्लास नाम जोड़ें"
        tableId: "JavaScript मैनिपुलेशन के लिए तालिका के लिए अद्वितीय ID पहचानकर्ता सेट करें"
      jira:
        escape: "Jira तालिका सिंटैक्स के साथ संघर्ष से बचने के लिए पाइप वर्णों (|) को एस्केप करें"
      json:
        parsingJSON: "सेल में JSON स्ट्रिंग्स को ऑब्जेक्ट्स में बुद्धिमानी से पार्स करें"
        minify: "फ़ाइल आकार कम करने के लिए कॉम्पैक्ट सिंगल-लाइन JSON फॉर्मेट उत्पन्न करें"
        format: "आउटपुट JSON डेटा संरचना चुनें: ऑब्जेक्ट एरे, 2D एरे, आदि"
      latex:
        escape: "उचित संकलन सुनिश्चित करने के लिए LaTeX विशेष वर्णों (%, &, _, #, $, आदि) को एस्केप करें"
        ht: "पृष्ठ पर तालिका की स्थिति नियंत्रित करने के लिए फ्लोटिंग पोजीशन पैरामीटर [!ht] जोड़ें"
        mwe: "पूर्ण LaTeX दस्तावेज़ उत्पन्न करें"
        tableAlign: "पृष्ठ पर तालिका का क्षैतिज संरेखण सेट करें"
        tableBorder: "तालिका बॉर्डर स्टाइल कॉन्फ़िगर करें: कोई बॉर्डर नहीं, आंशिक बॉर्डर, पूर्ण बॉर्डर"
        label: "\\ref{} कमांड क्रॉस-रेफरेंसिंग के लिए तालिका लेबल सेट करें"
        caption: "तालिका के ऊपर या नीचे प्रदर्शित करने के लिए तालिका कैप्शन सेट करें"
        location: "तालिका कैप्शन प्रदर्शन स्थिति चुनें: ऊपर या नीचे"
        tableType: "तालिका वातावरण प्रकार चुनें: tabular, longtable, array, आदि"
      markdown:
        escape: "फॉर्मेट संघर्ष से बचने के लिए Markdown विशेष वर्णों (*, _, |, \\, आदि) को एस्केप करें"
        pretty: "अधिक सुंदर तालिका फॉर्मेट उत्पन्न करने के लिए कॉलम चौड़ाई को स्वतः संरेखित करें"
        simple: "बाहरी बॉर्डर वर्टिकल लाइनों को छोड़कर सरलीकृत सिंटैक्स का उपयोग करें"
        boldFirstRow: "पहली पंक्ति के टेक्स्ट को बोल्ड बनाएं"
        boldFirstColumn: "पहले कॉलम के टेक्स्ट को बोल्ड बनाएं"
        firstHeader: "पहली पंक्ति को हेडर के रूप में मानें और सेपरेटर लाइन जोड़ें"
        textAlign: "कॉलम टेक्स्ट संरेखण सेट करें: बाएं, केंद्र, दाएं"
        multilineHandling: "मल्टीलाइन टेक्स्ट हैंडलिंग: लाइन ब्रेक संरक्षित करें, \\n में एस्केप करें, &lt;br&gt; टैग का उपयोग करें"

        includeLineNumbers: "तालिका के बाईं ओर लाइन नंबर कॉलम जोड़ें"
      magic:
        builtin: "पूर्वनिर्धारित सामान्य टेम्प्लेट प्रारूप चुनें"
        rowsTpl: "<table> <tr> <th>जादुई सिंटैक्स</th> <th>विवरण</th> <th>JS मेथड्स समर्थन</th> </tr> <tr> <td>{h1} {h2} ...</td> <td><b>शी</b>र्षक का पहला, दूसरा ... फील्ड, यानी {hA} {hB} ...</td> <td>स्ट्रिंग मेथड्स</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>वर्तमान पंक्ति का पहला, दूसरा ... फील्ड, यानी {$A} {$B} ...</td> <td>स्ट्रिंग मेथड्स</td> </tr> <tr> <td>{F,} {F;}</td> <td><b>F</b> के बाद स्ट्रिंग द्वारा वर्तमान पंक्ति को विभाजित करें</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>वर्तमान <b>पं</b>क्ति की <b>सं</b>ख्या 1 या 100 से</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>पं</b>क्तियों की <b>अं</b>तिम <b>सं</b>ख्या </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>JavaScript कोड <b>चला</b>एं, उदा: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> ब्रेसेस {...} आउटपुट करने के लिए बैकस्लैश <b>\\</b> का उपयोग करें </td> <td></td> </tr></table>"
        headerTpl: "हेडर सेक्शन के लिए कस्टम आउटपुट टेम्प्लेट"
        footerTpl: "फूटर सेक्शन के लिए कस्टम आउटपुट टेम्प्लेट"
      textile:
        escape: "प्रारूप संघर्ष से बचने के लिए Textile सिंटैक्स वर्णों (|, ., -, ^) को एस्केप करें"
        rowHeader: "पहली पंक्ति को हेडर पंक्ति के रूप में सेट करें"
        thead: "तालिका हेड और बॉडी के लिए Textile सिंटैक्स मार्कर जोड़ें"
      xml:
        escape: "वैध XML सुनिश्चित करने के लिए XML विशेष वर्णों (&lt;, &gt;, &amp;, \", ') को एस्केप करें"
        minify: "अतिरिक्त व्हाइटस्पेस हटाकर संपीड़ित XML आउटपुट उत्पन्न करें"
        rootElement: "XML रूट एलिमेंट टैग नाम सेट करें"
        rowElement: "डेटा की प्रत्येक पंक्ति के लिए XML एलिमेंट टैग नाम सेट करें"
        declaration: "XML घोषणा हेडर जोड़ें (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "चाइल्ड एलिमेंट्स के बजाय XML एट्रिब्यूट्स के रूप में डेटा आउटपुट करें"
        cdata: "विशेष वर्णों की सुरक्षा के लिए टेक्स्ट कंटेंट को CDATA से लपेटें"
        encoding: "XML दस्तावेज़ के लिए कैरेक्टर एन्कोडिंग प्रारूप सेट करें"
        indentation: "XML इंडेंटेशन कैरेक्टर चुनें: स्पेस या टैब"
      yaml:
        indentSize: "YAML पदानुक्रम इंडेंटेशन के लिए स्पेस की संख्या सेट करें (आमतौर पर 2 या 4)"
        arrayStyle: "एरे प्रारूप: ब्लॉक (प्रति लाइन एक आइटम) या फ्लो (इनलाइन प्रारूप)"
        quotationStyle: "स्ट्रिंग उद्धरण शैली: कोई उद्धरण नहीं, सिंगल उद्धरण, डबल उद्धरण"
      pdf:
        theme: "पेशेवर दस्तावेज़ों के लिए PDF तालिका दृश्य शैली चुनें"
        headerColor: "PDF तालिका हेडर पृष्ठभूमि रंग चुनें"
        showHead: "PDF पृष्ठों पर हेडर प्रदर्शन नियंत्रित करें"
        docTitle: "PDF दस्तावेज़ के लिए वैकल्पिक शीर्षक"
        docDescription: "PDF दस्तावेज़ के लिए वैकल्पिक विवरण पाठ"
      csv:
        bom: "Excel और अन्य सॉफ्टवेयर को एन्कोडिंग पहचानने में मदद के लिए UTF-8 बाइट ऑर्डर मार्क जोड़ें"
      excel:
        autoWidth: "कंटेंट के आधार पर कॉलम चौड़ाई स्वचालित रूप से समायोजित करें"
        protectSheet: "पासवर्ड के साथ वर्कशीट सुरक्षा सक्षम करें: tableconvert.com"
      sql:
        primaryKey: "CREATE TABLE स्टेटमेंट के लिए प्राइमरी की फील्ड नाम निर्दिष्ट करें"
        dialect: "डेटाबेस प्रकार चुनें, जो उद्धरण और डेटा प्रकार सिंटैक्स को प्रभावित करता है"
      ascii:
        forceSep: "डेटा की प्रत्येक पंक्ति के बीच सेपरेटर लाइनें बाध्य करें"
        style: "ASCII तालिका बॉर्डर ड्राइंग शैली चुनें"
        comment: "पूरी तालिका को लपेटने के लिए कमेंट मार्कर जोड़ें"
      mediawiki:
        minify: "अतिरिक्त व्हाइटस्पेस हटाकर आउटपुट कोड संपीड़ित करें"
        header: "पहली पंक्ति को हेडर शैली के रूप में चिह्नित करें"
        sort: "तालिका क्लिक सॉर्टिंग कार्यक्षमता सक्षम करें"
      asciidoc:
        minify: "AsciiDoc प्रारूप आउटपुट संपीड़ित करें"
        firstHeader: "पहली पंक्ति को हेडर पंक्ति के रूप में सेट करें"
        lastFooter: "अंतिम पंक्ति को फूटर पंक्ति के रूप में सेट करें"
        title: "तालिका में शीर्षक टेक्स्ट जोड़ें"
      tracwiki:
        rowHeader: "पहली पंक्ति को हेडर के रूप में सेट करें"
        colHeader: "पहले कॉलम को हेडर के रूप में सेट करें"
      bbcode:
        minify: "BBCode आउटपुट प्रारूप संपीड़ित करें"
      restructuredtext:
        style: "reStructuredText तालिका बॉर्डर शैली चुनें"
        forceSep: "सेपरेटर लाइनें बाध्य करें"
    label:
      ascii:
        forceSep: "पंक्ति विभाजक"
        style: "बॉर्डर शैली"
        comment: "कमेंट रैपर"
      restructuredtext:
        style: "बॉर्डर शैली"
        forceSep: "विभाजक बाध्य करें"
      bbcode:
        minify: "आउटपुट संपीड़ित करें"
      csv:
        doubleQuote: "डबल कोट रैप"
        delimiter: "फील्ड डिलिमिटर"
        bom: "UTF-8 BOM"
        valueDelimiter: "वैल्यू डिलिमिटर"
        rowDelimiter: "पंक्ति डिलिमिटर"
        prefix: "पंक्ति प्रीफिक्स"
        suffix: "पंक्ति सफिक्स"
      excel:
        autoWidth: "ऑटो चौड़ाई"
        textFormat: "टेक्स्ट प्रारूप"
        protectSheet: "शीट सुरक्षा"
        boldFirstRow: "पहली पंक्ति बोल्ड"
        boldFirstColumn: "पहला कॉलम बोल्ड"
        sheetName: "शीट नाम"
      html:
        escape: "HTML वर्ण एस्केप"
        div: "DIV तालिका"
        minify: "कोड संपीड़ित करें"
        thead: "तालिका हेड संरचना"
        tableCaption: "तालिका कैप्शन"
        tableClass: "तालिका क्लास"
        tableId: "तालिका ID"
        rowHeader: "पंक्ति हेडर"
        colHeader: "कॉलम हेडर"
      jira:
        escape: "वर्ण एस्केप"
        rowHeader: "पंक्ति हेडर"
        colHeader: "कॉलम हेडर"
      json:
        parsingJSON: "JSON पार्स करें"
        minify: "आउटपुट संपीड़ित करें"
        format: "डेटा प्रारूप"
        rootName: "रूट ऑब्जेक्ट नाम"
        indentSize: "इंडेंट आकार"
      jsonlines:
        parsingJSON: "JSON पार्स करें"
        format: "डेटा प्रारूप"
      latex:
        escape: "LaTeX तालिका वर्ण एस्केप"
        ht: "फ्लोट स्थिति"
        mwe: "पूर्ण दस्तावेज़"
        tableAlign: "तालिका संरेखण"
        tableBorder: "बॉर्डर शैली"
        label: "संदर्भ लेबल"
        caption: "तालिका कैप्शन"
        location: "कैप्शन स्थिति"
        tableType: "तालिका प्रकार"
        boldFirstRow: "पहली पंक्ति बोल्ड"
        boldFirstColumn: "पहला कॉलम बोल्ड"
        textAlign: "टेक्स्ट संरेखण"
        borders: "बॉर्डर सेटिंग्स"
      markdown:
        escape: "वर्ण एस्केप"
        pretty: "सुंदर Markdown तालिका"
        simple: "सरल Markdown प्रारूप"
        boldFirstRow: "पहली पंक्ति बोल्ड"
        boldFirstColumn: "पहला कॉलम बोल्ड"
        firstHeader: "पहला हेडर"
        textAlign: "टेक्स्ट संरेखण"
        multilineHandling: "मल्टीलाइन हैंडलिंग"

        includeLineNumbers: "लाइन नंबर जोड़ें"
        align: "संरेखण"
      mediawiki:
        minify: "कोड संपीड़ित करें"
        header: "हेडर मार्कअप"
        sort: "सॉर्ट करने योग्य"
      asciidoc:
        minify: "प्रारूप संपीड़ित करें"
        firstHeader: "पहला हेडर"
        lastFooter: "अंतिम फूटर"
        title: "तालिका शीर्षक"
      tracwiki:
        rowHeader: "पंक्ति हेडर"
        colHeader: "कॉलम हेडर"
      sql:
        drop: "तालिका ड्रॉप करें (यदि मौजूद है)"
        create: "तालिका बनाएं"
        oneInsert: "बैच इन्सर्ट"
        table: "तालिका नाम"
        dialect: "डेटाबेस प्रकार"
        primaryKey: "प्राइमरी की"
      magic:
        builtin: "अंतर्निहित टेम्प्लेट"
        rowsTpl: "पंक्ति टेम्प्लेट, सिंटैक्स ->"
        headerTpl: "हेडर टेम्प्लेट"
        footerTpl: "फूटर टेम्प्लेट"
      textile:
        escape: "वर्ण एस्केप"
        rowHeader: "पंक्ति हेडर"
        thead: "तालिका हेड सिंटैक्स"
      xml:
        escape: "XML वर्ण एस्केप"
        minify: "आउटपुट संपीड़ित करें"
        rootElement: "रूट एलिमेंट"
        rowElement: "पंक्ति एलिमेंट"
        declaration: "XML घोषणा"
        attributes: "एट्रिब्यूट मोड"
        cdata: "CDATA रैपर"
        encoding: "एन्कोडिंग"
        indentSize: "इंडेंट आकार"
      yaml:
        indentSize: "इंडेंट आकार"
        arrayStyle: "एरे शैली"
        quotationStyle: "उद्धरण शैली"
      pdf:
        theme: "PDF तालिका थीम"
        headerColor: "PDF हेडर रंग"
        showHead: "PDF हेडर प्रदर्शन"
        docTitle: "PDF दस्तावेज़ शीर्षक"
        docDescription: "PDF दस्तावेज़ विवरण"
sidebar:
  all: "सभी कन्वर्जन टूल्स"
  dataSource:
    title: "डेटा स्रोत"
    description:
      converter: "%s को %s में कन्वर्ट करने के लिए आयात करें। फाइल अपलोड, ऑनलाइन एडिटिंग और वेब डेटा निष्कर्षण का समर्थन करता है।"
      generator: "मैन्युअल इनपुट, फाइल आयात और टेम्प्लेट जेनरेशन सहित कई इनपुट विधियों के समर्थन के साथ तालिका डेटा बनाएं।"
  tableEditor:
    title: "ऑनलाइन तालिका संपादक"
    description:
      converter: "हमारे तालिका संपादक का उपयोग करके %s को ऑनलाइन प्रोसेस करें। खाली पंक्तियों को हटाने, डुप्लिकेशन हटाने, सॉर्टिंग और खोजें और बदलें के समर्थन के साथ Excel जैसा ऑपरेशन अनुभव।"
      generator: "Excel जैसा ऑपरेशन अनुभव प्रदान करने वाला शक्तिशाली ऑनलाइन तालिका संपादक। खाली पंक्तियों को हटाने, डुप्लिकेशन हटाने, सॉर्टिंग और खोजें और बदलें का समर्थन करता है।"
  tableGenerator:
    title: "तालिका जेनरेटर"
    description:
      converter: "तालिका जेनरेटर के रियल-टाइम प्रीव्यू के साथ %s को तुरंत जेनरेट करें। समृद्ध निर्यात विकल्प, एक-क्लिक कॉपी और डाउनलोड।"
      generator: "विभिन्न उपयोग परिदृश्यों को पूरा करने के लिए %s डेटा को कई प्रारूपों में निर्यात करें। कस्टम विकल्प और रियल-टाइम प्रीव्यू का समर्थन करता है।"
footer:
  changelog: "परिवर्तन लॉग"
  sponsor: "प्रायोजक"
  contact: "हमसे संपर्क करें"
  privacyPolicy: "गोपनीयता नीति"
  about: "के बारे में"
  resources: "संसाधन"
  popularConverters: "लोकप्रिय कन्वर्टर"
  popularGenerators: "लोकप्रिय जेनरेटर"
  dataSecurity: "आपका डेटा सुरक्षित है - सभी रूपांतरण आपके ब्राउज़र में चलते हैं."
converters:
  Markdown:
    alias: "Markdown तालिका"
    what: "Markdown एक हल्की मार्कअप भाषा है जो तकनीकी दस्तावेज़ीकरण, ब्लॉग सामग्री निर्माण और वेब विकास के लिए व्यापक रूप से उपयोग की जाती है। इसका तालिका सिंटैक्स संक्षिप्त और सहज है, टेक्स्ट संरेखण, लिंक एम्बेडिंग और फॉर्मेटिंग का समर्थन करता है। यह प्रोग्रामर और तकनीकी लेखकों के लिए पसंदीदा उपकरण है, GitHub, GitLab और अन्य कोड होस्टिंग प्लेटफॉर्म के साथ पूर्ण रूप से संगत है।"
    step1: "Markdown तालिका डेटा को डेटा स्रोत क्षेत्र में पेस्ट करें, या अपलोड के लिए सीधे .md फाइलों को ड्रैग और ड्रॉप करें। उपकरण स्वचालित रूप से तालिका संरचना और फॉर्मेटिंग का विश्लेषण करता है, जटिल नेस्टेड सामग्री और विशेष वर्ण हैंडलिंग का समर्थन करता है।"
    step3: "वास्तविक समय में मानक Markdown तालिका कोड उत्पन्न करें, कई संरेखण विधियों, टेक्स्ट बोल्डिंग, लाइन नंबर जोड़ने और अन्य उन्नत फॉर्मेट सेटिंग्स का समर्थन करता है। उत्पन्न कोड GitHub और प्रमुख Markdown संपादकों के साथ पूर्ण रूप से संगत है, एक-क्लिक कॉपी के साथ उपयोग के लिए तैयार है।"
    from_alias: "Markdown तालिका फाइल"
    to_alias: "Markdown तालिका प्रारूप"
  Magic:
    alias: "कस्टम टेम्प्लेट"
    what: "Magic टेम्प्लेट इस उपकरण का एक अनूठा उन्नत डेटा जेनरेटर है, जो उपयोगकर्ताओं को कस्टम टेम्प्लेट सिंटैक्स के माध्यम से मनमाना प्रारूप डेटा आउटपुट बनाने की अनुमति देता है। वेरिएबल रिप्लेसमेंट, सशर्त निर्णय और लूप प्रोसेसिंग का समर्थन करता है। यह जटिल डेटा रूपांतरण आवश्यकताओं और व्यक्तिगत आउटपुट प्रारूपों को संभालने के लिए अंतिम समाधान है, विशेष रूप से डेवलपर्स और डेटा इंजीनियरों के लिए उपयुक्त है।"
    step1: "अंतर्निहित सामान्य टेम्प्लेट चुनें या कस्टम टेम्प्लेट सिंटैक्स बनाएं। समृद्ध वेरिएबल्स और फ़ंक्शन्स का समर्थन करता है जो जटिल डेटा संरचनाओं और व्यावसायिक तर्क को संभाल सकते हैं।"
    step3: "डेटा आउटपुट उत्पन्न करें जो कस्टम प्रारूप आवश्यकताओं को पूर्ण रूप से पूरा करता है। जटिल डेटा रूपांतरण तर्क और सशर्त प्रसंस्करण का समर्थन करता है, डेटा प्रसंस्करण दक्षता और आउटपुट गुणवत्ता में काफी सुधार करता है। बैच डेटा प्रसंस्करण के लिए एक शक्तिशाली उपकरण।"
    from_alias: "तालिका डेटा"
    to_alias: "कस्टम प्रारूप आउटपुट"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) सबसे व्यापक रूप से उपयोग किया जाने वाला डेटा एक्सचेंज प्रारूप है, Excel, Google Sheets, डेटाबेस सिस्टम और विभिन्न डेटा विश्लेषण उपकरणों द्वारा पूर्ण रूप से समर्थित है। इसकी सरल संरचना और मजबूत संगतता इसे डेटा माइग्रेशन, बैच आयात/निर्यात और क्रॉस-प्लेटफॉर्म डेटा एक्सचेंज के लिए मानक प्रारूप बनाती है, व्यावसायिक विश्लेषण, डेटा विज्ञान और सिस्टम एकीकरण में व्यापक रूप से उपयोग की जाती है।"
    step1: "CSV फाइलें अपलोड करें या सीधे CSV डेटा पेस्ट करें। उपकरण बुद्धिमानी से विभिन्न डिलिमिटर (कॉमा, टैब, सेमीकोलन, पाइप, आदि) को पहचानता है, स्वचालित रूप से डेटा प्रकार और एन्कोडिंग प्रारूप का पता लगाता है, बड़ी फाइलों और जटिल डेटा संरचनाओं के तेज़ पार्सिंग का समर्थन करता है।"
    step3: "कस्टम डिलिमिटर, उद्धरण शैलियों, एन्कोडिंग प्रारूपों और BOM मार्क सेटिंग्स के समर्थन के साथ मानक CSV प्रारूप फाइलें उत्पन्न करें। लक्ष्य सिस्टम के साथ पूर्ण संगतता सुनिश्चित करता है, एंटरप्राइज़-स्तरीय डेटा प्रसंस्करण आवश्यकताओं को पूरा करने के लिए डाउनलोड और संपीड़न विकल्प प्रदान करता है।"
    from_alias: "CSV डेटा फाइल"
    to_alias: "CSV मानक प्रारूप"
  JSON:
    alias: "JSON Array"
    what: "JSON (JavaScript Object Notation) आधुनिक वेब एप्लिकेशन, REST APIs और माइक्रोसर्विस आर्किटेक्चर के लिए मानक तालिका डेटा प्रारूप है। इसकी स्पष्ट संरचना और कुशल पार्सिंग इसे फ्रंट-एंड और बैक-एंड डेटा इंटरैक्शन, कॉन्फ़िगरेशन फाइल स्टोरेज और NoSQL डेटाबेस में व्यापक रूप से उपयोग करती है। नेस्टेड ऑब्जेक्ट्स, एरे संरचनाओं और कई डेटा प्रकारों का समर्थन करता है, जो इसे आधुनिक सॉफ्टवेयर विकास के लिए अपरिहार्य तालिका डेटा बनाता है।"
    step1: "JSON फाइलें अपलोड करें या JSON एरे पेस्ट करें। ऑब्जेक्ट एरे, नेस्टेड संरचनाओं और जटिल डेटा प्रकारों की स्वचालित पहचान और पार्सिंग का समर्थन करता है। उपकरण बुद्धिमानी से JSON सिंटैक्स को मान्य करता है और त्रुटि संकेत प्रदान करता है।"
    step3: "कई JSON प्रारूप आउटपुट उत्पन्न करें: मानक ऑब्जेक्ट एरे, 2D एरे, कॉलम एरे और की-वैल्यू पेयर प्रारूप। सुंदर आउटपुट, संपीड़न मोड, कस्टम रूट ऑब्जेक्ट नाम और इंडेंटेशन सेटिंग्स का समर्थन करता है, विभिन्न API इंटरफेस और डेटा स्टोरेज आवश्यकताओं के लिए पूर्ण रूप से अनुकूलित।"
    from_alias: "JSON Array फाइल"
    to_alias: "JSON मानक प्रारूप"
  JSONLines:
    alias: "JSONLines प्रारूप"
    what: "JSON Lines (NDJSON के रूप में भी जाना जाता है) बिग डेटा प्रोसेसिंग और स्ट्रीमिंग डेटा ट्रांसमिशन के लिए एक महत्वपूर्ण प्रारूप है, जिसमें प्रत्येक लाइन में एक स्वतंत्र JSON ऑब्जेक्ट होता है। लॉग विश्लेषण, डेटा स्ट्रीम प्रोसेसिंग, मशीन लर्निंग और वितरित सिस्टम में व्यापक रूप से उपयोग किया जाता है। वृद्धिशील प्रसंस्करण और समानांतर कंप्यूटिंग का समर्थन करता है, जो इसे बड़े पैमाने पर संरचित डेटा को संभालने के लिए आदर्श विकल्प बनाता है।"
    step1: "JSONLines फाइलें अपलोड करें या डेटा पेस्ट करें। उपकरण JSON ऑब्जेक्ट्स को लाइन दर लाइन पार्स करता है, बड़ी फाइल स्ट्रीमिंग प्रोसेसिंग और त्रुटि लाइन छोड़ने की कार्यक्षमता का समर्थन करता है।"
    step3: "मानक JSONLines प्रारूप उत्पन्न करें जिसमें प्रत्येक लाइन एक पूर्ण JSON ऑब्जेक्ट आउटपुट करती है। स्ट्रीमिंग प्रोसेसिंग, बैच आयात और बिग डेटा विश्लेषण परिदृश्यों के लिए उपयुक्त, डेटा सत्यापन और प्रारूप अनुकूलन का समर्थन करता है।"
    from_alias: "JSONLines डेटा"
    to_alias: "JSONLines स्ट्रीमिंग प्रारूप"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) एंटरप्राइज़-स्तरीय डेटा एक्सचेंज और कॉन्फ़िगरेशन प्रबंधन के लिए मानक प्रारूप है, जिसमें सख्त सिंटैक्स विनिर्देश और शक्तिशाली सत्यापन तंत्र हैं। वेब सेवाओं, कॉन्फ़िगरेशन फाइलों, दस्तावेज़ स्टोरेज और सिस्टम एकीकरण में व्यापक रूप से उपयोग किया जाता है। नेमस्पेस, स्कीमा सत्यापन और XSLT रूपांतरण का समर्थन करता है, जो इसे एंटरप्राइज़ एप्लिकेशन के लिए महत्वपूर्ण तालिका डेटा बनाता है।"
    step1: "XML फाइलें अपलोड करें या XML डेटा पेस्ट करें। उपकरण स्वचालित रूप से XML संरचना को पार्स करता है और इसे तालिका प्रारूप में कन्वर्ट करता है, नेमस्पेस, एट्रिब्यूट हैंडलिंग और जटिल नेस्टेड संरचनाओं का समर्थन करता है।"
    step3: "XML मानकों के अनुपालन में XML आउटपुट उत्पन्न करें। कस्टम रूट एलिमेंट्स, रो एलिमेंट नाम, एट्रिब्यूट मोड, CDATA रैपिंग और कैरेक्टर एन्कोडिंग सेटिंग्स का समर्थन करता है। डेटा अखंडता और संगतता सुनिश्चित करता है, एंटरप्राइज़-स्तरीय एप्लिकेशन आवश्यकताओं को पूरा करता है।"
    from_alias: "XML डेटा फाइल"
    to_alias: "XML मानक प्रारूप"
  YAML:
    alias: "YAML कॉन्फ़िगरेशन"
    what: "YAML एक मानव-अनुकूल डेटा सीरियलाइज़ेशन मानक है, जो अपनी स्पष्ट पदानुक्रमित संरचना और संक्षिप्त सिंटैक्स के लिए प्रसिद्ध है। कॉन्फ़िगरेशन फाइलों, DevOps टूल चेन, Docker Compose और Kubernetes डिप्लॉयमेंट में व्यापक रूप से उपयोग किया जाता है। इसकी मजबूत पठनीयता और संक्षिप्त सिंटैक्स इसे आधुनिक क्लाउड-नेटिव एप्लिकेशन और स्वचालित संचालन के लिए एक महत्वपूर्ण कॉन्फ़िगरेशन प्रारूप बनाती है।"
    step1: "YAML फाइलें अपलोड करें या YAML डेटा पेस्ट करें। उपकरण बुद्धिमानी से YAML संरचना को पार्स करता है और सिंटैक्स सही होने को मान्य करता है, मल्टी-डॉक्यूमेंट प्रारूप और जटिल डेटा प्रकारों का समर्थन करता है।"
    step3: "ब्लॉक और फ्लो एरे शैलियों, कई उद्धरण सेटिंग्स, कस्टम इंडेंटेशन और कमेंट संरक्षण के समर्थन के साथ मानक YAML प्रारूप आउटपुट उत्पन्न करें। सुनिश्चित करता है कि आउटपुट YAML फाइलें विभिन्न पार्सर और कॉन्फ़िगरेशन सिस्टम के साथ पूर्ण रूप से संगत हैं।"
    from_alias: "YAML कॉन्फ़िगरेशन फाइल"
    to_alias: "YAML मानक प्रारूप"
  MySQL:
      alias: "MySQL क्वेरी परिणाम"
      what: "MySQL दुनिया का सबसे लोकप्रिय ओपन-सोर्स रिलेशनल डेटाबेस मैनेजमेंट सिस्टम है, जो अपनी उच्च प्रदर्शन, विश्वसनीयता और उपयोग में आसानी के लिए प्रसिद्ध है। वेब एप्लिकेशन, एंटरप्राइज़ सिस्टम और डेटा विश्लेषण प्लेटफॉर्म में व्यापक रूप से उपयोग किया जाता है। MySQL क्वेरी परिणाम आमतौर पर संरचित तालिका डेटा होते हैं, जो डेटाबेस प्रबंधन और डेटा विश्लेषण कार्य में एक महत्वपूर्ण डेटा स्रोत के रूप में काम करते हैं।"
      step1: "MySQL क्वेरी आउटपुट परिणामों को डेटा स्रोत क्षेत्र में पेस्ट करें। उपकरण स्वचालित रूप से MySQL कमांड-लाइन आउटपुट प्रारूप को पहचानता और पार्स करता है, विभिन्न क्वेरी परिणाम शैलियों और कैरेक्टर एन्कोडिंग का समर्थन करता है, बुद्धिमानी से हेडर और डेटा पंक्तियों को संभालता है।"
      step3: "MySQL क्वेरी परिणामों को कई तालिका डेटा प्रारूपों में तुरंत कन्वर्ट करें, डेटा विश्लेषण, रिपोर्ट जेनरेशन, क्रॉस-सिस्टम डेटा माइग्रेशन और डेटा सत्यापन की सुविधा प्रदान करें। डेटाबेस प्रशासकों और डेटा विश्लेषकों के लिए एक व्यावहारिक उपकरण।"
      from_alias: "MySQL क्वेरी आउटपुट"
      to_alias: "MySQL तालिका डेटा"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) रिलेशनल डेटाबेस के लिए मानक ऑपरेशन भाषा है, जो डेटा क्वेरी, इन्सर्ट, अपडेट और डिलीट ऑपरेशन के लिए उपयोग की जाती है। डेटाबेस प्रबंधन की मुख्य तकनीक के रूप में, SQL डेटा विश्लेषण, बिजनेस इंटेलिजेंस, ETL प्रोसेसिंग और डेटा वेयरहाउस निर्माण में व्यापक रूप से उपयोग किया जाता है। यह डेटा पेशेवरों के लिए एक आवश्यक कौशल उपकरण है।"
    step1: "INSERT SQL स्टेटमेंट्स पेस्ट करें या .sql फाइलें अपलोड करें। उपकरण बुद्धिमानी से SQL सिंटैक्स को पार्स करता है और तालिका डेटा निकालता है, कई SQL डायलेक्ट्स और जटिल क्वेरी स्टेटमेंट प्रोसेसिंग का समर्थन करता है।"
    step3: "मानक SQL INSERT स्टेटमेंट्स और तालिका निर्माण स्टेटमेंट्स उत्पन्न करें। कई डेटाबेस डायलेक्ट्स (MySQL, PostgreSQL, SQLite, SQL Server, Oracle) का समर्थन करता है, स्वचालित रूप से डेटा प्रकार मैपिंग, कैरेक्टर एस्केपिंग और प्राइमरी की बाधाओं को संभालता है। सुनिश्चित करता है कि उत्पन्न SQL कोड सीधे निष्पादित किया जा सकता है।"
    from_alias: "SQL डेटा फाइल"
    to_alias: "SQL मानक स्टेटमेंट"
  Qlik:
      alias: "Qlik तालिका"
      what: "Qlik एक सॉफ्टवेयर विक्रेता है जो डेटा विज़ुअलाइज़ेशन, कार्यकारी डैशबोर्ड और सेल्फ-सर्विस बिजनेस इंटेलिजेंस उत्पादों में विशेषज्ञता रखता है, Tableau और Microsoft के साथ।"
      step1: ""
      step3: "अंत में, [तालिका जेनरेटर](#TableGenerator) कन्वर्जन परिणाम दिखाता है। अपने Qlik Sense, Qlik AutoML, QlikView या अन्य Qlik-सक्षम सॉफ्टवेयर में उपयोग करें।"
      from_alias: "Qlik तालिका"
      to_alias: "Qlik तालिका"
  DAX:
      alias: "DAX तालिका"
      what: "DAX (Data Analysis Expressions) एक प्रोग्रामिंग भाषा है जो Microsoft Power BI में गणना कॉलम, मेजर और कस्टम तालिकाएं बनाने के लिए उपयोग की जाती है।"
      step1: ""
      step3: "अंत में, [तालिका जेनरेटर](#TableGenerator) कन्वर्जन परिणाम दिखाता है। जैसा कि अपेक्षित है, यह Microsoft Power BI, Microsoft Analysis Services और Microsoft Power Pivot for Excel सहित कई Microsoft उत्पादों में उपयोग किया जाता है।"
      from_alias: "DAX तालिका"
      to_alias: "DAX तालिका"
  Firebase:
    alias: "Firebase सूची"
    what: "Firebase एक BaaS एप्लिकेशन विकास प्लेटफॉर्म है जो रियल-टाइम डेटाबेस, क्लाउड स्टोरेज, प्रमाणीकरण, क्रैश रिपोर्टिंग आदि जैसी होस्टेड बैकएंड सेवाएं प्रदान करता है।"
    step1: ""
    step3: "अंत में, [तालिका जेनरेटर](#TableGenerator) कन्वर्जन परिणाम दिखाता है। फिर आप Firebase डेटाबेस में डेटा की सूची में जोड़ने के लिए Firebase API में push मेथड का उपयोग कर सकते हैं।"
    from_alias: "Firebase सूची"
    to_alias: "Firebase सूची"
  HTML:
    alias: "HTML तालिका"
    what: "HTML तालिकाएं वेब पेजों में संरचित डेटा प्रदर्शित करने का मानक तरीका हैं, जो table, tr, td और अन्य टैग्स के साथ बनाई जाती हैं। समृद्ध शैली कस्टमाइज़ेशन, रिस्पॉन्सिव लेआउट और इंटरैक्टिव कार्यक्षमता का समर्थन करती हैं। वेबसाइट विकास, डेटा प्रदर्शन और रिपोर्ट जेनरेशन में व्यापक रूप से उपयोग की जाती हैं, फ्रंट-एंड विकास और वेब डिज़ाइन के एक महत्वपूर्ण घटक के रूप में काम करती हैं।"
    step1: "तालिकाओं वाले HTML कोड पेस्ट करें या HTML फाइलें अपलोड करें। उपकरण स्वचालित रूप से पेजों से तालिका डेटा को पहचानता और निकालता है, जटिल HTML संरचनाओं, CSS शैलियों और नेस्टेड तालिका प्रोसेसिंग का समर्थन करता है।"
    step3: "thead/tbody संरचना, CSS क्लास सेटिंग्स, तालिका कैप्शन, पंक्ति/कॉलम हेडर और रिस्पॉन्सिव एट्रिब्यूट कॉन्फ़िगरेशन के समर्थन के साथ सिमेंटिक HTML तालिका कोड उत्पन्न करें। सुनिश्चित करता है कि उत्पन्न तालिका कोड अच्छी पहुंच और SEO मित्रता के साथ वेब मानकों को पूरा करता है।"
    from_alias: "HTML वेब तालिका"
    to_alias: "HTML मानक तालिका"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel दुनिया का सबसे लोकप्रिय स्प्रेडशीट सॉफ्टवेयर है, जो व्यापारिक विश्लेषण, वित्तीय प्रबंधन, डेटा प्रोसेसिंग और रिपोर्ट निर्माण में व्यापक रूप से उपयोग किया जाता है। इसकी शक्तिशाली डेटा प्रोसेसिंग क्षमताएं, समृद्ध फ़ंक्शन लाइब्रेरी और लचीली विज़ुअलाइज़ेशन सुविधाएं इसे कार्यालय स्वचालन और डेटा विश्लेषण के लिए मानक उपकरण बनाती हैं, जिसका लगभग सभी उद्योगों और क्षेत्रों में व्यापक अनुप्रयोग है।"
    step1: "Excel फाइलें अपलोड करें (.xlsx, .xls प्रारूपों का समर्थन करता है) या Excel से सीधे तालिका डेटा कॉपी करके पेस्ट करें। उपकरण मल्टी-वर्कशीट प्रोसेसिंग, जटिल प्रारूप पहचान और बड़ी फाइलों की तेज़ पार्सिंग का समर्थन करता है, स्वचालित रूप से मर्ज किए गए सेल और डेटा प्रकारों को संभालता है।"
    step3: "Excel-संगत तालिका डेटा उत्पन्न करें जो सीधे Excel में पेस्ट किया जा सकता है या मानक .xlsx फाइलों के रूप में डाउनलोड किया जा सकता है। वर्कशीट नामकरण, सेल फॉर्मेटिंग, ऑटो कॉलम चौड़ाई, हेडर स्टाइलिंग और डेटा सत्यापन सेटिंग्स का समर्थन करता है। सुनिश्चित करता है कि आउटपुट Excel फाइलों में पेशेवर उपस्थिति और पूर्ण कार्यक्षमता हो।"
    from_alias: "Excel स्प्रेडशीट"
    to_alias: "Excel मानक प्रारूप"
  LaTeX:
    alias: "LaTeX तालिका"
    what: "LaTeX एक पेशेवर दस्तावेज़ टाइपसेटिंग सिस्टम है, विशेष रूप से शैक्षणिक पत्र, तकनीकी दस्तावेज़ और वैज्ञानिक प्रकाशन बनाने के लिए उपयुक्त है। इसकी तालिका कार्यक्षमता शक्तिशाली है, जटिल गणितीय सूत्रों, सटीक लेआउट नियंत्रण और उच्च गुणवत्ता PDF आउटपुट का समर्थन करती है। यह शिक्षा और वैज्ञानिक प्रकाशन में मानक उपकरण है, जर्नल पेपर, शोध प्रबंध और तकनीकी मैनुअल टाइपसेटिंग में व्यापक रूप से उपयोग किया जाता है।"
    step1: "LaTeX तालिका कोड पेस्ट करें या .tex फाइलें अपलोड करें। उपकरण LaTeX तालिका सिंटैक्स को पार्स करता है और डेटा सामग्री निकालता है, कई तालिका वातावरण (tabular, longtable, array, आदि) और जटिल प्रारूप कमांड का समर्थन करता है।"
    step3: "कई तालिका वातावरण चयन, बॉर्डर शैली कॉन्फ़िगरेशन, कैप्शन स्थिति सेटिंग्स, दस्तावेज़ क्लास विनिर्देश और पैकेज प्रबंधन के समर्थन के साथ पेशेवर LaTeX तालिका कोड उत्पन्न करें। पूर्ण संकलन योग्य LaTeX दस्तावेज़ उत्पन्न कर सकता है, सुनिश्चित करता है कि आउटपुट तालिकाएं शैक्षणिक प्रकाशन मानकों को पूरा करती हैं।"
    from_alias: "LaTeX दस्तावेज़ तालिका"
    to_alias: "LaTeX पेशेवर प्रारूप"
  ASCII:
    alias: "ASCII तालिका"
    what: "ASCII तालिकाएं तालिका बॉर्डर और संरचनाओं को खींचने के लिए सादे टेक्स्ट वर्णों का उपयोग करती हैं, सर्वोत्तम संगतता और पोर्टेबिलिटी प्रदान करती हैं। सभी टेक्स्ट एडिटर्स, टर्मिनल वातावरण और ऑपरेटिंग सिस्टम के साथ संगत हैं। कोड दस्तावेज़ीकरण, तकनीकी मैनुअल, README फाइलों और कमांड-लाइन टूल आउटपुट में व्यापक रूप से उपयोग की जाती हैं। प्रोग्रामर और सिस्टम प्रशासकों के लिए पसंदीदा डेटा प्रदर्शन प्रारूप।"
    step1: "ASCII तालिकाओं वाली टेक्स्ट फाइलें अपलोड करें या सीधे तालिका डेटा पेस्ट करें। उपकरण बुद्धिमानी से ASCII तालिका संरचनाओं को पहचानता और पार्स करता है, कई बॉर्डर शैलियों और संरेखण प्रारूपों का समर्थन करता है।"
    step3: "कई बॉर्डर शैलियों (सिंगल लाइन, डबल लाइन, गोल कोने, आदि), टेक्स्ट संरेखण विधियों और ऑटो कॉलम चौड़ाई के समर्थन के साथ सुंदर सादे टेक्स्ट ASCII तालिकाएं उत्पन्न करें। उत्पन्न तालिकाएं कोड एडिटर्स, दस्तावेज़ों और कमांड लाइनों में पूर्ण रूप से प्रदर्शित होती हैं।"
    from_alias: "ASCII टेक्स्ट तालिका"
    to_alias: "ASCII मानक प्रारूप"
  MediaWiki:
    alias: "MediaWiki तालिका"
    what: "MediaWiki विकिपीडिया जैसी प्रसिद्ध विकि साइटों द्वारा उपयोग किया जाने वाला ओपन-सोर्स सॉफ्टवेयर प्लेटफॉर्म है। इसका तालिका सिंटैक्स संक्षिप्त लेकिन शक्तिशाली है, तालिका शैली कस्टमाइज़ेशन, सॉर्टिंग कार्यक्षमता और लिंक एम्बेडिंग का समर्थन करता है। ज्ञान प्रबंधन, सहयोगी संपादन और सामग्री प्रबंधन सिस्टम में व्यापक रूप से उपयोग किया जाता है, विकि विश्वकोश और ज्ञान आधार निर्माण के लिए मुख्य तकनीक के रूप में काम करता है।"
    step1: "MediaWiki तालिका कोड पेस्ट करें या विकि स्रोत फाइलें अपलोड करें। उपकरण विकि मार्कअप सिंटैक्स को पार्स करता है और तालिका डेटा निकालता है, जटिल विकि सिंटैक्स और टेम्प्लेट प्रोसेसिंग का समर्थन करता है।"
    step3: "हेडर शैली सेटिंग्स, सेल संरेखण, सॉर्टिंग कार्यक्षमता सक्षम करने और कोड संपीड़न विकल्पों के समर्थन के साथ मानक MediaWiki तालिका कोड उत्पन्न करें। उत्पन्न कोड सीधे विकि पेज संपादन के लिए उपयोग किया जा सकता है, MediaWiki प्लेटफॉर्म पर पूर्ण प्रदर्शन सुनिश्चित करता है।"
    from_alias: "MediaWiki स्रोत कोड"
    to_alias: "MediaWiki तालिका सिंटैक्स"
  TracWiki:
    alias: "TracWiki तालिका"
    what: "Trac एक वेब-आधारित प्रोजेक्ट प्रबंधन और बग ट्रैकिंग सिस्टम है जो तालिका सामग्री बनाने के लिए सरलीकृत विकि सिंटैक्स का उपयोग करता है।"
    step1: "TracWiki फाइलें अपलोड करें या तालिका डेटा पेस्ट करें।"
    step3: "पंक्ति/कॉलम हेडर सेटिंग्स के समर्थन के साथ TracWiki-संगत तालिका कोड उत्पन्न करें, प्रोजेक्ट दस्तावेज़ प्रबंधन की सुविधा प्रदान करें।"
    from_alias: "TracWiki तालिका"
    to_alias: "TracWiki प्रारूप"
  AsciiDoc:
    alias: "AsciiDoc तालिका"
    what: "AsciiDoc एक हल्की मार्कअप भाषा है जिसे HTML, PDF, मैनुअल पेज और अन्य प्रारूपों में कन्वर्ट किया जा सकता है, तकनीकी दस्तावेज़ीकरण लेखन के लिए व्यापक रूप से उपयोग की जाती है।"
    step1: "AsciiDoc फाइलें अपलोड करें या डेटा पेस्ट करें।"
    step3: "हेडर, फूटर और शीर्षक सेटिंग्स के समर्थन के साथ AsciiDoc तालिका सिंटैक्स उत्पन्न करें, AsciiDoc एडिटर्स में सीधे उपयोग योग्य।"
    from_alias: "AsciiDoc तालिका"
    to_alias: "AsciiDoc प्रारूप"
  reStructuredText:
    alias: "reStructuredText तालिका"
    what: "reStructuredText Python समुदाय के लिए मानक दस्तावेज़ीकरण प्रारूप है, समृद्ध तालिका सिंटैक्स का समर्थन करता है, आमतौर पर Sphinx दस्तावेज़ीकरण जेनरेशन के लिए उपयोग किया जाता है।"
    step1: ".rst फाइलें अपलोड करें या reStructuredText डेटा पेस्ट करें।"
    step3: "कई बॉर्डर शैलियों के समर्थन के साथ मानक reStructuredText तालिकाएं उत्पन्न करें, Sphinx दस्तावेज़ीकरण प्रोजेक्ट्स में सीधे उपयोग योग्य।"
    from_alias: "reStructuredText तालिका"
    to_alias: "reStructuredText प्रारूप"
  PHP:
    alias: "PHP Array"
    what: "PHP एक लोकप्रिय सर्वर-साइड स्क्रिप्टिंग भाषा है, जिसमें एरे इसकी मुख्य डेटा संरचना हैं, वेब विकास और डेटा प्रोसेसिंग में व्यापक रूप से उपयोग की जाती है।"
    step1: "PHP एरे वाली फाइलें अपलोड करें या सीधे डेटा पेस्ट करें।"
    step3: "मानक PHP एरे कोड उत्पन्न करें जो PHP प्रोजेक्ट्स में सीधे उपयोग किया जा सकता है, एसोसिएटिव और इंडेक्स्ड एरे प्रारूपों का समर्थन करता है।"
    from_alias: "PHP Array"
    to_alias: "PHP कोड"
  Ruby:
    alias: "Ruby Array"
    what: "Ruby एक गतिशील ऑब्जेक्ट-ओरिएंटेड प्रोग्रामिंग भाषा है जिसमें संक्षिप्त और सुरुचिपूर्ण सिंटैक्स है, एरे एक महत्वपूर्ण डेटा संरचना हैं।"
    step1: "Ruby फाइलें अपलोड करें या एरे डेटा पेस्ट करें।"
    step3: "Ruby सिंटैक्स विनिर्देशों के अनुपालन में Ruby एरे कोड उत्पन्न करें, Ruby प्रोजेक्ट्स में सीधे उपयोग योग्य।"
    from_alias: "Ruby Array"
    to_alias: "Ruby कोड"
  ASP:
    alias: "ASP Array"
    what: "ASP (Active Server Pages) Microsoft का सर्वर-साइड स्क्रिप्टिंग वातावरण है, गतिशील वेब पेज विकसित करने के लिए कई प्रोग्रामिंग भाषाओं का समर्थन करता है।"
    step1: "ASP फाइलें अपलोड करें या एरे डेटा पेस्ट करें।"
    step3: "VBScript और JScript सिंटैक्स के समर्थन के साथ ASP-संगत एरे कोड उत्पन्न करें, ASP.NET प्रोजेक्ट्स में उपयोग योग्य।"
    from_alias: "ASP Array"
    to_alias: "ASP कोड"
  ActionScript:
    alias: "ActionScript Array"
    what: "ActionScript एक ऑब्जेक्ट-ओरिएंटेड प्रोग्रामिंग भाषा है जो मुख्य रूप से Adobe Flash और AIR एप्लिकेशन विकास के लिए उपयोग की जाती है।"
    step1: ".as फाइलें अपलोड करें या ActionScript डेटा पेस्ट करें।"
    step3: "AS3 सिंटैक्स मानकों के अनुपालन में ActionScript एरे कोड उत्पन्न करें, Flash और Flex प्रोजेक्ट विकास के लिए उपयोग योग्य।"
    from_alias: "ActionScript Array"
    to_alias: "ActionScript कोड"
  BBCode:
    alias: "BBCode तालिका"
    what: "BBCode एक हल्की मार्कअप भाषा है जो आमतौर पर फोरम और ऑनलाइन समुदायों में उपयोग की जाती है, तालिका समर्थन सहित सरल फॉर्मेटिंग कार्यक्षमता प्रदान करती है।"
    step1: "BBCode वाली फाइलें अपलोड करें या डेटा पेस्ट करें।"
    step3: "फोरम पोस्टिंग और समुदाय सामग्री निर्माण के लिए उपयुक्त BBCode तालिका कोड उत्पन्न करें, संपीड़ित आउटपुट प्रारूप के समर्थन के साथ।"
    from_alias: "BBCode तालिका"
    to_alias: "BBCode प्रारूप"
  PDF:
    alias: "PDF तालिका"
    what: "PDF (Portable Document Format) एक क्रॉस-प्लेटफॉर्म दस्तावेज़ मानक है जिसमें निश्चित लेआउट, सुसंगत प्रदर्शन और उच्च गुणवत्ता मुद्रण विशेषताएं हैं। औपचारिक दस्तावेज़ों, रिपोर्टों, चालानों, अनुबंधों और शैक्षणिक पत्रों में व्यापक रूप से उपयोग किया जाता है। व्यापारिक संचार और दस्तावेज़ संग्रहण के लिए पसंदीदा प्रारूप, विभिन्न उपकरणों और ऑपरेटिंग सिस्टम में पूर्ण रूप से सुसंगत दृश्य प्रभाव सुनिश्चित करता है।"
    step1: "किसी भी प्रारूप में तालिका डेटा आयात करें। उपकरण स्वचालित रूप से डेटा संरचना का विश्लेषण करता है और बुद्धिमान लेआउट डिज़ाइन करता है, बड़ी तालिका ऑटो-पेजिनेशन और जटिल डेटा प्रकार प्रोसेसिंग का समर्थन करता है।"
    step3: "कई पेशेवर थीम शैलियों (व्यापारिक, शैक्षणिक, न्यूनतम, आदि), बहुभाषी फॉन्ट, ऑटो-पेजिनेशन, वॉटरमार्क जोड़ने और प्रिंट अनुकूलन के समर्थन के साथ उच्च गुणवत्ता PDF तालिका फाइलें उत्पन्न करें। सुनिश्चित करता है कि आउटपुट PDF दस्तावेज़ों में पेशेवर उपस्थिति हो, व्यापारिक प्रस्तुतियों और औपचारिक प्रकाशन के लिए सीधे उपयोग योग्य।"
    from_alias: "तालिका डेटा"
    to_alias: "PDF पेशेवर दस्तावेज़"
  JPEG:
    alias: "JPEG छवि"
    what: "JPEG सबसे व्यापक रूप से उपयोग किया जाने वाला डिजिटल छवि प्रारूप है जिसमें उत्कृष्ट संपीड़न प्रभाव और व्यापक संगतता है। इसका छोटा फाइल आकार और तेज़ लोडिंग गति इसे वेब प्रदर्शन, सोशल मीडिया साझाकरण, दस्तावेज़ चित्रण और ऑनलाइन प्रस्तुतियों के लिए उपयुक्त बनाती है। डिजिटल मीडिया और नेटवर्क संचार के लिए मानक छवि प्रारूप, लगभग सभी उपकरणों और सॉफ्टवेयर द्वारा पूर्ण रूप से समर्थित।"
    step1: "किसी भी प्रारूप में तालिका डेटा आयात करें। उपकरण बुद्धिमान लेआउट डिज़ाइन और दृश्य अनुकूलन करता है, स्वचालित रूप से इष्टतम आकार और रिज़ॉल्यूशन की गणना करता है।"
    step3: "कई थीम रंग योजनाओं (हल्का, गहरा, आंख-अनुकूल, आदि), अनुकूली लेआउट, टेक्स्ट स्पष्टता अनुकूलन और आकार कस्टमाइज़ेशन के समर्थन के साथ उच्च-परिभाषा JPEG तालिका छवियां उत्पन्न करें। ऑनलाइन साझाकरण, दस्तावेज़ सम्मिलन और प्रस्तुति उपयोग के लिए उपयुक्त, विभिन्न प्रदर्शन उपकरणों पर उत्कृष्ट दृश्य प्रभाव सुनिश्चित करता है।"
    from_alias: "तालिका डेटा"
    to_alias: "JPEG उच्च-परिभाषा छवि"
  Jira:
    alias: "Jira तालिका"
    what: "JIRA Atlassian द्वारा विकसित पेशेवर प्रोजेक्ट प्रबंधन और बग ट्रैकिंग सॉफ्टवेयर है, जो चुस्त विकास, सॉफ्टवेयर परीक्षण और प्रोजेक्ट सहयोग में व्यापक रूप से उपयोग किया जाता है। इसकी तालिका कार्यक्षमता समृद्ध फॉर्मेटिंग विकल्प और डेटा प्रदर्शन का समर्थन करती है, सॉफ्टवेयर विकास टीमों, प्रोजेक्ट प्रबंधकों और गुणवत्ता आश्वासन कर्मियों के लिए आवश्यकता प्रबंधन, बग ट्रैकिंग और प्रगति रिपोर्टिंग में एक महत्वपूर्ण उपकरण के रूप में काम करती है।"
    step1: "तालिका डेटा वाली फाइलें अपलोड करें या सीधे डेटा सामग्री पेस्ट करें। उपकरण स्वचालित रूप से तालिका डेटा और विशेष वर्ण एस्केपिंग को प्रोसेस करता है।"
    step3: "हेडर शैली सेटिंग्स, सेल संरेखण, वर्ण एस्केप प्रोसेसिंग और प्रारूप अनुकूलन के समर्थन के साथ JIRA प्लेटफॉर्म-संगत तालिका कोड उत्पन्न करें। उत्पन्न कोड सीधे JIRA इश्यू विवरण, टिप्पणियों या विकि पेजों में पेस्ट किया जा सकता है, JIRA सिस्टम में सही प्रदर्शन और रेंडरिंग सुनिश्चित करता है।"
    from_alias: "प्रोजेक्ट डेटा"
    to_alias: "Jira तालिका सिंटैक्स"
  Textile:
    alias: "Textile तालिका"
    what: "Textile एक संक्षिप्त हल्की मार्कअप भाषा है जिसमें सरल और सीखने में आसान सिंटैक्स है, सामग्री प्रबंधन सिस्टम, ब्लॉग प्लेटफॉर्म और फोरम सिस्टम में व्यापक रूप से उपयोग की जाती है। इसका तालिका सिंटैक्स स्पष्ट और सहज है, त्वरित फॉर्मेटिंग और शैली सेटिंग्स का समर्थन करता है। सामग्री निर्माताओं और वेबसाइट प्रशासकों के लिए तेज़ दस्तावेज़ लेखन और सामग्री प्रकाशन के लिए एक आदर्श उपकरण।"
    step1: "Textile प्रारूप फाइलें अपलोड करें या तालिका डेटा पेस्ट करें। उपकरण Textile मार्कअप सिंटैक्स को पार्स करता है और तालिका सामग्री निकालता है।"
    step3: "हेडर मार्कअप, सेल संरेखण, विशेष वर्ण एस्केपिंग और प्रारूप अनुकूलन के समर्थन के साथ मानक Textile तालिका सिंटैक्स उत्पन्न करें। उत्पन्न कोड सीधे CMS सिस्टम, ब्लॉग प्लेटफॉर्म और Textile का समर्थन करने वाले दस्तावेज़ सिस्टम में उपयोग किया जा सकता है, सही सामग्री रेंडरिंग और प्रदर्शन सुनिश्चित करता है।"
    from_alias: "Textile दस्तावेज़"
    to_alias: "Textile तालिका सिंटैक्स"
  PNG:
    alias: "PNG छवि"
    what: "PNG (Portable Network Graphics) एक हानिरहित छवि प्रारूप है जिसमें उत्कृष्ट संपीड़न और पारदर्शिता समर्थन है। वेब डिज़ाइन, डिजिटल ग्राफिक्स और पेशेवर फोटोग्राफी में व्यापक रूप से उपयोग किया जाता है। इसकी उच्च गुणवत्ता और व्यापक संगतता इसे स्क्रीनशॉट, लोगो, आरेख और स्पष्ट विवरण और पारदर्शी पृष्ठभूमि की आवश्यकता वाली किसी भी छवि के लिए आदर्श बनाती है।"
    step1: "किसी भी प्रारूप में तालिका डेटा आयात करें। उपकरण बुद्धिमान लेआउट डिज़ाइन और दृश्य अनुकूलन करता है, PNG आउटपुट के लिए स्वचालित रूप से इष्टतम आकार और रिज़ॉल्यूशन की गणना करता है।"
    step3: "कई थीम रंग योजनाओं, पारदर्शी पृष्ठभूमि, अनुकूली लेआउट और टेक्स्ट स्पष्टता अनुकूलन के समर्थन के साथ उच्च गुणवत्ता PNG तालिका छवियां उत्पन्न करें। उत्कृष्ट दृश्य गुणवत्ता के साथ वेब उपयोग, दस्तावेज़ सम्मिलन और पेशेवर प्रस्तुतियों के लिए पूर्ण।"
    from_alias: "तालिका डेटा"
    to_alias: "PNG उच्च गुणवत्ता छवि"
  TOML:
    alias: "TOML कॉन्फ़िगरेशन"
    what: "TOML (Tom's Obvious, Minimal Language) एक कॉन्फ़िगरेशन फाइल प्रारूप है जो पढ़ने और लिखने में आसान है। निर्विवाद और सरल होने के लिए डिज़ाइन किया गया, यह कॉन्फ़िगरेशन प्रबंधन के लिए आधुनिक सॉफ्टवेयर प्रोजेक्ट्स में व्यापक रूप से उपयोग किया जाता है। इसका स्पष्ट सिंटैक्स और मजबूत टाइपिंग इसे एप्लिकेशन सेटिंग्स और प्रोजेक्ट कॉन्फ़िगरेशन फाइलों के लिए एक उत्कृष्ट विकल्प बनाती है।"
    step1: "TOML फाइलें अपलोड करें या कॉन्फ़िगरेशन डेटा पेस्ट करें। उपकरण TOML सिंटैक्स को पार्स करता है और संरचित कॉन्फ़िगरेशन जानकारी निकालता है।"
    step3: "नेस्टेड संरचनाओं, डेटा प्रकारों और टिप्पणियों के समर्थन के साथ मानक TOML प्रारूप उत्पन्न करें। उत्पन्न TOML फाइलें एप्लिकेशन कॉन्फ़िगरेशन, बिल्ड टूल्स और प्रोजेक्ट सेटिंग्स के लिए पूर्ण हैं।"
    from_alias: "TOML कॉन्फ़िगरेशन"
    to_alias: "TOML प्रारूप"
  INI:
    alias: "INI कॉन्फ़िगरेशन"
    what: "INI फाइलें कई एप्लिकेशन और ऑपरेटिंग सिस्टम द्वारा उपयोग की जाने वाली सरल कॉन्फ़िगरेशन फाइलें हैं। उनकी सीधी की-वैल्यू जोड़ी संरचना उन्हें मैन्युअल रूप से पढ़ना और संपादित करना आसान बनाती है। Windows एप्लिकेशन, लीगेसी सिस्टम और सरल कॉन्फ़िगरेशन परिदृश्यों में व्यापक रूप से उपयोग किया जाता है जहां मानव पठनीयता महत्वपूर्ण है।"
    step1: "INI फाइलें अपलोड करें या कॉन्फ़िगरेशन डेटा पेस्ट करें। उपकरण INI सिंटैक्स को पार्स करता है और सेक्शन-आधारित कॉन्फ़िगरेशन जानकारी निकालता है।"
    step3: "सेक्शन, टिप्पणियों और विभिन्न डेटा प्रकारों के समर्थन के साथ मानक INI प्रारूप उत्पन्न करें। उत्पन्न INI फाइलें अधिकांश एप्लिकेशन और कॉन्फ़िगरेशन सिस्टम के साथ संगत हैं।"
    from_alias: "INI कॉन्फ़िगरेशन"
    to_alias: "INI प्रारूप"
  Avro:
    alias: "Avro स्कीमा"
    what: "Apache Avro एक डेटा सीरियलाइज़ेशन सिस्टम है जो समृद्ध डेटा संरचनाएं, कॉम्पैक्ट बाइनरी प्रारूप और स्कीमा विकास क्षमताएं प्रदान करता है। बिग डेटा प्रोसेसिंग, मैसेज क्यू और वितरित सिस्टम में व्यापक रूप से उपयोग किया जाता है। इसकी स्कीमा परिभाषा जटिल डेटा प्रकारों और संस्करण संगतता का समर्थन करती है, जो इसे डेटा इंजीनियरों और सिस्टम आर्किटेक्ट्स के लिए एक महत्वपूर्ण उपकरण बनाती है।"
    step1: "Avro स्कीमा फाइलें अपलोड करें या डेटा पेस्ट करें। उपकरण Avro स्कीमा परिभाषाओं को पार्स करता है और तालिका संरचना जानकारी निकालता है।"
    step3: "डेटा प्रकार मैपिंग, फील्ड बाधाओं और स्कीमा सत्यापन के समर्थन के साथ मानक Avro स्कीमा परिभाषाएं उत्पन्न करें। उत्पन्न स्कीमा सीधे Hadoop इकोसिस्टम, Kafka मैसेज सिस्टम और अन्य बिग डेटा प्लेटफॉर्म में उपयोग किए जा सकते हैं।"
    from_alias: "Avro स्कीमा"
    to_alias: "Avro डेटा प्रारूप"
  Protobuf:
    alias: "प्रोटोकॉल बफर्स"
    what: "प्रोटोकॉल बफर्स (protobuf) Google का भाषा-तटस्थ, प्लेटफॉर्म-तटस्थ, संरचित डेटा को सीरियलाइज़ करने के लिए विस्तार योग्य तंत्र है। माइक्रोसर्विसेज, API विकास और डेटा स्टोरेज में व्यापक रूप से उपयोग किया जाता है। इसका कुशल बाइनरी प्रारूप और मजबूत टाइपिंग इसे उच्च-प्रदर्शन एप्लिकेशन और क्रॉस-भाषा संचार के लिए आदर्श बनाती है।"
    step1: ".proto फाइलें अपलोड करें या प्रोटोकॉल बफर परिभाषाएं पेस्ट करें। उपकरण protobuf सिंटैक्स को पार्स करता है और मैसेज संरचना जानकारी निकालता है।"
    step3: "मैसेज प्रकार, फील्ड विकल्प और सेवा परिभाषाओं के समर्थन के साथ मानक प्रोटोकॉल बफर परिभाषाएं उत्पन्न करें। उत्पन्न .proto फाइलें कई प्रोग्रामिंग भाषाओं के लिए कंपाइल की जा सकती हैं।"
    from_alias: "प्रोटोकॉल बफर"
    to_alias: "Protobuf स्कीमा"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas Python में सबसे लोकप्रिय डेटा विश्लेषण लाइब्रेरी है, जिसमें DataFrame इसकी मुख्य डेटा संरचना है। यह शक्तिशाली डेटा मैनिपुलेशन, सफाई और विश्लेषण क्षमताएं प्रदान करती है, डेटा साइंस, मशीन लर्निंग और बिजनेस इंटेलिजेंस में व्यापक रूप से उपयोग की जाती है। Python डेवलपर्स और डेटा विश्लेषकों के लिए एक अपरिहार्य उपकरण।"
    step1: "DataFrame कोड वाली Python फाइलें अपलोड करें या डेटा पेस्ट करें। उपकरण Pandas सिंटैक्स को पार्स करता है और DataFrame संरचना जानकारी निकालता है।"
    step3: "डेटा प्रकार विनिर्देशों, इंडेक्स सेटिंग्स और डेटा ऑपरेशन के समर्थन के साथ मानक Pandas DataFrame कोड उत्पन्न करें। उत्पन्न कोड डेटा विश्लेषण और प्रोसेसिंग के लिए Python वातावरण में सीधे निष्पादित किया जा सकता है।"
    from_alias: "Pandas DataFrame"
    to_alias: "Python डेटा संरचना"
  RDF:
    alias: "RDF ट्रिपल"
    what: "RDF (Resource Description Framework) वेब पर डेटा इंटरचेंज के लिए एक मानक मॉडल है, जो संसाधनों के बारे में जानकारी को ग्राफ रूप में प्रस्तुत करने के लिए डिज़ाइन किया गया है। सिमेंटिक वेब, नॉलेज ग्राफ और लिंक्ड डेटा एप्लिकेशन में व्यापक रूप से उपयोग किया जाता है। इसकी ट्रिपल संरचना समृद्ध मेटाडेटा प्रतिनिधित्व और सिमेंटिक संबंधों को सक्षम बनाती है।"
    step1: "RDF फाइलें अपलोड करें या ट्रिपल डेटा पेस्ट करें। उपकरण RDF सिंटैक्स को पार्स करता है और सिमेंटिक संबंध और संसाधन जानकारी निकालता है।"
    step3: "विभिन्न सीरियलाइज़ेशन (RDF/XML, Turtle, N-Triples) के समर्थन के साथ मानक RDF प्रारूप उत्पन्न करें। उत्पन्न RDF का उपयोग सिमेंटिक वेब एप्लिकेशन, नॉलेज बेस और लिंक्ड डेटा सिस्टम में किया जा सकता है।"
    from_alias: "RDF डेटा"
    to_alias: "RDF सिमेंटिक प्रारूप"
  MATLAB:
    alias: "MATLAB Array"
    what: "MATLAB एक उच्च-प्रदर्शन संख्यात्मक कंप्यूटिंग और विज़ुअलाइज़ेशन सॉफ्टवेयर है जो इंजीनियरिंग कंप्यूटिंग, डेटा विश्लेषण और एल्गोरिदम विकास में व्यापक रूप से उपयोग किया जाता है। इसके एरे और मैट्रिक्स ऑपरेशन शक्तिशाली हैं, जटिल गणितीय गणनाओं और डेटा प्रोसेसिंग का समर्थन करते हैं। इंजीनियरों, शोधकर्ताओं और डेटा वैज्ञानिकों के लिए एक आवश्यक उपकरण।"
    step1: "MATLAB .m फाइलें अपलोड करें या एरे डेटा पेस्ट करें। उपकरण MATLAB सिंटैक्स को पार्स करता है और एरे संरचना जानकारी निकालता है।"
    step3: "बहु-आयामी एरे, डेटा प्रकार विनिर्देशों और वेरिएबल नामकरण के समर्थन के साथ मानक MATLAB एरे कोड उत्पन्न करें। उत्पन्न कोड डेटा विश्लेषण और वैज्ञानिक कंप्यूटिंग के लिए MATLAB वातावरण में सीधे निष्पादित किया जा सकता है।"
    from_alias: "MATLAB Array"
    to_alias: "MATLAB कोड प्रारूप"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame R प्रोग्रामिंग भाषा में मुख्य डेटा संरचना है, जो सांख्यिकीय विश्लेषण, डेटा माइनिंग और मशीन लर्निंग में व्यापक रूप से उपयोग की जाती है। R सांख्यिकीय कंप्यूटिंग और ग्राफिक्स के लिए प्रमुख उपकरण है, जिसमें DataFrame शक्तिशाली डेटा मैनिपुलेशन, सांख्यिकीय विश्लेषण और विज़ुअलाइज़ेशन क्षमताएं प्रदान करता है। संरचित डेटा विश्लेषण के साथ काम करने वाले डेटा वैज्ञानिकों, सांख्यिकीविदों और शोधकर्ताओं के लिए आवश्यक।"
    step1: "R डेटा फाइलें अपलोड करें या DataFrame कोड पेस्ट करें। उपकरण R सिंटैक्स को पार्स करता है और कॉलम प्रकार, पंक्ति नाम और डेटा सामग्री सहित DataFrame संरचना जानकारी निकालता है।"
    step3: "डेटा प्रकार विनिर्देशों, फैक्टर स्तरों, पंक्ति/कॉलम नामों और R-विशिष्ट डेटा संरचनाओं के समर्थन के साथ मानक R DataFrame कोड उत्पन्न करें। उत्पन्न कोड सांख्यिकीय विश्लेषण और डेटा प्रोसेसिंग के लिए R वातावरण में सीधे निष्पादित किया जा सकता है।"
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "रूपांतरण शुरू करें"
  start_generating: "जेनरेट करना शुरू करें"
  api_docs: "API दस्तावेज़"
related:
  section_title: 'अधिक {{ if and .from (ne .from "generator") }}{{ .from }} और {{ end }}{{ .to }} कन्वर्टर'
  section_description: '{{ if and .from (ne .from "generator") }}{{ .from }} और {{ end }}{{ .to }} फॉर्मेट के लिए अधिक कन्वर्टर खोजें। हमारे पेशेवर ऑनलाइन रूपांतरण टूल के साथ अपने डेटा को कई फॉर्मेट के बीच रूपांतरित करें।'
  title: "{{ .from }} से {{ .to }}"
howto:
  step2: "पेशेवर सुविधाओं के साथ हमारे उन्नत ऑनलाइन टेबल एडिटर का उपयोग करके डेटा संपादित करें। खाली पंक्तियों को हटाना, डुप्लिकेट हटाना, डेटा ट्रांसपोज़िशन, सॉर्टिंग, रेगेक्स खोज और बदलना, और रियल-टाइम प्रीव्यू का समर्थन करता है। सभी परिवर्तन स्वचालित रूप से %s फॉर्मेट में सटीक, विश्वसनीय परिणामों के साथ रूपांतरित हो जाते हैं।"
  section_title: "{{ . }} का उपयोग कैसे करें"
  converter_description: "हमारे चरण-दर-चरण गाइड के साथ {{ .from }} को {{ .to }} में रूपांतरित करना सीखें। उन्नत सुविधाओं और रियल-टाइम प्रीव्यू के साथ पेशेवर ऑनलाइन कन्वर्टर।"
  generator_description: "हमारे ऑनलाइन जेनरेटर के साथ पेशेवर {{ .to }} टेबल बनाना सीखें। Excel जैसी संपादन, रियल-टाइम प्रीव्यू, और तत्काल निर्यात क्षमताएं।"
extension:
  section_title: "टेबल डिटेक्शन और एक्सट्रैक्शन एक्सटेंशन"
  section_description: "एक क्लिक में किसी भी वेबसाइट से टेबल निकालें। Excel, CSV, JSON सहित 30+ फॉर्मेट में तुरंत कन्वर्ट करें - कॉपी-पेस्टिंग की आवश्यकता नहीं।"
  features:
    extraction_title: "वन-क्लिक टेबल एक्सट्रैक्शन"
    extraction_description: "कॉपी-पेस्टिंग के बिना किसी भी वेबपेज से तुरंत टेबल निकालें - पेशेवर डेटा एक्सट्रैक्शन को सरल बनाया गया"
    formats_title: "30+ फॉर्मेट कन्वर्टर सपोर्ट"
    formats_description: "हमारे उन्नत टेबल कन्वर्टर के साथ निकाली गई टेबल को Excel, CSV, JSON, Markdown, SQL, और अधिक में कन्वर्ट करें"
    detection_title: "स्मार्ट टेबल डिटेक्शन"
    detection_description: "तेज़ डेटा एक्सट्रैक्शन और रूपांतरण के लिए किसी भी वेबपेज पर टेबल को स्वचालित रूप से पहचानता और हाइलाइट करता है"
  hover_tip: "✨ एक्सट्रैक्शन आइकन देखने के लिए किसी भी टेबल पर होवर करें"
recommendations:
  section_title: "विश्वविद्यालयों और पेशेवरों द्वारा अनुशंसित"
  section_description: "विश्वसनीय टेबल रूपांतरण और डेटा प्रोसेसिंग के लिए विश्वविद्यालयों, अनुसंधान संस्थानों और विकास टीमों के पेशेवरों द्वारा TableConvert पर भरोसा किया जाता है।"
  cards:
    university_title: "विस्कॉन्सिन-मैडिसन विश्वविद्यालय"
    university_description: "TableConvert.com - पेशेवर मुफ्त ऑनलाइन टेबल कन्वर्टर और डेटा फॉर्मेट टूल"
    university_link: "लेख पढ़ें"
    facebook_title: "डेटा प्रोफेशनल कम्युनिटी"
    facebook_description: "Facebook डेवलपर समूहों में डेटा विश्लेषकों और पेशेवरों द्वारा साझा और अनुशंसित"
    facebook_link: "पोस्ट देखें"
    twitter_title: "डेवलपर कम्युनिटी"
    twitter_description: "टेबल रूपांतरण के लिए X (Twitter) पर @xiaoying_eth और अन्य डेवलपर्स द्वारा अनुशंसित"
    twitter_link: "ट्वीट देखें"
faq:
  section_title: "अक्सर पूछे जाने वाले प्रश्न"
  section_description: "हमारे मुफ्त ऑनलाइन टेबल कन्वर्टर, डेटा फॉर्मेट और रूपांतरण प्रक्रिया के बारे में सामान्य प्रश्न."
  what: "%s फॉर्मेट क्या है?"
  howto_convert:
    question: "{{ . }} का मुफ्त में उपयोग कैसे करें?"
    answer: "हमारे मुफ्त ऑनलाइन टेबल कन्वर्टर का उपयोग करके अपनी {{ .from }} फाइल अपलोड करें, डेटा पेस्ट करें, या वेब पेजों से निकालें। हमारा पेशेवर कन्वर्टर टूल रियल-टाइम प्रीव्यू और उन्नत संपादन सुविधाओं के साथ आपके डेटा को तुरंत {{ .to }} फॉर्मेट में रूपांतरित करता है। रूपांतरित परिणाम को तुरंत डाउनलोड या कॉपी करें।"
  security:
    question: "क्या इस ऑनलाइन कन्वर्टर का उपयोग करते समय मेरा डेटा सुरक्षित है?"
    answer: "बिल्कुल! सभी टेबल रूपांतरण आपके ब्राउज़र में स्थानीय रूप से होते हैं - आपका डेटा कभी भी आपके डिवाइस को नहीं छोड़ता। हमारा ऑनलाइन कन्वर्टर सब कुछ क्लाइंट-साइड प्रोसेस करता है, पूर्ण गोपनीयता और डेटा सुरक्षा सुनिश्चित करता है। हमारे सर्वर पर कोई फाइलें संग्रहीत नहीं की जातीं।"
  free:
    question: "क्या TableConvert वास्तव में उपयोग के लिए मुफ्त है?"
    answer: "हाँ, TableConvert पूरी तरह से मुफ्त है! सभी कन्वर्टर सुविधाएं, टेबल एडिटर, डेटा जेनरेटर टूल, और निर्यात विकल्प बिना किसी लागत, पंजीकरण या छुपी हुई फीस के उपलब्ध हैं। मुफ्त में ऑनलाइन असीमित फाइलें कन्वर्ट करें।"
  filesize:
    question: "ऑनलाइन कन्वर्टर की फाइल साइज़ सीमा क्या है?"
    answer: "हमारा मुफ्त ऑनलाइन टेबल कन्वर्टर 10MB तक की फाइलों का समर्थन करता है। बड़ी फाइलों, बैच प्रोसेसिंग, या एंटरप्राइज़ आवश्यकताओं के लिए, उच्च सीमा के साथ हमारे ब्राउज़र एक्सटेंशन या पेशेवर API सेवा का उपयोग करें।"
stats:
  conversions: "रूपांतरित टेबल"
  tables: "जेनरेट की गई टेबल"
  formats: "डेटा फाइल फॉर्मेट"
  rating: "उपयोगकर्ता रेटिंग"
