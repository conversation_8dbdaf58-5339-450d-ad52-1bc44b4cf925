site:
  fullname: "Convertisseur de Tableaux en Ligne"
  name: "TableConvert"
  subtitle: "Convertisseur et Générateur de Tableaux en Ligne Gratuit"
  intro: "TableConvert est un outil gratuit en ligne de conversion de tableaux et de génération de données supportant la conversion entre plus de 30 formats incluant Excel, CSV, JSON, Markdown, LaTeX, SQL et plus."
  followTwitter: "Suivez-nous sur X"
title:
  converter: "%s vers %s"
  generator: "Générateur %s"
post:
  tags:
    converter: "Convertisseur"
    editor: "Éditeur"
    generator: "Générateur"
    maker: "Constructeur"
  converter:
    title: "Convertir %s vers %s en Ligne"
    short: "Un outil en ligne gratuit et puissant de %s vers %s"
    intro: "Convertisseur en ligne %s vers %s facile à utiliser. Transformez les données de tableau sans effort avec notre outil de conversion intuitif. Rapide, fiable et convivial."
  generator:
    title: "Éditeur et Générateur %s en Ligne"
    short: "Outil professionnel de génération en ligne %s avec des fonctionnalités complètes"
    intro: "Générateur %s en ligne et éditeur de tableau facile à utiliser. Créez des tableaux de données professionnels sans effort avec notre outil intuitif et aperçu en temps réel."
navbar:
  search:
    placeholder: "Rechercher un convertisseur..."
  sponsor: "Offrez-nous un Café"
  extension: "Extension"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Source de Données"
    placeholder: "Collez vos données %s ou glissez les fichiers %s ici"
    example: "Exemple"
    upload: "Télécharger un Fichier"
    extract:
      enter: "Extraire d'une Page Web"
      intro: "Entrez une URL de page web contenant des données de tableau pour extraire automatiquement des données structurées"
      btn: "Extraire %s"
    excel:
      sheet: "Feuille de Calcul"
      none: "Aucune"
  tableEditor:
    title: "Éditeur de Tableaux en Ligne"
    undo: "Annuler"
    redo: "Refaire"
    transpose: "Transposer"
    clear: "Effacer"
    deleteBlank: "Supprimer les Vides"
    deleteDuplicate: "Supprimer les Doublons"
    uppercase: "MAJUSCULES"
    lowercase: "minuscules"
    capitalize: "Capitaliser"
    replace:
      replace: "Rechercher et Remplacer (Regex pris en charge)"
      subst: "Remplacer par..."
      btn: "Remplacer Tout"
  tableGenerator:
    title: "Générateur de Tableaux"
    sponsor: "Offrez-nous un Café"
    copy: "Copier dans le Presse-papiers"
    download: "Télécharger le Fichier"
    tooltip:
      html:
        escape: "Échapper les caractères spéciaux HTML (&, <, >, \", ') pour éviter les erreurs d'affichage"
        div: "Utiliser la mise en page DIV+CSS au lieu des balises TABLE traditionnelles, mieux adapté au design responsive"
        minify: "Supprimer les espaces et les sauts de ligne pour générer du code HTML compressé"
        thead: "Générer une structure standard d'en-tête de tableau (&lt;thead&gt;) et de corps (&lt;tbody&gt;)"
        tableCaption: "Ajouter un titre descriptif au-dessus du tableau (élément &lt;caption&gt;)"
        tableClass: "Ajouter un nom de classe CSS au tableau pour une personnalisation de style facile"
        tableId: "Définir un identifiant ID unique pour le tableau pour la manipulation JavaScript"
      jira:
        escape: "Échapper les caractères pipe (|) pour éviter les conflits avec la syntaxe de tableau Jira"
      json:
        parsingJSON: "Analyser intelligemment les chaînes JSON dans les cellules en objets"
        minify: "Générer un format JSON compact sur une ligne pour réduire la taille du fichier"
        format: "Sélectionner la structure de données JSON de sortie : tableau d'objets, tableau 2D, etc."
      latex:
        escape: "Échapper les caractères spéciaux LaTeX (%, &, _, #, $, etc.) pour assurer une compilation correcte"
        ht: "Ajouter le paramètre de position flottante [!ht] pour contrôler la position du tableau sur la page"
        mwe: "Générer un document LaTeX complet"
        tableAlign: "Définir l'alignement horizontal du tableau sur la page"
        tableBorder: "Configurer le style de bordure du tableau : sans bordure, bordure partielle, bordure complète"
        label: "Définir l'étiquette du tableau pour les références croisées de la commande \\ref{}"
        caption: "Définir la légende du tableau à afficher au-dessus ou en dessous du tableau"
        location: "Choisir la position d'affichage de la légende du tableau : au-dessus ou en dessous"
        tableType: "Choisir le type d'environnement de tableau : tabular, longtable, array, etc."
      markdown:
        escape: "Échapper les caractères spéciaux Markdown (*, _, |, \\, etc.) pour éviter les conflits de format"
        pretty: "Auto-aligner les largeurs de colonnes pour générer un format de tableau plus beau"
        simple: "Utiliser une syntaxe simplifiée, omettant les lignes verticales de bordure extérieure"
        boldFirstRow: "Mettre le texte de la première ligne en gras"
        boldFirstColumn: "Mettre le texte de la première colonne en gras"
        firstHeader: "Traiter la première ligne comme en-tête et ajouter une ligne de séparation"
        textAlign: "Définir l'alignement du texte de colonne : gauche, centre, droite"
        multilineHandling: "Gestion du texte multiligne : préserver les sauts de ligne, échapper vers \\n, utiliser les balises &lt;br&gt;"

        includeLineNumbers: "Ajouter une colonne de numéros de ligne sur le côté gauche du tableau"
      magic:
        builtin: "Sélectionner les formats de modèle communs prédéfinis"
        rowsTpl: "<table> <tr> <th>Syntaxe Magique</th> <th>Description</th> <th>Méthodes JS Supportées</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>1er, 2ème ... champ de l'<b>e</b>n-tête, Alias {hA} {hB} ...</td> <td>Méthodes de chaîne</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>1er, 2ème ... champ de la ligne actuelle, Alias {$A} {$B} ...</td> <td>Méthodes de chaîne</td> </tr> <tr> <td>{F,} {F;}</td> <td>Diviser la ligne actuelle par la chaîne après <b>F</b></td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td><b>N</b>uméro de ligne de la ligne actuelle à partir de 1 ou 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>N</b>uméro de ligne de <b>f</b>in des lignes </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>E<b>x</b>écuter du code JavaScript, ex : {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Utiliser la barre oblique inverse <b>\\</b> pour afficher les accolades {...} </td> <td></td> </tr></table>"
        headerTpl: "Modèle de sortie personnalisé pour la section d'en-tête"
        footerTpl: "Modèle de sortie personnalisé pour la section de pied de page"
      textile:
        escape: "Échapper les caractères de syntaxe Textile (|, ., -, ^) pour éviter les conflits de format"
        rowHeader: "Définir la première ligne comme ligne d'en-tête"
        thead: "Ajouter des marqueurs de syntaxe Textile pour l'en-tête et le corps du tableau"
      xml:
        escape: "Échapper les caractères spéciaux XML (&lt;, &gt;, &amp;, \", ') pour assurer un XML valide"
        minify: "Générer une sortie XML compressée, supprimant les espaces supplémentaires"
        rootElement: "Définir le nom de balise de l'élément racine XML"
        rowElement: "Définir le nom de balise de l'élément XML pour chaque ligne de données"
        declaration: "Ajouter l'en-tête de déclaration XML (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Sortir les données comme attributs XML au lieu d'éléments enfants"
        cdata: "Envelopper le contenu texte avec CDATA pour protéger les caractères spéciaux"
        encoding: "Définir le format d'encodage de caractères pour le document XML"
        indentation: "Choisir le caractère d'indentation XML : espaces ou tabulations"
      yaml:
        indentSize: "Définir le nombre d'espaces pour l'indentation de hiérarchie YAML (généralement 2 ou 4)"
        arrayStyle: "Format de tableau : bloc (un élément par ligne) ou flux (format en ligne)"
        quotationStyle: "Style de guillemets de chaîne : sans guillemets, guillemets simples, guillemets doubles"
      pdf:
        theme: "Choisir le style visuel de tableau PDF pour documents professionnels"
        headerColor: "Choisir la couleur d'arrière-plan d'en-tête de tableau PDF"
        showHead: "Contrôler l'affichage d'en-tête sur les pages PDF"
        docTitle: "Titre optionnel pour le document PDF"
        docDescription: "Texte de description optionnel pour document PDF"
      csv:
        bom: "Ajouter la marque d'ordre d'octets UTF-8 pour aider Excel et autres logiciels à reconnaître l'encodage"
      excel:
        autoWidth: "Ajuster automatiquement la largeur de colonne basée sur le contenu"
        protectSheet: "Activer la protection de feuille de calcul avec mot de passe : tableconvert.com"
      sql:
        primaryKey: "Spécifier le nom de champ de clé primaire pour l'instruction CREATE TABLE"
        dialect: "Sélectionner le type de base de données, affectant la syntaxe des guillemets et types de données"
      ascii:
        forceSep: "Forcer les lignes de séparation entre chaque ligne de données"
        style: "Sélectionner le style de dessin de bordure de tableau ASCII"
        comment: "Ajouter des marqueurs de commentaire pour envelopper tout le tableau"
      mediawiki:
        minify: "Compresser le code de sortie, supprimant les espaces supplémentaires"
        header: "Marquer la première ligne comme style d'en-tête"
        sort: "Activer la fonctionnalité de tri par clic sur tableau"
      asciidoc:
        minify: "Compresser la sortie de format AsciiDoc"
        firstHeader: "Définir la première ligne comme ligne d'en-tête"
        lastFooter: "Définir la dernière ligne comme ligne de pied de page"
        title: "Ajouter du texte de titre au tableau"
      tracwiki:
        rowHeader: "Définir la première ligne comme en-tête"
        colHeader: "Définir la première colonne comme en-tête"
      bbcode:
        minify: "Compresser le format de sortie BBCode"
      restructuredtext:
        style: "Sélectionner le style de bordure de tableau reStructuredText"
        forceSep: "Forcer les lignes de séparation"
    label:
      ascii:
        forceSep: "Séparateurs de Ligne"
        style: "Style de Bordure"
        comment: "Enveloppe de Commentaire"
      restructuredtext:
        style: "Style de Bordure"
        forceSep: "Forcer les Séparateurs"
      bbcode:
        minify: "Compresser la Sortie"
      csv:
        doubleQuote: "Guillemets Doubles"
        delimiter: "Délimiteur de Champ"
        bom: "UTF-8 BOM"
        valueDelimiter: "Délimiteur de Valeur"
        rowDelimiter: "Délimiteur de Ligne"
        prefix: "Préfixe de Ligne"
        suffix: "Suffixe de Ligne"
      excel:
        autoWidth: "Largeur Automatique"
        textFormat: "Format de Texte"
        protectSheet: "Protéger la Feuille"
        boldFirstRow: "Première Ligne en Gras"
        boldFirstColumn: "Première Colonne en Gras"
        sheetName: "Nom de la Feuille"
      html:
        escape: "Échapper les Caractères HTML"
        div: "Tableau DIV"
        minify: "Compresser le Code"
        thead: "Structure d'En-tête"
        tableCaption: "Légende du Tableau"
        tableClass: "Classe du Tableau"
        tableId: "ID du Tableau"
        rowHeader: "En-tête de Ligne"
        colHeader: "En-tête de Colonne"
      jira:
        escape: "Échapper les Caractères"
        rowHeader: "En-tête de Ligne"
        colHeader: "En-tête de Colonne"
      json:
        parsingJSON: "Analyser JSON"
        minify: "Compresser la Sortie"
        format: "Format de Données"
        rootName: "Nom de l'Objet Racine"
        indentSize: "Taille d'Indentation"
      jsonlines:
        parsingJSON: "Analyser JSON"
        format: "Format de Données"
      latex:
        escape: "Échapper les Caractères LaTeX"
        ht: "Position Flottante"
        mwe: "Document Complet"
        tableAlign: "Alignement du Tableau"
        tableBorder: "Style de Bordure"
        label: "Étiquette de Référence"
        caption: "Légende du Tableau"
        location: "Position de la Légende"
        tableType: "Type de Tableau"
        boldFirstRow: "Première Ligne en Gras"
        boldFirstColumn: "Première Colonne en Gras"
        textAlign: "Alignement du Texte"
        borders: "Paramètres de Bordure"
      markdown:
        escape: "Échapper les Caractères"
        pretty: "Tableau Markdown Élégant"
        simple: "Format Markdown Simple"
        boldFirstRow: "Première Ligne en Gras"
        boldFirstColumn: "Première Colonne en Gras"
        firstHeader: "Premier En-tête"
        textAlign: "Alignement du Texte"
        multilineHandling: "Gestion Multiligne"

        includeLineNumbers: "Ajouter Numéros de Ligne"
        align: "Alignement"
      mediawiki:
        minify: "Compresser le Code"
        header: "Balisage d'En-tête"
        sort: "Triable"
      asciidoc:
        minify: "Compresser le Format"
        firstHeader: "Premier En-tête"
        lastFooter: "Dernier Pied de Page"
        title: "Titre du Tableau"
      tracwiki:
        rowHeader: "En-tête de Ligne"
        colHeader: "En-tête de Colonne"
      sql:
        drop: "Supprimer Table (Si Existe)"
        create: "Créer Table"
        oneInsert: "Insertion par Lot"
        table: "Nom de Table"
        dialect: "Type de Base de Données"
        primaryKey: "Clé Primaire"
      magic:
        builtin: "Modèle Intégré"
        rowsTpl: "Modèle de Ligne, Syntaxe ->"
        headerTpl: "Modèle d'En-tête"
        footerTpl: "Modèle de Pied"
      textile:
        escape: "Échapper les Caractères"
        rowHeader: "En-tête de Ligne"
        thead: "Syntaxe d'En-tête"
      xml:
        escape: "Échapper les Caractères XML"
        minify: "Compresser la Sortie"
        rootElement: "Élément Racine"
        rowElement: "Élément de Ligne"
        declaration: "Déclaration XML"
        attributes: "Mode d'Attributs"
        cdata: "Enveloppe CDATA"
        encoding: "Encodage"
        indentSize: "Taille d'Indentation"
      yaml:
        indentSize: "Taille d'Indentation"
        arrayStyle: "Style de Tableau"
        quotationStyle: "Style de Guillemets"
      pdf:
        theme: "Thème de Tableau PDF"
        headerColor: "Couleur d'En-tête PDF"
        showHead: "Affichage d'En-tête PDF"
        docTitle: "Titre de Document PDF"
        docDescription: "Description de Document PDF"
sidebar:
  all: "Tous les Outils de Conversion"
  dataSource:
    title: "Source de Données"
    description:
      converter: "Importez %s pour conversion vers %s. Prend en charge le téléchargement de fichiers, l'édition en ligne, et l'extraction de données web."
      generator: "Créez des données de tableau avec prise en charge de plusieurs méthodes d'entrée incluant la saisie manuelle, l'importation de fichiers, et la génération de modèles."
  tableEditor:
    title: "Éditeur de Tableaux en Ligne"
    description:
      converter: "Traitez %s en ligne en utilisant notre éditeur de tableaux. Expérience d'opération similaire à Excel avec prise en charge de la suppression des lignes vides, déduplication, tri, et rechercher et remplacer."
      generator: "Puissant éditeur de tableaux en ligne offrant une expérience d'opération similaire à Excel. Prend en charge la suppression des lignes vides, déduplication, tri, et rechercher et remplacer."
  tableGenerator:
    title: "Générateur de Tableaux"
    description:
      converter: "Générez rapidement %s avec aperçu en temps réel du générateur de tableaux. Options d'exportation riches, copie et téléchargement en un clic."
      generator: "Exportez les données %s dans plusieurs formats pour répondre à différents scénarios d'utilisation. Prend en charge les options personnalisées et l'aperçu en temps réel."
footer:
  changelog: "Journal des Modifications"
  sponsor: "Sponsors"
  contact: "Nous Contacter"
  privacyPolicy: "Politique de Confidentialité"
  about: "À Propos"
  resources: "Ressources"
  popularConverters: "Convertisseurs Populaires"
  popularGenerators: "Générateurs Populaires"
  dataSecurity: "Vos données sont sécurisées - toutes les conversions s'exécutent dans votre navigateur."
converters:
  Markdown:
    alias: "Tableau Markdown"
    what: "Markdown est un langage de balisage léger largement utilisé pour la documentation technique, la création de contenu de blog, et le développement web. Sa syntaxe de tableau est concise et intuitive, prenant en charge l'alignement du texte, l'intégration de liens, et le formatage. C'est l'outil préféré des programmeurs et rédacteurs techniques, parfaitement compatible avec GitHub, GitLab, et autres plateformes d'hébergement de code."
    step1: "Collez les données de tableau Markdown dans la zone source de données, ou glissez-déposez directement les fichiers .md pour téléchargement. L'outil analyse automatiquement la structure et le formatage du tableau, prenant en charge le contenu imbriqué complexe et la gestion des caractères spéciaux."
    step3: "Générez du code de tableau Markdown standard en temps réel, prenant en charge plusieurs méthodes d'alignement, mise en gras du texte, ajout de numéros de ligne, et autres paramètres de format avancés. Le code généré est entièrement compatible avec GitHub et les principaux éditeurs Markdown, prêt à utiliser avec copie en un clic."
    from_alias: "Fichier de Tableau Markdown"
    to_alias: "Format de Tableau Markdown"
  Magic:
    alias: "Modèle Personnalisé"
    what: "Le modèle Magic est un générateur de données avancé unique de cet outil, permettant aux utilisateurs de créer une sortie de données de format arbitraire grâce à une syntaxe de modèle personnalisée. Prend en charge le remplacement de variables, le jugement conditionnel, et le traitement en boucle. C'est la solution ultime pour gérer les besoins complexes de conversion de données et les formats de sortie personnalisés, particulièrement adapté aux développeurs et ingénieurs de données."
    step1: "Sélectionnez des modèles communs intégrés ou créez une syntaxe de modèle personnalisée. Prend en charge des variables et fonctions riches qui peuvent gérer des structures de données complexes et la logique métier."
    step3: "Générez une sortie de données qui répond entièrement aux exigences de format personnalisé. Prend en charge la logique de conversion de données complexe et le traitement conditionnel, améliorant considérablement l'efficacité du traitement des données et la qualité de sortie. Un outil puissant pour le traitement de données par lots."
    from_alias: "Données de Tableau"
    to_alias: "Sortie de Format Personnalisé"
  CSV:
    alias: "CSV"
    what: "CSV (Valeurs Séparées par des Virgules) est le format d'échange de données le plus largement utilisé, parfaitement pris en charge par Excel, Google Sheets, systèmes de base de données, et divers outils d'analyse de données. Sa structure simple et sa forte compatibilité en font le format standard pour la migration de données, l'importation/exportation par lots, et l'échange de données multi-plateforme, largement utilisé dans l'analyse commerciale, la science des données, et l'intégration de systèmes."
    step1: "Téléchargez des fichiers CSV ou collez directement des données CSV. L'outil reconnaît intelligemment divers délimiteurs (virgule, tabulation, point-virgule, pipe, etc.), détecte automatiquement les types de données et formats d'encodage, prenant en charge l'analyse rapide de gros fichiers et structures de données complexes."
    step3: "Générez des fichiers de format CSV standard avec prise en charge des délimiteurs personnalisés, styles de guillemets, formats d'encodage, et paramètres de marque BOM. Assure une compatibilité parfaite avec les systèmes cibles, fournissant des options de téléchargement et compression pour répondre aux besoins de traitement de données de niveau entreprise."
    from_alias: "Fichier de Données CSV"
    to_alias: "Format CSV Standard"
  JSON:
    alias: "Tableau JSON"
    what: "JSON (JavaScript Object Notation) est le format de données de tableau standard pour les applications web modernes, les API REST et les architectures de microservices. Sa structure claire et son analyse efficace le rendent largement utilisé dans l'interaction de données front-end et back-end, le stockage de fichiers de configuration et les bases de données NoSQL. Prend en charge les objets imbriqués, les structures de tableau et les types de données multiples, en faisant des données de tableau indispensables pour le développement logiciel moderne."
    step1: "Téléchargez des fichiers JSON ou collez des tableaux JSON. Prend en charge la reconnaissance automatique et l'analyse de tableaux d'objets, structures imbriquées et types de données complexes. L'outil valide intelligemment la syntaxe JSON et fournit des invites d'erreur."
    step3: "Générez plusieurs sorties de format JSON : tableaux d'objets standard, tableaux 2D, tableaux de colonnes et formats de paires clé-valeur. Prend en charge la sortie embellie, le mode de compression, les noms d'objets racine personnalisés et les paramètres d'indentation, s'adaptant parfaitement à diverses interfaces API et besoins de stockage de données."
    from_alias: "Fichier de Tableau JSON"
    to_alias: "Format JSON Standard"
  JSONLines:
    alias: "Format JSONLines"
    what: "JSON Lines (également connu sous le nom de NDJSON) est un format important pour le traitement de big data et la transmission de données en streaming, avec chaque ligne contenant un objet JSON indépendant. Largement utilisé dans l'analyse de logs, le traitement de flux de données, l'apprentissage automatique et les systèmes distribués. Prend en charge le traitement incrémental et le calcul parallèle, en faisant le choix idéal pour gérer des données structurées à grande échelle."
    step1: "Téléchargez des fichiers JSONLines ou collez des données. L'outil analyse les objets JSON ligne par ligne, prenant en charge le traitement de streaming de gros fichiers et la fonctionnalité de saut de lignes d'erreur."
    step3: "Générez un format JSONLines standard avec chaque ligne produisant un objet JSON complet. Adapté pour le traitement de streaming, l'importation par lots et les scénarios d'analyse de big data, prenant en charge la validation de données et l'optimisation de format."
    from_alias: "Données JSONLines"
    to_alias: "Format de Streaming JSONLines"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) est le format standard pour l'échange de données et la gestion de configuration au niveau entreprise, avec des spécifications de syntaxe strictes et des mécanismes de validation puissants. Largement utilisé dans les services web, les fichiers de configuration, le stockage de documents et l'intégration de systèmes. Prend en charge les espaces de noms, la validation de schéma et la transformation XSLT, en faisant des données de tableau importantes pour les applications d'entreprise."
    step1: "Téléchargez des fichiers XML ou collez des données XML. L'outil analyse automatiquement la structure XML et la convertit en format de tableau, prenant en charge l'espace de noms, la gestion d'attributs et les structures imbriquées complexes."
    step3: "Générez une sortie XML conforme aux standards XML. Prend en charge les éléments racine personnalisés, les noms d'éléments de ligne, les modes d'attributs, l'enveloppement CDATA et les paramètres d'encodage de caractères. Assure l'intégrité et la compatibilité des données, répondant aux exigences d'applications de niveau entreprise."
    from_alias: "Fichier de Données XML"
    to_alias: "Format XML Standard"
  YAML:
    alias: "Configuration YAML"
    what: "YAML est un standard de sérialisation de données convivial pour les humains, réputé pour sa structure hiérarchique claire et sa syntaxe concise. Largement utilisé dans les fichiers de configuration, les chaînes d'outils DevOps, Docker Compose et le déploiement Kubernetes. Sa forte lisibilité et sa syntaxe concise en font un format de configuration important pour les applications cloud-natives modernes et les opérations automatisées."
    step1: "Téléchargez des fichiers YAML ou collez des données YAML. L'outil analyse intelligemment la structure YAML et valide la correction de syntaxe, prenant en charge les formats multi-documents et les types de données complexes."
    step3: "Générez une sortie de format YAML standard avec prise en charge des styles de tableau bloc et flux, des paramètres de guillemets multiples, de l'indentation personnalisée et de la préservation de commentaires. Assure que les fichiers YAML de sortie sont entièrement compatibles avec divers analyseurs et systèmes de configuration."
    from_alias: "Fichier de Configuration YAML"
    to_alias: "Format YAML Standard"
  MySQL:
      alias: "Résultats de Requête MySQL"
      what: "MySQL est le système de gestion de base de données relationnelle open-source le plus populaire au monde, réputé pour ses hautes performances, sa fiabilité et sa facilité d'utilisation. Largement utilisé dans les applications web, les systèmes d'entreprise et les plateformes d'analyse de données. Les résultats de requête MySQL contiennent généralement des données de tableau structurées, servant de source de données importante dans la gestion de base de données et le travail d'analyse de données."
      step1: "Collez les résultats de sortie de requête MySQL dans la zone source de données. L'outil reconnaît automatiquement et analyse le format de sortie en ligne de commande MySQL, prenant en charge divers styles de résultats de requête et encodages de caractères, gérant intelligemment les en-têtes et les lignes de données."
      step3: "Convertissez rapidement les résultats de requête MySQL en plusieurs formats de données de tableau, facilitant l'analyse de données, la génération de rapports, la migration de données inter-systèmes et la validation de données. Un outil pratique pour les administrateurs de base de données et les analystes de données."
      from_alias: "Sortie de Requête MySQL"
      to_alias: "Données de Tableau MySQL"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) est le langage d'opération standard pour les bases de données relationnelles, utilisé pour les opérations de requête, insertion, mise à jour et suppression de données. En tant que technologie centrale de la gestion de base de données, SQL est largement utilisé dans l'analyse de données, l'intelligence d'affaires, le traitement ETL et la construction d'entrepôts de données. C'est un outil de compétence essentiel pour les professionnels des données."
    step1: "Collez les instructions INSERT SQL ou téléchargez des fichiers .sql. L'outil analyse intelligemment la syntaxe SQL et extrait les données de tableau, prenant en charge plusieurs dialectes SQL et le traitement d'instructions de requête complexes."
    step3: "Générez des instructions INSERT SQL standard et des instructions de création de tableau. Prend en charge plusieurs dialectes de base de données (MySQL, PostgreSQL, SQLite, SQL Server, Oracle), gère automatiquement le mappage de types de données, l'échappement de caractères et les contraintes de clé primaire. Assure que le code SQL généré peut être exécuté directement."
    from_alias: "Fichier de Données SQL"
    to_alias: "Instruction SQL Standard"
  Qlik:
      alias: "Tableau Qlik"
      what: "Qlik est un fournisseur de logiciels spécialisé dans la visualisation de données, les tableaux de bord exécutifs et les produits d'intelligence d'affaires en libre-service, aux côtés de Tableau et Microsoft."
      step1: ""
      step3: "Enfin, le [Générateur de Tableaux](#TableGenerator) affiche les résultats de conversion. Utilisez dans votre Qlik Sense, Qlik AutoML, QlikView, ou autre logiciel compatible Qlik."
      from_alias: "Tableau Qlik"
      to_alias: "Tableau Qlik"
  DAX:
      alias: "Tableau DAX"
      what: "DAX (Data Analysis Expressions) est un langage de programmation utilisé dans Microsoft Power BI pour créer des colonnes calculées, des mesures et des tableaux personnalisés."
      step1: ""
      step3: "Enfin, le [Générateur de Tableaux](#TableGenerator) affiche les résultats de conversion. Comme attendu, il est utilisé dans plusieurs produits Microsoft incluant Microsoft Power BI, Microsoft Analysis Services et Microsoft Power Pivot pour Excel."
      from_alias: "Tableau DAX"
      to_alias: "Tableau DAX"
  Firebase:
    alias: "Liste Firebase"
    what: "Firebase est une plateforme de développement d'applications BaaS qui fournit des services backend hébergés tels que base de données en temps réel, stockage cloud, authentification, rapport de plantage, etc."
    step1: ""
    step3: "Enfin, le [Générateur de Tableaux](#TableGenerator) affiche les résultats de conversion. Vous pouvez ensuite utiliser la méthode push dans l'API Firebase pour ajouter à une liste de données dans la base de données Firebase."
    from_alias: "Liste Firebase"
    to_alias: "Liste Firebase"
  HTML:
    alias: "Tableau HTML"
    what: "Les tableaux HTML sont la méthode standard pour afficher des données structurées dans les pages web, construits avec les balises table, tr, td et autres. Prend en charge la personnalisation de style riche, la mise en page responsive et la fonctionnalité interactive. Largement utilisé dans le développement de sites web, l'affichage de données et la génération de rapports, servant de composant important du développement front-end et de la conception web."
    step1: "Collez le code HTML contenant des tableaux ou téléchargez des fichiers HTML. L'outil reconnaît automatiquement et extrait les données de tableau des pages, prenant en charge les structures HTML complexes, les styles CSS et le traitement de tableaux imbriqués."
    step3: "Générez du code de tableau HTML sémantique avec prise en charge de la structure thead/tbody, des paramètres de classe CSS, des légendes de tableau, des en-têtes de ligne/colonne et de la configuration d'attributs responsive. Assure que le code de tableau généré respecte les standards web avec une bonne accessibilité et convivialité SEO."
    from_alias: "Tableau Web HTML"
    to_alias: "Tableau HTML Standard"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel est le logiciel de tableur le plus populaire au monde, largement utilisé dans l'analyse d'affaires, la gestion financière, le traitement de données et la création de rapports. Ses puissantes capacités de traitement de données, sa riche bibliothèque de fonctions et ses fonctionnalités de visualisation flexibles en font l'outil standard pour l'automatisation de bureau et l'analyse de données, avec des applications étendues dans presque toutes les industries et domaines."
    step1: "Téléchargez des fichiers Excel (prend en charge les formats .xlsx, .xls) ou copiez directement les données de tableau d'Excel et collez. L'outil prend en charge le traitement multi-feuilles, la reconnaissance de format complexe et l'analyse rapide de gros fichiers, gérant automatiquement les cellules fusionnées et les types de données."
    step3: "Générez des données de tableau compatibles Excel qui peuvent être directement collées dans Excel ou téléchargées comme fichiers .xlsx standard. Prend en charge la dénomination de feuilles de calcul, le formatage de cellules, la largeur de colonne automatique, le style d'en-tête et les paramètres de validation de données. Assure que les fichiers Excel de sortie ont une apparence professionnelle et une fonctionnalité complète."
    from_alias: "Feuille de Calcul Excel"
    to_alias: "Format Excel Standard"
  LaTeX:
    alias: "Tableau LaTeX"
    what: "LaTeX est un système professionnel de composition de documents, particulièrement adapté pour créer des articles académiques, des documents techniques et des publications scientifiques. Sa fonctionnalité de tableau est puissante, prenant en charge les formules mathématiques complexes, le contrôle de mise en page précis et la sortie PDF de haute qualité. C'est l'outil standard en académie et en publication scientifique, largement utilisé dans les articles de journaux, les thèses et la composition de manuels techniques."
    step1: "Collez le code de tableau LaTeX ou téléchargez des fichiers .tex. L'outil analyse la syntaxe de tableau LaTeX et extrait le contenu des données, prenant en charge plusieurs environnements de tableau (tabular, longtable, array, etc.) et les commandes de format complexes."
    step3: "Générez du code de tableau LaTeX professionnel avec prise en charge de la sélection d'environnements de tableau multiples, de la configuration de style de bordure, des paramètres de position de légende, de la spécification de classe de document et de la gestion de paquets. Peut générer des documents LaTeX compilables complets, assurant que les tableaux de sortie respectent les standards de publication académique."
    from_alias: "Tableau de Document LaTeX"
    to_alias: "Format Professionnel LaTeX"
  ASCII:
    alias: "Tableau ASCII"
    what: "Les tableaux ASCII utilisent des caractères de texte brut pour dessiner les bordures et structures de tableau, offrant la meilleure compatibilité et portabilité. Compatible avec tous les éditeurs de texte, environnements de terminal et systèmes d'exploitation. Largement utilisé dans la documentation de code, les manuels techniques, les fichiers README et la sortie d'outils en ligne de commande. Le format d'affichage de données préféré pour les programmeurs et administrateurs système."
    step1: "Téléchargez des fichiers texte contenant des tableaux ASCII ou collez directement les données de tableau. L'outil reconnaît et analyse intelligemment les structures de tableau ASCII, prenant en charge plusieurs styles de bordure et formats d'alignement."
    step3: "Générez de beaux tableaux ASCII en texte brut avec prise en charge de plusieurs styles de bordure (ligne simple, ligne double, coins arrondis, etc.), méthodes d'alignement de texte et largeur de colonne automatique. Les tableaux générés s'affichent parfaitement dans les éditeurs de code, documents et lignes de commande."
    from_alias: "Tableau Texte ASCII"
    to_alias: "Format Standard ASCII"
  MediaWiki:
    alias: "Tableau MediaWiki"
    what: "MediaWiki est la plateforme logicielle open-source utilisée par des sites wiki célèbres comme Wikipedia. Sa syntaxe de tableau est concise mais puissante, prenant en charge la personnalisation de style de tableau, la fonctionnalité de tri et l'intégration de liens. Largement utilisé dans la gestion des connaissances, l'édition collaborative et les systèmes de gestion de contenu, servant de technologie centrale pour construire des encyclopédies wiki et des bases de connaissances."
    step1: "Collez le code de tableau MediaWiki ou téléchargez des fichiers source wiki. L'outil analyse la syntaxe de balisage wiki et extrait les données de tableau, prenant en charge la syntaxe wiki complexe et le traitement de modèles."
    step3: "Générez du code de tableau MediaWiki standard avec prise en charge des paramètres de style d'en-tête, de l'alignement de cellules, de l'activation de la fonctionnalité de tri et des options de compression de code. Le code généré peut être directement utilisé pour l'édition de pages wiki, assurant un affichage parfait sur les plateformes MediaWiki."
    from_alias: "Code Source MediaWiki"
    to_alias: "Syntaxe de Tableau MediaWiki"
  TracWiki:
    alias: "Tableau TracWiki"
    what: "Trac est un système de gestion de projet et de suivi de bogues basé sur le web qui utilise une syntaxe wiki simplifiée pour créer du contenu de tableau."
    step1: "Téléchargez des fichiers TracWiki ou collez des données de tableau."
    step3: "Générez du code de tableau compatible TracWiki avec prise en charge des paramètres d'en-tête de ligne/colonne, facilitant la gestion de documents de projet."
    from_alias: "Tableau TracWiki"
    to_alias: "Format TracWiki"
  AsciiDoc:
    alias: "Tableau AsciiDoc"
    what: "AsciiDoc est un langage de balisage léger qui peut être converti en HTML, PDF, pages de manuel et autres formats, largement utilisé pour l'écriture de documentation technique."
    step1: "Téléchargez des fichiers AsciiDoc ou collez des données."
    step3: "Générez la syntaxe de tableau AsciiDoc avec prise en charge des paramètres d'en-tête, de pied de page et de titre, directement utilisable dans les éditeurs AsciiDoc."
    from_alias: "Tableau AsciiDoc"
    to_alias: "Format AsciiDoc"
  reStructuredText:
    alias: "Tableau reStructuredText"
    what: "reStructuredText est le format de documentation standard pour la communauté Python, prenant en charge une syntaxe de tableau riche, couramment utilisé pour la génération de documentation Sphinx."
    step1: "Téléchargez des fichiers .rst ou collez des données reStructuredText."
    step3: "Générez des tableaux reStructuredText standard avec prise en charge de plusieurs styles de bordure, directement utilisables dans les projets de documentation Sphinx."
    from_alias: "Tableau reStructuredText"
    to_alias: "Format reStructuredText"
  PHP:
    alias: "Tableau PHP"
    what: "PHP est un langage de script côté serveur populaire, avec les tableaux étant sa structure de données centrale, largement utilisé dans le développement web et le traitement de données."
    step1: "Téléchargez des fichiers contenant des tableaux PHP ou collez directement les données."
    step3: "Générez du code de tableau PHP standard qui peut être directement utilisé dans les projets PHP, prenant en charge les formats de tableau associatif et indexé."
    from_alias: "Tableau PHP"
    to_alias: "Code PHP"
  Ruby:
    alias: "Tableau Ruby"
    what: "Ruby est un langage de programmation orienté objet dynamique avec une syntaxe concise et élégante, avec les tableaux étant une structure de données importante."
    step1: "Téléchargez des fichiers Ruby ou collez des données de tableau."
    step3: "Générez du code de tableau Ruby qui respecte les spécifications de syntaxe Ruby, directement utilisable dans les projets Ruby."
    from_alias: "Tableau Ruby"
    to_alias: "Code Ruby"
  ASP:
    alias: "Tableau ASP"
    what: "ASP (Active Server Pages) est l'environnement de script côté serveur de Microsoft, prenant en charge plusieurs langages de programmation pour développer des pages web dynamiques."
    step1: "Téléchargez des fichiers ASP ou collez des données de tableau."
    step3: "Générez du code de tableau compatible ASP avec prise en charge de la syntaxe VBScript et JScript, utilisable dans les projets ASP.NET."
    from_alias: "Tableau ASP"
    to_alias: "Code ASP"
  ActionScript:
    alias: "Tableau ActionScript"
    what: "ActionScript est un langage de programmation orienté objet principalement utilisé pour le développement d'applications Adobe Flash et AIR."
    step1: "Téléchargez des fichiers .as ou collez des données ActionScript."
    step3: "Générez du code de tableau ActionScript qui respecte les standards de syntaxe AS3, utilisable pour le développement de projets Flash et Flex."
    from_alias: "Tableau ActionScript"
    to_alias: "Code ActionScript"
  BBCode:
    alias: "Tableau BBCode"
    what: "BBCode est un langage de balisage léger couramment utilisé dans les forums et communautés en ligne, fournissant une fonctionnalité de formatage simple incluant la prise en charge de tableaux."
    step1: "Téléchargez des fichiers contenant du BBCode ou collez des données."
    step3: "Générez du code de tableau BBCode adapté pour les publications de forum et la création de contenu communautaire, avec prise en charge du format de sortie compressé."
    from_alias: "Tableau BBCode"
    to_alias: "Format BBCode"
  PDF:
    alias: "Tableau PDF"
    what: "PDF (Portable Document Format) est un standard de document multiplateforme avec mise en page fixe, affichage cohérent et caractéristiques d'impression de haute qualité. Largement utilisé dans les documents formels, rapports, factures, contrats et articles académiques. Le format préféré pour la communication d'affaires et l'archivage de documents, assurant des effets visuels complètement cohérents sur différents appareils et systèmes d'exploitation."
    step1: "Importez des données de tableau dans n'importe quel format. L'outil analyse automatiquement la structure des données et effectue une conception de mise en page intelligente, prenant en charge la pagination automatique de grandes tables et le traitement de types de données complexes."
    step3: "Générez des fichiers de tableau PDF de haute qualité avec prise en charge de plusieurs styles de thème professionnel (affaires, académique, minimaliste, etc.), polices multilingues, pagination automatique, ajout de filigrane et optimisation d'impression. Assure que les documents PDF de sortie ont une apparence professionnelle, directement utilisables pour les présentations d'affaires et la publication formelle."
    from_alias: "Données de Tableau"
    to_alias: "Document PDF Professionnel"
  JPEG:
    alias: "Image JPEG"
    what: "JPEG est le format d'image numérique le plus largement utilisé avec d'excellents effets de compression et une large compatibilité. Sa petite taille de fichier et sa vitesse de chargement rapide le rendent adapté pour l'affichage web, le partage sur les réseaux sociaux, les illustrations de documents et les présentations en ligne. Le format d'image standard pour les médias numériques et la communication réseau, parfaitement pris en charge par presque tous les appareils et logiciels."
    step1: "Importez des données de tableau dans n'importe quel format. L'outil effectue une conception de mise en page intelligente et une optimisation visuelle, calculant automatiquement la taille et la résolution optimales."
    step3: "Générez des images de tableau JPEG haute définition avec prise en charge de plusieurs schémas de couleurs de thème (clair, sombre, convivial pour les yeux, etc.), mise en page adaptative, optimisation de la clarté du texte et personnalisation de la taille. Adapté pour le partage en ligne, l'insertion de documents et l'utilisation de présentation, assurant d'excellents effets visuels sur divers appareils d'affichage."
    from_alias: "Données de Tableau"
    to_alias: "Image JPEG Haute Définition"
  Jira:
    alias: "Tableau Jira"
    what: "JIRA est un logiciel professionnel de gestion de projet et de suivi de bogues développé par Atlassian, largement utilisé dans le développement agile, les tests de logiciels et la collaboration de projet. Sa fonctionnalité de tableau prend en charge des options de formatage riches et l'affichage de données, servant d'outil important pour les équipes de développement logiciel, les gestionnaires de projet et le personnel d'assurance qualité dans la gestion des exigences, le suivi de bogues et le rapport de progrès."
    step1: "Téléchargez des fichiers contenant des données de tableau ou collez directement le contenu des données. L'outil traite automatiquement les données de tableau et l'échappement de caractères spéciaux."
    step3: "Générez du code de tableau compatible avec la plateforme JIRA avec prise en charge des paramètres de style d'en-tête, de l'alignement de cellules, du traitement d'échappement de caractères et de l'optimisation de format. Le code généré peut être directement collé dans les descriptions d'issues JIRA, commentaires ou pages wiki, assurant un affichage et un rendu corrects dans les systèmes JIRA."
    from_alias: "Données de Projet"
    to_alias: "Syntaxe de Tableau Jira"
  Textile:
    alias: "Tableau Textile"
    what: "Textile est un langage de balisage léger concis avec une syntaxe simple et facile à apprendre, largement utilisé dans les systèmes de gestion de contenu, plateformes de blog et systèmes de forum. Sa syntaxe de tableau est claire et intuitive, prenant en charge le formatage rapide et les paramètres de style. Un outil idéal pour les créateurs de contenu et administrateurs de sites web pour l'écriture rapide de documents et la publication de contenu."
    step1: "Téléchargez des fichiers de format Textile ou collez des données de tableau. L'outil analyse la syntaxe de balisage Textile et extrait le contenu de tableau."
    step3: "Générez la syntaxe de tableau Textile standard avec prise en charge du balisage d'en-tête, de l'alignement de cellules, de l'échappement de caractères spéciaux et de l'optimisation de format. Le code généré peut être directement utilisé dans les systèmes CMS, plateformes de blog et systèmes de documents qui prennent en charge Textile, assurant un rendu et un affichage corrects du contenu."
    from_alias: "Document Textile"
    to_alias: "Syntaxe de Tableau Textile"
  PNG:
    alias: "Image PNG"
    what: "PNG (Portable Network Graphics) est un format d'image sans perte avec excellente compression et prise en charge de la transparence. Largement utilisé dans la conception web, les graphiques numériques et la photographie professionnelle. Sa haute qualité et sa large compatibilité le rendent idéal pour les captures d'écran, logos, diagrammes et toute image nécessitant des détails nets et des arrière-plans transparents."
    step1: "Importez des données de tableau dans n'importe quel format. L'outil effectue une conception de mise en page intelligente et une optimisation visuelle, calculant automatiquement la taille et la résolution optimales pour la sortie PNG."
    step3: "Générez des images de tableau PNG de haute qualité avec prise en charge de plusieurs schémas de couleurs de thème, arrière-plans transparents, mise en page adaptative et optimisation de la clarté du texte. Parfait pour l'utilisation web, l'insertion de documents et les présentations professionnelles avec une excellente qualité visuelle."
    from_alias: "Données de Tableau"
    to_alias: "Image PNG Haute Qualité"
  TOML:
    alias: "Configuration TOML"
    what: "TOML (Tom's Obvious, Minimal Language) est un format de fichier de configuration facile à lire et à écrire. Conçu pour être non ambigu et simple, il est largement utilisé dans les projets logiciels modernes pour la gestion de configuration. Sa syntaxe claire et son typage fort en font un excellent choix pour les paramètres d'application et les fichiers de configuration de projet."
    step1: "Téléchargez des fichiers TOML ou collez des données de configuration. L'outil analyse la syntaxe TOML et extrait les informations de configuration structurées."
    step3: "Générez le format TOML standard avec prise en charge des structures imbriquées, types de données et commentaires. Les fichiers TOML générés sont parfaits pour la configuration d'application, les outils de construction et les paramètres de projet."
    from_alias: "Configuration TOML"
    to_alias: "Format TOML"
  INI:
    alias: "Configuration INI"
    what: "Les fichiers INI sont des fichiers de configuration simples utilisés par de nombreuses applications et systèmes d'exploitation. Leur structure directe de paires clé-valeur les rend faciles à lire et à éditer manuellement. Largement utilisés dans les applications Windows, systèmes hérités et scénarios de configuration simples où la lisibilité humaine est importante."
    step1: "Téléchargez des fichiers INI ou collez des données de configuration. L'outil analyse la syntaxe INI et extrait les informations de configuration basées sur les sections."
    step3: "Générez le format INI standard avec prise en charge des sections, commentaires et divers types de données. Les fichiers INI générés sont compatibles avec la plupart des applications et systèmes de configuration."
    from_alias: "Configuration INI"
    to_alias: "Format INI"
  Avro:
    alias: "Schéma Avro"
    what: "Apache Avro est un système de sérialisation de données qui fournit des structures de données riches, un format binaire compact et des capacités d'évolution de schéma. Largement utilisé dans le traitement de big data, les files d'attente de messages et les systèmes distribués. Sa définition de schéma prend en charge les types de données complexes et la compatibilité de version, en faisant un outil important pour les ingénieurs de données et architectes système."
    step1: "Téléchargez des fichiers de schéma Avro ou collez des données. L'outil analyse les définitions de schéma Avro et extrait les informations de structure de tableau."
    step3: "Générez des définitions de schéma Avro standard avec prise en charge du mappage de types de données, contraintes de champ et validation de schéma. Les schémas générés peuvent être directement utilisés dans les écosystèmes Hadoop, systèmes de messages Kafka et autres plateformes de big data."
    from_alias: "Schéma Avro"
    to_alias: "Format de Données Avro"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) est le mécanisme neutre au langage, neutre à la plateforme et extensible de Google pour sérialiser les données structurées. Largement utilisé dans les microservices, développement d'API et stockage de données. Son format binaire efficace et son typage fort le rendent idéal pour les applications haute performance et la communication inter-langages."
    step1: "Téléchargez des fichiers .proto ou collez des définitions Protocol Buffer. L'outil analyse la syntaxe protobuf et extrait les informations de structure de message."
    step3: "Générez des définitions Protocol Buffer standard avec prise en charge des types de message, options de champ et définitions de service. Les fichiers .proto générés peuvent être compilés pour plusieurs langages de programmation."
    from_alias: "Protocol Buffer"
    to_alias: "Schéma Protobuf"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas est la bibliothèque d'analyse de données la plus populaire en Python, avec DataFrame étant sa structure de données centrale. Elle fournit de puissantes capacités de manipulation, nettoyage et analyse de données, largement utilisée en science des données, apprentissage automatique et intelligence d'affaires. Un outil indispensable pour les développeurs Python et analystes de données."
    step1: "Téléchargez des fichiers Python contenant du code DataFrame ou collez des données. L'outil analyse la syntaxe Pandas et extrait les informations de structure DataFrame."
    step3: "Générez du code Pandas DataFrame standard avec prise en charge des spécifications de type de données, paramètres d'index et opérations de données. Le code généré peut être directement exécuté dans l'environnement Python pour l'analyse et le traitement de données."
    from_alias: "Pandas DataFrame"
    to_alias: "Structure de Données Python"
  RDF:
    alias: "Triple RDF"
    what: "RDF (Resource Description Framework) est un modèle standard pour l'échange de données sur le Web, conçu pour représenter les informations sur les ressources sous forme de graphe. Largement utilisé dans le web sémantique, graphes de connaissances et applications de données liées. Sa structure de triple permet une représentation riche de métadonnées et des relations sémantiques."
    step1: "Téléchargez des fichiers RDF ou collez des données de triple. L'outil analyse la syntaxe RDF et extrait les relations sémantiques et informations de ressources."
    step3: "Générez le format RDF standard avec prise en charge de diverses sérialisations (RDF/XML, Turtle, N-Triples). Le RDF généré peut être utilisé dans les applications web sémantique, bases de connaissances et systèmes de données liées."
    from_alias: "Données RDF"
    to_alias: "Format Sémantique RDF"
  MATLAB:
    alias: "Tableau MATLAB"
    what: "MATLAB est un logiciel de calcul numérique et de visualisation haute performance largement utilisé dans le calcul d'ingénierie, l'analyse de données et le développement d'algorithmes. Ses opérations de tableau et matrice sont puissantes, prenant en charge les calculs mathématiques complexes et le traitement de données. Un outil essentiel pour les ingénieurs, chercheurs et scientifiques de données."
    step1: "Téléchargez des fichiers MATLAB .m ou collez des données de tableau. L'outil analyse la syntaxe MATLAB et extrait les informations de structure de tableau."
    step3: "Générez du code de tableau MATLAB standard avec prise en charge des tableaux multidimensionnels, spécifications de type de données et dénomination de variables. Le code généré peut être directement exécuté dans l'environnement MATLAB pour l'analyse de données et le calcul scientifique."
    from_alias: "Tableau MATLAB"
    to_alias: "Format de Code MATLAB"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame est la structure de données centrale du langage de programmation R, largement utilisée dans l'analyse statistique, l'exploration de données et l'apprentissage automatique. R est l'outil de premier plan pour le calcul statistique et les graphiques, avec DataFrame fournissant de puissantes capacités de manipulation de données, d'analyse statistique et de visualisation. Essentiel pour les scientifiques de données, statisticiens et chercheurs travaillant avec l'analyse de données structurées."
    step1: "Téléchargez des fichiers de données R ou collez du code DataFrame. L'outil analyse la syntaxe R et extrait les informations de structure DataFrame incluant les types de colonnes, noms de lignes et contenu de données."
    step3: "Générez du code R DataFrame standard avec prise en charge des spécifications de type de données, niveaux de facteurs, noms de ligne/colonne et structures de données spécifiques à R. Le code généré peut être directement exécuté dans l'environnement R pour l'analyse statistique et le traitement de données."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Commencer la Conversion"
  start_generating: "Commencer à générer"
  api_docs: "Documentation API"
related:
  section_title: 'Plus de Convertisseurs {{ if and .from (ne .from "generator") }}{{ .from }} et {{ end }}{{ .to }}'
  section_description: 'Explorez plus de convertisseurs pour les formats {{ if and .from (ne .from "generator") }}{{ .from }} et {{ end }}{{ .to }}. Transformez vos données entre plusieurs formats avec nos outils de conversion en ligne professionnels.'
  title: "{{ .from }} vers {{ .to }}"
howto:
  step2: "Modifiez les données en utilisant notre éditeur de tableau en ligne avancé avec des fonctionnalités professionnelles. Prend en charge la suppression des lignes vides, la suppression des doublons, la transposition des données, le tri, la recherche et remplacement regex, et l'aperçu en temps réel. Tous les changements se convertissent automatiquement au format %s avec des résultats précis et fiables."
  section_title: "Comment utiliser le {{ . }}"
  converter_description: "Apprenez à convertir {{ .from }} vers {{ .to }} avec notre guide étape par étape. Convertisseur en ligne professionnel avec des fonctionnalités avancées et aperçu en temps réel."
  generator_description: "Apprenez à créer des tableaux {{ .to }} professionnels avec notre générateur en ligne. Édition similaire à Excel, aperçu en temps réel, et capacités d'exportation instantanée."
extension:
  section_title: "Extension de Détection et Extraction de Tableaux"
  section_description: "Extrayez les tableaux de n'importe quel site web en un clic. Convertissez instantanément vers plus de 30 formats incluant Excel, CSV, JSON - aucun copier-coller requis."
  features:
    extraction_title: "Extraction de Tableaux en Un Clic"
    extraction_description: "Extrayez instantanément les tableaux de n'importe quelle page web sans copier-coller - extraction de données professionnelle simplifiée"
    formats_title: "Support de Plus de 30 Formats"
    formats_description: "Convertissez les tableaux extraits vers Excel, CSV, JSON, Markdown, SQL, et plus avec notre convertisseur de tableaux avancé"
    detection_title: "Détection Intelligente de Tableaux"
    detection_description: "Détecte et surligne automatiquement les tableaux sur n'importe quelle page web pour une extraction et conversion rapide des données"
  hover_tip: "✨ Survolez n'importe quel tableau pour voir l'icône d'extraction"
recommendations:
  section_title: "Recommandé par les Universités et Professionnels"
  section_description: "TableConvert est approuvé par les professionnels des universités, institutions de recherche, et équipes de développement pour une conversion de tableaux fiable et le traitement de données."
  cards:
    university_title: "Université du Wisconsin-Madison"
    university_description: "TableConvert.com - Outil professionnel gratuit en ligne de conversion de tableaux et formats de données"
    university_link: "Lire l'Article"
    facebook_title: "Communauté de Professionnels des Données"
    facebook_description: "Partagé et recommandé par les analystes de données et professionnels dans les groupes de développeurs Facebook"
    facebook_link: "Voir le Post"
    twitter_title: "Communauté de Développeurs"
    twitter_description: "Recommandé par @xiaoying_eth et autres développeurs sur X (Twitter) pour la conversion de tableaux"
    twitter_link: "Voir le Tweet"
faq:
  section_title: "Questions Fréquemment Posées"
  section_description: "Questions courantes sur notre convertisseur de tableaux en ligne gratuit, formats de données, et processus de conversion."
  what: "Qu'est-ce que le format %s ?"
  howto_convert:
    question: "Comment utiliser le {{ . }} gratuitement ?"
    answer: "Téléchargez votre fichier {{ .from }}, collez des données, ou extrayez de pages web en utilisant notre convertisseur de tableaux en ligne gratuit. Notre outil de conversion professionnel transforme instantanément vos données au format {{ .to }} avec aperçu en temps réel et fonctionnalités d'édition avancées. Téléchargez ou copiez le résultat converti immédiatement."
  security:
    question: "Mes données sont-elles sécurisées lors de l'utilisation de ce convertisseur en ligne ?"
    answer: "Absolument ! Toutes les conversions de tableaux se déroulent localement dans votre navigateur - vos données ne quittent jamais votre appareil. Notre convertisseur en ligne traite tout côté client, assurant une confidentialité complète et la sécurité des données. Aucun fichier n'est stocké sur nos serveurs."
  free:
    question: "TableConvert est-il vraiment gratuit à utiliser ?"
    answer: "Oui, TableConvert est complètement gratuit ! Toutes les fonctionnalités du convertisseur, éditeur de tableaux, outils générateurs de données, et options d'exportation sont disponibles sans coût, inscription, ou frais cachés. Convertissez des fichiers illimités en ligne gratuitement."
  filesize:
    question: "Quelles sont les limites de taille de fichier du convertisseur en ligne ?"
    answer: "Notre convertisseur de tableaux en ligne gratuit supporte les fichiers jusqu'à 10MB. Pour des fichiers plus volumineux, traitement par lots, ou besoins d'entreprise, utilisez notre extension de navigateur ou service API professionnel avec des limites plus élevées."
stats:
  conversions: "Tableaux Convertis"
  tables: "Tableaux Générés"
  formats: "Formats de Fichiers de Données"
  rating: "Évaluation Utilisateur"
