site:
  fullname: "온라인 테이블 변환"
  name: "TableConvert"
  subtitle: "무료 온라인 테이블 변환기 및 생성기"
  intro: "TableConvert는 Excel, CSV, JSON, Markdown, LaTeX, SQL 등 30개 이상의 형식 간 변환을 지원하는 무료 온라인 테이블 변환 및 데이터 생성 도구입니다."
  followTwitter: "X에서 팔로우하기"
title:
  converter: "%s에서 %s로"
  generator: "%s 생성기"
post:
  tags:
    converter: "변환기"
    editor: "편집기"
    generator: "생성기"
    maker: "빌더"
  converter:
    title: "%s에서 %s로 온라인 변환"
    short: "무료이고 강력한 %s에서 %s로의 온라인 도구"
    intro: "사용하기 쉬운 온라인 %s에서 %s로 변환기. 직관적인 변환 도구로 테이블 데이터를 손쉽게 변환하세요. 빠르고 안정적이며 사용자 친화적입니다."
  generator:
    title: "온라인 %s 편집기 및 생성기"
    short: "포괄적인 기능을 갖춘 전문 %s 온라인 생성 도구"
    intro: "사용하기 쉬운 온라인 %s 생성기 및 테이블 편집기. 직관적인 도구와 실시간 미리보기로 전문적인 데이터 테이블을 손쉽게 만드세요."
navbar:
  search:
    placeholder: "변환기 검색..."
  sponsor: "커피 한 잔 사주기"
  extension: "확장 프로그램"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "데이터 소스"
    placeholder: "%s 데이터를 붙여넣거나 %s 파일을 여기로 드래그하세요"
    example: "예시"
    upload: "파일 업로드"
    extract:
      enter: "웹페이지에서 추출"
      intro: "테이블 데이터가 포함된 웹페이지 URL을 입력하여 구조화된 데이터를 자동으로 추출합니다"
      btn: "%s 추출"
    excel:
      sheet: "워크시트"
      none: "없음"
  tableEditor:
    title: "온라인 테이블 편집기"
    undo: "실행 취소"
    redo: "다시 실행"
    transpose: "전치"
    clear: "지우기"
    deleteBlank: "빈 항목 삭제"
    deleteDuplicate: "중복 제거"
    uppercase: "대문자"
    lowercase: "소문자"
    capitalize: "첫 글자 대문자"
    replace:
      replace: "찾기 및 바꾸기 (정규식 지원)"
      subst: "다음으로 바꾸기..."
      btn: "모두 바꾸기"
  tableGenerator:
    title: "테이블 생성기"
    sponsor: "커피 한 잔 사주기"
    copy: "클립보드에 복사"
    download: "파일 다운로드"
    tooltip:
      html:
        escape: "HTML 특수 문자(&, <, >, \", ')를 이스케이프하여 표시 오류 방지"
        div: "기존 TABLE 태그 대신 DIV+CSS 레이아웃 사용, 반응형 디자인에 더 적합"
        minify: "공백과 줄바꿈을 제거하여 압축된 HTML 코드 생성"
        thead: "표준 테이블 헤드(&lt;thead&gt;)와 바디(&lt;tbody&gt;) 구조 생성"
        tableCaption: "테이블 위에 설명 제목 추가(&lt;caption&gt; 요소)"
        tableClass: "쉬운 스타일 커스터마이징을 위해 테이블에 CSS 클래스명 추가"
        tableId: "JavaScript 조작을 위해 테이블에 고유 ID 식별자 설정"
      jira:
        escape: "Jira 테이블 구문과의 충돌을 피하기 위해 파이프 문자(|) 이스케이프"
      json:
        parsingJSON: "셀의 JSON 문자열을 객체로 지능적으로 파싱"
        minify: "파일 크기를 줄이기 위해 압축된 단일 라인 JSON 형식 생성"
        format: "출력 JSON 데이터 구조 선택: 객체 배열, 2D 배열 등"
      latex:
        escape: "적절한 컴파일을 위해 LaTeX 특수 문자(%, &, _, #, $ 등) 이스케이프"
        ht: "페이지에서 테이블 위치를 제어하기 위해 플로팅 위치 매개변수 [!ht] 추가"
        mwe: "완전한 LaTeX 문서 생성"
        tableAlign: "페이지에서 테이블의 수평 정렬 설정"
        tableBorder: "테이블 테두리 스타일 구성: 테두리 없음, 부분 테두리, 전체 테두리"
        label: "\\ref{} 명령 상호 참조를 위한 테이블 라벨 설정"
        caption: "테이블 위 또는 아래에 표시할 테이블 캡션 설정"
        location: "테이블 캡션 표시 위치 선택: 위 또는 아래"
        tableType: "테이블 환경 유형 선택: tabular, longtable, array 등"
      markdown:
        escape: "형식 충돌을 피하기 위해 Markdown 특수 문자(*, _, |, \\ 등) 이스케이프"
        pretty: "더 아름다운 테이블 형식을 생성하기 위해 열 너비 자동 정렬"
        simple: "외부 테두리 세로선을 생략한 단순화된 구문 사용"
        boldFirstRow: "첫 번째 행 텍스트를 굵게 만들기"
        boldFirstColumn: "첫 번째 열 텍스트를 굵게 만들기"
        firstHeader: "첫 번째 행을 헤더로 처리하고 구분선 추가"
        textAlign: "열 텍스트 정렬 설정: 왼쪽, 가운데, 오른쪽"
        multilineHandling: "다중 라인 텍스트 처리: 줄바꿈 보존, \\n으로 이스케이프, &lt;br&gt; 태그 사용"

        includeLineNumbers: "테이블 왼쪽에 행 번호 열 추가"
      magic:
        builtin: "미리 정의된 공통 템플릿 형식 선택"
        rowsTpl: "<table> <tr> <th>매직 구문</th> <th>설명</th> <th>지원 JS 메서드</th> </tr> <tr> <td>{h1} {h2} ...</td> <td><b>헤</b>더의 1번째, 2번째 ... 필드, 별칭 {hA} {hB} ...</td> <td>문자열 메서드</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>현재 행의 1번째, 2번째 ... 필드, 별칭 {$A} {$B} ...</td> <td>문자열 메서드</td> </tr> <tr> <td>{F,} {F;}</td> <td><b>F</b> 뒤의 문자열로 현재 행 분할</td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>현재 <b>행</b>의 행 <b>번</b>호를 1 또는 100부터</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> 행의 <b>끝</b> 행 <b>번</b>호 </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>JavaScript 코드 <b>실</b>행, 예: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> 백슬래시 <b>\\</b>를 사용하여 중괄호 {...} 출력 </td> <td></td> </tr></table>"
        headerTpl: "헤더 섹션용 사용자 정의 출력 템플릿"
        footerTpl: "푸터 섹션용 사용자 정의 출력 템플릿"
      textile:
        escape: "형식 충돌을 피하기 위해 Textile 구문 문자(|, ., -, ^) 이스케이프"
        rowHeader: "첫 번째 행을 헤더 행으로 설정"
        thead: "테이블 헤드와 바디용 Textile 구문 마커 추가"
      xml:
        escape: "유효한 XML을 보장하기 위해 XML 특수 문자(&lt;, &gt;, &amp;, \", ') 이스케이프"
        minify: "추가 공백을 제거하여 압축된 XML 출력 생성"
        rootElement: "XML 루트 요소 태그명 설정"
        rowElement: "각 데이터 행의 XML 요소 태그명 설정"
        declaration: "XML 선언 헤더 추가(&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "자식 요소 대신 XML 속성으로 데이터 출력"
        cdata: "특수 문자를 보호하기 위해 텍스트 내용을 CDATA로 감싸기"
        encoding: "XML 문서의 문자 인코딩 형식 설정"
        indentation: "XML 들여쓰기 문자 선택: 공백 또는 탭"
      yaml:
        indentSize: "YAML 계층 들여쓰기 공백 수 설정(보통 2 또는 4)"
        arrayStyle: "배열 형식: 블록(한 줄에 하나씩) 또는 플로우(인라인 형식)"
        quotationStyle: "문자열 인용 스타일: 인용부호 없음, 단일 인용부호, 이중 인용부호"
      pdf:
        theme: "전문 문서용 PDF 표 시각적 스타일 선택"
        headerColor: "PDF 표 헤더 배경색 선택"
        showHead: "PDF 페이지에서 헤더 표시 제어"
        docTitle: "PDF 문서의 선택적 제목"
        docDescription: "PDF 문서의 선택적 설명 텍스트"
      csv:
        bom: "Excel 및 기타 소프트웨어가 인코딩을 인식하도록 UTF-8 바이트 순서 표시 추가"
      excel:
        autoWidth: "내용에 따라 열 너비 자동 조정"
        protectSheet: "비밀번호로 워크시트 보호 활성화: tableconvert.com"
      sql:
        primaryKey: "CREATE TABLE 문의 기본 키 필드명 지정"
        dialect: "인용부호와 데이터 타입 구문에 영향을 주는 데이터베이스 타입 선택"
      ascii:
        forceSep: "각 데이터 행 사이에 구분선 강제"
        style: "ASCII 테이블 테두리 그리기 스타일 선택"
        comment: "전체 테이블을 감싸는 주석 마커 추가"
      mediawiki:
        minify: "추가 공백을 제거하여 출력 코드 압축"
        header: "첫 번째 행을 헤더 스타일로 표시"
        sort: "테이블 클릭 정렬 기능 활성화"
      asciidoc:
        minify: "AsciiDoc 형식 출력 압축"
        firstHeader: "첫 번째 행을 헤더 행으로 설정"
        lastFooter: "마지막 행을 푸터 행으로 설정"
        title: "테이블에 제목 텍스트 추가"
      tracwiki:
        rowHeader: "첫 번째 행을 헤더로 설정"
        colHeader: "첫 번째 열을 헤더로 설정"
      bbcode:
        minify: "BBCode 출력 형식 압축"
      restructuredtext:
        style: "reStructuredText 테이블 테두리 스타일 선택"
        forceSep: "구분선 강제"
    label:
      ascii:
        forceSep: "행 구분자"
        style: "테두리 스타일"
        comment: "주석 래퍼"
      restructuredtext:
        style: "테두리 스타일"
        forceSep: "구분자 강제"
      bbcode:
        minify: "출력 압축"
      csv:
        doubleQuote: "이중 따옴표 래핑"
        delimiter: "필드 구분자"
        bom: "UTF-8 BOM"
        valueDelimiter: "값 구분자"
        rowDelimiter: "행 구분자"
        prefix: "행 접두사"
        suffix: "행 접미사"
      excel:
        autoWidth: "자동 너비"
        textFormat: "텍스트 형식"
        protectSheet: "시트 보호"
        boldFirstRow: "첫 번째 행 굵게"
        boldFirstColumn: "첫 번째 열 굵게"
        sheetName: "시트 이름"
      html:
        escape: "HTML 문자 이스케이프"
        div: "DIV 테이블"
        minify: "코드 압축"
        thead: "테이블 헤드 구조"
        tableCaption: "테이블 캡션"
        tableClass: "테이블 클래스"
        tableId: "테이블 ID"
        rowHeader: "행 헤더"
        colHeader: "열 헤더"
      jira:
        escape: "문자 이스케이프"
        rowHeader: "행 헤더"
        colHeader: "열 헤더"
      json:
        parsingJSON: "JSON 파싱"
        minify: "출력 압축"
        format: "데이터 형식"
        rootName: "루트 객체 이름"
        indentSize: "들여쓰기 크기"
      jsonlines:
        parsingJSON: "JSON 파싱"
        format: "데이터 형식"
      latex:
        escape: "LaTeX 테이블 문자 이스케이프"
        ht: "플로트 위치"
        mwe: "완전한 문서"
        tableAlign: "테이블 정렬"
        tableBorder: "테두리 스타일"
        label: "참조 라벨"
        caption: "테이블 캡션"
        location: "캡션 위치"
        tableType: "테이블 타입"
        boldFirstRow: "첫 번째 행 굵게"
        boldFirstColumn: "첫 번째 열 굵게"
        textAlign: "텍스트 정렬"
        borders: "테두리 설정"
      markdown:
        escape: "문자 이스케이프"
        pretty: "예쁜 Markdown 테이블"
        simple: "간단한 Markdown 형식"
        boldFirstRow: "첫 번째 행 굵게"
        boldFirstColumn: "첫 번째 열 굵게"
        firstHeader: "첫 번째 헤더"
        textAlign: "텍스트 정렬"
        multilineHandling: "다중 라인 처리"

        includeLineNumbers: "행 번호 추가"
        align: "정렬"
      mediawiki:
        minify: "코드 압축"
        header: "헤더 마크업"
        sort: "정렬 가능"
      asciidoc:
        minify: "형식 압축"
        firstHeader: "첫 번째 헤더"
        lastFooter: "마지막 푸터"
        title: "테이블 제목"
      tracwiki:
        rowHeader: "행 헤더"
        colHeader: "열 헤더"
      sql:
        drop: "테이블 삭제 (존재하는 경우)"
        create: "테이블 생성"
        oneInsert: "배치 삽입"
        table: "테이블 이름"
        dialect: "데이터베이스 타입"
        primaryKey: "기본 키"
      magic:
        builtin: "내장 템플릿"
        rowsTpl: "행 템플릿, 구문 ->"
        headerTpl: "헤더 템플릿"
        footerTpl: "푸터 템플릿"
      textile:
        escape: "문자 이스케이프"
        rowHeader: "행 헤더"
        thead: "테이블 헤드 구문"
      xml:
        escape: "XML 문자 이스케이프"
        minify: "출력 압축"
        rootElement: "루트 요소"
        rowElement: "행 요소"
        declaration: "XML 선언"
        attributes: "속성 모드"
        cdata: "CDATA 래퍼"
        encoding: "인코딩"
        indentSize: "들여쓰기 크기"
      yaml:
        indentSize: "들여쓰기 크기"
        arrayStyle: "배열 스타일"
        quotationStyle: "인용 스타일"
      pdf:
        theme: "PDF 표 테마"
        headerColor: "PDF 헤더 색상"
        showHead: "PDF 헤더 표시"
        docTitle: "PDF 문서 제목"
        docDescription: "PDF 문서 설명"
sidebar:
  all: "모든 변환 도구"
  dataSource:
    title: "데이터 소스"
    description:
      converter: "%s를 %s로 변환하기 위해 가져오기. 파일 업로드, 온라인 편집, 웹 데이터 추출을 지원합니다."
      generator: "수동 입력, 파일 가져오기, 템플릿 생성을 포함한 여러 입력 방법을 지원하여 테이블 데이터를 생성합니다."
  tableEditor:
    title: "온라인 테이블 편집기"
    description:
      converter: "테이블 편집기를 사용하여 %s를 온라인으로 처리합니다. 빈 행 삭제, 중복 제거, 정렬, 찾기 및 바꾸기를 지원하는 Excel과 같은 작업 경험."
      generator: "Excel과 같은 작업 경험을 제공하는 강력한 온라인 테이블 편집기. 빈 행 삭제, 중복 제거, 정렬, 찾기 및 바꾸기를 지원합니다."
  tableGenerator:
    title: "테이블 생성기"
    description:
      converter: "테이블 생성기의 실시간 미리보기로 %s를 빠르게 생성합니다. 풍부한 내보내기 옵션, 원클릭 복사 및 다운로드."
      generator: "다양한 사용 시나리오를 충족하기 위해 %s 데이터를 여러 형식으로 내보냅니다. 사용자 정의 옵션과 실시간 미리보기를 지원합니다."
footer:
  changelog: "변경 로그"
  sponsor: "후원자"
  contact: "문의하기"
  privacyPolicy: "개인정보 보호정책"
  about: "소개"
  resources: "리소스"
  popularConverters: "인기 변환기"
  popularGenerators: "인기 생성기"
  dataSecurity: "데이터가 안전합니다 - 모든 변환이 브라우저에서 실행됩니다."
converters:
  Markdown:
    alias: "Markdown 테이블"
    what: "Markdown은 기술 문서, 블로그 콘텐츠 작성, 웹 개발에 널리 사용되는 경량 마크업 언어입니다. 테이블 구문이 간결하고 직관적이며, 텍스트 정렬, 링크 임베딩, 포맷팅을 지원합니다. 프로그래머와 기술 작가들이 선호하는 도구로, GitHub, GitLab 및 기타 코드 호스팅 플랫폼과 완벽하게 호환됩니다."
    step1: "Markdown 테이블 데이터를 데이터 소스 영역에 붙여넣거나 .md 파일을 직접 드래그 앤 드롭으로 업로드하세요. 도구가 자동으로 테이블 구조와 포맷팅을 파싱하며, 복잡한 중첩 콘텐츠와 특수 문자 처리를 지원합니다."
    step3: "실시간으로 표준 Markdown 테이블 코드를 생성하며, 다양한 정렬 방법, 텍스트 굵게 표시, 행 번호 추가 및 기타 고급 포맷 설정을 지원합니다. 생성된 코드는 GitHub 및 주요 Markdown 에디터와 완전히 호환되며, 원클릭 복사로 바로 사용할 수 있습니다."
    from_alias: "Markdown 테이블 파일"
    to_alias: "Markdown 테이블 형식"
  Magic:
    alias: "사용자 정의 템플릿"
    what: "Magic 템플릿은 이 도구의 고유한 고급 데이터 생성기로, 사용자가 사용자 정의 템플릿 구문을 통해 임의 형식의 데이터 출력을 생성할 수 있게 합니다. 변수 치환, 조건 판단, 루프 처리를 지원합니다. 복잡한 데이터 변환 요구사항과 개인화된 출력 형식을 처리하는 궁극적인 솔루션으로, 특히 개발자와 데이터 엔지니어에게 적합합니다."
    step1: "내장된 공통 템플릿을 선택하거나 사용자 정의 템플릿 구문을 생성하세요. 복잡한 데이터 구조와 비즈니스 로직을 처리할 수 있는 풍부한 변수와 함수를 지원합니다."
    step3: "사용자 정의 형식 요구사항을 완전히 충족하는 데이터 출력을 생성합니다. 복잡한 데이터 변환 로직과 조건 처리를 지원하여 데이터 처리 효율성과 출력 품질을 크게 향상시킵니다. 배치 데이터 처리를 위한 강력한 도구입니다."
    from_alias: "테이블 데이터"
    to_alias: "사용자 정의 형식 출력"
  CSV:
    alias: "CSV"
    what: "CSV(Comma-Separated Values)는 가장 널리 사용되는 데이터 교환 형식으로, Excel, Google Sheets, 데이터베이스 시스템 및 다양한 데이터 분석 도구에서 완벽하게 지원됩니다. 간단한 구조와 강력한 호환성으로 데이터 마이그레이션, 배치 가져오기/내보내기, 크로스 플랫폼 데이터 교환의 표준 형식이 되었으며, 비즈니스 분석, 데이터 사이언스, 시스템 통합에서 널리 사용됩니다."
    step1: "CSV 파일을 업로드하거나 CSV 데이터를 직접 붙여넣으세요. 도구가 다양한 구분자(쉼표, 탭, 세미콜론, 파이프 등)를 지능적으로 인식하고, 데이터 타입과 인코딩 형식을 자동으로 감지하며, 대용량 파일과 복잡한 데이터 구조의 빠른 파싱을 지원합니다."
    step3: "사용자 정의 구분자, 인용 스타일, 인코딩 형식, BOM 마크 설정을 지원하는 표준 CSV 형식 파일을 생성합니다. 대상 시스템과의 완벽한 호환성을 보장하며, 기업급 데이터 처리 요구사항을 충족하는 다운로드 및 압축 옵션을 제공합니다."
    from_alias: "CSV 데이터 파일"
    to_alias: "CSV 표준 형식"
  JSON:
    alias: "JSON 배열"
    what: "JSON(JavaScript Object Notation)은 현대 웹 애플리케이션, REST API, 마이크로서비스 아키텍처의 표준 테이블 데이터 형식입니다. 명확한 구조와 효율적인 파싱으로 프론트엔드와 백엔드 데이터 상호작용, 설정 파일 저장, NoSQL 데이터베이스에서 널리 사용됩니다. 중첩된 객체, 배열 구조, 다양한 데이터 타입을 지원하여 현대 소프트웨어 개발에 필수적인 테이블 데이터가 되었습니다."
    step1: "JSON 파일을 업로드하거나 JSON 배열을 붙여넣으세요. 객체 배열, 중첩 구조, 복잡한 데이터 타입의 자동 인식과 파싱을 지원합니다. 도구가 JSON 구문을 지능적으로 검증하고 오류 프롬프트를 제공합니다."
    step3: "다양한 JSON 형식 출력을 생성합니다: 표준 객체 배열, 2D 배열, 열 배열, 키-값 쌍 형식. 미화된 출력, 압축 모드, 사용자 정의 루트 객체 이름, 들여쓰기 설정을 지원하여 다양한 API 인터페이스와 데이터 저장 요구사항에 완벽하게 적응합니다."
    from_alias: "JSON 배열 파일"
    to_alias: "JSON 표준 형식"
  JSONLines:
    alias: "JSONLines 형식"
    what: "JSON Lines(NDJSON이라고도 함)는 빅데이터 처리와 스트리밍 데이터 전송의 중요한 형식으로, 각 줄에 독립적인 JSON 객체가 포함됩니다. 로그 분석, 데이터 스트림 처리, 머신러닝, 분산 시스템에서 널리 사용됩니다. 증분 처리와 병렬 컴퓨팅을 지원하여 대규모 구조화 데이터 처리의 이상적인 선택입니다."
    step1: "JSONLines 파일을 업로드하거나 데이터를 붙여넣으세요. 도구가 JSON 객체를 줄별로 파싱하며, 대용량 파일 스트리밍 처리와 오류 줄 건너뛰기 기능을 지원합니다."
    step3: "각 줄이 완전한 JSON 객체를 출력하는 표준 JSONLines 형식을 생성합니다. 스트리밍 처리, 배치 가져오기, 빅데이터 분석 시나리오에 적합하며, 데이터 검증과 형식 최적화를 지원합니다."
    from_alias: "JSONLines 데이터"
    to_alias: "JSONLines 스트리밍 형식"
  XML:
    alias: "XML"
    what: "XML(eXtensible Markup Language)은 엄격한 구문 사양과 강력한 검증 메커니즘을 갖춘 기업급 데이터 교환 및 설정 관리의 표준 형식입니다. 웹 서비스, 설정 파일, 문서 저장, 시스템 통합에서 널리 사용됩니다. 네임스페이스, 스키마 검증, XSLT 변환을 지원하여 기업 애플리케이션의 중요한 테이블 데이터가 됩니다."
    step1: "XML 파일을 업로드하거나 XML 데이터를 붙여넣으세요. 도구가 자동으로 XML 구조를 파싱하여 테이블 형식으로 변환하며, 네임스페이스, 속성 처리, 복잡한 중첩 구조를 지원합니다."
    step3: "XML 표준을 준수하는 XML 출력을 생성합니다. 사용자 정의 루트 요소, 행 요소 이름, 속성 모드, CDATA 래핑, 문자 인코딩 설정을 지원합니다. 데이터 무결성과 호환성을 보장하여 기업급 애플리케이션 요구사항을 충족합니다."
    from_alias: "XML 데이터 파일"
    to_alias: "XML 표준 형식"
  YAML:
    alias: "YAML 설정"
    what: "YAML은 명확한 계층 구조와 간결한 구문으로 유명한 인간 친화적인 데이터 직렬화 표준입니다. 설정 파일, DevOps 도구 체인, Docker Compose, Kubernetes 배포에서 널리 사용됩니다. 강력한 가독성과 간결한 구문으로 현대 클라우드 네이티브 애플리케이션과 자동화 운영의 중요한 설정 형식이 되었습니다."
    step1: "YAML 파일을 업로드하거나 YAML 데이터를 붙여넣으세요. 도구가 YAML 구조를 지능적으로 파싱하고 구문 정확성을 검증하며, 다중 문서 형식과 복잡한 데이터 타입을 지원합니다."
    step3: "블록 및 플로우 배열 스타일, 다양한 인용 설정, 사용자 정의 들여쓰기, 주석 보존을 지원하는 표준 YAML 형식 출력을 생성합니다. 출력 YAML 파일이 다양한 파서와 설정 시스템과 완전히 호환되도록 보장합니다."
    from_alias: "YAML 설정 파일"
    to_alias: "YAML 표준 형식"
  MySQL:
      alias: "MySQL 쿼리 결과"
      what: "MySQL은 세계에서 가장 인기 있는 오픈소스 관계형 데이터베이스 관리 시스템으로, 높은 성능, 신뢰성, 사용 편의성으로 유명합니다. 웹 애플리케이션, 기업 시스템, 데이터 분석 플랫폼에서 널리 사용됩니다. MySQL 쿼리 결과는 일반적으로 구조화된 테이블 데이터를 포함하며, 데이터베이스 관리 및 데이터 분석 작업에서 중요한 데이터 소스 역할을 합니다."
      step1: "MySQL 쿼리 출력 결과를 데이터 소스 영역에 붙여넣으세요. 도구가 MySQL 명령줄 출력 형식을 자동으로 인식하고 파싱하며, 다양한 쿼리 결과 스타일과 문자 인코딩을 지원하고, 헤더와 데이터 행을 지능적으로 처리합니다."
      step3: "MySQL 쿼리 결과를 여러 테이블 데이터 형식으로 빠르게 변환하여 데이터 분석, 보고서 생성, 시스템 간 데이터 마이그레이션, 데이터 검증을 용이하게 합니다. 데이터베이스 관리자와 데이터 분석가를 위한 실용적인 도구입니다."
      from_alias: "MySQL 쿼리 출력"
      to_alias: "MySQL 테이블 데이터"
  SQL:
    alias: "Insert SQL"
    what: "SQL(Structured Query Language)은 관계형 데이터베이스의 표준 조작 언어로, 데이터 쿼리, 삽입, 업데이트, 삭제 작업에 사용됩니다. 데이터베이스 관리의 핵심 기술로서 SQL은 데이터 분석, 비즈니스 인텔리전스, ETL 처리, 데이터 웨어하우스 구축에서 널리 사용됩니다. 데이터 전문가들에게 필수적인 기술 도구입니다."
    step1: "INSERT SQL 문을 붙여넣거나 .sql 파일을 업로드하세요. 도구가 SQL 구문을 지능적으로 파싱하고 테이블 데이터를 추출하며, 여러 SQL 방언과 복잡한 쿼리 문 처리를 지원합니다."
    step3: "표준 SQL INSERT 문과 테이블 생성 문을 생성합니다. 여러 데이터베이스 방언(MySQL, PostgreSQL, SQLite, SQL Server, Oracle)을 지원하고, 데이터 타입 매핑, 문자 이스케이프, 기본 키 제약 조건을 자동으로 처리합니다. 생성된 SQL 코드가 직접 실행될 수 있도록 보장합니다."
    from_alias: "SQL 데이터 파일"
    to_alias: "SQL 표준 문"
  Qlik:
      alias: "Qlik 테이블"
      what: "Qlik은 Tableau 및 Microsoft와 함께 데이터 시각화, 경영진 대시보드, 셀프서비스 비즈니스 인텔리전스 제품을 전문으로 하는 소프트웨어 공급업체입니다."
      step1: ""
      step3: "마지막으로 [테이블 생성기](#TableGenerator)가 변환 결과를 보여줍니다. Qlik Sense, Qlik AutoML, QlikView 또는 기타 Qlik 지원 소프트웨어에서 사용하세요."
      from_alias: "Qlik 테이블"
      to_alias: "Qlik 테이블"
  DAX:
      alias: "DAX 테이블"
      what: "DAX(Data Analysis Expressions)는 계산된 열, 측정값, 사용자 정의 테이블을 만들기 위해 Microsoft Power BI 전반에서 사용되는 프로그래밍 언어입니다."
      step1: ""
      step3: "마지막으로 [테이블 생성기](#TableGenerator)가 변환 결과를 보여줍니다. 예상대로 Microsoft Power BI, Microsoft Analysis Services, Microsoft Power Pivot for Excel을 포함한 여러 Microsoft 제품에서 사용됩니다."
      from_alias: "DAX 테이블"
      to_alias: "DAX 테이블"
  Firebase:
    alias: "Firebase 목록"
    what: "Firebase는 실시간 데이터베이스, 클라우드 스토리지, 인증, 크래시 보고 등과 같은 호스팅된 백엔드 서비스를 제공하는 BaaS 애플리케이션 개발 플랫폼입니다."
    step1: ""
    step3: "마지막으로 [테이블 생성기](#TableGenerator)가 변환 결과를 보여줍니다. 그런 다음 Firebase API의 push 메서드를 사용하여 Firebase 데이터베이스의 데이터 목록에 추가할 수 있습니다."
    from_alias: "Firebase 목록"
    to_alias: "Firebase 목록"
  HTML:
    alias: "HTML 테이블"
    what: "HTML 테이블은 웹 페이지에서 구조화된 데이터를 표시하는 표준 방법으로, table, tr, td 및 기타 태그로 구축됩니다. 풍부한 스타일 사용자 정의, 반응형 레이아웃, 대화형 기능을 지원합니다. 웹사이트 개발, 데이터 표시, 보고서 생성에서 널리 사용되며, 프론트엔드 개발과 웹 디자인의 중요한 구성 요소 역할을 합니다."
    step1: "테이블이 포함된 HTML 코드를 붙여넣거나 HTML 파일을 업로드하세요. 도구가 페이지에서 테이블 데이터를 자동으로 인식하고 추출하며, 복잡한 HTML 구조, CSS 스타일, 중첩 테이블 처리를 지원합니다."
    step3: "thead/tbody 구조, CSS 클래스 설정, 테이블 캡션, 행/열 헤더, 반응형 속성 구성을 지원하는 시맨틱 HTML 테이블 코드를 생성합니다. 생성된 테이블 코드가 좋은 접근성과 SEO 친화성을 갖춘 웹 표준을 충족하도록 보장합니다."
    from_alias: "HTML 웹 테이블"
    to_alias: "HTML 표준 테이블"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel은 세계에서 가장 인기 있는 스프레드시트 소프트웨어로, 비즈니스 분석, 재무 관리, 데이터 처리, 보고서 작성에서 널리 사용됩니다. 강력한 데이터 처리 기능, 풍부한 함수 라이브러리, 유연한 시각화 기능으로 사무 자동화와 데이터 분석의 표준 도구가 되었으며, 거의 모든 산업과 분야에서 광범위하게 응용됩니다."
    step1: "Excel 파일을 업로드하거나(.xlsx, .xls 형식 지원) Excel에서 테이블 데이터를 직접 복사하여 붙여넣으세요. 도구는 다중 워크시트 처리, 복잡한 형식 인식, 대용량 파일의 빠른 파싱을 지원하며, 병합된 셀과 데이터 타입을 자동으로 처리합니다."
    step3: "Excel에 직접 붙여넣거나 표준 .xlsx 파일로 다운로드할 수 있는 Excel 호환 테이블 데이터를 생성합니다. 워크시트 명명, 셀 서식, 자동 열 너비, 헤더 스타일링, 데이터 유효성 검사 설정을 지원합니다. 출력 Excel 파일이 전문적인 외관과 완전한 기능을 갖도록 보장합니다."
    from_alias: "Excel 스프레드시트"
    to_alias: "Excel 표준 형식"
  LaTeX:
    alias: "LaTeX 테이블"
    what: "LaTeX는 학술 논문, 기술 문서, 과학 출판물 작성에 특히 적합한 전문 문서 조판 시스템입니다. 테이블 기능이 강력하여 복잡한 수학 공식, 정밀한 레이아웃 제어, 고품질 PDF 출력을 지원합니다. 학계와 과학 출판의 표준 도구로, 저널 논문, 학위논문, 기술 매뉴얼 조판에서 널리 사용됩니다."
    step1: "LaTeX 테이블 코드를 붙여넣거나 .tex 파일을 업로드하세요. 도구가 LaTeX 테이블 구문을 파싱하고 데이터 내용을 추출하며, 여러 테이블 환경(tabular, longtable, array 등)과 복잡한 형식 명령을 지원합니다."
    step3: "여러 테이블 환경 선택, 테두리 스타일 구성, 캡션 위치 설정, 문서 클래스 지정, 패키지 관리를 지원하는 전문 LaTeX 테이블 코드를 생성합니다. 완전한 컴파일 가능한 LaTeX 문서를 생성할 수 있으며, 출력 테이블이 학술 출판 표준을 충족하도록 보장합니다."
    from_alias: "LaTeX 문서 테이블"
    to_alias: "LaTeX 전문 형식"
  ASCII:
    alias: "ASCII 테이블"
    what: "ASCII 테이블은 일반 텍스트 문자를 사용하여 테이블 테두리와 구조를 그려 최고의 호환성과 이식성을 제공합니다. 모든 텍스트 편집기, 터미널 환경, 운영 체제와 호환됩니다. 코드 문서화, 기술 매뉴얼, README 파일, 명령줄 도구 출력에서 널리 사용됩니다. 프로그래머와 시스템 관리자가 선호하는 데이터 표시 형식입니다."
    step1: "ASCII 테이블이 포함된 텍스트 파일을 업로드하거나 테이블 데이터를 직접 붙여넣으세요. 도구가 ASCII 테이블 구조를 지능적으로 인식하고 파싱하며, 여러 테두리 스타일과 정렬 형식을 지원합니다."
    step3: "여러 테두리 스타일(단일선, 이중선, 둥근 모서리 등), 텍스트 정렬 방법, 자동 열 너비를 지원하는 아름다운 일반 텍스트 ASCII 테이블을 생성합니다. 생성된 테이블은 코드 편집기, 문서, 명령줄에서 완벽하게 표시됩니다."
    from_alias: "ASCII 텍스트 테이블"
    to_alias: "ASCII 표준 형식"
  MediaWiki:
    alias: "MediaWiki 테이블"
    what: "MediaWiki는 Wikipedia와 같은 유명한 위키 사이트에서 사용되는 오픈소스 소프트웨어 플랫폼입니다. 테이블 구문이 간결하면서도 강력하여 테이블 스타일 사용자 정의, 정렬 기능, 링크 임베딩을 지원합니다. 지식 관리, 협업 편집, 콘텐츠 관리 시스템에서 널리 사용되며, 위키 백과사전과 지식 베이스 구축의 핵심 기술 역할을 합니다."
    step1: "MediaWiki 테이블 코드를 붙여넣거나 위키 소스 파일을 업로드하세요. 도구가 위키 마크업 구문을 파싱하고 테이블 데이터를 추출하며, 복잡한 위키 구문과 템플릿 처리를 지원합니다."
    step3: "헤더 스타일 설정, 셀 정렬, 정렬 기능 활성화, 코드 압축 옵션을 지원하는 표준 MediaWiki 테이블 코드를 생성합니다. 생성된 코드는 위키 페이지 편집에 직접 사용할 수 있으며, MediaWiki 플랫폼에서 완벽한 표시를 보장합니다."
    from_alias: "MediaWiki 소스 코드"
    to_alias: "MediaWiki 테이블 구문"
  TracWiki:
    alias: "TracWiki 테이블"
    what: "Trac은 간소화된 위키 구문을 사용하여 테이블 콘텐츠를 만드는 웹 기반 프로젝트 관리 및 버그 추적 시스템입니다."
    step1: "TracWiki 파일을 업로드하거나 테이블 데이터를 붙여넣으세요."
    step3: "행/열 헤더 설정을 지원하는 TracWiki 호환 테이블 코드를 생성하여 프로젝트 문서 관리를 용이하게 합니다."
    from_alias: "TracWiki 테이블"
    to_alias: "TracWiki 형식"
  AsciiDoc:
    alias: "AsciiDoc 테이블"
    what: "AsciiDoc은 HTML, PDF, 매뉴얼 페이지 및 기타 형식으로 변환할 수 있는 경량 마크업 언어로, 기술 문서 작성에 널리 사용됩니다."
    step1: "AsciiDoc 파일을 업로드하거나 데이터를 붙여넣으세요."
    step3: "헤더, 푸터, 제목 설정을 지원하는 AsciiDoc 테이블 구문을 생성하여 AsciiDoc 편집기에서 직접 사용할 수 있습니다."
    from_alias: "AsciiDoc 테이블"
    to_alias: "AsciiDoc 형식"
  reStructuredText:
    alias: "reStructuredText 테이블"
    what: "reStructuredText는 Python 커뮤니티의 표준 문서화 형식으로, 풍부한 테이블 구문을 지원하며 Sphinx 문서 생성에 일반적으로 사용됩니다."
    step1: ".rst 파일을 업로드하거나 reStructuredText 데이터를 붙여넣으세요."
    step3: "여러 테두리 스타일을 지원하는 표준 reStructuredText 테이블을 생성하여 Sphinx 문서 프로젝트에서 직접 사용할 수 있습니다."
    from_alias: "reStructuredText 테이블"
    to_alias: "reStructuredText 형식"
  PHP:
    alias: "PHP 배열"
    what: "PHP는 인기 있는 서버 사이드 스크립팅 언어로, 배열이 핵심 데이터 구조이며 웹 개발과 데이터 처리에서 널리 사용됩니다."
    step1: "PHP 배열이 포함된 파일을 업로드하거나 데이터를 직접 붙여넣으세요."
    step3: "PHP 프로젝트에서 직접 사용할 수 있는 표준 PHP 배열 코드를 생성하며, 연관 배열과 인덱스 배열 형식을 지원합니다."
    from_alias: "PHP 배열"
    to_alias: "PHP 코드"
  Ruby:
    alias: "Ruby 배열"
    what: "Ruby는 간결하고 우아한 구문을 가진 동적 객체 지향 프로그래밍 언어로, 배열이 중요한 데이터 구조입니다."
    step1: "Ruby 파일을 업로드하거나 배열 데이터를 붙여넣으세요."
    step3: "Ruby 구문 사양을 준수하는 Ruby 배열 코드를 생성하여 Ruby 프로젝트에서 직접 사용할 수 있습니다."
    from_alias: "Ruby 배열"
    to_alias: "Ruby 코드"
  ASP:
    alias: "ASP 배열"
    what: "ASP(Active Server Pages)는 동적 웹 페이지 개발을 위해 여러 프로그래밍 언어를 지원하는 Microsoft의 서버 사이드 스크립팅 환경입니다."
    step1: "ASP 파일을 업로드하거나 배열 데이터를 붙여넣으세요."
    step3: "VBScript 및 JScript 구문을 지원하는 ASP 호환 배열 코드를 생성하여 ASP.NET 프로젝트에서 사용할 수 있습니다."
    from_alias: "ASP 배열"
    to_alias: "ASP 코드"
  ActionScript:
    alias: "ActionScript 배열"
    what: "ActionScript는 주로 Adobe Flash 및 AIR 애플리케이션 개발에 사용되는 객체 지향 프로그래밍 언어입니다."
    step1: ".as 파일을 업로드하거나 ActionScript 데이터를 붙여넣으세요."
    step3: "AS3 구문 표준을 준수하는 ActionScript 배열 코드를 생성하여 Flash 및 Flex 프로젝트 개발에 사용할 수 있습니다."
    from_alias: "ActionScript 배열"
    to_alias: "ActionScript 코드"
  BBCode:
    alias: "BBCode 테이블"
    what: "BBCode는 포럼과 온라인 커뮤니티에서 일반적으로 사용되는 경량 마크업 언어로, 테이블 지원을 포함한 간단한 서식 기능을 제공합니다."
    step1: "BBCode가 포함된 파일을 업로드하거나 데이터를 붙여넣으세요."
    step3: "포럼 게시물과 커뮤니티 콘텐츠 생성에 적합한 BBCode 테이블 코드를 생성하며, 압축된 출력 형식을 지원합니다."
    from_alias: "BBCode 테이블"
    to_alias: "BBCode 형식"
  PDF:
    alias: "PDF 테이블"
    what: "PDF(Portable Document Format)는 고정 레이아웃, 일관된 표시, 고품질 인쇄 특성을 가진 크로스 플랫폼 문서 표준입니다. 공식 문서, 보고서, 송장, 계약서, 학술 논문에서 널리 사용됩니다. 비즈니스 커뮤니케이션과 문서 보관을 위한 선호 형식으로, 다양한 장치와 운영 체제에서 완전히 일관된 시각적 효과를 보장합니다."
    step1: "모든 형식의 테이블 데이터를 가져오세요. 도구가 데이터 구조를 자동으로 분석하고 지능적인 레이아웃 디자인을 수행하며, 대형 테이블 자동 페이지 매김과 복잡한 데이터 타입 처리를 지원합니다."
    step3: "여러 전문 테마 스타일(비즈니스, 학술, 미니멀 등), 다국어 폰트, 자동 페이지 매김, 워터마크 추가, 인쇄 최적화를 지원하는 고품질 PDF 테이블 파일을 생성합니다. 출력 PDF 문서가 전문적인 외관을 갖도록 보장하여 비즈니스 프레젠테이션과 공식 출판에 직접 사용할 수 있습니다."
    from_alias: "테이블 데이터"
    to_alias: "PDF 전문 문서"
  JPEG:
    alias: "JPEG 이미지"
    what: "JPEG는 뛰어난 압축 효과와 광범위한 호환성을 가진 가장 널리 사용되는 디지털 이미지 형식입니다. 작은 파일 크기와 빠른 로딩 속도로 웹 표시, 소셜 미디어 공유, 문서 일러스트레이션, 온라인 프레젠테이션에 적합합니다. 디지털 미디어와 네트워크 통신의 표준 이미지 형식으로, 거의 모든 장치와 소프트웨어에서 완벽하게 지원됩니다."
    step1: "모든 형식의 테이블 데이터를 가져오세요. 도구가 지능적인 레이아웃 디자인과 시각적 최적화를 수행하며, 최적의 크기와 해상도를 자동으로 계산합니다."
    step3: "여러 테마 색상 구성표(밝음, 어두움, 눈에 친화적 등), 적응형 레이아웃, 텍스트 선명도 최적화, 크기 사용자 정의를 지원하는 고화질 JPEG 테이블 이미지를 생성합니다. 온라인 공유, 문서 삽입, 프레젠테이션 사용에 적합하며, 다양한 디스플레이 장치에서 뛰어난 시각적 효과를 보장합니다."
    from_alias: "테이블 데이터"
    to_alias: "JPEG 고화질 이미지"
  Jira:
    alias: "Jira 테이블"
    what: "JIRA는 Atlassian에서 개발한 전문 프로젝트 관리 및 버그 추적 소프트웨어로, 애자일 개발, 소프트웨어 테스팅, 프로젝트 협업에서 널리 사용됩니다. 테이블 기능은 풍부한 서식 옵션과 데이터 표시를 지원하며, 요구사항 관리, 버그 추적, 진행 상황 보고에서 소프트웨어 개발팀, 프로젝트 관리자, 품질 보증 담당자를 위한 중요한 도구 역할을 합니다."
    step1: "테이블 데이터가 포함된 파일을 업로드하거나 데이터 내용을 직접 붙여넣으세요. 도구가 테이블 데이터와 특수 문자 이스케이프를 자동으로 처리합니다."
    step3: "헤더 스타일 설정, 셀 정렬, 문자 이스케이프 처리, 형식 최적화를 지원하는 JIRA 플랫폼 호환 테이블 코드를 생성합니다. 생성된 코드는 JIRA 이슈 설명, 댓글 또는 위키 페이지에 직접 붙여넣을 수 있으며, JIRA 시스템에서 올바른 표시와 렌더링을 보장합니다."
    from_alias: "프로젝트 데이터"
    to_alias: "Jira 테이블 구문"
  Textile:
    alias: "Textile 테이블"
    what: "Textile은 간단하고 배우기 쉬운 구문을 가진 간결한 경량 마크업 언어로, 콘텐츠 관리 시스템, 블로그 플랫폼, 포럼 시스템에서 널리 사용됩니다. 테이블 구문이 명확하고 직관적이며, 빠른 서식과 스타일 설정을 지원합니다. 콘텐츠 제작자와 웹사이트 관리자가 빠른 문서 작성과 콘텐츠 게시를 위한 이상적인 도구입니다."
    step1: "Textile 형식 파일을 업로드하거나 테이블 데이터를 붙여넣으세요. 도구가 Textile 마크업 구문을 파싱하고 테이블 내용을 추출합니다."
    step3: "헤더 마크업, 셀 정렬, 특수 문자 이스케이프, 형식 최적화를 지원하는 표준 Textile 테이블 구문을 생성합니다. 생성된 코드는 Textile을 지원하는 CMS 시스템, 블로그 플랫폼, 문서 시스템에서 직접 사용할 수 있으며, 올바른 콘텐츠 렌더링과 표시를 보장합니다."
    from_alias: "Textile 문서"
    to_alias: "Textile 테이블 구문"
  PNG:
    alias: "PNG 이미지"
    what: "PNG(Portable Network Graphics)는 뛰어난 압축과 투명도 지원을 가진 무손실 이미지 형식입니다. 웹 디자인, 디지털 그래픽, 전문 사진에서 널리 사용됩니다. 높은 품질과 광범위한 호환성으로 스크린샷, 로고, 다이어그램, 선명한 세부 사항과 투명한 배경이 필요한 모든 이미지에 이상적입니다."
    step1: "모든 형식의 테이블 데이터를 가져오세요. 도구가 지능적인 레이아웃 디자인과 시각적 최적화를 수행하며, PNG 출력을 위한 최적의 크기와 해상도를 자동으로 계산합니다."
    step3: "여러 테마 색상 구성표, 투명한 배경, 적응형 레이아웃, 텍스트 선명도 최적화를 지원하는 고품질 PNG 테이블 이미지를 생성합니다. 뛰어난 시각적 품질로 웹 사용, 문서 삽입, 전문 프레젠테이션에 완벽합니다."
    from_alias: "테이블 데이터"
    to_alias: "PNG 고품질 이미지"
  TOML:
    alias: "TOML 설정"
    what: "TOML(Tom's Obvious, Minimal Language)은 읽고 쓰기 쉬운 설정 파일 형식입니다. 명확하고 간단하도록 설계되어 현대 소프트웨어 프로젝트의 설정 관리에 널리 사용됩니다. 명확한 구문과 강력한 타이핑으로 애플리케이션 설정과 프로젝트 설정 파일에 탁월한 선택입니다."
    step1: "TOML 파일을 업로드하거나 설정 데이터를 붙여넣으세요. 도구가 TOML 구문을 파싱하고 구조화된 설정 정보를 추출합니다."
    step3: "중첩 구조, 데이터 타입, 주석을 지원하는 표준 TOML 형식을 생성합니다. 생성된 TOML 파일은 애플리케이션 설정, 빌드 도구, 프로젝트 설정에 완벽합니다."
    from_alias: "TOML 설정"
    to_alias: "TOML 형식"
  INI:
    alias: "INI 설정"
    what: "INI 파일은 많은 애플리케이션과 운영 체제에서 사용되는 간단한 설정 파일입니다. 직관적인 키-값 쌍 구조로 수동으로 읽고 편집하기 쉽습니다. Windows 애플리케이션, 레거시 시스템, 인간 가독성이 중요한 간단한 설정 시나리오에서 널리 사용됩니다."
    step1: "INI 파일을 업로드하거나 설정 데이터를 붙여넣으세요. 도구가 INI 구문을 파싱하고 섹션 기반 설정 정보를 추출합니다."
    step3: "섹션, 주석, 다양한 데이터 타입을 지원하는 표준 INI 형식을 생성합니다. 생성된 INI 파일은 대부분의 애플리케이션과 설정 시스템과 호환됩니다."
    from_alias: "INI 설정"
    to_alias: "INI 형식"
  Avro:
    alias: "Avro 스키마"
    what: "Apache Avro는 풍부한 데이터 구조, 컴팩트한 바이너리 형식, 스키마 진화 기능을 제공하는 데이터 직렬화 시스템입니다. 빅데이터 처리, 메시지 큐, 분산 시스템에서 널리 사용됩니다. 스키마 정의는 복잡한 데이터 타입과 버전 호환성을 지원하여 데이터 엔지니어와 시스템 아키텍트에게 중요한 도구입니다."
    step1: "Avro 스키마 파일을 업로드하거나 데이터를 붙여넣으세요. 도구가 Avro 스키마 정의를 파싱하고 테이블 구조 정보를 추출합니다."
    step3: "데이터 타입 매핑, 필드 제약 조건, 스키마 검증을 지원하는 표준 Avro 스키마 정의를 생성합니다. 생성된 스키마는 Hadoop 생태계, Kafka 메시지 시스템, 기타 빅데이터 플랫폼에서 직접 사용할 수 있습니다."
    from_alias: "Avro 스키마"
    to_alias: "Avro 데이터 형식"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers(protobuf)는 구조화된 데이터를 직렬화하기 위한 Google의 언어 중립적, 플랫폼 중립적, 확장 가능한 메커니즘입니다. 마이크로서비스, API 개발, 데이터 저장에서 널리 사용됩니다. 효율적인 바이너리 형식과 강력한 타이핑으로 고성능 애플리케이션과 언어 간 통신에 이상적입니다."
    step1: ".proto 파일을 업로드하거나 Protocol Buffer 정의를 붙여넣으세요. 도구가 protobuf 구문을 파싱하고 메시지 구조 정보를 추출합니다."
    step3: "메시지 타입, 필드 옵션, 서비스 정의를 지원하는 표준 Protocol Buffer 정의를 생성합니다. 생성된 .proto 파일은 여러 프로그래밍 언어로 컴파일할 수 있습니다."
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf 스키마"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas는 Python에서 가장 인기 있는 데이터 분석 라이브러리로, DataFrame이 핵심 데이터 구조입니다. 강력한 데이터 조작, 정리, 분석 기능을 제공하며, 데이터 사이언스, 머신러닝, 비즈니스 인텔리전스에서 널리 사용됩니다. Python 개발자와 데이터 분석가에게 없어서는 안 될 도구입니다."
    step1: "DataFrame 코드가 포함된 Python 파일을 업로드하거나 데이터를 붙여넣으세요. 도구가 Pandas 구문을 파싱하고 DataFrame 구조 정보를 추출합니다."
    step3: "데이터 타입 사양, 인덱스 설정, 데이터 연산을 지원하는 표준 Pandas DataFrame 코드를 생성합니다. 생성된 코드는 데이터 분석과 처리를 위해 Python 환경에서 직접 실행할 수 있습니다."
    from_alias: "Pandas DataFrame"
    to_alias: "Python 데이터 구조"
  RDF:
    alias: "RDF 트리플"
    what: "RDF(Resource Description Framework)는 웹에서 데이터 교환을 위한 표준 모델로, 리소스에 대한 정보를 그래프 형태로 표현하도록 설계되었습니다. 시맨틱 웹, 지식 그래프, 연결된 데이터 애플리케이션에서 널리 사용됩니다. 트리플 구조는 풍부한 메타데이터 표현과 시맨틱 관계를 가능하게 합니다."
    step1: "RDF 파일을 업로드하거나 트리플 데이터를 붙여넣으세요. 도구가 RDF 구문을 파싱하고 시맨틱 관계와 리소스 정보를 추출합니다."
    step3: "다양한 직렬화(RDF/XML, Turtle, N-Triples)를 지원하는 표준 RDF 형식을 생성합니다. 생성된 RDF는 시맨틱 웹 애플리케이션, 지식 베이스, 연결된 데이터 시스템에서 사용할 수 있습니다."
    from_alias: "RDF 데이터"
    to_alias: "RDF 시맨틱 형식"
  MATLAB:
    alias: "MATLAB 배열"
    what: "MATLAB은 엔지니어링 컴퓨팅, 데이터 분석, 알고리즘 개발에서 널리 사용되는 고성능 수치 컴퓨팅 및 시각화 소프트웨어입니다. 배열과 행렬 연산이 강력하여 복잡한 수학적 계산과 데이터 처리를 지원합니다. 엔지니어, 연구자, 데이터 사이언티스트에게 필수적인 도구입니다."
    step1: "MATLAB .m 파일을 업로드하거나 배열 데이터를 붙여넣으세요. 도구가 MATLAB 구문을 파싱하고 배열 구조 정보를 추출합니다."
    step3: "다차원 배열, 데이터 타입 사양, 변수 명명을 지원하는 표준 MATLAB 배열 코드를 생성합니다. 생성된 코드는 데이터 분석과 과학적 컴퓨팅을 위해 MATLAB 환경에서 직접 실행할 수 있습니다."
    from_alias: "MATLAB 배열"
    to_alias: "MATLAB 코드 형식"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame은 통계 분석, 데이터 마이닝, 머신러닝에서 널리 사용되는 R 프로그래밍 언어의 핵심 데이터 구조입니다. R은 통계 컴퓨팅과 그래픽스의 최고 도구로, DataFrame은 강력한 데이터 조작, 통계 분석, 시각화 기능을 제공합니다. 구조화된 데이터 분석을 다루는 데이터 사이언티스트, 통계학자, 연구자에게 필수적입니다."
    step1: "R 데이터 파일을 업로드하거나 DataFrame 코드를 붙여넣으세요. 도구가 R 구문을 파싱하고 열 타입, 행 이름, 데이터 내용을 포함한 DataFrame 구조 정보를 추출합니다."
    step3: "데이터 타입 사양, 인수 레벨, 행/열 이름, R 특화 데이터 구조를 지원하는 표준 R DataFrame 코드를 생성합니다. 생성된 코드는 통계 분석과 데이터 처리를 위해 R 환경에서 직접 실행할 수 있습니다."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "변환 시작"
  start_generating: "생성 시작"
  api_docs: "API 문서"
related:
  section_title: '더 많은 {{ if and .from (ne .from "generator") }}{{ .from }} 및 {{ end }}{{ .to }} 변환기'
  section_description: '{{ if and .from (ne .from "generator") }}{{ .from }} 및 {{ end }}{{ .to }} 형식을 위한 더 많은 변환기를 탐색하세요. 전문 온라인 변환 도구로 여러 형식 간에 데이터를 변환하세요.'
  title: "{{ .from }}에서 {{ .to }}로"
howto:
  step2: "전문 기능을 갖춘 고급 온라인 테이블 편집기를 사용하여 데이터를 편집하세요. 빈 행 삭제, 중복 제거, 데이터 전치, 정렬, 정규식 찾기 및 바꾸기, 실시간 미리보기를 지원합니다. 모든 변경 사항이 정확하고 신뢰할 수 있는 결과로 %s 형식으로 자동 변환됩니다."
  section_title: "{{ . }} 사용 방법"
  converter_description: "단계별 가이드로 {{ .from }}을 {{ .to }}로 변환하는 방법을 배우세요. 고급 기능과 실시간 미리보기를 갖춘 전문 온라인 변환기."
  generator_description: "온라인 생성기로 전문적인 {{ .to }} 테이블을 만드는 방법을 배우세요. Excel과 같은 편집, 실시간 미리보기, 즉시 내보내기 기능."
extension:
  section_title: "테이블 감지 및 추출 확장 프로그램"
  section_description: "한 번의 클릭으로 모든 웹사이트에서 테이블을 추출하세요. Excel, CSV, JSON을 포함한 30개 이상의 형식으로 즉시 변환 - 복사 붙여넣기 불필요."
  features:
    extraction_title: "원클릭 테이블 추출"
    extraction_description: "복사 붙여넣기 없이 모든 웹페이지에서 즉시 테이블 추출 - 전문 데이터 추출이 간단해집니다"
    formats_title: "30개 이상 형식 변환기 지원"
    formats_description: "고급 테이블 변환기로 추출된 테이블을 Excel, CSV, JSON, Markdown, SQL 등으로 변환"
    detection_title: "스마트 테이블 감지"
    detection_description: "빠른 데이터 추출 및 변환을 위해 모든 웹페이지에서 테이블을 자동으로 감지하고 강조 표시"
  hover_tip: "✨ 테이블 위에 마우스를 올려 추출 아이콘 보기"
recommendations:
  section_title: "대학 및 전문가 추천"
  section_description: "TableConvert는 신뢰할 수 있는 테이블 변환 및 데이터 처리를 위해 대학, 연구 기관, 개발 팀의 전문가들에게 신뢰받고 있습니다."
  cards:
    university_title: "위스콘신 대학교 매디슨 캠퍼스"
    university_description: "TableConvert.com - 전문 무료 온라인 테이블 변환기 및 데이터 형식 도구"
    university_link: "기사 읽기"
    facebook_title: "데이터 전문가 커뮤니티"
    facebook_description: "Facebook 개발자 그룹의 데이터 분석가 및 전문가들이 공유하고 추천"
    facebook_link: "게시물 보기"
    twitter_title: "개발자 커뮤니티"
    twitter_description: "테이블 변환을 위해 X(Twitter)에서 @xiaoying_eth 및 다른 개발자들이 추천"
    twitter_link: "트윗 보기"
faq:
  section_title: "자주 묻는 질문"
  section_description: "무료 온라인 테이블 변환기, 데이터 형식 및 변환 과정에 대한 일반적인 질문."
  what: "%s 형식이란 무엇인가요?"
  howto_convert:
    question: "{{ . }}을 무료로 사용하는 방법은?"
    answer: "무료 온라인 테이블 변환기를 사용하여 {{ .from }} 파일을 업로드하거나, 데이터를 붙여넣거나, 웹 페이지에서 추출하세요. 전문 변환기 도구가 실시간 미리보기와 고급 편집 기능으로 데이터를 {{ .to }} 형식으로 즉시 변환합니다. 변환된 결과를 즉시 다운로드하거나 복사하세요."
  security:
    question: "이 온라인 변환기를 사용할 때 데이터가 안전한가요?"
    answer: "물론입니다! 모든 테이블 변환은 브라우저에서 로컬로 수행됩니다 - 데이터가 기기를 떠나지 않습니다. 온라인 변환기는 모든 것을 클라이언트 측에서 처리하여 완전한 개인정보 보호와 데이터 보안을 보장합니다. 서버에는 파일이 저장되지 않습니다."
  free:
    question: "TableConvert는 정말 무료로 사용할 수 있나요?"
    answer: "네, TableConvert는 완전히 무료입니다! 모든 변환기 기능, 테이블 편집기, 데이터 생성기 도구, 내보내기 옵션이 비용, 등록 또는 숨겨진 수수료 없이 이용 가능합니다. 온라인에서 무제한 파일을 무료로 변환하세요."
  filesize:
    question: "온라인 변환기의 파일 크기 제한은 얼마인가요?"
    answer: "무료 온라인 테이블 변환기는 최대 10MB 파일을 지원합니다. 더 큰 파일, 일괄 처리 또는 기업 요구사항의 경우 더 높은 제한을 가진 브라우저 확장 프로그램 또는 전문 API 서비스를 사용하세요."
stats:
  conversions: "변환된 테이블"
  tables: "생성된 테이블"
  formats: "데이터 파일 형식"
  rating: "사용자 평점"
