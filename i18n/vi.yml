site:
  fullname: "Chuyển Đổi Bảng Trực <PERSON>"
  name: "TableConvert"
  subtitle: "Bộ Chuyển Đổi và Tạo Bảng Trực Tuyến Miễn <PERSON>"
  intro: "TableConvert là công cụ chuyển đổi bảng và tạo dữ liệu trực tuyến miễn phí hỗ trợ chuyển đổi giữa hơn 30 định dạng bao gồm Excel, CSV, JSON, Markdown, LaTeX, SQL và nhiều hơn nữa."
  followTwitter: "Theo dõi chúng tôi trên X"
title:
  converter: "%s sang %s"
  generator: "Trình Tạo %s"
post:
  tags:
    converter: "Bộ Chuyển Đổi"
    editor: "Trình Chỉnh Sửa"
    generator: "Trình Tạo"
    maker: "Người Xây Dựng"
  converter:
    title: "Chuyển Đổi %s sang %s Trự<PERSON>"
    short: "Công cụ trực tuyến miễn phí và mạnh mẽ từ %s sang %s"
    intro: "Bộ chuyển đổi trực tuyến từ %s sang %s dễ sử dụng. Chuyển đổi dữ liệu bảng một cách dễ dàng với công cụ chuyển đổi trực quan của chúng tôi. Nhanh chóng, đáng tin cậy và thân thiện với người dùng."
  generator:
    title: "Trình Chỉnh Sửa và Tạo %s Trực Tuyến"
    short: "Công cụ tạo %s trực tuyến chuyên nghiệp với các tính năng toàn diện"
    intro: "Trình tạo %s trực tuyến và trình chỉnh sửa bảng dễ sử dụng. Tạo bảng dữ liệu chuyên nghiệp một cách dễ dàng với công cụ trực quan và xem trước thời gian thực của chúng tôi."
navbar:
  search:
    placeholder: "Tìm kiếm bộ chuyển đổi..."
  sponsor: "Mua Cho Chúng Tôi Cà Phê"
  extension: "Tiện Ích Mở Rộng"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Nguồn Dữ Liệu"
    placeholder: "Dán dữ liệu %s của bạn hoặc kéo thả tệp %s vào đây"
    example: "Ví dụ"
    upload: "Tải Lên Tệp"
    extract:
      enter: "Trích Xuất từ Trang Web"
      intro: "Nhập URL trang web chứa dữ liệu bảng để tự động trích xuất dữ liệu có cấu trúc"
      btn: "Trích Xuất %s"
    excel:
      sheet: "Bảng Tính"
      none: "Không có"
  tableEditor:
    title: "Trình Chỉnh Sửa Bảng Trực Tuyến"
    undo: "Hoàn Tác"
    redo: "Làm Lại"
    transpose: "Chuyển Vị"
    clear: "Xóa"
    deleteBlank: "Xóa Trống"
    deleteDuplicate: "Xóa Trùng Lặp"
    uppercase: "CHỮ HOA"
    lowercase: "chữ thường"
    capitalize: "Viết Hoa Chữ Đầu"
    replace:
      replace: "Tìm và Thay Thế (hỗ trợ Regex)"
      subst: "Thay thế bằng..."
      btn: "Thay Thế Tất Cả"
  tableGenerator:
    title: "Trình Tạo Bảng"
    sponsor: "Mua Cho Chúng Tôi Cà Phê"
    copy: "Sao Chép vào Clipboard"
    download: "Tải Xuống Tệp"
    tooltip:
      html:
        escape: "Thoát các ký tự đặc biệt HTML (&, <, >, \", ') để ngăn lỗi hiển thị"
        div: "Sử dụng bố cục DIV+CSS thay vì thẻ TABLE truyền thống, phù hợp hơn cho thiết kế responsive"
        minify: "Loại bỏ khoảng trắng và ngắt dòng để tạo mã HTML nén"
        thead: "Tạo cấu trúc tiêu đề (&lt;thead&gt;) và thân (&lt;tbody&gt;) bảng chuẩn"
        tableCaption: "Thêm tiêu đề mô tả phía trên bảng (phần tử &lt;caption&gt;)"
        tableClass: "Thêm tên lớp CSS vào bảng để dễ tùy chỉnh kiểu"
        tableId: "Đặt ID định danh duy nhất cho bảng để thao tác JavaScript"
      jira:
        escape: "Thoát ký tự pipe (|) để tránh xung đột với cú pháp bảng Jira"
      json:
        parsingJSON: "Phân tích thông minh chuỗi JSON trong ô thành đối tượng"
        minify: "Tạo định dạng JSON một dòng compact để giảm kích thước tệp"
        format: "Chọn cấu trúc dữ liệu JSON đầu ra: mảng đối tượng, mảng 2D, v.v."
      latex:
        escape: "Thoát các ký tự đặc biệt LaTeX (%, &, _, #, $, v.v.) để đảm bảo biên dịch đúng"
        ht: "Thêm tham số vị trí nổi [!ht] để kiểm soát vị trí bảng trên trang"
        mwe: "Tạo tài liệu LaTeX hoàn chỉnh"
        tableAlign: "Đặt căn chỉnh ngang của bảng trên trang"
        tableBorder: "Cấu hình kiểu viền bảng: không viền, viền một phần, viền đầy đủ"
        label: "Đặt nhãn bảng cho lệnh \\ref{} tham chiếu chéo"
        caption: "Đặt chú thích bảng để hiển thị phía trên hoặc dưới bảng"
        location: "Chọn vị trí hiển thị chú thích bảng: phía trên hoặc dưới"
        tableType: "Chọn loại môi trường bảng: tabular, longtable, array, v.v."
      markdown:
        escape: "Thoát các ký tự đặc biệt Markdown (*, _, |, \\, v.v.) để tránh xung đột định dạng"
        pretty: "Tự động căn chỉnh độ rộng cột để tạo định dạng bảng đẹp hơn"
        simple: "Sử dụng cú pháp đơn giản, bỏ qua các đường viền dọc bên ngoài"
        boldFirstRow: "Làm đậm văn bản hàng đầu tiên"
        boldFirstColumn: "Làm đậm văn bản cột đầu tiên"
        firstHeader: "Xử lý hàng đầu tiên như tiêu đề và thêm đường phân cách"
        textAlign: "Đặt căn chỉnh văn bản cột: trái, giữa, phải"
        multilineHandling: "Xử lý văn bản nhiều dòng: giữ nguyên ngắt dòng, thoát thành \\n, sử dụng thẻ &lt;br&gt;"

        includeLineNumbers: "Thêm cột số dòng ở phía bên trái bảng"
      magic:
        builtin: "Chọn các định dạng mẫu phổ biến được xác định trước"
        rowsTpl: "<table> <tr> <th>Cú pháp Magic</th> <th>Mô tả</th> <th>Hỗ trợ phương thức JS</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>Trường thứ 1, thứ 2 ... của <b>tiêu đề</b>, tức là {hA} {hB} ...</td> <td>Phương thức chuỗi</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>Trường thứ 1, thứ 2 ... của hàng hiện tại, tức là {$A} {$B} ...</td> <td>Phương thức chuỗi</td> </tr> <tr> <td>{F,} {F;}</td> <td>Phân tách hàng hiện tại bằng chuỗi sau <b>F</b></td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td><b>Số</b> dòng của <b>hàng</b> hiện tại từ 1 hoặc 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>Số</b> dòng <b>cuối</b> của các <b>hàng</b> </td> <td></td> </tr> <tr> <td>{x ...}</td> <td><b>Thực thi</b> mã JavaScript, ví dụ: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Sử dụng dấu gạch chéo ngược <b>\\</b> để xuất dấu ngoặc {...} </td> <td></td> </tr></table>"
        headerTpl: "Mẫu đầu ra tùy chỉnh cho phần tiêu đề"
        footerTpl: "Mẫu đầu ra tùy chỉnh cho phần chân trang"
      textile:
        escape: "Thoát ký tự cú pháp Textile (|, ., -, ^) để tránh xung đột định dạng"
        rowHeader: "Đặt hàng đầu tiên làm hàng tiêu đề"
        thead: "Thêm dấu hiệu cú pháp Textile cho đầu và thân bảng"
      xml:
        escape: "Thoát ký tự đặc biệt XML (&lt;, &gt;, &amp;, \", ') để đảm bảo XML hợp lệ"
        minify: "Tạo đầu ra XML nén, loại bỏ khoảng trắng thừa"
        rootElement: "Đặt tên thẻ phần tử gốc XML"
        rowElement: "Đặt tên thẻ phần tử XML cho mỗi hàng dữ liệu"
        declaration: "Thêm tiêu đề khai báo XML (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Xuất dữ liệu dưới dạng thuộc tính XML thay vì phần tử con"
        cdata: "Bao bọc nội dung văn bản bằng CDATA để bảo vệ ký tự đặc biệt"
        encoding: "Đặt định dạng mã hóa ký tự cho tài liệu XML"
        indentation: "Chọn ký tự thụt lề XML: khoảng trắng hoặc tab"
      yaml:
        indentSize: "Đặt số khoảng trắng cho thụt lề phân cấp YAML (thường là 2 hoặc 4)"
        arrayStyle: "Định dạng mảng: khối (một mục mỗi dòng) hoặc dòng chảy (định dạng nội tuyến)"
        quotationStyle: "Kiểu dấu ngoặc kép chuỗi: không có dấu ngoặc kép, dấu ngoặc kép đơn, dấu ngoặc kép đôi"
      pdf:
        theme: "Chọn kiểu hình ảnh bảng PDF cho tài liệu chuyên nghiệp"
        headerColor: "Chọn màu nền tiêu đề bảng PDF"
        showHead: "Kiểm soát hiển thị tiêu đề trên các trang PDF"
        docTitle: "Tiêu đề tùy chọn cho tài liệu PDF"
        docDescription: "Văn bản mô tả tùy chọn cho tài liệu PDF"
      csv:
        bom: "Thêm dấu thứ tự byte UTF-8 để giúp Excel và phần mềm khác nhận diện mã hóa"
      excel:
        autoWidth: "Tự động điều chỉnh chiều rộng cột dựa trên nội dung"
        protectSheet: "Bật bảo vệ bảng tính với mật khẩu: tableconvert.com"
      sql:  
        primaryKey: "Chỉ định tên trường khóa chính cho câu lệnh CREATE TABLE"
        dialect: "Chọn loại cơ sở dữ liệu, ảnh hưởng đến cú pháp dấu ngoặc kép và kiểu dữ liệu"
      ascii:
        forceSep: "Ép buộc các dòng phân cách giữa mỗi hàng dữ liệu"
        style: "Chọn kiểu vẽ viền bảng ASCII"
        comment: "Thêm dấu hiệu bình luận để bao bọc toàn bộ bảng"
      mediawiki:
        minify: "Nén mã đầu ra, loại bỏ khoảng trắng thừa"
        header: "Đánh dấu hàng đầu tiên như kiểu tiêu đề"
        sort: "Bật chức năng sắp xếp bảng bằng cách nhấp chuột"
      asciidoc:
        minify: "Nén đầu ra định dạng AsciiDoc"
        firstHeader: "Đặt hàng đầu tiên làm hàng tiêu đề"
        lastFooter: "Đặt hàng cuối cùng làm hàng chân trang"
        title: "Thêm văn bản tiêu đề vào bảng"
      tracwiki:
        rowHeader: "Đặt hàng đầu tiên làm tiêu đề"
        colHeader: "Đặt cột đầu tiên làm tiêu đề"
      bbcode:
        minify: "Nén định dạng đầu ra BBCode"
      restructuredtext:
        style: "Chọn kiểu viền bảng reStructuredText"
        forceSep: "Ép buộc các dòng phân cách"
    label:
      ascii:
        forceSep: "Dấu Phân Cách Hàng"
        style: "Kiểu Viền"
        comment: "Bao Bọc Bình Luận"
      restructuredtext:
        style: "Kiểu Viền"
        forceSep: "Ép Buộc Phân Cách"
      bbcode:
        minify: "Thu Gọn Đầu Ra"
      csv:
        doubleQuote: "Bao Bọc Dấu Ngoặc Kép"
        delimiter: "Dấu Phân Cách Trường"
        bom: "UTF-8 BOM"
        valueDelimiter: "Dấu Phân Cách Giá Trị"
        rowDelimiter: "Dấu Phân Cách Hàng"
        prefix: "Tiền Tố Hàng"
        suffix: "Hậu Tố Hàng"
      excel:
        autoWidth: "Tự Động Độ Rộng"
        textFormat: "Định Dạng Văn Bản"
        protectSheet: "Bảo Vệ Trang Tính"
        boldFirstRow: "Làm Đậm Hàng Đầu"
        boldFirstColumn: "Làm Đậm Cột Đầu"
        sheetName: "Tên Trang Tính"
      html:
        escape: "Thoát Ký Tự HTML"
        div: "Bảng DIV"
        minify: "Thu Gọn Mã"
        thead: "Cấu Trúc Đầu Bảng"
        tableCaption: "Chú Thích Bảng"
        tableClass: "Lớp Bảng"
        tableId: "ID Bảng"
        rowHeader: "Tiêu Đề Hàng"
        colHeader: "Tiêu Đề Cột"
      jira:
        escape: "Thoát Ký Tự"
        rowHeader: "Tiêu Đề Hàng"
        colHeader: "Tiêu Đề Cột"
      json:
        parsingJSON: "Phân Tích JSON"
        minify: "Thu Gọn Đầu Ra"
        format: "Định Dạng Dữ Liệu"
        rootName: "Tên Đối Tượng Gốc"
        indentSize: "Kích Thước Thụt Lề"
      jsonlines:
        parsingJSON: "Phân Tích JSON"
        format: "Định Dạng Dữ Liệu"
      latex:
        escape: "Thoát Ký Tự Bảng LaTeX"
        ht: "Vị Trí Nổi"
        mwe: "Tài Liệu Hoàn Chỉnh"
        tableAlign: "Căn Chỉnh Bảng"
        tableBorder: "Kiểu Viền"
        label: "Nhãn Tham Chiếu"
        caption: "Chú Thích Bảng"
        location: "Vị Trí Chú Thích"
        tableType: "Loại Bảng"
        boldFirstRow: "Làm Đậm Hàng Đầu"
        boldFirstColumn: "Làm Đậm Cột Đầu"
        textAlign: "Căn Chỉnh Văn Bản"
        borders: "Cài Đặt Viền"
      markdown:
        escape: "Thoát Ký Tự"
        pretty: "Bảng Markdown Đẹp"
        simple: "Định Dạng Markdown Đơn Giản"
        boldFirstRow: "Làm Đậm Hàng Đầu"
        boldFirstColumn: "Làm Đậm Cột Đầu"
        firstHeader: "Tiêu Đề Đầu"
        textAlign: "Căn Chỉnh Văn Bản"
        multilineHandling: "Xử Lý Nhiều Dòng"

        includeLineNumbers: "Thêm Số Dòng"
        align: "Căn Chỉnh"
      mediawiki:
        minify: "Thu Gọn Mã"
        header: "Đánh Dấu Tiêu Đề"
        sort: "Có Thể Sắp Xếp"
      asciidoc:
        minify: "Thu Gọn Định Dạng"
        firstHeader: "Tiêu Đề Đầu"
        lastFooter: "Chân Trang Cuối"
        title: "Tiêu Đề Bảng"
      tracwiki:
        rowHeader: "Tiêu Đề Hàng"
        colHeader: "Tiêu Đề Cột"
      sql:
        drop: "Xóa Bảng (Nếu Tồn Tại)"
        create: "Tạo Bảng"
        oneInsert: "Chèn Hàng Loạt"
        table: "Tên Bảng"
        dialect: "Loại Cơ Sở Dữ Liệu"
        primaryKey: "Khóa Chính"
      magic:
        builtin: "Mẫu Tích Hợp"
        rowsTpl: "Mẫu Hàng, Cú Pháp ->"
        headerTpl: "Mẫu Tiêu Đề"
        footerTpl: "Mẫu Chân Trang"
      textile:
        escape: "Thoát Ký Tự"
        rowHeader: "Tiêu Đề Hàng"
        thead: "Cú pháp đầu bảng"
      xml:
        escape: "Thoát ký tự đặc biệt XML"
        minify: "Thu gọn đầu ra"
        rootElement: "Phần tử gốc"
        rowElement: "Phần tử hàng"
        declaration: "Khai báo XML"
        attributes: "Chế độ thuộc tính"
        cdata: "Bao bọc CDATA"
        encoding: "Mã hóa"
        indentSize: "Kích thước thụt lề"
      yaml:
        indentSize: "Kích thước thụt lề"
        arrayStyle: "Kiểu mảng"
        quotationStyle: "Kiểu dấu ngoặc kép"
      pdf:
        theme: "Chủ Đề Bảng PDF"
        headerColor: "Màu Tiêu Đề PDF"
        showHead: "Hiển Thị Tiêu Đề PDF"
        docTitle: "Tiêu Đề Tài Liệu PDF"
        docDescription: "Mô Tả Tài Liệu PDF"
sidebar:
  all: "Tất cả công cụ chuyển đổi"
  dataSource:
    title: "Nguồn dữ liệu"
    description:
      converter: "Nhập %s để chuyển đổi sang %s. Hỗ trợ tải lên tệp, chỉnh sửa trực tuyến và trích xuất dữ liệu web."
      generator: "Tạo dữ liệu bảng với hỗ trợ nhiều phương thức nhập bao gồm nhập thủ công, nhập tệp và tạo mẫu."
  tableEditor:
    title: "Trình chỉnh sửa bảng trực tuyến"
    description:
      converter: "Xử lý %s trực tuyến bằng trình chỉnh sửa bảng của chúng tôi. Trải nghiệm vận hành giống Excel với hỗ trợ xóa hàng trống, khử trùng lặp, sắp xếp và tìm kiếm & thay thế."
      generator: "Trình chỉnh sửa bảng trực tuyến mạnh mẽ cung cấp trải nghiệm vận hành giống Excel. Hỗ trợ xóa hàng trống, khử trùng lặp, sắp xếp và tìm kiếm & thay thế."
  tableGenerator:
    title: "Trình tạo bảng"
    description:
      converter: "Nhanh chóng tạo %s với xem trước thời gian thực của trình tạo bảng. Tùy chọn xuất phong phú, sao chép và tải xuống một cú nhấp chuột."
      generator: "Xuất dữ liệu %s ở nhiều định dạng để đáp ứng các tình huống sử dụng khác nhau. Hỗ trợ tùy chọn tùy chỉnh và xem trước thời gian thực."
footer:
  changelog: "Nhật ký thay đổi"
  sponsor: "Nhà tài trợ"
  contact: "Liên hệ với chúng tôi"
  privacyPolicy: "Chính sách bảo mật"
  about: "Giới thiệu"
  resources: "Tài Nguyên"
  popularConverters: "Bộ Chuyển Đổi Phổ Biến"
  popularGenerators: "Trình Tạo Phổ Biến"
  dataSecurity: "Dữ liệu của bạn được bảo mật - tất cả chuyển đổi chạy trong trình duyệt của bạn."
converters:
  Markdown:
    alias: "Bảng Markdown"
    what: "Markdown là ngôn ngữ đánh dấu nhẹ được sử dụng rộng rãi cho tài liệu kỹ thuật, tạo nội dung blog và phát triển web. Cú pháp bảng của nó ngắn gọn và trực quan, hỗ trợ căn chỉnh văn bản, nhúng liên kết và định dạng. Đây là công cụ ưa thích của các lập trình viên và nhà viết kỹ thuật, hoàn toàn tương thích với GitHub, GitLab và các nền tảng lưu trữ mã khác."
    step1: "Dán dữ liệu bảng Markdown vào khu vực nguồn dữ liệu, hoặc trực tiếp kéo thả tệp .md để tải lên. Công cụ tự động phân tích cấu trúc và định dạng bảng, hỗ trợ nội dung lồng nhau phức tạp và xử lý ký tự đặc biệt."
    step3: "Tạo mã bảng Markdown chuẩn theo thời gian thực, hỗ trợ nhiều phương pháp căn chỉnh, làm đậm văn bản, thêm số dòng và các cài đặt định dạng nâng cao khác. Mã được tạo hoàn toàn tương thích với GitHub và các trình chỉnh sửa Markdown chính, sẵn sàng sử dụng với một cú nhấp chuột sao chép."
    from_alias: "Tệp Bảng Markdown"
    to_alias: "Định Dạng Bảng Markdown"
  Magic:
    alias: "Mẫu Tùy Chỉnh"
    what: "Mẫu Magic là trình tạo dữ liệu nâng cao độc đáo của công cụ này, cho phép người dùng tạo đầu ra dữ liệu định dạng tùy ý thông qua cú pháp mẫu tùy chỉnh. Hỗ trợ thay thế biến, phán đoán có điều kiện và xử lý vòng lặp. Đây là giải pháp cuối cùng để xử lý nhu cầu chuyển đổi dữ liệu phức tạp và định dạng đầu ra cá nhân hóa, đặc biệt phù hợp cho các nhà phát triển và kỹ sư dữ liệu."
    step1: "Chọn các mẫu phổ biến tích hợp sẵn hoặc tạo cú pháp mẫu tùy chỉnh. Hỗ trợ các biến và hàm phong phú có thể xử lý cấu trúc dữ liệu phức tạp và logic kinh doanh."
    step3: "Tạo đầu ra dữ liệu hoàn toàn đáp ứng yêu cầu định dạng tùy chỉnh. Hỗ trợ logic chuyển đổi dữ liệu phức tạp và xử lý có điều kiện, cải thiện đáng kể hiệu quả xử lý dữ liệu và chất lượng đầu ra. Một công cụ mạnh mẽ cho xử lý dữ liệu hàng loạt."
    from_alias: "Dữ Liệu Bảng"
    to_alias: "Đầu Ra Định Dạng Tùy Chỉnh"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) là định dạng trao đổi dữ liệu được sử dụng rộng rãi nhất, được hỗ trợ hoàn hảo bởi Excel, Google Sheets, hệ thống cơ sở dữ liệu và các công cụ phân tích dữ liệu khác nhau. Cấu trúc đơn giản và khả năng tương thích mạnh mẽ của nó làm cho nó trở thành định dạng chuẩn cho di chuyển dữ liệu, nhập/xuất hàng loạt và trao đổi dữ liệu đa nền tảng, được sử dụng rộng rãi trong phân tích kinh doanh, khoa học dữ liệu và tích hợp hệ thống."
    step1: "Tải lên tệp CSV hoặc dán trực tiếp dữ liệu CSV. Công cụ nhận dạng thông minh các dấu phân cách khác nhau (dấu phẩy, tab, dấu chấm phẩy, ống dẫn, v.v.), tự động phát hiện loại dữ liệu và định dạng mã hóa, hỗ trợ phân tích nhanh các tệp lớn và cấu trúc dữ liệu phức tạp."
    step3: "Tạo tệp định dạng CSV chuẩn với hỗ trợ dấu phân cách tùy chỉnh, kiểu dấu ngoặc kép, định dạng mã hóa và cài đặt dấu BOM. Đảm bảo khả năng tương thích hoàn hảo với hệ thống đích, cung cấp các tùy chọn tải xuống và nén để đáp ứng nhu cầu xử lý dữ liệu cấp doanh nghiệp."
    from_alias: "Tệp Dữ Liệu CSV"
    to_alias: "Định Dạng CSV Chuẩn"
  JSON:
    alias: "Mảng JSON"
    what: "JSON (JavaScript Object Notation) là định dạng dữ liệu bảng tiêu chuẩn cho các ứng dụng web hiện đại, REST API và kiến trúc microservice. Cấu trúc rõ ràng và phân tích hiệu quả của nó làm cho nó được sử dụng rộng rãi trong tương tác dữ liệu front-end và back-end, lưu trữ tệp cấu hình và cơ sở dữ liệu NoSQL. Hỗ trợ các đối tượng lồng nhau, cấu trúc mảng và nhiều loại dữ liệu, làm cho nó trở thành dữ liệu bảng không thể thiếu cho phát triển phần mềm hiện đại."
    step1: "Tải lên tệp JSON hoặc dán mảng JSON. Hỗ trợ nhận dạng và phân tích tự động các mảng đối tượng, cấu trúc lồng nhau và các loại dữ liệu phức tạp. Công cụ xác thực cú pháp JSON một cách thông minh và cung cấp lời nhắc lỗi."
    step3: "Tạo nhiều đầu ra định dạng JSON: mảng đối tượng tiêu chuẩn, mảng 2D, mảng cột và định dạng cặp khóa-giá trị. Hỗ trợ đầu ra được làm đẹp, chế độ nén, tên đối tượng gốc tùy chỉnh và cài đặt thụt lề, thích ứng hoàn hảo với các giao diện API khác nhau và nhu cầu lưu trữ dữ liệu."
    from_alias: "Tệp Mảng JSON"
    to_alias: "Định Dạng JSON Chuẩn"
  JSONLines:
    alias: "Định Dạng JSONLines"
    what: "JSON Lines (còn được gọi là NDJSON) là một định dạng quan trọng cho xử lý dữ liệu lớn và truyền dữ liệu luồng, với mỗi dòng chứa một đối tượng JSON độc lập. Được sử dụng rộng rãi trong phân tích nhật ký, xử lý luồng dữ liệu, học máy và hệ thống phân tán. Hỗ trợ xử lý gia tăng và tính toán song song, làm cho nó trở thành lựa chọn lý tưởng để xử lý dữ liệu có cấu trúc quy mô lớn."
    step1: "Tải lên tệp JSONLines hoặc dán dữ liệu. Công cụ phân tích các đối tượng JSON từng dòng, hỗ trợ xử lý luồng tệp lớn và chức năng bỏ qua dòng lỗi."
    step3: "Tạo định dạng JSONLines chuẩn với mỗi dòng xuất ra một đối tượng JSON hoàn chỉnh. Phù hợp cho xử lý luồng, nhập hàng loạt và các tình huống phân tích dữ liệu lớn, hỗ trợ xác thực dữ liệu và tối ưu hóa định dạng."
    from_alias: "Dữ Liệu JSONLines"
    to_alias: "Định Dạng Luồng JSONLines"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) là định dạng tiêu chuẩn cho trao đổi dữ liệu cấp doanh nghiệp và quản lý cấu hình, với đặc tả cú pháp nghiêm ngặt và cơ chế xác thực mạnh mẽ. Được sử dụng rộng rãi trong dịch vụ web, tệp cấu hình, lưu trữ tài liệu và tích hợp hệ thống. Hỗ trợ không gian tên, xác thực lược đồ và chuyển đổi XSLT, làm cho nó trở thành dữ liệu bảng quan trọng cho các ứng dụng doanh nghiệp."
    step1: "Tải lên tệp XML hoặc dán dữ liệu XML. Công cụ tự động phân tích cấu trúc XML và chuyển đổi thành định dạng bảng, hỗ trợ không gian tên, xử lý thuộc tính và cấu trúc lồng nhau phức tạp."
    step3: "Tạo đầu ra XML tuân thủ tiêu chuẩn XML. Hỗ trợ phần tử gốc tùy chỉnh, tên phần tử hàng, chế độ thuộc tính, bao bọc CDATA và cài đặt mã hóa ký tự. Đảm bảo tính toàn vẹn và tương thích dữ liệu, đáp ứng yêu cầu ứng dụng cấp doanh nghiệp."
    from_alias: "Tệp Dữ Liệu XML"
    to_alias: "Định Dạng XML Chuẩn"
  YAML:
    alias: "Cấu Hình YAML"
    what: "YAML là tiêu chuẩn tuần tự hóa dữ liệu thân thiện với con người, nổi tiếng với cấu trúc phân cấp rõ ràng và cú pháp súc tích. Được sử dụng rộng rãi trong tệp cấu hình, chuỗi công cụ DevOps, Docker Compose và triển khai Kubernetes. Khả năng đọc mạnh mẽ và cú pháp súc tích của nó làm cho nó trở thành định dạng cấu hình quan trọng cho các ứng dụng đám mây gốc hiện đại và hoạt động tự động."
    step1: "Tải lên tệp YAML hoặc dán dữ liệu YAML. Công cụ phân tích thông minh cấu trúc YAML và xác thực tính đúng đắn của cú pháp, hỗ trợ định dạng đa tài liệu và các kiểu dữ liệu phức tạp."
    step3: "Tạo đầu ra định dạng YAML chuẩn với hỗ trợ kiểu mảng khối và luồng, nhiều cài đặt trích dẫn, thụt lề tùy chỉnh và bảo toàn bình luận. Đảm bảo các tệp YAML đầu ra hoàn toàn tương thích với các bộ phân tích và hệ thống cấu hình khác nhau."
    from_alias: "Tệp Cấu Hình YAML"
    to_alias: "Định Dạng YAML Chuẩn"
  MySQL:
      alias: "Kết Quả Truy Vấn MySQL"
      what: "MySQL là hệ thống quản lý cơ sở dữ liệu quan hệ mã nguồn mở phổ biến nhất thế giới, nổi tiếng với hiệu suất cao, độ tin cậy và dễ sử dụng. Được sử dụng rộng rãi trong các ứng dụng web, hệ thống doanh nghiệp và nền tảng phân tích dữ liệu. Kết quả truy vấn MySQL thường chứa dữ liệu bảng có cấu trúc, phục vụ như một nguồn dữ liệu quan trọng trong quản lý cơ sở dữ liệu và công việc phân tích dữ liệu."
      step1: "Dán kết quả đầu ra truy vấn MySQL vào khu vực nguồn dữ liệu. Công cụ tự động nhận dạng và phân tích định dạng đầu ra dòng lệnh MySQL, hỗ trợ các kiểu kết quả truy vấn khác nhau và mã hóa ký tự, xử lý thông minh tiêu đề và hàng dữ liệu."
      step3: "Nhanh chóng chuyển đổi kết quả truy vấn MySQL sang nhiều định dạng dữ liệu bảng, tạo điều kiện thuận lợi cho phân tích dữ liệu, tạo báo cáo, di chuyển dữ liệu giữa các hệ thống và xác thực dữ liệu. Một công cụ thực tế cho quản trị viên cơ sở dữ liệu và nhà phân tích dữ liệu."
      from_alias: "Đầu Ra Truy Vấn MySQL"
      to_alias: "Dữ Liệu Bảng MySQL"
  SQL:
    alias: "SQL Chèn"
    what: "SQL (Structured Query Language) là ngôn ngữ hoạt động tiêu chuẩn cho cơ sở dữ liệu quan hệ, được sử dụng cho các hoạt động truy vấn, chèn, cập nhật và xóa dữ liệu. Là công nghệ cốt lõi của quản lý cơ sở dữ liệu, SQL được sử dụng rộng rãi trong phân tích dữ liệu, trí tuệ kinh doanh, xử lý ETL và xây dựng kho dữ liệu. Đây là công cụ kỹ năng thiết yếu cho các chuyên gia dữ liệu."
    step1: "Dán các câu lệnh SQL INSERT hoặc tải lên tệp .sql. Công cụ phân tích thông minh cú pháp SQL và trích xuất dữ liệu bảng, hỗ trợ nhiều phương ngữ SQL và xử lý câu lệnh truy vấn phức tạp."
    step3: "Tạo các câu lệnh SQL INSERT chuẩn và câu lệnh tạo bảng. Hỗ trợ nhiều phương ngữ cơ sở dữ liệu (MySQL, PostgreSQL, SQLite, SQL Server, Oracle), tự động xử lý ánh xạ kiểu dữ liệu, thoát ký tự và ràng buộc khóa chính. Đảm bảo mã SQL được tạo có thể được thực thi trực tiếp."
    from_alias: "Tệp Dữ Liệu SQL"
    to_alias: "Câu Lệnh SQL Chuẩn"
  Qlik:
      alias: "Bảng Qlik"
      what: "Qlik là nhà cung cấp phần mềm chuyên về trực quan hóa dữ liệu, bảng điều khiển điều hành và các sản phẩm business intelligence tự phục vụ, cùng với Tableau và Microsoft."
      step1: ""
      step3: "Cuối cùng, [Trình Tạo Bảng](#TableGenerator) hiển thị kết quả chuyển đổi. Sử dụng trong Qlik Sense, Qlik AutoML, QlikView hoặc phần mềm hỗ trợ Qlik khác của bạn."
      from_alias: "Bảng Qlik"
      to_alias: "Bảng Qlik"
  DAX:
      alias: "Bảng DAX"
      what: "DAX (Data Analysis Expressions) là ngôn ngữ lập trình được sử dụng trong Microsoft Power BI để tạo các cột tính toán, thước đo và bảng tùy chỉnh."
      step1: ""
      step3: "Cuối cùng, [Trình Tạo Bảng](#TableGenerator) hiển thị kết quả chuyển đổi. Như mong đợi, nó được sử dụng trong một số sản phẩm Microsoft bao gồm Microsoft Power BI, Microsoft Analysis Services và Microsoft Power Pivot cho Excel."
      from_alias: "Bảng DAX"
      to_alias: "Bảng DAX"
  Firebase:
    alias: "Danh Sách Firebase"
    what: "Firebase là nền tảng phát triển ứng dụng BaaS cung cấp các dịch vụ backend được lưu trữ như cơ sở dữ liệu thời gian thực, lưu trữ đám mây, xác thực, báo cáo sự cố, v.v."
    step1: ""
    step3: "Cuối cùng, [Trình Tạo Bảng](#TableGenerator) hiển thị kết quả chuyển đổi. Sau đó bạn có thể sử dụng phương thức push trong API Firebase để thêm vào danh sách dữ liệu trong cơ sở dữ liệu Firebase."
    from_alias: "Danh Sách Firebase"
    to_alias: "Danh Sách Firebase"
  HTML:
    alias: "Bảng HTML"
    what: "Bảng HTML là cách tiêu chuẩn để hiển thị dữ liệu có cấu trúc trong các trang web, được xây dựng với các thẻ table, tr, td và các thẻ khác. Hỗ trợ tùy chỉnh kiểu phong phú, bố cục đáp ứng và chức năng tương tác. Được sử dụng rộng rãi trong phát triển trang web, hiển thị dữ liệu và tạo báo cáo, phục vụ như một thành phần quan trọng của phát triển front-end và thiết kế web."
    step1: "Dán mã HTML chứa bảng hoặc tải lên tệp HTML. Công cụ tự động nhận dạng và trích xuất dữ liệu bảng từ các trang, hỗ trợ cấu trúc HTML phức tạp, kiểu CSS và xử lý bảng lồng nhau."
    step3: "Tạo mã bảng HTML ngữ nghĩa với hỗ trợ cấu trúc thead/tbody, cài đặt lớp CSS, chú thích bảng, tiêu đề hàng/cột và cấu hình thuộc tính đáp ứng. Đảm bảo mã bảng được tạo đáp ứng tiêu chuẩn web với khả năng truy cập tốt và thân thiện với SEO."
    from_alias: "Bảng Web HTML"
    to_alias: "Bảng HTML Chuẩn"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel là phần mềm bảng tính phổ biến nhất thế giới, được sử dụng rộng rãi trong phân tích kinh doanh, quản lý tài chính, xử lý dữ liệu và tạo báo cáo. Khả năng xử lý dữ liệu mạnh mẽ, thư viện hàm phong phú và tính năng trực quan hóa linh hoạt làm cho nó trở thành công cụ tiêu chuẩn cho tự động hóa văn phòng và phân tích dữ liệu, với ứng dụng rộng rãi trong hầu hết các ngành và lĩnh vực."
    step1: "Tải lên tệp Excel (hỗ trợ định dạng .xlsx, .xls) hoặc sao chép dữ liệu bảng trực tiếp từ Excel và dán. Công cụ hỗ trợ xử lý nhiều trang tính, nhận dạng định dạng phức tạp và phân tích nhanh các tệp lớn, tự động xử lý các ô được hợp nhất và kiểu dữ liệu."
    step3: "Tạo dữ liệu bảng tương thích với Excel có thể được dán trực tiếp vào Excel hoặc tải xuống dưới dạng tệp .xlsx chuẩn. Hỗ trợ đặt tên trang tính, định dạng ô, tự động điều chỉnh độ rộng cột, kiểu tiêu đề và cài đặt xác thực dữ liệu. Đảm bảo tệp Excel đầu ra có giao diện chuyên nghiệp và chức năng hoàn chỉnh."
    from_alias: "Bảng Tính Excel"
    to_alias: "Định Dạng Excel Chuẩn"
  LaTeX:
    alias: "Bảng LaTeX"
    what: "LaTeX là hệ thống sắp chữ tài liệu chuyên nghiệp, đặc biệt phù hợp để tạo các bài báo học thuật, tài liệu kỹ thuật và xuất bản khoa học. Chức năng bảng của nó rất mạnh mẽ, hỗ trợ các công thức toán học phức tạp, kiểm soát bố cục chính xác và đầu ra PDF chất lượng cao. Đây là công cụ tiêu chuẩn trong học thuật và xuất bản khoa học, được sử dụng rộng rãi trong các bài báo tạp chí, luận văn và sắp chữ sách hướng dẫn kỹ thuật."
    step1: "Dán mã bảng LaTeX hoặc tải lên tệp .tex. Công cụ phân tích cú pháp bảng LaTeX và trích xuất nội dung dữ liệu, hỗ trợ nhiều môi trường bảng (tabular, longtable, array, v.v.) và các lệnh định dạng phức tạp."
    step3: "Tạo mã bảng LaTeX chuyên nghiệp với hỗ trợ lựa chọn nhiều môi trường bảng, cấu hình kiểu viền, cài đặt vị trí chú thích, đặc tả lớp tài liệu và quản lý gói. Có thể tạo tài liệu LaTeX hoàn chỉnh có thể biên dịch, đảm bảo bảng đầu ra đáp ứng tiêu chuẩn xuất bản học thuật."
    from_alias: "Bảng Tài Liệu LaTeX"
    to_alias: "Định Dạng LaTeX Chuyên Nghiệp"
  ASCII:
    alias: "Bảng ASCII"
    what: "Bảng ASCII sử dụng các ký tự văn bản thuần túy để vẽ viền và cấu trúc bảng, cung cấp khả năng tương thích và di động tốt nhất. Tương thích với tất cả trình soạn thảo văn bản, môi trường terminal và hệ điều hành. Được sử dụng rộng rãi trong tài liệu mã, sách hướng dẫn kỹ thuật, tệp README và đầu ra công cụ dòng lệnh. Định dạng hiển thị dữ liệu ưa thích cho các lập trình viên và quản trị viên hệ thống."
    step1: "Tải lên tệp văn bản chứa bảng ASCII hoặc dán trực tiếp dữ liệu bảng. Công cụ nhận dạng thông minh và phân tích cấu trúc bảng ASCII, hỗ trợ nhiều kiểu viền và định dạng căn chỉnh."
    step3: "Tạo bảng ASCII văn bản thuần túy đẹp mắt với hỗ trợ nhiều kiểu viền (đường đơn, đường đôi, góc tròn, v.v.), phương pháp căn chỉnh văn bản và tự động điều chỉnh độ rộng cột. Bảng được tạo hiển thị hoàn hảo trong trình soạn thảo mã, tài liệu và dòng lệnh."
    from_alias: "Bảng Văn Bản ASCII"
    to_alias: "Định Dạng ASCII Chuẩn"
  MediaWiki:
    alias: "Bảng MediaWiki"
    what: "MediaWiki là nền tảng phần mềm mã nguồn mở được sử dụng bởi các trang wiki nổi tiếng như Wikipedia. Cú pháp bảng của nó súc tích nhưng mạnh mẽ, hỗ trợ tùy chỉnh kiểu bảng, chức năng sắp xếp và nhúng liên kết. Được sử dụng rộng rãi trong quản lý tri thức, chỉnh sửa cộng tác và hệ thống quản lý nội dung, phục vụ như công nghệ cốt lõi để xây dựng bách khoa toàn thư wiki và cơ sở tri thức."
    step1: "Dán mã bảng MediaWiki hoặc tải lên tệp nguồn wiki. Công cụ phân tích cú pháp đánh dấu wiki và trích xuất dữ liệu bảng, hỗ trợ cú pháp wiki phức tạp và xử lý mẫu."
    step3: "Tạo mã bảng MediaWiki chuẩn với hỗ trợ cài đặt kiểu tiêu đề, căn chỉnh ô, kích hoạt chức năng sắp xếp và tùy chọn nén mã. Mã được tạo có thể được sử dụng trực tiếp để chỉnh sửa trang wiki, đảm bảo hiển thị hoàn hảo trên nền tảng MediaWiki."
    from_alias: "Mã Nguồn MediaWiki"
    to_alias: "Cú Pháp Bảng MediaWiki"
  TracWiki:
    alias: "Bảng TracWiki"
    what: "Trac là hệ thống quản lý dự án và theo dõi lỗi dựa trên web sử dụng cú pháp wiki đơn giản để tạo nội dung bảng."
    step1: "Tải lên tệp TracWiki hoặc dán dữ liệu bảng."
    step3: "Tạo mã bảng tương thích TracWiki với hỗ trợ cài đặt tiêu đề hàng/cột, tạo điều kiện thuận lợi cho quản lý tài liệu dự án."
    from_alias: "Bảng TracWiki"
    to_alias: "Định Dạng TracWiki"
  AsciiDoc:
    alias: "Bảng AsciiDoc"
    what: "AsciiDoc là ngôn ngữ đánh dấu nhẹ có thể được chuyển đổi thành HTML, PDF, trang hướng dẫn và các định dạng khác, được sử dụng rộng rãi để viết tài liệu kỹ thuật."
    step1: "Tải lên tệp AsciiDoc hoặc dán dữ liệu."
    step3: "Tạo cú pháp bảng AsciiDoc với hỗ trợ cài đặt tiêu đề, chân trang và tiêu đề, có thể sử dụng trực tiếp trong trình soạn thảo AsciiDoc."
    from_alias: "Bảng AsciiDoc"
    to_alias: "Định Dạng AsciiDoc"
  reStructuredText:
    alias: "Bảng reStructuredText"
    what: "reStructuredText là định dạng tài liệu tiêu chuẩn cho cộng đồng Python, hỗ trợ cú pháp bảng phong phú, thường được sử dụng để tạo tài liệu Sphinx."
    step1: "Tải lên tệp .rst hoặc dán dữ liệu reStructuredText."
    step3: "Tạo bảng reStructuredText chuẩn với hỗ trợ nhiều kiểu viền, có thể sử dụng trực tiếp trong các dự án tài liệu Sphinx."
    from_alias: "Bảng reStructuredText"
    to_alias: "Định Dạng reStructuredText"
  PHP:
    alias: "Mảng PHP"
    what: "PHP là ngôn ngữ kịch bản phía máy chủ phổ biến, với mảng là cấu trúc dữ liệu cốt lõi của nó, được sử dụng rộng rãi trong phát triển web và xử lý dữ liệu."
    step1: "Tải lên tệp chứa mảng PHP hoặc dán trực tiếp dữ liệu."
    step3: "Tạo mã mảng PHP chuẩn có thể được sử dụng trực tiếp trong các dự án PHP, hỗ trợ định dạng mảng kết hợp và mảng có chỉ mục."
    from_alias: "Mảng PHP"
    to_alias: "Mã PHP"
  Ruby:
    alias: "Mảng Ruby"
    what: "Ruby là ngôn ngữ lập trình hướng đối tượng động với cú pháp súc tích và thanh lịch, với mảng là cấu trúc dữ liệu quan trọng."
    step1: "Tải lên tệp Ruby hoặc dán dữ liệu mảng."
    step3: "Tạo mã mảng Ruby tuân thủ đặc tả cú pháp Ruby, có thể sử dụng trực tiếp trong các dự án Ruby."
    from_alias: "Mảng Ruby"
    to_alias: "Mã Ruby"
  ASP:
    alias: "Mảng ASP"
    what: "ASP (Active Server Pages) là môi trường kịch bản phía máy chủ của Microsoft, hỗ trợ nhiều ngôn ngữ lập trình để phát triển các trang web động."
    step1: "Tải lên tệp ASP hoặc dán dữ liệu mảng."
    step3: "Tạo mã mảng tương thích ASP với hỗ trợ cú pháp VBScript và JScript, có thể sử dụng trong các dự án ASP.NET."
    from_alias: "Mảng ASP"
    to_alias: "Mã ASP"
  ActionScript:
    alias: "Mảng ActionScript"
    what: "ActionScript là ngôn ngữ lập trình hướng đối tượng chủ yếu được sử dụng để phát triển ứng dụng Adobe Flash và AIR."
    step1: "Tải lên tệp .as hoặc dán dữ liệu ActionScript."
    step3: "Tạo mã mảng ActionScript tuân thủ tiêu chuẩn cú pháp AS3, có thể sử dụng để phát triển dự án Flash và Flex."
    from_alias: "Mảng ActionScript"
    to_alias: "Mã ActionScript"
  BBCode:
    alias: "Bảng BBCode"
    what: "BBCode là ngôn ngữ đánh dấu nhẹ thường được sử dụng trong diễn đàn và cộng đồng trực tuyến, cung cấp chức năng định dạng đơn giản bao gồm hỗ trợ bảng."
    step1: "Tải lên tệp chứa BBCode hoặc dán dữ liệu."
    step3: "Tạo mã bảng BBCode phù hợp để đăng diễn đàn và tạo nội dung cộng đồng, với hỗ trợ định dạng đầu ra nén."
    from_alias: "Bảng BBCode"
    to_alias: "Định Dạng BBCode"
  PDF:
    alias: "Bảng PDF"
    what: "PDF (Portable Document Format) là tiêu chuẩn tài liệu đa nền tảng với bố cục cố định, hiển thị nhất quán và đặc tính in chất lượng cao. Được sử dụng rộng rãi trong tài liệu chính thức, báo cáo, hóa đơn, hợp đồng và bài báo học thuật. Định dạng ưa thích cho giao tiếp kinh doanh và lưu trữ tài liệu, đảm bảo hiệu ứng hình ảnh hoàn toàn nhất quán trên các thiết bị và hệ điều hành khác nhau."
    step1: "Nhập dữ liệu bảng ở bất kỳ định dạng nào. Công cụ tự động phân tích cấu trúc dữ liệu và thực hiện thiết kế bố cục thông minh, hỗ trợ phân trang tự động bảng lớn và xử lý kiểu dữ liệu phức tạp."
    step3: "Tạo tệp bảng PDF chất lượng cao với hỗ trợ nhiều kiểu chủ đề chuyên nghiệp (kinh doanh, học thuật, tối giản, v.v.), phông chữ đa ngôn ngữ, phân trang tự động, thêm hình mờ và tối ưu hóa in. Đảm bảo tài liệu PDF đầu ra có giao diện chuyên nghiệp, có thể sử dụng trực tiếp cho bài thuyết trình kinh doanh và xuất bản chính thức."
    from_alias: "Dữ Liệu Bảng"
    to_alias: "Tài Liệu PDF Chuyên Nghiệp"
  JPEG:
    alias: "Hình Ảnh JPEG"
    what: "JPEG là định dạng hình ảnh kỹ thuật số được sử dụng rộng rãi nhất với hiệu ứng nén tuyệt vời và khả năng tương thích rộng rãi. Kích thước tệp nhỏ và tốc độ tải nhanh làm cho nó phù hợp để hiển thị web, chia sẻ mạng xã hội, minh họa tài liệu và thuyết trình trực tuyến. Định dạng hình ảnh tiêu chuẩn cho truyền thông kỹ thuật số và giao tiếp mạng, được hỗ trợ hoàn hảo bởi hầu hết các thiết bị và phần mềm."
    step1: "Nhập dữ liệu bảng ở bất kỳ định dạng nào. Công cụ thực hiện thiết kế bố cục thông minh và tối ưu hóa hình ảnh, tự động tính toán kích thước và độ phân giải tối ưu."
    step3: "Tạo hình ảnh bảng JPEG độ nét cao với hỗ trợ nhiều chủ đề màu sắc (sáng, tối, thân thiện với mắt, v.v.), bố cục thích ứng, tối ưu hóa độ rõ nét văn bản và tùy chỉnh kích thước. Phù hợp để chia sẻ trực tuyến, chèn tài liệu và sử dụng thuyết trình, đảm bảo hiệu ứng hình ảnh tuyệt vời trên các thiết bị hiển thị khác nhau."
    from_alias: "Dữ Liệu Bảng"
    to_alias: "Hình Ảnh JPEG Độ Nét Cao"
  Jira:
    alias: "Bảng Jira"
    what: "JIRA là phần mềm quản lý dự án và theo dõi lỗi chuyên nghiệp được phát triển bởi Atlassian, được sử dụng rộng rãi trong phát triển linh hoạt, kiểm thử phần mềm và hợp tác dự án. Chức năng bảng của nó hỗ trợ các tùy chọn định dạng phong phú và hiển thị dữ liệu, phục vụ như một công cụ quan trọng cho các nhóm phát triển phần mềm, quản lý dự án và nhân viên đảm bảo chất lượng trong quản lý yêu cầu, theo dõi lỗi và báo cáo tiến độ."
    step1: "Tải lên các tệp chứa dữ liệu bảng hoặc dán trực tiếp nội dung dữ liệu. Công cụ tự động xử lý dữ liệu bảng và thoát ký tự đặc biệt."
    step3: "Tạo mã bảng tương thích với nền tảng JIRA với hỗ trợ cài đặt kiểu tiêu đề, căn chỉnh ô, xử lý thoát ký tự và tối ưu hóa định dạng. Mã được tạo có thể được dán trực tiếp vào mô tả vấn đề JIRA, bình luận hoặc trang wiki, đảm bảo hiển thị và kết xuất chính xác trong hệ thống JIRA."
    from_alias: "Dữ Liệu Dự Án"
    to_alias: "Cú Pháp Bảng Jira"
  Textile:
    alias: "Bảng Textile"
    what: "Textile là ngôn ngữ đánh dấu nhẹ súc tích với cú pháp đơn giản và dễ học, được sử dụng rộng rãi trong hệ thống quản lý nội dung, nền tảng blog và hệ thống diễn đàn. Cú pháp bảng của nó rõ ràng và trực quan, hỗ trợ định dạng nhanh và cài đặt kiểu. Một công cụ lý tưởng cho người tạo nội dung và quản trị viên trang web để viết tài liệu nhanh và xuất bản nội dung."
    step1: "Tải lên tệp định dạng Textile hoặc dán dữ liệu bảng. Công cụ phân tích cú pháp đánh dấu Textile và trích xuất nội dung bảng."
    step3: "Tạo cú pháp bảng Textile chuẩn với hỗ trợ đánh dấu tiêu đề, căn chỉnh ô, thoát ký tự đặc biệt và tối ưu hóa định dạng. Mã được tạo có thể được sử dụng trực tiếp trong hệ thống CMS, nền tảng blog và hệ thống tài liệu hỗ trợ Textile, đảm bảo kết xuất và hiển thị nội dung chính xác."
    from_alias: "Tài Liệu Textile"
    to_alias: "Cú Pháp Bảng Textile"
  PNG:
    alias: "Hình Ảnh PNG"
    what: "PNG (Portable Network Graphics) là định dạng hình ảnh không mất dữ liệu với khả năng nén tuyệt vời và hỗ trợ độ trong suốt. Được sử dụng rộng rãi trong thiết kế web, đồ họa kỹ thuật số và nhiếp ảnh chuyên nghiệp. Chất lượng cao và khả năng tương thích rộng rãi của nó làm cho nó trở nên lý tưởng cho ảnh chụp màn hình, logo, sơ đồ và bất kỳ hình ảnh nào yêu cầu chi tiết rõ nét và nền trong suốt."
    step1: "Nhập dữ liệu bảng ở bất kỳ định dạng nào. Công cụ thực hiện thiết kế bố cục thông minh và tối ưu hóa hình ảnh, tự động tính toán kích thước và độ phân giải tối ưu cho đầu ra PNG."
    step3: "Tạo hình ảnh bảng PNG chất lượng cao với hỗ trợ nhiều chủ đề màu sắc, nền trong suốt, bố cục thích ứng và tối ưu hóa độ rõ nét văn bản. Hoàn hảo cho việc sử dụng web, chèn tài liệu và các bài thuyết trình chuyên nghiệp với chất lượng hình ảnh tuyệt vời."
    from_alias: "Dữ Liệu Bảng"
    to_alias: "Hình Ảnh PNG Chất Lượng Cao"
  TOML:
    alias: "Cấu Hình TOML"
    what: "TOML (Tom's Obvious, Minimal Language) là định dạng tệp cấu hình dễ đọc và viết. Được thiết kế để rõ ràng và đơn giản, nó được sử dụng rộng rãi trong các dự án phần mềm hiện đại để quản lý cấu hình. Cú pháp rõ ràng và kiểu dữ liệu mạnh mẽ của nó làm cho nó trở thành lựa chọn tuyệt vời cho cài đặt ứng dụng và tệp cấu hình dự án."
    step1: "Tải lên tệp TOML hoặc dán dữ liệu cấu hình. Công cụ phân tích cú pháp TOML và trích xuất thông tin cấu hình có cấu trúc."
    step3: "Tạo định dạng TOML chuẩn với hỗ trợ cấu trúc lồng nhau, kiểu dữ liệu và bình luận. Các tệp TOML được tạo hoàn hảo cho cấu hình ứng dụng, công cụ xây dựng và cài đặt dự án."
    from_alias: "Cấu Hình TOML"
    to_alias: "Định Dạng TOML"
  INI:
    alias: "Cấu Hình INI"
    what: "Tệp INI là các tệp cấu hình đơn giản được sử dụng bởi nhiều ứng dụng và hệ điều hành. Cấu trúc cặp khóa-giá trị đơn giản của chúng giúp chúng dễ đọc và chỉnh sửa thủ công. Được sử dụng rộng rãi trong các ứng dụng Windows, hệ thống cũ và các tình huống cấu hình đơn giản nơi khả năng đọc của con người là quan trọng."
    step1: "Tải lên tệp INI hoặc dán dữ liệu cấu hình. Công cụ phân tích cú pháp INI và trích xuất thông tin cấu hình dựa trên phần."
    step3: "Tạo định dạng INI chuẩn với hỗ trợ phần, bình luận và các kiểu dữ liệu khác nhau. Các tệp INI được tạo tương thích với hầu hết các ứng dụng và hệ thống cấu hình."
    from_alias: "Cấu Hình INI"
    to_alias: "Định Dạng INI"
  Avro:
    alias: "Lược Đồ Avro"
    what: "Apache Avro là hệ thống tuần tự hóa dữ liệu cung cấp cấu trúc dữ liệu phong phú, định dạng nhị phân compact và khả năng phát triển lược đồ. Được sử dụng rộng rãi trong xử lý dữ liệu lớn, hàng đợi tin nhắn và hệ thống phân tán. Định nghĩa lược đồ của nó hỗ trợ các kiểu dữ liệu phức tạp và khả năng tương thích phiên bản, làm cho nó trở thành công cụ quan trọng cho các kỹ sư dữ liệu và kiến trúc sư hệ thống."
    step1: "Tải lên tệp lược đồ Avro hoặc dán dữ liệu. Công cụ phân tích định nghĩa lược đồ Avro và trích xuất thông tin cấu trúc bảng."
    step3: "Tạo định nghĩa lược đồ Avro chuẩn với hỗ trợ ánh xạ kiểu dữ liệu, ràng buộc trường và xác thực lược đồ. Các lược đồ được tạo có thể được sử dụng trực tiếp trong hệ sinh thái Hadoop, hệ thống tin nhắn Kafka và các nền tảng dữ liệu lớn khác."
    from_alias: "Lược Đồ Avro"
    to_alias: "Định Dạng Dữ Liệu Avro"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) là cơ chế có thể mở rộng, trung lập ngôn ngữ, trung lập nền tảng của Google để tuần tự hóa dữ liệu có cấu trúc. Được sử dụng rộng rãi trong microservices, phát triển API và lưu trữ dữ liệu. Định dạng nhị phân hiệu quả và kiểu dữ liệu mạnh mẽ của nó làm cho nó lý tưởng cho các ứng dụng hiệu suất cao và giao tiếp đa ngôn ngữ."
    step1: "Tải lên tệp .proto hoặc dán định nghĩa Protocol Buffer. Công cụ phân tích cú pháp protobuf và trích xuất thông tin cấu trúc tin nhắn."
    step3: "Tạo định nghĩa Protocol Buffer chuẩn với hỗ trợ kiểu tin nhắn, tùy chọn trường và định nghĩa dịch vụ. Các tệp .proto được tạo có thể được biên dịch cho nhiều ngôn ngữ lập trình."
    from_alias: "Protocol Buffer"
    to_alias: "Lược Đồ Protobuf"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas là thư viện phân tích dữ liệu phổ biến nhất trong Python, với DataFrame là cấu trúc dữ liệu cốt lõi của nó. Nó cung cấp khả năng thao tác, làm sạch và phân tích dữ liệu mạnh mẽ, được sử dụng rộng rãi trong khoa học dữ liệu, học máy và trí tuệ kinh doanh. Một công cụ không thể thiếu cho các nhà phát triển Python và nhà phân tích dữ liệu."
    step1: "Tải lên tệp Python chứa mã DataFrame hoặc dán dữ liệu. Công cụ phân tích cú pháp Pandas và trích xuất thông tin cấu trúc DataFrame."
    step3: "Tạo mã Pandas DataFrame chuẩn với hỗ trợ đặc tả kiểu dữ liệu, cài đặt chỉ mục và các thao tác dữ liệu. Mã được tạo có thể được thực thi trực tiếp trong môi trường Python để phân tích và xử lý dữ liệu."
    from_alias: "Pandas DataFrame"
    to_alias: "Cấu Trúc Dữ Liệu Python"
  RDF:
    alias: "RDF Triple"
    what: "RDF (Resource Description Framework) là mô hình chuẩn cho trao đổi dữ liệu trên Web, được thiết kế để biểu diễn thông tin về tài nguyên dưới dạng đồ thị. Được sử dụng rộng rãi trong web ngữ nghĩa, đồ thị tri thức và ứng dụng dữ liệu liên kết. Cấu trúc ba thành phần của nó cho phép biểu diễn siêu dữ liệu phong phú và mối quan hệ ngữ nghĩa."
    step1: "Tải lên tệp RDF hoặc dán dữ liệu ba thành phần. Công cụ phân tích cú pháp RDF và trích xuất mối quan hệ ngữ nghĩa và thông tin tài nguyên."
    step3: "Tạo định dạng RDF chuẩn với hỗ trợ các cách tuần tự hóa khác nhau (RDF/XML, Turtle, N-Triples). RDF được tạo có thể được sử dụng trong ứng dụng web ngữ nghĩa, cơ sở tri thức và hệ thống dữ liệu liên kết."
    from_alias: "Dữ Liệu RDF"
    to_alias: "Định Dạng RDF Ngữ Nghĩa"
  MATLAB:
    alias: "Mảng MATLAB"
    what: "MATLAB là phần mềm tính toán số học và trực quan hóa hiệu suất cao được sử dụng rộng rãi trong tính toán kỹ thuật, phân tích dữ liệu và phát triển thuật toán. Các thao tác mảng và ma trận của nó rất mạnh mẽ, hỗ trợ tính toán toán học phức tạp và xử lý dữ liệu. Một công cụ thiết yếu cho các kỹ sư, nhà nghiên cứu và nhà khoa học dữ liệu."
    step1: "Tải lên tệp MATLAB .m hoặc dán dữ liệu mảng. Công cụ phân tích cú pháp MATLAB và trích xuất thông tin cấu trúc mảng."
    step3: "Tạo mã mảng MATLAB chuẩn với hỗ trợ mảng đa chiều, đặc tả kiểu dữ liệu và đặt tên biến. Mã được tạo có thể được thực thi trực tiếp trong môi trường MATLAB để phân tích dữ liệu và tính toán khoa học."
    from_alias: "Mảng MATLAB"
    to_alias: "Định Dạng Mã MATLAB"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame là cấu trúc dữ liệu cốt lõi trong ngôn ngữ lập trình R, được sử dụng rộng rãi trong phân tích thống kê, khai thác dữ liệu và học máy. R là công cụ hàng đầu cho tính toán thống kê và đồ họa, với DataFrame cung cấp khả năng thao tác dữ liệu mạnh mẽ, phân tích thống kê và trực quan hóa. Thiết yếu cho các nhà khoa học dữ liệu, nhà thống kê và nhà nghiên cứu làm việc với phân tích dữ liệu có cấu trúc."
    step1: "Tải lên tệp dữ liệu R hoặc dán mã DataFrame. Công cụ phân tích cú pháp R và trích xuất thông tin cấu trúc DataFrame bao gồm kiểu cột, tên hàng và nội dung dữ liệu."
    step3: "Tạo mã R DataFrame chuẩn với hỗ trợ đặc tả kiểu dữ liệu, mức độ yếu tố, tên hàng/cột và cấu trúc dữ liệu đặc thù của R. Mã được tạo có thể được thực thi trực tiếp trong môi trường R để phân tích thống kê và xử lý dữ liệu."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Bắt Đầu Chuyển Đổi"
  start_generating: "Bắt đầu tạo"
  api_docs: "Tài Liệu API"
related:
  section_title: 'Thêm Bộ Chuyển Đổi {{ if and .from (ne .from "generator") }}{{ .from }} và {{ end }}{{ .to }}'
  section_description: 'Khám phá thêm bộ chuyển đổi cho các định dạng {{ if and .from (ne .from "generator") }}{{ .from }} và {{ end }}{{ .to }}. Chuyển đổi dữ liệu của bạn giữa nhiều định dạng với các công cụ chuyển đổi trực tuyến chuyên nghiệp của chúng tôi.'
  title: "{{ .from }} sang {{ .to }}"
howto:
  step2: "Chỉnh sửa dữ liệu bằng trình chỉnh sửa bảng trực tuyến nâng cao với các tính năng chuyên nghiệp. Hỗ trợ xóa hàng trống, loại bỏ trùng lặp, chuyển vị dữ liệu, sắp xếp, tìm kiếm và thay thế regex, và xem trước thời gian thực. Tất cả thay đổi tự động chuyển đổi sang định dạng %s với kết quả chính xác, đáng tin cậy."
  section_title: "Cách sử dụng {{ . }}"
  converter_description: "Học cách chuyển đổi {{ .from }} sang {{ .to }} với hướng dẫn từng bước của chúng tôi. Bộ chuyển đổi trực tuyến chuyên nghiệp với các tính năng nâng cao và xem trước thời gian thực."
  generator_description: "Học cách tạo bảng {{ .to }} chuyên nghiệp với trình tạo trực tuyến của chúng tôi. Chỉnh sửa giống Excel, xem trước thời gian thực, và khả năng xuất tức thì."
extension:
  section_title: "Tiện Ích Mở Rộng Phát Hiện và Trích Xuất Bảng"
  section_description: "Trích xuất bảng từ bất kỳ trang web nào chỉ với một cú nhấp chuột. Chuyển đổi sang hơn 30 định dạng bao gồm Excel, CSV, JSON ngay lập tức - không cần sao chép dán."
  features:
    extraction_title: "Trích Xuất Bảng Một Cú Nhấp Chuột"
    extraction_description: "Trích xuất bảng ngay lập tức từ bất kỳ trang web nào mà không cần sao chép dán - trích xuất dữ liệu chuyên nghiệp được đơn giản hóa"
    formats_title: "Hỗ Trợ Bộ Chuyển Đổi Hơn 30 Định Dạng"
    formats_description: "Chuyển đổi bảng đã trích xuất sang Excel, CSV, JSON, Markdown, SQL, và nhiều hơn nữa với bộ chuyển đổi bảng nâng cao của chúng tôi"
    detection_title: "Phát Hiện Bảng Thông Minh"
    detection_description: "Tự động phát hiện và làm nổi bật bảng trên bất kỳ trang web nào để trích xuất và chuyển đổi dữ liệu nhanh chóng"
  hover_tip: "✨ Di chuột qua bất kỳ bảng nào để xem biểu tượng trích xuất"
recommendations:
  section_title: "Được Khuyến Nghị bởi Các Trường Đại Học và Chuyên Gia"
  section_description: "TableConvert được tin tưởng bởi các chuyên gia trong các trường đại học, viện nghiên cứu và nhóm phát triển để chuyển đổi bảng đáng tin cậy và xử lý dữ liệu."
  cards:
    university_title: "Đại học Wisconsin-Madison"
    university_description: "TableConvert.com - Công cụ chuyển đổi bảng trực tuyến miễn phí chuyên nghiệp và định dạng dữ liệu"
    university_link: "Đọc Bài Viết"
    facebook_title: "Cộng Đồng Chuyên Gia Dữ Liệu"
    facebook_description: "Được chia sẻ và khuyến nghị bởi các nhà phân tích dữ liệu và chuyên gia trong các nhóm phát triển Facebook"
    facebook_link: "Xem Bài Đăng"
    twitter_title: "Cộng Đồng Nhà Phát Triển"
    twitter_description: "Được khuyến nghị bởi @xiaoying_eth và các nhà phát triển khác trên X (Twitter) để chuyển đổi bảng"
    twitter_link: "Xem Tweet"
faq:
  section_title: "Câu Hỏi Thường Gặp"
  section_description: "Các câu hỏi phổ biến về bộ chuyển đổi bảng trực tuyến miễn phí, định dạng dữ liệu và quy trình chuyển đổi của chúng tôi."
  what: "Định dạng %s là gì?"
  howto_convert:
    question: "Cách sử dụng {{ . }} miễn phí?"
    answer: "Tải lên tệp {{ .from }} của bạn, dán dữ liệu, hoặc trích xuất từ trang web bằng bộ chuyển đổi bảng trực tuyến miễn phí của chúng tôi. Công cụ chuyển đổi chuyên nghiệp của chúng tôi ngay lập tức chuyển đổi dữ liệu của bạn sang định dạng {{ .to }} với xem trước thời gian thực và các tính năng chỉnh sửa nâng cao. Tải xuống hoặc sao chép kết quả đã chuyển đổi ngay lập tức."
  security:
    question: "Dữ liệu của tôi có an toàn khi sử dụng bộ chuyển đổi trực tuyến này không?"
    answer: "Hoàn toàn! Tất cả chuyển đổi bảng diễn ra cục bộ trong trình duyệt của bạn - dữ liệu của bạn không bao giờ rời khỏi thiết bị của bạn. Bộ chuyển đổi trực tuyến của chúng tôi xử lý mọi thứ ở phía máy khách, đảm bảo quyền riêng tư hoàn toàn và bảo mật dữ liệu. Không có tệp nào được lưu trữ trên máy chủ của chúng tôi."
  free:
    question: "TableConvert có thực sự miễn phí để sử dụng không?"
    answer: "Có, TableConvert hoàn toàn miễn phí! Tất cả các tính năng chuyển đổi, trình chỉnh sửa bảng, công cụ tạo dữ liệu và tùy chọn xuất đều có sẵn mà không tốn phí, đăng ký hoặc phí ẩn. Chuyển đổi tệp không giới hạn trực tuyến miễn phí."
  filesize:
    question: "Bộ chuyển đổi trực tuyến có giới hạn kích thước tệp nào?"
    answer: "Bộ chuyển đổi bảng trực tuyến miễn phí của chúng tôi hỗ trợ tệp lên đến 10MB. Đối với tệp lớn hơn, xử lý hàng loạt hoặc nhu cầu doanh nghiệp, hãy sử dụng tiện ích mở rộng trình duyệt hoặc dịch vụ API chuyên nghiệp của chúng tôi với giới hạn cao hơn."
stats:
  conversions: "Bảng Đã Chuyển Đổi"
  tables: "Bảng Đã Tạo"
  formats: "Định Dạng Tệp Dữ Liệu"
  rating: "Đánh Giá Người Dùng"
