site:
  fullname: "Table Convert"
  name: "TableConvert"
  subtitle: "Free Online Table Converter and Generator"
  intro: "TableConvert is a free online table converter and data generator tool supporting conversion between 30+ formats including Excel, CSV, JSON, Markdown, LaTeX, SQL and more."
  followTwitter: "Follow us on X"
title:
  converter: "%s to %s"
  generator: "%s Generator"
post:
  tags:
    converter: "Converter"
    editor: "Editor"
    generator: "Generator"
    maker: "Builder"
  converter:
    title: "Convert %s to %s Online"
    short: "A free and powerful %s to %s online tool"
    intro: "Easy-to-use online %s to %s converter. Transform table data effortlessly with our intuitive conversion tool. Fast, reliable, and user-friendly."
  generator:
    title: "Online %s Editor and Generator"
    short: "Professional %s online generation tool with comprehensive features"
    intro: "Easy-to-use online %s generator and table editor. Create professional data tables effortlessly with our intuitive tool and real-time preview."
navbar:
  search:
    placeholder: "Search converter ..."
  sponsor: "Buy Me a Coffee"
  extension: "Extension"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "Data Source"
    placeholder: "Paste your %s data or drag %s files here"
    example: "Example"
    upload: "Upload File"
    extract:
      enter: "Extract from Web Page"
      intro: "Enter a web page URL containing table data to automatically extract structured data"
      btn: "Extract %s"
    excel:
      sheet: "Worksheet"
      none: "None"
  tableEditor:
    title: "Online Table Editor"
    undo: "Undo"
    redo: "Redo"
    transpose: "Transpose"
    clear: "Clear"
    deleteBlank: "Delete Empty"
    deleteDuplicate: "Deduplicate"
    uppercase: "UPPERCASE"
    lowercase: "lowercase"
    capitalize: "Capitalize"
    replace:
      replace: "Find & Replace (Regex supported)"
      subst: "Replace with..."
      btn: "Replace All"
  tableGenerator:
    title: "Table Generator"
    sponsor: "Buy Me a Coffee"
    copy: "Copy to Clipboard"
    download: "Download File"
    tooltip:
      html:
        escape: "Escape HTML special characters (&, <, >, \", ') to prevent display errors"
        div: "Use DIV+CSS layout instead of traditional TABLE tags, better suited for responsive design"
        minify: "Remove whitespace and line breaks to generate compressed HTML code"
        thead: "Generate standard table head (&lt;thead&gt;) and body (&lt;tbody&gt;) structure"
        tableCaption: "Add descriptive title above the table (&lt;caption&gt; element)"
        tableClass: "Add CSS class name to the table for easy style customization"
        tableId: "Set unique ID identifier for the table for JavaScript manipulation"
      jira:
        escape: "Escape pipe characters (|) to avoid conflicts with Jira table syntax"
      json:
        parsingJSON: "Intelligently parse JSON strings in cells into objects"
        minify: "Generate compact single-line JSON format to reduce file size"
        format: "Select output JSON data structure: object array, 2D array, etc."
      latex:
        escape: "Escape LaTeX special characters (%, &, _, #, $, etc.) to ensure proper compilation"
        ht: "Add floating position parameter [!ht] to control table position on page"
        mwe: "Generate complete LaTeX document"
        tableAlign: "Set horizontal alignment of table on the page"
        tableBorder: "Configure table border style: no border, partial border, full border"
        label: "Set table label for \\ref{} command cross-referencing"
        caption: "Set table caption to display above or below the table"
        location: "Choose table caption display position: above or below"
        tableType: "Choose table environment type: tabular, longtable, array, etc."
      markdown:
        escape: "Escape Markdown special characters (*, _, |, \\, etc.) to avoid format conflicts"
        pretty: "Auto-align column widths to generate more beautiful table format"
        simple: "Use simplified syntax, omitting outer border vertical lines"
        boldFirstRow: "Make the first row text bold"
        boldFirstColumn: "Make the first column text bold"
        firstHeader: "Treat first row as header and add separator line"
        textAlign: "Set column text alignment: left, center, right"
        multilineHandling: "Multiline text handling: preserve line breaks, escape to \\n, use &lt;br&gt; tags"

        includeLineNumbers: "Add line number column on the left side of the table"
      magic:
        builtin: "Select predefined common template formats"
        rowsTpl: "<table> <tr> <th>Magic Syntax</th> <th>Description</th> <th>Support JS Methods</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>1th, 2th ... field of <b>h</b>eading, Aka {hA} {hB} ...</td> <td>String methods</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>1th, 2th ... field of current row, Aka {$A} {$B} ...</td> <td>String methods</td> </tr> <tr> <td>{F,} {F;}</td> <td>Split the current row by the string after <b>F</b></td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td>Line <b>N</b>umber of current <b>R</b>ow from 1 or 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>E</b>nd line <b>N</b>umber of <b>R</b>ows </td> <td></td> </tr> <tr> <td>{x ...}</td> <td>E<b>x</b>ecute JavaScript code, eg: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> Use backslash <b>\\</b> to output braces {...} </td> <td></td> </tr></table>"
        headerTpl: "Custom output template for header section"
        footerTpl: "Custom output template for footer section"
      textile:
        escape: "Escape Textile syntax characters (|, ., -, ^) to avoid format conflicts"
        rowHeader: "Set first row as header row"
        thead: "Add Textile syntax markers for table head and body"
      xml:
        escape: "Escape XML special characters (&lt;, &gt;, &amp;, \", ') to ensure valid XML"
        minify: "Generate compressed XML output, removing extra whitespace"
        rootElement: "Set XML root element tag name"
        rowElement: "Set XML element tag name for each row of data"
        declaration: "Add XML declaration header (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "Output data as XML attributes instead of child elements"
        cdata: "Wrap text content with CDATA to protect special characters"
        encoding: "Set character encoding format for XML document"
        indentation: "Choose XML indentation character: spaces or tabs"
      yaml:
        indentSize: "Set number of spaces for YAML hierarchy indentation (usually 2 or 4)"
        arrayStyle: "Array format: block (one item per line) or flow (inline format)"
        quotationStyle: "String quote style: no quotes, single quotes, double quotes"
      csv:
        bom: "Add UTF-8 byte order mark to help Excel and other software recognize encoding"
      excel:
        autoWidth: "Automatically adjust column width based on content"
        protectSheet: "Enable worksheet protection with password: tableconvert.com"
      sql:  
        primaryKey: "Specify primary key field name for CREATE TABLE statement"
        dialect: "Select database type, affecting quote and data type syntax"
      ascii:
        forceSep: "Force separator lines between each row of data"
        style: "Select ASCII table border drawing style"
        comment: "Add comment markers to wrap the entire table"
      mediawiki:
        minify: "Compress output code, removing extra whitespace"
        header: "Mark first row as header style"
        sort: "Enable table click sorting functionality"
      asciidoc:
        minify: "Compress AsciiDoc format output"
        firstHeader: "Set first row as header row"
        lastFooter: "Set last row as footer row"
        title: "Add title text to the table"
      tracwiki:
        rowHeader: "Set first row as header"
        colHeader: "Set first column as header"
      bbcode:
        minify: "Compress BBCode output format"
      restructuredtext:
        style: "Select reStructuredText table border style"
        forceSep: "Force separator lines"
      pdf:
        theme: "Select PDF table visual style for professional documents"
        headerColor: "Choose header background color for PDF tables"
        showHead: "Control header display across PDF pages"
        docTitle: "Optional title for the PDF document"
        docDescription: "Optional description text for PDF document"
    label:
      ascii:
        forceSep: "Row Separators"
        style: "Border Style"
        comment: "Comment Wrapper"
      restructuredtext:
        style: "Border Style"
        forceSep: "Force Separators"
      bbcode:
        minify: "Minify Output"
      csv:
        doubleQuote: "Double Quote Wrap"
        delimiter: "Field Delimiter"
        bom: "UTF-8 BOM"
        valueDelimiter: "Value Delimiter"
        rowDelimiter: "Row Delimiter"
        prefix: "Row Prefix"
        suffix: "Row Suffix"
      excel:
        autoWidth: "Auto Width"
        textFormat: "Text Format"
        protectSheet: "Protect Sheet"
        boldFirstRow: "Bold First Row"
        boldFirstColumn: "Bold First Column"
        sheetName: "Sheet Name"
      html:
        escape: "Escape HTML Characters"
        div: "DIV Table"
        minify: "Minify Code"
        thead: "Table Head Structure"
        tableCaption: "Table Caption"
        tableClass: "Table Class"
        tableId: "Table ID"
        rowHeader: "Row Header"
        colHeader: "Column Header"
      jira:
        escape: "Escape Characters"
        rowHeader: "Row Header"
        colHeader: "Column Header"
      json:
        parsingJSON: "Parse JSON"
        minify: "Minify Output"
        format: "Data Format"
        rootName: "Root Object Name"
        indentSize: "Indent Size"
      jsonlines:
        parsingJSON: "Parse JSON"
        format: "Data Format"
      latex:
        escape: "Escape LaTex Table Characters"
        ht: "Float Position"
        mwe: "Complete Document"
        tableAlign: "Table Alignment"
        tableBorder: "Border Style"
        label: "Reference Label"
        caption: "Table Caption"
        location: "Caption Position"
        tableType: "Table Type"
        boldFirstRow: "Bold First Row"
        boldFirstColumn: "Bold First Column"
        textAlign: "Text Alignment"
        borders: "Border Settings"
      markdown:
        escape: "Escape Characters"
        pretty: "Pretty Markdown Table"
        simple: "Simple Markdown Format"
        boldFirstRow: "Bold First Row"
        boldFirstColumn: "Bold First Column"
        firstHeader: "First Header"
        textAlign: "Text Alignment"
        multilineHandling: "Multiline Handling"

        includeLineNumbers: "Add Line Numbers"
        align: "Alignment"
      mediawiki:
        minify: "Minify Code"
        header: "Header Markup"
        sort: "Sortable"
      asciidoc:
        minify: "Minify Format"
        firstHeader: "First Header"
        lastFooter: "Last Footer"
        title: "Table Title"
      tracwiki:
        rowHeader: "Row Header"
        colHeader: "Column Header"
      sql:
        drop: "Drop Table (If Exists)"
        create: "Create Table"
        oneInsert: "Batch Insert"
        table: "Table Name"
        dialect: "Database Type"
        primaryKey: "Primary Key"
      magic:
        builtin: "Built-in Template"
        rowsTpl: "Row Template, Syntax ->"
        headerTpl: "Header Template"
        footerTpl: "Footer Template"
      textile:
        escape: "Escape Characters"
        rowHeader: "Row Header"
        thead: "Table Head Syntax"
      xml:
        escape: "Escape XML Characters"
        minify: "Minify Output"
        rootElement: "Root Element"
        rowElement: "Row Element"
        declaration: "XML Declaration"
        attributes: "Attribute Mode"
        cdata: "CDATA Wrapper"
        encoding: "Encoding"
        indentSize: "Indent Size"
      yaml:
        indentSize: "Indent Size"
        arrayStyle: "Array Style"
        quotationStyle: "Quote Style"
      pdf:
        theme: "PDF Table Theme"
        headerColor: "PDF Header Color"
        showHead: "PDF Header Display"
        docTitle: "PDF Document Title"
        docDescription: "PDF Document Description"

sidebar:
  all: "All Conversion Tools"
  dataSource:
    title: "Data Source"
    description:
      converter: "Import %s for conversion to %s. Supports file upload, online editing, and web data extraction."
      generator: "Create table data with support for multiple input methods including manual input, file import, and template generation."
  tableEditor:
    title: "Online Table Editor"
    description:
      converter: "Process %s online using our table editor. Excel-like operation experience with support for deleting empty rows, deduplication, sorting, and find & replace."
      generator: "Powerful online table editor providing Excel-like operation experience. Supports deleting empty rows, deduplication, sorting, and find & replace."
  tableGenerator:
    title: "Table Generator"
    description:
      converter: "Quickly generate %s with real-time preview of table generator. Rich export options, one-click copy & download."
      generator: "Export %s data in multiple formats to meet different usage scenarios. Supports custom options and real-time preview."
footer:
  changelog: "Changelog"
  sponsor: "Sponsors"
  contact: "Contact Us"
  privacyPolicy: "Privacy Policy"
  about: "About"
  resources: "Resources"
  popularConverters: "Popular Converters"
  popularGenerators: "Popular Generators"
  dataSecurity: "Your data is secure - all conversions run in your browser."
converters:
  Markdown:
    alias: "Markdown Table"
    what: "Markdown is a lightweight markup language widely used for technical documentation, blog content creation, and web development. Its table syntax is concise and intuitive, supporting text alignment, link embedding, and formatting. It's the preferred tool for programmers and technical writers, perfectly compatible with GitHub, GitLab, and other code hosting platforms."
    step1: "Paste Markdown table data into the data source area, or directly drag and drop .md files for upload. The tool automatically parses table structure and formatting, supporting complex nested content and special character handling."
    step3: "Generate standard Markdown table code in real-time, supporting multiple alignment methods, text bolding, line number addition, and other advanced format settings. The generated code is fully compatible with GitHub and major Markdown editors, ready to use with one-click copy."
    from_alias: "Markdown Table File"
    to_alias: "Markdown Table Format"
  Magic:
    alias: "Custom Template"
    what: "Magic template is a unique advanced data generator of this tool, allowing users to create arbitrary format data output through custom template syntax. Supports variable replacement, conditional judgment, and loop processing. It's the ultimate solution for handling complex data conversion needs and personalized output formats, especially suitable for developers and data engineers."
    step1: "Select built-in common templates or create custom template syntax. Supports rich variables and functions that can handle complex data structures and business logic."
    step3: "Generate data output that fully meets custom format requirements. Supports complex data conversion logic and conditional processing, greatly improving data processing efficiency and output quality. A powerful tool for batch data processing."
    from_alias: "Table Data"
    to_alias: "Custom Format Output"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) is the most widely used data exchange format, perfectly supported by Excel, Google Sheets, database systems, and various data analysis tools. Its simple structure and strong compatibility make it the standard format for data migration, batch import/export, and cross-platform data exchange, widely used in business analysis, data science, and system integration."
    step1: "Upload CSV files or directly paste CSV data. The tool intelligently recognizes various delimiters (comma, tab, semicolon, pipe, etc.), automatically detects data types and encoding formats, supporting fast parsing of large files and complex data structures."
    step3: "Generate standard CSV format files with support for custom delimiters, quote styles, encoding formats, and BOM mark settings. Ensures perfect compatibility with target systems, providing download and compression options to meet enterprise-level data processing needs."
    from_alias: "CSV Data File"
    to_alias: "CSV Standard Format"
  JSON:
    alias: "JSON Array"
    what: "JSON (JavaScript Object Notation) is the standard table data format for modern web applications, REST APIs, and microservice architectures. Its clear structure and efficient parsing make it widely used in front-end and back-end data interaction, configuration file storage, and NoSQL databases. Supports nested objects, array structures, and multiple data types, making it indispensable table data for modern software development."
    step1: "Upload JSON files or paste JSON arrays. Supports automatic recognition and parsing of object arrays, nested structures, and complex data types. The tool intelligently validates JSON syntax and provides error prompts."
    step3: "Generate multiple JSON format outputs: standard object arrays, 2D arrays, column arrays, and key-value pair formats. Supports beautified output, compression mode, custom root object names, and indentation settings, perfectly adapting to various API interfaces and data storage needs."
    from_alias: "JSON Array File"
    to_alias: "JSON Standard Format"
  JSONLines:
    alias: "JSONLines Format"
    what: "JSON Lines (also known as NDJSON) is an important format for big data processing and streaming data transmission, with each line containing an independent JSON object. Widely used in log analysis, data stream processing, machine learning, and distributed systems. Supports incremental processing and parallel computing, making it the ideal choice for handling large-scale structured data."
    step1: "Upload JSONLines files or paste data. The tool parses JSON objects line by line, supporting large file streaming processing and error line skipping functionality."
    step3: "Generate standard JSONLines format with each line outputting a complete JSON object. Suitable for streaming processing, batch import, and big data analysis scenarios, supporting data validation and format optimization."
    from_alias: "JSONLines Data"
    to_alias: "JSONLines Streaming Format"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) is the standard format for enterprise-level data exchange and configuration management, with strict syntax specifications and powerful validation mechanisms. Widely used in web services, configuration files, document storage, and system integration. Supports namespaces, schema validation, and XSLT transformation, making it important table data for enterprise applications."
    step1: "Upload XML files or paste XML data. The tool automatically parses XML structure and converts it to table format, supporting namespace, attribute handling, and complex nested structures."
    step3: "Generate XML output that complies with XML standards. Supports custom root elements, row element names, attribute modes, CDATA wrapping, and character encoding settings. Ensures data integrity and compatibility, meeting enterprise-level application requirements."
    from_alias: "XML Data File"
    to_alias: "XML Standard Format"
  YAML:
    alias: "YAML Configuration"
    what: "YAML is a human-friendly data serialization standard, renowned for its clear hierarchical structure and concise syntax. Widely used in configuration files, DevOps tool chains, Docker Compose, and Kubernetes deployment. Its strong readability and concise syntax make it an important configuration format for modern cloud-native applications and automated operations."
    step1: "Upload YAML files or paste YAML data. The tool intelligently parses YAML structure and validates syntax correctness, supporting multi-document formats and complex data types."
    step3: "Generate standard YAML format output with support for block and flow array styles, multiple quote settings, custom indentation, and comment preservation. Ensures output YAML files are fully compatible with various parsers and configuration systems."
    from_alias: "YAML Configuration File"
    to_alias: "YAML Standard Format"
  MySQL:
      alias: "MySQL Query Results"
      what: "MySQL is the world's most popular open-source relational database management system, renowned for its high performance, reliability, and ease of use. Widely used in web applications, enterprise systems, and data analysis platforms. MySQL query results typically contain structured table data, serving as an important data source in database management and data analysis work."
      step1: "Paste MySQL query output results into the data source area. The tool automatically recognizes and parses MySQL command-line output format, supporting various query result styles and character encodings, intelligently handling headers and data rows."
      step3: "Quickly convert MySQL query results to multiple table data formats, facilitating data analysis, report generation, cross-system data migration, and data validation. A practical tool for database administrators and data analysts."
      from_alias: "MySQL Query Output"
      to_alias: "MySQL Table Data"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) is the standard operation language for relational databases, used for data query, insert, update, and delete operations. As the core technology of database management, SQL is widely used in data analysis, business intelligence, ETL processing, and data warehouse construction. It's an essential skill tool for data professionals."
    step1: "Paste INSERT SQL statements or upload .sql files. The tool intelligently parses SQL syntax and extracts table data, supporting multiple SQL dialects and complex query statement processing."
    step3: "Generate standard SQL INSERT statements and table creation statements. Supports multiple database dialects (MySQL, PostgreSQL, SQLite, SQL Server, Oracle), automatically handles data type mapping, character escaping, and primary key constraints. Ensures generated SQL code can be executed directly."
    from_alias: "Insert SQL"
    to_alias: "SQL Statement"
  Qlik:
      alias: "Qlik Table"
      what: "Qlik is a software vendor specializing in data visualization, executive dashboards, and self-service business intelligence products, along with Tableau and Microsoft."
      step1: ""
      step3: "Finally, the [Table Generator](#TableGenerator) shows the conversion results. Use in your Qlik Sense, Qlik AutoML, QlikView, or other Qlik-enabled software."
      from_alias: "Qlik Table"
      to_alias: "Qlik Table"
  DAX:
      alias: "DAX Table"
      what: "DAX (Data Analysis Expressions) is a programming language used throughout Microsoft Power BI for creating calculated columns, measures, and custom tables."
      step1: ""
      step3: "Finally, the [Table Generator](#TableGenerator) shows the conversion results. As expected, it's used in several Microsoft products including Microsoft Power BI, Microsoft Analysis Services, and Microsoft Power Pivot for Excel."
      from_alias: "DAX Table"
      to_alias: "DAX Table"
  Firebase:
    alias: "Firebase List"
    what: "Firebase is a BaaS application development platform that provides hosted backend services such as real-time database, cloud storage, authentication, crash reporting, etc."
    step1: ""
    step3: "Finally, the [Table Generator](#TableGenerator) shows the conversion results. You can then use the push method in the Firebase API to add to a list of data in the Firebase database."
    from_alias: "Firebase List"
    to_alias: "Firebase List"
  HTML:
    alias: "HTML Table"
    what: "HTML tables are the standard way to display structured data in web pages, built with table, tr, td and other tags. Supports rich style customization, responsive layout, and interactive functionality. Widely used in website development, data display, and report generation, serving as an important component of front-end development and web design."
    step1: "Paste HTML code containing tables or upload HTML files. The tool automatically recognizes and extracts table data from pages, supporting complex HTML structures, CSS styles, and nested table processing."
    step3: "Generate semantic HTML table code with support for thead/tbody structure, CSS class settings, table captions, row/column headers, and responsive attribute configuration. Ensures generated table code meets web standards with good accessibility and SEO friendliness."
    from_alias: "HTML Table"
    to_alias: "HTML Table"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel is the world's most popular spreadsheet software, widely used in business analysis, financial management, data processing, and report creation. Its powerful data processing capabilities, rich function library, and flexible visualization features make it the standard tool for office automation and data analysis, with extensive applications across almost all industries and fields."
    step1: "Upload Excel files (supports .xlsx, .xls formats) or copy table data directly from Excel and paste. The tool supports multi-worksheet processing, complex format recognition, and fast parsing of large files, automatically handling merged cells and data types."
    step3: "Generate Excel-compatible table data that can be directly pasted into Excel or downloaded as standard .xlsx files. Supports worksheet naming, cell formatting, auto column width, header styling, and data validation settings. Ensures output Excel files have professional appearance and complete functionality."
    from_alias: "Excel Spreadsheet"
    to_alias: "Excel"
  LaTeX:
    alias: "LaTeX Table"
    what: "LaTeX is a professional document typesetting system, especially suitable for creating academic papers, technical documents, and scientific publications. Its table functionality is powerful, supporting complex mathematical formulas, precise layout control, and high-quality PDF output. It's the standard tool in academia and scientific publishing, widely used in journal papers, dissertations, and technical manual typesetting."
    step1: "Paste LaTeX table code or upload .tex files. The tool parses LaTeX table syntax and extracts data content, supporting multiple table environments (tabular, longtable, array, etc.) and complex format commands."
    step3: "Generate professional LaTeX table code with support for multiple table environment selection, border style configuration, caption position settings, document class specification, and package management. Can generate complete compilable LaTeX documents, ensuring output tables meet academic publishing standards."
    from_alias: "LaTeX Table"
    to_alias: "LaTeX Table"
  ASCII:
    alias: "ASCII Text Table"
    what: "ASCII tables use plain text characters to draw table borders and structures, providing the best compatibility and portability. Compatible with all text editors, terminal environments, and operating systems. Widely used in code documentation, technical manuals, README files, and command-line tool output. The preferred data display format for programmers and system administrators."
    step1: "Upload text files containing ASCII tables or directly paste table data. The tool intelligently recognizes and parses ASCII table structures, supporting multiple border styles and alignment formats."
    step3: "Generate beautiful plain text ASCII tables with support for multiple border styles (single line, double line, rounded corners, etc.), text alignment methods, and auto column width. Generated tables display perfectly in code editors, documents, and command lines."
    from_alias: "ASCII Text Table"
    to_alias: "ASCII Text Table"
  MediaWiki:
    alias: "MediaWiki Table"
    what: "MediaWiki is the open-source software platform used by famous wiki sites like Wikipedia. Its table syntax is concise yet powerful, supporting table style customization, sorting functionality, and link embedding. Widely used in knowledge management, collaborative editing, and content management systems, serving as core technology for building wiki encyclopedias and knowledge bases."
    step1: "Paste MediaWiki table code or upload wiki source files. The tool parses wiki markup syntax and extracts table data, supporting complex wiki syntax and template processing."
    step3: "Generate standard MediaWiki table code with support for header style settings, cell alignment, sorting functionality enabling, and code compression options. Generated code can be directly used for wiki page editing, ensuring perfect display on MediaWiki platforms."
    from_alias: "MediaWiki Table"
    to_alias: "MediaWiki Table"
  TracWiki:
    alias: "TracWiki Table"
    what: "Trac is a web-based project management and bug tracking system that uses simplified wiki syntax to create table content."
    step1: "Upload TracWiki files or paste table data."
    step3: "Generate TracWiki-compatible table code with support for row/column header settings, facilitating project document management."
    from_alias: "TracWiki Table"
    to_alias: "TracWiki Table"
  AsciiDoc:
    alias: "AsciiDoc Table"
    what: "AsciiDoc is a lightweight markup language that can be converted to HTML, PDF, manual pages, and other formats, widely used for technical documentation writing."
    step1: "Upload AsciiDoc files or paste data."
    step3: "Generate AsciiDoc table syntax with support for header, footer, and title settings, directly usable in AsciiDoc editors."
    from_alias: "AsciiDoc Table"
    to_alias: "AsciiDoc Table"
  reStructuredText:
    alias: "reStructuredText Table"
    what: "reStructuredText is the standard documentation format for the Python community, supporting rich table syntax, commonly used for Sphinx documentation generation."
    step1: "Upload .rst files or paste reStructuredText data."
    step3: "Generate standard reStructuredText tables with support for multiple border styles, directly usable in Sphinx documentation projects."
    from_alias: "reStructuredText Table"
    to_alias: "reStructuredText Table"
  PHP:
    alias: "PHP Array"
    what: "PHP is a popular server-side scripting language, with arrays being its core data structure, widely used in web development and data processing."
    step1: "Upload files containing PHP arrays or directly paste data."
    step3: "Generate standard PHP array code that can be directly used in PHP projects, supporting associative and indexed array formats."
    from_alias: "PHP Array"
    to_alias: "PHP Code"
  Ruby:
    alias: "Ruby Array"
    what: "Ruby is a dynamic object-oriented programming language with concise and elegant syntax, with arrays being an important data structure."
    step1: "Upload Ruby files or paste array data."
    step3: "Generate Ruby array code that complies with Ruby syntax specifications, directly usable in Ruby projects."
    from_alias: "Ruby Array"
    to_alias: "Ruby Code"
  ASP:
    alias: "ASP Array"
    what: "ASP (Active Server Pages) is Microsoft's server-side scripting environment, supporting multiple programming languages for developing dynamic web pages."
    step1: "Upload ASP files or paste array data."
    step3: "Generate ASP-compatible array code with support for VBScript and JScript syntax, usable in ASP.NET projects."
    from_alias: "ASP Array"
    to_alias: "ASP Code"
  ActionScript:
    alias: "ActionScript Array"
    what: "ActionScript is an object-oriented programming language primarily used for Adobe Flash and AIR application development."
    step1: "Upload .as files or paste ActionScript data."
    step3: "Generate ActionScript array code that complies with AS3 syntax standards, usable for Flash and Flex project development."
    from_alias: "ActionScript Array"
    to_alias: "ActionScript Code"
  BBCode:
    alias: "BBCode Table"
    what: "BBCode is a lightweight markup language commonly used in forums and online communities, providing simple formatting functionality including table support."
    step1: "Upload files containing BBCode or paste data."
    step3: "Generate BBCode table code suitable for forum posting and community content creation, with support for compressed output format."
    from_alias: "BBCode Table"
    to_alias: "BBCode Table"
  PDF:
    alias: "PDF Table"
    what: "PDF (Portable Document Format) is a cross-platform document standard with fixed layout, consistent display, and high-quality printing characteristics. Widely used in formal documents, reports, invoices, contracts, and academic papers. The preferred format for business communication and document archiving, ensuring completely consistent visual effects across different devices and operating systems."
    step1: "Import table data in any format. The tool automatically analyzes data structure and performs intelligent layout design, supporting large table auto-pagination and complex data type processing."
    step3: "Generate high-quality PDF table files with support for multiple professional theme styles (business, academic, minimalist, etc.), multilingual fonts, auto-pagination, watermark addition, and print optimization. Ensures output PDF documents have professional appearance, directly usable for business presentations and formal publication."
    from_alias: "Table Data"
    to_alias: "PDF Table"
  JPEG:
    alias: "JPEG Image"
    what: "JPEG is the most widely used digital image format with excellent compression effects and broad compatibility. Its small file size and fast loading speed make it suitable for web display, social media sharing, document illustrations, and online presentations. The standard image format for digital media and network communication, perfectly supported by almost all devices and software."
    step1: "Import table data in any format. The tool performs intelligent layout design and visual optimization, automatically calculating optimal size and resolution."
    step3: "Generate high-definition JPEG table images with support for multiple theme color schemes (light, dark, eye-friendly, etc.), adaptive layout, text clarity optimization, and size customization. Suitable for online sharing, document insertion, and presentation use, ensuring excellent visual effects on various display devices."
    from_alias: "Table Data"
    to_alias: "JPEG Image"
  Jira:
    alias: "Jira Table"
    what: "JIRA is professional project management and bug tracking software developed by Atlassian, widely used in agile development, software testing, and project collaboration. Its table functionality supports rich formatting options and data display, serving as an important tool for software development teams, project managers, and quality assurance personnel in requirement management, bug tracking, and progress reporting."
    step1: "Upload files containing table data or directly paste data content. The tool automatically processes table data and special character escaping."
    step3: "Generate JIRA platform-compatible table code with support for header style settings, cell alignment, character escape processing, and format optimization. Generated code can be directly pasted into JIRA issue descriptions, comments, or wiki pages, ensuring correct display and rendering in JIRA systems."
    from_alias: "Jira Table"
    to_alias: "Jira Table"
  Textile:
    alias: "Textile Table"
    what: "Textile is a concise lightweight markup language with simple and easy-to-learn syntax, widely used in content management systems, blog platforms, and forum systems. Its table syntax is clear and intuitive, supporting quick formatting and style settings. An ideal tool for content creators and website administrators for rapid document writing and content publishing."
    step1: "Upload Textile format files or paste table data. The tool parses Textile markup syntax and extracts table content."
    step3: "Generate standard Textile table syntax with support for header markup, cell alignment, special character escaping, and format optimization. Generated code can be directly used in CMS systems, blog platforms, and document systems that support Textile, ensuring correct content rendering and display."
    from_alias: "Textile Table"
    to_alias: "Textile Table"
  PNG:
    alias: "PNG Image"
    what: "PNG (Portable Network Graphics) is a lossless image format with excellent compression and transparency support. Widely used in web design, digital graphics, and professional photography. Its high quality and broad compatibility make it ideal for screenshots, logos, diagrams, and any images requiring crisp details and transparent backgrounds."
    step1: "Import table data in any format. The tool performs intelligent layout design and visual optimization, automatically calculating optimal size and resolution for PNG output."
    step3: "Generate high-quality PNG table images with support for multiple theme color schemes, transparent backgrounds, adaptive layout, and text clarity optimization. Perfect for web use, document insertion, and professional presentations with excellent visual quality."
    from_alias: ""
    to_alias: "PNG Image"
  TOML:
    alias: "TOML"
    what: "TOML (Tom's Obvious, Minimal Language) is a configuration file format that's easy to read and write. Designed to be unambiguous and simple, it's widely used in modern software projects for configuration management. Its clear syntax and strong typing make it an excellent choice for application settings and project configuration files."
    step1: "Upload TOML files or paste configuration data. The tool parses TOML syntax and extracts structured configuration information."
    step3: "Generate standard TOML format with support for nested structures, data types, and comments. Generated TOML files are perfect for application configuration, build tools, and project settings."
    from_alias: "TOML"
    to_alias: "TOML Format"
  INI:
    alias: "INI"
    what: "INI files are simple configuration files used by many applications and operating systems. Their straightforward key-value pair structure makes them easy to read and edit manually. Widely used in Windows applications, legacy systems, and simple configuration scenarios where human readability is important."
    step1: "Upload INI files or paste configuration data. The tool parses INI syntax and extracts section-based configuration information."
    step3: "Generate standard INI format with support for sections, comments, and various data types. Generated INI files are compatible with most applications and configuration systems."
    from_alias: "INI"
    to_alias: "INI Format"
  Avro:
    alias: "Avro Schema"
    what: "Apache Avro is a data serialization system that provides rich data structures, compact binary format, and schema evolution capabilities. Widely used in big data processing, message queues, and distributed systems. Its schema definition supports complex data types and version compatibility, making it an important tool for data engineers and system architects."
    step1: "Upload Avro schema files or paste data. The tool parses Avro schema definitions and extracts table structure information."
    step3: "Generate standard Avro schema definitions with support for data type mapping, field constraints, and schema validation. Generated schemas can be directly used in Hadoop ecosystems, Kafka message systems, and other big data platforms."
    from_alias: "Avro Schema"
    to_alias: "Avro Schema"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) is Google's language-neutral, platform-neutral, extensible mechanism for serializing structured data. Widely used in microservices, API development, and data storage. Its efficient binary format and strong typing make it ideal for high-performance applications and cross-language communication."
    step1: "Upload .proto files or paste Protocol Buffer definitions. The tool parses protobuf syntax and extracts message structure information."
    step3: "Generate standard Protocol Buffer definitions with support for message types, field options, and service definitions. Generated .proto files can be compiled for multiple programming languages."
    from_alias: "Protocol Buffer"
    to_alias: "Protobuf Schema"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas is the most popular data analysis library in Python, with DataFrame being its core data structure. It provides powerful data manipulation, cleaning, and analysis capabilities, widely used in data science, machine learning, and business intelligence. An indispensable tool for Python developers and data analysts."
    step1: "Upload Python files containing DataFrame code or paste data. The tool parses Pandas syntax and extracts DataFrame structure information."
    step3: "Generate standard Pandas DataFrame code with support for data type specifications, index settings, and data operations. Generated code can be directly executed in Python environment for data analysis and processing."
    from_alias: "Pandas DataFrame"
    to_alias: "Pandas DataFrame"
  RDF:
    alias: "RDF Triple"
    what: "RDF (Resource Description Framework) is a standard model for data interchange on the Web, designed to represent information about resources in a graph form. Widely used in semantic web, knowledge graphs, and linked data applications. Its triple structure enables rich metadata representation and semantic relationships."
    step1: "Upload RDF files or paste triple data. The tool parses RDF syntax and extracts semantic relationships and resource information."
    step3: "Generate standard RDF format with support for various serializations (RDF/XML, Turtle, N-Triples). Generated RDF can be used in semantic web applications, knowledge bases, and linked data systems."
    from_alias: "RDF"
    to_alias: "RDF Triple"
  MATLAB:
    alias: "MATLAB Array"
    what: "MATLAB is a high-performance numerical computing and visualization software widely used in engineering computing, data analysis, and algorithm development. Its array and matrix operations are powerful, supporting complex mathematical calculations and data processing. An essential tool for engineers, researchers, and data scientists."
    step1: "Upload MATLAB .m files or paste array data. The tool parses MATLAB syntax and extracts array structure information."
    step3: "Generate standard MATLAB array code with support for multi-dimensional arrays, data type specifications, and variable naming. Generated code can be directly executed in MATLAB environment for data analysis and scientific computing."
    from_alias: "MATLAB Array"
    to_alias: "MATLAB Array"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame is the core data structure in the R programming language, widely used in statistical analysis, data mining, and machine learning. R is the premier tool for statistical computing and graphics, with DataFrame providing powerful data manipulation, statistical analysis, and visualization capabilities. Essential for data scientists, statisticians, and researchers working with structured data analysis."
    step1: "Upload R data files or paste DataFrame code. The tool parses R syntax and extracts DataFrame structure information including column types, row names, and data content."
    step3: "Generate standard R DataFrame code with support for data type specifications, factor levels, row/column names, and R-specific data structures. Generated code can be directly executed in R environment for statistical analysis and data processing."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "Start Converting"
  start_generating: "Start Generating"
  api_docs: "API Docs"
related:
  section_title: 'More {{ if and .from (ne .from "generator") }}{{ .from }} and {{ end }}{{ .to }} Converters'
  section_description: 'Explore more converters for {{ if and .from (ne .from "generator") }}{{ .from }} and {{ end }}{{ .to }} formats. Transform your data between multiple formats with our professional online conversion tools.'
  title: "{{ .from }} to {{ .to }}"
howto:
  step2: "Edit data using our advanced online table editor with professional features. Supports deleting empty rows, removing duplicates, data transposition, sorting, regex find & replace, and real-time preview. All changes automatically convert to %s format with precise, reliable results."
  section_title: "How to use the {{ . }}"
  converter_description: "Learn to convert {{ .from }} to {{ .to }} with our step-by-step guide. Professional online converter with advanced features and real-time preview."
  generator_description: "Learn to create professional {{ .to }} tables with our online generator. Excel-like editing, real-time preview, and instant export capabilities."
extension:
  section_title: "Table Detection & Extraction Extension"
  section_description: "Extract tables from any website with one click. Convert to 30+ formats including Excel, CSV, JSON instantly - no copy-pasting required."
  features:
    extraction_title: "One-Click Table Extraction"
    extraction_description: "Instantly extract tables from any webpage without copy-pasting - professional data extraction made simple"
    formats_title: "30+ Format Converter Support"
    formats_description: "Convert extracted tables to Excel, CSV, JSON, Markdown, SQL, and more with our advanced table converter"
    detection_title: "Smart Table Detection"
    detection_description: "Automatically detects and highlights tables on any webpage for fast data extraction and conversion"
  hover_tip: "✨ Hover over any table to see the extraction icon"
recommendations:
  section_title: "Recommended by Universities & Professionals"
  section_description: "TableConvert is trusted by professionals across universities, research institutions, and development teams for reliable table conversion and data processing."
  cards:
    university_title: "University of Wisconsin-Madison"
    university_description: "TableConvert.com - Professional free online table converter and data formats tool"
    university_link: "Read Article"
    facebook_title: "Data Professional Community"
    facebook_description: "Shared and recommended by data analysts and professionals in Facebook developer groups"
    facebook_link: "View Post"
    twitter_title: "Developer Community"
    twitter_description: "Recommended by @xiaoying_eth and other developers on X (Twitter) for table conversion"
    twitter_link: "View Tweet"
faq:
  section_title: "Frequently Asked Questions"
  section_description: "Common questions about our free online table converter, data formats, and conversion process."
  what: "What is %s format?"
  howto_convert:
    question: "How to use the {{ . }} for free?"
    answer: "Upload your {{ .from }} file, paste data, or extract from web pages using our free online table converter. Our professional converter tool instantly transforms your data into {{ .to }} format with real-time preview and advanced editing features. Download or copy the converted result immediately."
  security:
    question: "Is my data secure when using this online converter?"
    answer: "Absolutely! All table conversions happen locally in your browser - your data never leaves your device. Our online converter processes everything client-side, ensuring complete privacy and data security. No files are stored on our servers."
  free:
    question: "Is TableConvert really free to use?"
    answer: "Yes, TableConvert is completely free! All converter features, table editor, data generator tools, and export options are available without cost, registration, or hidden fees. Convert unlimited files online for free."
  filesize:
    question: "What file size limits does the online converter have?"
    answer: "Our free online table converter supports files up to 10MB. For larger files, batch processing, or enterprise needs, use our browser extension or professional API service with higher limits."
stats:
  conversions: "Tables Converted"
  tables: "Tables Generated"
  formats: "Data File Formats"
  rating: "User Rating"
