site:
  fullname: "Table Convert"
  name: "TableConvert"
  subtitle: "ממיר וגנרטור טבלאות מקוון חינמי"
  intro: "TableConvert הוא כלי חינמי מקוון להמרת טבלאות וגנרטור נתונים התומך בהמרה בין 30+ פורמטים כולל Excel, CSV, JSON, Markdown, LaTeX, SQL ועוד."
  followTwitter: "עקבו אחרינו ב-X"
title:
  converter: "%s ל-%s"
  generator: "גנרטור %s"
post:
  tags:
    converter: "ממיר"
    editor: "עורך"
    generator: "גנרטור"
    maker: "בונה"
  converter:
    title: "המר %s ל-%s מקוון"
    short: "כלי מקוון חינמי וחזק %s ל-%s"
    intro: "ממיר מקוון %s ל-%s קל לשימוש. המר נתוני טבלאות ללא מאמץ עם כלי ההמרה האינטואיטיבי שלנו. מהיר, אמין וידידותי למשתמש."
  generator:
    title: "עורך וגנרטור %s מקוון"
    short: "כלי יצירה מקוון מקצועי %s עם תכונות מקיפות"
    intro: "גנרטור מקוון %s ועורך טבלאות קל לשימוש. צור טבלאות נתונים מקצועיות ללא מאמץ עם הכלי האינטואיטיבי שלנו ותצוגה מקדימה בזמן אמת."
navbar:
  search:
    placeholder: "חפש ממיר ..."
  sponsor: "קנה לי קפה"
  extension: "הרחבה"
  api: "API"
converter:
  extensionAd:
    message: "Extract web tables with one click using Table Detection & Extraction"
  dataSource:
    title: "מקור נתונים"
    placeholder: "הדבק את נתוני ה-%s שלך או גרור קבצי %s כאן"
    example: "דוגמה"
    upload: "העלה קובץ"
    extract:
      enter: "חלץ מדף אינטרנט"
      intro: "הזן כתובת URL של דף אינטרנט המכיל נתוני טבלה כדי לחלץ אוטומטית נתונים מובנים"
      btn: "חלץ %s"
    excel:
      sheet: "גיליון עבודה"
      none: "ללא"
  tableEditor:
    title: "עורך טבלאות מקוון"
    undo: "בטל"
    redo: "בצע שוב"
    transpose: "טרנספוזיציה"
    clear: "נקה"
    deleteBlank: "מחק ריקים"
    deleteDuplicate: "הסר כפילויות"
    uppercase: "אותיות גדולות"
    lowercase: "אותיות קטנות"
    capitalize: "אות ראשונה גדולה"
    replace:
      replace: "חפש והחלף (תמיכה ב-Regex)"
      subst: "החלף עם..."
      btn: "החלף הכל"
  tableGenerator:
    title: "גנרטור טבלאות"
    sponsor: "קנה לי קפה"
    copy: "העתק ללוח"
    download: "הורד קובץ"
    tooltip:
      html:
        escape: "בריחה מתווים מיוחדים של HTML (&, <, >, \", ') למניעת שגיאות תצוגה"
        div: "השתמש בפריסת DIV+CSS במקום תגי TABLE מסורתיים, מתאים יותר לעיצוב רספונסיבי"
        minify: "הסר רווחים ומעברי שורה ליצירת קוד HTML דחוס"
        thead: "צור מבנה ראש טבלה סטנדרטי (&lt;thead&gt;) וגוף (&lt;tbody&gt;)"
        tableCaption: "הוסף כותרת תיאורית מעל הטבלה (אלמנט &lt;caption&gt;)"
        tableClass: "הוסף שם מחלקת CSS לטבלה להתאמה קלה של הסגנון"
        tableId: "קבע מזהה ID ייחודי לטבלה למניפולציה של JavaScript"
      jira:
        escape: "בריחה מתווי pipe (|) למניעת קונפליקטים עם תחביר טבלת Jira"
      json:
        parsingJSON: "ניתוח חכם של מחרוזות JSON בתאים לאובייקטים"
        minify: "צור פורמט JSON קומפקטי בשורה אחת להקטנת גודל הקובץ"
        format: "בחר מבנה נתוני JSON פלט: מערך אובייקטים, מערך דו-ממדי, וכו'"
      latex:
        escape: "בריחה מתווים מיוחדים של LaTeX (%, &, _, #, $, וכו') להבטחת קומפילציה נכונה"
        ht: "הוסף פרמטר מיקום צף [!ht] לשליטה במיקום הטבלה בעמוד"
        mwe: "צור מסמך LaTeX מלא"
        tableAlign: "הגדר יישור אופקי של הטבלה בעמוד"
        tableBorder: "קבע סגנון גבול הטבלה: ללא גבול, גבול חלקי, גבול מלא"
        label: "הגדר תווית טבלה עבור הפניות צולבות של פקודת \\ref{}"
        caption: "הגדר כותרת טבלה להצגה מעל או מתחת לטבלה"
        location: "בחר מיקום הצגת כותרת הטבלה: מעל או מתחת"
        tableType: "בחר סוג סביבת טבלה: tabular, longtable, array, וכו'"
      markdown:
        escape: "הימלט מתווים מיוחדים של Markdown (*, _, |, \\, וכו') כדי למנוע קונפליקטים בפורמט"
        pretty: "יישר אוטומטית רוחבי עמודות ליצירת פורמט טבלה יפה יותר"
        simple: "השתמש בתחביר מפושט, השמט קווים אנכיים של גבול חיצוני"
        boldFirstRow: "הפוך את הטקסט של השורה הראשונה למודגש"
        boldFirstColumn: "הפוך את הטקסט של העמודה הראשונה למודגש"
        firstHeader: "התייחס לשורה הראשונה ככותרת והוסף קו מפריד"
        textAlign: "הגדר יישור טקסט עמודה: שמאל, מרכז, ימין"
        multilineHandling: "טיפול בטקסט רב-שורתי: שמור מעברי שורה, הימלט ל-\\n, השתמש בתגי &lt;br&gt;"

        includeLineNumbers: "הוסף עמודת מספר שורה בצד שמאל של הטבלה"
      magic:
        builtin: "בחר פורמטי תבנית נפוצים מוגדרים מראש"
        rowsTpl: "<table> <tr> <th>תחביר קסום</th> <th>תיאור</th> <th>שיטות JS נתמכות</th> </tr> <tr> <td>{h1} {h2} ...</td> <td>שדה 1, 2 ... של <b>כ</b>ותרת, כלומר {hA} {hB} ...</td> <td>שיטות מחרוזת</td> </tr> <tr> <td>{$1} {$2} ...</td> <td>שדה 1, 2 ... של השורה הנוכחית, כלומר {$A} {$B} ...</td> <td>שיטות מחרוזת</td> </tr> <tr> <td>{F,} {F;}</td> <td>פיצול השורה הנוכחית לפי המחרוזת אחרי <b>F</b></td> <td></td> </tr> <tr> <td>{NR} {NR+100}</td> <td><b>מ</b>ספר <b>ש</b>ורה נוכחית מ-1 או 100</td> <td></td> </tr> <tr> <td> {ENR} </td> <td> <b>מ</b>ספר שורה <b>א</b>חרון </td> <td></td> </tr> <tr> <td>{x ...}</td> <td><b>ה</b>רצת קוד JavaScript, לדוגמה: {x new Date()} </td> <td></td> </tr><tr> <td>{...<b>\\</b>}</td> <td> השתמש בלוכסן הפוך <b>\\</b> להוצאת סוגריים מסולסלים {...} </td> <td></td> </tr></table>"
        headerTpl: "תבנית פלט מותאמת אישית עבור קטע הכותרת"
        footerTpl: "תבנית פלט מותאמת אישית עבור קטע הכותרת התחתונה"
      textile:
        escape: "הימלט מתווי תחביר Textile (|, ., -, ^) כדי למנוע קונפליקטים בפורמט"
        rowHeader: "הגדר שורה ראשונה כשורת כותרת"
        thead: "הוסף סמני תחביר Textile עבור ראש וגוף הטבלה"
      xml:
        escape: "הימלט מתווים מיוחדים של XML (&lt;, &gt;, &amp;, \", ') כדי להבטיח XML תקין"
        minify: "צור פלט XML דחוס, הסר רווחים מיותרים"
        rootElement: "הגדר שם תג אלמנט שורש XML"
        rowElement: "הגדר שם תג אלמנט XML עבור כל שורת נתונים"
        declaration: "הוסף כותרת הצהרת XML (&lt;?xml version=\"1.0\"?&gt;)"
        attributes: "הוצא נתונים כמאפייני XML במקום אלמנטים צאצא"
        cdata: "עטוף תוכן טקסט עם CDATA כדי להגן על תווים מיוחדים"
        encoding: "הגדר פורמט קידוד תווים עבור מסמך XML"
        indentation: "בחר תו הזחה XML: רווחים או טאבים"
      yaml:
        indentSize: "הגדר מספר רווחים עבור הזחת היררכיית YAML (בדרך כלל 2 או 4)"
        arrayStyle: "פורמט מערך: בלוק (פריט אחד בכל שורה) או זרימה (פורמט מוטבע)"
        quotationStyle: "סגנון מרכאות מחרוזת: ללא מרכאות, מרכאות יחידות, מרכאות כפולות"
      csv:
        bom: "הוסף סימן סדר בתים UTF-8 כדי לעזור ל-Excel ותוכנות אחרות לזהות קידוד"
      excel:
        autoWidth: "התאם אוטומטית רוחב עמודה על בסיס תוכן"
        protectSheet: "אפשר הגנת גיליון עבודה עם סיסמה: tableconvert.com"
      sql:  
        primaryKey: "ציין שם שדה מפתח ראשי עבור הצהרת CREATE TABLE"
        dialect: "בחר סוג מסד נתונים, משפיע על תחביר ציטוטים וסוגי נתונים"
      ascii:
        forceSep: "אלץ קווי הפרדה בין כל שורת נתונים"
        style: "בחר סגנון ציור גבול טבלת ASCII"
        comment: "הוסף סמני הערות לעטיפת הטבלה כולה"
      mediawiki:
        minify: "דחוס קוד פלט, הסר רווחים מיותרים"
        header: "סמן שורה ראשונה כסגנון כותרת"
        sort: "אפשר פונקציונליות מיון לחיצה בטבלה"
      asciidoc:
        minify: "דחוס פלט פורמט AsciiDoc"
        firstHeader: "קבע שורה ראשונה כשורת כותרת"
        lastFooter: "קבע שורה אחרונה כשורת כותרת תחתונה"
        title: "הוסף טקסט כותרת לטבלה"
      tracwiki:
        rowHeader: "קבע שורה ראשונה ככותרת"
        colHeader: "קבע עמודה ראשונה ככותרת"
      bbcode:
        minify: "דחוס פורמט פלט BBCode"
      restructuredtext:
        style: "בחר סגנון גבול טבלת reStructuredText"
        forceSep: "אלץ קווי הפרדה"
      pdf:
        theme: "בחר סגנון ויזואלי של טבלת PDF למסמכים מקצועיים"
        headerColor: "בחר צבע רקע כותרת לטבלאות PDF"
        showHead: "שלוט בתצוגת כותרת בעמודי PDF"
        docTitle: "כותרת אופציונלית למסמך PDF"
        docDescription: "טקסט תיאור אופציונלי למסמך PDF"
    label:
      ascii:
        forceSep: "מפרידי שורות"
        style: "סגנון גבול"
        comment: "עטיפת הערה"
      restructuredtext:
        style: "סגנון גבול"
        forceSep: "אלץ מפרידים"
      bbcode:
        minify: "מזעור פלט"
      csv:
        doubleQuote: "עטיפת מרכאות כפולות"
        delimiter: "מפריד שדות"
        bom: "UTF-8 BOM"
        valueDelimiter: "מפריד ערכים"
        rowDelimiter: "מפריד שורות"
        prefix: "קידומת שורה"
        suffix: "סיומת שורה"
      excel:
        autoWidth: "רוחב אוטומטי"
        textFormat: "פורמט טקסט"
        protectSheet: "הגן על גיליון"
        boldFirstRow: "שורה ראשונה מודגשת"
        boldFirstColumn: "עמודה ראשונה מודגשת"
        sheetName: "שם גיליון"
      html:
        escape: "בריחה מתווי HTML"
        div: "טבלת DIV"
        minify: "מזעור קוד"
        thead: "מבנה ראש טבלה"
        tableCaption: "כותרת טבלה"
        tableClass: "מחלקת טבלה"
        tableId: "מזהה טבלה"
        rowHeader: "כותרת שורה"
        colHeader: "כותרת עמודה"
      jira:
        escape: "בריחה מתווים"
        rowHeader: "כותרת שורה"
        colHeader: "כותרת עמודה"
      json:
        parsingJSON: "ניתוח JSON"
        minify: "מזעור פלט"
        format: "פורמט נתונים"
        rootName: "שם אובייקט שורש"
        indentSize: "גודל הזחה"
      jsonlines:
        parsingJSON: "ניתוח JSON"
        format: "פורמט נתונים"
      latex:
        escape: "תווי טבלת LaTeX לבריחה"
        ht: "מיקום צף"
        mwe: "מסמך שלם"
        tableAlign: "יישור טבלה"
        tableBorder: "סגנון גבול"
        label: "תווית הפניה"
        caption: "כותרת טבלה"
        location: "מיקום כותרת"
        tableType: "סוג טבלה"
        boldFirstRow: "שורה ראשונה מודגשת"
        boldFirstColumn: "עמודה ראשונה מודגשת"
        textAlign: "יישור טקסט"
        borders: "הגדרות גבול"
      markdown:
        escape: "בריחה מתווים"
        pretty: "טבלת Markdown יפה"
        simple: "פורמט Markdown פשוט"
        boldFirstRow: "שורה ראשונה מודגשת"
        boldFirstColumn: "עמודה ראשונה מודגשת"
        firstHeader: "כותרת ראשונה"
        textAlign: "יישור טקסט"
        multilineHandling: "טיפול ברב-שורתי"

        includeLineNumbers: "הוסף מספרי שורות"
        align: "יישור"
      mediawiki:
        minify: "מזעור קוד"
        header: "סימון כותרת"
        sort: "ניתן למיון"
      asciidoc:
        minify: "מזעור פורמט"
        firstHeader: "כותרת ראשונה"
        lastFooter: "כותרת תחתונה אחרונה"
        title: "כותרת טבלה"
      tracwiki:
        rowHeader: "כותרת שורה"
        colHeader: "כותרת עמודה"
      sql:
        drop: "מחק טבלה (אם קיימת)"
        create: "צור טבלה"
        oneInsert: "הכנסה באצווה"
        table: "שם טבלה"
        dialect: "סוג מסד נתונים"
        primaryKey: "מפתח ראשי"
      magic:
        builtin: "תבנית מובנית"
        rowsTpl: "תבנית שורה, תחביר ->"
        headerTpl: "תבנית כותרת"
        footerTpl: "תבנית כותרת תחתונה"
      textile:
        escape: "בריחה מתווים"
        rowHeader: "כותרת שורה"
        thead: "תחביר ראש טבלה"
      xml:
        escape: "בריחה מתווי XML"
        minify: "מזעור פלט"
        rootElement: "אלמנט שורש"
        rowElement: "אלמנט שורה"
        declaration: "הצהרת XML"
        attributes: "מצב תכונות"
        cdata: "עטיפת CDATA"
        encoding: "קידוד"
        indentSize: "גודל הזחה"
      yaml:
        indentSize: "גודל הזחה"
        arrayStyle: "סגנון מערך"
        quotationStyle: "סגנון ציטוט"
      pdf:
        theme: "נושא טבלת PDF"
        headerColor: "צבע כותרת PDF"
        showHead: "תצוגת כותרת PDF"
        docTitle: "כותרת מסמך PDF"
        docDescription: "תיאור מסמך PDF"

sidebar:
  all: "כל כלי ההמרה"
  dataSource:
    title: "מקור נתונים"
    description:
      converter: "יבא %s להמרה ל-%s. תומך בהעלאת קבצים, עריכה מקוונת וחילוץ נתוני אינטרנט."
      generator: "צור נתוני טבלה עם תמיכה במספר שיטות קלט כולל קלט ידני, יבוא קבצים ויצירת תבניות."
  tableEditor:
    title: "עורך טבלאות מקוון"
    description:
      converter: "עבד %s מקוון באמצעות עורך הטבלאות שלנו. חוויית פעולה דמוית Excel עם תמיכה במחיקת שורות ריקות, הסרת כפילויות, מיון וחיפוש והחלפה."
      generator: "עורך טבלאות מקוון חזק המספק חוויית פעולה דמוית Excel. תומך במחיקת שורות ריקות, הסרת כפילויות, מיון וחיפוש והחלפה."
  tableGenerator:
    title: "גנרטור טבלאות"
    description:
      converter: "צור במהירות %s עם תצוגה מקדימה בזמן אמת של גנרטור הטבלאות. אפשרויות יצוא עשירות, העתקה והורדה בלחיצה אחת."
      generator: "יצא נתוני %s בפורמטים מרובים כדי לענות על תרחישי שימוש שונים. תומך באפשרויות מותאמות אישית ותצוגה מקדימה בזמן אמת."
footer:
  changelog: "יומן שינויים"
  sponsor: "נותני חסות"
  contact: "צור קשר"
  privacyPolicy: "מדיניות פרטיות"
  about: "אודות"
  resources: "משאבים"
  popularConverters: "ממירים פופולריים"
  popularGenerators: "גנרטורים פופולריים"
  dataSecurity: "הנתונים שלך מאובטחים - כל ההמרות רצות בדפדפן שלך."
converters:
  Markdown:
    alias: "טבלת Markdown"
    what: "Markdown היא שפת סימון קלילה הנמצאת בשימוש נרחב לתיעוד טכני, יצירת תוכן בלוגים ופיתוח אתרים. תחביר הטבלה שלה קצר ואינטואיטיבי, תומך ביישור טקסט, הטמעת קישורים ועיצוב. זהו הכלי המועדף על מתכנתים וכותבים טכניים, תואם לחלוטין עם GitHub, GitLab ופלטפורמות אירוח קוד אחרות."
    step1: "הדבק נתוני טבלת Markdown לאזור מקור הנתונים, או גרור ושחרר קבצי .md ישירות להעלאה. הכלי מנתח אוטומטית את מבנה הטבלה והעיצוב, תומך בתוכן מקונן מורכב וטיפול בתווים מיוחדים."
    step3: "צור קוד טבלת Markdown סטנדרטי בזמן אמת, תומך במספר שיטות יישור, הדגשת טקסט, הוספת מספרי שורות והגדרות פורמט מתקדמות אחרות. הקוד שנוצר תואם לחלוטין עם GitHub ועורכי Markdown מרכזיים, מוכן לשימוש עם העתקה בלחיצה אחת."
    from_alias: "קובץ טבלת Markdown"
    to_alias: "פורמט טבלת Markdown"
  Magic:
    alias: "תבנית מותאמת אישית"
    what: "תבנית Magic היא מחולל נתונים מתקדם ייחודי של כלי זה, המאפשר למשתמשים ליצור פלט נתונים בפורמט שרירותי באמצעות תחביר תבנית מותאם אישית. תומך בהחלפת משתנים, שיפוט מותנה ועיבוד לולאות. זהו הפתרון האולטימטיבי לטיפול בצרכי המרת נתונים מורכבים ופורמטי פלט מותאמים אישית, מתאים במיוחד למפתחים ומהנדסי נתונים."
    step1: "בחר תבניות נפוצות מובנות או צור תחביר תבנית מותאם אישית. תומך במשתנים ופונקציות עשירים שיכולים לטפל במבני נתונים מורכבים ולוגיקה עסקית."
    step3: "צור פלט נתונים שעונה במלואו על דרישות הפורמט המותאם אישית. תומך בלוגיקת המרת נתונים מורכבת ועיבוד מותנה, משפר משמעותית את יעילות עיבוד הנתונים ואיכות הפלט. כלי חזק לעיבוד נתונים באצווה."
    from_alias: "נתוני טבלה"
    to_alias: "פלט פורמט מותאם אישית"
  CSV:
    alias: "CSV"
    what: "CSV (Comma-Separated Values) הוא פורמט החלפת הנתונים הנפוץ ביותר, נתמך באופן מושלם על ידי Excel, Google Sheets, מערכות בסיסי נתונים וכלי ניתוח נתונים שונים. המבנה הפשוט והתאימות החזקה שלו הופכים אותו לפורמט הסטנדרטי להעברת נתונים, ייבוא/ייצוא באצווה והחלפת נתונים חוצת פלטפורמות, נמצא בשימוש נרחב בניתוח עסקי, מדעי הנתונים ואינטגרציית מערכות."
    step1: "העלה קבצי CSV או הדבק נתוני CSV ישירות. הכלי מזהה בחכמה מפרידים שונים (פסיק, טאב, נקודה-פסיק, צינור וכו'), מזהה אוטומטית סוגי נתונים ופורמטי קידוד, תומך בניתוח מהיר של קבצים גדולים ומבני נתונים מורכבים."
    step3: "צור קבצי פורמט CSV סטנדרטיים עם תמיכה במפרידים מותאמים אישית, סגנונות ציטוט, פורמטי קידוד והגדרות סימן BOM. מבטיח תאימות מושלמת עם מערכות יעד, מספק אפשרויות הורדה ודחיסה לעמידה בצרכי עיבוד נתונים ברמת הארגון."
    from_alias: "קובץ נתוני CSV"
    to_alias: "פורמט CSV סטנדרטי"
  JSON:
    alias: "מערך JSON"
    what: "JSON (JavaScript Object Notation) הוא פורמט נתוני הטבלה הסטנדרטי עבור יישומי אינטרנט מודרניים, REST APIs וארכיטקטורות מיקרו-שירותים. המבנה הברור והניתוח היעיל שלו הופכים אותו לנפוץ באינטראקציית נתונים בין frontend ו-backend, אחסון קבצי תצורה ובסיסי נתונים NoSQL. תומך באובייקטים מקוננים, מבני מערכים וסוגי נתונים מרובים, הופך אותו לנתוני טבלה הכרחיים לפיתוח תוכנה מודרני."
    step1: "העלה קבצי JSON או הדבק מערכי JSON. תומך בזיהוי אוטומטי וניתוח של מערכי אובייקטים, מבנים מקוננים וסוגי נתונים מורכבים. הכלי מאמת בחכמה את תחביר JSON ומספק הודעות שגיאה."
    step3: "צור פלטי פורמט JSON מרובים: מערכי אובייקטים סטנדרטיים, מערכים דו-ממדיים, מערכי עמודות ופורמטי זוגות מפתח-ערך. תומך בפלט מיופה, מצב דחיסה, שמות אובייקט שורש מותאמים אישית והגדרות הזחה, מתאים באופן מושלם לממשקי API שונים וצרכי אחסון נתונים."
    from_alias: "קובץ מערך JSON"
    to_alias: "פורמט JSON סטנדרטי"
  JSONLines:
    alias: "פורמט JSONLines"
    what: "JSON Lines (הידוע גם בשם NDJSON) הוא פורמט חשוב לעיבוד נתונים גדולים והעברת נתונים בזרימה, כאשר כל שורה מכילה אובייקט JSON עצמאי. נמצא בשימוש נרחב בניתוח לוגים, עיבוד זרמי נתונים, למידת מכונה ומערכות מבוזרות. תומך בעיבוד מצטבר וחישוב מקבילי, הופך אותו לבחירה האידיאלית לטיפול בנתונים מובנים בקנה מידה גדול."
    step1: "העלה קבצי JSONLines או הדבק נתונים. הכלי מנתח אובייקטי JSON שורה אחר שורה, תומך בעיבוד זרימה של קבצים גדולים ופונקציונליות דילוג על שורות שגויות."
    step3: "צור פורמט JSONLines סטנדרטי עם כל שורה מוציאה אובייקט JSON מלא. מתאים לעיבוד זרימה, ייבוא אצווה ותרחישי ניתוח נתונים גדולים, תומך באימות נתונים ואופטימיזציה של פורמט."
    from_alias: "נתוני JSONLines"
    to_alias: "פורמט זרימת JSONLines"
  XML:
    alias: "XML"
    what: "XML (eXtensible Markup Language) הוא הפורמט הסטנדרטי לחילופי נתונים ברמת הארגון וניהול תצורה, עם מפרטי תחביר קפדניים ומנגנוני אימות חזקים. נמצא בשימוש נרחב בשירותי אינטרנט, קבצי תצורה, אחסון מסמכים ואינטגרציית מערכות. תומך במרחבי שמות, אימות סכמה והמרת XSLT, הופך אותו לנתוני טבלה חשובים ליישומי ארגון."
    step1: "העלה קבצי XML או הדבק נתוני XML. הכלי מנתח אוטומטית מבנה XML וממיר אותו לפורמט טבלה, תומך במרחב שמות, טיפול בתכונות ומבנים מקוננים מורכבים."
    step3: "צור פלט XML התואם לתקני XML. תומך באלמנטי שורש מותאמים אישית, שמות אלמנטי שורה, מצבי תכונות, עטיפת CDATA והגדרות קידוד תווים. מבטיח שלמות נתונים ותאימות, עונה על דרישות יישומים ברמה ארגונית."
    from_alias: "קובץ נתוני XML"
    to_alias: "פורמט XML סטנדרטי"
  YAML:
    alias: "תצורת YAML"
    what: "YAML הוא תקן סריאליזציה של נתונים ידידותי לאדם, הידוע במבנה ההיררכי הברור והתחביר הקצר שלו. נמצא בשימוש נרחב בקבצי תצורה, שרשראות כלי DevOps, Docker Compose ופריסת Kubernetes. הקריאות החזקה והתחביר הקצר שלו הופכים אותו לפורמט תצורה חשוב ליישומי cloud-native מודרניים ופעולות אוטומטיות."
    step1: "העלה קבצי YAML או הדבק נתוני YAML. הכלי מנתח בחכמה מבנה YAML ומאמת נכונות תחביר, תומך בפורמטים רב-מסמכיים וסוגי נתונים מורכבים."
    step3: "צור פלט פורמט YAML סטנדרטי עם תמיכה בסגנונות מערך בלוק וזרימה, הגדרות ציטוט מרובות, הזחה מותאמת אישית ושימור הערות. מבטיח שקבצי YAML הפלט תואמים לחלוטין למנתחים שונים ומערכות תצורה."
    from_alias: "קובץ תצורת YAML"
    to_alias: "פורמט YAML סטנדרטי"
  MySQL:
      alias: "תוצאות שאילתת MySQL"
      what: "MySQL היא מערכת ניהול בסיסי הנתונים הרלציוניים בקוד פתוח הפופולרית ביותר בעולם, הידועה בביצועים הגבוהים, האמינות וקלות השימוש שלה. נמצאת בשימוש נרחב ביישומי אינטרנט, מערכות ארגוניות ופלטפורמות ניתוח נתונים. תוצאות שאילתת MySQL מכילות בדרך כלל נתוני טבלה מובנים, משמשות כמקור נתונים חשוב בניהול בסיסי נתונים ועבודת ניתוח נתונים."
      step1: "הדבק תוצאות שאילתת MySQL לאזור מקור הנתונים. הכלי מזהה ומנתח אוטומטית פורמט פלט שורת הפקודה של MySQL, תומך בסגנונות תוצאות שאילתה שונים וקידודי תווים, מטפל בחכמה בכותרות ושורות נתונים."
      step3: "המר במהירות תוצאות שאילתת MySQL לפורמטים מרובים של נתוני טבלה, מקל על ניתוח נתונים, יצירת דוחות, העברת נתונים בין מערכות ואימות נתונים. כלי מעשי למנהלי בסיסי נתונים ואנליסטי נתונים."
      from_alias: "פלט שאילתת MySQL"
      to_alias: "נתוני טבלת MySQL"
  SQL:
    alias: "Insert SQL"
    what: "SQL (Structured Query Language) היא שפת הפעולה הסטנדרטית לבסיסי נתונים רלציוניים, המשמשת לפעולות שאילתת נתונים, הכנסה, עדכון ומחיקה. כטכנולוגיית הליבה של ניהול בסיסי נתונים, SQL נמצאת בשימוש נרחב בניתוח נתונים, בינה עסקית, עיבוד ETL ובניית מחסני נתונים. זהו כלי מיומנות חיוני לאנשי מקצוע בתחום הנתונים."
    step1: "הדבק הצהרות INSERT SQL או העלה קבצי .sql. הכלי מנתח בחכמה תחביר SQL ומחלץ נתוני טבלה, תומך בניבים מרובים של SQL ועיבוד הצהרות שאילתה מורכבות."
    step3: "צור הצהרות INSERT SQL סטנדרטיות והצהרות יצירת טבלה. תומך בניבי מסד נתונים מרובים (MySQL, PostgreSQL, SQLite, SQL Server, Oracle), מטפל אוטומטית במיפוי סוגי נתונים, בריחת תווים ואילוצי מפתח ראשי. מבטיח שקוד SQL שנוצר יכול להתבצע ישירות."
    from_alias: "Insert SQL"
    to_alias: "הצהרת SQL"
  Qlik:
      alias: "טבלת Qlik"
      what: "Qlik היא ספקית תוכנה המתמחה בהדמיית נתונים, לוחות מחוונים למנהלים ומוצרי בינה עסקית בשירות עצמי, יחד עם Tableau ו-Microsoft."
      step1: ""
      step3: "לבסוף, [מחולל הטבלאות](#TableGenerator) מציג את תוצאות ההמרה. השתמש ב-Qlik Sense, Qlik AutoML, QlikView או תוכנות אחרות התומכות ב-Qlik שלך."
      from_alias: "טבלת Qlik"
      to_alias: "טבלת Qlik"
  DAX:
      alias: "טבלת DAX"
      what: "DAX (Data Analysis Expressions) היא שפת תכנות המשמשת ב-Microsoft Power BI ליצירת עמודות מחושבות, מדדים וטבלאות מותאמות אישית."
      step1: ""
      step3: "לבסוף, [מחולל הטבלאות](#TableGenerator) מציג את תוצאות ההמרה. כצפוי, היא משמשת במספר מוצרי Microsoft כולל Microsoft Power BI, Microsoft Analysis Services ו-Microsoft Power Pivot עבור Excel."
      from_alias: "טבלת DAX"
      to_alias: "טבלת DAX"
  Firebase:
    alias: "רשימת Firebase"
    what: "Firebase היא פלטפורמת פיתוח אפליקציות BaaS המספקת שירותי backend מתארחים כמו מסד נתונים בזמן אמת, אחסון ענן, אימות, דיווח קריסות וכו'."
    step1: ""
    step3: "לבסוף, [מחולל הטבלאות](#TableGenerator) מציג את תוצאות ההמרה. לאחר מכן תוכל להשתמש בשיטת push ב-API של Firebase כדי להוסיף לרשימת נתונים במסד הנתונים של Firebase."
    from_alias: "רשימת Firebase"
    to_alias: "רשימת Firebase"
  HTML:
    alias: "טבלת HTML"
    what: "טבלאות HTML הן הדרך הסטנדרטית להצגת נתונים מובנים בדפי אינטרנט, הבנויות עם תגי table, tr, td ואחרים. תומכות בהתאמה אישית עשירה של סגנונות, פריסה רספונסיבית ופונקציונליות אינטראקטיבית. נמצאות בשימוש נרחב בפיתוח אתרים, הצגת נתונים ויצירת דוחות, ומשמשות כרכיב חשוב בפיתוח frontend ועיצוב אתרים."
    step1: "הדבק קוד HTML המכיל טבלאות או העלה קבצי HTML. הכלי מזהה אוטומטית ומחלץ נתוני טבלאות מדפים, תומך במבני HTML מורכבים, סגנונות CSS ועיבוד טבלאות מקוננות."
    step3: "צור קוד טבלת HTML סמנטי עם תמיכה במבנה thead/tbody, הגדרות מחלקות CSS, כותרות טבלאות, כותרות שורה/עמודה והגדרת תכונות רספונסיביות. מבטיח שקוד הטבלה שנוצר עומד בתקני האינטרנט עם נגישות טובה וידידותיות SEO."
    from_alias: "טבלת HTML"
    to_alias: "טבלת HTML"
  Excel:
    alias: "Excel"
    what: "Microsoft Excel היא תוכנת הגיליונות האלקטרוניים הפופולרית ביותר בעולם, נמצאת בשימוש נרחב בניתוח עסקי, ניהול פיננסי, עיבוד נתונים ויצירת דוחות. יכולות עיבוד הנתונים החזקות שלה, ספריית הפונקציות העשירה ותכונות ההדמיה הגמישות הופכות אותה לכלי הסטנדרטי לאוטומציה משרדית וניתוח נתונים, עם יישומים נרחבים כמעט בכל התעשיות והתחומים."
    step1: "העלה קבצי Excel (תומך בפורמטים .xlsx, .xls) או העתק נתוני טבלה ישירות מ-Excel והדבק. הכלי תומך בעיבוד רב-גיליונות, זיהוי פורמטים מורכבים וניתוח מהיר של קבצים גדולים, מטפל אוטומטית בתאים מאוחדים וסוגי נתונים."
    step3: "צור נתוני טבלה תואמי Excel שניתן להדביק ישירות ב-Excel או להוריד כקבצי .xlsx סטנדרטיים. תומך בשמות גיליונות עבודה, עיצוב תאים, רוחב עמודות אוטומטי, עיצוב כותרות והגדרות אימות נתונים. מבטיח שקבצי Excel הפלט יש להם מראה מקצועי ופונקציונליות מלאה."
    from_alias: "גיליון Excel"
    to_alias: "Excel"
  LaTeX:
    alias: "טבלת LaTeX"
    what: "LaTeX היא מערכת גיבוב מסמכים מקצועית, מתאימה במיוחד ליצירת מאמרים אקדמיים, מסמכים טכניים ופרסומים מדעיים. פונקציונליות הטבלאות שלה חזקה, תומכת בנוסחאות מתמטיות מורכבות, שליטה מדויקת בפריסה ופלט PDF באיכות גבוהה. זהו הכלי הסטנדרטי באקדמיה ובפרסום מדעי, נמצא בשימוש נרחב במאמרי כתבי עת, עבודות גמר וגיבוב מדריכים טכניים."
    step1: "הדבק קוד טבלת LaTeX או העלה קבצי .tex. הכלי מנתח את תחביר טבלת LaTeX ומחלץ תוכן נתונים, תומך בסביבות טבלה מרובות (tabular, longtable, array וכו') ופקודות פורמט מורכבות."
    step3: "צור קוד טבלת LaTeX מקצועי עם תמיכה בבחירת סביבות טבלה מרובות, הגדרת סגנון גבולות, הגדרות מיקום כותרת, מפרט מחלקת מסמך וניהול חבילות. יכול ליצור מסמכי LaTeX שלמים הניתנים לקומפילציה, מבטיח שטבלאות הפלט עומדות בתקני פרסום אקדמי."
    from_alias: "טבלת LaTeX"
    to_alias: "טבלת LaTeX"
  ASCII:
    alias: "טבלת טקסט ASCII"
    what: "טבלאות ASCII משתמשות בתווי טקסט רגילים לציור גבולות ומבני טבלאות, מספקות את התאימות והניידות הטובות ביותר. תואמות לכל עורכי הטקסט, סביבות טרמינל ומערכות הפעלה. נמצאות בשימוש נרחב בתיעוד קוד, מדריכים טכניים, קבצי README ופלט כלי שורת פקודה. פורמט הצגת הנתונים המועדף על מתכנתים ומנהלי מערכת."
    step1: "העלה קבצי טקסט המכילים טבלאות ASCII או הדבק נתוני טבלה ישירות. הכלי מזהה בחכמה ומנתח מבני טבלאות ASCII, תומך בסגנונות גבול מרובים ופורמטי יישור."
    step3: "צור טבלאות ASCII טקסט רגיל יפות עם תמיכה בסגנונות גבול מרובים (קו יחיד, קו כפול, פינות מעוגלות וכו'), שיטות יישור טקסט ורוחב עמודות אוטומטי. הטבלאות שנוצרו מוצגות בצורה מושלמת בעורכי קוד, מסמכים ושורות פקודה."
    from_alias: "טבלת טקסט ASCII"
    to_alias: "טבלת טקסט ASCII"
  MediaWiki:
    alias: "טבלת MediaWiki"
    what: "MediaWiki היא פלטפורמת התוכנה בקוד פתוח המשמשת אתרי ויקי מפורסמים כמו ויקיפדיה. תחביר הטבלאות שלה קצר אך חזק, תומך בהתאמה אישית של סגנון טבלאות, פונקציונליות מיון והטמעת קישורים. נמצא בשימוש נרחב בניהול ידע, עריכה שיתופית ומערכות ניהול תוכן, משמש כטכנולוגיית ליבה לבניית אנציקלופדיות ויקי ובסיסי ידע."
    step1: "הדבק קוד טבלת MediaWiki או העלה קבצי מקור ויקי. הכלי מנתח תחביר סימון ויקי ומחלץ נתוני טבלה, תומך בתחביר ויקי מורכב ועיבוד תבניות."
    step3: "צור קוד טבלת MediaWiki סטנדרטי עם תמיכה בהגדרות סגנון כותרת, יישור תאים, הפעלת פונקציונליות מיון ואפשרויות דחיסת קוד. הקוד שנוצר יכול לשמש ישירות לעריכת דפי ויקי, מבטיח תצוגה מושלמת בפלטפורמות MediaWiki."
    from_alias: "טבלת MediaWiki"
    to_alias: "טבלת MediaWiki"
  TracWiki:
    alias: "טבלת TracWiki"
    what: "Trac היא מערכת ניהול פרויקטים ומעקב באגים מבוססת אינטרנט המשתמשת בתחביר ויקי מפושט ליצירת תוכן טבלאות."
    step1: "העלה קבצי TracWiki או הדבק נתוני טבלה."
    step3: "צור קוד טבלה תואם TracWiki עם תמיכה בהגדרות כותרות שורה/עמודה, מקל על ניהול מסמכי פרויקט."
    from_alias: "טבלת TracWiki"
    to_alias: "טבלת TracWiki"
  AsciiDoc:
    alias: "טבלת AsciiDoc"
    what: "AsciiDoc היא שפת סימון קלילה שניתן להמיר ל-HTML, PDF, דפי מדריך ופורמטים אחרים, נמצאת בשימוש נרחב לכתיבת תיעוד טכני."
    step1: "העלה קבצי AsciiDoc או הדבק נתונים."
    step3: "צור תחביר טבלת AsciiDoc עם תמיכה בהגדרות כותרת, כותרת תחתונה וכותרת, ניתן לשימוש ישיר בעורכי AsciiDoc."
    from_alias: "טבלת AsciiDoc"
    to_alias: "טבלת AsciiDoc"
  reStructuredText:
    alias: "טבלת reStructuredText"
    what: "reStructuredText הוא פורמט התיעוד הסטנדרטי עבור קהילת Python, תומך בתחביר טבלאות עשיר, נמצא בשימוש נפוץ ליצירת תיעוד Sphinx."
    step1: "העלה קבצי .rst או הדבק נתוני reStructuredText."
    step3: "צור טבלאות reStructuredText סטנדרטיות עם תמיכה בסגנונות גבול מרובים, ניתנות לשימוש ישיר בפרויקטי תיעוד Sphinx."
    from_alias: "טבלת reStructuredText"
    to_alias: "טבלת reStructuredText"
  PHP:
    alias: "מערך PHP"
    what: "PHP היא שפת סקריפט צד שרת פופולרית, עם מערכים כמבנה הנתונים הליבה שלה, נמצאת בשימוש נרחב בפיתוח אתרים ועיבוד נתונים."
    step1: "העלה קבצים המכילים מערכי PHP או הדבק נתונים ישירות."
    step3: "צור קוד מערך PHP סטנדרטי שניתן לשימוש ישיר בפרויקטי PHP, תומך בפורמטי מערכים אסוציאטיביים ומאונדקסים."
    from_alias: "מערך PHP"
    to_alias: "קוד PHP"
  Ruby:
    alias: "מערך Ruby"
    what: "Ruby היא שפת תכנות מונחית עצמים דינמית עם תחביר קצר ואלגנטי, עם מערכים כמבנה נתונים חשוב."
    step1: "העלה קבצי Ruby או הדבק נתוני מערך."
    step3: "צור קוד מערך Ruby התואם למפרטי תחביר Ruby, ניתן לשימוש ישיר בפרויקטי Ruby."
    from_alias: "מערך Ruby"
    to_alias: "קוד Ruby"
  ASP:
    alias: "מערך ASP"
    what: "ASP (Active Server Pages) היא סביבת הסקריפט צד שרת של Microsoft, תומכת במספר שפות תכנות לפיתוח דפי אינטרנט דינמיים."
    step1: "העלה קבצי ASP או הדבק נתוני מערך."
    step3: "צור קוד מערך תואם ASP עם תמיכה בתחביר VBScript ו-JScript, ניתן לשימוש בפרויקטי ASP.NET."
    from_alias: "מערך ASP"
    to_alias: "קוד ASP"
  ActionScript:
    alias: "מערך ActionScript"
    what: "ActionScript היא שפת תכנות מונחית עצמים המשמשת בעיקר לפיתוח יישומי Adobe Flash ו-AIR."
    step1: "העלה קבצי .as או הדבק נתוני ActionScript."
    step3: "צור קוד מערך ActionScript התואם לתקני תחביר AS3, ניתן לשימוש לפיתוח פרויקטי Flash ו-Flex."
    from_alias: "מערך ActionScript"
    to_alias: "קוד ActionScript"
  BBCode:
    alias: "טבלת BBCode"
    what: "BBCode היא שפת סימון קלילה הנמצאת בשימוש נפוץ בפורומים וקהילות מקוונות, מספקת פונקציונליות עיצוב פשוטה כולל תמיכה בטבלאות."
    step1: "העלה קבצים המכילים BBCode או הדבק נתונים."
    step3: "צור קוד טבלת BBCode המתאים לפרסום בפורומים ויצירת תוכן קהילתי, עם תמיכה בפורמט פלט דחוס."
    from_alias: "טבלת BBCode"
    to_alias: "טבלת BBCode"
  PDF:
    alias: "טבלת PDF"
    what: "PDF (Portable Document Format) הוא תקן מסמכים חוצה פלטפורמות עם פריסה קבועה, תצוגה עקבית ומאפייני הדפסה באיכות גבוהה. נמצא בשימוש נרחב במסמכים רשמיים, דוחות, חשבוניות, חוזים ומאמרים אקדמיים. הפורמט המועדף לתקשורת עסקית וארכיון מסמכים, מבטיח אפקטים ויזואליים עקביים לחלוטין בין מכשירים ומערכות הפעלה שונות."
    step1: "ייבא נתוני טבלה בכל פורמט. הכלי מנתח אוטומטית את מבנה הנתונים ומבצע עיצוב פריסה חכם, תומך בעימוד אוטומטי של טבלאות גדולות ועיבוד סוגי נתונים מורכבים."
    step3: "צור קבצי טבלת PDF באיכות גבוהה עם תמיכה בסגנונות נושא מקצועיים מרובים (עסקי, אקדמי, מינימליסטי וכו'), גופנים רב-לשוניים, עימוד אוטומטי, הוספת סימן מים ואופטימיזציה להדפסה. מבטיח שמסמכי PDF הפלט יש להם מראה מקצועי, ניתנים לשימוש ישיר בהצגות עסקיות ופרסום רשמי."
    from_alias: "נתוני טבלה"
    to_alias: "טבלת PDF"
  JPEG:
    alias: "תמונת JPEG"
    what: "JPEG הוא פורמט התמונה הדיגיטלית הנפוץ ביותר עם אפקטי דחיסה מעולים ותאימות רחבה. גודל הקובץ הקטן ומהירות הטעינה המהירה שלו הופכים אותו למתאים לתצוגה באינטרנט, שיתוף ברשתות חברתיות, איורי מסמכים והצגות מקוונות. פורמט התמונה הסטנדרטי למדיה דיגיטלית ותקשורת רשת, נתמך בצורה מושלמת על ידי כמעט כל המכשירים והתוכנות."
    step1: "ייבא נתוני טבלה בכל פורמט. הכלי מבצע עיצוב פריסה חכם ואופטימיזציה ויזואלית, מחשב אוטומטית גודל ורזולוציה אופטימליים."
    step3: "צור תמונות טבלת JPEG בחדות גבוהה עם תמיכה בערכות צבעים נושאיות מרובות (בהיר, כהה, ידידותי לעיניים וכו'), פריסה אדפטיבית, אופטימיזציה של בהירות טקסט והתאמה אישית של גודל. מתאים לשיתוף מקוון, הכנסה למסמכים ושימוש בהצגות, מבטיח אפקטים ויזואליים מעולים במכשירי תצוגה שונים."
    from_alias: "נתוני טבלה"
    to_alias: "תמונת JPEG"
  Jira:
    alias: "טבלת Jira"
    what: "JIRA היא תוכנת ניהול פרויקטים ומעקב באגים מקצועית שפותחה על ידי Atlassian, נמצאת בשימוש נרחב בפיתוח זריז, בדיקות תוכנה ושיתוף פעולה בפרויקטים. פונקציונליות הטבלאות שלה תומכת באפשרויות עיצוב עשירות ותצוגת נתונים, משמשת ככלי חשוב לצוותי פיתוח תוכנה, מנהלי פרויקטים ואנשי הבטחת איכות בניהול דרישות, מעקב באגים ודיווח התקדמות."
    step1: "העלה קבצים המכילים נתוני טבלה או הדבק תוכן נתונים ישירות. הכלי מעבד אוטומטית נתוני טבלה ובריחה של תווים מיוחדים."
    step3: "צור קוד טבלה תואם פלטפורמת JIRA עם תמיכה בהגדרות סגנון כותרת, יישור תאים, עיבוד בריחת תווים ואופטימיזציה של פורמט. הקוד שנוצר יכול להיות מודבק ישירות לתיאורי בעיות JIRA, הערות או דפי ויקי, מבטיח תצוגה ורינדור נכונים במערכות JIRA."
    from_alias: "טבלת Jira"
    to_alias: "טבלת Jira"
  Textile:
    alias: "טבלת Textile"
    what: "Textile היא שפת סימון קלילה קצרה עם תחביר פשוט וקל ללמידה, נמצאת בשימוש נרחב במערכות ניהול תוכן, פלטפורמות בלוג ומערכות פורום. תחביר הטבלאות שלה ברור ואינטואיטיבי, תומך בעיצוב מהיר והגדרות סגנון. כלי אידיאלי ליוצרי תוכן ומנהלי אתרים לכתיבת מסמכים מהירה ופרסום תוכן."
    step1: "העלה קבצי פורמט Textile או הדבק נתוני טבלה. הכלי מנתח תחביר סימון Textile ומחלץ תוכן טבלה."
    step3: "צור תחביר טבלת Textile סטנדרטי עם תמיכה בסימון כותרת, יישור תאים, בריחת תווים מיוחדים ואופטימיזציה של פורמט. הקוד שנוצר יכול לשמש ישירות במערכות CMS, פלטפורמות בלוג ומערכות מסמכים התומכות ב-Textile, מבטיח רינדור ותצוגה נכונים של תוכן."
    from_alias: "טבלת Textile"
    to_alias: "טבלת Textile"
  PNG:
    alias: "תמונת PNG"
    what: "PNG (Portable Network Graphics) הוא פורמט תמונה ללא אובדן עם דחיסה מעולה ותמיכה בשקיפות. נמצא בשימוש נרחב בעיצוב אתרים, גרפיקה דיגיטלית וצילום מקצועי. האיכות הגבוהה והתאימות הרחבה שלו הופכים אותו לאידיאלי לצילומי מסך, לוגואים, דיאגרמות וכל תמונה הדורשת פרטים חדים ורקע שקוף."
    step1: "ייבא נתוני טבלה בכל פורמט. הכלי מבצע עיצוב פריסה חכם ואופטימיזציה ויזואלית, מחשב אוטומטית גודל ורזולוציה אופטימליים לפלט PNG."
    step3: "צור תמונות טבלת PNG באיכות גבוהה עם תמיכה בערכות צבעים נושאיות מרובות, רקעים שקופים, פריסה אדפטיבית ואופטימיזציה של בהירות טקסט. מושלם לשימוש באינטרנט, הכנסה למסמכים והצגות מקצועיות עם איכות ויזואלית מעולה."
    from_alias: ""
    to_alias: "תמונת PNG"
  TOML:
    alias: "TOML"
    what: "TOML (Tom's Obvious, Minimal Language) הוא פורמט קובץ תצורה שקל לקריאה וכתיבה. מעוצב להיות חד-משמעי ופשוט, הוא נמצא בשימוש נרחב בפרויקטי תוכנה מודרניים לניהול תצורה. התחביר הברור והטיפוס החזק שלו הופכים אותו לבחירה מעולה להגדרות יישומים וקבצי תצורת פרויקט."
    step1: "העלה קבצי TOML או הדבק נתוני תצורה. הכלי מנתח תחביר TOML ומחלץ מידע תצורה מובנה."
    step3: "צור פורמט TOML סטנדרטי עם תמיכה במבנים מקוננים, סוגי נתונים והערות. קבצי TOML שנוצרו מושלמים לתצורת יישומים, כלי בנייה והגדרות פרויקט."
    from_alias: "TOML"
    to_alias: "פורמט TOML"
  INI:
    alias: "INI"
    what: "קבצי INI הם קבצי תצורה פשוטים המשמשים יישומים ומערכות הפעלה רבים. המבנה הישיר שלהם של זוגות מפתח-ערך הופך אותם לקלים לקריאה ועריכה ידנית. נמצאים בשימוש נרחב ביישומי Windows, מערכות מורשת ותרחישי תצורה פשוטים שבהם קריאות אנושית חשובה."
    step1: "העלה קבצי INI או הדבק נתוני תצורה. הכלי מנתח תחביר INI ומחלץ מידע תצורה מבוסס סעיפים."
    step3: "צור פורמט INI סטנדרטי עם תמיכה בסעיפים, הערות וסוגי נתונים שונים. קבצי INI שנוצרו תואמים לרוב היישומים ומערכות התצורה."
    from_alias: "INI"
    to_alias: "פורמט INI"
  Avro:
    alias: "סכמת Avro"
    what: "Apache Avro היא מערכת סריאליזציה של נתונים המספקת מבני נתונים עשירים, פורמט בינארי קומפקטי ויכולות התפתחות סכמה. נמצאת בשימוש נרחב בעיבוד נתונים גדולים, תורי הודעות ומערכות מבוזרות. הגדרת הסכמה שלה תומכת בסוגי נתונים מורכבים ותאימות גרסאות, הופכת אותה לכלי חשוב למהנדסי נתונים ואדריכלי מערכת."
    step1: "העלה קבצי סכמת Avro או הדבק נתונים. הכלי מנתח הגדרות סכמת Avro ומחלץ מידע מבנה טבלה."
    step3: "צור הגדרות סכמת Avro סטנדרטיות עם תמיכה במיפוי סוגי נתונים, אילוצי שדות ואימות סכמה. סכמות שנוצרו יכולות לשמש ישירות באקוסיסטמי Hadoop, מערכות הודעות Kafka ופלטפורמות נתונים גדולים אחרות."
    from_alias: "סכמת Avro"
    to_alias: "סכמת Avro"
  Protobuf:
    alias: "Protocol Buffers"
    what: "Protocol Buffers (protobuf) הוא המנגנון של Google הנייטרלי לשפה, נייטרלי לפלטפורמה וניתן להרחבה לסריאליזציה של נתונים מובנים. נמצא בשימוש נרחב במיקרו-שירותים, פיתוח API ואחסון נתונים. הפורמט הבינארי היעיל והטיפוס החזק שלו הופכים אותו לאידיאלי ליישומי ביצועים גבוהים ותקשורת חוצת שפות."
    step1: "העלה קבצי .proto או הדבק הגדרות Protocol Buffer. הכלי מנתח תחביר protobuf ומחלץ מידע מבנה הודעה."
    step3: "צור הגדרות Protocol Buffer סטנדרטיות עם תמיכה בסוגי הודעות, אפשרויות שדות והגדרות שירות. קבצי .proto שנוצרו יכולים להיות מקומפלים למספר שפות תכנות."
    from_alias: "Protocol Buffer"
    to_alias: "סכמת Protobuf"
  PandasDataFrame:
    alias: "Pandas DataFrame"
    what: "Pandas היא ספריית ניתוח הנתונים הפופולרית ביותר ב-Python, עם DataFrame כמבנה הנתונים הליבה שלה. היא מספקת יכולות מניפולציה, ניקוי וניתוח נתונים חזקות, נמצאת בשימוש נרחב במדעי הנתונים, למידת מכונה ובינה עסקית. כלי הכרחי למפתחי Python ואנליסטי נתונים."
    step1: "העלה קבצי Python המכילים קוד DataFrame או הדבק נתונים. הכלי מנתח תחביר Pandas ומחלץ מידע מבנה DataFrame."
    step3: "צור קוד Pandas DataFrame סטנדרטי עם תמיכה במפרטי סוגי נתונים, הגדרות אינדקס ופעולות נתונים. הקוד שנוצר יכול להיות מבוצע ישירות בסביבת Python לניתוח ועיבוד נתונים."
    from_alias: "Pandas DataFrame"
    to_alias: "Pandas DataFrame"
  RDF:
    alias: "שלישיית RDF"
    what: "RDF (Resource Description Framework) הוא מודל סטנדרטי לחילופי נתונים באינטרנט, מעוצב לייצג מידע על משאבים בצורת גרף. נמצא בשימוש נרחב באינטרנט הסמנטי, גרפי ידע ויישומי נתונים מקושרים. מבנה הטריפלט שלו מאפשר ייצוג מטא-נתונים עשיר ויחסים סמנטיים."
    step1: "העלה קבצי RDF או הדבק נתוני טריפלט. הכלי מנתח תחביר RDF ומחלץ יחסים סמנטיים ומידע משאבים."
    step3: "צור פורמט RDF סטנדרטי עם תמיכה בסריאליזציות שונות (RDF/XML, Turtle, N-Triples). RDF שנוצר יכול לשמש ביישומי אינטרנט סמנטיים, בסיסי ידע ומערכות נתונים מקושרים."
    from_alias: "RDF"
    to_alias: "שלישיית RDF"
  MATLAB:
    alias: "מערך MATLAB"
    what: "MATLAB הוא תוכנת חישוב מספרי וויזואליזציה בעלת ביצועים גבוהים הנמצאת בשימוש נרחב בחישוב הנדסי, ניתוח נתונים ופיתוח אלגוריתמים. פעולות המערכים והמטריצות שלו חזקות, תומכות בחישובים מתמטיים מורכבים ועיבוד נתונים. כלי חיוני למהנדסים, חוקרים ומדעני נתונים."
    step1: "העלה קבצי MATLAB .m או הדבק נתוני מערך. הכלי מנתח תחביר MATLAB ומחלץ מידע מבנה מערך."
    step3: "צור קוד מערך MATLAB סטנדרטי עם תמיכה במערכים רב-ממדיים, מפרטי סוגי נתונים ושמות משתנים. הקוד שנוצר יכול להיות מבוצע ישירות בסביבת MATLAB לניתוח נתונים וחישוב מדעי."
    from_alias: "מערך MATLAB"
    to_alias: "מערך MATLAB"
  RDataFrame:
    alias: "R DataFrame"
    what: "R DataFrame הוא מבנה הנתונים הליבה בשפת התכנות R, נמצא בשימוש נרחב בניתוח סטטיסטי, כריית נתונים ולמידת מכונה. R הוא הכלי המוביל לחישוב סטטיסטי וגרפיקה, עם DataFrame המספק יכולות מניפולציה חזקות של נתונים, ניתוח סטטיסטי וויזואליזציה. חיוני למדעני נתונים, סטטיסטיקאים וחוקרים העובדים עם ניתוח נתונים מובנים."
    step1: "העלה קבצי נתוני R או הדבק קוד DataFrame. הכלי מנתח תחביר R ומחלץ מידע מבנה DataFrame כולל סוגי עמודות, שמות שורות ותוכן נתונים."
    step3: "צור קוד R DataFrame סטנדרטי עם תמיכה במפרטי סוגי נתונים, רמות גורמים, שמות שורות/עמודות ומבני נתונים ספציפיים ל-R. הקוד שנוצר יכול להיות מבוצע ישירות בסביבת R לניתוח סטטיסטי ועיבוד נתונים."
    from_alias: "R DataFrame"
    to_alias: "R DataFrame"
hero:
  start_converting: "התחל המרה"
  start_generating: "התחל יצירה"
  api_docs: "תיעוד API"
related:
  section_title: 'עוד ממירי {{ if and .from (ne .from "generator") }}{{ .from }} ו{{ end }}{{ .to }}'
  section_description: 'חקור עוד ממירים עבור פורמטי {{ if and .from (ne .from "generator") }}{{ .from }} ו{{ end }}{{ .to }}. המר את הנתונים שלך בין פורמטים מרובים עם כלי ההמרה המקוונים המקצועיים שלנו.'
  title: "{{ .from }} ל-{{ .to }}"
howto:
  step2: "ערוך נתונים באמצעות עורך הטבלאות המקוון המתקדם שלנו עם תכונות מקצועיות. תומך במחיקת שורות ריקות, הסרת כפילויות, טרנספוזיציה של נתונים, מיון, חיפוש והחלפה regex, ותצוגה מקדימה בזמן אמת. כל השינויים מומרים אוטומטית לפורמט %s עם תוצאות מדויקות ואמינות."
  section_title: "איך להשתמש ב-{{ . }}"
  converter_description: "למד להמיר {{ .from }} ל-{{ .to }} עם המדריך שלב אחר שלב שלנו. ממיר מקוון מקצועי עם תכונות מתקדמות ותצוגה מקדימה בזמן אמת."
  generator_description: "למד ליצור טבלאות {{ .to }} מקצועיות עם הגנרטור המקוון שלנו. עריכה דמוית Excel, תצוגה מקדימה בזמן אמת ויכולות ייצוא מיידיות."
extension:
  section_title: "הרחבת זיהוי וחילוץ טבלאות"
  section_description: "חלץ טבלאות מכל אתר אינטרנט בלחיצה אחת. המר ל-30+ פורמטים כולל Excel, CSV, JSON מיידית - אין צורך בהעתקה והדבקה."
  features:
    extraction_title: "חילוץ טבלאות בלחיצה אחת"
    extraction_description: "חלץ מיידית טבלאות מכל דף אינטרנט ללא העתקה והדבקה - חילוץ נתונים מקצועי נעשה פשוט"
    formats_title: "תמיכה בממיר 30+ פורמטים"
    formats_description: "המר טבלאות מחולצות ל-Excel, CSV, JSON, Markdown, SQL ועוד עם ממיר הטבלאות המתקדם שלנו"
    detection_title: "זיהוי טבלאות חכם"
    detection_description: "מזהה ומדגיש אוטומטית טבלאות בכל דף אינטרנט לחילוץ והמרת נתונים מהירים"
  hover_tip: "✨ רחף מעל כל טבלה כדי לראות את סמל החילוץ"
recommendations:
  section_title: "מומלץ על ידי אוניברסיטאות ומקצועיים"
  section_description: "TableConvert זוכה לאמון של מקצועיים באוניברסיטאות, מוסדות מחקר וצוותי פיתוח להמרת טבלאות אמינה ועיבוד נתונים."
  cards:
    university_title: "University of Wisconsin-Madison"
    university_description: "TableConvert.com - כלי ממיר טבלאות מקוון מקצועי חינמי ופורמטי נתונים"
    university_link: "קרא מאמר"
    facebook_title: "קהילת מקצועי נתונים"
    facebook_description: "שותף והומלץ על ידי אנליסטי נתונים ומקצועיים בקבוצות מפתחי Facebook"
    facebook_link: "צפה בפוסט"
    twitter_title: "קהילת מפתחים"
    twitter_description: "הומלץ על ידי @xiaoying_eth ומפתחים אחרים ב-X (Twitter) להמרת טבלאות"
    twitter_link: "צפה בציוץ"
faq:
  section_title: "שאלות נפוצות"
  section_description: "שאלות נפוצות על ממיר הטבלאות המקוון החינמי שלנו, פורמטי נתונים ותהליך ההמרה."
  what: "מה זה פורמט %s?"
  howto_convert:
    question: "איך להשתמש ב-{{ . }} בחינם?"
    answer: "העלה את קובץ ה-{{ .from }} שלך, הדבק נתונים או חלץ מדפי אינטרנט באמצעות ממיר הטבלאות המקוון החינמי שלנו. כלי הממיר המקצועי שלנו מהמיר מיידית את הנתונים שלך לפורמט {{ .to }} עם תצוגה מקדימה בזמן אמת ותכונות עריכה מתקדמות. הורד או העתק את התוצאה המומרת מיידית."
  security:
    question: "האם הנתונים שלי בטוחים כשאני משתמש בממיר המקוון הזה?"
    answer: "בהחלט! כל המרות הטבלאות מתרחשות מקומית בדפדפן שלך - הנתונים שלך אף פעם לא עוזבים את המכשיר שלך. הממיר המקוון שלנו מעבד הכל בצד הלקוח, מבטיח פרטיות מלאה ואבטחת נתונים. אף קבצים לא נשמרים בשרתים שלנו."
  free:
    question: "האם TableConvert באמת חינמי לשימוש?"
    answer: "כן, TableConvert הוא חינמי לחלוטין! כל תכונות הממיר, עורך הטבלאות, כלי גנרטור הנתונים ואפשרויות הייצוא זמינים ללא עלות, הרשמה או עמלות נסתרות. המר קבצים ללא הגבלה מקוון בחינם."
  filesize:
    question: "מה הם מגבלות גודל הקובץ של הממיר המקוון?"
    answer: "ממיר הטבלאות המקוון החינמי שלנו תומך בקבצים עד 10MB. לקבצים גדולים יותר, עיבוד אצווה או צרכים ארגוניים, השתמש בהרחבת הדפדפן שלנו או בשירות API המקצועי עם מגבלות גבוהות יותר."
stats:
  conversions: "טבלאות הומרו"
  tables: "טבלאות נוצרו"
  formats: "פורמטי קבצי נתונים"
  rating: "דירוג משתמשים"
